// gen by exportxlsx.py

import { Event } from "laya/events/Event";

import { CfgCacheMgr } from "./CfgCacheMgr";
import { CfgCacheMapBase, SEARCH_TYPE } from "./CfgCacheMapBase";
import { CfgCacheMapMgr_CB } from "./CfgCacheMapMgr_CB";

import { cfg_acc_gift } from "./vo/cfg_acc_gift";
import { cfg_achievement } from "./vo/cfg_achievement";
import { cfg_activity_client } from "./vo/cfg_activity_client";
import { cfg_activity_desc } from "./vo/cfg_activity_desc";
import { cfg_activity_icon } from "./vo/cfg_activity_icon";
import { cfg_activity_limit_sign } from "./vo/cfg_activity_limit_sign";
import { cfg_activity_misc } from "./vo/cfg_activity_misc";
import { cfg_activity_mission } from "./vo/cfg_activity_mission";
import { cfg_activity_notice } from "./vo/cfg_activity_notice";
import { cfg_activity_page } from "./vo/cfg_activity_page";
import { cfg_activity_yueka } from "./vo/cfg_activity_yueka";
import { cfg_ad_show } from "./vo/cfg_ad_show";
import { cfg_agent_review } from "./vo/cfg_agent_review";
import { cfg_all_pinyin_dict } from "./vo/cfg_all_pinyin_dict";
import { cfg_arena } from "./vo/cfg_arena";
import { cfg_arena_match } from "./vo/cfg_arena_match";
import { cfg_arena_match_guess } from "./vo/cfg_arena_match_guess";
import { cfg_arena_max_reward } from "./vo/cfg_arena_max_reward";
import { cfg_arena_rank } from "./vo/cfg_arena_rank";
import { cfg_arena_reward } from "./vo/cfg_arena_reward";
import { cfg_arena_skip_limit } from "./vo/cfg_arena_skip_limit";
import { cfg_arena_weekly_reward } from "./vo/cfg_arena_weekly_reward";
import { cfg_ares_palace } from "./vo/cfg_ares_palace";
import { cfg_authorized_gifts } from "./vo/cfg_authorized_gifts";
import { cfg_bag_page } from "./vo/cfg_bag_page";
import { cfg_bai_jiang_gift } from "./vo/cfg_bai_jiang_gift";
import { cfg_battle_fly_name } from "./vo/cfg_battle_fly_name";
import { cfg_battle_trial_buff } from "./vo/cfg_battle_trial_buff";
import { cfg_battle_trial_buff_reset } from "./vo/cfg_battle_trial_buff_reset";
import { cfg_battle_trial_guaji_monster } from "./vo/cfg_battle_trial_guaji_monster";
import { cfg_battle_trial_misc } from "./vo/cfg_battle_trial_misc";
import { cfg_battle_trial_pass_guanqia } from "./vo/cfg_battle_trial_pass_guanqia";
import { cfg_battle_trial_pass_reward } from "./vo/cfg_battle_trial_pass_reward";
import { cfg_beast_platform } from "./vo/cfg_beast_platform";
import { cfg_bingfa } from "./vo/cfg_bingfa";
import { cfg_bingfa_ext } from "./vo/cfg_bingfa_ext";
import { cfg_bingfu } from "./vo/cfg_bingfu";
import { cfg_bingfu_discompose_recast } from "./vo/cfg_bingfu_discompose_recast";
import { cfg_bingfu_recast_lock } from "./vo/cfg_bingfu_recast_lock";
import { cfg_bingfu_refine } from "./vo/cfg_bingfu_refine";
import { cfg_bingfu_upgrade } from "./vo/cfg_bingfu_upgrade";
import { cfg_boat_peak_misc } from "./vo/cfg_boat_peak_misc";
import { cfg_boat_peak_rank } from "./vo/cfg_boat_peak_rank";
import { cfg_boat_peak_time } from "./vo/cfg_boat_peak_time";
import { cfg_boat_racc_rank_gold } from "./vo/cfg_boat_racc_rank_gold";
import { cfg_boat_race_auction_reward_type } from "./vo/cfg_boat_race_auction_reward_type";
import { cfg_boat_race_item } from "./vo/cfg_boat_race_item";
import { cfg_boat_race_misc } from "./vo/cfg_boat_race_misc";
import { cfg_buff } from "./vo/cfg_buff";
import { cfg_buff_type } from "./vo/cfg_buff_type";
import { cfg_building } from "./vo/cfg_building";
import { cfg_building_lv } from "./vo/cfg_building_lv";
import { cfg_building_mission } from "./vo/cfg_building_mission";
import { cfg_building_plot_dialogue } from "./vo/cfg_building_plot_dialogue";
import { cfg_buy_times } from "./vo/cfg_buy_times";
import { cfg_buy_times_type } from "./vo/cfg_buy_times_type";
import { cfg_cast_soul_active } from "./vo/cfg_cast_soul_active";
import { cfg_casting_soul } from "./vo/cfg_casting_soul";
import { cfg_casting_soul_buff } from "./vo/cfg_casting_soul_buff";
import { cfg_chapter_dialog } from "./vo/cfg_chapter_dialog";
import { cfg_chapter_script } from "./vo/cfg_chapter_script";
import { cfg_chat_bullet_msg } from "./vo/cfg_chat_bullet_msg";
import { cfg_chat_channel } from "./vo/cfg_chat_channel";
import { cfg_chat_skin } from "./vo/cfg_chat_skin";
import { cfg_chat_skin_widget } from "./vo/cfg_chat_skin_widget";
import { cfg_client_lang } from "./vo/cfg_client_lang";
import { cfg_client_w3_effect } from "./vo/cfg_client_w3_effect";
import { cfg_client_w3_effect_desc } from "./vo/cfg_client_w3_effect_desc";
import { cfg_client_w3_skill } from "./vo/cfg_client_w3_skill";
import { cfg_client_w3_skill_desc } from "./vo/cfg_client_w3_skill_desc";
import { cfg_client_w3_skin } from "./vo/cfg_client_w3_skin";
import { cfg_cmd } from "./vo/cfg_cmd";
import { cfg_code_cli } from "./vo/cfg_code_cli";
import { cfg_country_war_misc } from "./vo/cfg_country_war_misc";
import { cfg_cross_team_match_type } from "./vo/cfg_cross_team_match_type";
import { cfg_cross_team_misc } from "./vo/cfg_cross_team_misc";
import { cfg_cross_test_tower_open } from "./vo/cfg_cross_test_tower_open";
import { cfg_crush_fight_condition } from "./vo/cfg_crush_fight_condition";
import { cfg_crush_fight_mission } from "./vo/cfg_crush_fight_mission";
import { cfg_crush_fight_mission_type } from "./vo/cfg_crush_fight_mission_type";
import { cfg_csc_fmsolo_buff } from "./vo/cfg_csc_fmsolo_buff";
import { cfg_csc_fmsolo_challenge } from "./vo/cfg_csc_fmsolo_challenge";
import { cfg_csc_fmsolo_etc } from "./vo/cfg_csc_fmsolo_etc";
import { cfg_csc_fmsolo_log } from "./vo/cfg_csc_fmsolo_log";
import { cfg_csc_fmsolo_reward } from "./vo/cfg_csc_fmsolo_reward";
import { cfg_csc_fmsolo_shop_lv } from "./vo/cfg_csc_fmsolo_shop_lv";
import { cfg_csclan } from "./vo/cfg_csclan";
import { cfg_csclan_etc } from "./vo/cfg_csclan_etc";
import { cfg_csclan_solo_buff } from "./vo/cfg_csclan_solo_buff";
import { cfg_csclan_solo_challenge } from "./vo/cfg_csclan_solo_challenge";
import { cfg_csclan_solo_etc } from "./vo/cfg_csclan_solo_etc";
import { cfg_csclan_solo_log } from "./vo/cfg_csclan_solo_log";
import { cfg_csclan_solo_reward } from "./vo/cfg_csclan_solo_reward";
import { cfg_csclan_solo_shop_lv } from "./vo/cfg_csclan_solo_shop_lv";
import { cfg_daily_copy } from "./vo/cfg_daily_copy";
import { cfg_daily_copy_discount } from "./vo/cfg_daily_copy_discount";
import { cfg_daily_copy_monster } from "./vo/cfg_daily_copy_monster";
import { cfg_daily_copy_type } from "./vo/cfg_daily_copy_type";
import { cfg_daily_mission } from "./vo/cfg_daily_mission";
import { cfg_daily_mission_gift } from "./vo/cfg_daily_mission_gift";
import { cfg_daily_new_discount_rebate } from "./vo/cfg_daily_new_discount_rebate";
import { cfg_daily_pay } from "./vo/cfg_daily_pay";
import { cfg_dawanka } from "./vo/cfg_dawanka";
import { cfg_dawanka_tequan } from "./vo/cfg_dawanka_tequan";
import { cfg_day_acc_pay_gift } from "./vo/cfg_day_acc_pay_gift";
import { cfg_deputy } from "./vo/cfg_deputy";
import { cfg_deputy_level } from "./vo/cfg_deputy_level";
import { cfg_deputy_star } from "./vo/cfg_deputy_star";
import { cfg_device_excursion } from "./vo/cfg_device_excursion";
import { cfg_divine } from "./vo/cfg_divine";
import { cfg_divine_activate } from "./vo/cfg_divine_activate";
import { cfg_divine_copy } from "./vo/cfg_divine_copy";
import { cfg_divine_strengthen } from "./vo/cfg_divine_strengthen";
import { cfg_divine_wear } from "./vo/cfg_divine_wear";
import { cfg_dominate_pvp } from "./vo/cfg_dominate_pvp";
import { cfg_dominate_pvp_limit } from "./vo/cfg_dominate_pvp_limit";
import { cfg_dominate_pvp_reward } from "./vo/cfg_dominate_pvp_reward";
import { cfg_dominate_pvp_task } from "./vo/cfg_dominate_pvp_task";
import { cfg_dominate_pvp_task_rewards } from "./vo/cfg_dominate_pvp_task_rewards";
import { cfg_drop_group } from "./vo/cfg_drop_group";
import { cfg_eight_login } from "./vo/cfg_eight_login";
import { cfg_epic_battle_tips } from "./vo/cfg_epic_battle_tips";
import { cfg_equip } from "./vo/cfg_equip";
import { cfg_equip_compose } from "./vo/cfg_equip_compose";
import { cfg_equip_suit } from "./vo/cfg_equip_suit";
import { cfg_equip_suit_ext } from "./vo/cfg_equip_suit_ext";
import { cfg_fail_tips } from "./vo/cfg_fail_tips";
import { cfg_family } from "./vo/cfg_family";
import { cfg_family_active_attr } from "./vo/cfg_family_active_attr";
import { cfg_family_active_mission } from "./vo/cfg_family_active_mission";
import { cfg_family_boss } from "./vo/cfg_family_boss";
import { cfg_family_boss_attr } from "./vo/cfg_family_boss_attr";
import { cfg_family_boss_misc } from "./vo/cfg_family_boss_misc";
import { cfg_family_boss_rank } from "./vo/cfg_family_boss_rank";
import { cfg_family_etc } from "./vo/cfg_family_etc";
import { cfg_family_hongbao } from "./vo/cfg_family_hongbao";
import { cfg_family_hongbao_blessing } from "./vo/cfg_family_hongbao_blessing";
import { cfg_family_hongbao_reward } from "./vo/cfg_family_hongbao_reward";
import { cfg_family_log } from "./vo/cfg_family_log";
import { cfg_family_random_name } from "./vo/cfg_family_random_name";
import { cfg_family_science } from "./vo/cfg_family_science";
import { cfg_family_science_times } from "./vo/cfg_family_science_times";
import { cfg_family_sign } from "./vo/cfg_family_sign";
import { cfg_family_sign_active } from "./vo/cfg_family_sign_active";
import { cfg_fight_show } from "./vo/cfg_fight_show";
import { cfg_first_pay } from "./vo/cfg_first_pay";
import { cfg_fish } from "./vo/cfg_fish";
import { cfg_fish_attr } from "./vo/cfg_fish_attr";
import { cfg_fish_color } from "./vo/cfg_fish_color";
import { cfg_fish_fishbowl } from "./vo/cfg_fish_fishbowl";
import { cfg_fish_handbook } from "./vo/cfg_fish_handbook";
import { cfg_fish_help_gift } from "./vo/cfg_fish_help_gift";
import { cfg_fish_level } from "./vo/cfg_fish_level";
import { cfg_fish_map } from "./vo/cfg_fish_map";
import { cfg_fish_misc } from "./vo/cfg_fish_misc";
import { cfg_fish_mission } from "./vo/cfg_fish_mission";
import { cfg_fish_official_position } from "./vo/cfg_fish_official_position";
import { cfg_fish_reshape } from "./vo/cfg_fish_reshape";
import { cfg_fish_resources } from "./vo/cfg_fish_resources";
import { cfg_fish_rod_level } from "./vo/cfg_fish_rod_level";
import { cfg_fish_slot } from "./vo/cfg_fish_slot";
import { cfg_fish_stage } from "./vo/cfg_fish_stage";
import { cfg_fly_font_type } from "./vo/cfg_fly_font_type";
import { cfg_fuli_sign } from "./vo/cfg_fuli_sign";
import { cfg_fuli_sign_acc } from "./vo/cfg_fuli_sign_acc";
import { cfg_fuli_sign_day } from "./vo/cfg_fuli_sign_day";
import { cfg_fuli_token } from "./vo/cfg_fuli_token";
import { cfg_fuli_token_type } from "./vo/cfg_fuli_token_type";
import { cfg_fuli_yueka } from "./vo/cfg_fuli_yueka";
import { cfg_game_desc } from "./vo/cfg_game_desc";
import { cfg_game_desc_2 } from "./vo/cfg_game_desc_2";
import { cfg_game_desc_3 } from "./vo/cfg_game_desc_3";
import { cfg_general_pass_mission } from "./vo/cfg_general_pass_mission";
import { cfg_general_pass_reward } from "./vo/cfg_general_pass_reward";
import { cfg_general_pass_type } from "./vo/cfg_general_pass_type";
import { cfg_gift } from "./vo/cfg_gift";
import { cfg_god_equip } from "./vo/cfg_god_equip";
import { cfg_god_equip_compose } from "./vo/cfg_god_equip_compose";
import { cfg_god_equip_convert } from "./vo/cfg_god_equip_convert";
import { cfg_god_equip_enchant } from "./vo/cfg_god_equip_enchant";
import { cfg_god_equip_enchant_cost } from "./vo/cfg_god_equip_enchant_cost";
import { cfg_god_equip_suit } from "./vo/cfg_god_equip_suit";
import { cfg_god_equip_type } from "./vo/cfg_god_equip_type";
import { cfg_god_trial } from "./vo/cfg_god_trial";
import { cfg_god_trial_buff } from "./vo/cfg_god_trial_buff";
import { cfg_god_weapon } from "./vo/cfg_god_weapon";
import { cfg_god_weapon_level } from "./vo/cfg_god_weapon_level";
import { cfg_god_weapon_mission } from "./vo/cfg_god_weapon_mission";
import { cfg_god_weapon_refine } from "./vo/cfg_god_weapon_refine";
import { cfg_god_weapon_skill } from "./vo/cfg_god_weapon_skill";
import { cfg_god_weapon_skill_attr } from "./vo/cfg_god_weapon_skill_attr";
import { cfg_god_weapon_soul } from "./vo/cfg_god_weapon_soul";
import { cfg_god_weapon_star } from "./vo/cfg_god_weapon_star";
import { cfg_gray_pinyin } from "./vo/cfg_gray_pinyin";
import { cfg_grow_tips } from "./vo/cfg_grow_tips";
import { cfg_guaji_box_time } from "./vo/cfg_guaji_box_time";
import { cfg_guaji_monster } from "./vo/cfg_guaji_monster";
import { cfg_guaji_quick_navigation } from "./vo/cfg_guaji_quick_navigation";
import { cfg_guandu } from "./vo/cfg_guandu";
import { cfg_guandu_answer } from "./vo/cfg_guandu_answer";
import { cfg_guandu_chose } from "./vo/cfg_guandu_chose";
import { cfg_guandu_floor } from "./vo/cfg_guandu_floor";
import { cfg_guandu_mission } from "./vo/cfg_guandu_mission";
import { cfg_guandu_shop } from "./vo/cfg_guandu_shop";
import { cfg_guide_helper } from "./vo/cfg_guide_helper";
import { cfg_guide_helper_1 } from "./vo/cfg_guide_helper_1";
import { cfg_guide_helper_2 } from "./vo/cfg_guide_helper_2";
import { cfg_guide_helper_3 } from "./vo/cfg_guide_helper_3";
import { cfg_guide_helper_4 } from "./vo/cfg_guide_helper_4";
import { cfg_guide_helper_game_1 } from "./vo/cfg_guide_helper_game_1";
import { cfg_guide_helper_m2 } from "./vo/cfg_guide_helper_m2";
import { cfg_guide_helper_m3 } from "./vo/cfg_guide_helper_m3";
import { cfg_guide_mission } from "./vo/cfg_guide_mission";
import { cfg_guide_mission_1 } from "./vo/cfg_guide_mission_1";
import { cfg_guide_mission_2 } from "./vo/cfg_guide_mission_2";
import { cfg_guide_mission_3 } from "./vo/cfg_guide_mission_3";
import { cfg_guide_mission_4 } from "./vo/cfg_guide_mission_4";
import { cfg_guide_mission_game_1 } from "./vo/cfg_guide_mission_game_1";
import { cfg_guide_mission_m2 } from "./vo/cfg_guide_mission_m2";
import { cfg_guide_mission_m3 } from "./vo/cfg_guide_mission_m3";
import { cfg_guide_review } from "./vo/cfg_guide_review";
import { cfg_guide_story } from "./vo/cfg_guide_story";
import { cfg_hero_attr_addition } from "./vo/cfg_hero_attr_addition";
import { cfg_hero_attr_source } from "./vo/cfg_hero_attr_source";
import { cfg_hero_bag } from "./vo/cfg_hero_bag";
import { cfg_hero_base } from "./vo/cfg_hero_base";
import { cfg_hero_cheer } from "./vo/cfg_hero_cheer";
import { cfg_hero_cheer_bonus } from "./vo/cfg_hero_cheer_bonus";
import { cfg_hero_cheer_level } from "./vo/cfg_hero_cheer_level";
import { cfg_hero_cheer_unlock } from "./vo/cfg_hero_cheer_unlock";
import { cfg_hero_chip_star } from "./vo/cfg_hero_chip_star";
import { cfg_hero_come_mission } from "./vo/cfg_hero_come_mission";
import { cfg_hero_convert } from "./vo/cfg_hero_convert";
import { cfg_hero_convert_weight } from "./vo/cfg_hero_convert_weight";
import { cfg_hero_cost_plan } from "./vo/cfg_hero_cost_plan";
import { cfg_hero_evolve_skill } from "./vo/cfg_hero_evolve_skill";
import { cfg_hero_handbook_desc } from "./vo/cfg_hero_handbook_desc";
import { cfg_hero_level } from "./vo/cfg_hero_level";
import { cfg_hero_level_limit } from "./vo/cfg_hero_level_limit";
import { cfg_hero_nation } from "./vo/cfg_hero_nation";
import { cfg_hero_pass_mission } from "./vo/cfg_hero_pass_mission";
import { cfg_hero_pass_reward } from "./vo/cfg_hero_pass_reward";
import { cfg_hero_recommend_pre } from "./vo/cfg_hero_recommend_pre";
import { cfg_hero_recycle } from "./vo/cfg_hero_recycle";
import { cfg_hero_recycle_change } from "./vo/cfg_hero_recycle_change";
import { cfg_hero_recycle_change_star_stage } from "./vo/cfg_hero_recycle_change_star_stage";
import { cfg_hero_recycle_res } from "./vo/cfg_hero_recycle_res";
import { cfg_hero_recycle_special_switch } from "./vo/cfg_hero_recycle_special_switch";
import { cfg_hero_resonate_dhyana } from "./vo/cfg_hero_resonate_dhyana";
import { cfg_hero_resonate_dudu } from "./vo/cfg_hero_resonate_dudu";
import { cfg_hero_resonate_dudu_level } from "./vo/cfg_hero_resonate_dudu_level";
import { cfg_hero_resonate_five } from "./vo/cfg_hero_resonate_five";
import { cfg_hero_skin } from "./vo/cfg_hero_skin";
import { cfg_hero_skin_level } from "./vo/cfg_hero_skin_level";
import { cfg_hero_stage } from "./vo/cfg_hero_stage";
import { cfg_hero_stage_limit } from "./vo/cfg_hero_stage_limit";
import { cfg_hero_star } from "./vo/cfg_hero_star";
import { cfg_hero_star_attr } from "./vo/cfg_hero_star_attr";
import { cfg_hero_star_limit } from "./vo/cfg_hero_star_limit";
import { cfg_hero_star_stage } from "./vo/cfg_hero_star_stage";
import { cfg_hero_star_stage_attr } from "./vo/cfg_hero_star_stage_attr";
import { cfg_hero_strengthen } from "./vo/cfg_hero_strengthen";
import { cfg_hero_upgrade_tips } from "./vo/cfg_hero_upgrade_tips";
import { cfg_hero_zhouyin } from "./vo/cfg_hero_zhouyin";
import { cfg_hunt_buy } from "./vo/cfg_hunt_buy";
import { cfg_hunt_cost } from "./vo/cfg_hunt_cost";
import { cfg_hunt_desc } from "./vo/cfg_hunt_desc";
import { cfg_hunt_gift } from "./vo/cfg_hunt_gift";
import { cfg_hunt_rewards_show } from "./vo/cfg_hunt_rewards_show";
import { cfg_huoqutujing } from "./vo/cfg_huoqutujing";
import { cfg_hzzd_achievement } from "./vo/cfg_hzzd_achievement";
import { cfg_hzzd_event } from "./vo/cfg_hzzd_event";
import { cfg_hzzd_kills_reward } from "./vo/cfg_hzzd_kills_reward";
import { cfg_hzzd_misc } from "./vo/cfg_hzzd_misc";
import { cfg_i18n_lang } from "./vo/cfg_i18n_lang";
import { cfg_i18n_ts } from "./vo/cfg_i18n_ts";
import { cfg_i18n_ui } from "./vo/cfg_i18n_ui";
import { cfg_ingenious_plan } from "./vo/cfg_ingenious_plan";
import { cfg_ingenious_plan_compose } from "./vo/cfg_ingenious_plan_compose";
import { cfg_ingenious_plan_convert } from "./vo/cfg_ingenious_plan_convert";
import { cfg_ingenious_plan_level } from "./vo/cfg_ingenious_plan_level";
import { cfg_ingenious_plan_stage } from "./vo/cfg_ingenious_plan_stage";
import { cfg_ingenious_plan_star } from "./vo/cfg_ingenious_plan_star";
import { cfg_ip_set } from "./vo/cfg_ip_set";
import { cfg_item } from "./vo/cfg_item";
import { cfg_item_compose } from "./vo/cfg_item_compose";
import { cfg_item_time_client } from "./vo/cfg_item_time_client";
import { cfg_large_peak_agent } from "./vo/cfg_large_peak_agent";
import { cfg_large_peak_battle_table } from "./vo/cfg_large_peak_battle_table";
import { cfg_large_peak_misc } from "./vo/cfg_large_peak_misc";
import { cfg_large_peak_rank } from "./vo/cfg_large_peak_rank";
import { cfg_large_peak_season } from "./vo/cfg_large_peak_season";
import { cfg_large_peak_time } from "./vo/cfg_large_peak_time";
import { cfg_lazy_load } from "./vo/cfg_lazy_load";
import { cfg_lcqs_acc_star_reward } from "./vo/cfg_lcqs_acc_star_reward";
import { cfg_lcqs_chapter_open } from "./vo/cfg_lcqs_chapter_open";
import { cfg_lcqs_floor_reward } from "./vo/cfg_lcqs_floor_reward";
import { cfg_lcqs_mission } from "./vo/cfg_lcqs_mission";
import { cfg_level } from "./vo/cfg_level";
import { cfg_level_gift } from "./vo/cfg_level_gift";
import { cfg_limit_hero_skin_chip_exchange } from "./vo/cfg_limit_hero_skin_chip_exchange";
import { cfg_lineup_buff } from "./vo/cfg_lineup_buff";
import { cfg_lineup_buff_icon } from "./vo/cfg_lineup_buff_icon";
import { cfg_lineup_career_rule } from "./vo/cfg_lineup_career_rule";
import { cfg_lineup_num } from "./vo/cfg_lineup_num";
import { cfg_lineup_recommend } from "./vo/cfg_lineup_recommend";
import { cfg_lineup_style } from "./vo/cfg_lineup_style";
import { cfg_load_tips } from "./vo/cfg_load_tips";
import { cfg_login_activity } from "./vo/cfg_login_activity";
import { cfg_login_activity_round } from "./vo/cfg_login_activity_round";
import { cfg_lord_activation } from "./vo/cfg_lord_activation";
import { cfg_lord_base } from "./vo/cfg_lord_base";
import { cfg_lord_camp } from "./vo/cfg_lord_camp";
import { cfg_lord_exchange } from "./vo/cfg_lord_exchange";
import { cfg_lord_skill } from "./vo/cfg_lord_skill";
import { cfg_lord_skill_enhance } from "./vo/cfg_lord_skill_enhance";
import { cfg_lord_star } from "./vo/cfg_lord_star";
import { cfg_lord_suit_compose } from "./vo/cfg_lord_suit_compose";
import { cfg_lord_suit_select } from "./vo/cfg_lord_suit_select";
import { cfg_lord_treasure } from "./vo/cfg_lord_treasure";
import { cfg_lord_treasure_entry } from "./vo/cfg_lord_treasure_entry";
import { cfg_lord_treasure_forge } from "./vo/cfg_lord_treasure_forge";
import { cfg_lord_treasure_level } from "./vo/cfg_lord_treasure_level";
import { cfg_lottery_day_limit } from "./vo/cfg_lottery_day_limit";
import { cfg_lottery_ext } from "./vo/cfg_lottery_ext";
import { cfg_lottery_nation } from "./vo/cfg_lottery_nation";
import { cfg_lottery_nation_times } from "./vo/cfg_lottery_nation_times";
import { cfg_lottery_score } from "./vo/cfg_lottery_score";
import { cfg_lottery_show } from "./vo/cfg_lottery_show";
import { cfg_lottery_times } from "./vo/cfg_lottery_times";
import { cfg_lottery_weight } from "./vo/cfg_lottery_weight";
import { cfg_main_battle } from "./vo/cfg_main_battle";
import { cfg_main_battle_box } from "./vo/cfg_main_battle_box";
import { cfg_main_battle_box_level } from "./vo/cfg_main_battle_box_level";
import { cfg_main_battle_box_reward } from "./vo/cfg_main_battle_box_reward";
import { cfg_main_battle_box_tequan } from "./vo/cfg_main_battle_box_tequan";
import { cfg_main_battle_fetch } from "./vo/cfg_main_battle_fetch";
import { cfg_main_battle_hanging } from "./vo/cfg_main_battle_hanging";
import { cfg_main_battle_mission } from "./vo/cfg_main_battle_mission";
import { cfg_map } from "./vo/cfg_map";
import { cfg_map_item } from "./vo/cfg_map_item";
import { cfg_map_item_type } from "./vo/cfg_map_item_type";
import { cfg_map_type } from "./vo/cfg_map_type";
import { cfg_master_card } from "./vo/cfg_master_card";
import { cfg_master_card_attr } from "./vo/cfg_master_card_attr";
import { cfg_master_card_color } from "./vo/cfg_master_card_color";
import { cfg_master_card_drum_level } from "./vo/cfg_master_card_drum_level";
import { cfg_master_card_first_pay } from "./vo/cfg_master_card_first_pay";
import { cfg_master_card_help_gift } from "./vo/cfg_master_card_help_gift";
import { cfg_master_card_misc } from "./vo/cfg_master_card_misc";
import { cfg_master_card_mission } from "./vo/cfg_master_card_mission";
import { cfg_master_card_official } from "./vo/cfg_master_card_official";
import { cfg_master_card_official_position } from "./vo/cfg_master_card_official_position";
import { cfg_master_card_reshape } from "./vo/cfg_master_card_reshape";
import { cfg_master_card_slot } from "./vo/cfg_master_card_slot";
import { cfg_master_card_stage } from "./vo/cfg_master_card_stage";
import { cfg_master_talent_science } from "./vo/cfg_master_talent_science";
import { cfg_master_talent_science_icon } from "./vo/cfg_master_talent_science_icon";
import { cfg_match_type } from "./vo/cfg_match_type";
import { cfg_match_type_team } from "./vo/cfg_match_type_team";
import { cfg_maze } from "./vo/cfg_maze";
import { cfg_maze_diff_rewards } from "./vo/cfg_maze_diff_rewards";
import { cfg_maze_mission_spoils } from "./vo/cfg_maze_mission_spoils";
import { cfg_maze_monster } from "./vo/cfg_maze_monster";
import { cfg_maze_reset } from "./vo/cfg_maze_reset";
import { cfg_maze_revive } from "./vo/cfg_maze_revive";
import { cfg_maze_shop } from "./vo/cfg_maze_shop";
import { cfg_maze_theme } from "./vo/cfg_maze_theme";
import { cfg_medal } from "./vo/cfg_medal";
import { cfg_microterminal_open } from "./vo/cfg_microterminal_open";
import { cfg_microterminal_sign } from "./vo/cfg_microterminal_sign";
import { cfg_misc_config } from "./vo/cfg_misc_config";
import { cfg_mission_shop } from "./vo/cfg_mission_shop";
import { cfg_mission_shop_client } from "./vo/cfg_mission_shop_client";
import { cfg_mock_pvp_database } from "./vo/cfg_mock_pvp_database";
import { cfg_mock_pvp_hero_base } from "./vo/cfg_mock_pvp_hero_base";
import { cfg_mock_pvp_limit } from "./vo/cfg_mock_pvp_limit";
import { cfg_mock_pvp_misc } from "./vo/cfg_mock_pvp_misc";
import { cfg_mock_pvp_mission } from "./vo/cfg_mock_pvp_mission";
import { cfg_modular_activity_bless } from "./vo/cfg_modular_activity_bless";
import { cfg_modular_activity_brick } from "./vo/cfg_modular_activity_brick";
import { cfg_modular_activity_carnival_link } from "./vo/cfg_modular_activity_carnival_link";
import { cfg_modular_activity_client_setting } from "./vo/cfg_modular_activity_client_setting";
import { cfg_modular_activity_compose_list } from "./vo/cfg_modular_activity_compose_list";
import { cfg_modular_activity_customized_gift } from "./vo/cfg_modular_activity_customized_gift";
import { cfg_modular_activity_dice } from "./vo/cfg_modular_activity_dice";
import { cfg_modular_activity_dice_boss } from "./vo/cfg_modular_activity_dice_boss";
import { cfg_modular_activity_dice_client_diff } from "./vo/cfg_modular_activity_dice_client_diff";
import { cfg_modular_activity_dice_misc } from "./vo/cfg_modular_activity_dice_misc";
import { cfg_modular_activity_drop } from "./vo/cfg_modular_activity_drop";
import { cfg_modular_activity_drop_show } from "./vo/cfg_modular_activity_drop_show";
import { cfg_modular_activity_exchange } from "./vo/cfg_modular_activity_exchange";
import { cfg_modular_activity_festival_wish } from "./vo/cfg_modular_activity_festival_wish";
import { cfg_modular_activity_festival_wish_choose } from "./vo/cfg_modular_activity_festival_wish_choose";
import { cfg_modular_activity_festival_wish_cost } from "./vo/cfg_modular_activity_festival_wish_cost";
import { cfg_modular_activity_free_switch } from "./vo/cfg_modular_activity_free_switch";
import { cfg_modular_activity_general_pass_vip } from "./vo/cfg_modular_activity_general_pass_vip";
import { cfg_modular_activity_hero_challenge } from "./vo/cfg_modular_activity_hero_challenge";
import { cfg_modular_activity_holiday_welfare_reward } from "./vo/cfg_modular_activity_holiday_welfare_reward";
import { cfg_modular_activity_hunt_cost } from "./vo/cfg_modular_activity_hunt_cost";
import { cfg_modular_activity_hunt_desc } from "./vo/cfg_modular_activity_hunt_desc";
import { cfg_modular_activity_hunt_misc } from "./vo/cfg_modular_activity_hunt_misc";
import { cfg_modular_activity_huoqutujing } from "./vo/cfg_modular_activity_huoqutujing";
import { cfg_modular_activity_icon } from "./vo/cfg_modular_activity_icon";
import { cfg_modular_activity_login } from "./vo/cfg_modular_activity_login";
import { cfg_modular_activity_lottery_target } from "./vo/cfg_modular_activity_lottery_target";
import { cfg_modular_activity_lottery_times } from "./vo/cfg_modular_activity_lottery_times";
import { cfg_modular_activity_lucky_bag } from "./vo/cfg_modular_activity_lucky_bag";
import { cfg_modular_activity_mission } from "./vo/cfg_modular_activity_mission";
import { cfg_modular_activity_open } from "./vo/cfg_modular_activity_open";
import { cfg_modular_activity_open_preview } from "./vo/cfg_modular_activity_open_preview";
import { cfg_modular_activity_pay_welfare } from "./vo/cfg_modular_activity_pay_welfare";
import { cfg_modular_activity_payment_shop_item } from "./vo/cfg_modular_activity_payment_shop_item";
import { cfg_modular_activity_payment_shop_item_show } from "./vo/cfg_modular_activity_payment_shop_item_show";
import { cfg_modular_activity_preview } from "./vo/cfg_modular_activity_preview";
import { cfg_modular_activity_preview_rewards } from "./vo/cfg_modular_activity_preview_rewards";
import { cfg_modular_activity_rank } from "./vo/cfg_modular_activity_rank";
import { cfg_modular_activity_rank_reward } from "./vo/cfg_modular_activity_rank_reward";
import { cfg_modular_activity_round_mission } from "./vo/cfg_modular_activity_round_mission";
import { cfg_modular_activity_round_mission_reward } from "./vo/cfg_modular_activity_round_mission_reward";
import { cfg_modular_activity_shop_client } from "./vo/cfg_modular_activity_shop_client";
import { cfg_modular_activity_sign } from "./vo/cfg_modular_activity_sign";
import { cfg_modular_activity_six_bless } from "./vo/cfg_modular_activity_six_bless";
import { cfg_modular_activity_star_plan_hero } from "./vo/cfg_modular_activity_star_plan_hero";
import { cfg_modular_activity_star_plan_reward } from "./vo/cfg_modular_activity_star_plan_reward";
import { cfg_modular_activity_story } from "./vo/cfg_modular_activity_story";
import { cfg_modular_activity_story_chapter } from "./vo/cfg_modular_activity_story_chapter";
import { cfg_modular_activity_story_chapter_map } from "./vo/cfg_modular_activity_story_chapter_map";
import { cfg_modular_activity_story_dialogue } from "./vo/cfg_modular_activity_story_dialogue";
import { cfg_modular_activity_sub_type } from "./vo/cfg_modular_activity_sub_type";
import { cfg_modular_activity_target } from "./vo/cfg_modular_activity_target";
import { cfg_modular_activity_time_item } from "./vo/cfg_modular_activity_time_item";
import { cfg_modular_activity_wall } from "./vo/cfg_modular_activity_wall";
import { cfg_modular_activity_war_log_acc_reward } from "./vo/cfg_modular_activity_war_log_acc_reward";
import { cfg_modular_activity_war_log_mission } from "./vo/cfg_modular_activity_war_log_mission";
import { cfg_modular_activity_weekly_card_reward } from "./vo/cfg_modular_activity_weekly_card_reward";
import { cfg_money } from "./vo/cfg_money";
import { cfg_monster } from "./vo/cfg_monster";
import { cfg_monster_group } from "./vo/cfg_monster_group";
import { cfg_monster_skill_tier } from "./vo/cfg_monster_skill_tier";
import { cfg_monster_tips } from "./vo/cfg_monster_tips";
import { cfg_month_fund } from "./vo/cfg_month_fund";
import { cfg_month_fund_type } from "./vo/cfg_month_fund_type";
import { cfg_music } from "./vo/cfg_music";
import { cfg_nation_tower_lineup } from "./vo/cfg_nation_tower_lineup";
import { cfg_nation_tower_open } from "./vo/cfg_nation_tower_open";
import { cfg_noob_pay } from "./vo/cfg_noob_pay";
import { cfg_online_reward } from "./vo/cfg_online_reward";
import { cfg_pass_behead } from "./vo/cfg_pass_behead";
import { cfg_pass_behead_box } from "./vo/cfg_pass_behead_box";
import { cfg_pass_behead_guanqia } from "./vo/cfg_pass_behead_guanqia";
import { cfg_pass_behead_revive } from "./vo/cfg_pass_behead_revive";
import { cfg_pass_check_mission } from "./vo/cfg_pass_check_mission";
import { cfg_pass_check_reward } from "./vo/cfg_pass_check_reward";
import { cfg_pass_check_vip } from "./vo/cfg_pass_check_vip";
import { cfg_pay_vip } from "./vo/cfg_pay_vip";
import { cfg_pay_vip_privilege } from "./vo/cfg_pay_vip_privilege";
import { cfg_pay_vip_privilege_function } from "./vo/cfg_pay_vip_privilege_function";
import { cfg_payment_shop_item } from "./vo/cfg_payment_shop_item";
import { cfg_payment_shop_link } from "./vo/cfg_payment_shop_link";
import { cfg_payment_time_gift } from "./vo/cfg_payment_time_gift";
import { cfg_peak_misc } from "./vo/cfg_peak_misc";
import { cfg_peak_time } from "./vo/cfg_peak_time";
import { cfg_peerless_act_hero_gift } from "./vo/cfg_peerless_act_hero_gift";
import { cfg_platform_ad_id_misc } from "./vo/cfg_platform_ad_id_misc";
import { cfg_player_strategy } from "./vo/cfg_player_strategy";
import { cfg_playing_preview_reward } from "./vo/cfg_playing_preview_reward";
import { cfg_progress_gift } from "./vo/cfg_progress_gift";
import { cfg_pull_words } from "./vo/cfg_pull_words";
import { cfg_pvp_map } from "./vo/cfg_pvp_map";
import { cfg_qq_group } from "./vo/cfg_qq_group";
import { cfg_qq_vip } from "./vo/cfg_qq_vip";
import { cfg_qxzl_misc } from "./vo/cfg_qxzl_misc";
import { cfg_random_box } from "./vo/cfg_random_box";
import { cfg_random_pvp } from "./vo/cfg_random_pvp";
import { cfg_random_pvp_head_frame } from "./vo/cfg_random_pvp_head_frame";
import { cfg_random_pvp_limit } from "./vo/cfg_random_pvp_limit";
import { cfg_random_pvp_reward } from "./vo/cfg_random_pvp_reward";
import { cfg_random_pvp_task } from "./vo/cfg_random_pvp_task";
import { cfg_random_pvp_task_rewards } from "./vo/cfg_random_pvp_task_rewards";
import { cfg_rank_desc } from "./vo/cfg_rank_desc";
import { cfg_rank_mission } from "./vo/cfg_rank_mission";
import { cfg_rank_rewards } from "./vo/cfg_rank_rewards";
import { cfg_rank_worship } from "./vo/cfg_rank_worship";
import { cfg_red_cliff } from "./vo/cfg_red_cliff";
import { cfg_red_cliff_boss } from "./vo/cfg_red_cliff_boss";
import { cfg_red_cliff_open } from "./vo/cfg_red_cliff_open";
import { cfg_red_cliff_refresh } from "./vo/cfg_red_cliff_refresh";
import { cfg_red_cliff_review } from "./vo/cfg_red_cliff_review";
import { cfg_retrieval } from "./vo/cfg_retrieval";
import { cfg_river_text_const } from "./vo/cfg_river_text_const";
import { cfg_role_profile } from "./vo/cfg_role_profile";
import { cfg_san_xiao_actor } from "./vo/cfg_san_xiao_actor";
import { cfg_san_xiao_guanqia } from "./vo/cfg_san_xiao_guanqia";
import { cfg_san_xiao_item } from "./vo/cfg_san_xiao_item";
import { cfg_san_xiao_level } from "./vo/cfg_san_xiao_level";
import { cfg_san_xiao_map } from "./vo/cfg_san_xiao_map";
import { cfg_san_xiao_misc } from "./vo/cfg_san_xiao_misc";
import { cfg_scene } from "./vo/cfg_scene";
import { cfg_sdk_concern_reward } from "./vo/cfg_sdk_concern_reward";
import { cfg_sdk_platform_desc } from "./vo/cfg_sdk_platform_desc";
import { cfg_sdk_rewards } from "./vo/cfg_sdk_rewards";
import { cfg_select_box } from "./vo/cfg_select_box";
import { cfg_seven_goal } from "./vo/cfg_seven_goal";
import { cfg_seven_goal_gift } from "./vo/cfg_seven_goal_gift";
import { cfg_seven_goal_mission } from "./vo/cfg_seven_goal_mission";
import { cfg_share_cycle_reward } from "./vo/cfg_share_cycle_reward";
import { cfg_share_daily_reward } from "./vo/cfg_share_daily_reward";
import { cfg_share_level_reward } from "./vo/cfg_share_level_reward";
import { cfg_shili_preview } from "./vo/cfg_shili_preview";
import { cfg_shop } from "./vo/cfg_shop";
import { cfg_shop_item } from "./vo/cfg_shop_item";
import { cfg_shop_item_tips } from "./vo/cfg_shop_item_tips";
import { cfg_shop_reset_times } from "./vo/cfg_shop_reset_times";
import { cfg_shop_shortcut } from "./vo/cfg_shop_shortcut";
import { cfg_shop_tab_limit } from "./vo/cfg_shop_tab_limit";
import { cfg_show_off } from "./vo/cfg_show_off";
import { cfg_skeleton_adaptive } from "./vo/cfg_skeleton_adaptive";
import { cfg_skill } from "./vo/cfg_skill";
import { cfg_skill_effect } from "./vo/cfg_skill_effect";
import { cfg_skill_event } from "./vo/cfg_skill_event";
import { cfg_skill_level } from "./vo/cfg_skill_level";
import { cfg_skill_summon } from "./vo/cfg_skill_summon";
import { cfg_small_game } from "./vo/cfg_small_game";
import { cfg_soldier_game } from "./vo/cfg_soldier_game";
import { cfg_soldier_game_rewards } from "./vo/cfg_soldier_game_rewards";
import { cfg_soul_hero_link_level } from "./vo/cfg_soul_hero_link_level";
import { cfg_soul_hero_link_limit_unlock } from "./vo/cfg_soul_hero_link_limit_unlock";
import { cfg_soul_hero_link_nation } from "./vo/cfg_soul_hero_link_nation";
import { cfg_stage_breed } from "./vo/cfg_stage_breed";
import { cfg_stage_breed_attr } from "./vo/cfg_stage_breed_attr";
import { cfg_stage_copy } from "./vo/cfg_stage_copy";
import { cfg_stage_copy_boss } from "./vo/cfg_stage_copy_boss";
import { cfg_stage_copy_daily_mission } from "./vo/cfg_stage_copy_daily_mission";
import { cfg_stage_copy_misc } from "./vo/cfg_stage_copy_misc";
import { cfg_stage_copy_story } from "./vo/cfg_stage_copy_story";
import { cfg_stage_mission } from "./vo/cfg_stage_mission";
import { cfg_stage_skill_attr } from "./vo/cfg_stage_skill_attr";
import { cfg_stage_skill_type } from "./vo/cfg_stage_skill_type";
import { cfg_star_plan_gift } from "./vo/cfg_star_plan_gift";
import { cfg_star_plan_hero } from "./vo/cfg_star_plan_hero";
import { cfg_star_plan_reward } from "./vo/cfg_star_plan_reward";
import { cfg_story } from "./vo/cfg_story";
import { cfg_story_action } from "./vo/cfg_story_action";
import { cfg_story_actor } from "./vo/cfg_story_actor";
import { cfg_story_bubble } from "./vo/cfg_story_bubble";
import { cfg_story_maze } from "./vo/cfg_story_maze";
import { cfg_story_maze_mission_spoils } from "./vo/cfg_story_maze_mission_spoils";
import { cfg_story_maze_monster } from "./vo/cfg_story_maze_monster";
import { cfg_story_maze_reset } from "./vo/cfg_story_maze_reset";
import { cfg_story_maze_revive } from "./vo/cfg_story_maze_revive";
import { cfg_story_maze_rewards } from "./vo/cfg_story_maze_rewards";
import { cfg_story_maze_shop } from "./vo/cfg_story_maze_shop";
import { cfg_story_maze_theme } from "./vo/cfg_story_maze_theme";
import { cfg_story_siegelord_city_type } from "./vo/cfg_story_siegelord_city_type";
import { cfg_story_siegelord_level } from "./vo/cfg_story_siegelord_level";
import { cfg_story_siegelord_level_reward } from "./vo/cfg_story_siegelord_level_reward";
import { cfg_story_siegelord_misc } from "./vo/cfg_story_siegelord_misc";
import { cfg_story_siegelord_pass_reward } from "./vo/cfg_story_siegelord_pass_reward";
import { cfg_story_tower_battle } from "./vo/cfg_story_tower_battle";
import { cfg_story_tower_battle_monster } from "./vo/cfg_story_tower_battle_monster";
import { cfg_story_tower_battle_reward } from "./vo/cfg_story_tower_battle_reward";
import { cfg_suit_attr } from "./vo/cfg_suit_attr";
import { cfg_supreme_lottery } from "./vo/cfg_supreme_lottery";
import { cfg_svip_pay_gift } from "./vo/cfg_svip_pay_gift";
import { cfg_sys_open_notice } from "./vo/cfg_sys_open_notice";
import { cfg_sys_openlv } from "./vo/cfg_sys_openlv";
import { cfg_sys_use_times } from "./vo/cfg_sys_use_times";
import { cfg_tax } from "./vo/cfg_tax";
import { cfg_tax_reward } from "./vo/cfg_tax_reward";
import { cfg_td_main } from "./vo/cfg_td_main";
import { cfg_td_main_mission } from "./vo/cfg_td_main_mission";
import { cfg_td_main_monster } from "./vo/cfg_td_main_monster";
import { cfg_td_main_pass_mission } from "./vo/cfg_td_main_pass_mission";
import { cfg_td_map } from "./vo/cfg_td_map";
import { cfg_td_monster_talk } from "./vo/cfg_td_monster_talk";
import { cfg_td_trial } from "./vo/cfg_td_trial";
import { cfg_td_trial_monster } from "./vo/cfg_td_trial_monster";
import { cfg_team_boss } from "./vo/cfg_team_boss";
import { cfg_team_xswh_boss } from "./vo/cfg_team_xswh_boss";
import { cfg_team_xswh_gift } from "./vo/cfg_team_xswh_gift";
import { cfg_team_xswh_hurt_rewards } from "./vo/cfg_team_xswh_hurt_rewards";
import { cfg_team_xswh_rank_rewards } from "./vo/cfg_team_xswh_rank_rewards";
import { cfg_tequan } from "./vo/cfg_tequan";
import { cfg_test_tower } from "./vo/cfg_test_tower";
import { cfg_test_tower_extra_reward } from "./vo/cfg_test_tower_extra_reward";
import { cfg_test_tower_skin } from "./vo/cfg_test_tower_skin";
import { cfg_theme_act_famous_lottery_reward } from "./vo/cfg_theme_act_famous_lottery_reward";
import { cfg_theme_act_hero_lottery_show } from "./vo/cfg_theme_act_hero_lottery_show";
import { cfg_theme_act_item } from "./vo/cfg_theme_act_item";
import { cfg_theme_act_rare_lottery_reward } from "./vo/cfg_theme_act_rare_lottery_reward";
import { cfg_theme_act_skin_lottery } from "./vo/cfg_theme_act_skin_lottery";
import { cfg_theme_act_skin_lottery_cost } from "./vo/cfg_theme_act_skin_lottery_cost";
import { cfg_theme_act_wish_lottery } from "./vo/cfg_theme_act_wish_lottery";
import { cfg_theme_act_wish_lottery_item } from "./vo/cfg_theme_act_wish_lottery_item";
import { cfg_theme_act_wish_lottery_show } from "./vo/cfg_theme_act_wish_lottery_show";
import { cfg_tiled_effect } from "./vo/cfg_tiled_effect";
import { cfg_tiled_map } from "./vo/cfg_tiled_map";
import { cfg_time_achievement } from "./vo/cfg_time_achievement";
import { cfg_time_activity_drop } from "./vo/cfg_time_activity_drop";
import { cfg_time_activity_shop } from "./vo/cfg_time_activity_shop";
import { cfg_time_activity_week } from "./vo/cfg_time_activity_week";
import { cfg_tips } from "./vo/cfg_tips";
import { cfg_title } from "./vo/cfg_title";
import { cfg_travel } from "./vo/cfg_travel";
import { cfg_travel_ext } from "./vo/cfg_travel_ext";
import { cfg_treasure_box } from "./vo/cfg_treasure_box";
import { cfg_treasure_box_type } from "./vo/cfg_treasure_box_type";
import { cfg_treasure_energy } from "./vo/cfg_treasure_energy";
import { cfg_treasure_gift } from "./vo/cfg_treasure_gift";
import { cfg_treasure_misc } from "./vo/cfg_treasure_misc";
import { cfg_treasure_refresh_cost } from "./vo/cfg_treasure_refresh_cost";
import { cfg_treasure_worker } from "./vo/cfg_treasure_worker";
import { cfg_trig_skill } from "./vo/cfg_trig_skill";
import { cfg_ui_button_style } from "./vo/cfg_ui_button_style";
import { cfg_ui_preload } from "./vo/cfg_ui_preload";
import { cfg_ui_resident } from "./vo/cfg_ui_resident";
import { cfg_up_star_gift } from "./vo/cfg_up_star_gift";
import { cfg_up_star_reward } from "./vo/cfg_up_star_reward";
import { cfg_vip_daily_mission } from "./vo/cfg_vip_daily_mission";
import { cfg_vip_daily_mission_gift } from "./vo/cfg_vip_daily_mission_gift";
import { cfg_vip_kefu } from "./vo/cfg_vip_kefu";
import { cfg_vip_kefu_review } from "./vo/cfg_vip_kefu_review";
import { cfg_war_flag } from "./vo/cfg_war_flag";
import { cfg_war_flag_facade } from "./vo/cfg_war_flag_facade";
import { cfg_war_flag_level } from "./vo/cfg_war_flag_level";
import { cfg_war_flag_link } from "./vo/cfg_war_flag_link";
import { cfg_war_flag_recycle } from "./vo/cfg_war_flag_recycle";
import { cfg_war_flag_stage } from "./vo/cfg_war_flag_stage";
import { cfg_war_log_mission } from "./vo/cfg_war_log_mission";
import { cfg_war_log_mission_pay_reward } from "./vo/cfg_war_log_mission_pay_reward";
import { cfg_war_log_mission_score_reward } from "./vo/cfg_war_log_mission_score_reward";
import { cfg_wars_honor } from "./vo/cfg_wars_honor";
import { cfg_wars_map_camp } from "./vo/cfg_wars_map_camp";
import { cfg_wars_map_city } from "./vo/cfg_wars_map_city";
import { cfg_wars_map_near_city2 } from "./vo/cfg_wars_map_near_city2";
import { cfg_wars_map_type } from "./vo/cfg_wars_map_type";
import { cfg_wars_misc } from "./vo/cfg_wars_misc";
import { cfg_wars_mission } from "./vo/cfg_wars_mission";
import { cfg_wars_state } from "./vo/cfg_wars_state";
import { cfg_wars_text_const } from "./vo/cfg_wars_text_const";
import { cfg_week_target } from "./vo/cfg_week_target";
import { cfg_week_target_level } from "./vo/cfg_week_target_level";
import { cfg_wing_buff } from "./vo/cfg_wing_buff";
import { cfg_wing_hero } from "./vo/cfg_wing_hero";
import { cfg_wing_hero_skin } from "./vo/cfg_wing_hero_skin";
import { cfg_wing_level } from "./vo/cfg_wing_level";
import { cfg_world_boss_hurt_rewards } from "./vo/cfg_world_boss_hurt_rewards";
import { cfg_world_boss_level } from "./vo/cfg_world_boss_level";
import { cfg_world_map } from "./vo/cfg_world_map";
import { cfg_world_map_2 } from "./vo/cfg_world_map_2";
import { cfg_wxShare } from "./vo/cfg_wxShare";
import { cfg_wxTurn_gift } from "./vo/cfg_wxTurn_gift";
import { cfg_wx_game_club } from "./vo/cfg_wx_game_club";
import { cfg_xswh_boss } from "./vo/cfg_xswh_boss";
import { cfg_xswh_gift } from "./vo/cfg_xswh_gift";
import { cfg_xswh_hurt_rewards } from "./vo/cfg_xswh_hurt_rewards";
import { cfg_xswh_rank_rewards } from "./vo/cfg_xswh_rank_rewards";
import { cfg_ybzk_reward } from "./vo/cfg_ybzk_reward";
import { cfg_zero_buy } from "./vo/cfg_zero_buy";
import { errorCode } from "./vo/errorCode";
import { fightAttr } from "./vo/fightAttr";
import { victoryMacro } from "./vo/victoryMacro";


export class CfgCacheMapMgr {

    static cfg_acc_giftCache: CfgCacheMapBase<cfg_acc_gift[]>;
    static cfg_achievementCache: CfgCacheMapBase<cfg_achievement>;
    static cfg_activity_clientCache: CfgCacheMapBase<cfg_activity_client>;
    static cfg_activity_descCache: CfgCacheMapBase<cfg_activity_desc>;
    static cfg_activity_iconCache: CfgCacheMapBase<cfg_activity_icon>;
    static cfg_activity_iconParentCache: CfgCacheMapBase<cfg_activity_icon[]>;
    static cfg_activity_limit_signCache: CfgCacheMapBase<cfg_activity_limit_sign>;
    static cfg_activity_miscCacheTemp: CfgCacheMapBase<cfg_activity_misc>;
    static cfg_activity_missionCache: CfgCacheMapBase<cfg_activity_mission[]>;
    static cfg_activity_missionAllCache: CfgCacheMapBase<cfg_activity_mission>;
    static cfg_activity_noticeCache: CfgCacheMapBase<cfg_activity_notice>;
    static cfg_activity_pageCache: CfgCacheMapBase<cfg_activity_page>;
    static cfg_activity_yuekaCache: CfgCacheMapBase<cfg_activity_yueka>;
    static cfg_ad_showCache: CfgCacheMapBase<cfg_ad_show>;
    static cfg_agent_reviewCache: CfgCacheMapBase<cfg_agent_review>;
    static cfg_all_pinyin_dictCacheTemp: CfgCacheMapBase<cfg_all_pinyin_dict>;
    static cfg_arenaCacheTemp: CfgCacheMapBase<cfg_arena>;
    static cfg_arena_matchCache: CfgCacheMapBase<cfg_arena_match>;
    static cfg_arena_match_guess_allCache: CfgCacheMapBase<cfg_arena_match_guess>;
    static cfg_arena_max_rewardCache: CfgCacheMapBase<cfg_arena_max_reward>;
    static cfg_arena_rankCache: CfgCacheMapBase<cfg_arena_rank[]>;
    static cfg_arena_rewardCache: CfgCacheMapBase<cfg_arena_reward>;
    static cfg_arena_skip_limitCacheTemp: CfgCacheMapBase<cfg_arena_skip_limit>;
    static cfg_arena_weekly_rewardCache: CfgCacheMapBase<cfg_arena_weekly_reward>;
    static cfg_ares_palaceCache: CfgCacheMapBase<cfg_ares_palace>;
    static cfg_authorized_giftsCache: CfgCacheMapBase<cfg_authorized_gifts>;
    static cfg_bag_pageCallCache: CfgCacheMapBase<cfg_bag_page>;
    static cfg_bai_jiang_giftCache: CfgCacheMapBase<cfg_bai_jiang_gift>;
    static cfg_battle_fly_nameCache: CfgCacheMapBase<cfg_battle_fly_name>;
    static cfg_battle_trial_buffCache: CfgCacheMapBase<cfg_battle_trial_buff>;
    static cfg_battle_trial_buff_listCache: CfgCacheMapBase<cfg_battle_trial_buff[]>;
    static cfg_battle_trial_buff_resetCache: CfgCacheMapBase<cfg_battle_trial_buff_reset>;
    static cfg_battle_trial_guaji_monsterCache: CfgCacheMapBase<cfg_battle_trial_guaji_monster>;
    static cfg_battle_trial_miscCache: CfgCacheMapBase<cfg_battle_trial_misc>;
    static cfg_battle_trial_pass_guanqiaCache: CfgCacheMapBase<cfg_battle_trial_pass_guanqia>;
    static cfg_battle_trial_pass_rewardCache: CfgCacheMapBase<cfg_battle_trial_pass_reward>;
    static cfg_beast_platformCache: CfgCacheMapBase<cfg_beast_platform>;
    static cfg_bingfaCache: CfgCacheMapBase<cfg_bingfa>;
    static cfg_bingfaLevelCache: CfgCacheMapBase<cfg_bingfa[]>;
    static cfg_bingfa_extCache: CfgCacheMapBase<cfg_bingfa_ext>;
    static cfg_bingfa_extBySkillIdCache: CfgCacheMapBase<cfg_bingfa_ext[]>;
    static cfg_bingfa_extBySkillLevelListCache: CfgCacheMapBase<cfg_bingfa_ext[]>;
    static cfg_bingfuCache: CfgCacheMapBase<cfg_bingfu>;
    static cfg_bingfu_discompose_recastCache: CfgCacheMapBase<cfg_bingfu_discompose_recast>;
    static cfg_bingfu_recast_lockCache: CfgCacheMapBase<cfg_bingfu_recast_lock>;
    static cfg_bingfu_refineCache: CfgCacheMapBase<cfg_bingfu_refine>;
    static cfg_bingfu_refine_TargetIdCache: CfgCacheMapBase<cfg_bingfu_refine>;
    static cfg_bingfu_upgradeCache: CfgCacheMapBase<cfg_bingfu_upgrade>;
    static cfg_boat_peak_miscCache: CfgCacheMapBase<cfg_boat_peak_misc>;
    static cfg_boat_peak_rankCache: CfgCacheMapBase<cfg_boat_peak_rank[]>;
    static cfg_boat_peak_timeCache: CfgCacheMapBase<cfg_boat_peak_time>;
    static cfg_boat_peak_time_day_roundCache: CfgCacheMapBase<cfg_boat_peak_time[]>;
    static cfg_boat_peak_time_round_stepCache: CfgCacheMapBase<cfg_boat_peak_time>;
    static cfg_boat_racc_rank_goldCache: CfgCacheMapBase<cfg_boat_racc_rank_gold>;
    static cfg_boat_race_auction_reward_typeCache: CfgCacheMapBase<cfg_boat_race_auction_reward_type>;
    static cfg_boat_race_itemCache: CfgCacheMapBase<cfg_boat_race_item>;
    static cfg_boat_race_miscCache: CfgCacheMapBase<cfg_boat_race_misc>;
    static cfg_buffCache: CfgCacheMapBase<cfg_buff>;
    static cfg_buff_typeCache: CfgCacheMapBase<cfg_buff_type>;
    static cfg_buildingCache: CfgCacheMapBase<cfg_building>;
    static cfg_building_lvCache: CfgCacheMapBase<cfg_building_lv>;
    static cfg_building_missionCache: CfgCacheMapBase<cfg_building_mission>;
    static cfg_building_plot_dialogueCache: CfgCacheMapBase<cfg_building_plot_dialogue>;
    static cfg_buy_timesCache: CfgCacheMapBase<cfg_buy_times[]>;
    static cfg_buy_times_typeCache: CfgCacheMapBase<cfg_buy_times_type>;
    static cfg_cast_soul_activeCache: CfgCacheMapBase<cfg_cast_soul_active>;
    static cfg_casting_soulCache: CfgCacheMapBase<cfg_casting_soul>;
    static cfg_casting_soul_buffCache: CfgCacheMapBase<cfg_casting_soul_buff>;
    static cfg_casting_soul_kind_buffCache: CfgCacheMapBase<cfg_casting_soul_buff[]>;
    static cfg_chapter_dialogCacheTemp: CfgCacheMapBase<cfg_chapter_dialog>;
    static cfg_chapter_scriptCache: CfgCacheMapBase<cfg_chapter_script[]>;
    static cfg_chat_bullet_msgCache: CfgCacheMapBase<cfg_chat_bullet_msg>;
    static cfg_chat_channelCache: CfgCacheMapBase<cfg_chat_channel>;
    static cfg_chat_skinCache: CfgCacheMapBase<cfg_chat_skin>;
    static cfg_chat_skin_widgetCache: CfgCacheMapBase<cfg_chat_skin_widget>;
    static cfg_client_langCache: CfgCacheMapBase<cfg_client_lang>;
    static cfg_client_w3_effectCacheTemp: CfgCacheMapBase<cfg_client_w3_effect>;
    static cfg_client_w3_effect_descCache: CfgCacheMapBase<cfg_client_w3_effect_desc>;
    static cfg_client_w3_skillCacheTemp: CfgCacheMapBase<cfg_client_w3_skill>;
    static cfg_client_w3_skill_descCache: CfgCacheMapBase<cfg_client_w3_skill_desc>;
    static cfg_client_w3_skinCache: CfgCacheMapBase<cfg_client_w3_skin>;
    static cfg_cmdCache: CfgCacheMapBase<cfg_cmd[]>;
    static cfg_code_cliCache: CfgCacheMapBase<cfg_code_cli>;
    static cfg_country_war_miscCache: CfgCacheMapBase<cfg_country_war_misc>;
    static cfg_cross_team_match_typeCache: CfgCacheMapBase<cfg_cross_team_match_type>;
    static cfg_cross_team_match_type_data_listCache: CfgCacheMapBase<cfg_cross_team_match_type[]>;
    static cfg_cross_team_miscCache: CfgCacheMapBase<cfg_cross_team_misc>;
    static cfg_cross_test_tower_openCache: CfgCacheMapBase<cfg_cross_test_tower_open>;
    static cfg_crush_fight_conditionCache: CfgCacheMapBase<cfg_crush_fight_condition>;
    static cfg_crush_fight_missionCache: CfgCacheMapBase<cfg_crush_fight_mission>;
    static cfg_crush_fight_mission_typeCache: CfgCacheMapBase<cfg_crush_fight_mission_type>;
    static cfg_csc_fmsolo_buffCache: CfgCacheMapBase<cfg_csc_fmsolo_buff>;
    static cfg_csc_fmsolo_challengeCache: CfgCacheMapBase<cfg_csc_fmsolo_challenge>;
    static cfg_csc_fmsolo_etcCache: CfgCacheMapBase<cfg_csc_fmsolo_etc>;
    static cfg_csc_fmsolo_logCache: CfgCacheMapBase<cfg_csc_fmsolo_log>;
    static cfg_csc_fmsolo_rewardCache: CfgCacheMapBase<cfg_csc_fmsolo_reward>;
    static cfg_csc_fmsolo_rewardTypeCache: CfgCacheMapBase<cfg_csc_fmsolo_reward[]>;
    static cfg_csc_fmsolo_shop_lvCache: CfgCacheMapBase<cfg_csc_fmsolo_shop_lv>;
    static cfg_csclanCache: CfgCacheMapBase<cfg_csclan>;
    static cfg_csclan_etcCache: CfgCacheMapBase<cfg_csclan_etc>;
    static cfg_csclan_solo_buffCache: CfgCacheMapBase<cfg_csclan_solo_buff>;
    static cfg_csclan_solo_challengeCache: CfgCacheMapBase<cfg_csclan_solo_challenge>;
    static cfg_csclan_solo_etcCache: CfgCacheMapBase<cfg_csclan_solo_etc>;
    static cfg_csclan_solo_logCache: CfgCacheMapBase<cfg_csclan_solo_log>;
    static cfg_csclan_solo_rewardCache: CfgCacheMapBase<cfg_csclan_solo_reward>;
    static cfg_csclan_solo_rewardTypeCache: CfgCacheMapBase<cfg_csclan_solo_reward[]>;
    static cfg_csclan_solo_shop_lvCache: CfgCacheMapBase<cfg_csclan_solo_shop_lv>;
    static cfg_daily_copyCache: CfgCacheMapBase<cfg_daily_copy[]>;
    static cfg_daily_copy_discountCacheTemp: CfgCacheMapBase<cfg_daily_copy_discount>;
    static cfg_daily_copy_monsterCache: CfgCacheMapBase<cfg_daily_copy_monster>;
    static cfg_daily_copy_typeCache: CfgCacheMapBase<cfg_daily_copy_type>;
    static cfg_daily_missionCache: CfgCacheMapBase<cfg_daily_mission>;
    static cfg_daily_mission_giftCache: CfgCacheMapBase<cfg_daily_mission_gift>;
    static cfg_daily_new_discount_rebateCache: CfgCacheMapBase<cfg_daily_new_discount_rebate>;
    static cfg_daily_new_discount_rebateByGroupCache: CfgCacheMapBase<cfg_daily_new_discount_rebate[]>;
    static cfg_daily_payCache: CfgCacheMapBase<cfg_daily_pay>;
    static cfg_dawankaCache: CfgCacheMapBase<cfg_dawanka>;
    static cfg_dawanka_tequanGroupCache: CfgCacheMapBase<cfg_dawanka_tequan[]>;
    static cfg_dawanka_tequanCache: CfgCacheMapBase<cfg_dawanka_tequan>;
    static cfg_day_acc_pay_giftCache: CfgCacheMapBase<cfg_day_acc_pay_gift>;
    static cfg_deputyCache: CfgCacheMapBase<cfg_deputy>;
    static cfg_deputy_levelCache: CfgCacheMapBase<cfg_deputy_level>;
    static cfg_deputy_starCache: CfgCacheMapBase<cfg_deputy_star>;
    static cfg_device_excursion_name1Cache: CfgCacheMapBase<cfg_device_excursion>;
    static cfg_device_excursion_name2Cache: CfgCacheMapBase<cfg_device_excursion>;
    static cfg_divineCache: CfgCacheMapBase<cfg_divine>;
    static cfg_divine_activateCache: CfgCacheMapBase<cfg_divine_activate>;
    static cfg_divine_copyCache: CfgCacheMapBase<cfg_divine_copy>;
    static cfg_divine_strengthenCache: CfgCacheMapBase<cfg_divine_strengthen>;
    static cfg_divine_wearCache: CfgCacheMapBase<cfg_divine_wear>;
    static cfg_dominate_pvpCache: CfgCacheMapBase<cfg_dominate_pvp>;
    static cfg_dominate_pvpGroupCache: CfgCacheMapBase<cfg_dominate_pvp[]>;
    static cfg_dominate_pvp_limitCache: CfgCacheMapBase<cfg_dominate_pvp_limit>;
    static cfg_dominate_pvp_rewardCache: CfgCacheMapBase<cfg_dominate_pvp_reward>;
    static cfg_dominate_pvp_taskCache: CfgCacheMapBase<cfg_dominate_pvp_task>;
    static cfg_dominate_pvp_task_rewardsCache: CfgCacheMapBase<cfg_dominate_pvp_task_rewards>;
    static cfg_drop_groupCache: CfgCacheMapBase<cfg_drop_group[]>;
    static cfg_eight_loginCache: CfgCacheMapBase<cfg_eight_login>;
    static cfg_epic_battle_tipsCache: CfgCacheMapBase<cfg_epic_battle_tips>;
    static cfg_equipCache: CfgCacheMapBase<cfg_equip>;
    static cfg_equipbySuitIdCache: CfgCacheMapBase<cfg_equip[]>;
    static cfg_equip_composeCache: CfgCacheMapBase<cfg_equip_compose>;
    static cfg_equip_compose_target_idCache: CfgCacheMapBase<cfg_equip_compose>;
    static cfg_equip_suitCache: CfgCacheMapBase<cfg_equip_suit[]>;
    static cfg_equip_suit_extCache: CfgCacheMapBase<cfg_equip_suit_ext>;
    static cfg_fail_tipsCache: CfgCacheMapBase<cfg_fail_tips>;
    static cfg_familyCache: CfgCacheMapBase<cfg_family>;
    static cfg_family_active_attrCache: CfgCacheMapBase<cfg_family_active_attr>;
    static cfg_family_active_missionCache: CfgCacheMapBase<cfg_family_active_mission>;
    static cfg_family_bossCache: CfgCacheMapBase<cfg_family_boss>;
    static cfg_family_boss_attrCache: CfgCacheMapBase<cfg_family_boss_attr>;
    static cfg_family_boss_miscCache: CfgCacheMapBase<cfg_family_boss_misc>;
    static cfg_family_boss_rankCache: CfgCacheMapBase<cfg_family_boss_rank[]>;
    static cfg_family_etcCacheTemp: CfgCacheMapBase<cfg_family_etc>;
    static cfg_family_hongbaoCache: CfgCacheMapBase<cfg_family_hongbao>;
    static cfg_family_hongbaoItemCache: CfgCacheMapBase<cfg_family_hongbao>;
    static cfg_family_hongbao_blessingCache: CfgCacheMapBase<cfg_family_hongbao_blessing>;
    static cfg_family_hongbao_rewardCache: CfgCacheMapBase<cfg_family_hongbao_reward[]>;
    static cfg_family_logCache: CfgCacheMapBase<cfg_family_log>;
    static cfg_family_random_nameCache: CfgCacheMapBase<cfg_family_random_name>;
    static cfg_family_scienceCacheTemp: CfgCacheMapBase<cfg_family_science>;
    static cfg_family_science_timesCacheTemp: CfgCacheMapBase<cfg_family_science_times>;
    static cfg_family_signCache: CfgCacheMapBase<cfg_family_sign>;
    static cfg_family_sign_activeCache: CfgCacheMapBase<cfg_family_sign_active>;
    static cfg_fight_showCache: CfgCacheMapBase<cfg_fight_show>;
    static cfg_first_payCache: CfgCacheMapBase<cfg_first_pay>;
    static cfg_fishCache: CfgCacheMapBase<cfg_fish>;
    static cfg_fishByGroupCache: CfgCacheMapBase<cfg_fish[]>;
    static cfg_fish_attrCache: CfgCacheMapBase<cfg_fish_attr>;
    static cfg_fish_colorCache: CfgCacheMapBase<cfg_fish_color>;
    static cfg_fish_fishbowlCache: CfgCacheMapBase<cfg_fish_fishbowl>;
    static cfg_fish_handbookCache: CfgCacheMapBase<cfg_fish_handbook[]>;
    static cfg_fish_handbookByIdCache: CfgCacheMapBase<cfg_fish_handbook>;
    static cfg_fish_help_giftCache: CfgCacheMapBase<cfg_fish_help_gift>;
    static cfg_fish_levelCache: CfgCacheMapBase<cfg_fish_level>;
    static cfg_fish_mapCache: CfgCacheMapBase<cfg_fish_map>;
    static cfg_fish_miscCache: CfgCacheMapBase<cfg_fish_misc>;
    static cfg_fish_missionCache: CfgCacheMapBase<cfg_fish_mission>;
    static cfg_fish_mission_guide_idCache: CfgCacheMapBase<cfg_fish_mission>;
    static cfg_fish_official_positionCache: CfgCacheMapBase<cfg_fish_official_position>;
    static cfg_fish_reshapeCache: CfgCacheMapBase<cfg_fish_reshape>;
    static cfg_fish_resourcesCache: CfgCacheMapBase<cfg_fish_resources>;
    static cfg_fish_resourcesByHandbookTypeCache: CfgCacheMapBase<cfg_fish_resources[]>;
    static cfg_fish_rod_levelCache: CfgCacheMapBase<cfg_fish_rod_level>;
    static cfg_fish_slotCache: CfgCacheMapBase<cfg_fish_slot>;
    static cfg_fish_stageCache: CfgCacheMapBase<cfg_fish_stage>;
    static cfg_fly_font_typeCache: CfgCacheMapBase<cfg_fly_font_type>;
    static cfg_fuli_signCache: CfgCacheMapBase<cfg_fuli_sign[]>;
    static cfg_fuli_sign_accCache: CfgCacheMapBase<cfg_fuli_sign_acc>;
    static cfg_fuli_sign_dayCache: CfgCacheMapBase<cfg_fuli_sign_day>;
    static cfg_fuli_tokenCache: CfgCacheMapBase<cfg_fuli_token>;
    static cfg_fuli_tokenByLevelCache: CfgCacheMapBase<cfg_fuli_token[]>;
    static cfg_fuli_token_typeCache: CfgCacheMapBase<cfg_fuli_token_type>;
    static cfg_fuli_yuekaCache: CfgCacheMapBase<cfg_fuli_yueka>;
    static cfg_game_descCache: CfgCacheMapBase<cfg_game_desc>;
    static cfg_game_desc_2CacheTemp: CfgCacheMapBase<cfg_game_desc_2>;
    static cfg_game_desc_3Cache: CfgCacheMapBase<cfg_game_desc_3[]>;
    static cfg_general_pass_missionCache: CfgCacheMapBase<cfg_general_pass_mission>;
    static cfg_general_pass_missionBypass_typeandturnCache: CfgCacheMapBase<cfg_general_pass_mission[]>;
    static cfg_general_pass_rewardCache: CfgCacheMapBase<cfg_general_pass_reward>;
    static cfg_general_pass_rewardByTurnCache: CfgCacheMapBase<cfg_general_pass_reward[]>;
    static cfg_general_pass_rewardIdCache: CfgCacheMapBase<cfg_general_pass_reward>;
    static cfg_general_pass_typeCache: CfgCacheMapBase<cfg_general_pass_type>;
    static cfg_general_pass_typeByprocess_typeCache: CfgCacheMapBase<cfg_general_pass_type[]>;
    static cfg_giftCache: CfgCacheMapBase<cfg_gift>;
    static cfg_god_equipCache: CfgCacheMapBase<cfg_god_equip>;
    static cfg_god_equip_starCache: CfgCacheMapBase<cfg_god_equip[]>;
    static cfg_god_equip_composeCache: CfgCacheMapBase<cfg_god_equip_compose>;
    static cfg_god_equip_compose_itemCache: CfgCacheMapBase<cfg_god_equip_compose>;
    static cfg_god_equip_convertCache: CfgCacheMapBase<cfg_god_equip_convert>;
    static cfg_god_equip_enchantCache: CfgCacheMapBase<cfg_god_equip_enchant>;
    static cfg_god_equip_enchant_costCache: CfgCacheMapBase<cfg_god_equip_enchant_cost>;
    static cfg_god_equip_suitCache: CfgCacheMapBase<cfg_god_equip_suit>;
    static cfg_god_equip_suitByStarCache: CfgCacheMapBase<cfg_god_equip_suit[]>;
    static cfg_god_equip_typeCache: CfgCacheMapBase<cfg_god_equip_type>;
    static cfg_god_trialCache: CfgCacheMapBase<cfg_god_trial>;
    static cfg_god_trial_buffCache: CfgCacheMapBase<cfg_god_trial_buff>;
    static cfg_god_weaponCache: CfgCacheMapBase<cfg_god_weapon>;
    static cfg_god_weapon_levelCache: CfgCacheMapBase<cfg_god_weapon_level>;
    static cfg_god_weapon_missionCache: CfgCacheMapBase<cfg_god_weapon_mission>;
    static cfg_god_weapon_missionTypeCache: CfgCacheMapBase<cfg_god_weapon_mission[]>;
    static cfg_god_weapon_refineCache: CfgCacheMapBase<cfg_god_weapon_refine>;
    static cfg_god_weapon_plan_refineCache: CfgCacheMapBase<cfg_god_weapon_refine>;
    static cfg_god_weapon_skillCache: CfgCacheMapBase<cfg_god_weapon_skill>;
    static cfg_god_weapon_skill_attrTypeCache: CfgCacheMapBase<cfg_god_weapon_skill_attr[]>;
    static cfg_god_weapon_soulCache: CfgCacheMapBase<cfg_god_weapon_soul>;
    static cfg_god_weapon_starCache: CfgCacheMapBase<cfg_god_weapon_star>;
    static cfg_gray_pinyinCacheTemp: CfgCacheMapBase<cfg_gray_pinyin>;
    static cfg_grow_tipsCache: CfgCacheMapBase<cfg_grow_tips>;
    static cfg_grow_tipsTagMap: CfgCacheMapBase<cfg_grow_tips[]>;
    static cfg_guaji_box_timeCache: CfgCacheMapBase<cfg_guaji_box_time>;
    static cfg_guaji_monsterCache: CfgCacheMapBase<cfg_guaji_monster>;
    static cfg_guaji_quick_navigationCache: CfgCacheMapBase<cfg_guaji_quick_navigation>;
    static cfg_guanduCache: CfgCacheMapBase<cfg_guandu[]>;
    static cfg_guandu_answerCache: CfgCacheMapBase<cfg_guandu_answer>;
    static cfg_guandu_choseCache: CfgCacheMapBase<cfg_guandu_chose>;
    static cfg_guandu_floorCache: CfgCacheMapBase<cfg_guandu_floor>;
    static cfg_guandu_missionCache: CfgCacheMapBase<cfg_guandu_mission>;
    static cfg_guandu_shopCache: CfgCacheMapBase<cfg_guandu_shop>;
    static cfg_guide_helperCache: CfgCacheMapBase<cfg_guide_helper[]>;
    static cfg_guide_helper_1Cache: CfgCacheMapBase<cfg_guide_helper_1[]>;
    static cfg_guide_helper_2Cache: CfgCacheMapBase<cfg_guide_helper_2[]>;
    static cfg_guide_helper_3Cache: CfgCacheMapBase<cfg_guide_helper_3[]>;
    static cfg_guide_helper_4Cache: CfgCacheMapBase<cfg_guide_helper_4[]>;
    static cfg_guide_helper_game_1Cache: CfgCacheMapBase<cfg_guide_helper_game_1[]>;
    static cfg_guide_helper_m2Cache: CfgCacheMapBase<cfg_guide_helper_m2[]>;
    static cfg_guide_helper_m3Cache: CfgCacheMapBase<cfg_guide_helper_m3[]>;
    static cfg_guide_missionCache: CfgCacheMapBase<cfg_guide_mission>;
    static cfg_guide_uinameCache: CfgCacheMapBase<cfg_guide_mission>;
    static guideRearIdsCache: CfgCacheMapBase<cfg_guide_mission>;
    static cfg_guide_mission_1Cache: CfgCacheMapBase<cfg_guide_mission_1>;
    static cfg_guide_uiname_1Cache: CfgCacheMapBase<cfg_guide_mission_1>;
    static guideRearIds_1Cache: CfgCacheMapBase<cfg_guide_mission_1>;
    static cfg_guide_mission_2Cache: CfgCacheMapBase<cfg_guide_mission_2>;
    static cfg_guide_uiname_2Cache: CfgCacheMapBase<cfg_guide_mission_2>;
    static guideRearIds_2Cache: CfgCacheMapBase<cfg_guide_mission_2>;
    static cfg_guide_mission_3Cache: CfgCacheMapBase<cfg_guide_mission_3>;
    static cfg_guide_uiname_3Cache: CfgCacheMapBase<cfg_guide_mission_3>;
    static guideRearIds_3Cache: CfgCacheMapBase<cfg_guide_mission_3>;
    static cfg_guide_mission_4Cache: CfgCacheMapBase<cfg_guide_mission_4>;
    static cfg_guide_uiname_4Cache: CfgCacheMapBase<cfg_guide_mission_4>;
    static guideRearIds_4Cache: CfgCacheMapBase<cfg_guide_mission_4>;
    static cfg_guide_mission_game_1Cache: CfgCacheMapBase<cfg_guide_mission_game_1>;
    static cfg_guide_uiname_game_1Cache: CfgCacheMapBase<cfg_guide_mission_game_1>;
    static guideRearIds_game_1Cache: CfgCacheMapBase<cfg_guide_mission_game_1>;
    static cfg_guide_mission_m2Cache: CfgCacheMapBase<cfg_guide_mission_m2>;
    static cfg_guide_uiname_m2Cache: CfgCacheMapBase<cfg_guide_mission_m2>;
    static guideRearIds_m2Cache: CfgCacheMapBase<cfg_guide_mission_m2>;
    static cfg_guide_mission_m3Cache: CfgCacheMapBase<cfg_guide_mission_m3>;
    static cfg_guide_uiname_m3Cache: CfgCacheMapBase<cfg_guide_mission_m3>;
    static guideRearIds_m3Cache: CfgCacheMapBase<cfg_guide_mission_m3>;
    static cfg_guide_reviewCache: CfgCacheMapBase<cfg_guide_review>;
    static cfg_guide_storyCache: CfgCacheMapBase<cfg_guide_story>;
    static cfg_hero_attr_addition_careerCache: CfgCacheMapBase<cfg_hero_attr_addition[]>;
    static cfg_hero_attr_addition_nationCache: CfgCacheMapBase<cfg_hero_attr_addition[]>;
    static cfg_hero_attr_sourceCache: CfgCacheMapBase<cfg_hero_attr_source>;
    static cfg_hero_bagCache: CfgCacheMapBase<cfg_hero_bag>;
    static cfg_hero_baseCache: CfgCacheMapBase<cfg_hero_base>;
    static cfg_hero_base_chipIdCache: CfgCacheMapBase<cfg_hero_base>;
    static cfg_hero_base_completeIdCache: CfgCacheMapBase<cfg_hero_base>;
    static cfg_hero_cheerCache: CfgCacheMapBase<cfg_hero_cheer>;
    static cfg_hero_cheerByNationCache: CfgCacheMapBase<cfg_hero_cheer[]>;
    static cfg_hero_cheer_bonusCache: CfgCacheMapBase<cfg_hero_cheer_bonus>;
    static cfg_hero_cheer_levelCache: CfgCacheMapBase<cfg_hero_cheer_level>;
    static cfg_hero_cheer_unlockCache: CfgCacheMapBase<cfg_hero_cheer_unlock>;
    static cfg_hero_chip_starCache: CfgCacheMapBase<cfg_hero_chip_star>;
    static cfg_hero_come_missionCache: CfgCacheMapBase<cfg_hero_come_mission>;
    static cfg_hero_come_missionTypeCache: CfgCacheMapBase<cfg_hero_come_mission[]>;
    static cfg_hero_convertCache: CfgCacheMapBase<cfg_hero_convert>;
    static cfg_hero_convert_weightCache: CfgCacheMapBase<cfg_hero_convert_weight[]>;
    static cfg_hero_cost_planCache: CfgCacheMapBase<cfg_hero_cost_plan>;
    static cfg_hero_evolve_skillCache: CfgCacheMapBase<cfg_hero_evolve_skill>;
    static cfg_hero_handbook_descCache: CfgCacheMapBase<cfg_hero_handbook_desc>;
    static cfg_hero_levelCache: CfgCacheMapBase<cfg_hero_level>;
    static cfg_hero_level_limitCache: CfgCacheMapBase<cfg_hero_level_limit>;
    static cfg_hero_level_limit_typeCache: CfgCacheMapBase<cfg_hero_level_limit>;
    static cfg_hero_nationCache: CfgCacheMapBase<cfg_hero_nation>;
    static cfg_hero_pass_missionCache: CfgCacheMapBase<cfg_hero_pass_mission>;
    static cfg_hero_pass_rewardCache: CfgCacheMapBase<cfg_hero_pass_reward>;
    static cfg_hero_pass_rewardByTurnCache: CfgCacheMapBase<cfg_hero_pass_reward[]>;
    static cfg_hero_recommend_preCache: CfgCacheMapBase<cfg_hero_recommend_pre>;
    static cfg_hero_recycleCache: CfgCacheMapBase<cfg_hero_recycle>;
    static cfg_hero_recycle_changeCacheTemp: CfgCacheMapBase<cfg_hero_recycle_change>;
    static cfg_hero_recycle_change_star_stageCacheTemp: CfgCacheMapBase<cfg_hero_recycle_change_star_stage>;
    static cfg_hero_recycleCaches: CfgCacheMapBase<cfg_hero_recycle_res>;
    static cfg_hero_recycle_special_switchCache: CfgCacheMapBase<cfg_hero_recycle_special_switch>;
    static cfg_hero_resonate_dhyanaCache: CfgCacheMapBase<cfg_hero_resonate_dhyana>;
    static cfg_hero_resonate_duduCache: CfgCacheMapBase<cfg_hero_resonate_dudu>;
    static cfg_hero_resonate_dudu_levelCache: CfgCacheMapBase<cfg_hero_resonate_dudu_level>;
    static cfg_hero_resonate_fiveCache: CfgCacheMapBase<cfg_hero_resonate_five>;
    static cfg_hero_skinCache: CfgCacheMapBase<cfg_hero_skin>;
    static cfg_hero_skin_listCache: CfgCacheMapBase<cfg_hero_skin[]>;
    static cfg_hero_skinByIconCaChe: CfgCacheMapBase<cfg_hero_skin>;
    static cfg_hero_skin_levelCache: CfgCacheMapBase<cfg_hero_skin_level>;
    static cfg_hero_skin_level_listCache: CfgCacheMapBase<cfg_hero_skin_level[]>;
    static cfg_hero_skin_attriCache: CfgCacheMapBase<cfg_hero_skin_level>;
    static cfg_hero_stageCache: CfgCacheMapBase<cfg_hero_stage>;
    static cfg_hero_stage_limitCache: CfgCacheMapBase<cfg_hero_stage_limit>;
    static cfg_hero_starCache: CfgCacheMapBase<cfg_hero_star[]>;
    static cfg_hero_starCache2: CfgCacheMapBase<cfg_hero_star>;
    static cfg_hero_star_attrCache: CfgCacheMapBase<cfg_hero_star_attr>;
    static cfg_hero_star_limitCache: CfgCacheMapBase<cfg_hero_star_limit>;
    static cfg_hero_star_limit_listCache: CfgCacheMapBase<cfg_hero_star_limit[]>;
    static cfg_hero_star_stageCache: CfgCacheMapBase<cfg_hero_star_stage[]>;
    static cfg_hero_star_stageCache2: CfgCacheMapBase<cfg_hero_star_stage>;
    static cfg_hero_star_stage_attrCache: CfgCacheMapBase<cfg_hero_star_stage_attr>;
    static cfg_hero_star_stage_attr_listCache: CfgCacheMapBase<cfg_hero_star_stage_attr[]>;
    static cfg_hero_strengthenCache: CfgCacheMapBase<cfg_hero_strengthen>;
    static cfg_hero_upgrade_tipsCache: CfgCacheMapBase<cfg_hero_upgrade_tips>;
    static cfg_hero_zhouyinCache: CfgCacheMapBase<cfg_hero_zhouyin>;
    static cfg_hunt_buyCache: CfgCacheMapBase<cfg_hunt_buy>;
    static cfg_hunt_costCache: CfgCacheMapBase<cfg_hunt_cost>;
    static cfg_hunt_descCache: CfgCacheMapBase<cfg_hunt_desc[]>;
    static cfg_hunt_giftCache: CfgCacheMapBase<cfg_hunt_gift>;
    static cfg_hunt_rewards_showCache: CfgCacheMapBase<cfg_hunt_rewards_show>;
    static cfg_huoqutujingCache: CfgCacheMapBase<cfg_huoqutujing[]>;
    static cfg_hzzd_achievementCache: CfgCacheMapBase<cfg_hzzd_achievement>;
    static cfg_hzzd_eventCache: CfgCacheMapBase<cfg_hzzd_event>;
    static cfg_hzzd_kills_rewardCache: CfgCacheMapBase<cfg_hzzd_kills_reward>;
    static cfg_hzzd_miscCache: CfgCacheMapBase<cfg_hzzd_misc>;
    static cfg_i18n_langCache: CfgCacheMapBase<cfg_i18n_lang>;
    static cfg_i18n_tsCache: CfgCacheMapBase<cfg_i18n_ts>;
    static cfg_i18n_uiCache: CfgCacheMapBase<cfg_i18n_ui>;
    static cfg_ingenious_planCache: CfgCacheMapBase<cfg_ingenious_plan>;
    static cfg_ingenious_planTypeIdCache: CfgCacheMapBase<cfg_ingenious_plan>;
    static cfg_ingenious_planGroup: CfgCacheMapBase<cfg_ingenious_plan[]>;
    static cfg_ingenious_planIdGroup: CfgCacheMapBase<cfg_ingenious_plan[]>;
    static cfg_ingenious_plan_composeCache: CfgCacheMapBase<cfg_ingenious_plan_compose>;
    static cfg_ingenious_plan_convertCache: CfgCacheMapBase<cfg_ingenious_plan_convert>;
    static cfg_ingenious_plan_levelCache: CfgCacheMapBase<cfg_ingenious_plan_level>;
    static cfg_ingenious_plan_stageCache: CfgCacheMapBase<cfg_ingenious_plan_stage>;
    static cfg_ingenious_plan_starCache: CfgCacheMapBase<cfg_ingenious_plan_star>;
    static cfg_ip_setCache: CfgCacheMapBase<cfg_ip_set>;
    static cfg_itemCache: CfgCacheMapBase<cfg_item>;
    static cfg_itemKindCache: CfgCacheMapBase<cfg_item[]>;
    static cfg_itemAttrCache: CfgCacheMapBase<cfg_item>;
    static cfg_item_composeCache: CfgCacheMapBase<cfg_item_compose>;
    static cfg_item_compose_target_idCache: CfgCacheMapBase<cfg_item_compose>;
    static cfg_item_time_clientCache: CfgCacheMapBase<cfg_item_time_client>;
    static cfg_large_peak_agentCache: CfgCacheMapBase<cfg_large_peak_agent>;
    static cfg_large_peak_battle_table_allCache: CfgCacheMapBase<cfg_large_peak_battle_table>;
    static cfg_large_peak_battle_tableCache: CfgCacheMapBase<cfg_large_peak_battle_table>;
    static cfg_large_peak_battle_table_groupCache: CfgCacheMapBase<cfg_large_peak_battle_table[]>;
    static cfg_large_peak_battle_table_key_groupCache: CfgCacheMapBase<cfg_large_peak_battle_table[]>;
    static cfg_large_peak_miscCache: CfgCacheMapBase<cfg_large_peak_misc>;
    static cfg_large_peak_rankCache: CfgCacheMapBase<cfg_large_peak_rank[]>;
    static cfg_large_peak_seasonCache: CfgCacheMapBase<cfg_large_peak_season>;
    static cfg_large_peak_timeCache: CfgCacheMapBase<cfg_large_peak_time>;
    static cfg_large_peak_time_listCache: CfgCacheMapBase<cfg_large_peak_time[]>;
    static cfg_lazy_loadCacheTemp: CfgCacheMapBase<cfg_lazy_load>;
    static cfg_lcqs_acc_star_rewardCache: CfgCacheMapBase<cfg_lcqs_acc_star_reward[]>;
    static cfg_lcqs_chapter_openCache: CfgCacheMapBase<cfg_lcqs_chapter_open>;
    static cfg_lcqs_floor_rewardCache: CfgCacheMapBase<cfg_lcqs_floor_reward[]>;
    static cfg_lcqs_missionCache: CfgCacheMapBase<cfg_lcqs_mission>;
    static cfg_levelCache: CfgCacheMapBase<cfg_level>;
    static cfg_level_giftCache: CfgCacheMapBase<cfg_level_gift>;
    static cfg_limit_hero_skin_chip_exchangeCaches: CfgCacheMapBase<cfg_limit_hero_skin_chip_exchange>;
    static cfg_lineup_buffCache: CfgCacheMapBase<cfg_lineup_buff>;
    static cfg_lineup_Buff_iconCache: CfgCacheMapBase<cfg_lineup_buff_icon>;
    static cfg_lineup_career_ruleCache: CfgCacheMapBase<cfg_lineup_career_rule>;
    static cfg_lineup_numCache: CfgCacheMapBase<cfg_lineup_num>;
    static cfg_lineup_recommendCache: CfgCacheMapBase<cfg_lineup_recommend>;
    static cfg_lineup_styleCache: CfgCacheMapBase<cfg_lineup_style>;
    static cfg_load_tipsCache: CfgCacheMapBase<cfg_load_tips>;
    static cfg_login_activityCache: CfgCacheMapBase<cfg_login_activity>;
    static cfg_login_activity_roundCache: CfgCacheMapBase<cfg_login_activity_round>;
    static cfg_lord_activationCache: CfgCacheMapBase<cfg_lord_activation>;
    static cfg_lord_baseCache: CfgCacheMapBase<cfg_lord_base>;
    static cfg_lord_campCache: CfgCacheMapBase<cfg_lord_camp>;
    static cfg_lord_campByGradeCache: CfgCacheMapBase<cfg_lord_camp[]>;
    static cfg_lord_campByNeedCardStageCache: CfgCacheMapBase<cfg_lord_camp[]>;
    static cfg_lord_campByCamp: CfgCacheMapBase<cfg_lord_camp[]>;
    static cfg_lord_campBySkill_id: CfgCacheMapBase<cfg_lord_camp>;
    static cfg_lord_campByStageAndCamp: CfgCacheMapBase<cfg_lord_camp>;
    static cfg_lord_exchangeCache: CfgCacheMapBase<cfg_lord_exchange>;
    static cfg_lord_skillCache: CfgCacheMapBase<cfg_lord_skill[]>;
    static cfg_lord_skillByType: CfgCacheMapBase<cfg_lord_skill[]>;
    static cfg_lord_skill_enhanceCache: CfgCacheMapBase<cfg_lord_skill_enhance>;
    static cfg_lord_starCache: CfgCacheMapBase<cfg_lord_star>;
    static cfg_lord_suit_composeCache: CfgCacheMapBase<cfg_lord_suit_compose>;
    static cfg_lord_suit_selectCache: CfgCacheMapBase<cfg_lord_suit_select>;
    static cfg_lord_treasureCache: CfgCacheMapBase<cfg_lord_treasure>;
    static cfg_lord_treasure_entryCache: CfgCacheMapBase<cfg_lord_treasure_entry>;
    static cfg_lord_treasure_entryByEntry_idCache: CfgCacheMapBase<cfg_lord_treasure_entry[]>;
    static cfg_lord_treasure_forgeCache: CfgCacheMapBase<cfg_lord_treasure_forge>;
    static cfg_lord_treasure_forgeByEntry_idCache: CfgCacheMapBase<cfg_lord_treasure_forge[]>;
    static cfg_lord_treasure_levelCache: CfgCacheMapBase<cfg_lord_treasure_level>;
    static cfg_lord_treasure_levelBytreasure_idCache: CfgCacheMapBase<cfg_lord_treasure_level[]>;
    static cfg_lottery_day_limitCache: CfgCacheMapBase<cfg_lottery_day_limit>;
    static cfg_lottery_extCache: CfgCacheMapBase<cfg_lottery_ext>;
    static cfg_lottery_nationCache: CfgCacheMapBase<cfg_lottery_nation[]>;
    static cfg_lottery_nation_timesCache: CfgCacheMapBase<cfg_lottery_nation_times>;
    static cfg_lottery_scoreCache: CfgCacheMapBase<cfg_lottery_score>;
    static cfg_lottery_showCache: CfgCacheMapBase<cfg_lottery_show[]>;
    static cfg_lottery_timesCache: CfgCacheMapBase<cfg_lottery_times>;
    static cfg_lottery_weightCache: CfgCacheMapBase<cfg_lottery_weight[]>;
    static cfg_main_battleCache: CfgCacheMapBase<cfg_main_battle>;
    static cfg_main_battle_groupCache: CfgCacheMapBase<cfg_main_battle[]>;
    static cfg_main_battle_boxCache: CfgCacheMapBase<cfg_main_battle_box>;
    static cfg_main_battle_boxByItemIdCache: CfgCacheMapBase<cfg_main_battle_box>;
    static cfg_main_battle_box_levelCache: CfgCacheMapBase<cfg_main_battle_box_level>;
    static cfg_main_battle_box_rewardCache: CfgCacheMapBase<cfg_main_battle_box_reward>;
    static cfg_main_battle_box_rewardMapCache: CfgCacheMapBase<cfg_main_battle_box_reward>;
    static cfg_main_battle_box_rewardGrpCache: CfgCacheMapBase<cfg_main_battle_box_reward[]>;
    static cfg_main_battle_box_tequanCache: CfgCacheMapBase<cfg_main_battle_box_tequan>;
    static cfg_main_battle_fetchCache: CfgCacheMapBase<cfg_main_battle_fetch>;
    static cfg_main_battle_hangingCache: CfgCacheMapBase<cfg_main_battle_hanging>;
    static cfg_main_battle_missionCache: CfgCacheMapBase<cfg_main_battle_mission>;
    static cfg_mapCache: CfgCacheMapBase<cfg_map>;
    static cfg_map_itemCache: CfgCacheMapBase<cfg_map_item>;
    static cfg_map_itemGrpCache: CfgCacheMapBase<cfg_map_item[]>;
    static cfg_map_item_typeCache: CfgCacheMapBase<cfg_map_item_type>;
    static cfg_map_typeCache: CfgCacheMapBase<cfg_map_type>;
    static cfg_master_cardCache: CfgCacheMapBase<cfg_master_card>;
    static cfg_master_cardByGroupCache: CfgCacheMapBase<cfg_master_card[]>;
    static cfg_master_card_attrCache: CfgCacheMapBase<cfg_master_card_attr>;
    static cfg_master_card_colorCache: CfgCacheMapBase<cfg_master_card_color>;
    static cfg_master_card_drum_levelCache: CfgCacheMapBase<cfg_master_card_drum_level>;
    static cfg_master_card_first_payCache: CfgCacheMapBase<cfg_master_card_first_pay>;
    static cfg_master_card_help_giftCache: CfgCacheMapBase<cfg_master_card_help_gift>;
    static cfg_master_card_miscCache: CfgCacheMapBase<cfg_master_card_misc>;
    static cfg_master_card_missionCache: CfgCacheMapBase<cfg_master_card_mission>;
    static cfg_master_card_mission_guide_idCache: CfgCacheMapBase<cfg_master_card_mission>;
    static cfg_master_card_officialCache: CfgCacheMapBase<cfg_master_card_official>;
    static cfg_master_card_official_positionCache: CfgCacheMapBase<cfg_master_card_official_position>;
    static cfg_master_card_reshapeCache: CfgCacheMapBase<cfg_master_card_reshape>;
    static cfg_master_card_slotCache: CfgCacheMapBase<cfg_master_card_slot>;
    static cfg_master_card_stageCache: CfgCacheMapBase<cfg_master_card_stage>;
    static cfg_master_talent_scienceCache: CfgCacheMapBase<cfg_master_talent_science>;
    static cfg_master_talent_science_iconCache: CfgCacheMapBase<cfg_master_talent_science_icon>;
    static cfg_match_typeCache: CfgCacheMapBase<cfg_match_type>;
    static cfg_match_type_teamCache: CfgCacheMapBase<cfg_match_type_team>;
    static cfg_match_type_team_listCache: CfgCacheMapBase<cfg_match_type_team[]>;
    static cfg_mazeCache: CfgCacheMapBase<cfg_maze>;
    static cfg_maze_diff_rewardsCache: CfgCacheMapBase<cfg_maze_diff_rewards>;
    static cfg_maze_mission_spoilsCache: CfgCacheMapBase<cfg_maze_mission_spoils>;
    static cfg_maze_monsterCache: CfgCacheMapBase<cfg_maze_monster[]>;
    static cfg_maze_resetCache: CfgCacheMapBase<cfg_maze_reset>;
    static cfg_maze_reviveCache: CfgCacheMapBase<cfg_maze_revive>;
    static cfg_maze_shopCache: CfgCacheMapBase<cfg_maze_shop>;
    static cfg_maze_themeCache: CfgCacheMapBase<cfg_maze_theme>;
    static cfg_medalCache: CfgCacheMapBase<cfg_medal>;
    static cfg_medalBytype: CfgCacheMapBase<cfg_medal[]>;
    static cfg_medalByidex: CfgCacheMapBase<cfg_medal[]>;
    static cfg_medalBySpmedal: CfgCacheMapBase<cfg_medal[]>;
    static cfg_medalByclassification: CfgCacheMapBase<cfg_medal[]>;
    static cfg_microterminal_openCache: CfgCacheMapBase<cfg_microterminal_open>;
    static cfg_microterminal_signCache: CfgCacheMapBase<cfg_microterminal_sign[]>;
    static cfg_misc_configCache: CfgCacheMapBase<cfg_misc_config>;
    static cfg_mission_shopCache: CfgCacheMapBase<cfg_mission_shop>;
    static cfg_mission_shop_clientCache: CfgCacheMapBase<cfg_mission_shop_client>;
    static cfg_mock_pvp_databaseCache: CfgCacheMapBase<cfg_mock_pvp_database>;
    static cfg_mock_pvp_hero_baseCache: CfgCacheMapBase<cfg_mock_pvp_hero_base>;
    static cfg_mock_pvp_hero_baseByNationCache: CfgCacheMapBase<cfg_mock_pvp_hero_base[]>;
    static cfg_mock_pvp_hero_baseByTypeIdCache: CfgCacheMapBase<cfg_mock_pvp_hero_base>;
    static cfg_mock_pvp_limitCache: CfgCacheMapBase<cfg_mock_pvp_limit>;
    static cfg_mock_pvp_miscCache: CfgCacheMapBase<cfg_mock_pvp_misc>;
    static cfg_mock_pvp_missionCache: CfgCacheMapBase<cfg_mock_pvp_mission>;
    static cfg_modular_activity_blessCache: CfgCacheMapBase<cfg_modular_activity_bless>;
    static cfg_modular_activity_brickCache: CfgCacheMapBase<cfg_modular_activity_brick>;
    static cfg_modular_activity_carnival_linkCache: CfgCacheMapBase<cfg_modular_activity_carnival_link>;
    static cfg_modular_activity_client_settingCache: CfgCacheMapBase<cfg_modular_activity_client_setting>;
    static cfg_modular_activity_client_settingGroupCache: CfgCacheMapBase<cfg_modular_activity_client_setting[]>;
    static cfg_modular_activity_compose_listCache: CfgCacheMapBase<cfg_modular_activity_compose_list[]>;
    static cfg_modular_activity_compose_listClientKeyCache: CfgCacheMapBase<cfg_modular_activity_compose_list[]>;
    static cfg_modular_activity_customized_giftCache: CfgCacheMapBase<cfg_modular_activity_customized_gift>;
    static cfg_modular_activity_customized_giftByplayidCache: CfgCacheMapBase<cfg_modular_activity_customized_gift[]>;
    static cfg_modular_activity_diceCache: CfgCacheMapBase<cfg_modular_activity_dice[]>;
    static cfg_modular_activity_dice_bossCache: CfgCacheMapBase<cfg_modular_activity_dice_boss>;
    static cfg_modular_activity_dice_bossGroupCache: CfgCacheMapBase<cfg_modular_activity_dice_boss[]>;
    static cfg_modular_activity_dice_client_diffCache: CfgCacheMapBase<cfg_modular_activity_dice_client_diff>;
    static cfg_modular_activity_dice_miscCache: CfgCacheMapBase<cfg_modular_activity_dice_misc>;
    static cfg_modular_activity_dropCache: CfgCacheMapBase<cfg_modular_activity_drop>;
    static cfg_modular_activity_drop_showCache: CfgCacheMapBase<cfg_modular_activity_drop_show>;
    static cfg_modular_activity_exchangeCache: CfgCacheMapBase<cfg_modular_activity_exchange>;
    static cfg_modular_activity_exchangeGrpCache: CfgCacheMapBase<cfg_modular_activity_exchange[]>;
    static cfg_modular_activity_festival_wishCache: CfgCacheMapBase<cfg_modular_activity_festival_wish[]>;
    static cfg_modular_activity_festival_wish_floorCache: CfgCacheMapBase<cfg_modular_activity_festival_wish[]>;
    static cfg_modular_activity_festival_wish_chooseCache: CfgCacheMapBase<cfg_modular_activity_festival_wish_choose[]>;
    static cfg_modular_activity_festival_wish_chooseListCache: CfgCacheMapBase<cfg_modular_activity_festival_wish_choose[]>;
    static cfg_modular_activity_festival_wish_costCache: CfgCacheMapBase<cfg_modular_activity_festival_wish_cost>;
    static cfg_modular_activity_free_switchCache: CfgCacheMapBase<cfg_modular_activity_free_switch>;
    static cfg_modular_activity_general_pass_vipCache: CfgCacheMapBase<cfg_modular_activity_general_pass_vip>;
    static cfg_modular_activity_general_pass_vipByPlanidCache: CfgCacheMapBase<cfg_modular_activity_general_pass_vip[]>;
    static cfg_modular_activity_hero_challengeCache: CfgCacheMapBase<cfg_modular_activity_hero_challenge>;
    static cfg_modular_activity_hero_challengeByPlanidCache: CfgCacheMapBase<cfg_modular_activity_hero_challenge[]>;
    static cfg_modular_activity_holiday_welfare_rewardCache: CfgCacheMapBase<cfg_modular_activity_holiday_welfare_reward>;
    static cfg_modular_activity_hunt_costCache: CfgCacheMapBase<cfg_modular_activity_hunt_cost>;
    static cfg_modular_activity_hunt_cost_listCache: CfgCacheMapBase<cfg_modular_activity_hunt_cost[]>;
    static cfg_modular_activity_hunt_descCache: CfgCacheMapBase<cfg_modular_activity_hunt_desc[]>;
    static cfg_modular_activity_hunt_miscCache: CfgCacheMapBase<cfg_modular_activity_hunt_misc>;
    static cfg_modular_activity_huoqutujingCache: CfgCacheMapBase<cfg_modular_activity_huoqutujing[]>;
    static cfg_modular_activity_iconCache: CfgCacheMapBase<cfg_modular_activity_icon>;
    static cfg_modular_activity_loginCache: CfgCacheMapBase<cfg_modular_activity_login>;
    static cfg_modular_activity_lottery_targetCache: CfgCacheMapBase<cfg_modular_activity_lottery_target>;
    static cfg_modular_activity_lottery_timesCache: CfgCacheMapBase<cfg_modular_activity_lottery_times>;
    static cfg_modular_activity_lucky_bagCache: CfgCacheMapBase<cfg_modular_activity_lucky_bag>;
    static cfg_modular_activity_lucky_bagByPlan_id: CfgCacheMapBase<cfg_modular_activity_lucky_bag[]>;
    static cfg_modular_activity_missionCache: CfgCacheMapBase<cfg_modular_activity_mission>;
    static cfg_modular_activity_missionListCache: CfgCacheMapBase<cfg_modular_activity_mission[]>;
    static cfg_modular_activity_openCache: CfgCacheMapBase<cfg_modular_activity_open>;
    static cfg_modular_activity_open_previewCache: CfgCacheMapBase<cfg_modular_activity_open_preview>;
    static cfg_modular_activity_pay_welfareCache: CfgCacheMapBase<cfg_modular_activity_pay_welfare[]>;
    static cfg_modular_activity_payment_shop_itemCache: CfgCacheMapBase<cfg_modular_activity_payment_shop_item>;
    static cfg_modular_activity_payment_shop_item_showCache: CfgCacheMapBase<cfg_modular_activity_payment_shop_item_show>;
    static cfg_modular_activity_previewCache: CfgCacheMapBase<cfg_modular_activity_preview[]>;
    static cfg_modular_activity_preview_rewardsCache: CfgCacheMapBase<cfg_modular_activity_preview_rewards>;
    static cfg_modular_activity_rankCache: CfgCacheMapBase<cfg_modular_activity_rank>;
    static cfg_modular_activity_rank_rewardCache: CfgCacheMapBase<cfg_modular_activity_rank_reward[]>;
    static cfg_modular_activity_round_missionCache: CfgCacheMapBase<cfg_modular_activity_round_mission[]>;
    static cfg_modular_activity_round_missionAllCache: CfgCacheMapBase<cfg_modular_activity_round_mission>;
    static cfg_modular_activity_round_mission_rewardCache: CfgCacheMapBase<cfg_modular_activity_round_mission_reward[]>;
    static cfg_modular_activity_shop_clientCache: CfgCacheMapBase<cfg_modular_activity_shop_client>;
    static cfg_modular_activity_signCache: CfgCacheMapBase<cfg_modular_activity_sign>;
    static cfg_modular_activity_signbyplanIdCache: CfgCacheMapBase<cfg_modular_activity_sign[]>;
    static cfg_modular_activity_six_blessCache: CfgCacheMapBase<cfg_modular_activity_six_bless>;
    static cfg_modular_activity_star_plan_heroCache: CfgCacheMapBase<cfg_modular_activity_star_plan_hero>;
    static cfg_modular_activity_star_plan_rewardCache: CfgCacheMapBase<cfg_modular_activity_star_plan_reward>;
    static cfg_modular_activity_star_plan_rewardByplan_idCache: CfgCacheMapBase<cfg_modular_activity_star_plan_reward[]>;
    static cfg_modular_activity_star_plan_rewardByplan_idAndMain_pushCache: CfgCacheMapBase<cfg_modular_activity_star_plan_reward[]>;
    static cfg_modular_activity_storyCache: CfgCacheMapBase<cfg_modular_activity_story>;
    static cfg_modular_activity_story_chapterCache: CfgCacheMapBase<cfg_modular_activity_story_chapter[]>;
    static cfg_modular_activity_story_chapter_mapCache: CfgCacheMapBase<cfg_modular_activity_story_chapter_map>;
    static cfg_modular_activity_story_chapter_map_listCache: CfgCacheMapBase<cfg_modular_activity_story_chapter_map[]>;
    static cfg_modular_activity_story_dialogueCache: CfgCacheMapBase<cfg_modular_activity_story_dialogue[]>;
    static cfg_modular_activity_sub_typeCache: CfgCacheMapBase<cfg_modular_activity_sub_type>;
    static cfg_modular_activity_targetCache: CfgCacheMapBase<cfg_modular_activity_target>;
    static cfg_modular_activity_time_itemCache: CfgCacheMapBase<cfg_modular_activity_time_item>;
    static cfg_modular_activity_wallCache: CfgCacheMapBase<cfg_modular_activity_wall>;
    static cfg_modular_activity_war_log_acc_rewardCache: CfgCacheMapBase<cfg_modular_activity_war_log_acc_reward>;
    static cfg_modular_activity_war_log_missionCache: CfgCacheMapBase<cfg_modular_activity_war_log_mission>;
    static cfg_modular_activity_weekly_card_rewardCacheTemp: CfgCacheMapBase<cfg_modular_activity_weekly_card_reward>;
    static cfg_moneyCache: CfgCacheMapBase<cfg_money>;
    static cfg_monsterCache: CfgCacheMapBase<cfg_monster>;
    static cfg_monster_groupCache: CfgCacheMapBase<cfg_monster_group>;
    static cfg_monster_skill_tierCache: CfgCacheMapBase<cfg_monster_skill_tier>;
    static cfg_monster_tipsCache: CfgCacheMapBase<cfg_monster_tips>;
    static cfg_month_fundCache: CfgCacheMapBase<cfg_month_fund[]>;
    static cfg_month_fundCache2: CfgCacheMapBase<cfg_month_fund[]>;
    static cfg_month_fund_typeCache: CfgCacheMapBase<cfg_month_fund_type>;
    static cfg_musicCache: CfgCacheMapBase<cfg_music>;
    static cfg_musicUICache: CfgCacheMapBase<cfg_music[]>;
    static cfg_nation_tower_lineupCache: CfgCacheMapBase<cfg_nation_tower_lineup[]>;
    static cfg_nation_tower_openCache: CfgCacheMapBase<cfg_nation_tower_open>;
    static cfg_noob_payCache: CfgCacheMapBase<cfg_noob_pay>;
    static cfg_online_rewardCache: CfgCacheMapBase<cfg_online_reward>;
    static cfg_pass_beheadCache: CfgCacheMapBase<cfg_pass_behead>;
    static cfg_pass_behead_boxCache: CfgCacheMapBase<cfg_pass_behead_box>;
    static cfg_pass_behead_guanqiaCache: CfgCacheMapBase<cfg_pass_behead_guanqia[]>;
    static cfg_pass_behead_reviveCache: CfgCacheMapBase<cfg_pass_behead_revive>;
    static cfg_pass_check_missionCache: CfgCacheMapBase<cfg_pass_check_mission>;
    static cfg_pass_check_rewardCache: CfgCacheMapBase<cfg_pass_check_reward>;
    static cfg_pass_check_rewardByTurnCache: CfgCacheMapBase<cfg_pass_check_reward[]>;
    static cfg_pass_check_vipCache: CfgCacheMapBase<cfg_pass_check_vip>;
    static cfg_pay_vipCache: CfgCacheMapBase<cfg_pay_vip>;
    static cfg_pay_vip_privilegeCache: CfgCacheMapBase<cfg_pay_vip_privilege[]>;
    static cfg_pay_vip_privilege_typeCache: CfgCacheMapBase<cfg_pay_vip_privilege[]>;
    static cfg_pay_vip_privilege_function_typeCache: CfgCacheMapBase<cfg_pay_vip_privilege_function>;
    static cfg_payment_shop_itemCache: CfgCacheMapBase<cfg_payment_shop_item>;
    static cfg_payment_shop_linkCache: CfgCacheMapBase<cfg_payment_shop_link>;
    static cfg_payment_time_giftCache: CfgCacheMapBase<cfg_payment_time_gift[]>;
    static cfg_peak_miscCache: CfgCacheMapBase<cfg_peak_misc>;
    static cfg_peak_timeCache: CfgCacheMapBase<cfg_peak_time>;
    static cfg_peak_time_model_roundCache: CfgCacheMapBase<cfg_peak_time[]>;
    static cfg_peerless_act_hero_giftCache: CfgCacheMapBase<cfg_peerless_act_hero_gift>;
    static cfg_platform_ad_id_miscCache: CfgCacheMapBase<cfg_platform_ad_id_misc>;
    static cfg_player_strategyCache: CfgCacheMapBase<cfg_player_strategy>;
    static cfg_playing_preview_rewardCache: CfgCacheMapBase<cfg_playing_preview_reward>;
    static cfg_progress_giftCache: CfgCacheMapBase<cfg_progress_gift>;
    static cfg_progress_giftByTypeCache: CfgCacheMapBase<cfg_progress_gift[]>;
    static cfg_pull_wordsCacheTemp: CfgCacheMapBase<cfg_pull_words>;
    static cfg_pvp_mapCache: CfgCacheMapBase<cfg_pvp_map>;
    static cfg_qq_groupCache: CfgCacheMapBase<cfg_qq_group>;
    static cfg_qq_vipCacheTemp: CfgCacheMapBase<cfg_qq_vip>;
    static cfg_qxzl_miscCache: CfgCacheMapBase<cfg_qxzl_misc>;
    static cfg_random_boxCache: CfgCacheMapBase<cfg_random_box>;
    static cfg_random_pvpAllCache: CfgCacheMapBase<cfg_random_pvp>;
    static cfg_random_pvpGroupCache: CfgCacheMapBase<cfg_random_pvp[]>;
    static cfg_random_pvp_head_frameCache: CfgCacheMapBase<cfg_random_pvp_head_frame>;
    static cfg_random_pvp_limitCache: CfgCacheMapBase<cfg_random_pvp_limit>;
    static cfg_random_pvp_rewardCache: CfgCacheMapBase<cfg_random_pvp_reward>;
    static cfg_random_pvp_taskCache: CfgCacheMapBase<cfg_random_pvp_task>;
    static cfg_random_pvp_task_rewardsCache: CfgCacheMapBase<cfg_random_pvp_task_rewards>;
    static cfg_rank_descCache: CfgCacheMapBase<cfg_rank_desc>;
    static cfg_rank_missionCache: CfgCacheMapBase<cfg_rank_mission[]>;
    static cfg_rank_missionCacheAll: CfgCacheMapBase<cfg_rank_mission>;
    static cfg_rank_rewardsCache: CfgCacheMapBase<cfg_rank_rewards[]>;
    static cfg_rank_worshipCache: CfgCacheMapBase<cfg_rank_worship>;
    static cfg_red_cliffCache: CfgCacheMapBase<cfg_red_cliff[]>;
    static cfg_red_cliff_bossCacheTemp: CfgCacheMapBase<cfg_red_cliff_boss>;
    static cfg_red_cliff_openCache: CfgCacheMapBase<cfg_red_cliff_open>;
    static cfg_red_cliff_refreshCache: CfgCacheMapBase<cfg_red_cliff_refresh>;
    static cfg_red_cliff_reviewCache: CfgCacheMapBase<cfg_red_cliff_review>;
    static cfg_retrievalCache: CfgCacheMapBase<cfg_retrieval>;
    static cfg_river_text_constCache: CfgCacheMapBase<cfg_river_text_const>;
    static cfg_profilesByTypeCache: CfgCacheMapBase<cfg_role_profile[]>;
    static cfg_profileCache: CfgCacheMapBase<cfg_role_profile>;
    static cfg_san_xiao_actorCache: CfgCacheMapBase<cfg_san_xiao_actor>;
    static cfg_san_xiao_guanqiaCache: CfgCacheMapBase<cfg_san_xiao_guanqia>;
    static cfg_san_xiao_itemCache: CfgCacheMapBase<cfg_san_xiao_item>;
    static cfg_san_xiao_item_typeCache: CfgCacheMapBase<cfg_san_xiao_item[]>;
    static cfg_san_xiao_levelCache: CfgCacheMapBase<cfg_san_xiao_level>;
    static cfg_san_xiao_mapCache: CfgCacheMapBase<cfg_san_xiao_map>;
    static cfg_san_xiao_miscCache: CfgCacheMapBase<cfg_san_xiao_misc>;
    static cfg_sceneCache: CfgCacheMapBase<cfg_scene>;
    static cfg_sdk_concern_rewardCache: CfgCacheMapBase<cfg_sdk_concern_reward>;
    static cfg_sdk_platform_descCanhe: CfgCacheMapBase<cfg_sdk_platform_desc>;
    static cfg_sdk_rewardsCache: CfgCacheMapBase<cfg_sdk_rewards>;
    static cfg_select_boxCache: CfgCacheMapBase<cfg_select_box>;
    static cfg_seven_goal_singleCache: CfgCacheMapBase<cfg_seven_goal>;
    static cfg_seven_goalCache: CfgCacheMapBase<cfg_seven_goal[]>;
    static cfg_seven_goal_giftCache: CfgCacheMapBase<cfg_seven_goal_gift[]>;
    static cfg_seven_goal_missionCache: CfgCacheMapBase<cfg_seven_goal_mission>;
    static cfg_seven_goal_missionListCache: CfgCacheMapBase<cfg_seven_goal_mission[]>;
    static cfg_share_cycle_rewardCache: CfgCacheMapBase<cfg_share_cycle_reward>;
    static cfg_share_daily_rewardCache: CfgCacheMapBase<cfg_share_daily_reward>;
    static cfg_share_level_rewardCache: CfgCacheMapBase<cfg_share_level_reward>;
    static cfg_shili_previewCache: CfgCacheMapBase<cfg_shili_preview>;
    static cfg_shopCache: CfgCacheMapBase<cfg_shop>;
    static cfg_shopGroupCache: CfgCacheMapBase<cfg_shop[]>;
    static cfg_shop_itemCache: CfgCacheMapBase<cfg_shop_item>;
    static cfg_shop_itemByshop_idAndgoods_idCache: CfgCacheMapBase<cfg_shop_item>;
    static cfg_shop_item_tipsCache: CfgCacheMapBase<cfg_shop_item_tips>;
    static cfg_shop_reset_timesCache: CfgCacheMapBase<cfg_shop_reset_times>;
    static cfg_shop_shortcutCache: CfgCacheMapBase<cfg_shop_shortcut>;
    static cfg_shop_tab_limitCache: CfgCacheMapBase<cfg_shop_tab_limit[]>;
    static cfg_shop_tab_limit_mapCache: CfgCacheMapBase<cfg_shop_tab_limit>;
    static cfg_show_offCache: CfgCacheMapBase<cfg_show_off>;
    static cfg_skeleton_adaptiveCache: CfgCacheMapBase<cfg_skeleton_adaptive>;
    static cfg_skillCache: CfgCacheMapBase<cfg_skill>;
    static cfg_skill_effectCache: CfgCacheMapBase<cfg_skill_effect>;
    static cfg_skill_eventCache: CfgCacheMapBase<cfg_skill_event[]>;
    static cfg_skill_levelCache: CfgCacheMapBase<cfg_skill_level[]>;
    static cfg_skill_summonCache: CfgCacheMapBase<cfg_skill_summon>;
    static cfg_small_gameCache: CfgCacheMapBase<cfg_small_game>;
    static cfg_soldier_gameCache: CfgCacheMapBase<cfg_soldier_game>;
    static cfg_soldier_game_rewardsCache: CfgCacheMapBase<cfg_soldier_game_rewards>;
    static cfg_soul_hero_link_levelCache: CfgCacheMapBase<cfg_soul_hero_link_level>;
    static cfg_soul_hero_link_limit_unlockCache: CfgCacheMapBase<cfg_soul_hero_link_limit_unlock>;
    static cfg_soul_hero_link_nationCache: CfgCacheMapBase<cfg_soul_hero_link_nation>;
    static cfg_stage_breedCache: CfgCacheMapBase<cfg_stage_breed>;
    static cfg_stage_breed_attrCache: CfgCacheMapBase<cfg_stage_breed_attr>;
    static cfg_stage_copyCache: CfgCacheMapBase<cfg_stage_copy>;
    static cfg_stage_copy_bossCache: CfgCacheMapBase<cfg_stage_copy_boss>;
    static cfg_stage_copy_bossChapterCache: CfgCacheMapBase<cfg_stage_copy_boss[]>;
    static cfg_stage_copy_boss_mapCache: CfgCacheMapBase<cfg_stage_copy_boss>;
    static cfg_stage_copy_daily_missionCache: CfgCacheMapBase<cfg_stage_copy_daily_mission>;
    static cfg_stage_copy_miscCache: CfgCacheMapBase<cfg_stage_copy_misc>;
    static cfg_stage_copy_storyCache: CfgCacheMapBase<cfg_stage_copy_story>;
    static cfg_stage_missionCache: CfgCacheMapBase<cfg_stage_mission>;
    static cfg_stage_skill_attrCache: CfgCacheMapBase<cfg_stage_skill_attr>;
    static cfg_stage_skill_attrByTypeCache: CfgCacheMapBase<cfg_stage_skill_attr[]>;
    static cfg_stage_skill_typeCache: CfgCacheMapBase<cfg_stage_skill_type>;
    static cfg_star_plan_giftCache: CfgCacheMapBase<cfg_star_plan_gift>;
    static cfg_star_plan_giftByreact_typeCache: CfgCacheMapBase<cfg_star_plan_gift[]>;
    static cfg_star_plan_heroCache: CfgCacheMapBase<cfg_star_plan_hero>;
    static cfg_star_plan_rewardCache: CfgCacheMapBase<cfg_star_plan_reward>;
    static cfg_star_plan_rewardByrewards_idCache: CfgCacheMapBase<cfg_star_plan_reward[]>;
    static cfg_star_plan_rewardByrewards_idAndMain_pushCache: CfgCacheMapBase<cfg_star_plan_reward[]>;
    static cfg_storyCache: CfgCacheMapBase<cfg_story>;
    static cfg_story_match_typeCache: CfgCacheMapBase<cfg_story[]>;
    static cfg_story_actionCache: CfgCacheMapBase<cfg_story_action>;
    static cfg_story_actorCache: CfgCacheMapBase<cfg_story_actor>;
    static cfg_story_bubbleCache: CfgCacheMapBase<cfg_story_bubble>;
    static cfg_story_mazeCache: CfgCacheMapBase<cfg_story_maze>;
    static cfg_story_maze_mission_spoilsCache: CfgCacheMapBase<cfg_story_maze_mission_spoils>;
    static cfg_story_maze_monsterCache: CfgCacheMapBase<cfg_story_maze_monster[]>;
    static cfg_story_maze_resetCache: CfgCacheMapBase<cfg_story_maze_reset>;
    static cfg_story_maze_reviveCache: CfgCacheMapBase<cfg_story_maze_revive>;
    static cfg_story_maze_rewardsCache: CfgCacheMapBase<cfg_story_maze_rewards>;
    static cfg_story_maze_shopCache: CfgCacheMapBase<cfg_story_maze_shop>;
    static cfg_story_maze_themeCache: CfgCacheMapBase<cfg_story_maze_theme>;
    static cfg_story_siegelord_city_typeCache: CfgCacheMapBase<cfg_story_siegelord_city_type>;
    static cfg_story_siegelord_levelCache: CfgCacheMapBase<cfg_story_siegelord_level[]>;
    static cfg_story_siegelord_level_rewardCache: CfgCacheMapBase<cfg_story_siegelord_level_reward[]>;
    static cfg_story_siegelord_miscCache: CfgCacheMapBase<cfg_story_siegelord_misc>;
    static cfg_story_siegelord_pass_rewardCache: CfgCacheMapBase<cfg_story_siegelord_pass_reward>;
    static cfg_story_siegelord_pass_reward_bystarCache: CfgCacheMapBase<cfg_story_siegelord_pass_reward>;
    static cfg_story_tower_battleCache: CfgCacheMapBase<cfg_story_tower_battle[]>;
    static cfg_story_tower_battle_Game_rewardCache: CfgCacheMapBase<cfg_story_tower_battle[]>;
    static cfg_story_tower_battle_monsterCache: CfgCacheMapBase<cfg_story_tower_battle_monster>;
    static cfg_story_tower_battle_rewardCache: CfgCacheMapBase<cfg_story_tower_battle_reward>;
    static cfg_suit_attr_Cache: CfgCacheMapBase<cfg_suit_attr[]>;
    static cfg_supreme_lotteryCache: CfgCacheMapBase<cfg_supreme_lottery[]>;
    static cfg_svip_pay_giftCache: CfgCacheMapBase<cfg_svip_pay_gift>;
    static cfg_sys_open_noticeCache: CfgCacheMapBase<cfg_sys_open_notice>;
    static cfg_sys_openlvCacheTemp: CfgCacheMapBase<cfg_sys_openlv>;
    static cfg_sys_use_timesCache: CfgCacheMapBase<cfg_sys_use_times>;
    static cfg_taxCache: CfgCacheMapBase<cfg_tax>;
    static cfg_tax_rewardCache: CfgCacheMapBase<cfg_tax_reward>;
    static cfg_td_mainCache: CfgCacheMapBase<cfg_td_main>;
    static cfg_td_main_missionCache: CfgCacheMapBase<cfg_td_main_mission>;
    static cfg_td_main_monsterCache: CfgCacheMapBase<cfg_td_main_monster>;
    static cfg_td_main_monsterByPassCache: CfgCacheMapBase<cfg_td_main_monster[]>;
    static cfg_td_main_pass_missionCache: CfgCacheMapBase<cfg_td_main_pass_mission>;
    static cfg_td_mapCache: CfgCacheMapBase<cfg_td_map>;
    static cfg_td_monster_talkGroupCache: CfgCacheMapBase<cfg_td_monster_talk[]>;
    static cfg_td_trialCache: CfgCacheMapBase<cfg_td_trial>;
    static cfg_td_trial_monsterCache: CfgCacheMapBase<cfg_td_trial_monster>;
    static cfg_team_bossCacheTemp: CfgCacheMapBase<cfg_team_boss>;
    static cfg_team_xswh_bossCache: CfgCacheMapBase<cfg_team_xswh_boss[]>;
    static cfg_team_xswh_giftCache: CfgCacheMapBase<cfg_team_xswh_gift>;
    static cfg_team_xswh_giftbyseasontypeCache: CfgCacheMapBase<cfg_team_xswh_gift[]>;
    static cfg_team_xswh_hurt_rewardsCache: CfgCacheMapBase<cfg_team_xswh_hurt_rewards[]>;
    static cfg_team_xswh_rank_rewardsCache: CfgCacheMapBase<cfg_team_xswh_rank_rewards[]>;
    static cfg_tequanCache: CfgCacheMapBase<cfg_tequan>;
    static cfg_test_towerCache: CfgCacheMapBase<cfg_test_tower>;
    static cfg_test_towerByTypeCache: CfgCacheMapBase<cfg_test_tower[]>;
    static cfg_test_towerByGetArr: CfgCacheMapBase<cfg_test_tower[]>;
    static cfg_test_tower_extra_rewardCache: CfgCacheMapBase<cfg_test_tower_extra_reward[]>;
    static cfg_test_tower_extra_reward_keyCache: CfgCacheMapBase<cfg_test_tower_extra_reward>;
    static cfg_test_tower_skinCache: CfgCacheMapBase<cfg_test_tower_skin>;
    static cfg_test_tower_skin_resCache: CfgCacheMapBase<cfg_test_tower_skin>;
    static cfg_theme_act_famous_lottery_rewardCache: CfgCacheMapBase<cfg_theme_act_famous_lottery_reward>;
    static cfg_theme_act_hero_lottery_showCacheTemp: CfgCacheMapBase<cfg_theme_act_hero_lottery_show>;
    static cfg_theme_act_itemCacheTemp: CfgCacheMapBase<cfg_theme_act_item>;
    static cfg_theme_act_rare_lottery_rewardCache: CfgCacheMapBase<cfg_theme_act_rare_lottery_reward>;
    static cfg_theme_act_skin_lotteryAllCache: CfgCacheMapBase<cfg_theme_act_skin_lottery>;
    static cfg_theme_act_skin_lottery_costCache: CfgCacheMapBase<cfg_theme_act_skin_lottery_cost>;
    static cfg_theme_act_wish_lotteryCache: CfgCacheMapBase<cfg_theme_act_wish_lottery>;
    static cfg_theme_act_wish_lottery_itemCache: CfgCacheMapBase<cfg_theme_act_wish_lottery_item[]>;
    static cfg_theme_act_wish_lottery_showCacheTemp: CfgCacheMapBase<cfg_theme_act_wish_lottery_show>;
    static cfg_tiled_effectCache: CfgCacheMapBase<cfg_tiled_effect>;
    static cfg_tiled_mapCache: CfgCacheMapBase<cfg_tiled_map>;
    static cfg_time_achievementByIdCache: CfgCacheMapBase<cfg_time_achievement>;
    static cfg_time_achievementCache: CfgCacheMapBase<cfg_time_achievement[]>;
    static cfg_time_activity_dropCache: CfgCacheMapBase<cfg_time_activity_drop>;
    static cfg_time_activity_shopCache: CfgCacheMapBase<cfg_time_activity_shop[]>;
    static cfg_time_activity_shopAllCache: CfgCacheMapBase<cfg_time_activity_shop>;
    static cfg_time_activity_weekCache: CfgCacheMapBase<cfg_time_activity_week>;
    static cfg_tipsCache: CfgCacheMapBase<cfg_tips>;
    static cfg_titleCache: CfgCacheMapBase<cfg_title>;
    static cfg_travelCache: CfgCacheMapBase<cfg_travel>;
    static cfg_travel_extCache: CfgCacheMapBase<cfg_travel_ext>;
    static cfg_treasure_boxCache: CfgCacheMapBase<cfg_treasure_box>;
    static cfg_treasure_box_typeCache: CfgCacheMapBase<cfg_treasure_box_type>;
    static cfg_treasure_box_typeGrpCache: CfgCacheMapBase<cfg_treasure_box_type[]>;
    static cfg_treasure_energyCache: CfgCacheMapBase<cfg_treasure_energy>;
    static cfg_treasure_giftCache: CfgCacheMapBase<cfg_treasure_gift>;
    static cfg_treasure_miscCache: CfgCacheMapBase<cfg_treasure_misc>;
    static cfg_treasure_refresh_costCache: CfgCacheMapBase<cfg_treasure_refresh_cost>;
    static cfg_treasure_workerCache: CfgCacheMapBase<cfg_treasure_worker>;
    static cfg_trig_skillCache: CfgCacheMapBase<cfg_trig_skill[]>;
    static cfg_ui_button_styleCache: CfgCacheMapBase<cfg_ui_button_style>;
    static cfg_ui_preloadCache: CfgCacheMapBase<cfg_ui_preload>;
    static cfg_ui_residentCache: CfgCacheMapBase<cfg_ui_resident>;
    static cfg_up_star_giftCache: CfgCacheMapBase<cfg_up_star_gift>;
    static cfg_up_star_rewardCache: CfgCacheMapBase<cfg_up_star_reward>;
    static cfg_up_star_rewardByroundCache: CfgCacheMapBase<cfg_up_star_reward[]>;
    static cfg_up_star_rewardByreward_idCache: CfgCacheMapBase<cfg_up_star_reward[]>;
    static cfg_vip_daily_missionCache: CfgCacheMapBase<cfg_vip_daily_mission>;
    static cfg_vip_daily_mission_giftCacheTemp: CfgCacheMapBase<cfg_vip_daily_mission_gift>;
    static cfg_vip_kefuCache: CfgCacheMapBase<cfg_vip_kefu>;
    static cfg_vip_kefuBysdknameCache: CfgCacheMapBase<cfg_vip_kefu>;
    static cfg_vip_kefu_reviewCache: CfgCacheMapBase<cfg_vip_kefu_review>;
    static cfg_war_flagCache: CfgCacheMapBase<cfg_war_flag>;
    static cfg_war_flag_facadeCache: CfgCacheMapBase<cfg_war_flag_facade[]>;
    static cfg_war_flag_levelCache: CfgCacheMapBase<cfg_war_flag_level>;
    static cfg_war_flag_linkCache: CfgCacheMapBase<cfg_war_flag_link>;
    static cfg_war_flag_recycleCache: CfgCacheMapBase<cfg_war_flag_recycle>;
    static cfg_war_flag_stageCache: CfgCacheMapBase<cfg_war_flag_stage>;
    static cfg_war_flag_stageCacheByNationCache: CfgCacheMapBase<cfg_war_flag_stage[]>;
    static cfg_war_log_missionCache: CfgCacheMapBase<cfg_war_log_mission>;
    static cfg_war_log_mission_pay_rewardTypeCache: CfgCacheMapBase<cfg_war_log_mission_pay_reward[]>;
    static cfg_war_log_mission_score_rewardTypeCache: CfgCacheMapBase<cfg_war_log_mission_score_reward[]>;
    static cfg_wars_honorCache: CfgCacheMapBase<cfg_wars_honor>;
    static cfg_wars_map_campCache: CfgCacheMapBase<cfg_wars_map_camp>;
    static cfg_wars_map_camp_listCache: CfgCacheMapBase<cfg_wars_map_camp[]>;
    static cfg_wars_map_city_allCache: CfgCacheMapBase<cfg_wars_map_city[]>;
    static cfg_wars_map_cityCache: CfgCacheMapBase<cfg_wars_map_city>;
    static cfg_wars_map_city_posCache: CfgCacheMapBase<cfg_wars_map_city>;
    static cfg_wars_map_near_city2Cache: CfgCacheMapBase<cfg_wars_map_near_city2>;
    static cfg_wars_map_near_city_list2Cache: CfgCacheMapBase<cfg_wars_map_near_city2[]>;
    static cfg_wars_map_typeCache: CfgCacheMapBase<cfg_wars_map_type>;
    static cfg_wars_map_type_sys_idCache: CfgCacheMapBase<cfg_wars_map_type>;
    static cfg_wars_map_type_matchCache: CfgCacheMapBase<cfg_wars_map_type[]>;
    static cfg_wars_miscCache: CfgCacheMapBase<cfg_wars_misc>;
    static cfg_wars_missionCache: CfgCacheMapBase<cfg_wars_mission>;
    static cfg_wars_stateCache: CfgCacheMapBase<cfg_wars_state>;
    static cfg_wars_state_modelCache: CfgCacheMapBase<cfg_wars_state[]>;
    static cfg_wars_text_constCache: CfgCacheMapBase<cfg_wars_text_const>;
    static cfg_week_targetBytypeCache: CfgCacheMapBase<cfg_week_target[]>;
    static cfg_week_targetCache: CfgCacheMapBase<cfg_week_target>;
    static cfg_week_target_levelBytypeCache: CfgCacheMapBase<cfg_week_target_level[]>;
    static cfg_week_target_levelCache: CfgCacheMapBase<cfg_week_target_level>;
    static cfg_wing_buffAllCache: CfgCacheMapBase<cfg_wing_buff>;
    static cfg_wing_heroAllCache: CfgCacheMapBase<cfg_wing_hero>;
    static cfg_wing_heroCache: CfgCacheMapBase<cfg_wing_hero[]>;
    static cfg_wing_hero_skinCache: CfgCacheMapBase<cfg_wing_hero_skin>;
    static cfg_wing_levelAllCache: CfgCacheMapBase<cfg_wing_level>;
    static cfg_wing_levelCache: CfgCacheMapBase<cfg_wing_level[]>;
    static cfg_world_boss_hurt_rewardsTypeCache: CfgCacheMapBase<cfg_world_boss_hurt_rewards[]>;
    static cfg_world_boss_hurt_rewardsCache: CfgCacheMapBase<cfg_world_boss_hurt_rewards[]>;
    static cfg_world_boss_levelCache: CfgCacheMapBase<cfg_world_boss_level[]>;
    static cfg_world_boss_levelbytypeIdCache: CfgCacheMapBase<cfg_world_boss_level[]>;
    static cfg_world_mapCache: CfgCacheMapBase<cfg_world_map>;
    static cfg_world_map_2Cache: CfgCacheMapBase<cfg_world_map_2>;
    static cfg_wxShareCache: CfgCacheMapBase<cfg_wxShare[]>;
    static cfg_wxTurn_giftCache: CfgCacheMapBase<cfg_wxTurn_gift>;
    static cfg_wx_game_clubCache: CfgCacheMapBase<cfg_wx_game_club[]>;
    static cfg_xswh_bossCache: CfgCacheMapBase<cfg_xswh_boss[]>;
    static cfg_xswh_giftCache: CfgCacheMapBase<cfg_xswh_gift>;
    static cfg_xswh_hurt_rewardsCache: CfgCacheMapBase<cfg_xswh_hurt_rewards[]>;
    static cfg_xswh_rank_rewardsCache: CfgCacheMapBase<cfg_xswh_rank_rewards[]>;
    static cfg_ybzk_rewardCache: CfgCacheMapBase<cfg_ybzk_reward[]>;
    static cfg_zero_buyCache: CfgCacheMapBase<cfg_zero_buy>;
    static cfg_errorCodeCache: CfgCacheMapBase<errorCode>;
    static cfg_fightAttrByIdCache: CfgCacheMapBase<fightAttr>;
    static cfg_fightAttrByKeyCache: CfgCacheMapBase<fightAttr>;
    static cfg_fightAttrByTypeCache: CfgCacheMapBase<fightAttr[]>;
    static victoryMacroCache: CfgCacheMapBase<victoryMacro>;

    static get is_complete(): boolean {
        return CfgCacheMgr.ins.is_complete;
    }

    static init(): void {
        var cache_mgr = CfgCacheMgr.ins;
        var cachemap_mgr = CfgCacheMapMgr;
        
        cachemap_mgr.cfg_acc_giftCache = new CfgCacheMapBase<cfg_acc_gift[]>(cache_mgr.cfg_acc_gift_cache, SEARCH_TYPE.GRP, "big_type");
        cachemap_mgr.cfg_achievementCache = new CfgCacheMapBase<cfg_achievement>(cache_mgr.cfg_achievement_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_activity_clientCache = new CfgCacheMapBase<cfg_activity_client>(cache_mgr.cfg_activity_client_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_activity_descCache = new CfgCacheMapBase<cfg_activity_desc>(cache_mgr.cfg_activity_desc_cache, SEARCH_TYPE.ID, "type");
        cachemap_mgr.cfg_activity_iconCache = new CfgCacheMapBase<cfg_activity_icon>(cache_mgr.cfg_activity_icon_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_activity_iconParentCache = new CfgCacheMapBase<cfg_activity_icon[]>(cache_mgr.cfg_activity_icon_cache, SEARCH_TYPE.GRP, "parentID");
        cachemap_mgr.cfg_activity_limit_signCache = new CfgCacheMapBase<cfg_activity_limit_sign>(cache_mgr.cfg_activity_limit_sign_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_activity_miscCacheTemp = new CfgCacheMapBase<cfg_activity_misc>(cache_mgr.cfg_activity_misc_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_activity_missionCache = new CfgCacheMapBase<cfg_activity_mission[]>(cache_mgr.cfg_activity_mission_cache, SEARCH_TYPE.MGRP, "rewards_id,act_type");
        cachemap_mgr.cfg_activity_missionAllCache = new CfgCacheMapBase<cfg_activity_mission>(cache_mgr.cfg_activity_mission_cache, SEARCH_TYPE.MKEY, "rewards_id,act_type,id");
        cachemap_mgr.cfg_activity_noticeCache = new CfgCacheMapBase<cfg_activity_notice>(cache_mgr.cfg_activity_notice_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_activity_pageCache = new CfgCacheMapBase<cfg_activity_page>(cache_mgr.cfg_activity_page_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_activity_yuekaCache = new CfgCacheMapBase<cfg_activity_yueka>(cache_mgr.cfg_activity_yueka_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_ad_showCache = new CfgCacheMapBase<cfg_ad_show>(cache_mgr.cfg_ad_show_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_agent_reviewCache = new CfgCacheMapBase<cfg_agent_review>(cache_mgr.cfg_agent_review_cache, SEARCH_TYPE.KEY, "platform");
        cachemap_mgr.cfg_all_pinyin_dictCacheTemp = new CfgCacheMapBase<cfg_all_pinyin_dict>(cache_mgr.cfg_all_pinyin_dict_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_arenaCacheTemp = new CfgCacheMapBase<cfg_arena>(cache_mgr.cfg_arena_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_arena_matchCache = new CfgCacheMapBase<cfg_arena_match>(cache_mgr.cfg_arena_match_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_arena_match_guess_allCache = new CfgCacheMapBase<cfg_arena_match_guess>(cache_mgr.cfg_arena_match_guess_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_arena_max_rewardCache = new CfgCacheMapBase<cfg_arena_max_reward>(cache_mgr.cfg_arena_max_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_arena_rankCache = new CfgCacheMapBase<cfg_arena_rank[]>(cache_mgr.cfg_arena_rank_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_arena_rewardCache = new CfgCacheMapBase<cfg_arena_reward>(cache_mgr.cfg_arena_reward_cache, SEARCH_TYPE.KEY, "min_times");
        cachemap_mgr.cfg_arena_skip_limitCacheTemp = new CfgCacheMapBase<cfg_arena_skip_limit>(cache_mgr.cfg_arena_skip_limit_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_arena_weekly_rewardCache = new CfgCacheMapBase<cfg_arena_weekly_reward>(cache_mgr.cfg_arena_weekly_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_ares_palaceCache = new CfgCacheMapBase<cfg_ares_palace>(cache_mgr.cfg_ares_palace_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_authorized_giftsCache = new CfgCacheMapBase<cfg_authorized_gifts>(cache_mgr.cfg_authorized_gifts_cache, SEARCH_TYPE.KEY, "platform");
        cachemap_mgr.cfg_bag_pageCallCache = new CfgCacheMapBase<cfg_bag_page>(cache_mgr.cfg_bag_page_cache, SEARCH_TYPE.ID, "index");
        cachemap_mgr.cfg_bai_jiang_giftCache = new CfgCacheMapBase<cfg_bai_jiang_gift>(cache_mgr.cfg_bai_jiang_gift_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_battle_fly_nameCache = new CfgCacheMapBase<cfg_battle_fly_name>(cache_mgr.cfg_battle_fly_name_cache, SEARCH_TYPE.MKEY, "id,level");
        cachemap_mgr.cfg_battle_trial_buffCache = new CfgCacheMapBase<cfg_battle_trial_buff>(cache_mgr.cfg_battle_trial_buff_cache, SEARCH_TYPE.MKEY, "type,level");
        cachemap_mgr.cfg_battle_trial_buff_listCache = new CfgCacheMapBase<cfg_battle_trial_buff[]>(cache_mgr.cfg_battle_trial_buff_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_battle_trial_buff_resetCache = new CfgCacheMapBase<cfg_battle_trial_buff_reset>(cache_mgr.cfg_battle_trial_buff_reset_cache, SEARCH_TYPE.KEY, "count");
        cachemap_mgr.cfg_battle_trial_guaji_monsterCache = new CfgCacheMapBase<cfg_battle_trial_guaji_monster>(cache_mgr.cfg_battle_trial_guaji_monster_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_battle_trial_miscCache = new CfgCacheMapBase<cfg_battle_trial_misc>(cache_mgr.cfg_battle_trial_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_battle_trial_pass_guanqiaCache = new CfgCacheMapBase<cfg_battle_trial_pass_guanqia>(cache_mgr.cfg_battle_trial_pass_guanqia_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_battle_trial_pass_rewardCache = new CfgCacheMapBase<cfg_battle_trial_pass_reward>(cache_mgr.cfg_battle_trial_pass_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_beast_platformCache = new CfgCacheMapBase<cfg_beast_platform>(cache_mgr.cfg_beast_platform_cache, SEARCH_TYPE.KEY, "platform_id");
        cachemap_mgr.cfg_bingfaCache = new CfgCacheMapBase<cfg_bingfa>(cache_mgr.cfg_bingfa_cache, SEARCH_TYPE.MKEY, "skill_level,skill_id");
        cachemap_mgr.cfg_bingfaLevelCache = new CfgCacheMapBase<cfg_bingfa[]>(cache_mgr.cfg_bingfa_cache, SEARCH_TYPE.GRP, "skill_level");
        cachemap_mgr.cfg_bingfa_extCache = new CfgCacheMapBase<cfg_bingfa_ext>(cache_mgr.cfg_bingfa_ext_cache, SEARCH_TYPE.MKEY, "skill_level,skill_id");
        cachemap_mgr.cfg_bingfa_extBySkillIdCache = new CfgCacheMapBase<cfg_bingfa_ext[]>(cache_mgr.cfg_bingfa_ext_cache, SEARCH_TYPE.GRP, "skill_id");
        cachemap_mgr.cfg_bingfa_extBySkillLevelListCache = new CfgCacheMapBase<cfg_bingfa_ext[]>(cache_mgr.cfg_bingfa_ext_cache, SEARCH_TYPE.GRP, "skill_level");
        cachemap_mgr.cfg_bingfuCache = new CfgCacheMapBase<cfg_bingfu>(cache_mgr.cfg_bingfu_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_bingfu_discompose_recastCache = new CfgCacheMapBase<cfg_bingfu_discompose_recast>(cache_mgr.cfg_bingfu_discompose_recast_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_bingfu_recast_lockCache = new CfgCacheMapBase<cfg_bingfu_recast_lock>(cache_mgr.cfg_bingfu_recast_lock_cache, SEARCH_TYPE.ID, "skill_level");
        cachemap_mgr.cfg_bingfu_refineCache = new CfgCacheMapBase<cfg_bingfu_refine>(cache_mgr.cfg_bingfu_refine_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_bingfu_refine_TargetIdCache = new CfgCacheMapBase<cfg_bingfu_refine>(cache_mgr.cfg_bingfu_refine_cache, SEARCH_TYPE.KEY, "target_id");
        cachemap_mgr.cfg_bingfu_upgradeCache = new CfgCacheMapBase<cfg_bingfu_upgrade>(cache_mgr.cfg_bingfu_upgrade_cache, SEARCH_TYPE.MKEY, "color,need_num");
        cachemap_mgr.cfg_boat_peak_miscCache = new CfgCacheMapBase<cfg_boat_peak_misc>(cache_mgr.cfg_boat_peak_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_boat_peak_rankCache = new CfgCacheMapBase<cfg_boat_peak_rank[]>(cache_mgr.cfg_boat_peak_rank_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_boat_peak_timeCache = new CfgCacheMapBase<cfg_boat_peak_time>(cache_mgr.cfg_boat_peak_time_cache, SEARCH_TYPE.MKEY, "day,round,step");
        cachemap_mgr.cfg_boat_peak_time_day_roundCache = new CfgCacheMapBase<cfg_boat_peak_time[]>(cache_mgr.cfg_boat_peak_time_cache, SEARCH_TYPE.MGRP, "day,round");
        cachemap_mgr.cfg_boat_peak_time_round_stepCache = new CfgCacheMapBase<cfg_boat_peak_time>(cache_mgr.cfg_boat_peak_time_cache, SEARCH_TYPE.MKEY, "round,step");
        cachemap_mgr.cfg_boat_racc_rank_goldCache = new CfgCacheMapBase<cfg_boat_racc_rank_gold>(cache_mgr.cfg_boat_racc_rank_gold_cache, SEARCH_TYPE.KEY, "min_rank");
        cachemap_mgr.cfg_boat_race_auction_reward_typeCache = new CfgCacheMapBase<cfg_boat_race_auction_reward_type>(cache_mgr.cfg_boat_race_auction_reward_type_cache, SEARCH_TYPE.KEY, "type_id");
        cachemap_mgr.cfg_boat_race_itemCache = new CfgCacheMapBase<cfg_boat_race_item>(cache_mgr.cfg_boat_race_item_cache, SEARCH_TYPE.KEY, "type_id");
        cachemap_mgr.cfg_boat_race_miscCache = new CfgCacheMapBase<cfg_boat_race_misc>(cache_mgr.cfg_boat_race_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_buffCache = new CfgCacheMapBase<cfg_buff>(cache_mgr.cfg_buff_cache, SEARCH_TYPE.ID, "uid");
        cachemap_mgr.cfg_buff_typeCache = new CfgCacheMapBase<cfg_buff_type>(cache_mgr.cfg_buff_type_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_buildingCache = new CfgCacheMapBase<cfg_building>(cache_mgr.cfg_building_cache, SEARCH_TYPE.KEY, "type");
        cachemap_mgr.cfg_building_lvCache = new CfgCacheMapBase<cfg_building_lv>(cache_mgr.cfg_building_lv_cache, SEARCH_TYPE.MKEY, "type,level");
        cachemap_mgr.cfg_building_missionCache = new CfgCacheMapBase<cfg_building_mission>(cache_mgr.cfg_building_mission_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_building_plot_dialogueCache = new CfgCacheMapBase<cfg_building_plot_dialogue>(cache_mgr.cfg_building_plot_dialogue_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_buy_timesCache = new CfgCacheMapBase<cfg_buy_times[]>(cache_mgr.cfg_buy_times_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_buy_times_typeCache = new CfgCacheMapBase<cfg_buy_times_type>(cache_mgr.cfg_buy_times_type_cache, SEARCH_TYPE.KEY, "type");
        cachemap_mgr.cfg_cast_soul_activeCache = new CfgCacheMapBase<cfg_cast_soul_active>(cache_mgr.cfg_cast_soul_active_cache, SEARCH_TYPE.ID, "nation");
        cachemap_mgr.cfg_casting_soulCache = new CfgCacheMapBase<cfg_casting_soul>(cache_mgr.cfg_casting_soul_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_casting_soul_buffCache = new CfgCacheMapBase<cfg_casting_soul_buff>(cache_mgr.cfg_casting_soul_buff_cache, SEARCH_TYPE.MKEY, "item_kind,casting_soul_level");
        cachemap_mgr.cfg_casting_soul_kind_buffCache = new CfgCacheMapBase<cfg_casting_soul_buff[]>(cache_mgr.cfg_casting_soul_buff_cache, SEARCH_TYPE.GRP, "item_kind");
        cachemap_mgr.cfg_chapter_dialogCacheTemp = new CfgCacheMapBase<cfg_chapter_dialog>(cache_mgr.cfg_chapter_dialog_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_chapter_scriptCache = new CfgCacheMapBase<cfg_chapter_script[]>(cache_mgr.cfg_chapter_script_cache, SEARCH_TYPE.GRP, "script_id");
        cachemap_mgr.cfg_chat_bullet_msgCache = new CfgCacheMapBase<cfg_chat_bullet_msg>(cache_mgr.cfg_chat_bullet_msg_cache, SEARCH_TYPE.ID, "type");
        cachemap_mgr.cfg_chat_channelCache = new CfgCacheMapBase<cfg_chat_channel>(cache_mgr.cfg_chat_channel_cache, SEARCH_TYPE.KEY, "type");
        cachemap_mgr.cfg_chat_skinCache = new CfgCacheMapBase<cfg_chat_skin>(cache_mgr.cfg_chat_skin_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_chat_skin_widgetCache = new CfgCacheMapBase<cfg_chat_skin_widget>(cache_mgr.cfg_chat_skin_widget_cache, SEARCH_TYPE.KEY, "widget_id");
        cachemap_mgr.cfg_client_langCache = new CfgCacheMapBase<cfg_client_lang>(cache_mgr.cfg_client_lang_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_client_w3_effectCacheTemp = new CfgCacheMapBase<cfg_client_w3_effect>(cache_mgr.cfg_client_w3_effect_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_client_w3_effect_descCache = new CfgCacheMapBase<cfg_client_w3_effect_desc>(cache_mgr.cfg_client_w3_effect_desc_cache, SEARCH_TYPE.KEY, "field");
        cachemap_mgr.cfg_client_w3_skillCacheTemp = new CfgCacheMapBase<cfg_client_w3_skill>(cache_mgr.cfg_client_w3_skill_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_client_w3_skill_descCache = new CfgCacheMapBase<cfg_client_w3_skill_desc>(cache_mgr.cfg_client_w3_skill_desc_cache, SEARCH_TYPE.KEY, "field");
        cachemap_mgr.cfg_client_w3_skinCache = new CfgCacheMapBase<cfg_client_w3_skin>(cache_mgr.cfg_client_w3_skin_cache, SEARCH_TYPE.KEY, "skin_id");
        cachemap_mgr.cfg_cmdCache = new CfgCacheMapBase<cfg_cmd[]>(cache_mgr.cfg_cmd_cache, SEARCH_TYPE.GRP, "group");
        cachemap_mgr.cfg_code_cliCache = new CfgCacheMapBase<cfg_code_cli>(cache_mgr.cfg_code_cli_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_country_war_miscCache = new CfgCacheMapBase<cfg_country_war_misc>(cache_mgr.cfg_country_war_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_cross_team_match_typeCache = new CfgCacheMapBase<cfg_cross_team_match_type>(cache_mgr.cfg_cross_team_match_type_cache, SEARCH_TYPE.ID, "match_type");
        cachemap_mgr.cfg_cross_team_match_type_data_listCache = new CfgCacheMapBase<cfg_cross_team_match_type[]>(cache_mgr.cfg_cross_team_match_type_cache, SEARCH_TYPE.GRP, "lineup_data_type");
        cachemap_mgr.cfg_cross_team_miscCache = new CfgCacheMapBase<cfg_cross_team_misc>(cache_mgr.cfg_cross_team_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_cross_test_tower_openCache = new CfgCacheMapBase<cfg_cross_test_tower_open>(cache_mgr.cfg_cross_test_tower_open_cache, SEARCH_TYPE.ID, "nation");
        cachemap_mgr.cfg_crush_fight_conditionCache = new CfgCacheMapBase<cfg_crush_fight_condition>(cache_mgr.cfg_crush_fight_condition_cache, SEARCH_TYPE.ID, "match_type");
        cachemap_mgr.cfg_crush_fight_missionCache = new CfgCacheMapBase<cfg_crush_fight_mission>(cache_mgr.cfg_crush_fight_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_crush_fight_mission_typeCache = new CfgCacheMapBase<cfg_crush_fight_mission_type>(cache_mgr.cfg_crush_fight_mission_type_cache, SEARCH_TYPE.ID, "type");
        cachemap_mgr.cfg_csc_fmsolo_buffCache = new CfgCacheMapBase<cfg_csc_fmsolo_buff>(cache_mgr.cfg_csc_fmsolo_buff_cache, SEARCH_TYPE.KEY, "buff_level");
        cachemap_mgr.cfg_csc_fmsolo_challengeCache = new CfgCacheMapBase<cfg_csc_fmsolo_challenge>(cache_mgr.cfg_csc_fmsolo_challenge_cache, SEARCH_TYPE.MKEY, "rank_sn,star");
        cachemap_mgr.cfg_csc_fmsolo_etcCache = new CfgCacheMapBase<cfg_csc_fmsolo_etc>(cache_mgr.cfg_csc_fmsolo_etc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_csc_fmsolo_logCache = new CfgCacheMapBase<cfg_csc_fmsolo_log>(cache_mgr.cfg_csc_fmsolo_log_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_csc_fmsolo_rewardCache = new CfgCacheMapBase<cfg_csc_fmsolo_reward>(cache_mgr.cfg_csc_fmsolo_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_csc_fmsolo_rewardTypeCache = new CfgCacheMapBase<cfg_csc_fmsolo_reward[]>(cache_mgr.cfg_csc_fmsolo_reward_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_csc_fmsolo_shop_lvCache = new CfgCacheMapBase<cfg_csc_fmsolo_shop_lv>(cache_mgr.cfg_csc_fmsolo_shop_lv_cache, SEARCH_TYPE.KEY, "shop_level");
        cachemap_mgr.cfg_csclanCache = new CfgCacheMapBase<cfg_csclan>(cache_mgr.cfg_csclan_cache, SEARCH_TYPE.KEY, "min_power");
        cachemap_mgr.cfg_csclan_etcCache = new CfgCacheMapBase<cfg_csclan_etc>(cache_mgr.cfg_csclan_etc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_csclan_solo_buffCache = new CfgCacheMapBase<cfg_csclan_solo_buff>(cache_mgr.cfg_csclan_solo_buff_cache, SEARCH_TYPE.KEY, "buff_level");
        cachemap_mgr.cfg_csclan_solo_challengeCache = new CfgCacheMapBase<cfg_csclan_solo_challenge>(cache_mgr.cfg_csclan_solo_challenge_cache, SEARCH_TYPE.MKEY, "rank_sn,star");
        cachemap_mgr.cfg_csclan_solo_etcCache = new CfgCacheMapBase<cfg_csclan_solo_etc>(cache_mgr.cfg_csclan_solo_etc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_csclan_solo_logCache = new CfgCacheMapBase<cfg_csclan_solo_log>(cache_mgr.cfg_csclan_solo_log_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_csclan_solo_rewardCache = new CfgCacheMapBase<cfg_csclan_solo_reward>(cache_mgr.cfg_csclan_solo_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_csclan_solo_rewardTypeCache = new CfgCacheMapBase<cfg_csclan_solo_reward[]>(cache_mgr.cfg_csclan_solo_reward_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_csclan_solo_shop_lvCache = new CfgCacheMapBase<cfg_csclan_solo_shop_lv>(cache_mgr.cfg_csclan_solo_shop_lv_cache, SEARCH_TYPE.KEY, "shop_level");
        cachemap_mgr.cfg_daily_copyCache = new CfgCacheMapBase<cfg_daily_copy[]>(cache_mgr.cfg_daily_copy_cache, SEARCH_TYPE.GRP, "copy_type");
        cachemap_mgr.cfg_daily_copy_discountCacheTemp = new CfgCacheMapBase<cfg_daily_copy_discount>(cache_mgr.cfg_daily_copy_discount_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_daily_copy_monsterCache = new CfgCacheMapBase<cfg_daily_copy_monster>(cache_mgr.cfg_daily_copy_monster_cache, SEARCH_TYPE.MKEY, "map_id,wave");
        cachemap_mgr.cfg_daily_copy_typeCache = new CfgCacheMapBase<cfg_daily_copy_type>(cache_mgr.cfg_daily_copy_type_cache, SEARCH_TYPE.ID, "copy_type");
        cachemap_mgr.cfg_daily_missionCache = new CfgCacheMapBase<cfg_daily_mission>(cache_mgr.cfg_daily_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_daily_mission_giftCache = new CfgCacheMapBase<cfg_daily_mission_gift>(cache_mgr.cfg_daily_mission_gift_cache, SEARCH_TYPE.ID, "score");
        cachemap_mgr.cfg_daily_new_discount_rebateCache = new CfgCacheMapBase<cfg_daily_new_discount_rebate>(cache_mgr.cfg_daily_new_discount_rebate_cache, SEARCH_TYPE.ID, "day");
        cachemap_mgr.cfg_daily_new_discount_rebateByGroupCache = new CfgCacheMapBase<cfg_daily_new_discount_rebate[]>(cache_mgr.cfg_daily_new_discount_rebate_cache, SEARCH_TYPE.GRP, "floor");
        cachemap_mgr.cfg_daily_payCache = new CfgCacheMapBase<cfg_daily_pay>(cache_mgr.cfg_daily_pay_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_dawankaCache = new CfgCacheMapBase<cfg_dawanka>(cache_mgr.cfg_dawanka_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_dawanka_tequanGroupCache = new CfgCacheMapBase<cfg_dawanka_tequan[]>(cache_mgr.cfg_dawanka_tequan_cache, SEARCH_TYPE.GRP, "level");
        cachemap_mgr.cfg_dawanka_tequanCache = new CfgCacheMapBase<cfg_dawanka_tequan>(cache_mgr.cfg_dawanka_tequan_cache, SEARCH_TYPE.MKEY, "level,type");
        cachemap_mgr.cfg_day_acc_pay_giftCache = new CfgCacheMapBase<cfg_day_acc_pay_gift>(cache_mgr.cfg_day_acc_pay_gift_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_deputyCache = new CfgCacheMapBase<cfg_deputy>(cache_mgr.cfg_deputy_cache, SEARCH_TYPE.KEY, "hero_type_id");
        cachemap_mgr.cfg_deputy_levelCache = new CfgCacheMapBase<cfg_deputy_level>(cache_mgr.cfg_deputy_level_cache, SEARCH_TYPE.KEY, "level");
        cachemap_mgr.cfg_deputy_starCache = new CfgCacheMapBase<cfg_deputy_star>(cache_mgr.cfg_deputy_star_cache, SEARCH_TYPE.KEY, "star");
        cachemap_mgr.cfg_device_excursion_name1Cache = new CfgCacheMapBase<cfg_device_excursion>(cache_mgr.cfg_device_excursion_cache, SEARCH_TYPE.KEY, "name_1");
        cachemap_mgr.cfg_device_excursion_name2Cache = new CfgCacheMapBase<cfg_device_excursion>(cache_mgr.cfg_device_excursion_cache, SEARCH_TYPE.KEY, "name_2");
        cachemap_mgr.cfg_divineCache = new CfgCacheMapBase<cfg_divine>(cache_mgr.cfg_divine_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_divine_activateCache = new CfgCacheMapBase<cfg_divine_activate>(cache_mgr.cfg_divine_activate_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_divine_copyCache = new CfgCacheMapBase<cfg_divine_copy>(cache_mgr.cfg_divine_copy_cache, SEARCH_TYPE.ID, "storey");
        cachemap_mgr.cfg_divine_strengthenCache = new CfgCacheMapBase<cfg_divine_strengthen>(cache_mgr.cfg_divine_strengthen_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_divine_wearCache = new CfgCacheMapBase<cfg_divine_wear>(cache_mgr.cfg_divine_wear_cache, SEARCH_TYPE.ID, "career");
        cachemap_mgr.cfg_dominate_pvpCache = new CfgCacheMapBase<cfg_dominate_pvp>(cache_mgr.cfg_dominate_pvp_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_dominate_pvpGroupCache = new CfgCacheMapBase<cfg_dominate_pvp[]>(cache_mgr.cfg_dominate_pvp_cache, SEARCH_TYPE.GRP, "big_level");
        cachemap_mgr.cfg_dominate_pvp_limitCache = new CfgCacheMapBase<cfg_dominate_pvp_limit>(cache_mgr.cfg_dominate_pvp_limit_cache, SEARCH_TYPE.ID, "type");
        cachemap_mgr.cfg_dominate_pvp_rewardCache = new CfgCacheMapBase<cfg_dominate_pvp_reward>(cache_mgr.cfg_dominate_pvp_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_dominate_pvp_taskCache = new CfgCacheMapBase<cfg_dominate_pvp_task>(cache_mgr.cfg_dominate_pvp_task_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_dominate_pvp_task_rewardsCache = new CfgCacheMapBase<cfg_dominate_pvp_task_rewards>(cache_mgr.cfg_dominate_pvp_task_rewards_cache, SEARCH_TYPE.ID, "grade");
        cachemap_mgr.cfg_drop_groupCache = new CfgCacheMapBase<cfg_drop_group[]>(cache_mgr.cfg_drop_group_cache, SEARCH_TYPE.GRP, "group_id");
        cachemap_mgr.cfg_eight_loginCache = new CfgCacheMapBase<cfg_eight_login>(cache_mgr.cfg_eight_login_cache, SEARCH_TYPE.ID, "day");
        cachemap_mgr.cfg_epic_battle_tipsCache = new CfgCacheMapBase<cfg_epic_battle_tips>(cache_mgr.cfg_epic_battle_tips_cache, SEARCH_TYPE.ID, "sys_id");
        cachemap_mgr.cfg_equipCache = new CfgCacheMapBase<cfg_equip>(cache_mgr.cfg_equip_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_equipbySuitIdCache = new CfgCacheMapBase<cfg_equip[]>(cache_mgr.cfg_equip_cache, SEARCH_TYPE.GRP, "suit_id");
        cachemap_mgr.cfg_equip_composeCache = new CfgCacheMapBase<cfg_equip_compose>(cache_mgr.cfg_equip_compose_cache, SEARCH_TYPE.KEY, "item_id");
        cachemap_mgr.cfg_equip_compose_target_idCache = new CfgCacheMapBase<cfg_equip_compose>(cache_mgr.cfg_equip_compose_cache, SEARCH_TYPE.KEY, "target_item_id");
        cachemap_mgr.cfg_equip_suitCache = new CfgCacheMapBase<cfg_equip_suit[]>(cache_mgr.cfg_equip_suit_cache, SEARCH_TYPE.GRP, "id");
        cachemap_mgr.cfg_equip_suit_extCache = new CfgCacheMapBase<cfg_equip_suit_ext>(cache_mgr.cfg_equip_suit_ext_cache, SEARCH_TYPE.ID, "suit_id");
        cachemap_mgr.cfg_fail_tipsCache = new CfgCacheMapBase<cfg_fail_tips>(cache_mgr.cfg_fail_tips_cache, SEARCH_TYPE.ID, "index");
        cachemap_mgr.cfg_familyCache = new CfgCacheMapBase<cfg_family>(cache_mgr.cfg_family_cache, SEARCH_TYPE.KEY, "level");
        cachemap_mgr.cfg_family_active_attrCache = new CfgCacheMapBase<cfg_family_active_attr>(cache_mgr.cfg_family_active_attr_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_family_active_missionCache = new CfgCacheMapBase<cfg_family_active_mission>(cache_mgr.cfg_family_active_mission_cache, SEARCH_TYPE.ID, "type");
        cachemap_mgr.cfg_family_bossCache = new CfgCacheMapBase<cfg_family_boss>(cache_mgr.cfg_family_boss_cache, SEARCH_TYPE.KEY, "pass");
        cachemap_mgr.cfg_family_boss_attrCache = new CfgCacheMapBase<cfg_family_boss_attr>(cache_mgr.cfg_family_boss_attr_cache, SEARCH_TYPE.KEY, "times");
        cachemap_mgr.cfg_family_boss_miscCache = new CfgCacheMapBase<cfg_family_boss_misc>(cache_mgr.cfg_family_boss_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_family_boss_rankCache = new CfgCacheMapBase<cfg_family_boss_rank[]>(cache_mgr.cfg_family_boss_rank_cache, SEARCH_TYPE.GRP, "pass");
        cachemap_mgr.cfg_family_etcCacheTemp = new CfgCacheMapBase<cfg_family_etc>(cache_mgr.cfg_family_etc_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_family_hongbaoCache = new CfgCacheMapBase<cfg_family_hongbao>(cache_mgr.cfg_family_hongbao_cache, SEARCH_TYPE.ID, "type");
        cachemap_mgr.cfg_family_hongbaoItemCache = new CfgCacheMapBase<cfg_family_hongbao>(cache_mgr.cfg_family_hongbao_cache, SEARCH_TYPE.KEY, "item_type_id");
        cachemap_mgr.cfg_family_hongbao_blessingCache = new CfgCacheMapBase<cfg_family_hongbao_blessing>(cache_mgr.cfg_family_hongbao_blessing_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_family_hongbao_rewardCache = new CfgCacheMapBase<cfg_family_hongbao_reward[]>(cache_mgr.cfg_family_hongbao_reward_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_family_logCache = new CfgCacheMapBase<cfg_family_log>(cache_mgr.cfg_family_log_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_family_random_nameCache = new CfgCacheMapBase<cfg_family_random_name>(cache_mgr.cfg_family_random_name_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_family_scienceCacheTemp = new CfgCacheMapBase<cfg_family_science>(cache_mgr.cfg_family_science_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_family_science_timesCacheTemp = new CfgCacheMapBase<cfg_family_science_times>(cache_mgr.cfg_family_science_times_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_family_signCache = new CfgCacheMapBase<cfg_family_sign>(cache_mgr.cfg_family_sign_cache, SEARCH_TYPE.ID, "type");
        cachemap_mgr.cfg_family_sign_activeCache = new CfgCacheMapBase<cfg_family_sign_active>(cache_mgr.cfg_family_sign_active_cache, SEARCH_TYPE.ID, "need_score");
        cachemap_mgr.cfg_fight_showCache = new CfgCacheMapBase<cfg_fight_show>(cache_mgr.cfg_fight_show_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_first_payCache = new CfgCacheMapBase<cfg_first_pay>(cache_mgr.cfg_first_pay_cache, SEARCH_TYPE.ID, "gift_id");
        cachemap_mgr.cfg_fishCache = new CfgCacheMapBase<cfg_fish>(cache_mgr.cfg_fish_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_fishByGroupCache = new CfgCacheMapBase<cfg_fish[]>(cache_mgr.cfg_fish_cache, SEARCH_TYPE.GRP, "slot");
        cachemap_mgr.cfg_fish_attrCache = new CfgCacheMapBase<cfg_fish_attr>(cache_mgr.cfg_fish_attr_cache, SEARCH_TYPE.ID, "attr_key");
        cachemap_mgr.cfg_fish_colorCache = new CfgCacheMapBase<cfg_fish_color>(cache_mgr.cfg_fish_color_cache, SEARCH_TYPE.ID, "color");
        cachemap_mgr.cfg_fish_fishbowlCache = new CfgCacheMapBase<cfg_fish_fishbowl>(cache_mgr.cfg_fish_fishbowl_cache, SEARCH_TYPE.ID, "fishbowl_id");
        cachemap_mgr.cfg_fish_handbookCache = new CfgCacheMapBase<cfg_fish_handbook[]>(cache_mgr.cfg_fish_handbook_cache, SEARCH_TYPE.GRP, "map_id");
        cachemap_mgr.cfg_fish_handbookByIdCache = new CfgCacheMapBase<cfg_fish_handbook>(cache_mgr.cfg_fish_handbook_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_fish_help_giftCache = new CfgCacheMapBase<cfg_fish_help_gift>(cache_mgr.cfg_fish_help_gift_cache, SEARCH_TYPE.ID, "gift_id");
        cachemap_mgr.cfg_fish_levelCache = new CfgCacheMapBase<cfg_fish_level>(cache_mgr.cfg_fish_level_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_fish_mapCache = new CfgCacheMapBase<cfg_fish_map>(cache_mgr.cfg_fish_map_cache, SEARCH_TYPE.ID, "map_id");
        cachemap_mgr.cfg_fish_miscCache = new CfgCacheMapBase<cfg_fish_misc>(cache_mgr.cfg_fish_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_fish_missionCache = new CfgCacheMapBase<cfg_fish_mission>(cache_mgr.cfg_fish_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_fish_mission_guide_idCache = new CfgCacheMapBase<cfg_fish_mission>(cache_mgr.cfg_fish_mission_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_fish_official_positionCache = new CfgCacheMapBase<cfg_fish_official_position>(cache_mgr.cfg_fish_official_position_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_fish_reshapeCache = new CfgCacheMapBase<cfg_fish_reshape>(cache_mgr.cfg_fish_reshape_cache, SEARCH_TYPE.MKEY, "slot,color");
        cachemap_mgr.cfg_fish_resourcesCache = new CfgCacheMapBase<cfg_fish_resources>(cache_mgr.cfg_fish_resources_cache, SEARCH_TYPE.MKEY, "map_id,type_id");
        cachemap_mgr.cfg_fish_resourcesByHandbookTypeCache = new CfgCacheMapBase<cfg_fish_resources[]>(cache_mgr.cfg_fish_resources_cache, SEARCH_TYPE.GRP, "handbook_type");
        cachemap_mgr.cfg_fish_rod_levelCache = new CfgCacheMapBase<cfg_fish_rod_level>(cache_mgr.cfg_fish_rod_level_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_fish_slotCache = new CfgCacheMapBase<cfg_fish_slot>(cache_mgr.cfg_fish_slot_cache, SEARCH_TYPE.ID, "slot");
        cachemap_mgr.cfg_fish_stageCache = new CfgCacheMapBase<cfg_fish_stage>(cache_mgr.cfg_fish_stage_cache, SEARCH_TYPE.ID, "stage");
        cachemap_mgr.cfg_fly_font_typeCache = new CfgCacheMapBase<cfg_fly_font_type>(cache_mgr.cfg_fly_font_type_cache, SEARCH_TYPE.KEY, "from_type");
        cachemap_mgr.cfg_fuli_signCache = new CfgCacheMapBase<cfg_fuli_sign[]>(cache_mgr.cfg_fuli_sign_cache, SEARCH_TYPE.GRP, "id");
        cachemap_mgr.cfg_fuli_sign_accCache = new CfgCacheMapBase<cfg_fuli_sign_acc>(cache_mgr.cfg_fuli_sign_acc_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_fuli_sign_dayCache = new CfgCacheMapBase<cfg_fuli_sign_day>(cache_mgr.cfg_fuli_sign_day_cache, SEARCH_TYPE.ID, "days_num");
        cachemap_mgr.cfg_fuli_tokenCache = new CfgCacheMapBase<cfg_fuli_token>(cache_mgr.cfg_fuli_token_cache, SEARCH_TYPE.MKEY, "type,level,rewards_id");
        cachemap_mgr.cfg_fuli_tokenByLevelCache = new CfgCacheMapBase<cfg_fuli_token[]>(cache_mgr.cfg_fuli_token_cache, SEARCH_TYPE.MGRP, "type,level");
        cachemap_mgr.cfg_fuli_token_typeCache = new CfgCacheMapBase<cfg_fuli_token_type>(cache_mgr.cfg_fuli_token_type_cache, SEARCH_TYPE.MKEY, "type,level");
        cachemap_mgr.cfg_fuli_yuekaCache = new CfgCacheMapBase<cfg_fuli_yueka>(cache_mgr.cfg_fuli_yueka_cache, SEARCH_TYPE.ID, "yueka_type");
        cachemap_mgr.cfg_game_descCache = new CfgCacheMapBase<cfg_game_desc>(cache_mgr.cfg_game_desc_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_game_desc_2CacheTemp = new CfgCacheMapBase<cfg_game_desc_2>(cache_mgr.cfg_game_desc_2_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_game_desc_3Cache = new CfgCacheMapBase<cfg_game_desc_3[]>(cache_mgr.cfg_game_desc_3_cache, SEARCH_TYPE.GRP, "group_name");
        cachemap_mgr.cfg_general_pass_missionCache = new CfgCacheMapBase<cfg_general_pass_mission>(cache_mgr.cfg_general_pass_mission_cache, SEARCH_TYPE.MKEY, "task_id,pass_type,turn");
        cachemap_mgr.cfg_general_pass_missionBypass_typeandturnCache = new CfgCacheMapBase<cfg_general_pass_mission[]>(cache_mgr.cfg_general_pass_mission_cache, SEARCH_TYPE.MGRP, "pass_type,turn");
        cachemap_mgr.cfg_general_pass_rewardCache = new CfgCacheMapBase<cfg_general_pass_reward>(cache_mgr.cfg_general_pass_reward_cache, SEARCH_TYPE.MKEY, "turn,pass_type,sort_id");
        cachemap_mgr.cfg_general_pass_rewardByTurnCache = new CfgCacheMapBase<cfg_general_pass_reward[]>(cache_mgr.cfg_general_pass_reward_cache, SEARCH_TYPE.MGRP, "turn,pass_type");
        cachemap_mgr.cfg_general_pass_rewardIdCache = new CfgCacheMapBase<cfg_general_pass_reward>(cache_mgr.cfg_general_pass_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_general_pass_typeCache = new CfgCacheMapBase<cfg_general_pass_type>(cache_mgr.cfg_general_pass_type_cache, SEARCH_TYPE.MKEY, "turn,pass_type");
        cachemap_mgr.cfg_general_pass_typeByprocess_typeCache = new CfgCacheMapBase<cfg_general_pass_type[]>(cache_mgr.cfg_general_pass_type_cache, SEARCH_TYPE.GRP, "pass_type");
        cachemap_mgr.cfg_giftCache = new CfgCacheMapBase<cfg_gift>(cache_mgr.cfg_gift_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_god_equipCache = new CfgCacheMapBase<cfg_god_equip>(cache_mgr.cfg_god_equip_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_god_equip_starCache = new CfgCacheMapBase<cfg_god_equip[]>(cache_mgr.cfg_god_equip_cache, SEARCH_TYPE.GRP, "star");
        cachemap_mgr.cfg_god_equip_composeCache = new CfgCacheMapBase<cfg_god_equip_compose>(cache_mgr.cfg_god_equip_compose_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_god_equip_compose_itemCache = new CfgCacheMapBase<cfg_god_equip_compose>(cache_mgr.cfg_god_equip_compose_cache, SEARCH_TYPE.KEY, "item_id");
        cachemap_mgr.cfg_god_equip_convertCache = new CfgCacheMapBase<cfg_god_equip_convert>(cache_mgr.cfg_god_equip_convert_cache, SEARCH_TYPE.KEY, "star");
        cachemap_mgr.cfg_god_equip_enchantCache = new CfgCacheMapBase<cfg_god_equip_enchant>(cache_mgr.cfg_god_equip_enchant_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_god_equip_enchant_costCache = new CfgCacheMapBase<cfg_god_equip_enchant_cost>(cache_mgr.cfg_god_equip_enchant_cost_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_god_equip_suitCache = new CfgCacheMapBase<cfg_god_equip_suit>(cache_mgr.cfg_god_equip_suit_cache, SEARCH_TYPE.MKEY, "star,suit_id");
        cachemap_mgr.cfg_god_equip_suitByStarCache = new CfgCacheMapBase<cfg_god_equip_suit[]>(cache_mgr.cfg_god_equip_suit_cache, SEARCH_TYPE.GRP, "star");
        cachemap_mgr.cfg_god_equip_typeCache = new CfgCacheMapBase<cfg_god_equip_type>(cache_mgr.cfg_god_equip_type_cache, SEARCH_TYPE.MKEY, "type,color,star");
        cachemap_mgr.cfg_god_trialCache = new CfgCacheMapBase<cfg_god_trial>(cache_mgr.cfg_god_trial_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_god_trial_buffCache = new CfgCacheMapBase<cfg_god_trial_buff>(cache_mgr.cfg_god_trial_buff_cache, SEARCH_TYPE.ID, "buff_id");
        cachemap_mgr.cfg_god_weaponCache = new CfgCacheMapBase<cfg_god_weapon>(cache_mgr.cfg_god_weapon_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_god_weapon_levelCache = new CfgCacheMapBase<cfg_god_weapon_level>(cache_mgr.cfg_god_weapon_level_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_god_weapon_missionCache = new CfgCacheMapBase<cfg_god_weapon_mission>(cache_mgr.cfg_god_weapon_mission_cache, SEARCH_TYPE.ID, "task_id");
        cachemap_mgr.cfg_god_weapon_missionTypeCache = new CfgCacheMapBase<cfg_god_weapon_mission[]>(cache_mgr.cfg_god_weapon_mission_cache, SEARCH_TYPE.GRP, "type_id");
        cachemap_mgr.cfg_god_weapon_refineCache = new CfgCacheMapBase<cfg_god_weapon_refine>(cache_mgr.cfg_god_weapon_refine_cache, SEARCH_TYPE.MKEY, "level,plan_id");
        cachemap_mgr.cfg_god_weapon_plan_refineCache = new CfgCacheMapBase<cfg_god_weapon_refine>(cache_mgr.cfg_god_weapon_refine_cache, SEARCH_TYPE.MKEY, "level,plan_id");
        cachemap_mgr.cfg_god_weapon_skillCache = new CfgCacheMapBase<cfg_god_weapon_skill>(cache_mgr.cfg_god_weapon_skill_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_god_weapon_skill_attrTypeCache = new CfgCacheMapBase<cfg_god_weapon_skill_attr[]>(cache_mgr.cfg_god_weapon_skill_attr_cache, SEARCH_TYPE.GRP, "id");
        cachemap_mgr.cfg_god_weapon_soulCache = new CfgCacheMapBase<cfg_god_weapon_soul>(cache_mgr.cfg_god_weapon_soul_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_god_weapon_starCache = new CfgCacheMapBase<cfg_god_weapon_star>(cache_mgr.cfg_god_weapon_star_cache, SEARCH_TYPE.MKEY, "god_weapon_id,star");
        cachemap_mgr.cfg_gray_pinyinCacheTemp = new CfgCacheMapBase<cfg_gray_pinyin>(cache_mgr.cfg_gray_pinyin_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_grow_tipsCache = new CfgCacheMapBase<cfg_grow_tips>(cache_mgr.cfg_grow_tips_cache, SEARCH_TYPE.KEY, "sys_id");
        cachemap_mgr.cfg_grow_tipsTagMap = new CfgCacheMapBase<cfg_grow_tips[]>(cache_mgr.cfg_grow_tips_cache, SEARCH_TYPE.GRP, "show_tag");
        cachemap_mgr.cfg_guaji_box_timeCache = new CfgCacheMapBase<cfg_guaji_box_time>(cache_mgr.cfg_guaji_box_time_cache, SEARCH_TYPE.ID, "min_time");
        cachemap_mgr.cfg_guaji_monsterCache = new CfgCacheMapBase<cfg_guaji_monster>(cache_mgr.cfg_guaji_monster_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_guaji_quick_navigationCache = new CfgCacheMapBase<cfg_guaji_quick_navigation>(cache_mgr.cfg_guaji_quick_navigation_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_guanduCache = new CfgCacheMapBase<cfg_guandu[]>(cache_mgr.cfg_guandu_cache, SEARCH_TYPE.GRP, "floor");
        cachemap_mgr.cfg_guandu_answerCache = new CfgCacheMapBase<cfg_guandu_answer>(cache_mgr.cfg_guandu_answer_cache, SEARCH_TYPE.ID, "title_id");
        cachemap_mgr.cfg_guandu_choseCache = new CfgCacheMapBase<cfg_guandu_chose>(cache_mgr.cfg_guandu_chose_cache, SEARCH_TYPE.ID, "title_id");
        cachemap_mgr.cfg_guandu_floorCache = new CfgCacheMapBase<cfg_guandu_floor>(cache_mgr.cfg_guandu_floor_cache, SEARCH_TYPE.ID, "floor");
        cachemap_mgr.cfg_guandu_missionCache = new CfgCacheMapBase<cfg_guandu_mission>(cache_mgr.cfg_guandu_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_guandu_shopCache = new CfgCacheMapBase<cfg_guandu_shop>(cache_mgr.cfg_guandu_shop_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_guide_helperCache = new CfgCacheMapBase<cfg_guide_helper[]>(cache_mgr.cfg_guide_helper_cache, SEARCH_TYPE.GRP, "guide_id");
        cachemap_mgr.cfg_guide_helper_1Cache = new CfgCacheMapBase<cfg_guide_helper_1[]>(cache_mgr.cfg_guide_helper_1_cache, SEARCH_TYPE.GRP, "guide_id");
        cachemap_mgr.cfg_guide_helper_2Cache = new CfgCacheMapBase<cfg_guide_helper_2[]>(cache_mgr.cfg_guide_helper_2_cache, SEARCH_TYPE.GRP, "guide_id");
        cachemap_mgr.cfg_guide_helper_3Cache = new CfgCacheMapBase<cfg_guide_helper_3[]>(cache_mgr.cfg_guide_helper_3_cache, SEARCH_TYPE.GRP, "guide_id");
        cachemap_mgr.cfg_guide_helper_4Cache = new CfgCacheMapBase<cfg_guide_helper_4[]>(cache_mgr.cfg_guide_helper_4_cache, SEARCH_TYPE.GRP, "guide_id");
        cachemap_mgr.cfg_guide_helper_game_1Cache = new CfgCacheMapBase<cfg_guide_helper_game_1[]>(cache_mgr.cfg_guide_helper_game_1_cache, SEARCH_TYPE.GRP, "guide_id");
        cachemap_mgr.cfg_guide_helper_m2Cache = new CfgCacheMapBase<cfg_guide_helper_m2[]>(cache_mgr.cfg_guide_helper_m2_cache, SEARCH_TYPE.GRP, "guide_id");
        cachemap_mgr.cfg_guide_helper_m3Cache = new CfgCacheMapBase<cfg_guide_helper_m3[]>(cache_mgr.cfg_guide_helper_m3_cache, SEARCH_TYPE.GRP, "guide_id");
        cachemap_mgr.cfg_guide_missionCache = new CfgCacheMapBase<cfg_guide_mission>(cache_mgr.cfg_guide_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_guide_uinameCache = new CfgCacheMapBase<cfg_guide_mission>(cache_mgr.cfg_guide_mission_cache, SEARCH_TYPE.KEY, "ui_name");
        cachemap_mgr.guideRearIdsCache = new CfgCacheMapBase<cfg_guide_mission>(cache_mgr.cfg_guide_mission_cache, SEARCH_TYPE.KEY, "pre_id");
        cachemap_mgr.cfg_guide_mission_1Cache = new CfgCacheMapBase<cfg_guide_mission_1>(cache_mgr.cfg_guide_mission_1_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_guide_uiname_1Cache = new CfgCacheMapBase<cfg_guide_mission_1>(cache_mgr.cfg_guide_mission_1_cache, SEARCH_TYPE.KEY, "ui_name");
        cachemap_mgr.guideRearIds_1Cache = new CfgCacheMapBase<cfg_guide_mission_1>(cache_mgr.cfg_guide_mission_1_cache, SEARCH_TYPE.KEY, "pre_id");
        cachemap_mgr.cfg_guide_mission_2Cache = new CfgCacheMapBase<cfg_guide_mission_2>(cache_mgr.cfg_guide_mission_2_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_guide_uiname_2Cache = new CfgCacheMapBase<cfg_guide_mission_2>(cache_mgr.cfg_guide_mission_2_cache, SEARCH_TYPE.KEY, "ui_name");
        cachemap_mgr.guideRearIds_2Cache = new CfgCacheMapBase<cfg_guide_mission_2>(cache_mgr.cfg_guide_mission_2_cache, SEARCH_TYPE.KEY, "pre_id");
        cachemap_mgr.cfg_guide_mission_3Cache = new CfgCacheMapBase<cfg_guide_mission_3>(cache_mgr.cfg_guide_mission_3_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_guide_uiname_3Cache = new CfgCacheMapBase<cfg_guide_mission_3>(cache_mgr.cfg_guide_mission_3_cache, SEARCH_TYPE.KEY, "ui_name");
        cachemap_mgr.guideRearIds_3Cache = new CfgCacheMapBase<cfg_guide_mission_3>(cache_mgr.cfg_guide_mission_3_cache, SEARCH_TYPE.KEY, "pre_id");
        cachemap_mgr.cfg_guide_mission_4Cache = new CfgCacheMapBase<cfg_guide_mission_4>(cache_mgr.cfg_guide_mission_4_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_guide_uiname_4Cache = new CfgCacheMapBase<cfg_guide_mission_4>(cache_mgr.cfg_guide_mission_4_cache, SEARCH_TYPE.KEY, "ui_name");
        cachemap_mgr.guideRearIds_4Cache = new CfgCacheMapBase<cfg_guide_mission_4>(cache_mgr.cfg_guide_mission_4_cache, SEARCH_TYPE.KEY, "pre_id");
        cachemap_mgr.cfg_guide_mission_game_1Cache = new CfgCacheMapBase<cfg_guide_mission_game_1>(cache_mgr.cfg_guide_mission_game_1_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_guide_uiname_game_1Cache = new CfgCacheMapBase<cfg_guide_mission_game_1>(cache_mgr.cfg_guide_mission_game_1_cache, SEARCH_TYPE.KEY, "ui_name");
        cachemap_mgr.guideRearIds_game_1Cache = new CfgCacheMapBase<cfg_guide_mission_game_1>(cache_mgr.cfg_guide_mission_game_1_cache, SEARCH_TYPE.KEY, "pre_id");
        cachemap_mgr.cfg_guide_mission_m2Cache = new CfgCacheMapBase<cfg_guide_mission_m2>(cache_mgr.cfg_guide_mission_m2_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_guide_uiname_m2Cache = new CfgCacheMapBase<cfg_guide_mission_m2>(cache_mgr.cfg_guide_mission_m2_cache, SEARCH_TYPE.KEY, "ui_name");
        cachemap_mgr.guideRearIds_m2Cache = new CfgCacheMapBase<cfg_guide_mission_m2>(cache_mgr.cfg_guide_mission_m2_cache, SEARCH_TYPE.KEY, "pre_id");
        cachemap_mgr.cfg_guide_mission_m3Cache = new CfgCacheMapBase<cfg_guide_mission_m3>(cache_mgr.cfg_guide_mission_m3_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_guide_uiname_m3Cache = new CfgCacheMapBase<cfg_guide_mission_m3>(cache_mgr.cfg_guide_mission_m3_cache, SEARCH_TYPE.KEY, "ui_name");
        cachemap_mgr.guideRearIds_m3Cache = new CfgCacheMapBase<cfg_guide_mission_m3>(cache_mgr.cfg_guide_mission_m3_cache, SEARCH_TYPE.KEY, "pre_id");
        cachemap_mgr.cfg_guide_reviewCache = new CfgCacheMapBase<cfg_guide_review>(cache_mgr.cfg_guide_review_cache, SEARCH_TYPE.KEY, "platform_id");
        cachemap_mgr.cfg_guide_storyCache = new CfgCacheMapBase<cfg_guide_story>(cache_mgr.cfg_guide_story_cache, SEARCH_TYPE.KEY, "platform_id");
        cachemap_mgr.cfg_hero_attr_addition_careerCache = new CfgCacheMapBase<cfg_hero_attr_addition[]>(cache_mgr.cfg_hero_attr_addition_cache, SEARCH_TYPE.GRP, "career");
        cachemap_mgr.cfg_hero_attr_addition_nationCache = new CfgCacheMapBase<cfg_hero_attr_addition[]>(cache_mgr.cfg_hero_attr_addition_cache, SEARCH_TYPE.GRP, "nation");
        cachemap_mgr.cfg_hero_attr_sourceCache = new CfgCacheMapBase<cfg_hero_attr_source>(cache_mgr.cfg_hero_attr_source_cache, SEARCH_TYPE.KEY, "type");
        cachemap_mgr.cfg_hero_bagCache = new CfgCacheMapBase<cfg_hero_bag>(cache_mgr.cfg_hero_bag_cache, SEARCH_TYPE.ID, "count");
        cachemap_mgr.cfg_hero_baseCache = new CfgCacheMapBase<cfg_hero_base>(cache_mgr.cfg_hero_base_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_hero_base_chipIdCache = new CfgCacheMapBase<cfg_hero_base>(cache_mgr.cfg_hero_base_cache, SEARCH_TYPE.KEY, "hero_chip_id");
        cachemap_mgr.cfg_hero_base_completeIdCache = new CfgCacheMapBase<cfg_hero_base>(cache_mgr.cfg_hero_base_cache, SEARCH_TYPE.KEY, "hero_complete_id");
        cachemap_mgr.cfg_hero_cheerCache = new CfgCacheMapBase<cfg_hero_cheer>(cache_mgr.cfg_hero_cheer_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_hero_cheerByNationCache = new CfgCacheMapBase<cfg_hero_cheer[]>(cache_mgr.cfg_hero_cheer_cache, SEARCH_TYPE.GRP, "nation");
        cachemap_mgr.cfg_hero_cheer_bonusCache = new CfgCacheMapBase<cfg_hero_cheer_bonus>(cache_mgr.cfg_hero_cheer_bonus_cache, SEARCH_TYPE.ID, "nation");
        cachemap_mgr.cfg_hero_cheer_levelCache = new CfgCacheMapBase<cfg_hero_cheer_level>(cache_mgr.cfg_hero_cheer_level_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_hero_cheer_unlockCache = new CfgCacheMapBase<cfg_hero_cheer_unlock>(cache_mgr.cfg_hero_cheer_unlock_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_hero_chip_starCache = new CfgCacheMapBase<cfg_hero_chip_star>(cache_mgr.cfg_hero_chip_star_cache, SEARCH_TYPE.MKEY, "source_star,target_star");
        cachemap_mgr.cfg_hero_come_missionCache = new CfgCacheMapBase<cfg_hero_come_mission>(cache_mgr.cfg_hero_come_mission_cache, SEARCH_TYPE.ID, "event_id");
        cachemap_mgr.cfg_hero_come_missionTypeCache = new CfgCacheMapBase<cfg_hero_come_mission[]>(cache_mgr.cfg_hero_come_mission_cache, SEARCH_TYPE.MGRP, "plan_id,type_id");
        cachemap_mgr.cfg_hero_convertCache = new CfgCacheMapBase<cfg_hero_convert>(cache_mgr.cfg_hero_convert_cache, SEARCH_TYPE.MKEY, "nation,star");
        cachemap_mgr.cfg_hero_convert_weightCache = new CfgCacheMapBase<cfg_hero_convert_weight[]>(cache_mgr.cfg_hero_convert_weight_cache, SEARCH_TYPE.GRP, "hero_type_id");
        cachemap_mgr.cfg_hero_cost_planCache = new CfgCacheMapBase<cfg_hero_cost_plan>(cache_mgr.cfg_hero_cost_plan_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_hero_evolve_skillCache = new CfgCacheMapBase<cfg_hero_evolve_skill>(cache_mgr.cfg_hero_evolve_skill_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_hero_handbook_descCache = new CfgCacheMapBase<cfg_hero_handbook_desc>(cache_mgr.cfg_hero_handbook_desc_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_hero_levelCache = new CfgCacheMapBase<cfg_hero_level>(cache_mgr.cfg_hero_level_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_hero_level_limitCache = new CfgCacheMapBase<cfg_hero_level_limit>(cache_mgr.cfg_hero_level_limit_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_hero_level_limit_typeCache = new CfgCacheMapBase<cfg_hero_level_limit>(cache_mgr.cfg_hero_level_limit_cache, SEARCH_TYPE.MKEY, "type,value");
        cachemap_mgr.cfg_hero_nationCache = new CfgCacheMapBase<cfg_hero_nation>(cache_mgr.cfg_hero_nation_cache, SEARCH_TYPE.ID, "nation");
        cachemap_mgr.cfg_hero_pass_missionCache = new CfgCacheMapBase<cfg_hero_pass_mission>(cache_mgr.cfg_hero_pass_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_hero_pass_rewardCache = new CfgCacheMapBase<cfg_hero_pass_reward>(cache_mgr.cfg_hero_pass_reward_cache, SEARCH_TYPE.MKEY, "level,sort_id");
        cachemap_mgr.cfg_hero_pass_rewardByTurnCache = new CfgCacheMapBase<cfg_hero_pass_reward[]>(cache_mgr.cfg_hero_pass_reward_cache, SEARCH_TYPE.GRP, "level");
        cachemap_mgr.cfg_hero_recommend_preCache = new CfgCacheMapBase<cfg_hero_recommend_pre>(cache_mgr.cfg_hero_recommend_pre_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_hero_recycleCache = new CfgCacheMapBase<cfg_hero_recycle>(cache_mgr.cfg_hero_recycle_cache, SEARCH_TYPE.ID, "count");
        cachemap_mgr.cfg_hero_recycle_changeCacheTemp = new CfgCacheMapBase<cfg_hero_recycle_change>(cache_mgr.cfg_hero_recycle_change_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_hero_recycle_change_star_stageCacheTemp = new CfgCacheMapBase<cfg_hero_recycle_change_star_stage>(cache_mgr.cfg_hero_recycle_change_star_stage_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_hero_recycleCaches = new CfgCacheMapBase<cfg_hero_recycle_res>(cache_mgr.cfg_hero_recycle_res_cache, SEARCH_TYPE.MKEY, "type,value");
        cachemap_mgr.cfg_hero_recycle_special_switchCache = new CfgCacheMapBase<cfg_hero_recycle_special_switch>(cache_mgr.cfg_hero_recycle_special_switch_cache, SEARCH_TYPE.KEY, "type");
        cachemap_mgr.cfg_hero_resonate_dhyanaCache = new CfgCacheMapBase<cfg_hero_resonate_dhyana>(cache_mgr.cfg_hero_resonate_dhyana_cache, SEARCH_TYPE.ID, "add_level");
        cachemap_mgr.cfg_hero_resonate_duduCache = new CfgCacheMapBase<cfg_hero_resonate_dudu>(cache_mgr.cfg_hero_resonate_dudu_cache, SEARCH_TYPE.ID, "slot_id");
        cachemap_mgr.cfg_hero_resonate_dudu_levelCache = new CfgCacheMapBase<cfg_hero_resonate_dudu_level>(cache_mgr.cfg_hero_resonate_dudu_level_cache, SEARCH_TYPE.ID, "star");
        cachemap_mgr.cfg_hero_resonate_fiveCache = new CfgCacheMapBase<cfg_hero_resonate_five>(cache_mgr.cfg_hero_resonate_five_cache, SEARCH_TYPE.ID, "slot_id");
        cachemap_mgr.cfg_hero_skinCache = new CfgCacheMapBase<cfg_hero_skin>(cache_mgr.cfg_hero_skin_cache, SEARCH_TYPE.ID, "skin_id");
        cachemap_mgr.cfg_hero_skin_listCache = new CfgCacheMapBase<cfg_hero_skin[]>(cache_mgr.cfg_hero_skin_cache, SEARCH_TYPE.GRP, "hero_type_id");
        cachemap_mgr.cfg_hero_skinByIconCaChe = new CfgCacheMapBase<cfg_hero_skin>(cache_mgr.cfg_hero_skin_cache, SEARCH_TYPE.KEY, "item_id");
        cachemap_mgr.cfg_hero_skin_levelCache = new CfgCacheMapBase<cfg_hero_skin_level>(cache_mgr.cfg_hero_skin_level_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_hero_skin_level_listCache = new CfgCacheMapBase<cfg_hero_skin_level[]>(cache_mgr.cfg_hero_skin_level_cache, SEARCH_TYPE.GRP, "skin_id");
        cachemap_mgr.cfg_hero_skin_attriCache = new CfgCacheMapBase<cfg_hero_skin_level>(cache_mgr.cfg_hero_skin_level_cache, SEARCH_TYPE.MKEY, "skin_id,level");
        cachemap_mgr.cfg_hero_stageCache = new CfgCacheMapBase<cfg_hero_stage>(cache_mgr.cfg_hero_stage_cache, SEARCH_TYPE.ID, "stage");
        cachemap_mgr.cfg_hero_stage_limitCache = new CfgCacheMapBase<cfg_hero_stage_limit>(cache_mgr.cfg_hero_stage_limit_cache, SEARCH_TYPE.ID, "star");
        cachemap_mgr.cfg_hero_starCache = new CfgCacheMapBase<cfg_hero_star[]>(cache_mgr.cfg_hero_star_cache, SEARCH_TYPE.GRP, "type_id");
        cachemap_mgr.cfg_hero_starCache2 = new CfgCacheMapBase<cfg_hero_star>(cache_mgr.cfg_hero_star_cache, SEARCH_TYPE.MKEY, "type_id,star");
        cachemap_mgr.cfg_hero_star_attrCache = new CfgCacheMapBase<cfg_hero_star_attr>(cache_mgr.cfg_hero_star_attr_cache, SEARCH_TYPE.ID, "star");
        cachemap_mgr.cfg_hero_star_limitCache = new CfgCacheMapBase<cfg_hero_star_limit>(cache_mgr.cfg_hero_star_limit_cache, SEARCH_TYPE.MKEY, "star,star_stage");
        cachemap_mgr.cfg_hero_star_limit_listCache = new CfgCacheMapBase<cfg_hero_star_limit[]>(cache_mgr.cfg_hero_star_limit_cache, SEARCH_TYPE.GRP, "star");
        cachemap_mgr.cfg_hero_star_stageCache = new CfgCacheMapBase<cfg_hero_star_stage[]>(cache_mgr.cfg_hero_star_stage_cache, SEARCH_TYPE.MGRP, "type_id,star");
        cachemap_mgr.cfg_hero_star_stageCache2 = new CfgCacheMapBase<cfg_hero_star_stage>(cache_mgr.cfg_hero_star_stage_cache, SEARCH_TYPE.MKEY, "type_id,star,star_stage");
        cachemap_mgr.cfg_hero_star_stage_attrCache = new CfgCacheMapBase<cfg_hero_star_stage_attr>(cache_mgr.cfg_hero_star_stage_attr_cache, SEARCH_TYPE.MKEY, "star,star_stage");
        cachemap_mgr.cfg_hero_star_stage_attr_listCache = new CfgCacheMapBase<cfg_hero_star_stage_attr[]>(cache_mgr.cfg_hero_star_stage_attr_cache, SEARCH_TYPE.GRP, "star");
        cachemap_mgr.cfg_hero_strengthenCache = new CfgCacheMapBase<cfg_hero_strengthen>(cache_mgr.cfg_hero_strengthen_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_hero_upgrade_tipsCache = new CfgCacheMapBase<cfg_hero_upgrade_tips>(cache_mgr.cfg_hero_upgrade_tips_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_hero_zhouyinCache = new CfgCacheMapBase<cfg_hero_zhouyin>(cache_mgr.cfg_hero_zhouyin_cache, SEARCH_TYPE.KEY, "type_id");
        cachemap_mgr.cfg_hunt_buyCache = new CfgCacheMapBase<cfg_hunt_buy>(cache_mgr.cfg_hunt_buy_cache, SEARCH_TYPE.MKEY, "big_type,num_type");
        cachemap_mgr.cfg_hunt_costCache = new CfgCacheMapBase<cfg_hunt_cost>(cache_mgr.cfg_hunt_cost_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_hunt_descCache = new CfgCacheMapBase<cfg_hunt_desc[]>(cache_mgr.cfg_hunt_desc_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_hunt_giftCache = new CfgCacheMapBase<cfg_hunt_gift>(cache_mgr.cfg_hunt_gift_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_hunt_rewards_showCache = new CfgCacheMapBase<cfg_hunt_rewards_show>(cache_mgr.cfg_hunt_rewards_show_cache, SEARCH_TYPE.ID, "big_type");
        cachemap_mgr.cfg_huoqutujingCache = new CfgCacheMapBase<cfg_huoqutujing[]>(cache_mgr.cfg_huoqutujing_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_hzzd_achievementCache = new CfgCacheMapBase<cfg_hzzd_achievement>(cache_mgr.cfg_hzzd_achievement_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_hzzd_eventCache = new CfgCacheMapBase<cfg_hzzd_event>(cache_mgr.cfg_hzzd_event_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_hzzd_kills_rewardCache = new CfgCacheMapBase<cfg_hzzd_kills_reward>(cache_mgr.cfg_hzzd_kills_reward_cache, SEARCH_TYPE.ID, "kills_num");
        cachemap_mgr.cfg_hzzd_miscCache = new CfgCacheMapBase<cfg_hzzd_misc>(cache_mgr.cfg_hzzd_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_i18n_langCache = new CfgCacheMapBase<cfg_i18n_lang>(cache_mgr.cfg_i18n_lang_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_i18n_tsCache = new CfgCacheMapBase<cfg_i18n_ts>(cache_mgr.cfg_i18n_ts_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_i18n_uiCache = new CfgCacheMapBase<cfg_i18n_ui>(cache_mgr.cfg_i18n_ui_cache, SEARCH_TYPE.KEY, "lang_tag");
        cachemap_mgr.cfg_ingenious_planCache = new CfgCacheMapBase<cfg_ingenious_plan>(cache_mgr.cfg_ingenious_plan_cache, SEARCH_TYPE.MKEY, "id,star");
        cachemap_mgr.cfg_ingenious_planTypeIdCache = new CfgCacheMapBase<cfg_ingenious_plan>(cache_mgr.cfg_ingenious_plan_cache, SEARCH_TYPE.KEY, "type_id");
        cachemap_mgr.cfg_ingenious_planGroup = new CfgCacheMapBase<cfg_ingenious_plan[]>(cache_mgr.cfg_ingenious_plan_cache, SEARCH_TYPE.GRP, "star");
        cachemap_mgr.cfg_ingenious_planIdGroup = new CfgCacheMapBase<cfg_ingenious_plan[]>(cache_mgr.cfg_ingenious_plan_cache, SEARCH_TYPE.GRP, "id");
        cachemap_mgr.cfg_ingenious_plan_composeCache = new CfgCacheMapBase<cfg_ingenious_plan_compose>(cache_mgr.cfg_ingenious_plan_compose_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_ingenious_plan_convertCache = new CfgCacheMapBase<cfg_ingenious_plan_convert>(cache_mgr.cfg_ingenious_plan_convert_cache, SEARCH_TYPE.KEY, "type_id");
        cachemap_mgr.cfg_ingenious_plan_levelCache = new CfgCacheMapBase<cfg_ingenious_plan_level>(cache_mgr.cfg_ingenious_plan_level_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_ingenious_plan_stageCache = new CfgCacheMapBase<cfg_ingenious_plan_stage>(cache_mgr.cfg_ingenious_plan_stage_cache, SEARCH_TYPE.KEY, "stage");
        cachemap_mgr.cfg_ingenious_plan_starCache = new CfgCacheMapBase<cfg_ingenious_plan_star>(cache_mgr.cfg_ingenious_plan_star_cache, SEARCH_TYPE.KEY, "star");
        cachemap_mgr.cfg_ip_setCache = new CfgCacheMapBase<cfg_ip_set>(cache_mgr.cfg_ip_set_cache, SEARCH_TYPE.KEY, "name");
        cachemap_mgr.cfg_itemCache = new CfgCacheMapBase<cfg_item>(cache_mgr.cfg_item_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_itemKindCache = new CfgCacheMapBase<cfg_item[]>(cache_mgr.cfg_item_cache, SEARCH_TYPE.GRP, "kind");
        cachemap_mgr.cfg_itemAttrCache = new CfgCacheMapBase<cfg_item>(cache_mgr.cfg_item_cache, SEARCH_TYPE.KEY, "trans_attr");
        cachemap_mgr.cfg_item_composeCache = new CfgCacheMapBase<cfg_item_compose>(cache_mgr.cfg_item_compose_cache, SEARCH_TYPE.ID, "item_id");
        cachemap_mgr.cfg_item_compose_target_idCache = new CfgCacheMapBase<cfg_item_compose>(cache_mgr.cfg_item_compose_cache, SEARCH_TYPE.KEY, "target_item_id");
        cachemap_mgr.cfg_item_time_clientCache = new CfgCacheMapBase<cfg_item_time_client>(cache_mgr.cfg_item_time_client_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_large_peak_agentCache = new CfgCacheMapBase<cfg_large_peak_agent>(cache_mgr.cfg_large_peak_agent_cache, SEARCH_TYPE.MKEY, "agent,season");
        cachemap_mgr.cfg_large_peak_battle_table_allCache = new CfgCacheMapBase<cfg_large_peak_battle_table>(cache_mgr.cfg_large_peak_battle_table_cache, SEARCH_TYPE.MKEY, "model,round");
        cachemap_mgr.cfg_large_peak_battle_tableCache = new CfgCacheMapBase<cfg_large_peak_battle_table>(cache_mgr.cfg_large_peak_battle_table_cache, SEARCH_TYPE.MKEY, "key_group,key_round");
        cachemap_mgr.cfg_large_peak_battle_table_groupCache = new CfgCacheMapBase<cfg_large_peak_battle_table[]>(cache_mgr.cfg_large_peak_battle_table_cache, SEARCH_TYPE.GRP, "big_group");
        cachemap_mgr.cfg_large_peak_battle_table_key_groupCache = new CfgCacheMapBase<cfg_large_peak_battle_table[]>(cache_mgr.cfg_large_peak_battle_table_cache, SEARCH_TYPE.GRP, "key_group");
        cachemap_mgr.cfg_large_peak_miscCache = new CfgCacheMapBase<cfg_large_peak_misc>(cache_mgr.cfg_large_peak_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_large_peak_rankCache = new CfgCacheMapBase<cfg_large_peak_rank[]>(cache_mgr.cfg_large_peak_rank_cache, SEARCH_TYPE.GRP, "season");
        cachemap_mgr.cfg_large_peak_seasonCache = new CfgCacheMapBase<cfg_large_peak_season>(cache_mgr.cfg_large_peak_season_cache, SEARCH_TYPE.ID, "season");
        cachemap_mgr.cfg_large_peak_timeCache = new CfgCacheMapBase<cfg_large_peak_time>(cache_mgr.cfg_large_peak_time_cache, SEARCH_TYPE.MKEY, "model,round");
        cachemap_mgr.cfg_large_peak_time_listCache = new CfgCacheMapBase<cfg_large_peak_time[]>(cache_mgr.cfg_large_peak_time_cache, SEARCH_TYPE.GRP, "day");
        cachemap_mgr.cfg_lazy_loadCacheTemp = new CfgCacheMapBase<cfg_lazy_load>(cache_mgr.cfg_lazy_load_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_lcqs_acc_star_rewardCache = new CfgCacheMapBase<cfg_lcqs_acc_star_reward[]>(cache_mgr.cfg_lcqs_acc_star_reward_cache, SEARCH_TYPE.GRP, "chapter");
        cachemap_mgr.cfg_lcqs_chapter_openCache = new CfgCacheMapBase<cfg_lcqs_chapter_open>(cache_mgr.cfg_lcqs_chapter_open_cache, SEARCH_TYPE.ID, "chapter");
        cachemap_mgr.cfg_lcqs_floor_rewardCache = new CfgCacheMapBase<cfg_lcqs_floor_reward[]>(cache_mgr.cfg_lcqs_floor_reward_cache, SEARCH_TYPE.GRP, "chapter");
        cachemap_mgr.cfg_lcqs_missionCache = new CfgCacheMapBase<cfg_lcqs_mission>(cache_mgr.cfg_lcqs_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_levelCache = new CfgCacheMapBase<cfg_level>(cache_mgr.cfg_level_cache, SEARCH_TYPE.KEY, "level");
        cachemap_mgr.cfg_level_giftCache = new CfgCacheMapBase<cfg_level_gift>(cache_mgr.cfg_level_gift_cache, SEARCH_TYPE.ID, "gift_id");
        cachemap_mgr.cfg_limit_hero_skin_chip_exchangeCaches = new CfgCacheMapBase<cfg_limit_hero_skin_chip_exchange>(cache_mgr.cfg_limit_hero_skin_chip_exchange_cache, SEARCH_TYPE.MKEY, "type,value");
        cachemap_mgr.cfg_lineup_buffCache = new CfgCacheMapBase<cfg_lineup_buff>(cache_mgr.cfg_lineup_buff_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_lineup_Buff_iconCache = new CfgCacheMapBase<cfg_lineup_buff_icon>(cache_mgr.cfg_lineup_buff_icon_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_lineup_career_ruleCache = new CfgCacheMapBase<cfg_lineup_career_rule>(cache_mgr.cfg_lineup_career_rule_cache, SEARCH_TYPE.ID, "career");
        cachemap_mgr.cfg_lineup_numCache = new CfgCacheMapBase<cfg_lineup_num>(cache_mgr.cfg_lineup_num_cache, SEARCH_TYPE.ID, "match_type");
        cachemap_mgr.cfg_lineup_recommendCache = new CfgCacheMapBase<cfg_lineup_recommend>(cache_mgr.cfg_lineup_recommend_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_lineup_styleCache = new CfgCacheMapBase<cfg_lineup_style>(cache_mgr.cfg_lineup_style_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_load_tipsCache = new CfgCacheMapBase<cfg_load_tips>(cache_mgr.cfg_load_tips_cache, SEARCH_TYPE.ID, "index");
        cachemap_mgr.cfg_login_activityCache = new CfgCacheMapBase<cfg_login_activity>(cache_mgr.cfg_login_activity_cache, SEARCH_TYPE.MKEY, "round,day");
        cachemap_mgr.cfg_login_activity_roundCache = new CfgCacheMapBase<cfg_login_activity_round>(cache_mgr.cfg_login_activity_round_cache, SEARCH_TYPE.ID, "round");
        cachemap_mgr.cfg_lord_activationCache = new CfgCacheMapBase<cfg_lord_activation>(cache_mgr.cfg_lord_activation_cache, SEARCH_TYPE.ID, "lord_id");
        cachemap_mgr.cfg_lord_baseCache = new CfgCacheMapBase<cfg_lord_base>(cache_mgr.cfg_lord_base_cache, SEARCH_TYPE.ID, "lord_id");
        cachemap_mgr.cfg_lord_campCache = new CfgCacheMapBase<cfg_lord_camp>(cache_mgr.cfg_lord_camp_cache, SEARCH_TYPE.ID, "lord_id");
        cachemap_mgr.cfg_lord_campByGradeCache = new CfgCacheMapBase<cfg_lord_camp[]>(cache_mgr.cfg_lord_camp_cache, SEARCH_TYPE.GRP, "lord_grade");
        cachemap_mgr.cfg_lord_campByNeedCardStageCache = new CfgCacheMapBase<cfg_lord_camp[]>(cache_mgr.cfg_lord_camp_cache, SEARCH_TYPE.GRP, "need_card_stage");
        cachemap_mgr.cfg_lord_campByCamp = new CfgCacheMapBase<cfg_lord_camp[]>(cache_mgr.cfg_lord_camp_cache, SEARCH_TYPE.GRP, "camp");
        cachemap_mgr.cfg_lord_campBySkill_id = new CfgCacheMapBase<cfg_lord_camp>(cache_mgr.cfg_lord_camp_cache, SEARCH_TYPE.KEY, "skill_1");
        cachemap_mgr.cfg_lord_campByStageAndCamp = new CfgCacheMapBase<cfg_lord_camp>(cache_mgr.cfg_lord_camp_cache, SEARCH_TYPE.MKEY, "need_card_stage,camp");
        cachemap_mgr.cfg_lord_exchangeCache = new CfgCacheMapBase<cfg_lord_exchange>(cache_mgr.cfg_lord_exchange_cache, SEARCH_TYPE.ID, "lord_id");
        cachemap_mgr.cfg_lord_skillCache = new CfgCacheMapBase<cfg_lord_skill[]>(cache_mgr.cfg_lord_skill_cache, SEARCH_TYPE.GRP, "skill_id");
        cachemap_mgr.cfg_lord_skillByType = new CfgCacheMapBase<cfg_lord_skill[]>(cache_mgr.cfg_lord_skill_cache, SEARCH_TYPE.GRP, "skill_type");
        cachemap_mgr.cfg_lord_skill_enhanceCache = new CfgCacheMapBase<cfg_lord_skill_enhance>(cache_mgr.cfg_lord_skill_enhance_cache, SEARCH_TYPE.ID, "skill_id");
        cachemap_mgr.cfg_lord_starCache = new CfgCacheMapBase<cfg_lord_star>(cache_mgr.cfg_lord_star_cache, SEARCH_TYPE.ID, "lord_id");
        cachemap_mgr.cfg_lord_suit_composeCache = new CfgCacheMapBase<cfg_lord_suit_compose>(cache_mgr.cfg_lord_suit_compose_cache, SEARCH_TYPE.ID, "select_id");
        cachemap_mgr.cfg_lord_suit_selectCache = new CfgCacheMapBase<cfg_lord_suit_select>(cache_mgr.cfg_lord_suit_select_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_lord_treasureCache = new CfgCacheMapBase<cfg_lord_treasure>(cache_mgr.cfg_lord_treasure_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_lord_treasure_entryCache = new CfgCacheMapBase<cfg_lord_treasure_entry>(cache_mgr.cfg_lord_treasure_entry_cache, SEARCH_TYPE.MKEY, "entry_id,entry_level");
        cachemap_mgr.cfg_lord_treasure_entryByEntry_idCache = new CfgCacheMapBase<cfg_lord_treasure_entry[]>(cache_mgr.cfg_lord_treasure_entry_cache, SEARCH_TYPE.GRP, "entry_id");
        cachemap_mgr.cfg_lord_treasure_forgeCache = new CfgCacheMapBase<cfg_lord_treasure_forge>(cache_mgr.cfg_lord_treasure_forge_cache, SEARCH_TYPE.MKEY, "entry_id,forge_time");
        cachemap_mgr.cfg_lord_treasure_forgeByEntry_idCache = new CfgCacheMapBase<cfg_lord_treasure_forge[]>(cache_mgr.cfg_lord_treasure_forge_cache, SEARCH_TYPE.GRP, "entry_id");
        cachemap_mgr.cfg_lord_treasure_levelCache = new CfgCacheMapBase<cfg_lord_treasure_level>(cache_mgr.cfg_lord_treasure_level_cache, SEARCH_TYPE.MKEY, "type_id,level");
        cachemap_mgr.cfg_lord_treasure_levelBytreasure_idCache = new CfgCacheMapBase<cfg_lord_treasure_level[]>(cache_mgr.cfg_lord_treasure_level_cache, SEARCH_TYPE.GRP, "type_id");
        cachemap_mgr.cfg_lottery_day_limitCache = new CfgCacheMapBase<cfg_lottery_day_limit>(cache_mgr.cfg_lottery_day_limit_cache, SEARCH_TYPE.KEY, "min_day");
        cachemap_mgr.cfg_lottery_extCache = new CfgCacheMapBase<cfg_lottery_ext>(cache_mgr.cfg_lottery_ext_cache, SEARCH_TYPE.KEY, "lottery_type");
        cachemap_mgr.cfg_lottery_nationCache = new CfgCacheMapBase<cfg_lottery_nation[]>(cache_mgr.cfg_lottery_nation_cache, SEARCH_TYPE.GRP, "nation");
        cachemap_mgr.cfg_lottery_nation_timesCache = new CfgCacheMapBase<cfg_lottery_nation_times>(cache_mgr.cfg_lottery_nation_times_cache, SEARCH_TYPE.ID, "nation");
        cachemap_mgr.cfg_lottery_scoreCache = new CfgCacheMapBase<cfg_lottery_score>(cache_mgr.cfg_lottery_score_cache, SEARCH_TYPE.KEY, "score");
        cachemap_mgr.cfg_lottery_showCache = new CfgCacheMapBase<cfg_lottery_show[]>(cache_mgr.cfg_lottery_show_cache, SEARCH_TYPE.GRP, "lottery_type");
        cachemap_mgr.cfg_lottery_timesCache = new CfgCacheMapBase<cfg_lottery_times>(cache_mgr.cfg_lottery_times_cache, SEARCH_TYPE.KEY, "group_id");
        cachemap_mgr.cfg_lottery_weightCache = new CfgCacheMapBase<cfg_lottery_weight[]>(cache_mgr.cfg_lottery_weight_cache, SEARCH_TYPE.GRP, "group_id");
        cachemap_mgr.cfg_main_battleCache = new CfgCacheMapBase<cfg_main_battle>(cache_mgr.cfg_main_battle_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_main_battle_groupCache = new CfgCacheMapBase<cfg_main_battle[]>(cache_mgr.cfg_main_battle_cache, SEARCH_TYPE.GRP, "group");
        cachemap_mgr.cfg_main_battle_boxCache = new CfgCacheMapBase<cfg_main_battle_box>(cache_mgr.cfg_main_battle_box_cache, SEARCH_TYPE.ID, "box");
        cachemap_mgr.cfg_main_battle_boxByItemIdCache = new CfgCacheMapBase<cfg_main_battle_box>(cache_mgr.cfg_main_battle_box_cache, SEARCH_TYPE.KEY, "item_id");
        cachemap_mgr.cfg_main_battle_box_levelCache = new CfgCacheMapBase<cfg_main_battle_box_level>(cache_mgr.cfg_main_battle_box_level_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_main_battle_box_rewardCache = new CfgCacheMapBase<cfg_main_battle_box_reward>(cache_mgr.cfg_main_battle_box_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_main_battle_box_rewardMapCache = new CfgCacheMapBase<cfg_main_battle_box_reward>(cache_mgr.cfg_main_battle_box_reward_cache, SEARCH_TYPE.MKEY, "box,item_id");
        cachemap_mgr.cfg_main_battle_box_rewardGrpCache = new CfgCacheMapBase<cfg_main_battle_box_reward[]>(cache_mgr.cfg_main_battle_box_reward_cache, SEARCH_TYPE.GRP, "box");
        cachemap_mgr.cfg_main_battle_box_tequanCache = new CfgCacheMapBase<cfg_main_battle_box_tequan>(cache_mgr.cfg_main_battle_box_tequan_cache, SEARCH_TYPE.KEY, "goods_id");
        cachemap_mgr.cfg_main_battle_fetchCache = new CfgCacheMapBase<cfg_main_battle_fetch>(cache_mgr.cfg_main_battle_fetch_cache, SEARCH_TYPE.ID, "pass_id");
        cachemap_mgr.cfg_main_battle_hangingCache = new CfgCacheMapBase<cfg_main_battle_hanging>(cache_mgr.cfg_main_battle_hanging_cache, SEARCH_TYPE.ID, "min_buy_times");
        cachemap_mgr.cfg_main_battle_missionCache = new CfgCacheMapBase<cfg_main_battle_mission>(cache_mgr.cfg_main_battle_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_mapCache = new CfgCacheMapBase<cfg_map>(cache_mgr.cfg_map_cache, SEARCH_TYPE.KEY, "map_id");
        cachemap_mgr.cfg_map_itemCache = new CfgCacheMapBase<cfg_map_item>(cache_mgr.cfg_map_item_cache, SEARCH_TYPE.KEY, "item_id");
        cachemap_mgr.cfg_map_itemGrpCache = new CfgCacheMapBase<cfg_map_item[]>(cache_mgr.cfg_map_item_cache, SEARCH_TYPE.GRP, "map_id");
        cachemap_mgr.cfg_map_item_typeCache = new CfgCacheMapBase<cfg_map_item_type>(cache_mgr.cfg_map_item_type_cache, SEARCH_TYPE.KEY, "item_type");
        cachemap_mgr.cfg_map_typeCache = new CfgCacheMapBase<cfg_map_type>(cache_mgr.cfg_map_type_cache, SEARCH_TYPE.KEY, "map_type");
        cachemap_mgr.cfg_master_cardCache = new CfgCacheMapBase<cfg_master_card>(cache_mgr.cfg_master_card_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_master_cardByGroupCache = new CfgCacheMapBase<cfg_master_card[]>(cache_mgr.cfg_master_card_cache, SEARCH_TYPE.GRP, "slot");
        cachemap_mgr.cfg_master_card_attrCache = new CfgCacheMapBase<cfg_master_card_attr>(cache_mgr.cfg_master_card_attr_cache, SEARCH_TYPE.ID, "attr_key");
        cachemap_mgr.cfg_master_card_colorCache = new CfgCacheMapBase<cfg_master_card_color>(cache_mgr.cfg_master_card_color_cache, SEARCH_TYPE.ID, "color");
        cachemap_mgr.cfg_master_card_drum_levelCache = new CfgCacheMapBase<cfg_master_card_drum_level>(cache_mgr.cfg_master_card_drum_level_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_master_card_first_payCache = new CfgCacheMapBase<cfg_master_card_first_pay>(cache_mgr.cfg_master_card_first_pay_cache, SEARCH_TYPE.ID, "gift_id");
        cachemap_mgr.cfg_master_card_help_giftCache = new CfgCacheMapBase<cfg_master_card_help_gift>(cache_mgr.cfg_master_card_help_gift_cache, SEARCH_TYPE.ID, "gift_id");
        cachemap_mgr.cfg_master_card_miscCache = new CfgCacheMapBase<cfg_master_card_misc>(cache_mgr.cfg_master_card_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_master_card_missionCache = new CfgCacheMapBase<cfg_master_card_mission>(cache_mgr.cfg_master_card_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_master_card_mission_guide_idCache = new CfgCacheMapBase<cfg_master_card_mission>(cache_mgr.cfg_master_card_mission_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_master_card_officialCache = new CfgCacheMapBase<cfg_master_card_official>(cache_mgr.cfg_master_card_official_cache, SEARCH_TYPE.ID, "stage");
        cachemap_mgr.cfg_master_card_official_positionCache = new CfgCacheMapBase<cfg_master_card_official_position>(cache_mgr.cfg_master_card_official_position_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_master_card_reshapeCache = new CfgCacheMapBase<cfg_master_card_reshape>(cache_mgr.cfg_master_card_reshape_cache, SEARCH_TYPE.MKEY, "slot,color");
        cachemap_mgr.cfg_master_card_slotCache = new CfgCacheMapBase<cfg_master_card_slot>(cache_mgr.cfg_master_card_slot_cache, SEARCH_TYPE.ID, "slot");
        cachemap_mgr.cfg_master_card_stageCache = new CfgCacheMapBase<cfg_master_card_stage>(cache_mgr.cfg_master_card_stage_cache, SEARCH_TYPE.ID, "stage");
        cachemap_mgr.cfg_master_talent_scienceCache = new CfgCacheMapBase<cfg_master_talent_science>(cache_mgr.cfg_master_talent_science_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_master_talent_science_iconCache = new CfgCacheMapBase<cfg_master_talent_science_icon>(cache_mgr.cfg_master_talent_science_icon_cache, SEARCH_TYPE.KEY, "type");
        cachemap_mgr.cfg_match_typeCache = new CfgCacheMapBase<cfg_match_type>(cache_mgr.cfg_match_type_cache, SEARCH_TYPE.ID, "match_type");
        cachemap_mgr.cfg_match_type_teamCache = new CfgCacheMapBase<cfg_match_type_team>(cache_mgr.cfg_match_type_team_cache, SEARCH_TYPE.MKEY, "plan_id,team_id");
        cachemap_mgr.cfg_match_type_team_listCache = new CfgCacheMapBase<cfg_match_type_team[]>(cache_mgr.cfg_match_type_team_cache, SEARCH_TYPE.GRP, "plan_id");
        cachemap_mgr.cfg_mazeCache = new CfgCacheMapBase<cfg_maze>(cache_mgr.cfg_maze_cache, SEARCH_TYPE.MKEY, "floor,pos");
        cachemap_mgr.cfg_maze_diff_rewardsCache = new CfgCacheMapBase<cfg_maze_diff_rewards>(cache_mgr.cfg_maze_diff_rewards_cache, SEARCH_TYPE.MKEY, "rewards_id,floor,diff_lv");
        cachemap_mgr.cfg_maze_mission_spoilsCache = new CfgCacheMapBase<cfg_maze_mission_spoils>(cache_mgr.cfg_maze_mission_spoils_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_maze_monsterCache = new CfgCacheMapBase<cfg_maze_monster[]>(cache_mgr.cfg_maze_monster_cache, SEARCH_TYPE.GRP, "rewards_id");
        cachemap_mgr.cfg_maze_resetCache = new CfgCacheMapBase<cfg_maze_reset>(cache_mgr.cfg_maze_reset_cache, SEARCH_TYPE.ID, "count");
        cachemap_mgr.cfg_maze_reviveCache = new CfgCacheMapBase<cfg_maze_revive>(cache_mgr.cfg_maze_revive_cache, SEARCH_TYPE.ID, "count");
        cachemap_mgr.cfg_maze_shopCache = new CfgCacheMapBase<cfg_maze_shop>(cache_mgr.cfg_maze_shop_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_maze_themeCache = new CfgCacheMapBase<cfg_maze_theme>(cache_mgr.cfg_maze_theme_cache, SEARCH_TYPE.ID, "rewards_id");
        cachemap_mgr.cfg_medalCache = new CfgCacheMapBase<cfg_medal>(cache_mgr.cfg_medal_cache, SEARCH_TYPE.ID, "medal_id");
        cachemap_mgr.cfg_medalBytype = new CfgCacheMapBase<cfg_medal[]>(cache_mgr.cfg_medal_cache, SEARCH_TYPE.GRP, "medal_type");
        cachemap_mgr.cfg_medalByidex = new CfgCacheMapBase<cfg_medal[]>(cache_mgr.cfg_medal_cache, SEARCH_TYPE.GRP, "idex");
        cachemap_mgr.cfg_medalBySpmedal = new CfgCacheMapBase<cfg_medal[]>(cache_mgr.cfg_medal_cache, SEARCH_TYPE.GRP, "is_sp_medal");
        cachemap_mgr.cfg_medalByclassification = new CfgCacheMapBase<cfg_medal[]>(cache_mgr.cfg_medal_cache, SEARCH_TYPE.GRP, "classification");
        cachemap_mgr.cfg_microterminal_openCache = new CfgCacheMapBase<cfg_microterminal_open>(cache_mgr.cfg_microterminal_open_cache, SEARCH_TYPE.KEY, "open_platform");
        cachemap_mgr.cfg_microterminal_signCache = new CfgCacheMapBase<cfg_microterminal_sign[]>(cache_mgr.cfg_microterminal_sign_cache, SEARCH_TYPE.GRP, "last_day");
        cachemap_mgr.cfg_misc_configCache = new CfgCacheMapBase<cfg_misc_config>(cache_mgr.cfg_misc_config_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_mission_shopCache = new CfgCacheMapBase<cfg_mission_shop>(cache_mgr.cfg_mission_shop_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_mission_shop_clientCache = new CfgCacheMapBase<cfg_mission_shop_client>(cache_mgr.cfg_mission_shop_client_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_mock_pvp_databaseCache = new CfgCacheMapBase<cfg_mock_pvp_database>(cache_mgr.cfg_mock_pvp_database_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_mock_pvp_hero_baseCache = new CfgCacheMapBase<cfg_mock_pvp_hero_base>(cache_mgr.cfg_mock_pvp_hero_base_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_mock_pvp_hero_baseByNationCache = new CfgCacheMapBase<cfg_mock_pvp_hero_base[]>(cache_mgr.cfg_mock_pvp_hero_base_cache, SEARCH_TYPE.GRP, "nation");
        cachemap_mgr.cfg_mock_pvp_hero_baseByTypeIdCache = new CfgCacheMapBase<cfg_mock_pvp_hero_base>(cache_mgr.cfg_mock_pvp_hero_base_cache, SEARCH_TYPE.MKEY, "type_id,nation");
        cachemap_mgr.cfg_mock_pvp_limitCache = new CfgCacheMapBase<cfg_mock_pvp_limit>(cache_mgr.cfg_mock_pvp_limit_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_mock_pvp_miscCache = new CfgCacheMapBase<cfg_mock_pvp_misc>(cache_mgr.cfg_mock_pvp_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_mock_pvp_missionCache = new CfgCacheMapBase<cfg_mock_pvp_mission>(cache_mgr.cfg_mock_pvp_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_modular_activity_blessCache = new CfgCacheMapBase<cfg_modular_activity_bless>(cache_mgr.cfg_modular_activity_bless_cache, SEARCH_TYPE.ID, "plan_id");
        cachemap_mgr.cfg_modular_activity_brickCache = new CfgCacheMapBase<cfg_modular_activity_brick>(cache_mgr.cfg_modular_activity_brick_cache, SEARCH_TYPE.MKEY, "plan_id,round");
        cachemap_mgr.cfg_modular_activity_carnival_linkCache = new CfgCacheMapBase<cfg_modular_activity_carnival_link>(cache_mgr.cfg_modular_activity_carnival_link_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_modular_activity_client_settingCache = new CfgCacheMapBase<cfg_modular_activity_client_setting>(cache_mgr.cfg_modular_activity_client_setting_cache, SEARCH_TYPE.KEY, "client_key");
        cachemap_mgr.cfg_modular_activity_client_settingGroupCache = new CfgCacheMapBase<cfg_modular_activity_client_setting[]>(cache_mgr.cfg_modular_activity_client_setting_cache, SEARCH_TYPE.MGRP, "sub_type,plan_id");
        cachemap_mgr.cfg_modular_activity_compose_listCache = new CfgCacheMapBase<cfg_modular_activity_compose_list[]>(cache_mgr.cfg_modular_activity_compose_list_cache, SEARCH_TYPE.GRP, "act_key");
        cachemap_mgr.cfg_modular_activity_compose_listClientKeyCache = new CfgCacheMapBase<cfg_modular_activity_compose_list[]>(cache_mgr.cfg_modular_activity_compose_list_cache, SEARCH_TYPE.GRP, "client_key");
        cachemap_mgr.cfg_modular_activity_customized_giftCache = new CfgCacheMapBase<cfg_modular_activity_customized_gift>(cache_mgr.cfg_modular_activity_customized_gift_cache, SEARCH_TYPE.ID, "item_id");
        cachemap_mgr.cfg_modular_activity_customized_giftByplayidCache = new CfgCacheMapBase<cfg_modular_activity_customized_gift[]>(cache_mgr.cfg_modular_activity_customized_gift_cache, SEARCH_TYPE.GRP, "plan_id");
        cachemap_mgr.cfg_modular_activity_diceCache = new CfgCacheMapBase<cfg_modular_activity_dice[]>(cache_mgr.cfg_modular_activity_dice_cache, SEARCH_TYPE.MGRP, "plan_id,floor");
        cachemap_mgr.cfg_modular_activity_dice_bossCache = new CfgCacheMapBase<cfg_modular_activity_dice_boss>(cache_mgr.cfg_modular_activity_dice_boss_cache, SEARCH_TYPE.MKEY, "plan_id,floor");
        cachemap_mgr.cfg_modular_activity_dice_bossGroupCache = new CfgCacheMapBase<cfg_modular_activity_dice_boss[]>(cache_mgr.cfg_modular_activity_dice_boss_cache, SEARCH_TYPE.GRP, "plan_id");
        cachemap_mgr.cfg_modular_activity_dice_client_diffCache = new CfgCacheMapBase<cfg_modular_activity_dice_client_diff>(cache_mgr.cfg_modular_activity_dice_client_diff_cache, SEARCH_TYPE.ID, "plan_id");
        cachemap_mgr.cfg_modular_activity_dice_miscCache = new CfgCacheMapBase<cfg_modular_activity_dice_misc>(cache_mgr.cfg_modular_activity_dice_misc_cache, SEARCH_TYPE.MKEY, "plan_id,dice_type");
        cachemap_mgr.cfg_modular_activity_dropCache = new CfgCacheMapBase<cfg_modular_activity_drop>(cache_mgr.cfg_modular_activity_drop_cache, SEARCH_TYPE.KEY, "plan_id");
        cachemap_mgr.cfg_modular_activity_drop_showCache = new CfgCacheMapBase<cfg_modular_activity_drop_show>(cache_mgr.cfg_modular_activity_drop_show_cache, SEARCH_TYPE.MKEY, "type,plan_id");
        cachemap_mgr.cfg_modular_activity_exchangeCache = new CfgCacheMapBase<cfg_modular_activity_exchange>(cache_mgr.cfg_modular_activity_exchange_cache, SEARCH_TYPE.MKEY, "plan_id,id");
        cachemap_mgr.cfg_modular_activity_exchangeGrpCache = new CfgCacheMapBase<cfg_modular_activity_exchange[]>(cache_mgr.cfg_modular_activity_exchange_cache, SEARCH_TYPE.GRP, "plan_id");
        cachemap_mgr.cfg_modular_activity_festival_wishCache = new CfgCacheMapBase<cfg_modular_activity_festival_wish[]>(cache_mgr.cfg_modular_activity_festival_wish_cache, SEARCH_TYPE.MGRP, "plan_id,round");
        cachemap_mgr.cfg_modular_activity_festival_wish_floorCache = new CfgCacheMapBase<cfg_modular_activity_festival_wish[]>(cache_mgr.cfg_modular_activity_festival_wish_cache, SEARCH_TYPE.MGRP, "plan_id,round,floor");
        cachemap_mgr.cfg_modular_activity_festival_wish_chooseCache = new CfgCacheMapBase<cfg_modular_activity_festival_wish_choose[]>(cache_mgr.cfg_modular_activity_festival_wish_choose_cache, SEARCH_TYPE.MGRP, "plan_id,round");
        cachemap_mgr.cfg_modular_activity_festival_wish_chooseListCache = new CfgCacheMapBase<cfg_modular_activity_festival_wish_choose[]>(cache_mgr.cfg_modular_activity_festival_wish_choose_cache, SEARCH_TYPE.GRP, "plan_id");
        cachemap_mgr.cfg_modular_activity_festival_wish_costCache = new CfgCacheMapBase<cfg_modular_activity_festival_wish_cost>(cache_mgr.cfg_modular_activity_festival_wish_cost_cache, SEARCH_TYPE.MKEY, "plan_id,floor");
        cachemap_mgr.cfg_modular_activity_free_switchCache = new CfgCacheMapBase<cfg_modular_activity_free_switch>(cache_mgr.cfg_modular_activity_free_switch_cache, SEARCH_TYPE.KEY, "plan_id");
        cachemap_mgr.cfg_modular_activity_general_pass_vipCache = new CfgCacheMapBase<cfg_modular_activity_general_pass_vip>(cache_mgr.cfg_modular_activity_general_pass_vip_cache, SEARCH_TYPE.MKEY, "pass_type,turn");
        cachemap_mgr.cfg_modular_activity_general_pass_vipByPlanidCache = new CfgCacheMapBase<cfg_modular_activity_general_pass_vip[]>(cache_mgr.cfg_modular_activity_general_pass_vip_cache, SEARCH_TYPE.MGRP, "pass_type,turn");
        cachemap_mgr.cfg_modular_activity_hero_challengeCache = new CfgCacheMapBase<cfg_modular_activity_hero_challenge>(cache_mgr.cfg_modular_activity_hero_challenge_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_modular_activity_hero_challengeByPlanidCache = new CfgCacheMapBase<cfg_modular_activity_hero_challenge[]>(cache_mgr.cfg_modular_activity_hero_challenge_cache, SEARCH_TYPE.GRP, "plan_id");
        cachemap_mgr.cfg_modular_activity_holiday_welfare_rewardCache = new CfgCacheMapBase<cfg_modular_activity_holiday_welfare_reward>(cache_mgr.cfg_modular_activity_holiday_welfare_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_modular_activity_hunt_costCache = new CfgCacheMapBase<cfg_modular_activity_hunt_cost>(cache_mgr.cfg_modular_activity_hunt_cost_cache, SEARCH_TYPE.MKEY, "plan_id,num_type");
        cachemap_mgr.cfg_modular_activity_hunt_cost_listCache = new CfgCacheMapBase<cfg_modular_activity_hunt_cost[]>(cache_mgr.cfg_modular_activity_hunt_cost_cache, SEARCH_TYPE.GRP, "plan_id");
        cachemap_mgr.cfg_modular_activity_hunt_descCache = new CfgCacheMapBase<cfg_modular_activity_hunt_desc[]>(cache_mgr.cfg_modular_activity_hunt_desc_cache, SEARCH_TYPE.MGRP, "plan_id,type");
        cachemap_mgr.cfg_modular_activity_hunt_miscCache = new CfgCacheMapBase<cfg_modular_activity_hunt_misc>(cache_mgr.cfg_modular_activity_hunt_misc_cache, SEARCH_TYPE.ID, "plan_id");
        cachemap_mgr.cfg_modular_activity_huoqutujingCache = new CfgCacheMapBase<cfg_modular_activity_huoqutujing[]>(cache_mgr.cfg_modular_activity_huoqutujing_cache, SEARCH_TYPE.GRP, "client_key");
        cachemap_mgr.cfg_modular_activity_iconCache = new CfgCacheMapBase<cfg_modular_activity_icon>(cache_mgr.cfg_modular_activity_icon_cache, SEARCH_TYPE.KEY, "act_key");
        cachemap_mgr.cfg_modular_activity_loginCache = new CfgCacheMapBase<cfg_modular_activity_login>(cache_mgr.cfg_modular_activity_login_cache, SEARCH_TYPE.MKEY, "plan_id,day");
        cachemap_mgr.cfg_modular_activity_lottery_targetCache = new CfgCacheMapBase<cfg_modular_activity_lottery_target>(cache_mgr.cfg_modular_activity_lottery_target_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_modular_activity_lottery_timesCache = new CfgCacheMapBase<cfg_modular_activity_lottery_times>(cache_mgr.cfg_modular_activity_lottery_times_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_modular_activity_lucky_bagCache = new CfgCacheMapBase<cfg_modular_activity_lucky_bag>(cache_mgr.cfg_modular_activity_lucky_bag_cache, SEARCH_TYPE.MKEY, "plan_id,id");
        cachemap_mgr.cfg_modular_activity_lucky_bagByPlan_id = new CfgCacheMapBase<cfg_modular_activity_lucky_bag[]>(cache_mgr.cfg_modular_activity_lucky_bag_cache, SEARCH_TYPE.GRP, "plan_id");
        cachemap_mgr.cfg_modular_activity_missionCache = new CfgCacheMapBase<cfg_modular_activity_mission>(cache_mgr.cfg_modular_activity_mission_cache, SEARCH_TYPE.MKEY, "sub_type,plan_id,id");
        cachemap_mgr.cfg_modular_activity_missionListCache = new CfgCacheMapBase<cfg_modular_activity_mission[]>(cache_mgr.cfg_modular_activity_mission_cache, SEARCH_TYPE.MGRP, "sub_type,plan_id");
        cachemap_mgr.cfg_modular_activity_openCache = new CfgCacheMapBase<cfg_modular_activity_open>(cache_mgr.cfg_modular_activity_open_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_modular_activity_open_previewCache = new CfgCacheMapBase<cfg_modular_activity_open_preview>(cache_mgr.cfg_modular_activity_open_preview_cache, SEARCH_TYPE.KEY, "act_key");
        cachemap_mgr.cfg_modular_activity_pay_welfareCache = new CfgCacheMapBase<cfg_modular_activity_pay_welfare[]>(cache_mgr.cfg_modular_activity_pay_welfare_cache, SEARCH_TYPE.GRP, "plan_id");
        cachemap_mgr.cfg_modular_activity_payment_shop_itemCache = new CfgCacheMapBase<cfg_modular_activity_payment_shop_item>(cache_mgr.cfg_modular_activity_payment_shop_item_cache, SEARCH_TYPE.MKEY, "item_id,sub_type,plan_id");
        cachemap_mgr.cfg_modular_activity_payment_shop_item_showCache = new CfgCacheMapBase<cfg_modular_activity_payment_shop_item_show>(cache_mgr.cfg_modular_activity_payment_shop_item_show_cache, SEARCH_TYPE.MKEY, "item_id,sub_type,plan_id");
        cachemap_mgr.cfg_modular_activity_previewCache = new CfgCacheMapBase<cfg_modular_activity_preview[]>(cache_mgr.cfg_modular_activity_preview_cache, SEARCH_TYPE.GRP, "plan_id");
        cachemap_mgr.cfg_modular_activity_preview_rewardsCache = new CfgCacheMapBase<cfg_modular_activity_preview_rewards>(cache_mgr.cfg_modular_activity_preview_rewards_cache, SEARCH_TYPE.KEY, "client_key");
        cachemap_mgr.cfg_modular_activity_rankCache = new CfgCacheMapBase<cfg_modular_activity_rank>(cache_mgr.cfg_modular_activity_rank_cache, SEARCH_TYPE.ID, "plan_id");
        cachemap_mgr.cfg_modular_activity_rank_rewardCache = new CfgCacheMapBase<cfg_modular_activity_rank_reward[]>(cache_mgr.cfg_modular_activity_rank_reward_cache, SEARCH_TYPE.GRP, "rank_key");
        cachemap_mgr.cfg_modular_activity_round_missionCache = new CfgCacheMapBase<cfg_modular_activity_round_mission[]>(cache_mgr.cfg_modular_activity_round_mission_cache, SEARCH_TYPE.GRP, "plan_id");
        cachemap_mgr.cfg_modular_activity_round_missionAllCache = new CfgCacheMapBase<cfg_modular_activity_round_mission>(cache_mgr.cfg_modular_activity_round_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_modular_activity_round_mission_rewardCache = new CfgCacheMapBase<cfg_modular_activity_round_mission_reward[]>(cache_mgr.cfg_modular_activity_round_mission_reward_cache, SEARCH_TYPE.GRP, "plan_id");
        cachemap_mgr.cfg_modular_activity_shop_clientCache = new CfgCacheMapBase<cfg_modular_activity_shop_client>(cache_mgr.cfg_modular_activity_shop_client_cache, SEARCH_TYPE.MKEY, "sub_type,plan_id");
        cachemap_mgr.cfg_modular_activity_signCache = new CfgCacheMapBase<cfg_modular_activity_sign>(cache_mgr.cfg_modular_activity_sign_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_modular_activity_signbyplanIdCache = new CfgCacheMapBase<cfg_modular_activity_sign[]>(cache_mgr.cfg_modular_activity_sign_cache, SEARCH_TYPE.MGRP, "plan_id,repeat_x");
        cachemap_mgr.cfg_modular_activity_six_blessCache = new CfgCacheMapBase<cfg_modular_activity_six_bless>(cache_mgr.cfg_modular_activity_six_bless_cache, SEARCH_TYPE.KEY, "grade");
        cachemap_mgr.cfg_modular_activity_star_plan_heroCache = new CfgCacheMapBase<cfg_modular_activity_star_plan_hero>(cache_mgr.cfg_modular_activity_star_plan_hero_cache, SEARCH_TYPE.MKEY, "sub_type,plan_id");
        cachemap_mgr.cfg_modular_activity_star_plan_rewardCache = new CfgCacheMapBase<cfg_modular_activity_star_plan_reward>(cache_mgr.cfg_modular_activity_star_plan_reward_cache, SEARCH_TYPE.MKEY, "sub_type,plan_id,type_id,need_star");
        cachemap_mgr.cfg_modular_activity_star_plan_rewardByplan_idCache = new CfgCacheMapBase<cfg_modular_activity_star_plan_reward[]>(cache_mgr.cfg_modular_activity_star_plan_reward_cache, SEARCH_TYPE.MGRP, "sub_type,plan_id,type_id");
        cachemap_mgr.cfg_modular_activity_star_plan_rewardByplan_idAndMain_pushCache = new CfgCacheMapBase<cfg_modular_activity_star_plan_reward[]>(cache_mgr.cfg_modular_activity_star_plan_reward_cache, SEARCH_TYPE.MGRP, "sub_type,plan_id,main_push");
        cachemap_mgr.cfg_modular_activity_storyCache = new CfgCacheMapBase<cfg_modular_activity_story>(cache_mgr.cfg_modular_activity_story_cache, SEARCH_TYPE.ID, "plan_id");
        cachemap_mgr.cfg_modular_activity_story_chapterCache = new CfgCacheMapBase<cfg_modular_activity_story_chapter[]>(cache_mgr.cfg_modular_activity_story_chapter_cache, SEARCH_TYPE.GRP, "plan_id");
        cachemap_mgr.cfg_modular_activity_story_chapter_mapCache = new CfgCacheMapBase<cfg_modular_activity_story_chapter_map>(cache_mgr.cfg_modular_activity_story_chapter_map_cache, SEARCH_TYPE.MKEY, "plan_id,start_id,end_id");
        cachemap_mgr.cfg_modular_activity_story_chapter_map_listCache = new CfgCacheMapBase<cfg_modular_activity_story_chapter_map[]>(cache_mgr.cfg_modular_activity_story_chapter_map_cache, SEARCH_TYPE.GRP, "plan_id");
        cachemap_mgr.cfg_modular_activity_story_dialogueCache = new CfgCacheMapBase<cfg_modular_activity_story_dialogue[]>(cache_mgr.cfg_modular_activity_story_dialogue_cache, SEARCH_TYPE.MGRP, "plan_id,chapter_id");
        cachemap_mgr.cfg_modular_activity_sub_typeCache = new CfgCacheMapBase<cfg_modular_activity_sub_type>(cache_mgr.cfg_modular_activity_sub_type_cache, SEARCH_TYPE.ID, "sub_type");
        cachemap_mgr.cfg_modular_activity_targetCache = new CfgCacheMapBase<cfg_modular_activity_target>(cache_mgr.cfg_modular_activity_target_cache, SEARCH_TYPE.MKEY, "plan_id,round,mission_id");
        cachemap_mgr.cfg_modular_activity_time_itemCache = new CfgCacheMapBase<cfg_modular_activity_time_item>(cache_mgr.cfg_modular_activity_time_item_cache, SEARCH_TYPE.KEY, "client_key");
        cachemap_mgr.cfg_modular_activity_wallCache = new CfgCacheMapBase<cfg_modular_activity_wall>(cache_mgr.cfg_modular_activity_wall_cache, SEARCH_TYPE.MKEY, "plan_id,grid_id");
        cachemap_mgr.cfg_modular_activity_war_log_acc_rewardCache = new CfgCacheMapBase<cfg_modular_activity_war_log_acc_reward>(cache_mgr.cfg_modular_activity_war_log_acc_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_modular_activity_war_log_missionCache = new CfgCacheMapBase<cfg_modular_activity_war_log_mission>(cache_mgr.cfg_modular_activity_war_log_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_modular_activity_weekly_card_rewardCacheTemp = new CfgCacheMapBase<cfg_modular_activity_weekly_card_reward>(cache_mgr.cfg_modular_activity_weekly_card_reward_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_moneyCache = new CfgCacheMapBase<cfg_money>(cache_mgr.cfg_money_cache, SEARCH_TYPE.KEY, "platform");
        cachemap_mgr.cfg_monsterCache = new CfgCacheMapBase<cfg_monster>(cache_mgr.cfg_monster_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_monster_groupCache = new CfgCacheMapBase<cfg_monster_group>(cache_mgr.cfg_monster_group_cache, SEARCH_TYPE.ID, "group_id");
        cachemap_mgr.cfg_monster_skill_tierCache = new CfgCacheMapBase<cfg_monster_skill_tier>(cache_mgr.cfg_monster_skill_tier_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_monster_tipsCache = new CfgCacheMapBase<cfg_monster_tips>(cache_mgr.cfg_monster_tips_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_month_fundCache = new CfgCacheMapBase<cfg_month_fund[]>(cache_mgr.cfg_month_fund_cache, SEARCH_TYPE.MGRP, "fund_type,turn");
        cachemap_mgr.cfg_month_fundCache2 = new CfgCacheMapBase<cfg_month_fund[]>(cache_mgr.cfg_month_fund_cache, SEARCH_TYPE.GRP, "fund_type");
        cachemap_mgr.cfg_month_fund_typeCache = new CfgCacheMapBase<cfg_month_fund_type>(cache_mgr.cfg_month_fund_type_cache, SEARCH_TYPE.ID, "fund_type");
        cachemap_mgr.cfg_musicCache = new CfgCacheMapBase<cfg_music>(cache_mgr.cfg_music_cache, SEARCH_TYPE.ID, "music_id");
        cachemap_mgr.cfg_musicUICache = new CfgCacheMapBase<cfg_music[]>(cache_mgr.cfg_music_cache, SEARCH_TYPE.GRP, "ui_name");
        cachemap_mgr.cfg_nation_tower_lineupCache = new CfgCacheMapBase<cfg_nation_tower_lineup[]>(cache_mgr.cfg_nation_tower_lineup_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_nation_tower_openCache = new CfgCacheMapBase<cfg_nation_tower_open>(cache_mgr.cfg_nation_tower_open_cache, SEARCH_TYPE.ID, "type");
        cachemap_mgr.cfg_noob_payCache = new CfgCacheMapBase<cfg_noob_pay>(cache_mgr.cfg_noob_pay_cache, SEARCH_TYPE.ID, "gift_id");
        cachemap_mgr.cfg_online_rewardCache = new CfgCacheMapBase<cfg_online_reward>(cache_mgr.cfg_online_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_pass_beheadCache = new CfgCacheMapBase<cfg_pass_behead>(cache_mgr.cfg_pass_behead_cache, SEARCH_TYPE.ID, "type");
        cachemap_mgr.cfg_pass_behead_boxCache = new CfgCacheMapBase<cfg_pass_behead_box>(cache_mgr.cfg_pass_behead_box_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_pass_behead_guanqiaCache = new CfgCacheMapBase<cfg_pass_behead_guanqia[]>(cache_mgr.cfg_pass_behead_guanqia_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_pass_behead_reviveCache = new CfgCacheMapBase<cfg_pass_behead_revive>(cache_mgr.cfg_pass_behead_revive_cache, SEARCH_TYPE.ID, "count");
        cachemap_mgr.cfg_pass_check_missionCache = new CfgCacheMapBase<cfg_pass_check_mission>(cache_mgr.cfg_pass_check_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_pass_check_rewardCache = new CfgCacheMapBase<cfg_pass_check_reward>(cache_mgr.cfg_pass_check_reward_cache, SEARCH_TYPE.MKEY, "turn,level,sort_id");
        cachemap_mgr.cfg_pass_check_rewardByTurnCache = new CfgCacheMapBase<cfg_pass_check_reward[]>(cache_mgr.cfg_pass_check_reward_cache, SEARCH_TYPE.MGRP, "turn,level");
        cachemap_mgr.cfg_pass_check_vipCache = new CfgCacheMapBase<cfg_pass_check_vip>(cache_mgr.cfg_pass_check_vip_cache, SEARCH_TYPE.MKEY, "turn,pass_type");
        cachemap_mgr.cfg_pay_vipCache = new CfgCacheMapBase<cfg_pay_vip>(cache_mgr.cfg_pay_vip_cache, SEARCH_TYPE.KEY, "level");
        cachemap_mgr.cfg_pay_vip_privilegeCache = new CfgCacheMapBase<cfg_pay_vip_privilege[]>(cache_mgr.cfg_pay_vip_privilege_cache, SEARCH_TYPE.GRP, "level");
        cachemap_mgr.cfg_pay_vip_privilege_typeCache = new CfgCacheMapBase<cfg_pay_vip_privilege[]>(cache_mgr.cfg_pay_vip_privilege_cache, SEARCH_TYPE.GRP, "priv_type");
        cachemap_mgr.cfg_pay_vip_privilege_function_typeCache = new CfgCacheMapBase<cfg_pay_vip_privilege_function>(cache_mgr.cfg_pay_vip_privilege_function_cache, SEARCH_TYPE.KEY, "priv_type");
        cachemap_mgr.cfg_payment_shop_itemCache = new CfgCacheMapBase<cfg_payment_shop_item>(cache_mgr.cfg_payment_shop_item_cache, SEARCH_TYPE.KEY, "item_id");
        cachemap_mgr.cfg_payment_shop_linkCache = new CfgCacheMapBase<cfg_payment_shop_link>(cache_mgr.cfg_payment_shop_link_cache, SEARCH_TYPE.KEY, "link_id");
        cachemap_mgr.cfg_payment_time_giftCache = new CfgCacheMapBase<cfg_payment_time_gift[]>(cache_mgr.cfg_payment_time_gift_cache, SEARCH_TYPE.GRP, "item_tab");
        cachemap_mgr.cfg_peak_miscCache = new CfgCacheMapBase<cfg_peak_misc>(cache_mgr.cfg_peak_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_peak_timeCache = new CfgCacheMapBase<cfg_peak_time>(cache_mgr.cfg_peak_time_cache, SEARCH_TYPE.MKEY, "model,round,step");
        cachemap_mgr.cfg_peak_time_model_roundCache = new CfgCacheMapBase<cfg_peak_time[]>(cache_mgr.cfg_peak_time_cache, SEARCH_TYPE.MGRP, "model,round");
        cachemap_mgr.cfg_peerless_act_hero_giftCache = new CfgCacheMapBase<cfg_peerless_act_hero_gift>(cache_mgr.cfg_peerless_act_hero_gift_cache, SEARCH_TYPE.ID, "reward_id");
        cachemap_mgr.cfg_platform_ad_id_miscCache = new CfgCacheMapBase<cfg_platform_ad_id_misc>(cache_mgr.cfg_platform_ad_id_misc_cache, SEARCH_TYPE.KEY, "platform");
        cachemap_mgr.cfg_player_strategyCache = new CfgCacheMapBase<cfg_player_strategy>(cache_mgr.cfg_player_strategy_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_playing_preview_rewardCache = new CfgCacheMapBase<cfg_playing_preview_reward>(cache_mgr.cfg_playing_preview_reward_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_progress_giftCache = new CfgCacheMapBase<cfg_progress_gift>(cache_mgr.cfg_progress_gift_cache, SEARCH_TYPE.MKEY, "type,buy_times");
        cachemap_mgr.cfg_progress_giftByTypeCache = new CfgCacheMapBase<cfg_progress_gift[]>(cache_mgr.cfg_progress_gift_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_pull_wordsCacheTemp = new CfgCacheMapBase<cfg_pull_words>(cache_mgr.cfg_pull_words_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_pvp_mapCache = new CfgCacheMapBase<cfg_pvp_map>(cache_mgr.cfg_pvp_map_cache, SEARCH_TYPE.ID, "mapId");
        cachemap_mgr.cfg_qq_groupCache = new CfgCacheMapBase<cfg_qq_group>(cache_mgr.cfg_qq_group_cache, SEARCH_TYPE.KEY, "platform");
        cachemap_mgr.cfg_qq_vipCacheTemp = new CfgCacheMapBase<cfg_qq_vip>(cache_mgr.cfg_qq_vip_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_qxzl_miscCache = new CfgCacheMapBase<cfg_qxzl_misc>(cache_mgr.cfg_qxzl_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_random_boxCache = new CfgCacheMapBase<cfg_random_box>(cache_mgr.cfg_random_box_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_random_pvpAllCache = new CfgCacheMapBase<cfg_random_pvp>(cache_mgr.cfg_random_pvp_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_random_pvpGroupCache = new CfgCacheMapBase<cfg_random_pvp[]>(cache_mgr.cfg_random_pvp_cache, SEARCH_TYPE.GRP, "big_level");
        cachemap_mgr.cfg_random_pvp_head_frameCache = new CfgCacheMapBase<cfg_random_pvp_head_frame>(cache_mgr.cfg_random_pvp_head_frame_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_random_pvp_limitCache = new CfgCacheMapBase<cfg_random_pvp_limit>(cache_mgr.cfg_random_pvp_limit_cache, SEARCH_TYPE.ID, "type");
        cachemap_mgr.cfg_random_pvp_rewardCache = new CfgCacheMapBase<cfg_random_pvp_reward>(cache_mgr.cfg_random_pvp_reward_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_random_pvp_taskCache = new CfgCacheMapBase<cfg_random_pvp_task>(cache_mgr.cfg_random_pvp_task_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_random_pvp_task_rewardsCache = new CfgCacheMapBase<cfg_random_pvp_task_rewards>(cache_mgr.cfg_random_pvp_task_rewards_cache, SEARCH_TYPE.ID, "grade");
        cachemap_mgr.cfg_rank_descCache = new CfgCacheMapBase<cfg_rank_desc>(cache_mgr.cfg_rank_desc_cache, SEARCH_TYPE.KEY, "rank_key");
        cachemap_mgr.cfg_rank_missionCache = new CfgCacheMapBase<cfg_rank_mission[]>(cache_mgr.cfg_rank_mission_cache, SEARCH_TYPE.GRP, "rank_key");
        cachemap_mgr.cfg_rank_missionCacheAll = new CfgCacheMapBase<cfg_rank_mission>(cache_mgr.cfg_rank_mission_cache, SEARCH_TYPE.MKEY, "rank_key,id");
        cachemap_mgr.cfg_rank_rewardsCache = new CfgCacheMapBase<cfg_rank_rewards[]>(cache_mgr.cfg_rank_rewards_cache, SEARCH_TYPE.GRP, "rank_key");
        cachemap_mgr.cfg_rank_worshipCache = new CfgCacheMapBase<cfg_rank_worship>(cache_mgr.cfg_rank_worship_cache, SEARCH_TYPE.ID, "rank_key");
        cachemap_mgr.cfg_red_cliffCache = new CfgCacheMapBase<cfg_red_cliff[]>(cache_mgr.cfg_red_cliff_cache, SEARCH_TYPE.MGRP, "nation,leader_type_id");
        cachemap_mgr.cfg_red_cliff_bossCacheTemp = new CfgCacheMapBase<cfg_red_cliff_boss>(cache_mgr.cfg_red_cliff_boss_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_red_cliff_openCache = new CfgCacheMapBase<cfg_red_cliff_open>(cache_mgr.cfg_red_cliff_open_cache, SEARCH_TYPE.ID, "nation");
        cachemap_mgr.cfg_red_cliff_refreshCache = new CfgCacheMapBase<cfg_red_cliff_refresh>(cache_mgr.cfg_red_cliff_refresh_cache, SEARCH_TYPE.MKEY, "nation,leader_type_id");
        cachemap_mgr.cfg_red_cliff_reviewCache = new CfgCacheMapBase<cfg_red_cliff_review>(cache_mgr.cfg_red_cliff_review_cache, SEARCH_TYPE.KEY, "platform_id");
        cachemap_mgr.cfg_retrievalCache = new CfgCacheMapBase<cfg_retrieval>(cache_mgr.cfg_retrieval_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_river_text_constCache = new CfgCacheMapBase<cfg_river_text_const>(cache_mgr.cfg_river_text_const_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_profilesByTypeCache = new CfgCacheMapBase<cfg_role_profile[]>(cache_mgr.cfg_role_profile_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_profileCache = new CfgCacheMapBase<cfg_role_profile>(cache_mgr.cfg_role_profile_cache, SEARCH_TYPE.KEY, "fashion_id");
        cachemap_mgr.cfg_san_xiao_actorCache = new CfgCacheMapBase<cfg_san_xiao_actor>(cache_mgr.cfg_san_xiao_actor_cache, SEARCH_TYPE.ID, "actor_id");
        cachemap_mgr.cfg_san_xiao_guanqiaCache = new CfgCacheMapBase<cfg_san_xiao_guanqia>(cache_mgr.cfg_san_xiao_guanqia_cache, SEARCH_TYPE.ID, "guanqia_id");
        cachemap_mgr.cfg_san_xiao_itemCache = new CfgCacheMapBase<cfg_san_xiao_item>(cache_mgr.cfg_san_xiao_item_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_san_xiao_item_typeCache = new CfgCacheMapBase<cfg_san_xiao_item[]>(cache_mgr.cfg_san_xiao_item_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_san_xiao_levelCache = new CfgCacheMapBase<cfg_san_xiao_level>(cache_mgr.cfg_san_xiao_level_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_san_xiao_mapCache = new CfgCacheMapBase<cfg_san_xiao_map>(cache_mgr.cfg_san_xiao_map_cache, SEARCH_TYPE.ID, "map_id");
        cachemap_mgr.cfg_san_xiao_miscCache = new CfgCacheMapBase<cfg_san_xiao_misc>(cache_mgr.cfg_san_xiao_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_sceneCache = new CfgCacheMapBase<cfg_scene>(cache_mgr.cfg_scene_cache, SEARCH_TYPE.KEY, "scene_name");
        cachemap_mgr.cfg_sdk_concern_rewardCache = new CfgCacheMapBase<cfg_sdk_concern_reward>(cache_mgr.cfg_sdk_concern_reward_cache, SEARCH_TYPE.KEY, "platform_tag");
        cachemap_mgr.cfg_sdk_platform_descCanhe = new CfgCacheMapBase<cfg_sdk_platform_desc>(cache_mgr.cfg_sdk_platform_desc_cache, SEARCH_TYPE.KEY, "platform_tag");
        cachemap_mgr.cfg_sdk_rewardsCache = new CfgCacheMapBase<cfg_sdk_rewards>(cache_mgr.cfg_sdk_rewards_cache, SEARCH_TYPE.KEY, "type");
        cachemap_mgr.cfg_select_boxCache = new CfgCacheMapBase<cfg_select_box>(cache_mgr.cfg_select_box_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_seven_goal_singleCache = new CfgCacheMapBase<cfg_seven_goal>(cache_mgr.cfg_seven_goal_cache, SEARCH_TYPE.MKEY, "rewards_id,tag_id");
        cachemap_mgr.cfg_seven_goalCache = new CfgCacheMapBase<cfg_seven_goal[]>(cache_mgr.cfg_seven_goal_cache, SEARCH_TYPE.MGRP, "rewards_id,day");
        cachemap_mgr.cfg_seven_goal_giftCache = new CfgCacheMapBase<cfg_seven_goal_gift[]>(cache_mgr.cfg_seven_goal_gift_cache, SEARCH_TYPE.GRP, "rewards_id");
        cachemap_mgr.cfg_seven_goal_missionCache = new CfgCacheMapBase<cfg_seven_goal_mission>(cache_mgr.cfg_seven_goal_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_seven_goal_missionListCache = new CfgCacheMapBase<cfg_seven_goal_mission[]>(cache_mgr.cfg_seven_goal_mission_cache, SEARCH_TYPE.MGRP, "rewards_id,tag_id");
        cachemap_mgr.cfg_share_cycle_rewardCache = new CfgCacheMapBase<cfg_share_cycle_reward>(cache_mgr.cfg_share_cycle_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_share_daily_rewardCache = new CfgCacheMapBase<cfg_share_daily_reward>(cache_mgr.cfg_share_daily_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_share_level_rewardCache = new CfgCacheMapBase<cfg_share_level_reward>(cache_mgr.cfg_share_level_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_shili_previewCache = new CfgCacheMapBase<cfg_shili_preview>(cache_mgr.cfg_shili_preview_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_shopCache = new CfgCacheMapBase<cfg_shop>(cache_mgr.cfg_shop_cache, SEARCH_TYPE.ID, "shop_id");
        cachemap_mgr.cfg_shopGroupCache = new CfgCacheMapBase<cfg_shop[]>(cache_mgr.cfg_shop_cache, SEARCH_TYPE.GRP, "group");
        cachemap_mgr.cfg_shop_itemCache = new CfgCacheMapBase<cfg_shop_item>(cache_mgr.cfg_shop_item_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_shop_itemByshop_idAndgoods_idCache = new CfgCacheMapBase<cfg_shop_item>(cache_mgr.cfg_shop_item_cache, SEARCH_TYPE.MKEY, "shop_id,goods_id");
        cachemap_mgr.cfg_shop_item_tipsCache = new CfgCacheMapBase<cfg_shop_item_tips>(cache_mgr.cfg_shop_item_tips_cache, SEARCH_TYPE.MKEY, "shop_id,goods_id");
        cachemap_mgr.cfg_shop_reset_timesCache = new CfgCacheMapBase<cfg_shop_reset_times>(cache_mgr.cfg_shop_reset_times_cache, SEARCH_TYPE.ID, "shop_id");
        cachemap_mgr.cfg_shop_shortcutCache = new CfgCacheMapBase<cfg_shop_shortcut>(cache_mgr.cfg_shop_shortcut_cache, SEARCH_TYPE.KEY, "type_id");
        cachemap_mgr.cfg_shop_tab_limitCache = new CfgCacheMapBase<cfg_shop_tab_limit[]>(cache_mgr.cfg_shop_tab_limit_cache, SEARCH_TYPE.GRP, "shop_id");
        cachemap_mgr.cfg_shop_tab_limit_mapCache = new CfgCacheMapBase<cfg_shop_tab_limit>(cache_mgr.cfg_shop_tab_limit_cache, SEARCH_TYPE.MKEY, "shop_id,tab_id");
        cachemap_mgr.cfg_show_offCache = new CfgCacheMapBase<cfg_show_off>(cache_mgr.cfg_show_off_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_skeleton_adaptiveCache = new CfgCacheMapBase<cfg_skeleton_adaptive>(cache_mgr.cfg_skeleton_adaptive_cache, SEARCH_TYPE.MKEY, "anim_name,anim_dir");
        cachemap_mgr.cfg_skillCache = new CfgCacheMapBase<cfg_skill>(cache_mgr.cfg_skill_cache, SEARCH_TYPE.ID, "skill_id");
        cachemap_mgr.cfg_skill_effectCache = new CfgCacheMapBase<cfg_skill_effect>(cache_mgr.cfg_skill_effect_cache, SEARCH_TYPE.ID, "effect_id");
        cachemap_mgr.cfg_skill_eventCache = new CfgCacheMapBase<cfg_skill_event[]>(cache_mgr.cfg_skill_event_cache, SEARCH_TYPE.GRP, "skill_id");
        cachemap_mgr.cfg_skill_levelCache = new CfgCacheMapBase<cfg_skill_level[]>(cache_mgr.cfg_skill_level_cache, SEARCH_TYPE.GRP, "skill_id");
        cachemap_mgr.cfg_skill_summonCache = new CfgCacheMapBase<cfg_skill_summon>(cache_mgr.cfg_skill_summon_cache, SEARCH_TYPE.ID, "summon_id");
        cachemap_mgr.cfg_small_gameCache = new CfgCacheMapBase<cfg_small_game>(cache_mgr.cfg_small_game_cache, SEARCH_TYPE.KEY, "game_id");
        cachemap_mgr.cfg_soldier_gameCache = new CfgCacheMapBase<cfg_soldier_game>(cache_mgr.cfg_soldier_game_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_soldier_game_rewardsCache = new CfgCacheMapBase<cfg_soldier_game_rewards>(cache_mgr.cfg_soldier_game_rewards_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_soul_hero_link_levelCache = new CfgCacheMapBase<cfg_soul_hero_link_level>(cache_mgr.cfg_soul_hero_link_level_cache, SEARCH_TYPE.ID, "level");
        cachemap_mgr.cfg_soul_hero_link_limit_unlockCache = new CfgCacheMapBase<cfg_soul_hero_link_limit_unlock>(cache_mgr.cfg_soul_hero_link_limit_unlock_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_soul_hero_link_nationCache = new CfgCacheMapBase<cfg_soul_hero_link_nation>(cache_mgr.cfg_soul_hero_link_nation_cache, SEARCH_TYPE.MKEY, "type_id,nation");
        cachemap_mgr.cfg_stage_breedCache = new CfgCacheMapBase<cfg_stage_breed>(cache_mgr.cfg_stage_breed_cache, SEARCH_TYPE.KEY, "big_stage");
        cachemap_mgr.cfg_stage_breed_attrCache = new CfgCacheMapBase<cfg_stage_breed_attr>(cache_mgr.cfg_stage_breed_attr_cache, SEARCH_TYPE.KEY, "big_stage");
        cachemap_mgr.cfg_stage_copyCache = new CfgCacheMapBase<cfg_stage_copy>(cache_mgr.cfg_stage_copy_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_stage_copy_bossCache = new CfgCacheMapBase<cfg_stage_copy_boss>(cache_mgr.cfg_stage_copy_boss_cache, SEARCH_TYPE.KEY, "pass");
        cachemap_mgr.cfg_stage_copy_bossChapterCache = new CfgCacheMapBase<cfg_stage_copy_boss[]>(cache_mgr.cfg_stage_copy_boss_cache, SEARCH_TYPE.GRP, "chapter_id");
        cachemap_mgr.cfg_stage_copy_boss_mapCache = new CfgCacheMapBase<cfg_stage_copy_boss>(cache_mgr.cfg_stage_copy_boss_cache, SEARCH_TYPE.KEY, "map_item_id");
        cachemap_mgr.cfg_stage_copy_daily_missionCache = new CfgCacheMapBase<cfg_stage_copy_daily_mission>(cache_mgr.cfg_stage_copy_daily_mission_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_stage_copy_miscCache = new CfgCacheMapBase<cfg_stage_copy_misc>(cache_mgr.cfg_stage_copy_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_stage_copy_storyCache = new CfgCacheMapBase<cfg_stage_copy_story>(cache_mgr.cfg_stage_copy_story_cache, SEARCH_TYPE.KEY, "pass");
        cachemap_mgr.cfg_stage_missionCache = new CfgCacheMapBase<cfg_stage_mission>(cache_mgr.cfg_stage_mission_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_stage_skill_attrCache = new CfgCacheMapBase<cfg_stage_skill_attr>(cache_mgr.cfg_stage_skill_attr_cache, SEARCH_TYPE.MKEY, "type_id,skill_level");
        cachemap_mgr.cfg_stage_skill_attrByTypeCache = new CfgCacheMapBase<cfg_stage_skill_attr[]>(cache_mgr.cfg_stage_skill_attr_cache, SEARCH_TYPE.GRP, "type_id");
        cachemap_mgr.cfg_stage_skill_typeCache = new CfgCacheMapBase<cfg_stage_skill_type>(cache_mgr.cfg_stage_skill_type_cache, SEARCH_TYPE.ID, "type_id");
        cachemap_mgr.cfg_star_plan_giftCache = new CfgCacheMapBase<cfg_star_plan_gift>(cache_mgr.cfg_star_plan_gift_cache, SEARCH_TYPE.MKEY, "act_type,star");
        cachemap_mgr.cfg_star_plan_giftByreact_typeCache = new CfgCacheMapBase<cfg_star_plan_gift[]>(cache_mgr.cfg_star_plan_gift_cache, SEARCH_TYPE.GRP, "act_type");
        cachemap_mgr.cfg_star_plan_heroCache = new CfgCacheMapBase<cfg_star_plan_hero>(cache_mgr.cfg_star_plan_hero_cache, SEARCH_TYPE.ID, "rewards_id");
        cachemap_mgr.cfg_star_plan_rewardCache = new CfgCacheMapBase<cfg_star_plan_reward>(cache_mgr.cfg_star_plan_reward_cache, SEARCH_TYPE.MKEY, "rewards_id,type_id,need_star");
        cachemap_mgr.cfg_star_plan_rewardByrewards_idCache = new CfgCacheMapBase<cfg_star_plan_reward[]>(cache_mgr.cfg_star_plan_reward_cache, SEARCH_TYPE.MGRP, "rewards_id,type_id");
        cachemap_mgr.cfg_star_plan_rewardByrewards_idAndMain_pushCache = new CfgCacheMapBase<cfg_star_plan_reward[]>(cache_mgr.cfg_star_plan_reward_cache, SEARCH_TYPE.MGRP, "rewards_id,main_push");
        cachemap_mgr.cfg_storyCache = new CfgCacheMapBase<cfg_story>(cache_mgr.cfg_story_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_story_match_typeCache = new CfgCacheMapBase<cfg_story[]>(cache_mgr.cfg_story_cache, SEARCH_TYPE.GRP, "match_type");
        cachemap_mgr.cfg_story_actionCache = new CfgCacheMapBase<cfg_story_action>(cache_mgr.cfg_story_action_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_story_actorCache = new CfgCacheMapBase<cfg_story_actor>(cache_mgr.cfg_story_actor_cache, SEARCH_TYPE.ID, "actor_id");
        cachemap_mgr.cfg_story_bubbleCache = new CfgCacheMapBase<cfg_story_bubble>(cache_mgr.cfg_story_bubble_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_story_mazeCache = new CfgCacheMapBase<cfg_story_maze>(cache_mgr.cfg_story_maze_cache, SEARCH_TYPE.MKEY, "floor,pos");
        cachemap_mgr.cfg_story_maze_mission_spoilsCache = new CfgCacheMapBase<cfg_story_maze_mission_spoils>(cache_mgr.cfg_story_maze_mission_spoils_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_story_maze_monsterCache = new CfgCacheMapBase<cfg_story_maze_monster[]>(cache_mgr.cfg_story_maze_monster_cache, SEARCH_TYPE.GRP, "rewards_id");
        cachemap_mgr.cfg_story_maze_resetCache = new CfgCacheMapBase<cfg_story_maze_reset>(cache_mgr.cfg_story_maze_reset_cache, SEARCH_TYPE.ID, "count");
        cachemap_mgr.cfg_story_maze_reviveCache = new CfgCacheMapBase<cfg_story_maze_revive>(cache_mgr.cfg_story_maze_revive_cache, SEARCH_TYPE.ID, "count");
        cachemap_mgr.cfg_story_maze_rewardsCache = new CfgCacheMapBase<cfg_story_maze_rewards>(cache_mgr.cfg_story_maze_rewards_cache, SEARCH_TYPE.ID, "floor");
        cachemap_mgr.cfg_story_maze_shopCache = new CfgCacheMapBase<cfg_story_maze_shop>(cache_mgr.cfg_story_maze_shop_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_story_maze_themeCache = new CfgCacheMapBase<cfg_story_maze_theme>(cache_mgr.cfg_story_maze_theme_cache, SEARCH_TYPE.ID, "rewards_id");
        cachemap_mgr.cfg_story_siegelord_city_typeCache = new CfgCacheMapBase<cfg_story_siegelord_city_type>(cache_mgr.cfg_story_siegelord_city_type_cache, SEARCH_TYPE.ID, "type");
        cachemap_mgr.cfg_story_siegelord_levelCache = new CfgCacheMapBase<cfg_story_siegelord_level[]>(cache_mgr.cfg_story_siegelord_level_cache, SEARCH_TYPE.GRP, "id");
        cachemap_mgr.cfg_story_siegelord_level_rewardCache = new CfgCacheMapBase<cfg_story_siegelord_level_reward[]>(cache_mgr.cfg_story_siegelord_level_reward_cache, SEARCH_TYPE.GRP, "id");
        cachemap_mgr.cfg_story_siegelord_miscCache = new CfgCacheMapBase<cfg_story_siegelord_misc>(cache_mgr.cfg_story_siegelord_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_story_siegelord_pass_rewardCache = new CfgCacheMapBase<cfg_story_siegelord_pass_reward>(cache_mgr.cfg_story_siegelord_pass_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_story_siegelord_pass_reward_bystarCache = new CfgCacheMapBase<cfg_story_siegelord_pass_reward>(cache_mgr.cfg_story_siegelord_pass_reward_cache, SEARCH_TYPE.ID, "star");
        cachemap_mgr.cfg_story_tower_battleCache = new CfgCacheMapBase<cfg_story_tower_battle[]>(cache_mgr.cfg_story_tower_battle_cache, SEARCH_TYPE.MGRP, "game_level,house,floor");
        cachemap_mgr.cfg_story_tower_battle_Game_rewardCache = new CfgCacheMapBase<cfg_story_tower_battle[]>(cache_mgr.cfg_story_tower_battle_cache, SEARCH_TYPE.GRP, "game_level");
        cachemap_mgr.cfg_story_tower_battle_monsterCache = new CfgCacheMapBase<cfg_story_tower_battle_monster>(cache_mgr.cfg_story_tower_battle_monster_cache, SEARCH_TYPE.KEY, "monster_id");
        cachemap_mgr.cfg_story_tower_battle_rewardCache = new CfgCacheMapBase<cfg_story_tower_battle_reward>(cache_mgr.cfg_story_tower_battle_reward_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_suit_attr_Cache = new CfgCacheMapBase<cfg_suit_attr[]>(cache_mgr.cfg_suit_attr_cache, SEARCH_TYPE.GRP, "suit_id");
        cachemap_mgr.cfg_supreme_lotteryCache = new CfgCacheMapBase<cfg_supreme_lottery[]>(cache_mgr.cfg_supreme_lottery_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_svip_pay_giftCache = new CfgCacheMapBase<cfg_svip_pay_gift>(cache_mgr.cfg_svip_pay_gift_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_sys_open_noticeCache = new CfgCacheMapBase<cfg_sys_open_notice>(cache_mgr.cfg_sys_open_notice_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_sys_openlvCacheTemp = new CfgCacheMapBase<cfg_sys_openlv>(cache_mgr.cfg_sys_openlv_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_sys_use_timesCache = new CfgCacheMapBase<cfg_sys_use_times>(cache_mgr.cfg_sys_use_times_cache, SEARCH_TYPE.KEY, "type");
        cachemap_mgr.cfg_taxCache = new CfgCacheMapBase<cfg_tax>(cache_mgr.cfg_tax_cache, SEARCH_TYPE.KEY, "type");
        cachemap_mgr.cfg_tax_rewardCache = new CfgCacheMapBase<cfg_tax_reward>(cache_mgr.cfg_tax_reward_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_td_mainCache = new CfgCacheMapBase<cfg_td_main>(cache_mgr.cfg_td_main_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_td_main_missionCache = new CfgCacheMapBase<cfg_td_main_mission>(cache_mgr.cfg_td_main_mission_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_td_main_monsterCache = new CfgCacheMapBase<cfg_td_main_monster>(cache_mgr.cfg_td_main_monster_cache, SEARCH_TYPE.MKEY, "id,wave");
        cachemap_mgr.cfg_td_main_monsterByPassCache = new CfgCacheMapBase<cfg_td_main_monster[]>(cache_mgr.cfg_td_main_monster_cache, SEARCH_TYPE.GRP, "id");
        cachemap_mgr.cfg_td_main_pass_missionCache = new CfgCacheMapBase<cfg_td_main_pass_mission>(cache_mgr.cfg_td_main_pass_mission_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_td_mapCache = new CfgCacheMapBase<cfg_td_map>(cache_mgr.cfg_td_map_cache, SEARCH_TYPE.KEY, "map_id");
        cachemap_mgr.cfg_td_monster_talkGroupCache = new CfgCacheMapBase<cfg_td_monster_talk[]>(cache_mgr.cfg_td_monster_talk_cache, SEARCH_TYPE.GRP, "pass_id");
        cachemap_mgr.cfg_td_trialCache = new CfgCacheMapBase<cfg_td_trial>(cache_mgr.cfg_td_trial_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_td_trial_monsterCache = new CfgCacheMapBase<cfg_td_trial_monster>(cache_mgr.cfg_td_trial_monster_cache, SEARCH_TYPE.MKEY, "id,wave");
        cachemap_mgr.cfg_team_bossCacheTemp = new CfgCacheMapBase<cfg_team_boss>(cache_mgr.cfg_team_boss_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_team_xswh_bossCache = new CfgCacheMapBase<cfg_team_xswh_boss[]>(cache_mgr.cfg_team_xswh_boss_cache, SEARCH_TYPE.MGRP, "day,season_type");
        cachemap_mgr.cfg_team_xswh_giftCache = new CfgCacheMapBase<cfg_team_xswh_gift>(cache_mgr.cfg_team_xswh_gift_cache, SEARCH_TYPE.MKEY, "times,season_type");
        cachemap_mgr.cfg_team_xswh_giftbyseasontypeCache = new CfgCacheMapBase<cfg_team_xswh_gift[]>(cache_mgr.cfg_team_xswh_gift_cache, SEARCH_TYPE.GRP, "season_type");
        cachemap_mgr.cfg_team_xswh_hurt_rewardsCache = new CfgCacheMapBase<cfg_team_xswh_hurt_rewards[]>(cache_mgr.cfg_team_xswh_hurt_rewards_cache, SEARCH_TYPE.MGRP, "day,season_type");
        cachemap_mgr.cfg_team_xswh_rank_rewardsCache = new CfgCacheMapBase<cfg_team_xswh_rank_rewards[]>(cache_mgr.cfg_team_xswh_rank_rewards_cache, SEARCH_TYPE.MGRP, "day,season_type");
        cachemap_mgr.cfg_tequanCache = new CfgCacheMapBase<cfg_tequan>(cache_mgr.cfg_tequan_cache, SEARCH_TYPE.ID, "tequan_id");
        cachemap_mgr.cfg_test_towerCache = new CfgCacheMapBase<cfg_test_tower>(cache_mgr.cfg_test_tower_cache, SEARCH_TYPE.MKEY, "get_arr,floor");
        cachemap_mgr.cfg_test_towerByTypeCache = new CfgCacheMapBase<cfg_test_tower[]>(cache_mgr.cfg_test_tower_cache, SEARCH_TYPE.GRP, "fb_type");
        cachemap_mgr.cfg_test_towerByGetArr = new CfgCacheMapBase<cfg_test_tower[]>(cache_mgr.cfg_test_tower_cache, SEARCH_TYPE.GRP, "get_arr");
        cachemap_mgr.cfg_test_tower_extra_rewardCache = new CfgCacheMapBase<cfg_test_tower_extra_reward[]>(cache_mgr.cfg_test_tower_extra_reward_cache, SEARCH_TYPE.MGRP, "get_arr,type");
        cachemap_mgr.cfg_test_tower_extra_reward_keyCache = new CfgCacheMapBase<cfg_test_tower_extra_reward>(cache_mgr.cfg_test_tower_extra_reward_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_test_tower_skinCache = new CfgCacheMapBase<cfg_test_tower_skin>(cache_mgr.cfg_test_tower_skin_cache, SEARCH_TYPE.KEY, "skin_id");
        cachemap_mgr.cfg_test_tower_skin_resCache = new CfgCacheMapBase<cfg_test_tower_skin>(cache_mgr.cfg_test_tower_skin_cache, SEARCH_TYPE.KEY, "skin_res");
        cachemap_mgr.cfg_theme_act_famous_lottery_rewardCache = new CfgCacheMapBase<cfg_theme_act_famous_lottery_reward>(cache_mgr.cfg_theme_act_famous_lottery_reward_cache, SEARCH_TYPE.ID, "reward_id");
        cachemap_mgr.cfg_theme_act_hero_lottery_showCacheTemp = new CfgCacheMapBase<cfg_theme_act_hero_lottery_show>(cache_mgr.cfg_theme_act_hero_lottery_show_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_theme_act_itemCacheTemp = new CfgCacheMapBase<cfg_theme_act_item>(cache_mgr.cfg_theme_act_item_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_theme_act_rare_lottery_rewardCache = new CfgCacheMapBase<cfg_theme_act_rare_lottery_reward>(cache_mgr.cfg_theme_act_rare_lottery_reward_cache, SEARCH_TYPE.ID, "reward_id");
        cachemap_mgr.cfg_theme_act_skin_lotteryAllCache = new CfgCacheMapBase<cfg_theme_act_skin_lottery>(cache_mgr.cfg_theme_act_skin_lottery_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_theme_act_skin_lottery_costCache = new CfgCacheMapBase<cfg_theme_act_skin_lottery_cost>(cache_mgr.cfg_theme_act_skin_lottery_cost_cache, SEARCH_TYPE.MKEY, "plan_id,floor");
        cachemap_mgr.cfg_theme_act_wish_lotteryCache = new CfgCacheMapBase<cfg_theme_act_wish_lottery>(cache_mgr.cfg_theme_act_wish_lottery_cache, SEARCH_TYPE.ID, "reward_id");
        cachemap_mgr.cfg_theme_act_wish_lottery_itemCache = new CfgCacheMapBase<cfg_theme_act_wish_lottery_item[]>(cache_mgr.cfg_theme_act_wish_lottery_item_cache, SEARCH_TYPE.MGRP, "reward_id,hero_type_id");
        cachemap_mgr.cfg_theme_act_wish_lottery_showCacheTemp = new CfgCacheMapBase<cfg_theme_act_wish_lottery_show>(cache_mgr.cfg_theme_act_wish_lottery_show_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_tiled_effectCache = new CfgCacheMapBase<cfg_tiled_effect>(cache_mgr.cfg_tiled_effect_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_tiled_mapCache = new CfgCacheMapBase<cfg_tiled_map>(cache_mgr.cfg_tiled_map_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_time_achievementByIdCache = new CfgCacheMapBase<cfg_time_achievement>(cache_mgr.cfg_time_achievement_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_time_achievementCache = new CfgCacheMapBase<cfg_time_achievement[]>(cache_mgr.cfg_time_achievement_cache, SEARCH_TYPE.GRP, "page");
        cachemap_mgr.cfg_time_activity_dropCache = new CfgCacheMapBase<cfg_time_activity_drop>(cache_mgr.cfg_time_activity_drop_cache, SEARCH_TYPE.MKEY, "rewards_id,type");
        cachemap_mgr.cfg_time_activity_shopCache = new CfgCacheMapBase<cfg_time_activity_shop[]>(cache_mgr.cfg_time_activity_shop_cache, SEARCH_TYPE.MGRP, "shop_id,rewards_id");
        cachemap_mgr.cfg_time_activity_shopAllCache = new CfgCacheMapBase<cfg_time_activity_shop>(cache_mgr.cfg_time_activity_shop_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_time_activity_weekCache = new CfgCacheMapBase<cfg_time_activity_week>(cache_mgr.cfg_time_activity_week_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_tipsCache = new CfgCacheMapBase<cfg_tips>(cache_mgr.cfg_tips_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_titleCache = new CfgCacheMapBase<cfg_title>(cache_mgr.cfg_title_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_travelCache = new CfgCacheMapBase<cfg_travel>(cache_mgr.cfg_travel_cache, SEARCH_TYPE.KEY, "mission_type");
        cachemap_mgr.cfg_travel_extCache = new CfgCacheMapBase<cfg_travel_ext>(cache_mgr.cfg_travel_ext_cache, SEARCH_TYPE.KEY, "index");
        cachemap_mgr.cfg_treasure_boxCache = new CfgCacheMapBase<cfg_treasure_box>(cache_mgr.cfg_treasure_box_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_treasure_box_typeCache = new CfgCacheMapBase<cfg_treasure_box_type>(cache_mgr.cfg_treasure_box_type_cache, SEARCH_TYPE.MKEY, "type,box_id");
        cachemap_mgr.cfg_treasure_box_typeGrpCache = new CfgCacheMapBase<cfg_treasure_box_type[]>(cache_mgr.cfg_treasure_box_type_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_treasure_energyCache = new CfgCacheMapBase<cfg_treasure_energy>(cache_mgr.cfg_treasure_energy_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_treasure_giftCache = new CfgCacheMapBase<cfg_treasure_gift>(cache_mgr.cfg_treasure_gift_cache, SEARCH_TYPE.KEY, "gift_id");
        cachemap_mgr.cfg_treasure_miscCache = new CfgCacheMapBase<cfg_treasure_misc>(cache_mgr.cfg_treasure_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_treasure_refresh_costCache = new CfgCacheMapBase<cfg_treasure_refresh_cost>(cache_mgr.cfg_treasure_refresh_cost_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_treasure_workerCache = new CfgCacheMapBase<cfg_treasure_worker>(cache_mgr.cfg_treasure_worker_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_trig_skillCache = new CfgCacheMapBase<cfg_trig_skill[]>(cache_mgr.cfg_trig_skill_cache, SEARCH_TYPE.GRP, "skill_id");
        cachemap_mgr.cfg_ui_button_styleCache = new CfgCacheMapBase<cfg_ui_button_style>(cache_mgr.cfg_ui_button_style_cache, SEARCH_TYPE.KEY, "btn_res");
        cachemap_mgr.cfg_ui_preloadCache = new CfgCacheMapBase<cfg_ui_preload>(cache_mgr.cfg_ui_preload_cache, SEARCH_TYPE.KEY, "ui");
        cachemap_mgr.cfg_ui_residentCache = new CfgCacheMapBase<cfg_ui_resident>(cache_mgr.cfg_ui_resident_cache, SEARCH_TYPE.KEY, "res");
        cachemap_mgr.cfg_up_star_giftCache = new CfgCacheMapBase<cfg_up_star_gift>(cache_mgr.cfg_up_star_gift_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_up_star_rewardCache = new CfgCacheMapBase<cfg_up_star_reward>(cache_mgr.cfg_up_star_reward_cache, SEARCH_TYPE.MKEY, "reward_id,id");
        cachemap_mgr.cfg_up_star_rewardByroundCache = new CfgCacheMapBase<cfg_up_star_reward[]>(cache_mgr.cfg_up_star_reward_cache, SEARCH_TYPE.MGRP, "reward_id,round");
        cachemap_mgr.cfg_up_star_rewardByreward_idCache = new CfgCacheMapBase<cfg_up_star_reward[]>(cache_mgr.cfg_up_star_reward_cache, SEARCH_TYPE.GRP, "reward_id");
        cachemap_mgr.cfg_vip_daily_missionCache = new CfgCacheMapBase<cfg_vip_daily_mission>(cache_mgr.cfg_vip_daily_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_vip_daily_mission_giftCacheTemp = new CfgCacheMapBase<cfg_vip_daily_mission_gift>(cache_mgr.cfg_vip_daily_mission_gift_cache, SEARCH_TYPE.NULL, "null");
        cachemap_mgr.cfg_vip_kefuCache = new CfgCacheMapBase<cfg_vip_kefu>(cache_mgr.cfg_vip_kefu_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_vip_kefuBysdknameCache = new CfgCacheMapBase<cfg_vip_kefu>(cache_mgr.cfg_vip_kefu_cache, SEARCH_TYPE.KEY, "checkSdk");
        cachemap_mgr.cfg_vip_kefu_reviewCache = new CfgCacheMapBase<cfg_vip_kefu_review>(cache_mgr.cfg_vip_kefu_review_cache, SEARCH_TYPE.KEY, "platform_id");
        cachemap_mgr.cfg_war_flagCache = new CfgCacheMapBase<cfg_war_flag>(cache_mgr.cfg_war_flag_cache, SEARCH_TYPE.ID, "nation");
        cachemap_mgr.cfg_war_flag_facadeCache = new CfgCacheMapBase<cfg_war_flag_facade[]>(cache_mgr.cfg_war_flag_facade_cache, SEARCH_TYPE.GRP, "nation");
        cachemap_mgr.cfg_war_flag_levelCache = new CfgCacheMapBase<cfg_war_flag_level>(cache_mgr.cfg_war_flag_level_cache, SEARCH_TYPE.MKEY, "nation,level");
        cachemap_mgr.cfg_war_flag_linkCache = new CfgCacheMapBase<cfg_war_flag_link>(cache_mgr.cfg_war_flag_link_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_war_flag_recycleCache = new CfgCacheMapBase<cfg_war_flag_recycle>(cache_mgr.cfg_war_flag_recycle_cache, SEARCH_TYPE.KEY, "time");
        cachemap_mgr.cfg_war_flag_stageCache = new CfgCacheMapBase<cfg_war_flag_stage>(cache_mgr.cfg_war_flag_stage_cache, SEARCH_TYPE.MKEY, "nation,stage");
        cachemap_mgr.cfg_war_flag_stageCacheByNationCache = new CfgCacheMapBase<cfg_war_flag_stage[]>(cache_mgr.cfg_war_flag_stage_cache, SEARCH_TYPE.GRP, "nation");
        cachemap_mgr.cfg_war_log_missionCache = new CfgCacheMapBase<cfg_war_log_mission>(cache_mgr.cfg_war_log_mission_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_war_log_mission_pay_rewardTypeCache = new CfgCacheMapBase<cfg_war_log_mission_pay_reward[]>(cache_mgr.cfg_war_log_mission_pay_reward_cache, SEARCH_TYPE.GRP, "reward_id");
        cachemap_mgr.cfg_war_log_mission_score_rewardTypeCache = new CfgCacheMapBase<cfg_war_log_mission_score_reward[]>(cache_mgr.cfg_war_log_mission_score_reward_cache, SEARCH_TYPE.GRP, "reward_id");
        cachemap_mgr.cfg_wars_honorCache = new CfgCacheMapBase<cfg_wars_honor>(cache_mgr.cfg_wars_honor_cache, SEARCH_TYPE.KEY, "type");
        cachemap_mgr.cfg_wars_map_campCache = new CfgCacheMapBase<cfg_wars_map_camp>(cache_mgr.cfg_wars_map_camp_cache, SEARCH_TYPE.MKEY, "map_type,camp");
        cachemap_mgr.cfg_wars_map_camp_listCache = new CfgCacheMapBase<cfg_wars_map_camp[]>(cache_mgr.cfg_wars_map_camp_cache, SEARCH_TYPE.GRP, "map_type");
        cachemap_mgr.cfg_wars_map_city_allCache = new CfgCacheMapBase<cfg_wars_map_city[]>(cache_mgr.cfg_wars_map_city_cache, SEARCH_TYPE.GRP, "map_type");
        cachemap_mgr.cfg_wars_map_cityCache = new CfgCacheMapBase<cfg_wars_map_city>(cache_mgr.cfg_wars_map_city_cache, SEARCH_TYPE.MKEY, "map_type,city_id");
        cachemap_mgr.cfg_wars_map_city_posCache = new CfgCacheMapBase<cfg_wars_map_city>(cache_mgr.cfg_wars_map_city_cache, SEARCH_TYPE.MKEY, "map_type,pos");
        cachemap_mgr.cfg_wars_map_near_city2Cache = new CfgCacheMapBase<cfg_wars_map_near_city2>(cache_mgr.cfg_wars_map_near_city2_cache, SEARCH_TYPE.MKEY, "map_type,start_pos,end_pos");
        cachemap_mgr.cfg_wars_map_near_city_list2Cache = new CfgCacheMapBase<cfg_wars_map_near_city2[]>(cache_mgr.cfg_wars_map_near_city2_cache, SEARCH_TYPE.GRP, "map_type");
        cachemap_mgr.cfg_wars_map_typeCache = new CfgCacheMapBase<cfg_wars_map_type>(cache_mgr.cfg_wars_map_type_cache, SEARCH_TYPE.ID, "map_type");
        cachemap_mgr.cfg_wars_map_type_sys_idCache = new CfgCacheMapBase<cfg_wars_map_type>(cache_mgr.cfg_wars_map_type_cache, SEARCH_TYPE.KEY, "sys_id");
        cachemap_mgr.cfg_wars_map_type_matchCache = new CfgCacheMapBase<cfg_wars_map_type[]>(cache_mgr.cfg_wars_map_type_cache, SEARCH_TYPE.GRP, "match_type");
        cachemap_mgr.cfg_wars_miscCache = new CfgCacheMapBase<cfg_wars_misc>(cache_mgr.cfg_wars_misc_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_wars_missionCache = new CfgCacheMapBase<cfg_wars_mission>(cache_mgr.cfg_wars_mission_cache, SEARCH_TYPE.KEY, "id");
        cachemap_mgr.cfg_wars_stateCache = new CfgCacheMapBase<cfg_wars_state>(cache_mgr.cfg_wars_state_cache, SEARCH_TYPE.MKEY, "map_type,plan_id,state");
        cachemap_mgr.cfg_wars_state_modelCache = new CfgCacheMapBase<cfg_wars_state[]>(cache_mgr.cfg_wars_state_cache, SEARCH_TYPE.MGRP, "map_type,plan_id,model");
        cachemap_mgr.cfg_wars_text_constCache = new CfgCacheMapBase<cfg_wars_text_const>(cache_mgr.cfg_wars_text_const_cache, SEARCH_TYPE.KEY, "key");
        cachemap_mgr.cfg_week_targetBytypeCache = new CfgCacheMapBase<cfg_week_target[]>(cache_mgr.cfg_week_target_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_week_targetCache = new CfgCacheMapBase<cfg_week_target>(cache_mgr.cfg_week_target_cache, SEARCH_TYPE.MKEY, "type,id");
        cachemap_mgr.cfg_week_target_levelBytypeCache = new CfgCacheMapBase<cfg_week_target_level[]>(cache_mgr.cfg_week_target_level_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_week_target_levelCache = new CfgCacheMapBase<cfg_week_target_level>(cache_mgr.cfg_week_target_level_cache, SEARCH_TYPE.MKEY, "type,assist_level");
        cachemap_mgr.cfg_wing_buffAllCache = new CfgCacheMapBase<cfg_wing_buff>(cache_mgr.cfg_wing_buff_cache, SEARCH_TYPE.KEY, "level");
        cachemap_mgr.cfg_wing_heroAllCache = new CfgCacheMapBase<cfg_wing_hero>(cache_mgr.cfg_wing_hero_cache, SEARCH_TYPE.MKEY, "type_id,level");
        cachemap_mgr.cfg_wing_heroCache = new CfgCacheMapBase<cfg_wing_hero[]>(cache_mgr.cfg_wing_hero_cache, SEARCH_TYPE.GRP, "type_id");
        cachemap_mgr.cfg_wing_hero_skinCache = new CfgCacheMapBase<cfg_wing_hero_skin>(cache_mgr.cfg_wing_hero_skin_cache, SEARCH_TYPE.KEY, "model");
        cachemap_mgr.cfg_wing_levelAllCache = new CfgCacheMapBase<cfg_wing_level>(cache_mgr.cfg_wing_level_cache, SEARCH_TYPE.MKEY, "wing_id,level");
        cachemap_mgr.cfg_wing_levelCache = new CfgCacheMapBase<cfg_wing_level[]>(cache_mgr.cfg_wing_level_cache, SEARCH_TYPE.GRP, "wing_id");
        cachemap_mgr.cfg_world_boss_hurt_rewardsTypeCache = new CfgCacheMapBase<cfg_world_boss_hurt_rewards[]>(cache_mgr.cfg_world_boss_hurt_rewards_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_world_boss_hurt_rewardsCache = new CfgCacheMapBase<cfg_world_boss_hurt_rewards[]>(cache_mgr.cfg_world_boss_hurt_rewards_cache, SEARCH_TYPE.GRP, "type");
        cachemap_mgr.cfg_world_boss_levelCache = new CfgCacheMapBase<cfg_world_boss_level[]>(cache_mgr.cfg_world_boss_level_cache, SEARCH_TYPE.GRP, "boss_level");
        cachemap_mgr.cfg_world_boss_levelbytypeIdCache = new CfgCacheMapBase<cfg_world_boss_level[]>(cache_mgr.cfg_world_boss_level_cache, SEARCH_TYPE.GRP, "skin_half");
        cachemap_mgr.cfg_world_mapCache = new CfgCacheMapBase<cfg_world_map>(cache_mgr.cfg_world_map_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_world_map_2Cache = new CfgCacheMapBase<cfg_world_map_2>(cache_mgr.cfg_world_map_2_cache, SEARCH_TYPE.ID, "id");
        cachemap_mgr.cfg_wxShareCache = new CfgCacheMapBase<cfg_wxShare[]>(cache_mgr.cfg_wxShare_cache, SEARCH_TYPE.GRP, "pf");
        cachemap_mgr.cfg_wxTurn_giftCache = new CfgCacheMapBase<cfg_wxTurn_gift>(cache_mgr.cfg_wxTurn_gift_cache, SEARCH_TYPE.KEY, "platform");
        cachemap_mgr.cfg_wx_game_clubCache = new CfgCacheMapBase<cfg_wx_game_club[]>(cache_mgr.cfg_wx_game_club_cache, SEARCH_TYPE.GRP, "plat_id");
        cachemap_mgr.cfg_xswh_bossCache = new CfgCacheMapBase<cfg_xswh_boss[]>(cache_mgr.cfg_xswh_boss_cache, SEARCH_TYPE.GRP, "week");
        cachemap_mgr.cfg_xswh_giftCache = new CfgCacheMapBase<cfg_xswh_gift>(cache_mgr.cfg_xswh_gift_cache, SEARCH_TYPE.ID, "times");
        cachemap_mgr.cfg_xswh_hurt_rewardsCache = new CfgCacheMapBase<cfg_xswh_hurt_rewards[]>(cache_mgr.cfg_xswh_hurt_rewards_cache, SEARCH_TYPE.GRP, "week");
        cachemap_mgr.cfg_xswh_rank_rewardsCache = new CfgCacheMapBase<cfg_xswh_rank_rewards[]>(cache_mgr.cfg_xswh_rank_rewards_cache, SEARCH_TYPE.GRP, "week");
        cachemap_mgr.cfg_ybzk_rewardCache = new CfgCacheMapBase<cfg_ybzk_reward[]>(cache_mgr.cfg_ybzk_reward_cache, SEARCH_TYPE.MGRP, "rewards_id,type");
        cachemap_mgr.cfg_zero_buyCache = new CfgCacheMapBase<cfg_zero_buy>(cache_mgr.cfg_zero_buy_cache, SEARCH_TYPE.ID, "day");
        cachemap_mgr.cfg_errorCodeCache = new CfgCacheMapBase<errorCode>(cache_mgr.errorCode_cache, SEARCH_TYPE.ID, "codeId");
        cachemap_mgr.cfg_fightAttrByIdCache = new CfgCacheMapBase<fightAttr>(cache_mgr.fightAttr_cache, SEARCH_TYPE.ID, "attrID");
        cachemap_mgr.cfg_fightAttrByKeyCache = new CfgCacheMapBase<fightAttr>(cache_mgr.fightAttr_cache, SEARCH_TYPE.KEY, "attrKey");
        cachemap_mgr.cfg_fightAttrByTypeCache = new CfgCacheMapBase<fightAttr[]>(cache_mgr.fightAttr_cache, SEARCH_TYPE.GRP, "show_type");
        cachemap_mgr.victoryMacroCache = new CfgCacheMapBase<victoryMacro>(cache_mgr.victoryMacro_cache, SEARCH_TYPE.ID, "macroID");

        for(var k in cache_mgr.caches) {
            var cache = cache_mgr.caches[k];
            cache.once(Event.COMPLETE, null, (cache) => {
                CfgCacheMapMgr_CB.onComplete(cache);
            }, [cache]);
        }
        
    }
}

