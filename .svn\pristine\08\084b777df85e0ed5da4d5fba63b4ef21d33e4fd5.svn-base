import {Handler} from "laya/utils/Handler";
import {GlobalConfig} from "../../../../game/GlobalConfig";
import {SentryUtils} from "../../../../game/SentryUtils";
import {CfgCacheMapMgr} from "../../../cfg/CfgCacheMapMgr";
import {cfg_hero_base} from "../../../cfg/vo/cfg_hero_base";
import {cfg_hero_star} from "../../../cfg/vo/cfg_hero_star";
import {cfg_hero_star_stage} from "../../../cfg/vo/cfg_hero_star_stage";
import {cfg_skill_level} from "../../../cfg/vo/cfg_skill_level";
import {ConfigManager} from "../../../managers/ConfigManager";
import {DispatchManager} from "../../../managers/DispatchManager";
import {Connection} from "../../../net/Connection";
import {p_goods} from "../../../proto/common/p_goods";
import {p_hero} from "../../../proto/common/p_hero";
import {p_hero_fight} from "../../../proto/common/p_hero_fight";
import {p_hero_sm} from "../../../proto/common/p_hero_sm";
import {p_item} from "../../../proto/common/p_item";
import {p_kv} from "../../../proto/common/p_kv";
import {p_war_flag} from "../../../proto/common/p_war_flag";
import {m_casting_soul_op_tos} from "../../../proto/line/m_casting_soul_op_tos";
import {m_equip_auto_load_tos} from "../../../proto/line/m_equip_auto_load_tos";
import {m_equip_auto_unload_tos} from "../../../proto/line/m_equip_auto_unload_tos";
import {m_equip_bingfa_exchange_tos} from "../../../proto/line/m_equip_bingfa_exchange_tos";
import {m_equip_bingfa_load_tos} from "../../../proto/line/m_equip_bingfa_load_tos";
import {m_equip_bingfa_op_tos} from "../../../proto/line/m_equip_bingfa_op_tos";
import {m_god_equip_compose_tos} from "../../../proto/line/m_god_equip_compose_tos";
import {m_god_equip_recast_tos} from "../../../proto/line/m_god_equip_recast_tos";
import {m_hero_act_fourteen_toc} from "../../../proto/line/m_hero_act_fourteen_toc";
import {m_hero_act_fourteen_tos} from "../../../proto/line/m_hero_act_fourteen_tos";
import {m_hero_bag_expansion_tos} from "../../../proto/line/m_hero_bag_expansion_tos";
import {m_hero_list_toc} from "../../../proto/line/m_hero_list_toc";
import {m_hero_lock_tos} from "../../../proto/line/m_hero_lock_tos";
import {m_hero_my_rank_tos} from "../../../proto/line/m_hero_my_rank_tos";
import {m_hero_recycle_preview_tos} from "../../../proto/line/m_hero_recycle_preview_tos";
import {m_hero_recycle_tos} from "../../../proto/line/m_hero_recycle_tos";
import {m_hero_update_fight_toc} from "../../../proto/line/m_hero_update_fight_toc";
import {m_hero_upgrade_tos} from "../../../proto/line/m_hero_upgrade_tos";
import {m_item_compose_tos} from "../../../proto/line/m_item_compose_tos";
import {m_soul_hero_info_toc} from "../../../proto/line/m_soul_hero_info_toc";
import {m_soul_hero_link_tos} from "../../../proto/line/m_soul_hero_link_tos";
import {m_soul_hero_reset_tos} from "../../../proto/line/m_soul_hero_reset_tos";
import {m_soul_hero_unlock_tos} from "../../../proto/line/m_soul_hero_unlock_tos";
import {m_war_flag_active_tos} from "../../../proto/line/m_war_flag_active_tos";
import {m_war_flag_exchange_tos} from "../../../proto/line/m_war_flag_exchange_tos";
import {m_war_flag_link_tos} from "../../../proto/line/m_war_flag_link_tos";
import {m_war_flag_op_tos} from "../../../proto/line/m_war_flag_op_tos";
import {ColorUtil} from "../../../util/ColorUtil";
import {GameUtil} from "../../../util/GameUtil";
import {ObjectUtil} from "../../../util/ObjectUtil";
import {StringUtil} from "../../../util/StringUtil";
import {TipsUtil} from "../../../util/TipsUtil";
import {XmlFormatVo} from "../../../util/XmlFormatVo";
import {BaseDataCenter} from "../../BaseDataCenter";
import {DataCenter} from "../../DataCenter";
import {DeputyDataCenter} from "../../deputy/data/DeputyDataCenter";
import {DudufuDataCenter} from "../../dudufu/data/DudufuDataCenter";
import HeroEquipManager from "../../equip/data/HeroEquipManager";
import HeroEquipVo from "../../equip/data/HeroEquipVo";
import {EquipExtKey} from "../../equip/EquipExtKey";
import {EquipUtil} from "../../equip/EquipUtil";
import {ForgeDataCenter} from "../../forge/data/ForgeDataCenter";
import {GeneralMansionDataCenter} from "../../generalMansion/data/GeneralMansionDataCenter";
import {GoodsVO} from "../../goods/GoodsVO";
import {ItemConst} from "../../goods/ItemConst";
import {GuideConst} from "../../guide/GuideConst";
import {GuideMgr} from "../../guide/GuideMgr";
import {HeroSkinDataCenter} from "../../heroSkin/data/HeroSkinDataCenter";
import {LineUpDataCenter} from "../../lineUp/data/LineUpDataCenter";
import {LineUpUtil} from "../../lineUp/util/LineUpUtil";
import {LineUpBuffUtil} from "../../lineUpBuff/LineUpBuffUtil";
import {MiscConst} from "../../misc_config/MiscConst";
import {ModuleCommand} from "../../ModuleCommand";
import {PanelEventConstants} from "../../PanelEventConstants";
import VipTeQuanUtil from "../../payment/VipTeQuanUtil";
import {PaymentVO} from "../../payment/vo/PaymentVO";
import {RedPointMgr} from "../../redPoint/RedPointMgr";
import {FightAttrMgr} from "../../role/data/FightAttrMgr";
import {FightAttrVO} from "../../role/vo/FightAttrVO";
import {SettingDataCenter} from "../../setting/data/SettingDataCenter";
import {SkillDataCenter} from "../../skill/data/SkillDataCenter";
import {SkillItemVo} from "../../skill/vo/SkillItemVo";
import {GoodsChangeManager} from "../../test_bag/GoodsChangeManager";
import {GoodsManager} from "../../test_bag/GoodsManager";
import {WingDataCenter} from "../../wing/data/WingDataCenter";
import {HeroUtil} from "../util/HeroUtil";
import {HeroCardFacadeVo} from "../vo/HeroCardFacadeVo";
import {EHeroColor, HeroConsts, HeroMaterialData, HeroNation, PHero} from "./HeroConsts";
import HeroEquipSortVo from "./HeroEquipSortVo";

import {LangConst} from "../../../auto/LangConst";
import {MatchConst} from "../../../auto/ConstAuto";
import {MiscConstAuto} from "../../../auto/MiscConstAuto";
import {m_hero_evolve_op_tos} from "../../../proto/line/m_hero_evolve_op_tos";
import {DivineDataCenter} from "../../Divine/data/DivineDataCenter";
import {HeroCheerDataCenter} from "../../heroCheer/data/HeroCheerDataCenter";
import {HeroRecycleNewDataCenter} from "../../heroRecycle/data/HeroRecycleNewDataCenter";
import {HeroEvolveDataCenter} from "./HeroEvolveDataCenter";
import {EquipDataCenter} from "../../equip/data/EquipDataCenter";
import {ItemMacro} from "../../../auto/ItemMacro";

const enum EUpdateType {
    UPD_TYPE_NEW = 1,  //新增
    UPD_TYPE_MODIFY = 2, //修改
    UPD_TYPE_DELETE = 3, //删除
}

//数据不要用静态类型的
//可以在本模块引用，不可跨模块引用
//本模块引用的时候不要缓存instance，每次通过instance获取即可
export class HeroDataCenter extends BaseDataCenter {

    /**----------------------------------界面相关----------------------------------- */
        //标签ID-总览
    public static TAB_SUM: number = 0;
    //标签ID-升级
    public static TAB_LEVEL: number = 1;
    //标签ID-升阶
    public static TAB_STAGE: number = 2;
    //标签ID-升星
    public static TAB_STAR: number = 3;
    //标签ID-战魂
    public static TAB_FIGHT_SOUL: number = 4;
    //标签ID-能级
    public static TAB_SOUL_LEVEL: number = 5;
    //标签ID-碎片合成
    public static TAB_DEBRIS: number = 20;
    //标签ID-英雄
    public static TAB_WU_JIAN: number = 21;
    //标签ID-天赋
    public static TAB_BINGFA: number = 22;
    //标签ID-神装
    public static TAB_GODEQUIP: number = 23;
    //标签ID-皮肤
    public static TAB_HERO_SKIN: number = 24;
    //标签ID-觉醒
    public static TAB_HERO_AWAKE: number = 25;
    //英雄升级 星级限制类型
    public static LIMIT_STAR: number = 1;
    //英雄升级 阶位限制类型
    public static LIMIT_STAGE: number = 2;
    //英雄升级 需要 同等级以上英雄 数量
    public static LIMIT_NEED_HERO_NUM: number = 3;
    //英雄升级 升星每一阶满星的星数
    public static HERO_STAR_ROUND_NUM: number = 7;
    //协议----升级
    public static LEVEL_UPDATE: number = 1;
    //协议----升阶
    public static STAGE_UPDATE: number = 2;
    //协议----升星
    public static STAR_UPDATE: number = 3;
    //协议----星阶
    public static STAR_STAGE_UPDATE: number = 4;
    //协议----查看
    public static STAR_STAGE_SHOW: number = 6;
    //协议----能级
    public static SOUL_LEVEL: number = 8;
    //升阶天赋类型
    public static STAGE_TALENT: number = 1;
    //升星天赋类型
    public static STAR_TALENT: number = 2;

    /**----------------------------------阵容相关----------------------------------- */
    /**出战阵容 */
    public static LINE_UP_FIGHT: number = 1;
    /**援军阵容 */
    public static LINE_UP_YUAN_JUN: number = 2;
    /**助阵阵容 */
    public static LINE_UP_ZHU_ZHEN: number = 3;
    /**主题玩法租借英雄 */
    public static LINE_UP_MAZE_LEASE: number = 4;
    /**w3新版本助阵英雄 */
    public static LINE_UP_RENT: number = 5;
    /**推荐阵容__预览 */
    public static LINE_UP_PREVIEW: number = 99;
    /**竞技场防守阵容 */
    public static LINE_UP_AREAN: number = 102;
    /** 冠军赛 */
    public static LINE_UP_RANDOM_PVP: number = 110;
    //擂台争霸
    public static LINE_UP_ARENA_MATCH: number = 109;

    public static readonly LINE_UP_TM_MULTI_BOSS_TEAM: number = 203; //组队讨伐

    /**国战托管队伍阵容 */
    public static LINE_UP_WAR_AUTO: number = 2000; // 托管队伍有6只 2000 ~ 2005
    /** 六国争霸 */
    public static LINE_UP_CROSSREALM_WAR_AUTO: number = 3000; // 托管队伍有6只 3000 ~ 3005

    /** 官渡 */
    public static LINE_UP_CSGD_WAR_AUTO: number = 4000; // 托管队伍有6只 4000 ~ 4005

    /** 公会试炼 */
    public static LINE_UP_FAMILY_TRIAL_WAR_AUTO: number = 300; // 托管队伍有6只 5000 ~ 5005

    /**养成位置阵容 */
    public static LINE_UP_BREED: number = 9999;

    public static debrisArr: number[] = [0, 0, 0, 11060001, 11060002, 11060003, 11060004];


    public select_hero_id: number = 0;
    public select_type_id: number = 0;
    public select_chip_id: number = 0;
    public select_soul_hero_id: number = 0;// 当前解锁同心的异能英雄id

    //不显示
    public static ATTR_SHOW_TYPE_NOT: number = 0;
    //基础属性
    public static ATTR_SHOW_TYPE_NORAML: number = 1;
    //高级属性
    public static ATTR_SHOWT_TYPE_STRENGTH: number = 2;
    //阵营属性
    public static ATTR_SHOWT_TYPE_NATION: number = 3;
    /* 竞技属性 */
    public static ATTR_SHOWT_TYPE_PVP: number = 4;
    //装备kind列表
    public static equipKindList: number[] = [ItemMacro.ITEM_KIND_WEAPON, ItemMacro.ITEM_KIND_ARMET, ItemMacro.ITEM_KIND_SHOES, ItemMacro.ITEM_KIND_ARMOR, ItemMacro.ITEM_KIND_BING_FU, ItemMacro.ITEM_KIND_BING_FU, ItemMacro.ITEM_KIND_WING, ItemMacro.ITEM_KIND_DEPUTY];
    public static godEquipKindList: number[] = [ItemMacro.ITEM_KIND_GOD_EQUIP_1, ItemMacro.ITEM_KIND_GOD_EQUIP_2, ItemMacro.ITEM_KIND_GOD_EQUIP_3, ItemMacro.ITEM_KIND_GOD_EQUIP_4];
    /**英雄---------------------------------------------- */
    /**lineUpType -> heroId[] */
    private _completelineupMap: Map<number, number[]> = new Map;

    private _hero_list: PHero[] = [];
    private _heroMap: Map<number, PHero> = new Map;
    private _heroTypeListMap: Map<number, PHero[]> = new Map;

    //已重置次数
    public resetCount: number = 0;
    //锁定列表
    public lockHeroIds: number[] = [];//锁英雄
    public lockHeroTypes: number[] = [];//锁英雄类型
    //已扩容次数
    public expansionCount: number = 0;
    public heroCapacity: number = 0;
    public war_flag_infos: p_war_flag[] = [];
    public war_flag_link_infos: number[] = [];
    public war_flag_can_link_list: number[] = [];
    public war_flag_unlink_times: number = 0;
    public bingfa_half_times: number = 0;
    public war_flag_exchange_times: number = 0;
    public cur_war_flag_nation: number = 0;

    public heroMyRankList: p_kv[] = [];

    public soulHeroLinkInfo: m_soul_hero_info_toc;
    public isHadSoulHero: boolean = false;
    public heroInfoDialogCount: number = 0;
    public bingfaPosIndex: number = 0;

    public backOneKeyUpStarList: Map<number, number> = new Map();

    //已激活14星英雄列表
    public _fourteenInfo: m_hero_act_fourteen_toc;
    public set fourteenInfo(info: m_hero_act_fourteen_toc) {
        this._fourteenInfo = info;
    }


    public get fourteenInfo(): m_hero_act_fourteen_toc {
        if (!this._fourteenInfo) {
            this._fourteenInfo = new m_hero_act_fourteen_toc();
        }
        return this._fourteenInfo;
    }

    constructor() {
        super();
        this.init();
    }

    static _instance: HeroDataCenter = null;
    static get instance(): HeroDataCenter {
        if (HeroDataCenter._instance == null) {
            HeroDataCenter._instance = new HeroDataCenter();
        }
        return HeroDataCenter._instance;
    }

    reset(): void {
        HeroDataCenter._instance = null;
    }

    init() {
        GoodsChangeManager.ins.addItemChangeListener(40015, this, this.checkGodEquipRedPoint);
    }

    /**初始化英雄列表 */
    public setHeroInfo(vo: m_hero_list_toc): void {
        vo.lineup_list.forEach(lineUpInfo => {
            LineUpDataCenter.instance.setLineUpInfo(lineUpInfo);
        });

        this._hero_list.length = 0;
        this._heroMap.clear();
        this._heroTypeListMap.clear();
        this.lockHeroIds = vo.lock_hero_ids;
        this.lockHeroTypes = vo.lock_hero_types;
        this.fourteenInfo.fourteen_hero_ids = vo.fourteen_hero_ids;

        for (let i = 0; i < vo.hero_list.length; ++i) {
            let heroInfo = vo.hero_list[i];
            let pHero = this.toPHero(heroInfo);
            this._hero_list[i] = pHero;
            this.updateMaxPowerHero(pHero);
            if (!this.isHadSoulHero) {
                this.isHadSoulHero = pHero.ori_nation == HeroNation.NATION_HUN;
            }
        }

        HeroDataCenter.instance.updateHeroMap(this._hero_list, 1, false);

        HeroDataCenter.instance.resetCount = vo.recycle_count;
        HeroDataCenter.instance.lockHeroIds = vo.lock_hero_ids;
        HeroDataCenter.instance.expansionCount = vo.expansion_count;
        HeroDataCenter.instance.heroCapacity = vo.hero_capacity;
        HeroDataCenter.checkIsHeroStarUpGrowthOpen(this._hero_list);

        LineUpDataCenter.instance.CheckLineUpRedPoint();
    }

    /**当前选中的分解英雄 */
    private _selectRecycleHero: Map<number, GoodsVO>;

    public get selectRecycleHero(): Map<number, GoodsVO> {
        if (this._selectRecycleHero == null) {
            this._selectRecycleHero = new Map<number, GoodsVO>();
        }
        return this._selectRecycleHero;
    }

    /**获取英雄稀有度对应的颜色 */
    private static getColorByCfgHeroBase(cfgHeroBase: cfg_hero_base): EHeroColor {
        // w7 屏蔽
        if (cfgHeroBase) {
            return Math.max(Math.min(cfgHeroBase.init_star - 1, 5), 0)
        } else {
            return EHeroColor.QUALITY_PURPLE;
        }
    }

    /**获取英雄稀有度对应的颜色 */
    private static getColorByHero(p_hero: p_hero): EHeroColor {
        let cfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(p_hero.type_id);
        return this.getColorByCfgHeroBase(cfg);
    }

    /**获取英雄稀有度对应的颜色 */
    public static getColorByHeroTypeId(heroTypeId: number): EHeroColor {
        let cfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(heroTypeId);
        return this.getColorByCfgHeroBase(cfg);
    }


    /**获取英雄稀有度对应的颜色 */
    public static getColorByHeroOrCfgHero(hero: p_hero | cfg_hero_base): EHeroColor {
        if (!hero) {
            return EHeroColor.QUALITY_PURPLE;
        } else if (hero instanceof p_hero) {
            return this.getColorByHero(hero);
        } else {
            return this.getColorByCfgHeroBase(hero);
        }
    }

    /**获取 英雄稀有度 对应的 16进制颜色值 */
    public static getColorHexStrByHeroOrCfgHero(hero: p_hero | cfg_hero_base): string {
        let color: EHeroColor = this.getColorByHeroOrCfgHero(hero);
        return ColorUtil.GetQualityToColor(color);
    }

    /**当前国家拥有英雄的数量 */
    public getNationHeroNumber(nation: number): number {
        if (nation == 0)
            return this._hero_list.length;
        let num: number = 0;
        this._hero_list.forEach(hero => {
            let cfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(hero.type_id);
            if (cfg.nation == nation)
                num++;
        });
        return num;
    }

    /**当前国家拥有的英雄*/
    public getNationHero(nation: number, star: number = 5, excludeNation: number[] = [], contain_lineup: boolean = false): p_hero[] {
        let allNation = false;
        if (nation == 0) {
            allNation = true;
        }
        let heroList: p_hero[] = [];
        this._hero_list.forEach(hero => {
            let cfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(hero.type_id);

            let state: number = LineUpDataCenter.instance.getHeroLineUpType(hero.hero_id);
            if (!contain_lineup) {
                if (state > 0) {
                    return;
                }
            }
            if (hero.star == star) {
                if (allNation) {
                    if (excludeNation.length > 0) {
                        let isExcludeNation = false;
                        excludeNation.forEach((nation) => {
                            if (cfg.nation == nation) {
                                isExcludeNation = true;
                            }
                        });
                        if (!isExcludeNation) {
                            heroList.push(hero);
                        }
                    } else {
                        heroList.push(hero);
                    }
                } else {
                    if (cfg.nation == nation) {
                        heroList.push(hero);
                    }
                }
            }
        });
        return heroList;
    }

    /**当前国家所有英雄的数量 */
    public getNationHeroAllNumber(nation: number): number {
        if (nation == 0)
            return HeroDataCenter.baseCfgHeroArr.length;
        let num: number = 0;
        let arr: cfg_hero_base[] = HeroDataCenter.baseCfgHeroArr;
        ;
        arr.forEach(cfg => {
            if (cfg.nation == nation)
                num++;
        })
        return num;
    }

    /**判断英雄是否在占星台 共享位 中*/
    public static checkIsInTemple(hero: p_hero): boolean {
        /*W7屏蔽
        if (hero) {
            return hero.state == 11;
        }
        */
        return false;
    }

    public static checkIsInTempleByHeroId(hero_id: number): boolean {
        let h: p_hero = HeroDataCenter.instance.getHero(hero_id);
        return HeroDataCenter.checkIsInTemple(h);
    }

    /**判断 共享英雄 属于哪个占星台的 主星位英雄 */
    public static getTempleMainHero(heroId: number): p_hero {
        let mainHero: p_hero = null;
        let hero = HeroDataCenter.instance.getHero(heroId);
        if (HeroDataCenter.checkIsInTemple(hero)) {
            /*W7屏蔽
            mainHero = HeroDataCenter.instance.getHero(hero.temple_hero_id);
            */
        }
        return mainHero;
    }

    /**-------------------------------------------上阵相关 start --------------------------------------------- */
    /** 根据类型获取 阵容中每一队的名称,这里适用于多队形 */
    public getCompleteLineUpListByType(type: number): number[] {
        return HeroDataCenter.instance._completelineupMap.get(type) || [];
    }

    /**
     * 获取多队伍的数组列表
     * @param match_type
     */
    public getCompleteTeamList(match_type: number): number[][] {
        let teamList = HeroDataCenter.instance.getCompleteLineUpListByType(match_type);
        if (teamList) {
            let teamMemberList: number[][] = [];
            for (let i: number = 0; i < teamList.length; i++) {
                teamMemberList.push(LineUpDataCenter.instance.getLineUpListByType(teamList[i]));
            }
            return teamMemberList;
        }
        return null;
    }

    /**
     * 获取多队伍玩法中的某个队伍
     * @param match_type
     * @param teamIndex 数组列表位置
     */
    public getCompleteTeamByIndex(match_type: number, teamIndex: number): number[] {
        let teamList = HeroDataCenter.instance.getCompleteLineUpListByType(match_type);
        if (teamList) {
            return LineUpDataCenter.instance.getLineUpListByType(teamList[teamIndex]);
        }
        return null;
    }

    /**
     * 获取多队伍玩法中的队伍id
     * @param match_type
     * @param teamIndex 数组列表位置
     */
    public getCompleteTeamIdByIndex(match_type: number, teamIndex: number): number {
        let teamList = HeroDataCenter.instance.getCompleteLineUpListByType(match_type);
        if (teamList) {
            return teamList[teamIndex];
        }
        return null;
    }

    public getCompleteTeamMaxNum(match_type: number): number {
        let cfg = ConfigManager.cfg_lineup_numCache.get(match_type);
        if (cfg) {
            return cfg.maxnum;
        }
        return 0;
    }

    /**-------------------------------------------上阵相关 end --------------------------------------------- */
    /**更新英雄战斗属性 战斗力 */
    public updateHeroFight(msg: m_hero_update_fight_toc): void {
        let hero = this.getHero(msg.hero_id);
        if (hero) {
            let oriMaxHero = this.toPHero(this.maxPowerHero);
            hero.fight = msg.fight;
            hero.power = msg.power;
            this.updateMaxPowerHero(hero, oriMaxHero);
        }
    }

    private getHeroAddLevel(heroId: number, hero_lv: number): number {
        if (DudufuDataCenter.instance.is_unlock_dudu && !DudufuDataCenter.instance.checkHeroIsFiveHeroResonate(heroId)) {
            hero_lv += DudufuDataCenter.instance.dudu_add_level;
        }
        if (DudufuDataCenter.instance.is_unlock_dhyana) {
            hero_lv += DudufuDataCenter.instance.dhyana_add_level;
        }
        if (DudufuDataCenter.instance.checkHeroIsFiveHeroResonate(heroId) || DudufuDataCenter.instance.checkHeroIsDudu(heroId)) {
            hero_lv = DudufuDataCenter.instance.five_level;
        }
        return hero_lv;
    }

    /**更新英雄数据 更新或者激活新英雄 */
    public updateHeroList(updateType: number, heroes: p_hero[]): void {
        let updateHeroList: PHero[] = [];
        for (let i: number = 0; i < heroes.length; i++) {
            let updateHero = heroes[i];
            let heroId = updateHero.hero_id;
            let phero = this.toPHero(updateHero);
            if (updateType == EUpdateType.UPD_TYPE_NEW) {
                updateHeroList.push(phero);
                //新增
                let oriHero = this.getHero(heroId);
                if (oriHero) {
                    this.updateHeroByExist(oriHero, phero);
                } else {
                    this.updateHeroByAdd(phero);
                }

            } else if (updateType == EUpdateType.UPD_TYPE_MODIFY) {
                updateHeroList.push(phero);
                //更新
                let oriHero = this.getHero(heroId);
                if (oriHero) {
                    this.updateHeroByExist(oriHero, phero);
                }
            } else if (updateType == EUpdateType.UPD_TYPE_DELETE) {
                //删除
                let oriHero = this.getHero(heroId);
                if (oriHero) {
                    let hi = HeroDataCenter.instance._hero_list.indexOf(oriHero);
                    HeroDataCenter.instance._hero_list.splice(hi, 1);
                    HeroDataCenter.instance._heroMap.delete(heroId);
                    let list = HeroDataCenter.instance._heroTypeListMap.get(oriHero.type_id);
                    if (list) {
                        for (let i = 0; i < list.length; ++i) {
                            if (list[i].hero_id == heroId) {
                                list.splice(i, 1);
                                break;
                            }
                        }
                    }
                    if (this._maxPowerHero && heroId == this._maxPowerHero.hero_id) {
                        this._maxPowerHero = null;
                    }
                }
            }
        }

        if (updateType != EUpdateType.UPD_TYPE_DELETE) {
            this.updateHeroMap(updateHeroList, updateType);
        }
    }


    //新增武将
    private updateHeroByAdd(phero: PHero) {
        let oriMaxHero = this.toPHero(this.maxPowerHero);
        HeroDataCenter.instance._hero_list.push(phero);
        this.updateMaxPowerHero(phero, oriMaxHero);
        if (!this.isHadSoulHero) {
            this.isHadSoulHero = phero.ori_nation == HeroNation.NATION_HUN;
        }
    }

    // 更新存在的武将
    private updateHeroByExist(oriHero: PHero, phero: PHero) {
        let oriMaxHero = this.toPHero(this.maxPowerHero);
        let hi = HeroDataCenter.instance._hero_list.indexOf(oriHero);
        HeroDataCenter.instance._hero_list[hi] = phero;
        this.updateMaxPowerHero(phero, oriMaxHero);
    }

    public get hero_list(): PHero[] {
        return HeroDataCenter.instance._hero_list;
    }

    public set hero_list(heroes: PHero[]) {
        HeroDataCenter.instance._hero_list = heroes;
    }

    public get material_hero_list(): PHero[] {
        let materialList = [];
        let lineUpHeroList = [];
        for (let i = 0; i < this._hero_list.length; ++i) {
            let heroInfo = this._hero_list[i];
            if (heroInfo && LineUpDataCenter.instance.getHeroLineUpType(heroInfo.hero_id) > 0) {
                lineUpHeroList.push(heroInfo);
            } else {
                materialList.push(heroInfo);
            }
        }
        return materialList.concat(lineUpHeroList);
    }

    public updateHeroListInfo() {
        for (let i: number = 0; i < HeroDataCenter.instance._hero_list.length; i++) {
            let ori_hero = HeroDataCenter.instance._hero_list[i];
            this.toPHero(ori_hero, ori_hero);//更新数据
        }
    }

    private toPHero(fromHero: p_hero | PHero, toPHero?: PHero): PHero {
        if (!fromHero) {
            return new PHero();
        }

        let oriLevel = fromHero instanceof PHero ? fromHero.ori_level : fromHero.level;
        let newLevel = this.getHeroAddLevel(fromHero.hero_id, oriLevel);
        let heroCfg = ConfigManager.cfg_hero_baseCache.get(fromHero.type_id);
        //w10兼容
        if (!heroCfg) {
            return new PHero();
        }
        let oriNation = heroCfg.nation;
        let newNation = this.getHeroCurrNation(fromHero);
        if (!toPHero) {
            toPHero = new PHero();
        }
        ObjectUtil.cloneValues(fromHero, toPHero);
        toPHero.ori_level = oriLevel;
        toPHero.level = newLevel;
        toPHero.ori_nation = oriNation;
        toPHero.nation = newNation;
        return toPHero;
    }

    /**
     * 是否英雄觉醒
     */
    public static isCanAwaken(hero_id: number, star: number): boolean {
        //英雄升星
        let cfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(hero_id);

        if (cfg == null || star > cfg.max_star)
            return false;

        return star == MiscConst.hero_star_break_limit;
    }

    /**
     * 是否英雄觉醒
     */
    public static isHeroAwaken(star: number): boolean {
        return star > MiscConst.hero_star_break_limit;
    }

    public static getAwakenLevel(starLevel: number): number {
        let newLevel: number = 0;
        if (starLevel > HeroConsts.getShowMaxStar) {
            newLevel = starLevel % HeroConsts.getShowMaxStar;
            if (newLevel == 0) {
                newLevel = HeroConsts.getShowMaxStar;
            }
        } else {
            newLevel = starLevel;
        }
        return newLevel;
    }

    /**改变显示方式的星级 */
    public static get changeShowStar(): number {
        return 10;
    }

    /**
     * 获取阵容未养成英雄的列表
     * @param lineUpId
     * @returns
     */
    public GetLineUpInitialHero(lineUpId: number): number[] {
        let team: number[] = LineUpDataCenter.instance.getLineUpListByType(lineUpId);
        if (!team) {
            team = LineUpDataCenter.instance.getLineUpListByType(MatchConst.MATCH_TYPE_MAIN_BATTLE);
        }
        return this.GetLineUpListInitialHero(team);
    }

    /**
     * 判断是否有未养成的英雄
     * @param lineUpId
     * @returns true 有  false 没有
     */
    public GetLineUpListInitialHero(lineUpList: number[]): number[] {
        let backList: number[] = [];
        lineUpList.forEach((id) => {
            if (this.CheckHeroIsInitial(id)) {
                backList.push(id);
            }
        })
        return backList;
    }

    public CheckHeroIsInitial(id: number): boolean {
        let p: p_hero = HeroDataCenter.instance.getHero(id);
        if (p && p.stage == 0 && p.level == 1) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否有未养成的英雄
     * @param lineUpId
     * @returns true 有  false 没有
     */
    public CheckLineUpInitialHero(lineUpId: number): boolean {
        let backList: number[] = this.GetLineUpInitialHero(lineUpId);
        return backList.length > 0;
    }

    /**
     * 判断是否有未养成的英雄
     * @param lineUpId
     * @returns true 有  false 没有
     */
    public CheckLineUpListInitialHero(lineUpList: number[]): boolean {
        let backList: number[] = this.GetLineUpListInitialHero(lineUpList);
        return backList.length > 0;
    }


    /**-------------------------------------------进阶相关--------------------------------------------- */
    /**
     * 获取大阶位
     */
    public static getHeroBigStage(hero: p_hero): number {
        if (hero) {
            if (DudufuDataCenter.instance.checkHeroIsFiveHeroResonate(hero.hero_id)) {
                let phero: p_hero = HeroDataCenter.instance.getHero(hero.hero_id);
                let lv = phero.level
                if (lv < 30) {
                    return 0;
                } else if (lv > 30 && lv < 40) {
                    return 1;
                } else if (lv > 40 && lv < 50) {
                    return 2;
                } else if (lv > 50 && lv < 60) {
                    return 3;
                } else if (lv > 60 && lv < 70) {
                    return 4;
                } else if (lv > 80 && lv < 100) {
                    return 5;
                } else {
                    return 6;
                }
            }
            return Math.floor(hero.stage);
        }
        return 0;
    }

    /**
     * 获取阶位的中文描述
     */
    public static getChsStage(stage: number, format: string = window.iLang.L2_P0_JIE_P1_JI.il()): string {
        let bigStage: number = Math.floor(stage / 10);
        let smallStage: number = stage % 10;
        return StringUtil.Format(format, bigStage, smallStage);
    }

    /**-------------------------------------------升级相关--------------------------------------------- */
    /**
     * 英雄升级
     */
    public upgradeHero(heroId: number, upgradeType: number, newLevel: number) {
        //this.statistics(heroId, upgradeType, newLevel);
        //更新
        for (let j: number = 0; j < HeroDataCenter.instance._hero_list.length; j++) {
            let hero = HeroDataCenter.instance._hero_list[j];
            let theHeroId: number = hero.hero_id;
            if (theHeroId == heroId) {
                if (upgradeType == 1) {
                    hero.ori_level = newLevel
                    hero.level = this.getHeroAddLevel(heroId, newLevel);
                } else if (upgradeType == 2) {
                    hero.stage = newLevel;
                } else if (upgradeType == 3 || upgradeType == 5) {
                    hero.star = newLevel;
                    HeroDataCenter.checkIsHeroStarUpGrowthOpen([hero]);
                } else if (upgradeType == 4) {
                    hero.star_stage.push(newLevel);
                } else if (upgradeType == 8) {
                    hero.soul_level = newLevel;
                }
                break;
            }
        }
        this.CheckHeroInfoRedPoint();
        this.CheckListRedPoint();
        LineUpDataCenter.instance.CheckLineUpRedPoint();
        GeneralMansionDataCenter.instance.checkFusionAltarRedPoint();
    }

    //更新英雄的Map字典
    private updateHeroMap(heroes: PHero[], updateType: number, checkRedPoint: boolean = true) {
        for (let i: number = 0; i < heroes.length; i++) {
            let phero = heroes[i];
            let list = HeroDataCenter.instance._heroTypeListMap.get(phero.type_id);
            if (!list) {
                list = [];
                HeroDataCenter.instance._heroTypeListMap.set(phero.type_id, list);
            }
            let hi = list.findIndex(function (listHero) {
                return listHero.hero_id == phero.hero_id;
            });

            if (hi < 0) {
                hi = list.length;
            }

            if (updateType == 3) {
                this._heroMap.delete(phero.hero_id);
                list.splice(hi, 1);
            } else {
                this._heroMap.set(phero.hero_id, phero);
                list[hi] = phero;
            }
        }
        if (checkRedPoint) {
            DispatchManager.dispatchEvent(ModuleCommand.REFRESH_HERO_INFO);
            this.CheckHeroInfoRedPoint();
            this.CheckListRedPoint();
            LineUpDataCenter.instance.CheckLineUpRedPoint();
        }
    }

    /**根据英雄id获取英雄 */
    public getHero(id: number, is_ori: boolean = false): PHero {
        if (HeroDataCenter.instance._heroMap.has(id)) {
            return HeroDataCenter.instance._heroMap.get(id);
        }
        return null;
    }

    /**根据英雄type_id获取已有英雄 */
    public getHeroByTypeId(type_id: number): PHero {
        if (HeroDataCenter.instance._heroTypeListMap.has(type_id)) {
            let list = HeroDataCenter.instance._heroTypeListMap.get(type_id);
            return list ? list[0] : null;
        }
        return null;
    }

    /**根据英雄type_id获取已有英雄的最高星级 */
    public getHeroStarByTypeId(type_id: number): number {
        let maxStar = 0;
        if (HeroDataCenter.instance._heroTypeListMap.has(type_id)) {
            let list = HeroDataCenter.instance._heroTypeListMap.get(type_id);
            if (list) {
                for (let i = 0; i < list.length; ++i) {
                    let hero = list[i];
                    if (hero.limit_type == 0) {
                        maxStar = Math.max(maxStar, hero.star);
                    }
                }
            }
        }
        return maxStar;
    }

    /**根据英雄type_id获取英雄列表 */
    public getHeroListByTypeId(type_id: number): PHero[] {
        let res = [];
        if (HeroDataCenter.instance._heroTypeListMap.has(type_id)) {
            res.push(...HeroDataCenter.instance._heroTypeListMap.get(type_id));
        }
        return res;
    }

    /**根据英雄type_id获取最高等级的英雄 */
    public getTopLevelHeroByTypeId(type_id: number): p_hero[] {
        let heroList = [];
        heroList = HeroDataCenter.instance.hero_list.filter(p_hero => {
            return p_hero.type_id == type_id;
        });
        if (heroList.length > 0) {
            heroList.sort((ph1, ph2) => {
                return ph2.level - ph1.level;
            });
        }
        return heroList;
    }

    /**根据英雄type_id获取最高战力的英雄 */
    public getTopPowerHeroByTypeId(type_id: number, lineUpType: number = 0): p_hero[] {
        let heroList = [];
        heroList = HeroDataCenter.instance.hero_list.filter(p_hero => {
            if (p_hero.type_id == type_id) {
                if (lineUpType != 0) {
                    return LineUpDataCenter.instance.getHeroLineUpType(p_hero.hero_id) == lineUpType;
                }
                return true;
            }
            return false;
        });
        if (heroList.length > 0) {
            heroList.sort((ph1, ph2) => {
                return ph2.power - ph1.power;
            });
        }
        return heroList;
    }

    /**根据武将type_id获取最高战力的一个武将 */
    public getOneTopPowerHeroByTypeId(type_id: number): p_hero {
        let heroList: p_hero[] = [];
        if (HeroDataCenter.instance._heroTypeListMap.has(type_id)) {
            heroList = HeroDataCenter.instance._heroTypeListMap.get(type_id);
        }
        let topHero: p_hero = null;
        for (let heroInfo of heroList) {
            if (!topHero || heroInfo.power > topHero.power) {
                topHero = heroInfo;
            }
        }
        return topHero;
    }

    /**获取指定阵容战力最高的英雄 */
    public getMaxPowerHeroByLineupTyte(lineUpType: number): p_hero {
        let lineUp: number[] = LineUpDataCenter.instance.getLineUpListByType(lineUpType);
        let max: p_hero;
        let temp: p_hero;
        for (let id of lineUp) {
            temp = this.getHero(id);
            if (!temp) continue;
            if (!max) {
                max = temp;
            } else {
                if (temp.power > max.power) {
                    max = temp;
                }
            }
        }
        return max;
    }

    public get soulHeroLinkList(): p_kv[] {
        if (this.soulHeroLinkInfo) {
            return this.soulHeroLinkInfo.link_info;
        }
        return [];
    }

    public get soulHeroUnlockList(): p_kv[] {
        if (this.soulHeroLinkInfo) {
            return this.soulHeroLinkInfo.unlock_info;
        }
        return [];
    }

    /**获取被链魂的英雄 */
    public getBeLinkHero(hero_id: number): PHero {
        for (let kv of HeroDataCenter.instance.soulHeroLinkList) {
            if (kv.key == hero_id) {
                return this.getHero(kv.val);
            }
        }
        return null;
    }

    /**获取链魂的异能英雄 */
    public getLinkSoulHero(hero_id: number): PHero {
        for (let kv of HeroDataCenter.instance.soulHeroLinkList) {
            if (kv.val == hero_id) {
                return this.getHero(kv.key);
            }
        }
        return null;
    }

    /**获取与该英雄链魂的英雄Id */
    public getLinkHeroId(hero_id: number): number {
        for (let kv of HeroDataCenter.instance.soulHeroLinkList) {
            if (kv.key == hero_id) {
                return kv.val;
            }
            if (kv.val == hero_id) {
                return kv.key;
            }
        }
        return 0;
    }

    /**获取与该英雄链魂的英雄，返回的可能是异能英雄，也可能是非异能英雄 */
    public getLinkHero(hero_id: number): PHero {
        return this.getBeLinkHero(hero_id) || this.getLinkSoulHero(hero_id);
    }

    /**获取英雄当前阵营 */
    public getHeroCurrNation(hero_info: p_hero | p_hero_sm): number {
        if (!hero_info) {
            return -1;
        }
        let heroCfg = ConfigManager.cfg_hero_baseCache.get(hero_info.link_type_id);
        if (!heroCfg) {
            heroCfg = ConfigManager.cfg_hero_baseCache.get(hero_info.type_id);
        }
        return heroCfg.nation;
    }

    /**是否为异能英雄 */
    public checkIsSoulHero(hero_id: number): boolean {
        let heroInfo = this.getHero(hero_id);
        if (!heroInfo) return false;
        return heroInfo.ori_nation == HeroNation.NATION_HUN;
    }

    /**是否为异能英雄(通过p_hero) */
    public checkIsSoulHeroByInfo(heroInfo: p_hero): boolean {
        let heroCfg = CfgCacheMapMgr.cfg_hero_baseCache.get(heroInfo.type_id);
        return heroCfg.nation == HeroNation.NATION_HUN;
    }

    /**是否为异能英雄(通过type_id) */
    public checkIsSoulHeroByTypeId(type_id: number): boolean {
        let heroCfg = CfgCacheMapMgr.cfg_hero_baseCache.get(type_id);
        return heroCfg.nation == HeroNation.NATION_HUN;
    }

    /**是否已链魂 */
    public checkIsLinkHero(hero_id: number, tips: string = null): boolean {
        for (let kv of HeroDataCenter.instance.soulHeroLinkList) {
            if (kv.key == hero_id || kv.val == hero_id) {
                if (tips) {
                    TipsUtil.showTips(tips);
                }
                return true;
            }
        }
        return false;
    }

    /** 当前英雄是否为限定英雄,这个限定英雄是否已经升级 */
    public isLitmitHeroLevelUp(data: p_hero): boolean {
        let curLevel = data["ori_level"] || data.level;
        if (data.limit_type > 0 && curLevel > 1) {
            return true;
        }
        return false;
    }

    /**是否是被链魂英雄 */
    public checkIsBeLinkHero(hero_id: number, tips: string = null): boolean {
        for (let kv of HeroDataCenter.instance.soulHeroLinkList) {
            if (kv.val == hero_id) {
                if (tips) {
                    TipsUtil.showTips(tips);
                }
                return true;
            }
        }
        return false;
    }

    /**是否是已链魂异能英雄 */
    public checkIsLinkSoulHero(hero_id: number, tips: string = null): boolean {
        for (let kv of HeroDataCenter.instance.soulHeroLinkList) {
            if (kv.key == hero_id) {
                if (tips) {
                    TipsUtil.showTips(tips);
                }
                return true;
            }
        }
        return false;
    }

    /**是否解除异能英雄上阵限制(解锁同心限制) */
    public checkIsUnLockTongXinLimit(type_id: number): boolean {
        for (let kv of HeroDataCenter.instance.soulHeroUnlockList) {
            if (kv.key == type_id) {
                return true;
            }
        }
        return false;
    }

    private _maxPowerHero: PHero = null;
    public get maxPowerHero(): PHero {
        if (!this._maxPowerHero) {
            for (let i = 0; i < this.hero_list.length; ++i) {
                let heroInfo = this.hero_list[i];
                this.updateMaxPowerHero(heroInfo);
            }
        }
        return this._maxPowerHero;
    }

    private updateMaxPowerHero(phero: PHero, maxPowerHero?: PHero): void {
        if (!maxPowerHero) {
            maxPowerHero = this._maxPowerHero;
        }
        if (!maxPowerHero) {
            this._maxPowerHero = phero;
        } else if (phero && phero.hero_id == maxPowerHero.hero_id) {
            if (phero.power < maxPowerHero.power) {
                this._maxPowerHero = null;
            }
        } else if (phero && phero.power > maxPowerHero.power) {
            this._maxPowerHero = phero;
        }
    }

    /**获取指定阵容的英雄总战力 */
    public getLineUpTotalPower(lineUpType: number): number {
        let res: number = 0;
        let lineUp: number[] = LineUpDataCenter.instance.getLineUpListByType(lineUpType);
        if (lineUp.length <= 0) {
            lineUp = LineUpDataCenter.instance.getLineUpListByType(MatchConst.MATCH_TYPE_MAIN_BATTLE);
        }
        // let hero: p_hero;
        // for (let hero_id of lineUp) {
        //     hero = this.getHero(hero_id);
        //     if (hero) res = res + hero.power;
        // }
        return this.teamHeroPower(lineUp);
    }

    public teamHeroPower(heroIds: number[]): number {
        let res: number = 0;
        let hero: p_hero;
        for (let hero_id of heroIds) {
            hero = this.getHero(hero_id);
            if (hero) res = res + hero.power;
        }
        return res;
    }

    /**从上阵列表获取英雄  从1开始*/
    public getHeroByFightPos(index: number): p_hero {
        let list: any[] = LineUpDataCenter.instance.getLineUpListByType(MatchConst.MATCH_TYPE_MAIN_BATTLE);
        if (list.length >= index) {
            return HeroDataCenter.instance.getHero(list[index - 1]);
        } else {
            // TipsUtil.showTips("养成列表不存在该位置英雄：" + (index - 1).toString());
        }
        return null;
    }

    /**返回不是上阵、不是援军 */
    //当同一星位的英雄处于【布阵】当中时，该星位的主星位英雄不出现在回收列表中
    public getHeroAllBase(): p_hero[] {
        let arr: p_hero[] = [];
        let templeMainHeroList = [];
        for (let i: number = 0; i < HeroDataCenter.instance.hero_list.length; i++) {
            let hero: p_hero = HeroDataCenter.instance.hero_list[i];
            //当同一星位的英雄处于【布阵】当中时，该星位的主星位英雄不出现在回收列表中
            if (hero && templeMainHeroList.indexOf(hero.hero_id) >= 0) {
                continue;
            }
            if (HeroDataCenter.checkIsInTemple(hero) == false) { //不在占星台
                if (LineUpDataCenter.instance.getHeroLineUpType(hero.hero_id) == 0) {
                    // if (hero.stage > 0 || hero.level > 1 || hero.star > 7) {
                    arr.push(hero);
                    // }
                }
            } else {
                /*W7屏蔽
                   //在占星台共享位,且在阵容里面,就过滤所属的主将
                   if (hero && hero.temple_hero_id) {
                       if (HeroDataCenter.instance.getHeroLineUpType(hero.hero_id) != 0) {
                           templeMainHeroList.push(hero.temple_hero_id);
                       }
                   }
                   */
            }
        }
        return arr;
    }

    //秘法星级限制更改，已上阵的秘法不满足条件时触发
    public setLimitStar(hero: PHero): number {
        let star = 0;
        switch (HeroDataCenter.instance.bingfaPosIndex) {
            case 1:
                star = MiscConstAuto.bingfa_slot1_star_limit;
                break
            case 2:
                star = MiscConstAuto.bingfa_slot2_star_limit;
                break
            case 3:
                star = MiscConstAuto.bingfa_slot3_star_limit;
                break;
            default:
                break;
        }
        return star;
    }

    public isTabOpen(id: number) {
        let hero = HeroDataCenter.instance.getHero(HeroDataCenter.instance.select_hero_id);
        if (id === HeroDataCenter.TAB_BINGFA) {
            let isStarLimit = hero && hero.star >= MiscConstAuto.bingfa_slot1_star_limit;//秘法第一个槽位星级
            let content = window.iLang.L2_HERO_DA_DAO_P0_XING_KAI_QI.il([MiscConstAuto.bingfa_slot1_star_limit]);
            return {flag: isStarLimit, tipsText: content};
        }
        if (id === HeroDataCenter.TAB_GODEQUIP) {
            let isGodEquipOpen = GameUtil.isSysOpen(PanelEventConstants.CHALLENGE_CHAPTER, 0, false);
            let desc = GameUtil.getSysOpenDesc(PanelEventConstants.CHALLENGE_CHAPTER);
            return {flag: isGodEquipOpen, tipsText: desc};
        }
        return {flag: true, tipsText: null};
    }

    /**升星前英雄状态检测 */
    public checkHeroBeforeUpStar(materialDatas: HeroMaterialData[], callBackHandler: Handler): void {
        let tips: string[] = [];
        let allHeroNames: string[] = [];
        let heroNames: string[][] = new Array(4).fill([]);
        let heroIds = LineUpDataCenter.instance.getCurLineUpListByType(MatchConst.MATCH_TYPE_MAIN_BATTLE);
        let typeIds = heroIds.map(function (heroId) {
            let heroInfo = HeroDataCenter.instance.getHero(heroId);
            return heroInfo && heroInfo.type_id || 0;
        });
        for (let i = 0; i < materialDatas.length; ++i) {
            let data = materialDatas[i];
            if (data && !data.is_self) {
                for (let heroId of data.sel_ids) {
                    let heroInfo = HeroDataCenter.instance.getHero(heroId);
                    if (heroInfo) {
                        let heroCfg = ConfigManager.cfg_hero_baseCache.get(heroInfo.type_id);
                        if (heroInfo.star_stage.length > 0) {
                            if (!tips[1]) {
                                tips[1] = window.iLang.L2_DANG_QIAN_YI_YOU_SHENG_XING_JIE_ch31_SHENG_XING_XIAO_HAO_HOU_HUI_FAN_HUAN_DUI_YING.il();
                            }
                            if (allHeroNames.indexOf(heroCfg.name) < 0) {
                                allHeroNames.push(heroCfg.name);
                            }
                            if (heroNames[1].indexOf(heroCfg.name) < 0) {
                                heroNames[1].push(heroCfg.name);
                            }
                        }
                        if (data.type_id <= 0) {
                            if (typeIds.indexOf(heroInfo.type_id) >= 0) {
                                if (!tips[2]) {
                                    tips[2] = window.iLang.L2_WEI_ZHENG_ZHAN_ZHEN_RONG_YI_CHU_ZHAN_HERO_DE_BEN_TI_CAI_LIAO.il();
                                }
                                if (allHeroNames.indexOf(heroCfg.name) < 0) {
                                    allHeroNames.push(heroCfg.name);
                                }
                                if (heroNames[2].indexOf(heroCfg.name) < 0) {
                                    heroNames[2].push(heroCfg.name);
                                }
                            }
                            if (heroCfg.pillar != 0) {
                                if (!tips[0]) {
                                    tips[0] = window.iLang.L2_WEI_JIAO_NAN_HUO_DE_DE_JING_YING_JIANG.il();
                                }
                                if (allHeroNames.indexOf(heroCfg.name) < 0) {
                                    allHeroNames.push(heroCfg.name);
                                }
                                if (heroNames[0].indexOf(heroCfg.name) < 0) {
                                    heroNames[0].push(heroCfg.name);
                                }
                            }
                        }
                        if (this.checkIsActiveWing(heroId)) {
                            if (!tips[3]) {
                                tips[3] = window.iLang.L2_YI_JI_HUO_JING_LING_ch31_JI_XU_SHENG_XING_BU_FAN_HUAN_DAO_JU_ch23_JING_LING.il();
                            }
                            if (allHeroNames.indexOf(heroCfg.name) < 0) {
                                allHeroNames.push(heroCfg.name);
                            }
                            if (heroNames[3].indexOf(heroCfg.name) < 0) {
                                heroNames[3].push(heroCfg.name);
                            }
                        }
                    }
                }
            }
        }

        if (allHeroNames.length > 0) {
            let desc = window.i18n(window.iLang.L2_HERO_ch23_P0_ch24_DANG_QIAN_CHU_YU_RU_XIA_ZHUANG_TAI_ch31_SHI_FOU_YAO_JI_XU.il(), [allHeroNames.join("】【")]);
            for (let i = 0; i < tips.length; ++i) {
                let tip = tips[i];
                let names = heroNames[i];
                if (tip) {
                    desc += `<br>【${names.join("】【")}】` + tip;
                }
            }
            TipsUtil.showDialog(this, desc, "", function () {
                if (callBackHandler) {
                    callBackHandler.run();
                }
            }, {
                align: "left",
            });
        } else {
            if (callBackHandler) {
                callBackHandler.run();
            }
        }
    }

    public reqHeroUpdateStar(heroInfo: p_hero, materialDatas: HeroMaterialData[], useGreatPoint: boolean = false, upType: number = HeroDataCenter.STAR_UPDATE, upVal: number = 1, is_auto: boolean = false): void {
        if (!heroInfo || !heroInfo.hero_id) {
            TipsUtil.showTips(window.iLang.L2_MEI_YOU_HERO_SHU_JU.il());
            return;
        }

        let heroCfg = ConfigManager.cfg_hero_baseCache.get(heroInfo.type_id);
        if (heroInfo.star >= heroCfg.max_star) {
            TipsUtil.showTips(window.iLang.L2_YI_MAN_XING.il());
            return;
        }

        if (upType == HeroDataCenter.STAR_UPDATE) {
            if (!HeroDataCenter.instance.checkIsUpStarLimitMeet(heroInfo.type_id, heroInfo.star + 1, true)) {
                return;
            }
        }
        // else if (upType == HeroDataCenter.STAR_STAGE_UPDATE) {
        //     if (!HeroDataCenter.instance.checkIsUpStarStageLimitMeet(heroInfo.type_id, heroInfo.star + 1, upVal, true)) {
        //         return ;
        //     }
        // }

        let ids: number[] = [];
        let items: p_item[] = [];
        for (let i = 0; i < materialDatas.length; ++i) {
            let data = materialDatas[i];
            //用名将点替代守护灵
            if (useGreatPoint && data.is_vice) {
                if (!HeroDataCenter.instance.isCanUseGreatHeroPoint(data.type_id)) {
                    return;
                }
                let itemInfo = new p_item();
                itemInfo.type_id = ItemConst.COST_GREAT_HERO_POINT;
                itemInfo.num = HeroDataCenter.instance.getGreatHeroPointNum(data.type_id);
                items.push(itemInfo);
            } else if (!data.isEnoughNum()) {
                TipsUtil.showTips(window.iLang.L2_HERO_KA_PIAN_BU_ZU.il());
                return;
            } else {
                if (!data.is_self && data.sel_ids.length > 0) {
                    ids = ids.concat(data.sel_ids);
                }
                if (data.sel_goods.length > 0) {
                    for (let goodsInfo of data.sel_goods) {
                        let itemInfo = new p_item();
                        itemInfo.type_id = goodsInfo.type_id;
                        itemInfo.num = goodsInfo.num;
                        items.push(itemInfo);
                    }
                }
            }
        }

        HeroDataCenter.instance.m_hero_upgrade_tos(heroInfo.hero_id, upType, upVal, is_auto, ids, items);
    }

    /**获取武将可以升到的最高星级 */
    public getMaxStar(typeId: number): number {
        let heroCfg = ConfigManager.cfg_hero_baseCache.get(typeId);
        let maxStar = heroCfg ? heroCfg.max_star : 0;
        return maxStar;
    }

    /**根据世界等级、领主等级，获取英雄最高星级 */
    public getMaxStarByLimit(typeId: number, ingoreStar14: boolean = false): number {
        let heroCfg = ConfigManager.cfg_hero_baseCache.get(typeId);
        let initStar = heroCfg ? heroCfg.init_star : 0;
        let maxStar = heroCfg ? heroCfg.max_star : 0;
        let isMaxFinalStar = maxStar == HeroConsts.FINAL_STAR;
        maxStar = isMaxFinalStar && ingoreStar14 ? maxStar - 1 : maxStar;
        let curCfg = null;
        for (let star = initStar; star <= maxStar; ++star) {
            let cfgList = CfgCacheMapMgr.cfg_hero_star_limit_listCache.get(star);
            let limitCfg = cfgList ? cfgList[cfgList.length - 1] : null;
            if (limitCfg) {
                let isMeetLimit = DataCenter.world_level >= limitCfg.world_lv_limit || DudufuDataCenter.instance.five_level >= limitCfg.five_lv_limit;
                if (isMeetLimit && (!curCfg || limitCfg.star > curCfg.star)) {
                    curCfg = limitCfg;
                }
            }
        }

        return curCfg ? curCfg.star : maxStar;
    }

    /**根据世界等级、五虎将等级，获取英雄星阶 */
    public getMaxStarStageListByLimit(typeId: number, tarStar: number): number[] {
        let heroCfg = ConfigManager.cfg_hero_baseCache.get(typeId);
        let maxStar = heroCfg ? heroCfg.max_star : 0;
        if (tarStar > maxStar) {
            return [];
        }

        let cfgList = CfgCacheMapMgr.cfg_hero_star_limit_listCache.get(tarStar);
        let starStageList: number[] = [];
        for (let starCfg of cfgList) {
            if (DataCenter.world_level >= starCfg.world_lv_limit || DudufuDataCenter.instance.five_level >= starCfg.five_lv_limit) {
                starStageList.push(starCfg.star_stage);
            }
        }

        return starStageList;
    }

    /**检测是否满足升星的世界等级和领主等级要求 */
    public checkIsUpStarLimitMeet(typeId: number, tarStar: number, isTip: boolean = false): boolean {
        let starCfg = ConfigManager.getCfgHeroStar2(typeId, tarStar);
        if (starCfg) {
            if (DataCenter.world_level >= starCfg.world_level_limit || DataCenter.myLevel >= starCfg.level_limit) {
                return true;
            } else if (isTip) {
                TipsUtil.showTips(window.i18n(window.iLang.L2_LING_ZHU_DA_DAO_P0_JI_HUO_SHI_JIE_DENG_JI_P1_JI_KE_JI_XU_SHENG_XING.il(), [starCfg.level_limit, starCfg.world_level_limit]));
            }
        }

        return false;
    }

    /**检测是否满足星阶升星的世界等级和领主等级要求 */
    public checkIsUpStarStageLimitMeet(typeId: number, tarStar: number, tarStarStage: number, isTip: boolean = false): boolean {
        let starCfg = ConfigManager.getCfgHeroStarStage(typeId, tarStar, tarStarStage);
        if (starCfg) {
            if (DataCenter.world_level >= starCfg.world_level_limit || DudufuDataCenter.instance.five_level >= starCfg.level_limit) {
                return true;
            } else if (isTip) {
                TipsUtil.showTips(window.i18n(window.iLang.L2_GONG_MING_DENG_JI_DA_DAO_P0_JI_HUO_SHI_JIE_DENG_JI_P1_JI_KE_JI_XU.il(), [starCfg.level_limit, starCfg.world_level_limit]));
            }
        }

        return false;
    }

    /**获得一个满级英雄 */
    public getFullLevelHero(type_id: number): p_hero {
        let hero: p_hero = new p_hero();
        hero.hero_id = type_id;
        hero.type_id = type_id;
        hero.fight = new p_hero_fight();

        let cfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(type_id);
        if (cfg == null) {
            return hero;
        }

        //星级
        hero.star = this.getMaxStarByLimit(type_id);
        hero.star_stage = this.getMaxStarStageListByLimit(type_id, hero.star);
        hero.stage = ConfigManager.getHeroMaxUpgradeStage(hero.star);
        hero.level = ConfigManager.GetHeroMaxUpgradeLevel(hero.star, hero.stage, hero.star_stage);
        hero.fight = HeroUtil.getHeroFightAttr(type_id, {
            level: hero.level,
            stage: hero.stage,
            star: hero.star,
            starStage: hero.star_stage,
            withSkill: true
        });

        //英雄战斗力
        hero.power = HeroUtil.calcPower(hero.fight);
        return hero;
    }

    /**根据英雄配置ID获取名将点数量 */
    public getGreatHeroPointNum(typeId: number): number {
        let heroCfg = ConfigManager.cfg_hero_baseCache.get(typeId);
        if (!heroCfg) {
            return 0;
        }

        switch (heroCfg.nation) {
            case DataCenter.NATION_WEI:
            case DataCenter.NATION_SHU:
            case DataCenter.NATION_WU:
                if (heroCfg.init_star == 4) {
                    return MiscConst.hero_great_point_cost_normal_4;
                } else if (heroCfg.init_star == 5) {
                    return MiscConst.hero_great_point_cost_normal_5;
                }
            case DataCenter.NATION_SHEN:
            case DataCenter.NATION_MO:
                if (heroCfg.init_star == 4) {
                    return MiscConst.hero_great_point_cost_special_4;
                } else if (heroCfg.init_star == 5) {
                    return MiscConst.hero_great_point_cost_special_5;
                }
        }
        return 0;
    }

    /**获取所有英魂卡 */
    public getHeroUpStarMaterialCards(): p_goods[] {
        let cardCfgs = ConfigManager.cfg_itemKindCache.get(ItemMacro.ITEM_KIND_HERO_SOUL);
        cardCfgs.sort(function (itemCfg1, itemCfg2) {
            let order1 = itemCfg1.nation == 0 ? 100 : itemCfg1.nation;
            let order2 = itemCfg2.nation == 0 ? 100 : itemCfg2.nation;

            return order1 - order2 || itemCfg1.star - itemCfg2.star;
        });

        let id = 1;
        let goodsList: p_goods[] = [];
        for (let itemCfg of cardCfgs) {
            let itemId = itemCfg.type_id;
            let goodsNum = GoodsManager.instance.GetGoodsNumByTypeId(itemId);
            for (let num = 0; num < goodsNum; ++num) {
                let goodsInfo = new p_goods();
                goodsInfo.id = id++;
                goodsInfo.type_id = itemId;
                goodsInfo.num = 1;
                goodsList.push(goodsInfo);
            }
        }
        return goodsList;
    }

    /**获取英雄升星材料的替代英魂卡 */
    public getMaterialGoodsInfoList(materialData: HeroMaterialData, other_items: p_goods[]): p_goods[] {
        let type_id = materialData.type_id;
        let is_self = materialData.is_self;

        if (type_id > 0 || is_self) {
            return [];
        }

        let allCardGoods = this.getHeroUpStarMaterialCards();
        allCardGoods = allCardGoods.filter(function (goods) {
            let item_cfg = ConfigManager.cfg_itemCache.get(goods.type_id);
            return materialData.isConform(0, item_cfg.nation, item_cfg.star, 0, goods);
        });

        let goodsList: p_goods[] = [];
        for (let goodInfo of allCardGoods) {
            let selGoods = other_items.find(function (goods) {
                return goods.id == goodInfo.id;
            });
            if (!selGoods) {
                goodsList.push(goodInfo);
            }
        }
        return goodsList;
    }

    /**获取英雄升星材料英雄列表 */
    public getMaterialHeroList(materialData: HeroMaterialData, other_ids: number[], isContainLockHero: boolean = false, isUseLimitType: boolean = false): p_hero[] {
        let is_self = materialData.is_self;

        let _this = this;
        let hero_list: p_hero[] = [];
        let lineup_heros: p_hero[] = [];
        let lock_heros: p_hero[] = [];

        this.hero_list.forEach(function (hero_info) {
            //已选择用作其他材料的英雄不会出现在列表里
            if (other_ids && other_ids.indexOf(hero_info.hero_id) >= 0) {
                return;
            }

            if (isUseLimitType == false && hero_info.limit_type > 0) {
                return;
            }

            //检测英雄是否符合条件
            let hero_cfg = ConfigManager.cfg_hero_baseCache.get(hero_info.type_id);
            if (!materialData.isConform(hero_info.type_id, hero_cfg.nation, hero_info.star, hero_info.hero_id)) {
                return;
            }

            //上阵英雄排到后面
            if (LineUpDataCenter.instance.getHeroLineUpType(hero_info.hero_id) > 0) {
                lineup_heros.push(hero_info);
                return;
            }

            //不包含锁定的英雄
            if (!is_self && !_this.isMaterialHeroCanSelect(hero_info)) {
                if (isContainLockHero) {
                    lock_heros.push(hero_info);
                }
                return;
            }

            hero_list.push(hero_info);
        });

        return hero_list.sort((hero1, hero2) => {
            return hero1.type_id - hero2.type_id;
        }).concat(lineup_heros).concat(lock_heros);
    }

    /**
     *
     * @param typeId
     * @param tarStar
     * @returns
     */
    public getMaterialDatas(typeId: number, tarStar: number, {
        tarStarStage = 0,
        heroId = 0,
        addSelf = false,
        excludeNations = [],
    } = {}): HeroMaterialData[] {
        let materialDataList: HeroMaterialData[] = [];
        let starCfg: cfg_hero_star | cfg_hero_star_stage = null;
        if (tarStar > HeroConsts.openStarStageLV) {
            starCfg = ConfigManager.getCfgHeroStarStage(typeId, tarStar, tarStarStage);
        } else {
            starCfg = ConfigManager.getCfgHeroStar2(typeId, tarStar);
        }
        if (starCfg) {
            let index = 0;
            //获取材料数据
            if (addSelf) {
                let materialData = HeroMaterialData.create(typeId, 0, {star: tarStar - 1, num: 1, is_self: true});
                materialData.index = index++;
                materialDataList.push(materialData);
            }

            let materialDatas = GameUtil.parseCfgByField(starCfg, "cost_type_id_", "cost_nation_", "cost_star_", "cost_num_");
            for (let i = 0; i < materialDatas.length; ++i) {
                let values = materialDatas[i]
                let type_id = values[0];
                let nation = values[1];
                let star = values[2];
                let num = values[3];

                let materialData: HeroMaterialData = null;
                if (num > 0) {
                    materialData = HeroMaterialData.create(type_id, nation, {
                        hero_id: heroId,
                        star: star,
                        num: num,
                        excludeNations: excludeNations
                    });
                    materialData.is_vice = tarStar == 6 && i == 1 && type_id > 0;
                    materialData.index = index++;
                    if (!type_id && !nation) {
                        materialData.addExcludeNation(...excludeNations);
                    }
                    materialDataList.push(materialData);
                }
            }

        }
        return materialDataList;
    }

    public autoSelMaterials(materialDataList: HeroMaterialData[], {
        isAutoSel = true,
        isAllAutoSel = false,
        autoSelCond = 1,
        ignoreLineUpTypes = [],
    } = {}): HeroMaterialData[] {
        if (!isAutoSel && !isAllAutoSel) {
            return materialDataList;
        }

        let autoSelDataList: HeroMaterialData[] = [];
        if (isAllAutoSel) {
            autoSelDataList = materialDataList;
        } else {
            for (let j = 0; j < materialDataList.length; ++j) {
                let data = materialDataList[j];
                let isAutoSelByTypeId = (autoSelCond & 1) && data.type_id > 0;
                let isAutoSelByStar3 = (autoSelCond & 2) && data.star == 3;

                if (isAutoSelByTypeId || isAutoSelByStar3) {
                    autoSelDataList.push(data);
                }
            }
        }

        let self_hero = null;
        let sel_hero_list = [];
        let hero_list = this.material_hero_list;
        for (let i = 0; i < hero_list.length; ++i) {
            let hero_info = hero_list[i];
            let cfg = ConfigManager.cfg_hero_baseCache.get(hero_info.type_id);
            let isCanSel = this.isMaterialHeroCanSelect(hero_info);
            let lineUpType = LineUpDataCenter.instance.getHeroLineUpType(hero_info.hero_id);
            isCanSel = isCanSel && ignoreLineUpTypes && ignoreLineUpTypes.indexOf(lineUpType) < 0;

            if (cfg) {
                for (let j = 0; j < autoSelDataList.length; ++j) {
                    let data = autoSelDataList[j];
                    if (data.isConform(hero_info.type_id, cfg.nation, hero_info.star, hero_info.hero_id)) {
                        if (data.is_self) {//英雄本身，选择等级最高的
                            if (data.sel_ids.length < data.num) {
                                self_hero = hero_info;
                                data.sel_ids[0] = self_hero.hero_id;
                                break;
                            } else {
                                let order1 = (!this.isMaterialHeroCanSelect(self_hero) ? 10000 : 0) + self_hero.level;
                                let order2 = (!isCanSel ? 10000 : 0) + hero_info.level;

                                if (order2 > order1) {
                                    let temp_hero = self_hero;
                                    self_hero = hero_info;
                                    hero_info = temp_hero;
                                    data.sel_ids[0] = self_hero.hero_id;
                                }
                            }
                        } else if (data.is_vice && isCanSel) {//副英雄，选择等级最低的
                            if (data.sel_ids.length < data.num) {
                                data.sel_ids.push(hero_info.hero_id);
                                sel_hero_list.push(hero_info);
                                break;
                            } else {
                                for (let k = 0; k < sel_hero_list.length; ++k) {
                                    let info = sel_hero_list[k];
                                    if (hero_info.level < info.level) {
                                        sel_hero_list[k] = hero_info;
                                        data.sel_ids[k] = hero_info.hero_id;
                                        hero_info = info;
                                    }
                                }
                            }
                        } else if (data.sel_ids.length < data.num && isCanSel) {
                            //锁定的英雄或者出战英雄不会被自动选中
                            data.sel_ids.push(hero_info.hero_id);
                            break;
                        }
                    }
                }
            } else {
                SentryUtils.sendWarningMsg("--------HeroDataCenter.autoSelMaterials cfg == null, type_id = " + hero_info.type_id);
            }
        }
        let cards = this.getHeroUpStarMaterialCards();
        for (let i = 0; i < cards.length; ++i) {
            let goodsInfo = cards[i];
            let item_cfg = ConfigManager.cfg_itemCache.get(goodsInfo.type_id);
            for (let j = 0; j < autoSelDataList.length; ++j) {
                let data = autoSelDataList[j];
                autoSelDataList[j].index = j;
                if (data.type_id > 0 || data.is_self || data.selNum >= data.num) {
                    continue;
                }
                if (data.isConform(0, item_cfg.nation, item_cfg.star)) {
                    data.sel_goods.push(goodsInfo);
                    break;
                }
            }
        }
        return materialDataList;
    }

    /**
     * 小于等于5星，按照hero_base的排序id优先级，从小到大放入
     * 大于5星的，狗粮的放入优先级是：最高5星的合成英雄》同系的英魂》全系英魂》非核心英雄 ；按照hero_base的排序id优先级，从小到大放入
     * @returns
     */
    private getOneKeySelMaterialList(curStar: number) {
        let materialList = [];
        let heroList = this._hero_list;
        heroList.sort(function (a: PHero, b: PHero) {
            let cfgA = ConfigManager.cfg_hero_baseCache.get(a.type_id);
            let cfgB = ConfigManager.cfg_hero_baseCache.get(b.type_id);
            return cfgA.sort_id - cfgB.sort_id;
        });
        if (curStar == 4) {
            for (let i = 0; i < heroList.length; i++) {
                let heroInfo = heroList[i];
                if (heroInfo && LineUpDataCenter.instance.getHeroLineUpType(heroInfo.hero_id) == 0) {
                    let cfg = ConfigManager.cfg_hero_baseCache.get(heroInfo.type_id);
                    // if(cfg.pillar == 1){
                    //     continue;
                    // }
                    materialList.push(heroInfo);
                }
            }
        } else if (curStar == 5) {
            let maxFiveStarHeroList: any[] = [];
            let otherHeroList: any[] = [];
            for (let i = 0; i < heroList.length; i++) {
                let heroInfo = heroList[i];
                let cfg = ConfigManager.cfg_hero_baseCache.get(heroInfo.type_id);
                if (cfg.max_star == 5) {
                    maxFiveStarHeroList.push(heroInfo);
                    continue;
                } //else if(cfg.pillar != 1){
                otherHeroList.push(heroInfo);
                //}
            }
            let cards = this.getHeroUpStarMaterialCards();
            materialList = maxFiveStarHeroList.concat(cards).concat(otherHeroList);
        }

        return materialList;
    }

    public oneKeySelMaterials(materialDataList: HeroMaterialData[], {
        isAutoSel = true,
        isAllAutoSel = false,
        ignoreLineUpTypes = [],
        excludeIds = [],// 新增排除参数
        excludeGoods = [],
    } = {}): HeroMaterialData[] {
        if (!isAutoSel && !isAllAutoSel) {
            return materialDataList;
        }
        let autoSelDataList = materialDataList;
        let self_hero = null;
        let sel_hero_list = [];
        let materialList = this.getOneKeySelMaterialList(materialDataList[0].star);
        for (let material of materialList.values()) {
            if (material) {
                if (material instanceof PHero) {
                    let hero_info = material;
                    let cfg = ConfigManager.cfg_hero_baseCache.get(hero_info.type_id);
                    let isCanSel = this.isMaterialHeroCanSelect(hero_info);
                    let lineUpType = LineUpDataCenter.instance.getHeroLineUpType(hero_info.hero_id);
                    isCanSel = isCanSel && ignoreLineUpTypes && ignoreLineUpTypes.indexOf(lineUpType) < 0;
                    // 新增排除检查
                    let excludeIdList: number[] = excludeIds;
                    if (excludeIdList.indexOf(hero_info.hero_id) !== -1) {
                        continue;
                    }
                    if (cfg) {
                        for (let j = 0; j < autoSelDataList.length; ++j) {
                            let data = autoSelDataList[j];
                            if (data.selNum >= data.num) {
                                continue;
                            }
                            if (data.isConform(hero_info.type_id, cfg.nation, hero_info.star, hero_info.hero_id)) {
                                if (data.is_self) {//英雄本身，选择等级最高的
                                    if (data.sel_ids.length < data.num) {
                                        self_hero = hero_info;
                                        data.sel_ids[0] = self_hero.hero_id;
                                        break;
                                    } else {
                                        let order1 = (!this.isMaterialHeroCanSelect(self_hero) ? 10000 : 0) + self_hero.level;
                                        let order2 = (!isCanSel ? 10000 : 0) + hero_info.level;

                                        if (order2 > order1) {
                                            let temp_hero = self_hero;
                                            self_hero = hero_info;
                                            hero_info = temp_hero;
                                            data.sel_ids[0] = self_hero.hero_id;
                                        }
                                    }
                                } else if (data.is_vice && isCanSel) {//副英雄，选择等级最低的
                                    if (data.sel_ids.length < data.num) {
                                        data.sel_ids.push(hero_info.hero_id);
                                        sel_hero_list.push(hero_info);
                                        break;
                                    } else {
                                        for (let k = 0; k < sel_hero_list.length; ++k) {
                                            let info = sel_hero_list[k];
                                            if (hero_info.level < info.level) {
                                                sel_hero_list[k] = hero_info;
                                                data.sel_ids[k] = hero_info.hero_id;
                                                hero_info = info;
                                            }
                                        }
                                    }
                                } else if (data.sel_ids.length < data.num && isCanSel) {
                                    //锁定的英雄或者出战英雄不会被自动选中
                                    if (data.type_id == cfg.type_id) {
                                        // 英雄本身可以选择核心将
                                        data.sel_ids.push(hero_info.hero_id);
                                    } else {
                                        // 其他英雄不能选择核心将
                                        if (cfg.pillar === 0) {
                                            data.sel_ids.push(hero_info.hero_id);
                                        }
                                    }
                                    break;
                                }
                            }
                        }
                    } else {
                        SentryUtils.sendWarningMsg("--------HeroDataCenter.oneKeySelMaterials cfg == null, type_id = " + hero_info.type_id);
                    }
                } else if (material instanceof p_goods) {
                    let goodsInfo = material;
                    let item_cfg = ConfigManager.cfg_itemCache.get(goodsInfo.type_id);
                    let excludeGoodsList: p_goods[] = excludeGoods;
                    if (excludeGoodsList.some(goods => goods.type_id == goodsInfo.type_id && goods.id == goodsInfo.id)) {
                        continue;
                    }
                    for (let j = 0; j < autoSelDataList.length; ++j) {
                        let data = autoSelDataList[j];
                        autoSelDataList[j].index = j;
                        if (data.type_id > 0 || data.is_self || data.selNum >= data.num) {
                            continue;
                        }
                        if (data.isConform(0, item_cfg.nation, item_cfg.star)) {
                            data.sel_goods.push(goodsInfo);
                            break;
                        }
                    }
                }
            }
        }
        return materialDataList;
    }

    public getHeroWingLv(hero_id: number): number {
        let equipVo: HeroEquipVo = HeroEquipManager.instance.getHeroEquipVo(hero_id);
        if (equipVo) {
            let equipData = equipVo.getEquipByKind(ItemMacro.ITEM_KIND_WING);
            return EquipUtil.getEquipExtVal(equipData, EquipExtKey.WING_LEVEL);
        }
        return 0;
    }

    public getNationOrder(nation: number): number {
        switch (nation) {
            case HeroNation.NATION_HUN:
                return 6;
            case HeroNation.NATION_SHEN:
                return 5;
            case HeroNation.NATION_MO:
                return 4;
            case HeroNation.NATION_WEI:
                return 3;
            case HeroNation.NATION_SHU:
                return 2;
            case HeroNation.NATION_WU:
                return 1;
            default:
                return 0;
        }
    }

    public sortHeroId(heroId1: number, heroId2: number): number {
        let heroInfo1 = this.getHero(heroId1);
        let heroInfo2 = this.getHero(heroId2);
        return HeroDataCenter.instance.sortHero2(heroInfo1, heroInfo2);
    }

    public SortHero(vo1: p_hero, vo2: p_hero): number {
        let order1 = 99, order2 = 99;
        let matchInfo = LineUpDataCenter.instance.getMatchLineUpInfoByType(MatchConst.MATCH_TYPE_MAIN_BATTLE);
        for (let multiInfo of matchInfo.list) {
            if (order1 == 99 && multiInfo.hero_id_list.indexOf(vo1.hero_id) >= 0) {
                order1 = multiInfo.index;
            }
            if (order2 == 99 && multiInfo.hero_id_list.indexOf(vo2.hero_id) >= 0) {
                order2 = multiInfo.index;
            }
            if (order1 != 99 && order2 != 99) {
                break;
            }
        }
        return order1 - order2 || HeroDataCenter.instance.sortHero2(vo1, vo2);
    }

    public sortHero2(vo1: p_hero, vo2: p_hero): number {
        let cfg1: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(vo1.type_id);
        let cfg2: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(vo2.type_id);

        //星级 由大到小
        if (vo1.star != vo2.star) {
            return vo2.star - vo1.star;
        }
        //等级 由大到小
        if (vo1.level != vo2.level) {
            return vo2.level - vo1.level;
        }
        if (cfg1 && cfg2) {
            // 排核心
            if (cfg1.pillar !== 0 && cfg2.pillar === 0) return -1;
            if (cfg1.pillar === 0 && cfg2.pillar !== 0) return 1;
        }

        //国家 源神魔冰森炎
        let nation1 = HeroDataCenter.instance.getHeroCurrNation(vo1);
        let nation2 = HeroDataCenter.instance.getHeroCurrNation(vo2);
        let order1 = HeroDataCenter.instance.getNationOrder(nation1);
        let order2 = HeroDataCenter.instance.getNationOrder(nation2);
        if (order1 != order2) {
            return order2 - order1;
        }
        if (cfg1 && cfg2) {
            //按 sort_id 从大到小排序
            if (cfg2.sort_id !== cfg1.sort_id) {
                return cfg2.sort_id - cfg1.sort_id;
            }
        }
        //异能英雄阵营排序
        if (nation1 == HeroNation.NATION_HUN && nation2 == HeroNation.NATION_HUN) {
            if (vo1.link_type_id == 0 && vo2.link_type_id == 0) {
                //能级 由大到小
                if (vo1.soul_level != vo2.soul_level) {
                    return vo2.soul_level - vo1.soul_level;
                }
            }
        }
        //战力 由大到小
        if (vo1.power != vo2.power) {
            return vo2.power - vo1.power;
        }
        //id 由小到大
        if (vo1.type_id != vo2.type_id) {
            return vo1.type_id - vo2.type_id;
        }
    }

    public sortHero3(vo1: p_hero, vo2: p_hero): number {
        //星级 由大到小
        if (vo1.star != vo2.star) {
            return vo2.star - vo1.star;
        }
        //等级 由大到小
        if (vo1.level != vo2.level) {
            return vo2.level - vo1.level;
        }

        //战力 由大到小
        if (vo1.power != vo2.power) {
            return vo2.power - vo1.power;
        }
        //id 由小到大
        if (vo1.type_id != vo2.type_id) {
            return vo1.type_id - vo2.type_id;
        }
    }

    public sortHeroCfg(cfg1: cfg_hero_base, cfg2: cfg_hero_base): number {
        //星级 由大到小
        if (cfg1.init_star != cfg2.init_star) {
            return cfg2.init_star - cfg1.init_star;
        }

        //国家 源神魔冰森炎
        let order1 = HeroDataCenter.instance.getNationOrder(cfg1.nation);
        let order2 = HeroDataCenter.instance.getNationOrder(cfg2.nation);
        if (cfg1.nation != cfg2.nation) {
            return order2 - order1;
        }

        //id 由小到大
        if (cfg1.type_id != cfg2.type_id) {
            return cfg1.type_id - cfg2.type_id;
        }
    }

    public sortStarHeroMaterial(hero1: p_hero, hero2: p_hero): number {
        let order1 = LineUpDataCenter.instance.getHeroLineUpType(hero1.hero_id) > 0 ? 1 : 0;
        let order2 = LineUpDataCenter.instance.getHeroLineUpType(hero2.hero_id) > 0 ? 1 : 0;
        if (order1 != order2) {
            return order1 - order2;
        }
        return HeroDataCenter.instance.sortHero2(hero1, hero2);
    }

    /**根据是否被链魂排序 */
    public sortByLinkSoulHero(hero1: p_hero, hero2: p_hero): number {
        let order1 = HeroDataCenter.instance.checkIsBeLinkHero(hero1.hero_id) ? 1 : 0;
        let order2 = HeroDataCenter.instance.checkIsBeLinkHero(hero2.hero_id) ? 1 : 0;
        return order1 - order2;
    }

    /**根据是否被链魂排序 */
    public sortByLockHero(hero1: p_hero, hero2: p_hero): number {
        let order1 = HeroDataCenter.instance.isHeroLock(hero1.hero_id) ? 1 : 0;
        let order2 = HeroDataCenter.instance.isHeroLock(hero2.hero_id) ? 1 : 0;
        return order1 - order2;
    }

    // public GetHeroQuality(hero: p_hero): number {
    //     let ret = EHeroColor.QUALITY_PURPLE;
    //     if (hero) {
    //         ret = HeroDataCenter.getColorByHero(hero);
    //     }
    //     return ret;
    // }
    /**----------------------------------红点相关--------------------------------------- */

    /**
     * 材料消耗是否满足
     * @param cost
     */
    public isCheckMaterials(item_id: number, item_num: number): boolean {
        if (item_id > 0) {
            let ownNum2: number = GoodsManager.instance.GetGoodsNumByTypeId(item_id);
            return item_num <= ownNum2;
        }
        return true;
    }

    private exclude(typeid: number, cost_chip: string): boolean {
        if (cost_chip.length > 1) {
            let vo: XmlFormatVo = XmlFormatVo.GetVo(cost_chip);
            if (vo.id == typeid) {
                return false;
            }
        }
        return true;
    }

    /**是否有升星功能 */
    public isHasStarUpdate(type_id: number, star: number): boolean {
        let starCfg: cfg_hero_star = ConfigManager.getCfgHeroStar2(type_id, star);
        if (starCfg == null) {
            return false;
        }
        return true;
    }

    public isCheckHeroMaterials(type_id: number, star: number): boolean {
        return false;
    }

    /**是否可升星 */
    public IsCanStarUpdate(typeId: number, curStar: number = 0, heroId: number = 0, isIgnore: boolean = true): boolean {
        if (GameUtil.isSysOpen(PanelEventConstants.HERO_INFO, HeroDataCenter.TAB_STAR, false) == false) {
            return false;
        }

        let cfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(typeId);
        let tarStar = curStar <= 0 ? cfg.init_star + 1 : curStar + 1;
        if (cfg == null || curStar >= cfg.max_star) {
            return false;
        }

        if (!HeroDataCenter.instance.checkIsUpStarLimitMeet(typeId, tarStar)) {
            return false;
        }

        //英雄升星
        let starCfg: cfg_hero_star = ConfigManager.getCfgHeroStar2(typeId, tarStar);
        if (starCfg == null) {
            return false;
        }

        //道具材料判断
        for (let i = 1; i <= 5; ++i) {
            let item_id = starCfg["cost_item_id_" + i];
            let item_num = starCfg["cost_item_num_" + i];
            if (!this.isCheckMaterials(item_id, item_num)) {
                return false;
            }
        }

        //获取材料数据
        let materialDatas = this.getMaterialDatas(typeId, tarStar, {
            heroId: heroId,
            excludeNations: [HeroNation.NATION_HUN]
        });
        //策划需求：征战阵容里的英雄，不会列入到升星红点判断
        let ignoreList = isIgnore ? [MatchConst.MATCH_TYPE_MAIN_BATTLE] : [];
        this.autoSelMaterials(materialDatas, {isAllAutoSel: true, ignoreLineUpTypes: ignoreList});
        for (let data of materialDatas) {
            if (!data.isEnoughNum()) {
                return false;
            }
        }

        return true;
    }

    /**是否可直升 */
    public IsCanStarUpGtow(typeId: number, _hero_info_star: number, curStar: number = 0, heroId: number = 0, isIgnore: boolean = true): boolean {
        if (GameUtil.isSysOpen(PanelEventConstants.HERO_INFO, HeroDataCenter.TAB_STAR, false) == false) {
            return false;
        }

        let cfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(typeId);
        let tarStar = curStar <= 0 ? cfg.init_star + 1 : curStar + 1;
        if (cfg == null || curStar >= cfg.max_star) {
            return false;
        }

        if (!HeroDataCenter.instance.checkIsUpStarLimitMeet(typeId, tarStar)) {
            return false;
        }

        //英雄升星
        let starCfg: cfg_hero_star[] = [];
        for (let i = _hero_info_star; i < curStar; i++) {
            starCfg.push(ConfigManager.getCfgHeroStar2(typeId, i + 1));
        }
        if (starCfg.length == 0) {
            return false;
        }

        //道具材料判断
        for (let j = 0; j < starCfg.length; j++) {
            for (let i = 1; i <= 5; ++i) {
                let item_id = starCfg[j]["cost_item_id_" + i];
                let item_num = starCfg[j]["cost_item_num_" + i];
                if (!this.isCheckMaterials(item_id, item_num)) {
                    return false;
                }
            }
        }


        //获取材料数据
        for (let i = _hero_info_star; i < curStar; i++) {
            let materialDatas = this.getMaterialDatas(typeId, i + 1, {
                heroId: heroId,
                excludeNations: [HeroNation.NATION_HUN]
            });
            //策划需求：征战阵容里的英雄，不会列入到升星红点判断
            let ignoreList = isIgnore ? [MatchConst.MATCH_TYPE_MAIN_BATTLE] : [];
            this.autoSelMaterials(materialDatas, {isAllAutoSel: true, ignoreLineUpTypes: ignoreList});
            // for (let data of materialDatas) {
            //     if (!data.isEnoughNum()) {
            //         return false;
            //     }
            // }
            for (let i = 1; i < materialDatas.length; i++) {
                if (!materialDatas[i].isEnoughNum()) {
                    return false;
                }
            }
        }


        return true;
    }

    /**检测英雄是否有星阶可升星或者可以激活终极升星 */
    public isCanStarStageUpdate(typeId: number, curStar: number, curStarStage: number[], heroId: number = 0, hero: p_hero): boolean {
        let tarStar = curStar + 1;
        if (tarStar == HeroConsts.FINAL_STAR && !this.checkIsActiveUpStar14(heroId)) {
            return this.checkIsCanActiveUpStar14(heroId) && this.checkIsEnoughActiveUpStar14(typeId);
        }
        let cfgList = ConfigManager.getCfgHeroStarStageList(typeId, curStar + 1);
        if (cfgList && cfgList.length > 0) {
            for (let i = 0; i < cfgList.length; ++i) {
                let cfg = cfgList[i];
                if (cfg && curStarStage.indexOf(cfg.star_stage) < 0 && this.isCanStarStageSingleUpdate(typeId, curStar, cfg.star_stage, heroId, true, hero)) {
                    return true
                }
            }
        }
        return false;
    }

    /**检测英雄单个星阶是否可升星 */
    public isCanStarStageSingleUpdate(typeId: number, curStar: number, tarStarStage: number, heroId: number = 0, isIgnore: boolean = true, hero: p_hero = null): boolean {
        if (GameUtil.isSysOpen(PanelEventConstants.HERO_INFO, HeroDataCenter.TAB_STAR, false) == false) {
            return false;
        }

        //检测是否已满星
        let cfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(typeId);
        let tarStar = curStar <= 0 ? cfg.init_star + 1 : curStar + 1;
        if (cfg == null || curStar >= cfg.max_star) {
            return false;
        }

        //检测世界等级和玩家等级
        if (!HeroDataCenter.instance.checkIsUpStarStageLimitMeet(typeId, tarStar, tarStarStage)) {
            return false;
        }

        //获取星阶配置
        let starCfg = ConfigManager.getCfgHeroStarStage(typeId, tarStar, tarStarStage);
        if (starCfg == null) {
            return false;
        }

        //道具材料判断
        for (let i = 1; i <= 5; ++i) {
            let item_id = starCfg["cost_item_id_" + i];
            let item_num = starCfg["cost_item_num_" + i];
            if (!this.isCheckMaterials(item_id, item_num)) {
                return false;
            }
        }

        //获取材料数据
        let materialDatas = this.getMaterialDatas(typeId, tarStar, {
            tarStarStage: tarStarStage,
            heroId: heroId,
            excludeNations: [HeroNation.NATION_HUN]
        });
        //策划需求：征战阵容里的英雄，不会列入到升星红点判断
        let ignoreList = isIgnore ? [MatchConst.MATCH_TYPE_MAIN_BATTLE] : [];
        this.autoSelMaterials(materialDatas, {isAllAutoSel: true, ignoreLineUpTypes: ignoreList});
        for (let data of materialDatas) {
            if (!data.isEnoughNum()) {
                return false;
            }
        }

        //限定英雄不能升星
        if (hero && hero.limit_type > 0) {
            return false;
        }

        return true;
    }

    /**是否可进阶 */
    public IsCanStageUpdate(hero: p_hero): boolean {
        if (GameUtil.isSysOpen(PanelEventConstants.HERO_INFO, HeroDataCenter.TAB_STAGE, false) == false) {
            return false;
        }

        if (!hero) {
            return false;
        }

        let cfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(hero.type_id);
        if (!cfg) {
            console.log("not find cfg_hero_base ", hero.type_id);
        }
        let curMaxStage: number = ConfigManager.getHeroMaxUpgradeStage(hero.star);
        let curMaxLevel: number = ConfigManager.GetHeroMaxUpgradeLevel(hero.star, hero.stage);

        if (hero.stage >= curMaxStage
            || hero.level < curMaxLevel
        ) {
            return false;
        }

        //英雄升阶
        let stageCfg = ConfigManager.cfg_hero_stageCache.get(hero.stage + 1);
        if (stageCfg) {
            if (curMaxStage != hero.stage
                && stageCfg.cost_num_1 <= GoodsManager.instance.GetGoodsNumByTypeId(stageCfg.cost_1)
                && stageCfg.cost_num_2 <= GoodsManager.instance.GetGoodsNumByTypeId(stageCfg.cost_2)
            ) {
                return true;
            }
        }
        return false;
    }

    /**是否可升级 */
    public IsCanLevelUpdate(hero: p_hero): boolean {
        if (!hero || !(hero instanceof p_hero)) {
            return false;
        }
        if (HeroDataCenter.checkIsInTemple(hero)) {
            return false;
        }
        // let cfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(hero.type_id);
        //判断是否满足阶位和星级
        let curMaxLevel: number = ConfigManager.GetHeroMaxUpgradeLevel(hero.star, hero.stage, hero.star_stage);

        if (hero.level >= curMaxLevel) {
            return false;
        }

        //英雄升级
        let levelCfg = ConfigManager.cfg_hero_levelCache.get(hero.level);
        if (!levelCfg) {
            return false;
        }
        //道具材料判断
        for (let i = 1; levelCfg["cost_" + i]; ++i) {
            let item_id = levelCfg["cost_" + i];
            let item_num = levelCfg["cost_num_" + i];
            if (!this.isCheckMaterials(item_id, item_num)) {
                return false;
            }
        }

        return true;
    }

    /**是否可合成 */
    public IsCanCompose(typeId: number): boolean {
        let hero = ConfigManager.cfg_hero_baseCache.get(typeId);
        if (hero == null) {
            return false;
        }
        // w7 屏蔽
        // let ownNum: number = GoodsManager.instance.GetGoodsNumByTypeId(hero.hero_chip_id);
        // if (ownNum >= hero.compose_chip_num && HeroDataCenter.instance.getHeroByTypeId(hero.type_id) == null) {
        //     return true;
        // }
        return false;
    }

    /**英雄是否可升级或者升星或者升阶 装备升级或精炼*/
    public isCanUpdate(hero_id: number, isCheckEquip: boolean = true, isCheckLineUp: boolean = false): any {
        let isUpdate: any = {
            type: 0,
            bol: false
        };
        let hero: PHero = HeroDataCenter.instance.getHero(hero_id);
        if (hero == null) {
            return isUpdate;
        }

        if (hero_id == 5024) {
            console.log("isCanUpdate")
        }
        if (isCheckLineUp && LineUpDataCenter.instance.getHeroLineUpType(hero.hero_id) != MatchConst.MATCH_TYPE_MAIN_BATTLE) {
            return isUpdate;
        }

        //异能英雄-能级
        if (hero.ori_nation == HeroNation.NATION_HUN) {
            return isUpdate;
        } else {
            //升星
            if (hero.star < HeroConsts.openStarStageLV && HeroDataCenter.instance.IsCanStarUpdate(hero.type_id, hero.star, hero.hero_id)) {
                isUpdate.type = HeroDataCenter.TAB_STAR;
                isUpdate.bol = true;
                return isUpdate;
            }

            //星阶升星
            if (hero.star >= HeroConsts.openStarStageLV && HeroDataCenter.instance.isCanStarStageUpdate(hero.type_id, hero.star, hero.star_stage, hero.hero_id, hero)) {
                isUpdate.type = HeroDataCenter.TAB_STAR;
                isUpdate.bol = true;
                return isUpdate;
            }

            //英雄升阶
            if (HeroDataCenter.instance.IsCanStageUpdate(hero) && !DudufuDataCenter.instance.checkIsHeroResonate(hero.hero_id)) {
                isUpdate.type = HeroDataCenter.TAB_STAGE;
                isUpdate.bol = true;
                return isUpdate;
            }

            //英雄升级
            if (HeroDataCenter.instance.IsCanLevelUpdate(hero) && !DudufuDataCenter.instance.checkIsHeroResonate(hero.hero_id)) {
                isUpdate.type = HeroDataCenter.TAB_LEVEL;
                isUpdate.bol = true;
                return isUpdate;
            }

            //一键穿戴
            if (isCheckEquip && HeroDataCenter.instance.CheckIsCanOneKey(hero)) {
                isUpdate.type = HeroDataCenter.TAB_SUM;
                isUpdate.bol = true;
                return isUpdate;
            }
        }

        //神装
        if (isCheckEquip && HeroDataCenter.instance.checkHeroWearGodEquip(hero.hero_id)) {
            isUpdate.type = HeroDataCenter.TAB_GODEQUIP;
            isUpdate.bol = true;
            return isUpdate;
        }

        //神装附魔
        if (isCheckEquip && HeroDataCenter.instance.checkHeroGodEquipEnchant(hero.hero_id)) {
            isUpdate.type = HeroDataCenter.TAB_GODEQUIP;
            isUpdate.bol = true;
            return isUpdate;
        }

        //天赋
        if (isCheckEquip && HeroDataCenter.instance.checkHeroBingFaRedPoint(hero.hero_id)) {
            isUpdate.type = HeroDataCenter.TAB_BINGFA;
            isUpdate.bol = true;
            return isUpdate;
        }
        //英雄装备可强化和精炼
        // if (isCheckEquip && EquipDataCenter.instance.isHeroEquipRedPoint(hero.hero_id) > 0) {
        //     isUpdate.type = HeroDataCenter.TAB_SUM;
        //     isUpdate.bol = true;
        //     return isUpdate;
        // }
        // //英雄坐骑可升级升星
        // if (isCheckEquip && RideDataCenter.instance.CheckIsCanUpdateByHeroId(hero.hero_id)) {
        //     isUpdate.type = HeroDataCenter.TAB_SUM;
        //     isUpdate.bol = true;
        //     return isUpdate;
        // }
        // //英雄装备可穿装备
        // if (isCheckEquip && HeroDataCenter.instance.CheckIsCanLoadEquip(hero)) {
        //     isUpdate.type = HeroDataCenter.TAB_SUM;
        //     isUpdate.bol = true;
        //     return isUpdate;
        // }

        //精灵红点
        if (isCheckEquip && WingDataCenter.instance.isWingRed(hero.hero_id)) {
            isUpdate.type = HeroDataCenter.TAB_SUM;
            isUpdate.bol = true;
            return isUpdate;
        }

        //守护灵红点
        if (isCheckEquip && DeputyDataCenter.instance.isHeroDeputyRed(hero.hero_id)) {
            isUpdate.type = HeroDataCenter.TAB_SUM;
            isUpdate.bol = true;
            return isUpdate;
        }

        //武将进化红点
        let canEvolveSkill = HeroEvolveDataCenter.ins.checkCanEvolveSkill(hero.hero_id);
        if (canEvolveSkill) {
            isUpdate.type = HeroDataCenter.TAB_SUM;
            isUpdate.bol = true;
            return isUpdate;
        }

        //皮肤升级与激活红点
        let skinList = ConfigManager.cfg_hero_skin_listCache.get(hero.type_id);
        let isactiv = false;
        let isSkinActive = false;
        if (skinList && skinList instanceof Array) {
            for (let i = 0; i < skinList.length; i++) {
                let skinCfg = ConfigManager.cfg_hero_skinCache.get(skinList[i]?.skin_id);
                let itemCount = GoodsManager.instance.GetGoodsNumByTypeId(skinCfg?.item_id);
                let isCanUnlock = itemCount > 0;
                if (!HeroSkinDataCenter.instance.isSkinActive(skinList[i]?.skin_id) && isCanUnlock && HeroSkinDataCenter.instance.isSatisfyStar(skinList[i]?.skin_id)) {
                    isSkinActive = true;
                }
                if (HeroSkinDataCenter.instance.isSkinCanUpdate(skinList[i]?.skin_id)) {
                    isactiv = true;
                }
            }
            if (isactiv || isSkinActive) {
                isUpdate.type = HeroDataCenter.TAB_HERO_SKIN;
                isUpdate.bol = true;
                return isUpdate;
            }

        }
        return isUpdate;
    }

    //是否可以一键穿戴  排序 品质-套装-养成
    public CheckIsCanOneKey(hero: p_hero): boolean {
        if (hero == undefined) {
            return false;
        }
        let heroCfg = ConfigManager.cfg_hero_baseCache.get(hero.type_id);
        if (heroCfg && heroCfg.nation == HeroNation.NATION_HUN) {
            return false;
        }

        let suitNumDict: Map<number, number> = EquipDataCenter.instance.heroSuitMap(hero.hero_id, hero.type_id);
        let equipArr: GoodsVO[] = HeroEquipManager.instance.getEquips(hero.hero_id);

        //先品质 ，套装数量，养成，没穿装备的孔位 
        for (let i: number = 0; i < HeroDataCenter.equipKindList.length; i++) {
            if (HeroDataCenter.equipKindList[i] == ItemMacro.ITEM_KIND_BING_FU) {
                continue;
            }
            let isOwnEquip: boolean = false;
            for (let j: number = 0; j < equipArr.length; j++) {
                let vo: GoodsVO = equipArr[j];

                if (vo.kind == HeroDataCenter.equipKindList[i]) {
                    if (ForgeDataCenter.instance.GetIsCanChangeEquip3(vo, suitNumDict, hero)) {
                        return true;
                    } else {
                        isOwnEquip = true;
                        break;
                    }
                }
            }
            if (!isOwnEquip && ForgeDataCenter.instance.GetIsCanLoadEquip(HeroDataCenter.equipKindList[i], hero.level, hero.type_id)) {
                return true;
            }
        }
        return false;

    }

    sortEquipSuit(vo1: HeroEquipSortVo, vo2: HeroEquipSortVo): number {
        let suitNum: number = vo1.suitNum - vo2.suitNum;
        if (suitNum != 0) {
            return -suitNum;
        }
        return -(vo1.suitId - vo2.suitId);
    }

    /**检测是否有英雄可以合成或者升星 */
    public CheckListRedPoint(): void {
        let noActListArr: cfg_hero_base[] = HeroDataCenter.baseCfgHeroArr;

        var isRedPoint: boolean = false;
        for (let i: number = 0; i < noActListArr.length; i++) {
            let vo: cfg_hero_base = noActListArr[i];
            if (HeroDataCenter.instance.IsCanCompose(vo.type_id)) {//可合成
                isRedPoint = true;
                break;
            }
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.ALL_HERO, HeroDataCenter.TAB_DEBRIS, isRedPoint ? 1 : 0);

        // isRedPoint = false;
        // for (let i: number = 0; i < HeroDataCenter.instance.hero_list.length; i++) {
        //     let actHero: p_hero = HeroDataCenter.instance.hero_list[i];
        //     if (HeroDataCenter.instance.IsCanStarUpdate(actHero)) {
        //         isRedPoint = true;
        //         break;
        //     }
        // }
        // RedPointMgr.ins.SetRedData(PanelEventConstants.HERO_INFO, HeroDataCenter.TAB_STAR, isRedPoint ? 1 : 0);
    }

    public IsCanComposeByAll(): boolean {
        let noActListArr: cfg_hero_base[] = [];

        noActListArr = HeroDataCenter.baseCfgHeroArr;

        for (let i: number = 0; i < noActListArr.length; i++) {
            let vo: cfg_hero_base = noActListArr[i];
            if (HeroDataCenter.instance.IsCanCompose(vo.type_id)) {//可合成
                return true;
            }
        }
        return false;
    }

    /**总览菜单红点 */
    public IsCanTabSumSingle(hero: p_hero): boolean {
        // //英雄装备可强化和精炼
        // let equipNum: number = EquipDataCenter.instance.isHeroEquipRedPoint(hero.hero_id);
        // if (equipNum) {
        //     count += equipNum;
        // }
        // //英雄装备可穿装备
        // if (HeroDataCenter.instance.CheckIsCanLoadEquip(hero)) {
        //     count += 1;
        // }

        //异能英雄
        let heroCfg = ConfigManager.cfg_hero_baseCache.get(hero.type_id);
        if (heroCfg.nation == HeroNation.NATION_HUN) {
            return false;
        }

        if (hero) {
            //提携英雄没红点
            if (DudufuDataCenter.instance.checkIsHeroResonate(hero.hero_id)) {
                return false;
            }

            //英雄升级
            if (HeroDataCenter.instance.IsCanLevelUpdate(hero)) {
                return true;
            }

            //英雄升阶
            if (HeroDataCenter.instance.IsCanStageUpdate(hero)) {
                return true;
            }
        }

        return false;


    }


    //检查英雄红点
    public CheckHeroInfoRedPoint(): void {
        let count = 0;
        let lineUpList = LineUpDataCenter.instance.getLineUpListByType(MatchConst.MATCH_TYPE_MAIN_BATTLE);
        for (let i: number = 0; i < lineUpList.length; i++) {
            let heroId = lineUpList[i];
            let hero: p_hero = HeroDataCenter.instance.getHero(heroId);
            if (hero) {
                let result: any = HeroDataCenter.instance.isCanUpdate(hero.hero_id, true, true);
                if (result.bol == true) {
                    count = 1;
                    break;
                }
            }
        }
        HeroDataCenter.instance.checkGodEquipRedPoint();
        RedPointMgr.ins.SetRedData(PanelEventConstants.ACT_HERO_LIST, 0, count);
        HeroDataCenter.instance.checkLineUpHeroBingFaRedPoint();

        let allRedNum = RedPointMgr.ins.IsCheckRedPoint(PanelEventConstants.ACT_HERO_LIST) ? 1 : 0;
        RedPointMgr.ins.SetRedData(PanelEventConstants.ALL_HERO, PanelEventConstants.ACT_HERO_LIST, allRedNum);
    }

    public checkHeroWearGodEquip(hero_id: number): boolean {
        let wear_god_equip = false;

        let pHero = HeroDataCenter.instance.getHero(hero_id);
        if (pHero && pHero.star >= MiscConst.open_god_equip_role_limit_star && GameUtil.isSysOpen(PanelEventConstants.CHALLENGE_CHAPTER, 0, false)) {
            for (let i: number = 0; i < HeroDataCenter.godEquipKindList.length; i++) {
                let kind = HeroDataCenter.godEquipKindList[i];
                let equipVo: HeroEquipVo = HeroEquipManager.instance.getHeroEquipVo(hero_id);
                if (equipVo != undefined) {
                    let godEquipVo = equipVo.getEquipByKind(kind);
                    let godEquipList = GoodsManager.instance.GetBagEquipListByKind(kind);
                    if (godEquipVo == undefined && godEquipList.length > 0) {
                        wear_god_equip = true;
                    }
                }
            }
        }
        return wear_god_equip;
    }

    /**神装附魔红点 */
    public checkHeroGodEquipEnchant(hero_id: number): boolean {
        let pHero = HeroDataCenter.instance.getHero(hero_id);
        if (pHero && pHero.star >= MiscConst.open_god_equip_role_limit_star && GameUtil.isSysOpen(PanelEventConstants.CHALLENGE_CHAPTER, 0, false)) {
            for (let i: number = 0; i < HeroDataCenter.godEquipKindList.length; i++) {
                let kind = HeroDataCenter.godEquipKindList[i];
                let equipVo: HeroEquipVo = HeroEquipManager.instance.getHeroEquipVo(hero_id);
                if (!equipVo) {
                    continue;
                }
                let godEquipVo = equipVo.getEquipByKind(kind);
                if (this.checkHeroGodEquipEnchatActive(godEquipVo)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**检测神装附魔词条激活 */
    public checkHeroGodEquipEnchatActive(godEquipVo: GoodsVO): boolean {
        //3星及以上的传说神装，并且附魔词条少于2条，才进行激活红点判断
        if (godEquipVo && godEquipVo.star >= 3 && godEquipVo.color == 5 && godEquipVo.goods.skills.length < 2) {
            let enchant_cost_cfg = CfgCacheMapMgr.cfg_god_equip_enchant_costCache.get(godEquipVo.goods.skills.length + 1);
            if (enchant_cost_cfg) {
                let vo = GameUtil.parseSpecifyRewards(enchant_cost_cfg, "cost_");
                if (vo.has_count >= vo.showUINum) {
                    return true;
                }
            }
        }
        return false;
    }

    public checkGodEquipRedPoint(): any {
        let lineup_hero_god_equip = false;

        if (!GameUtil.isSysOpen(PanelEventConstants.CHALLENGE_CHAPTER, 0, false)) {
            return false;
        }
        let matchInfo = LineUpDataCenter.instance.getMatchLineUpInfoByType(MatchConst.MATCH_TYPE_MAIN_BATTLE);
        for (let multiInfo of matchInfo.list) {
            if (multiInfo) {
                for (let heroId of multiInfo.hero_id_list) {
                    if (heroId) {
                        let god_red = HeroDataCenter.instance.checkHeroWearGodEquip(heroId);
                        god_red = god_red || HeroDataCenter.instance.checkHeroGodEquipEnchant(heroId);
                        if (god_red) {
                            RedPointMgr.ins.SetRedData(PanelEventConstants.HERO_INFO, HeroDataCenter.TAB_GODEQUIP, 1);
                            RedPointMgr.ins.SetRedData(parseInt(PanelEventConstants.HERO_INFO.toString() + heroId.toString()), heroId, 1);
                            lineup_hero_god_equip = true;
                        } else {
                            RedPointMgr.ins.SetRedData(PanelEventConstants.HERO_INFO, HeroDataCenter.TAB_GODEQUIP, 0);
                            RedPointMgr.ins.SetRedData(parseInt(PanelEventConstants.HERO_INFO.toString() + heroId.toString()), heroId, 0);
                        }
                    }
                }
            }
        }

        if (lineup_hero_god_equip) {
            RedPointMgr.ins.SetRedData(PanelEventConstants.ACT_HERO_LIST, PanelEventConstants.HERO_INFO, 1);
        } else {
            let actHeroList = RedPointMgr.ins.IsCheckRedPoint(PanelEventConstants.HERO_INFO) ? 1 : 0;
            RedPointMgr.ins.SetRedData(PanelEventConstants.ACT_HERO_LIST, PanelEventConstants.HERO_INFO, actHeroList);
        }

        let allRedNum = RedPointMgr.ins.IsCheckRedPoint(PanelEventConstants.ACT_HERO_LIST) ? 1 : 0;
        RedPointMgr.ins.SetRedData(PanelEventConstants.ALL_HERO, PanelEventConstants.ACT_HERO_LIST, allRedNum);
    }

    /**是否有可以替换的品质更高级的英雄 */
    public IsCanChangeHero(hero: p_hero): boolean {
        let arr1 = LineUpDataCenter.instance.getLineUpListByType(MatchConst.MATCH_TYPE_MAIN_BATTLE);
        let arr2 = LineUpDataCenter.instance.getLineUpListByType(HeroDataCenter.LINE_UP_BREED);
        let arr3 = LineUpDataCenter.instance.getLineUpListByType(HeroDataCenter.LINE_UP_YUAN_JUN);
        //let currentCfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(hero.type_id);

        for (let i: number = 0; i < HeroDataCenter.instance.hero_list.length; i++) {
            let single: p_hero = HeroDataCenter.instance.hero_list[i];
            // let cfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(single.type_id);
            if (HeroDataCenter.getColorByHeroOrCfgHero(single) > HeroDataCenter.getColorByHeroOrCfgHero(hero) && arr1.indexOf(single.hero_id) == -1 && arr1.indexOf(single.hero_id) == -1 && arr1.indexOf(single.hero_id) == -1) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取英雄技能相关参数
     * heroInfo:指定heroInfo,否则取自己的
     */
    public GetSkillParmameter2(hero_type_id: number, heroInfo: p_hero = null, isOtherPlayerHero: boolean = false): SkillItemVo {
        let skillItemVo = new SkillItemVo();
        // skillItemVo.hero_type_id = hero_type_id;
        // skillItemVo.isOtherPlayerHero = isOtherPlayerHero;

        if (!heroInfo) {
            //不传heroInfo,就默认获取自己的
            if (!isOtherPlayerHero) {
                heroInfo = this.getHeroByTypeId(hero_type_id);
            }
        }
        if (heroInfo) {
            /**技能id_level映射表 */
            skillItemVo.id_level_map = SkillDataCenter.ins.GetHeroActiveSkills_id_Level_map(heroInfo);
        }

        skillItemVo.heroInfo = heroInfo;

        return skillItemVo;
    }

    /**满级技能 */
    public getSkillFull(hero_type_id: number): SkillItemVo {
        let skillItemVo = new SkillItemVo();
        /**技能id_level映射表 */
        let skillIds = SkillDataCenter.ins.getHeroActiveSkills(hero_type_id);
        let skill_id_level_map: {} = {};
        skillIds.forEach(id => {
            let skillLevels: cfg_skill_level[] = ConfigManager.cfg_skill_levelCache.get(id);
            let level: number = 0;
            for (const cfgItem of skillLevels) {
                if (cfgItem.is_close == 0) {
                    level++;
                }
            }
            if (!skill_id_level_map[id]) {
                skill_id_level_map[id] = level;
            }
        });

        skillItemVo.id_level_map = skill_id_level_map;
        skillItemVo.heroInfo = null;
        return skillItemVo;
    }

    public static get baseCfgHeroArr(): cfg_hero_base[] {
        let baseListArr: cfg_hero_base[] = [];
        let cfgArr = Array.from(ConfigManager.cfg_hero_baseCache.values());
        baseListArr = cfgArr;
        // for (let i: number = 0; i < cfgArr.length; i++) {
        //     let vo: cfg_hero_base = cfgArr[i];
        //     /** //w7 屏蔽 
        //     if (vo.is_hero == 0|| vo.is_show == 0 ) {
        //         continue;
        //     }
        //     */
        //     baseListArr.push(vo);
        // }
        return baseListArr;
    }


    /**检查是否援军 */
    public isCheckYuanJun(hero_id: number): boolean {
        if (LineUpDataCenter.instance.getHeroLineUpType(hero_id) == HeroDataCenter.LINE_UP_YUAN_JUN) {
            return true;
        }
        return false;
    }

    /**与上阵英雄是否有相同占星阁星位 */
    public isLineUpSameStar(_hero: p_hero, lineUp: number[]): boolean {
        /*W7屏蔽
        if (_hero && _hero.temple_hero_id > 0) {
            for (let i = 0; i < lineUp.length; i++) {
                let heroId = lineUp[i];
                if (heroId > 0) {
                    let hero: p_hero = this.getHero(heroId);
                    if (hero != null && hero.temple_hero_id == _hero.temple_hero_id) {
                        return true;
                    }
                }
            }
        }
        */
        return false;
    }

    /**英雄在布阵上的顺序 */
    public getCareerSortIndex(hero: p_hero): number {
        let cfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(hero.type_id);
        if (!cfg) {
            return -1;
        }
        switch (cfg.career) {
            case 1:
                return 1;
            case 2:
                return 3;
            case 3:
                return 5;
            case 4:
                return 4;
            case 5:
                return 2;
            case 6:
                return 6;
            default:
                break;
        }
        return 0;
    }

    public getHeroCardFacade(typeId: number, star: number, stage: number): HeroCardFacadeVo {
        // let arr: cfg_hero_card_facade[] = ConfigManager.cfg_hero_card_facadeCache.get(typeId);
        // if (arr) {
        //     let vo: HeroCardFacadeVo = new HeroCardFacadeVo();
        //     for (const iterator of arr) {
        //         if ((iterator.star > 0 && star >= iterator.star) || (iterator.stage > 0 && stage >= iterator.stage)) {
        //             if (iterator.card_frame.length > 1) {
        //                 vo.card_frame = iterator.card_frame;
        //             }
        //             if (iterator.head_frame.length > 1) {
        //                 vo.head_frame = iterator.head_frame;
        //             }
        //             if (iterator.card_icon.length > 1) {
        //                 vo.card_icon = iterator.card_icon;
        //             }
        //             if (iterator.hero_info.length > 1) {
        //                 vo.card_info = iterator.hero_info;
        //             }
        //         }
        //     }
        //     return vo;
        // }
        return null;
    }

    public getMaxResetCount(): number {
        let max_count = 0;
        for (let count of ConfigManager.cfg_hero_recycleCache.keys()) {
            max_count = Math.max(count, max_count);
        }
        return max_count;
    }

    public getMaxExpansionCount(): number {
        let max_count = 0;
        for (let count of ConfigManager.cfg_hero_bagCache.keys()) {
            max_count = Math.max(count, max_count);
        }
        return max_count;
    }

    public isHeroLock(hero_id: number): boolean {
        if (this.lockHeroIds.indexOf(hero_id) >= 0) {
            return true;
        }
        let heroInfo = this.getHero(hero_id);
        return this.isHeroTypeLock(heroInfo && heroInfo.type_id || 0);
    }

    public isHeroTypeLock(type_id: number): boolean {
        if (!type_id) {
            return false;
        }
        return this.lockHeroTypes.indexOf(type_id) >= 0;
    }

    public isCanReset(is_show_tip: boolean = false): boolean {
        let max_count = this.getMaxResetCount();
        if (this.resetCount >= max_count) {
            is_show_tip && TipsUtil.showTips(window.iLang.L2_ZHONG_ZHI_CI_SHU_BU_ZU.il());
            return false;
        }
        return true;
    }

    public isCanExpansionHeroBag(is_show_tip: boolean = false): boolean {
        let max_count = this.getMaxExpansionCount();
        if (this.expansionCount >= max_count) {
            is_show_tip && TipsUtil.showTips(window.iLang.L2_KUO_RONG_CI_SHU_BU_ZU.il());
            return false;
        }
        return true;
    }

    public isMaterialHeroCanSelect(hero_info: p_hero, isUseLimitTypeHero = false): boolean {
        if (!hero_info) return false;
        if (hero_info.star >= MiscConst.hero_material_cost_limit && hero_info.star_stage.length > 0) {
            return false;
        }
        if (isUseLimitTypeHero == false && hero_info.limit_type > 0) {
            return false;
        }
        let hero_id = hero_info.hero_id;
        return !this.checkIsLinkHero(hero_id) && !this.isHeroLock(hero_id) && !this.checkIsActiveUpStar14(hero_id);
    }

    public isCanUseGreatHeroPoint(typeId: number, showTip: boolean = true): boolean {
        let num = GoodsManager.instance.GetGoodsNumByTypeId(ItemConst.COST_GREAT_HERO_POINT);
        let needNum = HeroDataCenter.instance.getGreatHeroPointNum(typeId);
        if (num < needNum) {
            if (showTip) {
                TipsUtil.showDialog(this, window.iLang.L2_XIN_RUI_DIAN_BU_ZU_ch31_SHI_FOU_QIAN_WANG_MEI_RI_LI_BAO_HUO_QU_ch44_BR.il(), window.iLang.L2_TI_SHI.il(), function () {
                    GameUtil.gotoSystemPanelById(PanelEventConstants.PAYMENT, PaymentVO.KEY_EVERYDAY_SHOP);
                }, {okName: window.iLang.L2_QIAN_WANG.il()});
            }
            return false;
        }
        return true;
    }

    /**检测英雄背包容量是否足够 */
    public checkHeroCapacityEnough(add_count: number, is_show_tip: boolean = false): boolean {
        let cur_hero_num = HeroDataCenter.instance.hero_list.length;
        let cur_expansion_count = HeroDataCenter.instance.expansionCount;
        let cur_hero_bag_cfg = ConfigManager.cfg_hero_bagCache.get(cur_expansion_count);

        if (cur_hero_num + add_count > cur_hero_bag_cfg.capacity) {
            is_show_tip && TipsUtil.showTips(window.iLang.L2_HERO_WEI_ZHI_SHU_LIANG_BU_ZU.il());
            return false;
        }
        return true;
    }

    /**检测英雄等级等阶是否为初始状态 */
    public checkHeroInitialState(hero_id: number): boolean {
        let hero_info = this.getHero(hero_id);
        if (hero_info) {
            if (VipTeQuanUtil.checkHeroCompleteRecycle()) {
                let heroCfg = ConfigManager.cfg_hero_baseCache.get(hero_info.type_id);
                if (heroCfg.init_star < hero_info.star && hero_info.star <= 6) {
                    return false;
                }
            }
            return hero_info.level == 1 && hero_info.stage == 0;
        }
        return false;
    }

    public isTuijianBingFa(binga_id: number): boolean {
        let pHero: p_hero = HeroDataCenter.instance.getHero(HeroDataCenter.instance.select_hero_id);
        if (pHero) {
            let heroBaseCfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(pHero.type_id);
            let herobingfaList: string[] = [];
            if (heroBaseCfg.bingfa_ids && heroBaseCfg.bingfa_ids != "" && heroBaseCfg.bingfa_ids != "0" && heroBaseCfg.bingfa_ids.indexOf("|") != -1) {
                herobingfaList = heroBaseCfg.bingfa_ids.split("|");
            }
            for (let index = 0; index < herobingfaList.length; index++) {
                let herobingfaId = herobingfaList[index];
                if (herobingfaId == binga_id.toString()) {
                    return true;
                }
            }
        }
        return false;
    }

    public isOpenWarFlagLink(nation: number): boolean {

        let warFlagInfo = HeroDataCenter.instance.getWarFlagNationInfo(nation, HeroDataCenter.instance.war_flag_infos);
        let warFlagCfg = ConfigManager.cfg_war_flagCache.get(nation);
        if (warFlagInfo == null || warFlagCfg == undefined) {
            return false;
        }
        if (warFlagInfo.stage >= warFlagCfg.open_link_stage) {
            return true;
        }
        let link_nation = HeroDataCenter.instance.getWarFlagTargetNationLink(nation, HeroDataCenter.instance.war_flag_link_infos);
        if (link_nation > 0) {
            return true;
        }
        return false;
    }

    public getWarFlagTargetNationLink(nation: number, link_infos: number[]): number {
        if (link_infos.length == 0) {
            return 0;
        }
        for (let index = 0; index < link_infos.length; index++) {
            let war_flag_link_number: number = link_infos[index];
            let link_cfg = ConfigManager.cfg_war_flag_linkCache.get(war_flag_link_number);
            if (nation == link_cfg.nation_1) {
                return link_cfg.nation_2;
            }
            if (nation == link_cfg.nation_2) {
                return link_cfg.nation_1;
            }
        }
        return 0;
    }

    public getWarFlagNationInfo(nation: number, infos: p_war_flag[]): p_war_flag {
        if (infos.length == 0) {
            return null;
        }
        for (let index = 0; index < infos.length; index++) {
            let war_flag_info: p_war_flag = infos[index];
            if (nation == war_flag_info.nation) {
                return war_flag_info;
            }
        }
        return null;
    }

    public getWarFlagShowStage(stage: number): number {
        let showStage = 0;
        if (stage != 0) {
            showStage = (stage % 5 == 0) ? (stage / 5) : Math.floor(stage / 5);
        }
        return showStage;
    }

    public getWarFlagShowStageName(stage: number): string {
        let showStage = 0;
        if (stage != 0) {
            showStage = (stage % 5 == 0) ? (stage / 5) : Math.floor(stage / 5);
        }
        return LangConst.L_P0_STAGE.il([showStage]);
    }

    public getWarFlagHighLvNation(): number {
        if (HeroDataCenter.instance.war_flag_infos.length == 0) {
            return 0;
        }

        let infos = HeroDataCenter.instance.war_flag_infos.sort((info1, info2) => {
            if (info1.level == info2.level) {
                return info1.nation - info2.nation;
            }
            return info2.level - info1.level;
        });

        for (let index = 0; index < infos.length; index++) {
            let war_flag_info: p_war_flag = infos[index];
            let warFlagMaxLvCfg = ConfigManager.cfg_war_flag_levelCache.m_get(war_flag_info.nation, war_flag_info.level + 1);
            if (warFlagMaxLvCfg == undefined) {
                continue;
            }
            return war_flag_info.nation;
        }
        return 0;
    }

    public calTwoWarFlagStageAttr(nation: number, targetNation: number): FightAttrVO[] {

        let warFlagInfo1 = HeroDataCenter.instance.getWarFlagNationInfo(nation, HeroDataCenter.instance.war_flag_infos);
        let showAttrStage1 = HeroDataCenter.instance.getWarFlagShowStage(warFlagInfo1.stage) * 5;
        let attrValueLists1: FightAttrVO[] = HeroDataCenter.instance.calWarFlagStageAttr(nation, showAttrStage1);

        let warFlagInfo2 = HeroDataCenter.instance.getWarFlagNationInfo(targetNation, HeroDataCenter.instance.war_flag_infos);
        let showAttrStage2 = HeroDataCenter.instance.getWarFlagShowStage(warFlagInfo2.stage) * 5;
        let attrValueLists2: FightAttrVO[] = HeroDataCenter.instance.calWarFlagStageAttr(targetNation, showAttrStage2);

        let attrVos = GameUtil.mergeAttrVoArr(attrValueLists1.concat(attrValueLists2));

        return attrVos;
    }

    public calWarFlagLvAttr(lv: number, exp: number, nation: number): FightAttrVO[] {
        let lvAttr: FightAttrVO[] = [];
        let warFlagLvCfg = ConfigManager.cfg_war_flag_levelCache.m_get(nation, lv);
        if (warFlagLvCfg) {
            let lvTimes = warFlagLvCfg.add_exp == 0 ? 0 : (exp / warFlagLvCfg.add_exp);
            for (let i = 1; i < 10; i++) {

                if (warFlagLvCfg["attr_id_" + i] && warFlagLvCfg["attr_val_" + i]) {
                    let attrCode: number = warFlagLvCfg["attr_id_" + i];
                    let attrVal: number = warFlagLvCfg["attr_val_" + i];
                    if (lvTimes != 0) {
                        attrVal = attrVal + warFlagLvCfg["add_attr_val_" + i] * lvTimes;
                    }
                    let vo: FightAttrVO = FightAttrMgr.instance.getAttrVO(attrCode, attrVal);
                    if (vo) {
                        lvAttr.push(vo);
                    }
                } else {
                    break;
                }
            }
        }
        return lvAttr;
    }

    public calWarFlagStageAddAttr(nation: number, attr_code: number): string {
        if (attr_code != 21 && attr_code != 22 && attr_code != 14) {
            return "";
        }

        let warFlagInfo = HeroDataCenter.instance.getWarFlagNationInfo(nation, HeroDataCenter.instance.war_flag_infos);
        if (warFlagInfo == null) {
            return "";
        }

        let showAttrStage = HeroDataCenter.instance.getWarFlagShowStage(warFlagInfo.stage) * 5;
        if (showAttrStage == warFlagInfo.stage) {
            return "";
        }

        let add_attrs: FightAttrVO[] = [];
        for (let i = showAttrStage + 1; i <= warFlagInfo.stage; i++) {
            let warFlagStageCfg = ConfigManager.cfg_war_flag_stageCache.m_get(nation, i);
            if (warFlagStageCfg) {
                let attr: FightAttrVO = FightAttrMgr.instance.getAttrVO(warFlagStageCfg.add_attr_id, warFlagStageCfg.add_attr_val);
                if (attr != undefined) {
                    add_attrs.push(attr);
                }
            }
        }

        add_attrs = GameUtil.mergeAttrVoArr(add_attrs);
        for (let vo of add_attrs) {
            if (vo.code == attr_code) {
                return vo.valString;
            }
        }
        return "";
    }

    public calWarFlagStageAttr(nation: number, stage: number): FightAttrVO[] {
        let stageAttr: FightAttrVO[] = [];
        let warFlagStageCfg = ConfigManager.cfg_war_flag_stageCache.m_get(nation, stage);
        if (warFlagStageCfg) {
            for (let i = 1; i < 10; i++) {
                if (warFlagStageCfg["attr_id_" + i]) {
                    let attrCode: number = warFlagStageCfg["attr_id_" + i];
                    let attrVal: number = warFlagStageCfg["attr_val_" + i];
                    let vo: FightAttrVO = FightAttrMgr.instance.getAttrVO(attrCode, attrVal);
                    if (vo) {
                        stageAttr.push(vo);
                    }
                } else {
                    break;
                }
            }
        }
        return stageAttr;
    }

    public getWarFlagStageAttrValString(nation: number, attr_code: number): string {
        if (attr_code != 21 && attr_code != 22 && attr_code != 14) {
            return "";
        }

        let warFlagInfo = HeroDataCenter.instance.getWarFlagNationInfo(nation, HeroDataCenter.instance.war_flag_infos);
        if (warFlagInfo != null) {

            let showAttrStage = HeroDataCenter.instance.getWarFlagShowStage(warFlagInfo.stage) * 5;
            let attrInfos: FightAttrVO[] = HeroDataCenter.instance.calWarFlagStageAttr(warFlagInfo.nation, showAttrStage);
            for (let attrInfo of attrInfos) {
                if (attrInfo.code == attr_code) {
                    return attrInfo.valString;
                }
            }
        }
        let vo: FightAttrVO = FightAttrMgr.instance.getAttrVO(attr_code, 0);
        return vo.valString;
    }

    public suggestWarFlagLinkNation(): number {
        let lineupList: number[] = LineUpDataCenter.instance.getLineUpListByType(MatchConst.MATCH_TYPE_MAIN_BATTLE).filter(function (hero_id) {
            if (hero_id == 0) {
                return false;
            }

            let phero = HeroDataCenter.instance.getHero(hero_id);
            if (phero.nation == HeroDataCenter.instance.cur_war_flag_nation || phero.nation == HeroNation.NATION_HUN) {
                return false;
            }
            if (HeroDataCenter.instance.getWarFlagTargetNationLink(phero.nation, HeroDataCenter.instance.war_flag_link_infos) > 0) {
                return false;
            }
            if (HeroDataCenter.instance.getWarFlagNationInfo(phero.nation, HeroDataCenter.instance.war_flag_infos) == null) {
                return false;
            }
            return true;
        });
        if (lineupList.length == 0) {
            return 0;
        }
        let nationMap: Map<number, PHero[]> = new Map();
        for (let i = 0; i < lineupList.length; i++) {
            let hero_id = lineupList[i];
            let phero = HeroDataCenter.instance.getHero(hero_id);
            if (!nationMap.has(phero.nation)) {
                nationMap.set(phero.nation, []);
            }
            let list: PHero[] = nationMap.get(phero.nation);
            list.push(phero);
        }
        let nation_ids: number[] = [];
        Array.from(nationMap.keys()).forEach(nation => {
            nation_ids.push(nation);
        });
        nation_ids.sort((nation1, nation2) => {
            if (nationMap.get(nation1).length != nationMap.get(nation2).length) {
                return nationMap.get(nation2).length - nationMap.get(nation1).length;
            }
            let nation_herop_all_power1 = 0;
            let nation_herop_all_power2 = 0;
            nationMap.get(nation1).forEach(phero => {
                nation_herop_all_power1 += phero.power;
            });
            nationMap.get(nation2).forEach(phero => {
                nation_herop_all_power2 += phero.power;
            });
            if (nation_herop_all_power1 != nation_herop_all_power2) {
                return nation_herop_all_power2 - nation_herop_all_power1;
            }
            return nation1 - nation2;
        });

        return nation_ids[0];
    }

    public getWarFlagStageFrontBackAttr(nation: number, front: boolean, back: boolean): FightAttrVO {
        let warFlagInfo = HeroDataCenter.instance.getWarFlagNationInfo(nation, HeroDataCenter.instance.war_flag_infos);
        if (warFlagInfo == null) {
            return null;
        }
        let stage = warFlagInfo.stage;
        if (front) {
            let frontStage = stage;
            let frontCfg = ConfigManager.cfg_war_flag_stageCache.m_get(nation, frontStage);
            let frontChangeVo: FightAttrVO = FightAttrMgr.instance.getAttrVO(frontCfg.add_attr_id, frontCfg.add_attr_val);
            return frontChangeVo;
        }

        if (stage == 80) {
            return null;
        }
        let backStage = stage + 1;
        let backCfg = ConfigManager.cfg_war_flag_stageCache.m_get(nation, backStage);
        let backChangeVo: FightAttrVO = FightAttrMgr.instance.getAttrVO(backCfg.add_attr_id, backCfg.add_attr_val);
        return backChangeVo;
    }

    public getWarFlagLvFrontAttr(nation: number): string {
        let changeStr = "";
        let warFlagInfo = HeroDataCenter.instance.getWarFlagNationInfo(nation, HeroDataCenter.instance.war_flag_infos);
        let curCfg = ConfigManager.cfg_war_flag_levelCache.m_get(nation, warFlagInfo.level);
        let attrVo1: FightAttrVO = FightAttrMgr.instance.getAttrVO(curCfg.add_attr_id_1, curCfg.add_attr_val_1);
        let attrVo2: FightAttrVO = FightAttrMgr.instance.getAttrVO(curCfg.add_attr_id_2, curCfg.add_attr_val_2);
        if (attrVo1 || attrVo2) {
            changeStr = attrVo1.name + "+" + attrVo1.valString + "    " + attrVo2.name + "+" + attrVo2.valString;
        }
        return changeStr;
    }

    public checkWarFlagLineUpHeroSameNation(nation: number): boolean {
        //主队
        let heroIds1 = LineUpDataCenter.instance.getCurLineUpListByType(MatchConst.MATCH_TYPE_MAIN_BATTLE, 1);
        //副队
        let heroIds2 = LineUpDataCenter.instance.getCurLineUpListByType(MatchConst.MATCH_TYPE_MAIN_BATTLE, 2);
        let heroInfos = [];
        for (let i = 0; i < heroIds1.length; ++i) {
            let hero_id = heroIds1[i];
            let heroInfo = LineUpUtil.getHeroInfo(MatchConst.MATCH_TYPE_MAIN_BATTLE, hero_id);
            if (heroInfo) {
                heroInfos.push(heroInfo);
            }
        }
        for (let i = 0; i < heroIds2.length; ++i) {
            let hero_id = heroIds2[i];
            let heroInfo = LineUpUtil.getHeroInfo(MatchConst.MATCH_TYPE_MAIN_BATTLE, hero_id);
            if (heroInfo) {
                heroInfos.push(heroInfo);
            }
        }
        if (heroInfos.length > 0 && LineUpBuffUtil.getNationNumList(heroInfos)[nation - 1]) {
            return LineUpBuffUtil.getNationNumList(heroInfos)[nation - 1] > 0;
        }
        return false;
    }

    public checkWarFlagLevelUpRedPoint(nation: number): boolean {
        let warFlagInfo = HeroDataCenter.instance.getWarFlagNationInfo(nation, HeroDataCenter.instance.war_flag_infos);
        if (warFlagInfo == null) {
            return false;
        }
        // let key = "WarFlagLevelUp";
        // if(this.getTodayOpened(key)){
        //     return false;
        // }
        // if(HeroDataCenter.instance.getWarFlagHighLvNation() != warFlagInfo.nation){
        //     return false;
        // }
        if (!HeroDataCenter.instance.checkWarFlagLineUpHeroSameNation(nation)) {
            return false;
        }
        let lv: number = warFlagInfo.level;
        let warFlagLvCfg = ConfigManager.cfg_war_flag_levelCache.m_get(nation, lv);
        if (warFlagLvCfg) {
            let currnum1: number = GoodsManager.instance.GetGoodsNumByTypeId(warFlagLvCfg.cost_id_1);
            let currnum2: number = GoodsManager.instance.GetGoodsNumByTypeId(warFlagLvCfg.cost_id_2);
            if (currnum1 >= warFlagLvCfg.cost_val_1 && currnum2 >= warFlagLvCfg.cost_val_2 && currnum1 != 0 && currnum2 != 0 && warFlagLvCfg.total_exp != 0) {
                return true;
            }
        }
        return false;
    }

    public checkWarFlagStageRedPoint(nation: number): boolean {
        let warFlagInfo = HeroDataCenter.instance.getWarFlagNationInfo(nation, HeroDataCenter.instance.war_flag_infos);
        if (warFlagInfo == null) {
            return false;
        }
        if (!HeroDataCenter.instance.checkWarFlagLineUpHeroSameNation(nation)) {
            return false;
        }
        let stage: number = warFlagInfo.stage;
        let lv: number = warFlagInfo.level;
        let warFlagStageCfg = ConfigManager.cfg_war_flag_stageCache.m_get(nation, stage);
        if (warFlagStageCfg && stage != 80) {
            let currnum1: number = GoodsManager.instance.GetGoodsNumByTypeId(warFlagStageCfg.cost_id_1);
            let currnum2: number = GoodsManager.instance.GetGoodsNumByTypeId(warFlagStageCfg.cost_id_2);
            let isLvLimit = true;
            if (lv >= warFlagStageCfg.flag_level_limit && DataCenter.myLevel >= warFlagStageCfg.role_level_limit) {
                isLvLimit = false;
            }
            if (currnum1 >= warFlagStageCfg.cost_val_1 && currnum2 >= warFlagStageCfg.cost_val_2 && !isLvLimit && currnum1 != 0 && currnum2 != 0) {
                return true;
            }
        }
        return false;
    }

    public checkNationWarFlagUnLockRedPoint(nation: number): number {
        let count: number = 0
        let lvCount: number = this.checkWarFlagLevelUpRedPoint(nation) ? 1 : 0;
        count += lvCount;
        RedPointMgr.ins.SetRedData(PanelEventConstants.HERO_WAR_FLAG_LV, 1, lvCount);
        let stageCount: number = this.checkWarFlagStageRedPoint(nation) ? 1 : 0;
        count += stageCount;
        RedPointMgr.ins.SetRedData(PanelEventConstants.HERO_WAR_FLAG_STAGE, 2, stageCount);
        return count;
    }

    public checkNationWarFlagLockRedPoint(nation: number): number {
        let warFlagInfo = HeroDataCenter.instance.getWarFlagNationInfo(nation, HeroDataCenter.instance.war_flag_infos);
        if (warFlagInfo == null) {
            let heroList = HeroDataCenter.instance.getNationHero(nation, 5);
            heroList = heroList.filter(hero => {
                return !HeroDataCenter.instance.isHeroLock(hero.hero_id);
            });
            let material_data: HeroMaterialData = HeroMaterialData.create(0, nation, {star: 5});
            let itemList: any[] = HeroDataCenter.instance.getMaterialGoodsInfoList(material_data, []);
            if ((heroList.length > 0 || itemList.length > 0) && GameUtil.isSysOpen(PanelEventConstants.HERO_WAR_FLAG, 0, false)) {
                return 1;//可激活
            }
            return -1;//不能激活
        }
        return 0;//已解锁
    }

    public checkNationWarFlagRedPoint(nation: number): number {
        let warFlagStage: number = this.checkNationWarFlagLockRedPoint(nation);
        if (warFlagStage == 0) {
            RedPointMgr.ins.SetRedData(PanelEventConstants.HERO_WAR_FLAG, nation, this.checkNationWarFlagUnLockRedPoint(nation));
            return this.checkNationWarFlagUnLockRedPoint(nation);
        } else if (warFlagStage == 1) {
            RedPointMgr.ins.SetRedData(PanelEventConstants.HERO_WAR_FLAG, nation, 1);
            return 1;
        } else {
            RedPointMgr.ins.SetRedData(PanelEventConstants.HERO_WAR_FLAG, nation, 0);
            return 0;
        }
    }

    public checkAllNationWarFlagRedPoint(): number {
        let count: number = 0
        count += this.checkNationWarFlagRedPoint(ItemConst.NATION_WEI);
        count += this.checkNationWarFlagRedPoint(ItemConst.NATION_SHU);
        count += this.checkNationWarFlagRedPoint(ItemConst.NATION_WU);
        count += this.checkNationWarFlagRedPoint(ItemConst.NATION_GOD);
        count += this.checkNationWarFlagRedPoint(ItemConst.NATION_DEVIL);

        RedPointMgr.ins.SetRedData(PanelEventConstants.HERO_WAR_FLAG, PanelEventConstants.HERO_WAR_FLAG, count);
        RedPointMgr.ins.SetRedData(PanelEventConstants.ALL_HERO, 0, count);
        return count;
    }

    public checkLineUpHeroBingFaRedPoint(): void {
        let heroList = HeroDataCenter.instance.hero_list.filter(function (pHero) {
            let isLineUp = LineUpDataCenter.instance.getHeroLineUpType(pHero.hero_id) == MatchConst.MATCH_TYPE_MAIN_BATTLE;
            let isOpenBingfa = pHero.star >= 6;
            return isLineUp && isOpenBingfa;
        });

        let lineup_hero_bingfa = false;
        let matchInfo = LineUpDataCenter.instance.getMatchLineUpInfoByType(MatchConst.MATCH_TYPE_MAIN_BATTLE);
        for (let multiInfo of matchInfo.list) {
            if (multiInfo) {
                for (let heroId of multiInfo.hero_id_list) {
                    if (heroId) {
                        let single_hero_bingfa_redPoint = HeroDataCenter.instance.checkHeroBingFaRedPoint(heroId);
                        if (single_hero_bingfa_redPoint) {
                            RedPointMgr.ins.SetRedData(parseInt(PanelEventConstants.HERO_INFO.toString() + heroId.toString()), heroId, 1);
                            RedPointMgr.ins.SetRedData(PanelEventConstants.HERO_INFO, HeroDataCenter.TAB_BINGFA, 1);
                            lineup_hero_bingfa = true;
                        } else {
                            RedPointMgr.ins.SetRedData(parseInt(PanelEventConstants.HERO_INFO.toString() + heroId.toString()), heroId, 0);
                            RedPointMgr.ins.SetRedData(PanelEventConstants.HERO_INFO, HeroDataCenter.TAB_BINGFA, 0);
                        }
                    }
                }
            }
        }

        if (lineup_hero_bingfa) {
            RedPointMgr.ins.SetRedData(PanelEventConstants.ACT_HERO_LIST, PanelEventConstants.HERO_INFO, 1);
        } else {
            let actHeroList = RedPointMgr.ins.IsCheckRedPoint(PanelEventConstants.HERO_INFO) ? 1 : 0;
            RedPointMgr.ins.SetRedData(PanelEventConstants.ACT_HERO_LIST, PanelEventConstants.HERO_INFO, actHeroList);
        }

        let allRedNum = RedPointMgr.ins.IsCheckRedPoint(PanelEventConstants.ACT_HERO_LIST) ? 1 : 0;
        RedPointMgr.ins.SetRedData(PanelEventConstants.ALL_HERO, PanelEventConstants.ACT_HERO_LIST, allRedNum);
    }

    public checkHeroBingFaRedPoint(hero_id: number): boolean {
        let hero_bingfa_redPoint = false;

        if (HeroDataCenter.instance.checkIsSoulHero(hero_id)) {
            let isLinkSoulHero = HeroDataCenter.instance.checkIsLinkSoulHero(hero_id);
            if (!isLinkSoulHero) {
                return false;
            }
        }

        let pHero = HeroDataCenter.instance.getHero(hero_id);
        if (pHero == undefined) {
            return false;
        }

        let isStarLimit = pHero.star >= MiscConstAuto.bingfa_slot1_star_limit;//秘法第一个槽位星级
        if (!isStarLimit) {
            hero_bingfa_redPoint = false;
            return hero_bingfa_redPoint;
        }

        for (let i: number = 0; i < ItemConst.bingfaKinds.length; i++) {
            let bingfaKind = ItemConst.bingfaKinds[i];
            if (i == 1) {
                if (pHero.star >= 11 && HeroDataCenter.instance.checkHeroBingFaKindRedPoint(hero_id, bingfaKind)) {
                    hero_bingfa_redPoint = true;
                    break;
                }
            }
            if (i == 2) {
                if (pHero.star >= 13 && HeroDataCenter.instance.checkHeroBingFaKindRedPoint(hero_id, bingfaKind)) {
                    hero_bingfa_redPoint = true;
                    break;
                }
            }
            if (i == 0) {
                if (HeroDataCenter.instance.checkHeroBingFaKindRedPoint(hero_id, bingfaKind)) {
                    hero_bingfa_redPoint = true;
                    break;
                }
            }
        }
        return hero_bingfa_redPoint;
    }

    public checkHeroBingFaKindRedPoint(hero_id: number, bingfa_kind: number): boolean {
        let bingfaExtCfgs = ConfigManager.cfg_bingfa_extBySkillLevelListCache.get(1);
        let study_bingfa_cost = false;
        for (let i: number = 0; i < bingfaExtCfgs.length; i++) {
            let bingfa_ext_cfg = bingfaExtCfgs[i];
            let bagCostVo: GoodsVO[] = GoodsManager.instance.GetGoodsByTypeId(bingfa_ext_cfg.study_cost_id);
            if (bagCostVo.length > 0) {
                study_bingfa_cost = true;
            }
        }

        let equipVo: HeroEquipVo = HeroEquipManager.instance.getHeroEquipVo(hero_id);
        let hero_info = HeroDataCenter.instance.getHero(hero_id);
        if (equipVo) {
            let bingfaEquip = equipVo.getEquipByKind(bingfa_kind);
            if (bingfaEquip == undefined && study_bingfa_cost) {
                return true;
            }
            if (bingfaEquip != undefined) {
                let skills: p_kv[] = bingfaEquip.goods.skills;
                let bingfaNextExtCfg = ConfigManager.cfg_bingfa_extCache.m_get(skills[0].val + 1, skills[0].key);
                if (bingfaNextExtCfg != undefined) {
                    let curNum = GoodsManager.instance.GetGoodsNumByTypeId(bingfaNextExtCfg.upgrade_cost_id_1);
                    let curNum2 = GoodsManager.instance.GetGoodsNumByTypeId(bingfaNextExtCfg.upgrade_cost_id_2);
                    if (curNum >= bingfaNextExtCfg.upgrade_cost_num_1 && curNum2 >= bingfaNextExtCfg.upgrade_cost_num_2 && hero_info.star >= bingfaNextExtCfg.star) {
                        let key = "bingfaLevelDialog";
                        if (this.getTodayOpened(key)) {
                            return false;
                        }
                        return true;
                    }
                }
            }
        } else {
            let pHero = HeroDataCenter.instance.getHero(hero_id);
            //sentry容错
            if (pHero && pHero.star >= 6 && study_bingfa_cost) {
                return true;
            }
        }
        return false;
    }

    public checkHeroRecycleRedPoint() {
        let canRebirth = HeroRecycleNewDataCenter.instance.checkHasCanRebirth();
        RedPointMgr.ins.SetRedData(PanelEventConstants.RECYCLE, 1, canRebirth ? 1 : 0);
    }

    public getHeroGodEquipAttr(hero_id: number): FightAttrVO[] {
        let heroInfo: p_hero = HeroDataCenter.instance.getHero(hero_id);
        let attr_map: Map<number, number> = new Map();
        for (let i: number = 0; i < 4; i++) {
            let equipVo: HeroEquipVo = HeroEquipManager.instance.getHeroEquipVo(hero_id);
            let kind = HeroDataCenter.godEquipKindList[i];
            if (equipVo) {
                let godEquip = equipVo.getEquipByKind(kind);
                if (godEquip != undefined) {
                    //基础属性
                    let typeCfg = ConfigManager.cfg_god_equip_typeCache.m_get(godEquip.kind, godEquip.color, godEquip.star);
                    if (typeCfg) {
                        if (attr_map.get(typeCfg.attr_type)) {
                            attr_map.set(typeCfg.attr_type, attr_map.get(typeCfg.attr_type) + typeCfg.attr_val)
                        } else {
                            attr_map.set(typeCfg.attr_type, typeCfg.attr_val);
                        }
                    }
                    //随机属性
                    godEquip.goods.other_vals.forEach(element => {
                        if (attr_map.get(element.key)) {
                            attr_map.set(element.key, attr_map.get(element.key) + element.val)
                        } else {
                            attr_map.set(element.key, element.val);
                        }
                    });
                }
            }
        }

        let attr_list: p_kv[] = [];

        for (let attr_key of attr_map.keys()) {
            let attrKV = new p_kv();
            attrKV.key = attr_key;
            attrKV.val = attr_map.get(attr_key);
            attr_list.push(attrKV);
        }


        // let cfgArr: fightAttr[] = ConfigManager.cfg_fightAttrByTypeCache.get(HeroDataCenter.ATTR_SHOW_TYPE_NORAML);
        // for (let i: number = 0; i < cfgArr.length; i++) {
        //     let fightVo: fightAttr = cfgArr[i];

        //     for (let i: number = 0; i < attr_list.length; i++) {
        //         let kv = attr_list[i];
        //         if(fightVo.attrID == kv.key){
        //             let num: number = FightAttrMgr.getIncValue(kv.val, heroInfo.fight[fightVo.attrKey + "_inc"]);
        //             attr_list[i].val = num;
        //         }
        //     }
        // }
        return GameUtil.parseAttrKV(attr_list);
    }

    public checkHeroEquipCastSoul(hero_id: number, equip_kind: number): any {
        let showHeroId = hero_id;
        //异能英雄装备取被同调英雄的装备
        let belinkHero = HeroDataCenter.instance.getBeLinkHero(hero_id);
        if (belinkHero) {
            showHeroId = belinkHero.hero_id;
        }
        let equipVo: HeroEquipVo = HeroEquipManager.instance.getHeroEquipVo(showHeroId);
        let isFiveHeroResonateEquip = DudufuDataCenter.instance.checkHeroIsUseFiveHeroResonateEquip(showHeroId);
        let isSoulHero = this.checkIsSoulHero(hero_id);
        if (equipVo == undefined && !isFiveHeroResonateEquip) {
            return null;
        }
        let equipCount: number = ItemConst.equinKinds.length;
        let kind: number;
        for (let i: number = 0; i < equipCount; i++) {
            kind = ItemConst.equinKinds[i];
            if (equip_kind == kind) {
                let castSoulKind = ItemConst.castSoulKinds[i];
                let castSoulGoodsVo: GoodsVO;
                let equipGoodsVo: GoodsVO;
                if (isFiveHeroResonateEquip) {
                    equipGoodsVo = GoodsVO.GetVoByTypeId(DudufuDataCenter.instance.getFiveHeroResonateEquip(equip_kind));
                    if (equipGoodsVo != undefined) {
                        equipGoodsVo.hero_id = hero_id;
                    }
                    if (equipVo != undefined) {
                        castSoulGoodsVo = equipVo.getEquipByKind(castSoulKind);
                    }
                } else {
                    equipGoodsVo = equipVo.getEquipByKind(equip_kind);
                    castSoulGoodsVo = equipVo.getEquipByKind(castSoulKind);
                }

                if (isSoulHero) {
                    let soulHeroEquipVo = HeroEquipManager.instance.getHeroEquipVo(hero_id);
                    castSoulGoodsVo = soulHeroEquipVo ? soulHeroEquipVo.getEquipByKind(castSoulKind) : null;
                }

                let hero_info: p_hero = HeroDataCenter.instance.getHero(hero_id);
                let hero_type_id = hero_info.type_id;

                if (castSoulGoodsVo != null) {
                    let obj = {
                        csvo: castSoulGoodsVo,
                        equipvo: equipGoodsVo,
                        idx: i,
                        hero_type_id: hero_type_id
                    }
                    return obj;
                } else {
                    return null;
                }
            }
        }
        return null;
    }

    //铸魂通过技能id拿到英雄装备序号
    public godEquipHeroSkillIdToEquipIndex(hero_type_id: number, skill_id: number): number {
        let cfgBase = ConfigManager.cfg_hero_baseCache.get(hero_type_id);
        let skillList = [cfgBase.skill_1, cfgBase.skill_2, cfgBase.skill_3, cfgBase.skill_4];
        for (let i: number = 0; i < skillList.length; i++) {
            if (skillList[i] == skill_id) {
                return i;
            }
        }
        return 1;
    }

    public checkHeroCanActiveCastSoul(goods: GoodsVO): boolean {
        let equipVo: HeroEquipVo = HeroEquipManager.instance.getHeroEquipVo(goods.hero_id);
        let ph: p_hero = this.getHero(goods.hero_id);
        if (ph.star < 10) {
            return false;
        }
        for (let i: number = 0; i < ItemConst.equinKinds.length; i++) {
            let kind = ItemConst.equinKinds[i];
            if (goods.kind == kind) {
                let equipGoodsVo: GoodsVO;
                let isFiveHeroResonateEquip = DudufuDataCenter.instance.checkHeroIsUseFiveHeroResonateEquip(goods.hero_id);
                let isSoulHero = this.checkIsSoulHero(goods.hero_id);
                if (isFiveHeroResonateEquip) {
                    equipGoodsVo = GoodsVO.GetVoByTypeId(DudufuDataCenter.instance.getFiveHeroResonateEquip(kind));
                    if (equipGoodsVo != undefined) {
                        equipGoodsVo.hero_id = goods.hero_id;
                    }
                } else if (isSoulHero) {
                    equipGoodsVo = goods;
                } else {
                    equipGoodsVo = equipVo.getEquipByKind(kind);
                }

                if (equipGoodsVo && (equipGoodsVo.color != 5 && !equipGoodsVo.IsDivine)) {
                    return false;
                }
                let castSoulKind = ItemConst.castSoulKinds[i];
                if (equipVo == undefined && !isFiveHeroResonateEquip && !isSoulHero) {
                    return false;
                }
                let castSoulGoodsVo: GoodsVO;
                if (equipVo != undefined) {
                    castSoulGoodsVo = equipVo.getEquipByKind(castSoulKind);
                }
                if (castSoulGoodsVo == null) {
                    let heroBaseCfg = ConfigManager.cfg_hero_baseCache.get(ph.type_id);
                    let hero_nation = heroBaseCfg.nation;
                    if (hero_nation == HeroNation.NATION_HUN) {
                        hero_nation = 0;
                    }
                    let heroList = this.getNationHero(hero_nation, 5);
                    heroList = heroList.filter(hero => {
                        return !this.isHeroLock(hero.hero_id) && !HeroDataCenter.instance.checkIsSoulHero(hero.hero_id);
                    });
                    return heroList.length > 0;
                }
            }
        }
    }

    public checkCastsoulGuide(hero_id: number): void {
        let equipVo: HeroEquipVo = HeroEquipManager.instance.getHeroEquipVo(hero_id);
        let ph: p_hero = HeroDataCenter.instance.getHero(hero_id);
        for (let i: number = 0; i < ItemConst.equinKinds.length; i++) {
            let kind = ItemConst.equinKinds[i];
            let equipGoodsVo: GoodsVO = equipVo.getEquipByKind(kind);
            if (equipGoodsVo != undefined && HeroDataCenter.instance.checkHeroCanActiveCastSoul(equipGoodsVo)) {
                GuideConst.equipcastsoulId = i;
                GuideConst.herocastsoulId = ph.type_id;
                GuideMgr.ins.setGranchGuideId(GuideConst.castsoulId);
                return;
            }
        }
    }

    public getHeroStateDesc(hero_id: number, isMain: boolean = false): string {
        let is_five_hero_resonate = DudufuDataCenter.instance.checkHeroIsFiveHeroResonate(hero_id);
        let is_lineup = LineUpDataCenter.instance.getHeroLineUpType(hero_id) > 0;
        if (isMain) {
            is_lineup = LineUpDataCenter.instance.checkLineUpMainTeam(hero_id);
        }
        let is_dudu = DudufuDataCenter.instance.checkHeroIsDudu(hero_id);
        let is_five_hero = DudufuDataCenter.instance.checkHeroIsFiveHero(hero_id);
        let is_be_link_hero = HeroDataCenter.instance.checkIsBeLinkHero(hero_id)
        let desc = "";
        if (is_lineup) {
            desc += isMain ? window.iLang.L2_ch23_SHANG_ZHEN_ZHU_DUI_ch24.il() : window.iLang.L2_ch23_SHANG_ZHEN_ch24.il();
        }
        desc += is_five_hero_resonate ? window.iLang.L2_ch23_GONG_MING_ch24.il() : "";
        desc += is_dudu ? window.iLang.L2_ch23_REN_MING_ch24.il() : "";
        desc += is_five_hero ? window.iLang.L2_ch23_GONG_MING_HERO_ch24.il() : "";
        desc += is_be_link_hero ? window.iLang.L2_ch23_YI_NENG_HERO_LIAN_JIE_ch24.il() : "";
        return desc;
    }

    public checkHeroStatus(): void {

    }

    static checkIsHeroStarUpGrowthOpen(heroList: PHero[]) {
        let key: string = SettingDataCenter.IS_HERO_STAR_UPGROWTH_OPEN;
        let num: number = SettingDataCenter.instance.getVal(key);
        if (num == 1) {
            // this.setHeroStarUpGrowthOpen(true);
        } else {
            let isOpen = false;
            for (let i = 0; i < heroList.length; i++) {
                if (heroList[i].star >= 9) {
                    this.setHeroStarUpGrowthOpen(true);
                    break;
                }
            }
            this.setHeroStarUpGrowthOpen(false);
        }
    }

    static setHeroStarUpGrowthOpen(isOpen: boolean = false): void {
        let key: string = SettingDataCenter.IS_HERO_STAR_UPGROWTH_OPEN;
        let val: number = isOpen ? 1 : 0
        SettingDataCenter.instance.updateSetting(key, val);
        isOpen && SettingDataCenter.instance.m_role_setting_tos(key, val);
    }

    /**检测异能英雄是否开启 */
    public checkSoulHeroOpen(): boolean {
        if (MiscConst.soul_hero_open_tag == 0) {
            return false;
        }
        if (MiscConst.soul_hero_not_open_platform.indexOf(GlobalConfig.PlatName) >= 0) {
            return false;
        }
        return this.isHadSoulHero || GameUtil.isSysOpen(PanelEventConstants.SOUL_HERO, 0, false);
    }

    /**检测是否已激活精灵 */
    public checkIsActiveWing(hero_id: number): boolean {
        return HeroDataCenter.instance.getHeroWingLv(hero_id) > 0;
    }

    /**检测是否可激活14星升星 */
    public checkIsCanActiveUpStar14(hero_id: number): boolean {
        let openDay = MiscConst.hero_fourteen_star_limit[0];
        let wingLv = MiscConst.hero_fourteen_star_limit[1];
        return DataCenter.serverOpenedDay >= openDay && HeroDataCenter.instance.getHeroWingLv(hero_id) >= wingLv;
    }

    /**检测激活终极升星，是否拥有足够数量的材料 */
    public checkIsEnoughActiveUpStar14(type_id: number): boolean {
        let heroCfg = ConfigManager.cfg_hero_baseCache.get(type_id);
        let itemId = 0, itemNum = 0, hasCount = 0;
        if (HeroConsts.SAN_GUO_NATIONS.indexOf(heroCfg.nation) >= 0) {
            itemId = MiscConst.hero_fourteen_star_act_cost_1[0];
            itemNum = MiscConst.hero_fourteen_star_act_cost_1[1];
            hasCount = itemId ? GoodsManager.instance.GetGoodsNumByTypeId(itemId) : 0;
        } else if (HeroConsts.SHEN_MO_NATIONS.indexOf(heroCfg.nation) >= 0) {
            itemId = MiscConst.hero_fourteen_star_act_cost_2[0];
            itemNum = MiscConst.hero_fourteen_star_act_cost_2[1];
            hasCount = itemId ? GoodsManager.instance.GetGoodsNumByTypeId(itemId) : 0;
        }
        if (MiscConstAuto.sanxi_hero_fourteen_star_act_cost_2_list.indexOf(type_id.toString()) >= 0) {
            itemNum = MiscConst.hero_fourteen_star_act_cost_2[1];
        }
        return hasCount >= itemNum;
    }

    /**检测英雄是否已激活14星升星 */
    public checkIsActiveUpStar14(hero_id: number): boolean {
        if (this.fourteenInfo) {
            return this.fourteenInfo.fourteen_hero_ids.indexOf(hero_id) >= 0;
        }
        return false;
    }

    /**是否有英雄激活过14星升星 */
    public isHadActiveUpStar14(): boolean {
        if (this.fourteenInfo) {
            return this.fourteenInfo.fourteen_hero_ids.length > 0;
        }
        return false;
    }

    /**当前英雄背包中的英雄数量 */
    public getCurBagHeroNum(): number {
        //背包英雄数量需要加上英雄助阵的上阵英雄数量
        let cur_hero_cheer_num = HeroCheerDataCenter.instance.getHeroCheerNum();
        let cur_hero_num = HeroDataCenter.instance.hero_list.length + cur_hero_cheer_num;
        return cur_hero_num
    }

    //=========== 协议 ===========//

    /**提升类型：1=升级，2=升阶，3=升星，4=星阶*/
    public m_hero_upgrade_tos(hero_id: number, reinforce_type: number, up_val: number = 1, is_auto: boolean = false, hero_list: number[] = [], cost_list: p_item[] = []): void {
        let vo: m_hero_upgrade_tos = new m_hero_upgrade_tos();
        vo.hero_id = hero_id;
        vo.up_type = reinforce_type;
        vo.up_val = up_val;
        vo.is_auto = is_auto;
        vo.hero_ids = hero_list;
        vo.cost_list = cost_list;
        Connection.instance.sendMessage(vo);

    }

    public m_item_compose_tos(goods_id: number, compose_cnt: number = 1, compose_type: number = 2): void {
        let vo: m_item_compose_tos = new m_item_compose_tos();
        vo.goods_ids = [goods_id];
        vo.compose_cnt = compose_cnt;
        vo.compose_type = compose_type

        Connection.instance.sendMessage(vo);
    }

    public m_equip_bingfa_op_tos(op_type: number, hero_id: number, goods_id: number, target_skill_id: number = 1): void {
        let vo: m_equip_bingfa_op_tos = new m_equip_bingfa_op_tos();//操作类型： 1：遗忘：2：重置，3：升级，4：置换
        vo.op_type = op_type;
        vo.hero_id = hero_id;
        vo.goods_id = goods_id;
        vo.target_skill_id = target_skill_id;
        Connection.instance.sendMessage(vo);
    }

    public m_equip_bingfa_load_tos(hero_id: number, skill_id: number, slot: number): void {
        let vo: m_equip_bingfa_load_tos = new m_equip_bingfa_load_tos();
        vo.hero_id = hero_id;
        vo.skill_id = skill_id;
        vo.slot = slot;
        Connection.instance.sendMessage(vo);
    }

    public m_equip_bingfa_exchange_tos(hero_id: number, hero_id2: number, is_half_price: boolean = false): void {
        let vo: m_equip_bingfa_exchange_tos = new m_equip_bingfa_exchange_tos();
        vo.hero_id = hero_id;
        vo.hero_id2 = hero_id2;
        vo.is_half_price = is_half_price;
        Connection.instance.sendMessage(vo);
    }

    public m_equip_auto_load_tos(hero_id: number): void {
        let vo: m_equip_auto_load_tos = new m_equip_auto_load_tos();
        vo.hero_id = hero_id;

        Connection.instance.sendMessage(vo);
    }

    public m_equip_auto_unload_tos(hero_id: number): void {
        let vo: m_equip_auto_unload_tos = new m_equip_auto_unload_tos();
        vo.hero_id = hero_id;
        Connection.instance.sendMessage(vo);
    }

    /**
     * 英雄重生
     */
    public m_hero_recycle_tos_req(hero_id: number): void {
        let vo: m_hero_recycle_tos = new m_hero_recycle_tos();
        vo.hero_id = hero_id;
        Connection.instance.sendMessage(vo);
    }

    public m_hero_recycle_preview_tos(hero_id: number): void {
        let vo: m_hero_recycle_preview_tos = new m_hero_recycle_preview_tos();
        vo.hero_id = hero_id;
        Connection.instance.sendMessage(vo);
    }

    /**英雄锁定解锁 */
    public m_hero_lock_tos(op_type: number, hero_id: number, is_lock: boolean): void {
        let vo: m_hero_lock_tos = new m_hero_lock_tos();
        vo.op_type = op_type;
        vo.hero_id = hero_id;
        vo.is_lock = is_lock;
        Connection.instance.sendMessage(vo);
    }

    public m_hero_bag_expansion_tos(): void {
        let vo: m_hero_bag_expansion_tos = new m_hero_bag_expansion_tos();
        Connection.instance.sendMessage(vo);
    }

    public m_hero_my_rank_tos(): void {
        let vo: m_hero_my_rank_tos = new m_hero_my_rank_tos();
        Connection.instance.sendMessage(vo);
    }

    public m_war_flag_active_tos(nation: number, hero_id: number, cost_list: p_item[]): void {
        let vo: m_war_flag_active_tos = new m_war_flag_active_tos();
        vo.nation = nation;
        vo.hero_id = hero_id;
        vo.cost_list = cost_list;
        Connection.instance.sendMessage(vo);
    }

    public m_war_flag_op_tos(nation: number, op_type: number): void {
        let vo: m_war_flag_op_tos = new m_war_flag_op_tos();
        vo.nation = nation;
        vo.op_type = op_type;//1升级，2升阶，3进阶重置
        Connection.instance.sendMessage(vo);
    }

    public m_war_flag_link_tos(op_type: number, nation: number, target: number): void {
        let vo: m_war_flag_link_tos = new m_war_flag_link_tos();
        vo.nation = nation;
        vo.op_type = op_type;//1链接，2解除, 3链接战力，4解除链接战力
        vo.target = target;
        Connection.instance.sendMessage(vo);
    }

    public m_war_flag_exchange_tos(nation: number, target: number): void {
        let vo: m_war_flag_exchange_tos = new m_war_flag_exchange_tos();
        vo.nation = nation;
        vo.target = target;
        Connection.instance.sendMessage(vo);
    }

    public m_god_equip_recast_tos(op_type: number, hero_id: number, goods_id: number, lock_ids: number[]): void {
        let vo: m_god_equip_recast_tos = new m_god_equip_recast_tos();
        vo.op_type = op_type;//操作类型： 1=洗练，2=保存
        vo.hero_id = hero_id;//0 表示是背包里面的纹章
        vo.goods_id = goods_id;//重铸的神装物品id
        vo.lock_ids = lock_ids;//锁定的属性，[1,2]
        Connection.instance.sendMessage(vo);
    }

    public m_god_equip_compose_tos(goods_ids: number[], lock_attr: p_kv[]): void {
        let vo: m_god_equip_compose_tos = new m_god_equip_compose_tos();
        vo.goods_ids = goods_ids;// 合成的Ids
        vo.lock_attr = lock_attr;// 选择继承的随机属性
        Connection.instance.sendMessage(vo);
    }

    public m_casting_soul_op_tos(op_type: number, hero_id: number, item_kind: number, cost_hero_id: number, cost_list: p_item[] = []): void {
        let vo: m_casting_soul_op_tos = new m_casting_soul_op_tos();
        vo.op_type = op_type;
        vo.hero_id = hero_id;
        vo.item_kind = item_kind;
        vo.cost_hero_id = cost_hero_id;
        vo.cost_list = cost_list;
        Connection.instance.sendMessage(vo);
    }

    public m_soul_hero_link_tos(type_id: number, hero_id: number, link_hero_id: number): void {
        let vo: m_soul_hero_link_tos = new m_soul_hero_link_tos();
        vo.type = type_id;
        vo.hero_id = hero_id;
        vo.link_hero_id = link_hero_id;
        Connection.instance.sendMessage(vo);
    }

    public m_soul_hero_reset_tos(hero_id: number): void {
        let vo: m_soul_hero_reset_tos = new m_soul_hero_reset_tos();
        vo.hero_id = hero_id;
        Connection.instance.sendMessage(vo);
    }

    public m_soul_hero_unlock_tos(hero_id: number, hero_list: number[] = [], cost_list: p_item[] = []): void {
        let vo: m_soul_hero_unlock_tos = new m_soul_hero_unlock_tos();
        vo.hero_id = hero_id;
        vo.cost_hero_ids = hero_list;
        vo.cost_list = cost_list;
        Connection.instance.sendMessage(vo);
    }

    public m_hero_act_fourteen_tos(hero_id: number): void {
        let vo = new m_hero_act_fourteen_tos();
        vo.hero_id = hero_id;
        Connection.instance.sendMessage(vo);
    }

    public m_hero_evolve_op_tos(hero_id: number, cost_hero_ids: number[], cost_list: p_item[]): void {
        let vo = new m_hero_evolve_op_tos();
        vo.hero_id = hero_id;
        vo.cost_hero_ids = cost_hero_ids;
        vo.cost_list = cost_list;
        Connection.instance.sendMessage(vo);
    }
}
