import { Laya } from "Laya";
import { Event } from "laya/events/Event";
import { Point } from "laya/maths/Point";
import { Image } from "laya/ui/Image";
import { Ease } from "laya/utils/Ease";
import { Handler } from "laya/utils/Handler";
import { GlobalConfig } from "../../../../game/GlobalConfig";
import { UrlConfig } from "../../../../game/UrlConfig";
import { EBuildingType } from "../../../auto/ConstAuto";
import { cfg_building } from "../../../cfg/vo/cfg_building";
import { m_building_op_toc } from "../../../proto/line/m_building_op_toc";
import { com } from "../../../ui/layaMaxUI";
import { GameUtil } from "../../../util/GameUtil";
import { MathUtil2 } from "../../../util/MathUtil2";
import { TipsUtil } from "../../../util/TipsUtil";
import { UIUtil } from "../../../util/UIUtil";
import { DragAndDropScript } from "../../baseModules/scripts/DragAndDropScript";
import { ZoomControlScript } from "../../baseModules/scripts/ZoomControlScript";
import { GuideMgr } from "../../guide/GuideMgr";
import { LordDataCenter } from "../../Lord/data/LordDataCenter";
import { ModuleCommand } from "../../ModuleCommand";
import { BUILD_OP, GuaJiBuildDataCenter } from "../data/GuaJiBuildDataCenter";
import { GuajiBuildEditorView } from "../editor/GuajiBuildEditorView";
import { GuajiBuildPathPointVo } from "../vo/GuajiBuildPathPointVo";
import { GuajiBuildVo } from "../vo/GuajiBuildVo";
import GuaJiBuildItem from "./GuaJiBuildItem";
import { GuaJiDataCenter } from "../../guaJi/data/GuaJiDataCenter";
import { BaseDialog } from "../../BaseDialog";
import { GuajiBuildWorker } from "../game/GuajiBuildWorker";
import { LayerManager } from "../../../managers/LayerManager";

export default class GuaJiBuildView extends com.ui.res.guaJiBuild.GuaJiBuildViewUI {
    private dragScript: DragAndDropScript;
    private zoomScript: ZoomControlScript;
    public editor: GuajiBuildEditorView;
    public buildItemMap: Map<number, GuaJiBuildItem>;
    private flyImgList: Image[] = [];
    constructor() {
        super();
    }

    public get boxMap(){
        return this.box;
    }
    public get isMapLoaded(){
        // return this.boxMap.width > 0 && this.buildItemMap.size > 0;
        return this.buildItemMap.size > 0;
    }

    onOpen(param: any): void {
    }

    initData(param: any) {
        this.buildItemMap = new Map();
    }

    initUI(): void {
        this.resize();
        this.loadMainBg();
        // this.initBuildShow();
    }

    checkGuide() {
        
        let isGuideAllFinish = GuaJiBuildDataCenter.instance.isGuideAllFinish;
        if (this.dragScript){
            this.dragScript.enabled = isGuideAllFinish || this.editor?.visible;
        }
        if (this.zoomScript){
            this.zoomScript.enabled = isGuideAllFinish || this.editor?.visible;
        }
    }

    private loadMainBg(){
        // let bg_sub_name = this.isMainCityBgName();
        // if (GlobalConfig.isYeGame) {
        //     this.loadMainCityBg(this.cityBg, "guaji_bg", bg_sub_name);
        //     this.loadMainCityBg(this.cityBg2, "guaji_bg", bg_sub_name);
        //     this.cityBg.y += -200;
        //     this.cityBg2.y += -300;
        // } else {
        //     this.loadMainCityBg(this.cityBg, "guaji_bg", bg_sub_name);
        // }
        // this.loadMainCityBg(this.cityBg, "guaji_bg", bg_sub_name);

        let mapImgUrl = GuaJiBuildDataCenter.instance.getMapImageUrl();
        this.setFullBg(this.cityBg, mapImgUrl);

        if(this.cityBg.width == 0){
            this.cityBg.once(Laya.Event.LOADED, this, this.onMapLoaded);
            this.timer.once(10000, this, this.onMapLoadTimeOut);
        }else{
            this.onMapLoaded();
        }
    }

    private onMapLoadTimeOut(){
        if(this.boxMap.width == 0){
            this.onMapLoaded();
        }
    }

    private onMapLoaded():void{
        let miscVo = GuaJiBuildDataCenter.instance.saveMiscVoMap;
        let mapScale = miscVo.map_scale;
        
        let width = this.cityBg.width || miscVo.map_width;
        let height = this.cityBg.height || miscVo.map_height;

        this.boxMap.size(width, height);
        this.boxMap.scale(mapScale, mapScale);

        this.dragScript = this.boxMap.getOrAddComponent(DragAndDropScript);
        this.dragScript.setAttrs(true);
        this.boxMap.on(Laya.Event.DRAG_MOVE, this, this.onMapDragMove);

        let widthTimes = this.relativeWidth / width;
        let heightTimes = this.relativeHeight / height;

        let scale = Math.max(widthTimes, heightTimes);
        var minScale = scale;
        var maxScale = 3;
        this.zoomScript = this.boxMap.getOrAddComponent(ZoomControlScript);
        this.zoomScript.setAttr(minScale, maxScale, 0.004);
        this.zoomScript.on(Event.CHANGE, this, this.onMapDragMove);

        this.initBuildShow();
        this.checkGuide();

        let pos = window.MainUIManager.uiSaveData.get(this.name + "_map_pos") as string;
        if(pos){
            let xy = pos.split(",");
            this.boxMap.pos(+xy[0], +xy[1]);
            this.onMapDragMove();
            // let toPos = Point.create().setTo(+xy[0], +xy[1]);
            // this.moveToMapPos(toPos, 9999);
        }else{
            this.moveToBuilding(EBuildingType.BUILD_TYPE_BASE, 9999);
        }

        //测试
        // this.timer.once(1000, this, ()=>{
        //    this.moveToBuilding(1, 1);
        // });
    }

    private onMapDragMove() {

        if (GlobalConfig.IsDebug) {
            let script = this.boxMap.getOrAddComponent(ZoomControlScript);
            let widthTimes = this.relativeWidth / this.boxMap.width;
            let heightTimes = this.relativeHeight / this.boxMap.height;
            if (this.editor?.visible) {
                var minScale = 0.3;
                var maxScale = 3;
            } else {
                let scale = Math.max(widthTimes, heightTimes);
                var minScale = scale;
                var maxScale = 3;
            }

            script.setAttr(minScale, maxScale, 0.004);
        }

        UIUtil.setFixDragRange(this.boxMap, this.relativeWidth, this.relativeHeight);
    }

    public moveToBuilding(buildType: number, moveSpeed: number = 1, callback: Handler = null): void {
        let builditem = this.buildItemMap.get(buildType);
        if (builditem) {
            let toPos = Point.create().setTo(builditem.x + builditem.width / 2, builditem.y + builditem.height / 2);
            let toScale = GuaJiBuildDataCenter.instance.saveMiscVoMap.map_scale;
            this.moveToMapPos(toPos, moveSpeed, toScale, callback);
        }
    }

    /**
     * 
     * @param pos 基于this.boxMap的位置
     * @param moveSpeed 移动速度
     * @param toScale this.boxMap放大到多少倍
     * @param callback 完成回调
     */
    public moveToMapPos(pos: Point, moveSpeed: number = 1, toScale:number = 1, callback: Handler = null):void{

        //移动box, 让建筑居中, 先确保scale = 1;
        // this.boxMap.scale(1, 1);
        let mapScale = GuaJiBuildDataCenter.instance.saveMiscVoMap.map_scale;
        let mapWidth = this.boxMap.width * mapScale;
        let mapHeight = this.boxMap.height * mapScale;
        let minX = (Laya.stage.width - mapWidth);
        let minY = (Laya.stage.height - mapHeight);

        let fromPos = this.parentPointToGlobal(Point.create().setTo(this.boxMap.x, this.boxMap.y));

        let toStageCenterPos = Point.create().setTo(
            Laya.stage.width / 2 - pos.x * toScale,
            Laya.stage.height / 2 - pos.y * toScale
        );

        //位移到中点
        // let toStageCenterPos = Point.create().setTo(Laya.stage.width / 2 - pos.x, Laya.stage.height / 2 - pos.y);
        let deltaX = toStageCenterPos.x - fromPos.x;
        let deltaY = toStageCenterPos.y - fromPos.y;
        let boxToX = this.boxMap.x + deltaX;
        let boxToY = this.boxMap.y + deltaY;

        boxToX = MathUtil2.Clamp(boxToX, minX, 0);
        boxToY = MathUtil2.Clamp(boxToY, minY, 0);

        let duration = Math.max(Math.abs(deltaX), Math.abs(deltaY)) / moveSpeed;
        this.boxMap._tweenTo({ x: boxToX, y: boxToY, scale: toScale }, duration, Ease.quadOut, Handler.create(this, this._onMoveToBuildComplete, [callback]));

    }

    private _onMoveToBuildComplete(callback: Handler):void{
        this.onMapDragMove();
        callback && callback.run();
    }

    addClick(): void {

    }

    addEvent(): void {
        this.addEventListener(ModuleCommand.GUAJI_BUILD_UPDATE, this, this.refreshBuildShow);
        this.addEventListener(ModuleCommand.GUAJI_BUILD_INFO, this, this.initBuildShow);
        this.addEventListener(ModuleCommand.CLOSE_LORD_CHANGE_DIALOG, this, this.changeLord);
        this.addEventListener(ModuleCommand.GUAJI_BUILD_WORKER_PATH_END, this, this.showGainItemFly);
    }
    
    ///-------------显示-------------------

    private changeLord() {
        this.loadMainBg();
    }

    private showGainItemFly(worker: GuajiBuildWorker){
        let cfg: cfg_building = worker.editorVo.cfg
        let lastPoint = worker.lastPathPoint;
        if (cfg && cfg.item_res>0) {
            let point: GuajiBuildPathPointVo = lastPoint;
            //tween 显示图片, 然后隐藏
            let img = new Image();
            img.skin = GameUtil.getItemIconUrl(cfg.item_res)
            img.pos(point.targetX, point.targetY - 50);
            img.scale(0.5, 0.5);
            img.zOrder = LayerManager.STAGE_ZORDE_TOPEST;
            this.boxMap.addChild(img);
            this.flyImgList.push(img);

            // img._tweenTo({ y: point.targetY - 200,alpha:0 }, 1500, Ease.quadOut, Handler.create(this, (img)=>{
            //     //从flyImgList中删除
            //     this.flyImgList.splice(this.flyImgList.indexOf(img), 1);
            //     img.destroy();
            // }, [img]));

            img._tweenTo({ y: point.targetY - 100 }, 800, Ease.backOut, null);
            img._tweenTo({ alpha: 0 }, 500, Ease.quadOut, Handler.create(this, (img) => {
                //从flyImgList中删除
                this.flyImgList.splice(this.flyImgList.indexOf(img), 1);
                img.destroy();
            }, [img]), 600);
        }
    }


    // private loadMainCityBg(img: Image, bg_name: string, bg_sub_name: string): void {
    //     bg_name = bg_name + bg_sub_name + ".jpg";
    //     this.setFullBg(img, UrlConfig.GUAJI_MAP_URL + bg_name);
    // }

    resize(): void {
        this.height = this.relativeHeight;
        // this.y += this.offsetY;
        this.y = 0;
    } 

    private initBuildShow(): void {
        let buildvolist = GuaJiBuildDataCenter.instance.getBuildVoList();
        buildvolist.forEach((vo) => {
            let builditem: GuaJiBuildItem;
            if (this.buildItemMap.has(vo.buildType)) {
                builditem = this.buildItemMap.get(vo.buildType);
            } else {
                builditem = new GuaJiBuildItem();
                builditem.initShow(vo, this.boxMap);
                builditem.name = GuaJiBuildDataCenter.BUILDING_ITEM_NAME + vo.buildType;
                builditem.mouseEnabled = true;
                builditem.zOrder = builditem.y;
                this.buildItemMap.set(vo.buildType, builditem);
                this.addOnClick(this, builditem, this.OnBuildClick, Event.CLICK, [vo]);
                this.boxMap.addChild(builditem);
            }
            builditem.setData(vo);
        })
    }

    private refreshBuildShow(msg: m_building_op_toc): void {
        let buildvo = GuaJiBuildDataCenter.instance.getBuildVoByType(msg.building_id);
        if (this.buildItemMap.has(buildvo.buildType)) {
            let builditem = this.buildItemMap.get(buildvo.buildType);
            builditem.refreshBuildShow(buildvo, msg.op_type);
        }
        //刷新可建造图片
        for (const [type,item] of this.buildItemMap) {
            const otherbuildvo = GuaJiBuildDataCenter.instance.getBuildVoByType(type);
            if (item && otherbuildvo) {
                if (otherbuildvo.buildLv != 0) {
                    item.refImgUpgrade();
                }
                else{
                    item.refImgConstruction();
                }
            }
        }
    }
    public updateFrame(interval: number): void {
        this.buildItemMap.forEach(item => {
            item.updateFrame(interval);
        })
    }

    //------------------数据------------------
    // private isMainCityBgName(): string {
    //     return "_" + LordDataCenter.instance.camp;
    // }

    //-------------------点击------------------
    OnBuildClick(buildVo: GuajiBuildVo): void {

        let isGuideAllFinish = GuaJiBuildDataCenter.instance.isGuideAllFinish;
        if (isGuideAllFinish == false && GuideMgr.ins.curGuideHelper?.length){
            let preStep = GuideMgr.ins.curStep - 1;
            let cfg = GuideMgr.ins.curGuideHelper[preStep];
            let building = this.buildItemMap.get(buildVo.buildType);
            if (cfg && cfg.btn_name && cfg.btn_name._has(building.name) == false){
                TipsUtil.showTips3("请先完成引导!");
                return;
            }
        }
        // let guajiDialog = BaseDialog.getTopShowDialog();
        // if(guajiDialog && guajiDialog.guideTar != this){

        // }

        const tips = GuaJiBuildDataCenter.instance.getBuildCanUnLockStr(buildVo.buildType);
        const isUnlock = GuaJiBuildDataCenter.instance.getBuildCanUnlock(buildVo.buildType);
        if (!isUnlock) {
            TipsUtil.showTips(tips);
            return;
        }
        if (!buildVo.pvo) {
            GuaJiBuildDataCenter.instance.m_building_op_tos(BUILD_OP.CREATE, buildVo.buildType);
        } else {
            this.dispatchEvent(ModuleCommand.OPEN_GUAJI_BUILD_UP_DIALOG, { buildType:buildVo.buildType, buildView:this });
        }
    }

    destroy(destroyChild: boolean = true): void {
        let strPos = this.boxMap.x + "," + this.boxMap.y;
        window.MainUIManager.uiSaveData.set(this.name + "_map_pos", strPos);
        super.destroy(destroyChild);
    }
}
