import { UrlConfig } from "../../../../game/UrlConfig";
import { ConfigManager } from "../../../managers/ConfigManager";
import { LayerManager } from "../../../managers/LayerManager";
import { com } from "../../../ui/layaMaxUI";
import { GameUtil } from "../../../util/GameUtil";
import { HtmlUtil } from "../../../util/HtmlUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import UITab from "../../baseModules/UITab";
import { GameConst } from "../../GameConst";
import { LineUpConst } from "../../lineUp/data/LineUpConst";
import { LineUpTabVO, LineUpVO } from "../../lineUp/vo/LineUpVO";
import { MiscConst } from "../../misc_config/MiscConst";
import { PanelEventConstants } from "../../PanelEventConstants";
import { RedPointMgr } from "../../redPoint/RedPointMgr";
import { HeroDataCenter } from "../data/HeroDataCenter";
import HeroActListView from "../view/list/HeroActListView";
import { HeroTuJianListView } from "../view/list/HeroTuJianListView";
import { DataCenter } from "../../DataCenter";
import { MatchConst } from "../../../auto/ConstAuto";
import UIFactory from "../../../util/UIFactory";
import { HeroSkinDataCenter } from "../../heroSkin/data/HeroSkinDataCenter";
import { MiscConstAuto } from "../../../auto/MiscConstAuto";
import { UIUtil } from "../../../util/UIUtil";
//界面类都是每次打开每次新建的
export default class HeroActListDialog extends com.ui.res.hero.HeroActListDialogUI {
    constructor() {
        super();
        this.resName = [UrlConfig.HERO_INFO_RES];
        this.isModal = false;
        //先不用单例了, HeroActListItem监听太多事件,并且使用太多图片了
        this.isSingle = false;
        this._lockResName = true;
        //有大底图的都要单独加载，不要打包到图集
    }
    getClassName() {
        return "HeroActListDialog";
    }
    /**获取界面顶部展示的道具id，子类可重写此方法自定义展示其他道具 */
    getShowItemID1() {
        return GameConst.ITEM_TYPE_SILVER;
    }
    /**获取界面顶部展示的道具id，子类可重写此方法自定义展示其他道具 */
    getShowItemID2() {
        return GameConst.ITEM_TYPE_GOLD;
    }
    onOpen(param) {
        let idx = 0;
        if (param && param.child_id) {
            idx = this._uiTab.getIdToIndex(param.child_id);
        }
        this._uiTab.selectedIndex = idx;
        this.isShowBigBg = true;
        this.mouseThrough = false;
        this.UpdateBagCapacity();
        HeroDataCenter.instance.checkAllNationWarFlagRedPoint();
        this.checkRedPoint();
        this.initParam();
        this.updateBtnState();
    }
    afterOpen() {
        super.afterOpen();
        this.dispatchEvent("CLOSE_LAST_OPEN_MAIN" /* CLOSE_LAST_OPEN_MAIN */);
    }
    afterFront() {
        super.afterFront();
        this.dispatchEvent("CLOSE_LAST_OPEN_MAIN" /* CLOSE_LAST_OPEN_MAIN */);
    }
    initParam() {
        this.SuperzOrder(LayerManager.STAGE_ZORDE_BOTTOM);
    }
    initUI() {
        //减少text渲染打断
        this.drawCallOptimize = true;
        this.height = this.relativeHeight;
        this.y = 0;
        this._uiTab = UIFactory.createUITab_H(this, this.p_tab, {
            itemSkin: UIFactory.newCommonTabSkin1,
            onSelect: this.OnSeletcMenuTab,
            isSelectOne: false,
            subViewParent: this.viewBox
        });
        this._uiTab.SetSpace(8, 0);
        this._uiTab.SetPadding(15, 0, 0, 0);
        let tabDatas = [];
        tabDatas.push(UITab.GetItemData(PanelEventConstants.ACT_HERO_LIST, window.iLang.L2_YING_ch11_XIONG.il(), 0, HeroActListView, "v2_common/tab_01.png", PanelEventConstants.ALL_HERO));
        tabDatas.push(UITab.GetItemData(PanelEventConstants.HERO_TUJIAN_LIST, window.iLang.L2_TU_ch11_JIAN.il(), 0, HeroTuJianListView, "v2_common/tab_01.png", PanelEventConstants.ALL_HERO));
        //隐藏英雄界面的战旗和龙魂
        // tabDatas.push(UITab.GetItemData(1, window.iLang.L2_ZHAN_ch11_QI.il(), 1, HeroWarFlagView, "", PanelEventConstants.HERO_WAR_FLAG, PanelEventConstants.HERO_WAR_FLAG));
        //FIXME 暂时先隐藏
        //  if(DataCenter.myLevel>=80){
        //   let isOpenHandler = Handler.create(this,()=> GameUtil.isSysOpen(PanelEventConstants.QI_MOU),null,false);
        //   tabDatas.push(UITab.GetItemData(PanelEventConstants.QI_MOU, window.iLang.L2_LONG_ch11_HUN.il(), this._param, QiMouView, "", PanelEventConstants.ALL_HERO, PanelEventConstants.QI_MOU).setResName(UrlConfig.QI_MOU_RES).setViewPos(-this.viewBox.x,-this.viewBox.y).setCustomOpenFun(isOpenHandler));
        // } 
        this._uiTab.array = tabDatas;
        this._btnList = [
            this.strong,
            this.allskin,
            this.changeskin
        ];
    }
    layoutButtons() {
        // 清除之前布局
        //   this.btnBox.removeChildren();
        //   const spacing = 68; // 按钮之间的间距
        //   let visibleButtons = this._btnList.filter(btn => btn.visible); // 只处理可见按钮
        //   let x = this.btnBox.x -30;
        //   for (let i = 0; i < visibleButtons.length; i++) {
        //     let btn = visibleButtons[i];
        //     btn.x = x;
        //     x -= spacing;
        //     this.btnBox.addChild(btn); // 直接添加到当前容器
        // }
        UIUtil.layoutItems(this.btnBox, this._btnList, { rtl: true, singleRow: true, itemWidth: 68 });
    }
    addEvent() {
        this.addEventListener("UPDATE_HERO_BAG_EXPANSION" /* UPDATE_HERO_BAG_EXPANSION */, this, this.UpdateBagCapacity);
        this.addEventListener("REFRESH_HERO_LIST" /* REFRESH_HERO_LIST */, this, this.UpdateBagCapacity);
        this.addEventListener("RED_CHILD_CHANGE" /* RED_CHILD_CHANGE */, this, this.checkRedPoint);
        this.addEventListener("CHANGE_SUB_TAB" /* CHANGE_SUB_TAB */, this, this.changeSubTab);
    }
    addClick() {
        this.addOnClick(this, this.expansionBtn, this.onClickExpansionBtn);
        this.addOnClick(this, this.lineUpBtn, this.onClickLineUpBtn);
        this.addOnClick(this, this.strong, this.onClickStrong);
        this.addOnClick(this, this.allskin, this.onClickallsin);
        this.addOnClick(this, this.changeskin, this.onClickchangeskin);
    }
    updateBtnState() {
        if (HeroSkinDataCenter.instance.activeList && HeroSkinDataCenter.instance.activeList.length >= 1) {
            this.allskin.visible = true;
        }
        else {
            this.allskin.visible = false;
        }
        //皮肤兑换按钮
        this.changeskin.visible = (DataCenter.myLevel >= MiscConstAuto.show_change_skin_btn_level);
        let vo = RedPointMgr.ins.getRedVo(PanelEventConstants.STONG, 1);
        this.checkRedPoint(vo);
        this.layoutButtons();
    }
    onClickchangeskin() {
        this.dispatchEvent("OPEN_HERO_RECYCLE_NEW_DIALOG" /* OPEN_HERO_RECYCLE_NEW_DIALOG */, 3);
    }
    onClickallsin() {
        this.dispatchEvent("OPEN_SHOWSKIN_DIALOG" /* OPEN_SHOWSKIN_DIALOG */);
    }
    onClickStrong() {
        this.dispatchEvent("OPEN_STRONG_DIALOG" /* OPEN_STRONG_DIALOG */);
    }
    OnSeletcMenuTab(uitabData) {
        this.capacity_bg.visible = uitabData.childId == PanelEventConstants.ACT_HERO_LIST;
        this.dispatchEvent("WAR_FLAG_SELECT_TAB" /* WAR_FLAG_SELECT_TAB */);
    }
    onClickExpansionBtn() {
        if (HeroDataCenter.instance.isCanExpansionHeroBag(true)) {
            let max_count = HeroDataCenter.instance.getMaxExpansionCount();
            let cur_count = HeroDataCenter.instance.expansionCount;
            let next_count = Math.min(cur_count + 1, max_count);
            let cur_hero_bag_cfg = ConfigManager.cfg_hero_bagCache.get(cur_count);
            let next_hero_bag_cfg = ConfigManager.cfg_hero_bagCache.get(next_count);
            let item_cfg = ConfigManager.cfg_itemCache.get(next_hero_bag_cfg.item_id);
            let item_icon_str = HtmlUtil.GetHtmlImg(UrlConfig.getGoodsIcon(item_cfg), 40, 40);
            let desc = window.iLang.L2_SHI_FOU_HUA_FEI_P0_P1_KUO_CHONG_P2_GE_HERO_WEI_ZHI_SHU_LIANG_SHANG_XIAN.il([item_icon_str, GameUtil.gold(next_hero_bag_cfg.item_num), next_hero_bag_cfg.capacity - cur_hero_bag_cfg.capacity]);
            TipsUtil.showDialog(this, desc, window.iLang.L2_TI_SHI.il(), function () {
                HeroDataCenter.instance.m_hero_bag_expansion_tos();
            });
        }
    }
    onClickLineUpBtn() {
        let vo = new LineUpVO();
        vo.isCloseAfterSave = false;
        vo.isShowRedPoint = true;
        vo._tabDatas = [];
        let matchTypeList = MiscConst.line_up_tab_list;
        for (let i = 0; i < matchTypeList.length; ++i) {
            let matchType = matchTypeList[i];
            let cfg = ConfigManager.cfg_match_typeCache.get(matchType);
            let lineUpName = cfg.match_name;
            // let realType = LineUpDataCenter.instance.getRealLineUpType(matchType);
            let sysId = LineUpConst.getSysIdByMatchType(matchType);
            if (GameUtil.isSysOpen(sysId, 0, false)) {
                vo._tabDatas.push(new LineUpTabVO(matchType, lineUpName, { index: 1 }, PanelEventConstants.LINE_UP));
            }
        }
        TipsUtil.openHeroLineUpDialog(MatchConst.MATCH_TYPE_MAIN_BATTLE, vo);
    }
    UpdateBagCapacity(is_expansion = false) {
        let cur_hero_num = HeroDataCenter.instance.getCurBagHeroNum();
        let max_count = HeroDataCenter.instance.getMaxExpansionCount();
        let hero_capacity = HeroDataCenter.instance.heroCapacity;
        let cur_expansion_count = HeroDataCenter.instance.expansionCount;
        let cur_hero_bag_cfg = ConfigManager.cfg_hero_bagCache.get(cur_expansion_count);
        let cur_hero_bag_capacity = cur_hero_bag_cfg.capacity;
        let next_expansion_count = Math.min(cur_expansion_count + 1, max_count);
        let next_hero_bag_cfg = ConfigManager.cfg_hero_bagCache.get(next_expansion_count);
        let next_hero_bag_capacity = next_hero_bag_cfg.capacity;
        if (this.capacityLabel && !this.capacityLabel.destroyed) {
            this.capacityLabel.text = cur_hero_num + "/" + hero_capacity;
        }
        else if (!this.capacityLabel) {
            console.error("HeroActListDialog.UpdateBagCapacity this.capacityLabel is null!!!");
        }
        else {
            console.error("HeroActListDialog.UpdateBagCapacity this.capacityLabel is destroy!!!");
        }
        if (is_expansion) {
            TipsUtil.showTips(window.iLang.L2_KUO_CHONG_CHENG_GONG_ch31_BEI_BAO_SHU_LIANG_ch17.il() + (next_hero_bag_capacity - cur_hero_bag_capacity));
        }
    }
    checkRedPoint(vo = null) {
        if (!vo) {
            vo = RedPointMgr.ins.getRedVo(PanelEventConstants.ALL_HERO, PanelEventConstants.LINE_UP);
        }
        if (vo && vo.eventId == PanelEventConstants.ALL_HERO && vo.redId == PanelEventConstants.LINE_UP) {
            this.SetRedPoint(this.lineUpBtn, vo.isRedState);
        }
        if (vo.eventId == PanelEventConstants.STONG) {
            this.SetRedPoint(this.strong, vo.isRedState, 36, -6);
        }
    }
    changeSubTab(tabData) {
        if (this._uiTab && tabData) {
            this._uiTab.selectedIndex = tabData.tabIdx;
            let _this = this;
            this.callLater(function () {
                _this._uiTab.curBaseView.onOpen(tabData);
            });
        }
    }
    close(type = null) {
        if (type != null) {
            this.dispatchEvent("HIDE_ALL_UI" /* HIDE_ALL_UI */, false);
        }
        super.close(type);
    }
}
