import { ILaya } from "ILaya";
import { URL } from "laya/net/URL";
import { LayaGL } from "../../libs/laya/layagl/LayaGL";
import { Browser } from "../../libs/laya/utils/Browser";
import { cfg_hero_base } from "../com/cfg/vo/cfg_hero_base";
import { cfg_item } from "../com/cfg/vo/cfg_item";
import { cfg_monster } from "../com/cfg/vo/cfg_monster";
import { cfg_role_profile } from "../com/cfg/vo/cfg_role_profile";
import { ConfigManager } from "../com/managers/ConfigManager";
import { ESkeletonResType, ESkeletonType, SkeletonData } from "../com/modules/baseModules/skeleton/SkeletonData";
import { RoleDataCenter } from "../com/modules/role/data/RoleDataCenter";
import { p_fight_actor } from "../com/proto/common/p_fight_actor";
import { GameUtil } from "../com/util/GameUtil";
import { GlobalConfig } from "./GlobalConfig";
import { Laya3D } from "../../libs/Laya3D";
import { cfg_hero_skin } from "../com/cfg/vo/cfg_hero_skin";
import { HeroExtKey } from "../com/scene2d/BattleConst";
import { ActorType } from "../com/scene2d/SceneConst";
import { CfgCacheMapMgr } from "../com/cfg/CfgCacheMapMgr";
import { PDataCenter } from "../com/preload/PDataCenter";
import { PUrlConfig } from "../com/preload/PUrlConfig";
import { ConsoleUtils } from "../com/util/ConsoleUtils";


export class UrlConfig {
    static CONFIG_URL: string = "res/config/w7cfg.bin";
    static VER_URL: string = "res/ver/ver.txt";


    // static LEVEL_RES_MAP_JSON: string = "res/level_res_map.json";
    // static LEVEL_RES_MAP_JSON_GUA_JI: string = "res/level_res_map_guaji.json";

    static RES_LAZY_LOAD_GUA_JI_M2: string = "res/res_lazy_load_guaji_m2.json";
    static RES_LAZY_LOAD_GUA_JI_BUILDING_M3: string = "res/res_lazy_load_guaji_building_m3.json";
    static RES_LAZY_LOAD_TD_MAIN: string = "res/res_lazy_load_td_main.json";


    static BASE_RES_URL: string = "res/";
    static WEB_URL: string = "web_";
    static ACTIVITY_RES_URL: string = "res/ui/activity/";
    static LOGIN_ACTIVITY_RES_URL: string = "res/ui/loginActivity/";
    static PLATFORM_RES_URL: string = "res/ui/platform/";
    static GOODS_RES_URL: string = "res/goods/";
    static TAB_ICON_RES_URL: string = "res/ui/tabIcon/";
    static HERO_RES_URL: string = "res/hero/";
    static SKILL_RES_URL: string = "res/skill/";
    static SKILL_RES_V2_URL: string = "res/v2_skill/";
    static FIGHT_RES_URL: string = "res/ui/fight/";
    static FISH_RES_URL: string = "res/ui/fish/";
    static FISH_HANDBOOK_RES_URL: string = "res/ui/fish/handbook/";
    static BASE_RES_UI_URL: string = "res/ui/";
    //实力预览
    static HERO_ACTUAL_PREVIEW_RES_URL: string = "res/ui/heroactualpreview/";
    static BASE_RES_ATLAS_URL: string = "res/atlas/";
    static BASE_RES_I18N_URL: string = "res/_i18n/";
    static VIDEO_RES_URL: string = "res/videos/";


    static WEB_BASE_RES_UI_URL: string = "res/web_ui/";
    static ROLE_TITLE_URL: string = "res/ui/roleTitle/";
    static RIDE_URL: string = "res/ui/ride/";
    static RANK_URL: string = "res/ui/rank/";
    static FORGE_URL: string = "res/ui/forge/";
    static EQUIP_URL: string = "res/ui/equip/";
    static WAR_RES_URL: string = "res/ui/war/";
    static MAP_RES_URL: string = "res/ui/map/";
    static TIME_ACTIVITY_RES_URL: string = "res/ui/timeActivity/";
    static FLY_FONT_URL: string = "res/ui/flyFont/";
    static ARENA_URL: string = "res/ui/arena/";
    static ARENA_TYPE_URL: string = "res/ui/arenaType/";
    static GUIDE_URL: string = "res/ui/guide/";
    static TREASURE_URL: string = "res/ui/treasure/";
    static CREATE_PLAYER_URL: string = "res/ui/createPlayer/";
    static STORY_URL: string = "res/ui/story/";
    static SPECIALFUNDS_URL: string = "res/ui/specialFunds/";
    static WX_SHARE_PATH = "res/ui/wxShare/";
    static VIP_KEFU_URL = "res/ui/vipKefu/";
    static QQ_URL: string = "res/ui/qq/";
    static MATCH_URL: string = "res/ui/match/";
    static BAG_URL: string = "res/ui/bag/";
    static SPLASH_URL: string;
    static WIN_URL: string = "res/config/win.bin";
    static WAITING_URL: string = "res/ui/loading.png";
    static LOGIN_BG_URL: string;
    static GAME_LOGO_URL: string;
    static LOGIN_TEST_BTN_URL: string;
    static SELECT_SERVER_BG_URL: string;
    static LOADING_BG_URL: string;
    static DIVINE_URL: string = "res/ui/divine/";
    static DIVINE_HEAD_URL: string = "res/ui/divine/head/";

    static HERO_MODELS_PATH_2D: string = "res/hero/2d/";
    static SKELETON_MODEL_ACTION_PATH: string = "res/2d/action/";
    static SKELETON_MODEL_LIHUI_PATH: string = "res/2d/lihui/";
    static SKELETON_MODEL_EFFECTS_PATH: string = "res/2d/effects/";
    static SKELETON_MODEL_UI_EFFECTS_PATH: string = "res/2d/uieffect/";

    static SPINE_BASE_PATH: string = "res/2d_spine/";
    static SPINE_ACTION_PATH: string = "res/2d_spine/action/";
    static SPINE_LIHUI_PATH: string = "res/2d_spine/lihui/";
    static SPINE_EFFECTS_PATH: string = "res/2d_spine/effects/";
    static SPINE_UI_EFFECTS_PATH: string = "res/2d_spine/uieffect/";
    static SPINE_SKELETON_MODEL_ACTION_PATH: string = "res/2d_spine/action/";
    static SPINE_SKELETON_MODEL_LIHUI_PATH: string = "res/2d_spine/lihui/";
    static SPINE_SKELETON_MODEL_EFFECTS_PATH: string = "res/2d_spine/effects/";
    static SPINE_SKELETON_MODEL_UI_EFFECTS_PATH: string = "res/2d_spine/uieffect/";


   

    /**忍法战姬的spine资源路径 */
    static SPINE_ACTION_ZHAN_JI_PATH: string = "res/2d_spine/action_chongsheng/";
    static SPINE_LIHUI_ZHAN_JI_PATH: string = "res/2d_spine/lihui_chongsheng/";
    static SPINE_EFFECTS_ZHAN_JI_PATH: string = "res/2d_spine/effects_chongsheng/";
    static SPINE_UI_EFFECTS_ZHAN_JI_PATH: string = "res/2d_spine/uieffects_chongsheng/";
    // static SPINE_ACTION_ZHAN_JI_PATH: string = "";
    // static SPINE_LIHUI_ZHAN_JI_PATH: string = "";
    // static SPINE_EFFECTS_ZHAN_JI_PATH: string = "";
    // static SPINE_UI_EFFECTS_ZHAN_JI_PATH: string = "";


    static SPINE_ANI_BASE_PATH: string = "res/2d_ani/";
    static LAYA_ANIMATION_MODEL_ACTION_PATH: string = "res/2d_ani/action/";
    static LAYA_ANIMATION_MODEL_LIHUI_PATH: string = "res/2d_ani/lihui/";
    static LAYA_ANIMATION_MODEL_EFFECTS_PATH: string = "res/2d_ani/effects/";
    static LAYA_ANIMATION_MODEL_UI_EFFECTS_PATH: string = "res/2d_ani/uieffect/";
    /**laya原生方式特效的主目录 */
    static EFFECT_LAYA_WAY_PATH: string = "res/effects_laya/";

    static JY_ANI_3D_TO_2D_DIR = UrlConfig.SPINE_ANI_BASE_PATH + "action_3to2_dir5/";

    static _3to2_tag_JSON_PATH: string = UrlConfig.SPINE_ANI_BASE_PATH + "3to2_frame_tag_info.json";
    static _3to2_SK_JSON_PATH: string = UrlConfig.SPINE_ANI_BASE_PATH + "3to2_frame_sk_info.json";

    static GUA_JI_URL: string = "res/ui/guaJi/";
    static DIALOG_BG_URL: string = "res/ui/dialogBg/";
    static FAMILY_REDPACK_URL: string = "res/ui/familyRedPack/";
    static MASTER_UI_URL: string = "res/ui/master/";
    static LIE_ZHUAN_UI_URL: string = "res/ui/liezhuan/";
    static HERO_SKIN_UI_URL: string = "res/ui/heroskin/";
    static HERO_INFO_UI_URL: string = "res/ui/heroInfo/";
    static HERO_INFO_V2_UI_URL: string = "res/ui/v2_heroInfo/";
    static CROSSREAL_UI_URL: string = "res/ui/crossrealmwar/";
    static PASS_BEHEAD_UI_URL: string = "res/ui/passBehead/";
    static MAZE_UI_URL: string = "res/ui/maze/";
    static BATTLE_TRIAL_UI_URL: string = "res/ui/battleTrial/";
    static DOMINATE_PVP_UI_URL: string = `${UrlConfig.BASE_RES_UI_URL}dominate_pvp/`;

    static FAMILY_BOAT_RACE_URL: string = "res/ui/familyboatrace/";
    static BOAT_PEAK_URL: string = "res/ui/boatPeak/";
    static PLATFORM_BASE: string = "res/ui/platform_"
    static Lord_UI_URL: string = "res/ui/lord/";
    static V2_Lord_UI_URL: string = "res/ui/v2_lord/";
    static TD_TRIAL_UI_URL: string = "res/ui/tdTrial/";

    static COMMON_PATH: string;
    static COMMON3_PATH: string;
    static PANEL_BG_URL: string;
    //static COMP_URL: string = "res/atlas/comp.atlas";

    static UN_PACK_URL: string = "res/ui/unpack.json";
    // static   BASE_DIR:string = "res/";
    static EFFECT_PATH: string = "res/effects/";
    static D2MODELS_PATH: string;
    static BASE_ATLAS_URL: string = UrlConfig.BASE_RES_URL + "atlas/";

    static RES_BASE_DIR_2D: string = "res/2d/";
    static RES_BASE_DIR_3D: string = "res/3d/";
    static DEFAUL_UPDATE_URL: string = `http://${PDataCenter.gameProjectName}.kkyou.cn/debugserverlists`;
    // stat module start
    static DEFAUL_STAT_URL: string = `http://central.${PDataCenter.gameProjectName}debug.kkyou.cn/api/`;
    // stat module end
    static DEFAUL_PLATFORM: string = "debug";

    static PLATFORM_BASE_RES: string = UrlConfig.BASE_ATLAS_URL + "platform_";

    static LOGIN_RES: string = UrlConfig.BASE_ATLAS_URL + "login.atlas";

    static UI_URL: string = "res/ui.json";
    static UIBIN_URL: string = "res/ui.bin";
    static UITHEME_URL: string = "res/uitheme.bin";
    static FILECONFIG_URL_W7: string = "res/fileconfig_w7.json";
    static HERO_ACTUAL_PREVIEW_RES: string = UrlConfig.BASE_ATLAS_URL + "heroactualpreview.atlas";

    static FILECONFIG_URL_ORIGIN: string = "res/ui/fileconfig.json";
    static MENU_RES: string = UrlConfig.BASE_ATLAS_URL + "mainui.atlas";
    //static MENU2_RES: string = UrlConfig.BASE_ATLAS_URL + "mainui2.atlas";
    static SELECT_SERVER_RES: string = UrlConfig.BASE_ATLAS_URL + "selectServer.atlas";
    //static COMMON_RES: string = UrlConfig.BASE_ATLAS_URL + "common.atlas";
    // static COMMON2_RES: string = UrlConfig.BASE_ATLAS_URL + "common2.atlas";
    static STAGECOPY_RES: string = UrlConfig.BASE_ATLAS_URL + "stagecopy.atlas";
    static STAGECOPY_UI_URL: string = "res/ui/stagecopy/";
    static STAGECOPY_ASSETS_URL: string = "stagecopy/";
    static FISH_RES: string = UrlConfig.BASE_ATLAS_URL + "fish.atlas";
    static MASTER_TALENT_SCI_UI_URL: string = "res/ui/masterTalentScience/";

    //塔楼之战
    static TOWER_BATTLE: string = UrlConfig.BASE_ATLAS_URL + "towerBattle.atlas";
    static TOWER_BATTLE_URL: string = "res/ui/towerBattle/";

    //获取mainui中的图片地址
    static getMainuiResUrl(iconName: string) {

        if (iconName.endsWith(".png") == false) {
            iconName += ".png";
        }

        let pngUrl = "res/ui/mainui/" + iconName;
        if (this.checkResExist(pngUrl)) {
            return pngUrl;
        } else {
            return "mainui/" + iconName;
        }

        // if (Loader.textureMap[URL.formatURL("mainui/" + iconName)]){
        //     return "mainui/" + iconName;
        // }else{

        //     return pngUrl;
        // }

    }

    static get MENU2_RES(): string {
        return UrlConfig.BASE_ATLAS_URL + UrlConfig.COMMON_UI_SKIN("mainui2");
    }

    static get COMMON_RES(): string {
        return UrlConfig.BASE_ATLAS_URL + UrlConfig.COMMON_UI_SKIN("common");
    }

    static get COMMON2_RES(): string {
        return UrlConfig.BASE_ATLAS_URL + UrlConfig.COMMON_UI_SKIN("common2");
    }

    static get COMMON3_RES(): string {
        return UrlConfig.BASE_ATLAS_URL + UrlConfig.COMMON_UI_SKIN("common3");
    }

    static get COMMON4_RES(): string {
        return UrlConfig.BASE_ATLAS_URL + UrlConfig.COMMON_UI_SKIN("common4");
    }

    static get COMMON5_RES(): string {
        return UrlConfig.BASE_ATLAS_URL + UrlConfig.COMMON_UI_SKIN("common5");
    }
    static get COMMON_RAW_RES(): string {
        return UrlConfig.BASE_ATLAS_URL + UrlConfig.COMMON_UI_SKIN("common_raw");
    }

    private static COMMON_UI_SKIN(url: string): string {
        if (GlobalConfig.common_ui != null && GlobalConfig.common_ui.length > 0 && GlobalConfig.common_ui != "0") {
            return "common_" + GlobalConfig.common_ui + "/" + url + ".atlas";
        }
        return url + ".atlas";
    }

    static get D3MODELS_MANIFEST(): string {
        return UrlConfig.BASE_RES_URL + "3d/models/Conventional/manifest.json"
    }

    /**政务系统 */
    static TRAVEL_RES: string = UrlConfig.BASE_ATLAS_URL + "travel.atlas";
    /**无双试炼系统 */
    static TRIAL_RES: string = UrlConfig.BASE_ATLAS_URL + "trial.atlas";
    /**活动开启提示界面 */
    static SYS_OPEN_RES: string = UrlConfig.BASE_ATLAS_URL + "sysOpen.atlas";

    static WAR_RES: string = UrlConfig.BASE_ATLAS_URL + "war.atlas";
    /**加载页 */
    static LOADING_RES: string = UrlConfig.BASE_ATLAS_URL + "loading.atlas";
    /** 寻宝 */
    static TREASURE_RES: string = UrlConfig.BASE_ATLAS_URL + "treasure.atlas";

    static ROLE_RES: string = UrlConfig.BASE_ATLAS_URL + "role.atlas";
    /** 七日目标 */
    static SEVEN_GOAL_RES: string = UrlConfig.BASE_ATLAS_URL + "sevenGoal.atlas";
    static ROLE_OTHER_INFO_RES: string = UrlConfig.BASE_ATLAS_URL + "roleOtherInfo.atlas";
    static SETTING_RES: string = UrlConfig.BASE_ATLAS_URL + "setting.atlas";
    static PAYMENT_VIP_RES: string = UrlConfig.BASE_ATLAS_URL + "paymentVip.atlas";
    /**选服 */
    // static DIALOG_RES: string = UrlConfig.BASE_ATLAS_URL + "dialogBox.atlas";
    /**创角*/
    static CREATE_PLAYER_RES: string = UrlConfig.BASE_ATLAS_URL + "createPlayer.atlas";
    /**男 */
    static CREATE_PLAYER_MAN_SPINE_RES: string = "20000002";
    /**女 */
    static CREATE_PLAYER_WOMAN_SPINE_RES: string = "10000006";

    static TEST_RES: string = UrlConfig.BASE_ATLAS_URL + "test.atlas";
    static DAILY_MISSION_RES: string = UrlConfig.BASE_ATLAS_URL + "dailyMission.atlas";
    static FRIEND_RES: string = UrlConfig.BASE_ATLAS_URL + "friend.atlas";
    static TAX_RES: string = UrlConfig.BASE_ATLAS_URL + "tax.atlas";
    static LETTER_RES: string = UrlConfig.BASE_ATLAS_URL + "letter.atlas";
    static FLY_FONT_RES: string = UrlConfig.BASE_ATLAS_URL + "flyFont.atlas";

    static MASTER_HALO_RES: string = UrlConfig.BASE_ATLAS_URL + "masterHalo.atlas";
    static WAR_FLAG_RES: string = UrlConfig.BASE_ATLAS_URL + "warflag.atlas";
    /**铁匠铺 */
    static FORGE_RES: string = UrlConfig.BASE_ATLAS_URL + "forge.atlas";
    /**装备列表界面 */
    static EQUIP_RES: string = UrlConfig.BASE_ATLAS_URL + "equip.atlas";
    /**神装界面 */
    static GOD_EQUIP_RES: string = UrlConfig.BASE_ATLAS_URL + "godEquip.atlas";

    /**聊天 */
    static CHAT_RES: string = UrlConfig.BASE_ATLAS_URL + "chat.atlas";
    /**手册 */
    static BOOK_MISSION_RES: string = UrlConfig.BASE_ATLAS_URL + "bookmission.atlas";
    /**聊天 */
    static CHAT_FACE_RES: string = UrlConfig.BASE_ATLAS_URL + "chatFace.atlas";
    /**聊天气泡 */
    static CHAT_SKIN_RES: string = UrlConfig.BASE_ATLAS_URL + "chatSkin.atlas";
    /**充值 */
    static RECHARGE_RES: string = UrlConfig.BASE_ATLAS_URL + "recharge.atlas";
    /**新手豪礼 */
    static NEW_PAY_RES: string = UrlConfig.BASE_ATLAS_URL + "newPay.atlas";
    /**每日充值 */
    static DAILY_PAY_RES: string = UrlConfig.BASE_ATLAS_URL + "dailyPay.atlas";
    /**首充 */
    static FIRST_RECHARGE_RES: string = UrlConfig.BASE_ATLAS_URL + "firstRecharge.atlas";
    /**商城 */
    static SHOP_RES: string = UrlConfig.BASE_ATLAS_URL + "shop.atlas";
    /**历练主界面 */
    static GROW_RES: string = UrlConfig.BASE_ATLAS_URL + "grow.atlas";
    /**历练日常副本 */
    static GROW_DAILY_MISSION_RES: string = UrlConfig.BASE_ATLAS_URL + "growDailyMission.atlas";
    /**公会 */
    static FAMILY_RES: string = UrlConfig.BASE_ATLAS_URL + "family.atlas";
    /**公会科技 */
    static FAMILY_SCIENCE_RES: string = UrlConfig.BASE_ATLAS_URL + "familyScience.atlas";
    /**公会敌将 */
    static FAMILY_BOSS_RES: string = UrlConfig.BASE_ATLAS_URL + "familyBoss.atlas";
    /**公会战 */
    static FAMILY_WAR_RES: string = UrlConfig.BASE_ATLAS_URL + "familyWar.atlas";
    /**联盟赛艇 */
    static FAMILY_BOATRACE_RES: string = UrlConfig.BASE_ATLAS_URL + "familyboatrace.atlas";
    /**英雄技能TIPS */
    static HERO_SKILL_RES: string = UrlConfig.BASE_ATLAS_URL + "heroSkill.atlas";
    /**酒馆抽卡 */
    static LOTTERY_RES: string = UrlConfig.BASE_ATLAS_URL + "lottery.atlas";
    /**将军府抽卡阵营选择 */
    static LOTTERY_NATION_SELECT_RES: string = UrlConfig.BASE_ATLAS_URL + "lottery_nation_select.atlas";

    /**抽卡获得 */
    static LOTTERY_GAIN_RES: string = UrlConfig.BASE_ATLAS_URL + "lottery_gain.atlas";
    /**将军府抽卡 */
    static LOTTERY_BETTER_RES: string = UrlConfig.BASE_ATLAS_URL + "lotteryBetter.atlas";
    /**征战界面UI */
    static GUA_JI_RES: string = UrlConfig.BASE_ATLAS_URL + "guaJi.atlas";
    static GUA_JI_MINI_MAP_URL = UrlConfig.BASE_RES_URL + "ui/guaJi/scene13.jpg"
    static GUA_JI_BUILD_RES: string = UrlConfig.BASE_ATLAS_URL + "guaJiBuild.atlas";
    //萌将卡装扮资源路径
    static MASTER_CARD_BG_RES_UI_URL: string = "res/ui/mastercardBg/";

    static MASTER_CARD_DECORATION: string = "res/ui/mastercardecoration/"

    /* 名将卡资源图集 */
    static MASTER_CARD_RES: string = UrlConfig.BASE_ATLAS_URL + "mastercart.atlas";

    static FIGHT_SUCC_RES: string = UrlConfig.BASE_ATLAS_URL + "fightsucc.atlas";
    static FIGHT_FAIL_RES: string = UrlConfig.BASE_ATLAS_URL + "fightfail.atlas";
    /**征战界面UI */
    static FIGHT_RES: string = UrlConfig.BASE_ATLAS_URL + "fight.atlas";
    static FIGHT2_RES: string = UrlConfig.BASE_ATLAS_URL + "fight2.atlas";
    static FIGHTUI_RES: string = UrlConfig.BASE_ATLAS_URL + "fightUI.atlas";
    /**推荐阵容 */
    static RECOMMEND_LINE_UP_RES: string = UrlConfig.BASE_ATLAS_URL + "recommend_line_up.atlas";
    /**排行榜 */
    static RANK_RES: string = UrlConfig.BASE_ATLAS_URL + "rank.atlas";
    /**英雄*/
    static HERO_RES: string = UrlConfig.BASE_ATLAS_URL + "hero.atlas";
    /**英雄继承 */
    static HERO_INHERIT_RES: string = UrlConfig.BASE_ATLAS_URL + "heroInherit.atlas";
    /**英雄属性TIPS*/
    static HERO_ATTR_TIPS_RES: string = UrlConfig.BASE_ATLAS_URL + "heroAttrTips.atlas";
    /**英雄图鉴*/
    static HERO_TU_JIAN_RES: string = UrlConfig.BASE_ATLAS_URL + "heroTujian.atlas";
    /**英雄图鉴*/
    static HERO_TU_JIAN_HERO_CARD_RES: string = UrlConfig.BASE_RES_UI_URL + "heroTujian/hero/";
    /**英雄总览*/
    static HERO_INFO_RES: string = UrlConfig.BASE_ATLAS_URL + "heroInfo.atlas";
    /**工坊*/
    static EQUIP_WORK_RES: string = UrlConfig.BASE_ATLAS_URL + "equipwork.atlas";
    /**英灵堡*/
    static DUDUFU_RES: string = UrlConfig.BASE_ATLAS_URL + "dudufu.atlas";
    /**守护灵*/
    static DEPUTY_RES: string = UrlConfig.BASE_ATLAS_URL + "deputy.atlas";
    /**英雄进阶升星*/
    static HERO_UPDATE_RES: string = UrlConfig.BASE_ATLAS_URL + "heroUpdate.atlas";
    /**英雄天赋预览*/
    static HERO_TALENT_RES: string = UrlConfig.BASE_ATLAS_URL + "heroTalent.atlas";
    /**英雄评论*/
    static HERO_REMARK_RES: string = UrlConfig.BASE_ATLAS_URL + "heroRemark.atlas";
    /**英雄布阵*/
    static LINE_UP_RES: string = UrlConfig.BASE_ATLAS_URL + "lineup.atlas";
    /**阵营buff */
    static LINE_UP_BUFF_RES: string = UrlConfig.BASE_ATLAS_URL + "line_up_buff.atlas";
    /**英雄展示 */
    static HERO_SHOW_RES: string = UrlConfig.BASE_ATLAS_URL + "hero_show.atlas";
    /**竞技场 */
    static ARENA_RES: string = UrlConfig.BASE_ATLAS_URL + "arena.atlas";
    /**竞技场选择界面 */
    static ARENA_TYPE_RES: string = UrlConfig.BASE_ATLAS_URL + "arenaType.atlas";
    /**充值礼包商店 */
    static PAYMENT_RES: string = UrlConfig.BASE_ATLAS_URL + "payment.atlas";
    /**超值礼包 */
    static PROGRESS_GIFT_RES: string = UrlConfig.BASE_ATLAS_URL + "progressGift.atlas";
    /**特惠直购 */
    static PAYMENT_TIP_RES: string = UrlConfig.BASE_ATLAS_URL + "paymentTip.atlas";
    /**vip商店限购跳转 */
    static PAYMENT_LINK_RES: string = UrlConfig.BASE_ATLAS_URL + "paymentLink.atlas";
    /**新服限购 */
    static NWE_SERVER_RES: string = UrlConfig.BASE_ATLAS_URL + "newServer.atlas";
    /**三系密令/征战日志 */
    static SECRET_ORDER_RES: string = UrlConfig.BASE_ATLAS_URL + "secretOrder.atlas";
    /**超值基金 */
    static FUNDS_RES: string = UrlConfig.BASE_ATLAS_URL + "specialFunds.atlas";
    /**南蛮入侵 */
    static RANDOM_BOSS_RES: string = UrlConfig.BASE_ATLAS_URL + "randomBoss.atlas";
    /**坐骑 */
    static RIDE_RES: string[] = [UrlConfig.BASE_ATLAS_URL + "ride.atlas"];


    /**擂台 */
    static MATCH_RES: string = UrlConfig.BASE_ATLAS_URL + "match.atlas";

    /**战斗回放 */
    static FIGHT_PLAY_BACK_RES: string = UrlConfig.BASE_ATLAS_URL + "fightPlayBack.atlas";

    static QQ_RES: string = UrlConfig.BASE_ATLAS_URL + "qq.atlas";

    /**指引 */
    // static GUIDE_RES: string = UrlConfig.BASE_ATLAS_URL + "guide.atlas";
    /**指引 */
    static PLOT_RES: string = UrlConfig.BASE_ATLAS_URL + "plot.atlas";

    static GUIDE_GIRL: string = UrlConfig.BASE_RES_URL + "ui/guide/g_girl.png";
    /**充值福利 */
    static WELFARE_RES: string = UrlConfig.BASE_ATLAS_URL + "welfare.atlas";
    /**等级礼包 */
    static LEVEL_GIFT_RES: string = UrlConfig.BASE_ATLAS_URL + "levelGift.atlas";
    /**条件充值礼包 */
    static MISSION_SHOP_RES: string = UrlConfig.BASE_ATLAS_URL + "missionShop.atlas";
    /**VIP客服*/
    static VIP_KEFU: string = UrlConfig.BASE_ATLAS_URL + "vipKefu.atlas";
    /**活动 */
    static ACTIVITY: string = UrlConfig.BASE_ATLAS_URL + "activity.atlas";
    /**活动 */
    static LOGINACTIVITY: string = UrlConfig.BASE_ATLAS_URL + "loginActivity.atlas";

    /**限时活动 */
    static TIME_ACTIVITY: string = UrlConfig.BASE_ATLAS_URL + "timeActivity.atlas";
    static DAY_SHOP: string = UrlConfig.BASE_ATLAS_URL + "activitydayshop.atlas";
    /**主题玩法 */
    static MAZE: string = UrlConfig.BASE_ATLAS_URL + "maze.atlas";
    /**主题玩法模块化通行证 */
    static MAZE_PASS: string = UrlConfig.BASE_ATLAS_URL + "actMods/maze_pass.atlas";

    /**神器 */
    static WEAPON_RES: string = UrlConfig.BASE_ATLAS_URL + "weapon.atlas";
    /**武神庙 */
    static HERO_TEMPLE_RES: string = UrlConfig.BASE_ATLAS_URL + "heroTemple.atlas";
    /**英雄收集 */
    static HERO_COLLECT_RES: string = UrlConfig.BASE_ATLAS_URL + "heroCollect.atlas";
    /**战斗统计 */
    static FIGHT_STAT_RES: string = UrlConfig.BASE_ATLAS_URL + "fightStat.atlas";

    static GUA_JI_RES_URL: string = UrlConfig.BASE_RES_URL + "map/guaji/";
    /**国战托管 */
    static WAR_AUTO_RES: string = UrlConfig.BASE_ATLAS_URL + "warAuto.atlas";

    static FAMILY_REDPACK_RES: string = UrlConfig.BASE_ATLAS_URL + "familyRedPack.atlas";
    /** 我要变强 */
    static STRONG_RES: string = UrlConfig.BASE_ATLAS_URL + "strong.atlas";
    /**功能预告 */
    static FUNCTION_PRE_RES: string = UrlConfig.BASE_ATLAS_URL + "functionPre.atlas"
    /**属性图标 */
    static FIGHT_ATTR_RES: string = UrlConfig.BASE_ATLAS_URL + "fightAttr.atlas";

    static FIGHT_BUFF_RES: string = UrlConfig.BASE_ATLAS_URL + "fightBuff.atlas";

    /**战魂 */
    static HERO_SOUL_RES: string = UrlConfig.BASE_ATLAS_URL + "hero_soul.atlas";
    /**冠军赛 */
    static RANDOM_PVP_RES: string = UrlConfig.BASE_ATLAS_URL + "random_pvp.atlas";
    /**英雄售卖限时广告 */
    static HERO_ACT_AD_RES: string = UrlConfig.BASE_ATLAS_URL + "hero_act_ad.atlas";
    /**英雄售卖 */
    static HERO_ACT_RES: string = UrlConfig.BASE_ATLAS_URL + "hero_act.atlas";

    static LIE_ZHUAN_RES: string = UrlConfig.BASE_ATLAS_URL + "liezhuan.atlas";

    static LINEUP_VS: string = UrlConfig.BASE_ATLAS_URL + "lineupVs.atlas";
    static FAMILY_TRIAL_RES: string = UrlConfig.BASE_ATLAS_URL + "familyTrial.atlas";
    /** 三军备战 */
    static PREPARE_RES: string = UrlConfig.BASE_ATLAS_URL + "prepare.atlas";

    static MASTER_COMMON_RES: string = UrlConfig.BASE_ATLAS_URL + "master_common.atlas";
    static MASTER_RES: string[] = [UrlConfig.BASE_ATLAS_URL + "master.atlas", UrlConfig.MASTER_COMMON_RES];

    static HERO_SKIN_RES: string = UrlConfig.BASE_ATLAS_URL + "heroskin.atlas";
    static HERO_HALF_SKIN_RES: string = UrlConfig.BASE_ATLAS_URL + "heroskin" + "/half.atlas"
    /** qq蓝钻 */
    static HERO_UP_ACT_RES: string = UrlConfig.BASE_ATLAS_URL + "heroUpAct.atlas";
    static QQ_BLUE_DIAMOND_RES: string = UrlConfig.BASE_ATLAS_URL + "qqBlueDiamond.atlas";
    /** qq大厅 */
    static QQ_GAME_RES: string = UrlConfig.BASE_ATLAS_URL + "qqGame.atlas";

    /**sdk分享(邀请) */
    static SHARE_RES: string = UrlConfig.BASE_ATLAS_URL + "wxShare.atlas";
    static WX_TURN_RES: string = UrlConfig.BASE_ATLAS_URL + "wxTurn.atlas";
    static SIX_TURN_RES: string = UrlConfig.BASE_ATLAS_URL + "sixturn.atlas";

    /**战斗编辑器 */
    static BATTLE_EDITOR_RES: string = UrlConfig.BASE_ATLAS_URL + "battleEditor.atlas";

    static CROSSREAL_RES: string = UrlConfig.BASE_ATLAS_URL + "crossrealmwar.atlas";

    static HERO_ROLL_CALL_RES: string = UrlConfig.BASE_ATLAS_URL + "heroRollCall.atlas";
    /** 兽灵系统 */
    static HERO_TU_TENG: string = UrlConfig.BASE_ATLAS_URL + "heroTuTeng.atlas";

    /**勇闯异境 */
    static PASS_BEHEAD: string = UrlConfig.BASE_ATLAS_URL + "passBehead.atlas";

    /** 赤壁之战 */
    static RED_CLIFF_RES: string = UrlConfig.BASE_ATLAS_URL + "redCliff.atlas";
    /** 将军府 */
    static GENERAL_MANSION_RES: string = UrlConfig.BASE_ATLAS_URL + "generalMansion.atlas";
    /**试炼塔 */
    static TEST_TOWER_RES: string = UrlConfig.BASE_ATLAS_URL + "testTower.atlas";

    /**种族塔 */
    static TEST_TOWER_RES2: string = UrlConfig.BASE_ATLAS_URL + "testtower2.atlas";
    /**神器 */
    static GOD_WEAPON_RES: string = UrlConfig.BASE_ATLAS_URL + "godWeapon.atlas";

    /**英雄图鉴 */
    static HERO_TUJIAN_RES: string = UrlConfig.BASE_ATLAS_URL + "heroTuJian.atlas";

    /**神位争夺 */
    static ARES_PALACE_RES: string = UrlConfig.BASE_ATLAS_URL + "aresPalace.atlas";

    /**勇闯异境 */
    static GOD_TRIAL: string = UrlConfig.BASE_ATLAS_URL + "godTrial.atlas";

    /**暗黑地牢（官渡之战） */
    static GUAN_DU_RES: string = UrlConfig.BASE_ATLAS_URL + "guanDu.atlas";

    /**公会活跃 */
    static FAMILY_ACTIVE_RES: string = UrlConfig.BASE_ATLAS_URL + "familyActive.atlas";

    /**冠军赛 */
    static QXZL_RES: string = UrlConfig.BASE_ATLAS_URL + "qxzl.atlas";

    /**神装纷争 */
    static CHALLENGE_CHAPTER_RES: string = UrlConfig.BASE_ATLAS_URL + "challengeChapter.atlas";
    /**神装秘宝 */
    static GOD_EQUIP_TREASURE_RES: string = UrlConfig.BASE_ATLAS_URL + "godEquipTreasure.atlas";

    /**千抽活动 */
    static OPTION_LOTTERY_RES: string = UrlConfig.BASE_ATLAS_URL + "optionLottery.atlas";

    /**开场剧情 */
    static STORY_RES: string = UrlConfig.BASE_ATLAS_URL + "story.atlas";

    /* 剧情总览资源 */
    static ACT_MODS_STORY_DRAMA_RES: string = UrlConfig.BASE_ATLAS_URL + "actMods/drama.atlas";

    /**跨服争霸入口 */
    static CROSS_ENTER_RES: string = UrlConfig.BASE_ATLAS_URL + "crossEnter.atlas";

    /**三系通行证 */
    static PASSPORT_RES: string = UrlConfig.BASE_ATLAS_URL + "passports.atlas";

    /**名将招募-限时皮肤 */
    static THEME_ACT_SKIN_LOTTERY_RES: string = UrlConfig.BASE_ATLAS_URL + "themeActSkinLottery.atlas";
    /**名将招募-名将招募 */
    static THEME_ACT_HERO_LOTTERY_RES: string = UrlConfig.BASE_ATLAS_URL + "themeActHeroLottery.atlas";

    /**汉中争夺战 */
    static HZZD_RES: string = UrlConfig.BASE_ATLAS_URL + "hzzd.atlas";

    /**冠军赛 */
    static PEAK_RES: string = UrlConfig.BASE_ATLAS_URL + "peak.atlas";

    /**跨服天梯赛 */
    static CROSS_LADDER_RES: string = UrlConfig.BASE_ATLAS_URL + "crossLadder.atlas";

    /**仙山外海 */
    static XSWH_RES: string = UrlConfig.BASE_ATLAS_URL + "xswh.atlas";

    /**转端活动 */
    static MINI_DUAN_RES: string = UrlConfig.BASE_ATLAS_URL + "actMods/miniDuan.atlas";
    static MINI_DUAN_RES2: string = UrlConfig.BASE_ATLAS_URL + "actMods/miniDuan/skin1.atlas";
    /**龙魂 */
    static QI_MOU_RES: string = UrlConfig.BASE_ATLAS_URL + "qiMou.atlas";
    /**剧情活动 */
    static ACT_MODS_STORY_RES: string = UrlConfig.BASE_ATLAS_URL + "actMods/story.atlas";
    /**活动预览 */
    static ACT_MODS_OPEN_PREVIEW_RES: string = UrlConfig.BASE_ATLAS_URL + "actMods/open_preview.atlas";
    /**勋章 */
    static XUN_ZHANG_RES: string = UrlConfig.BASE_ATLAS_URL + "xunzhang.atlas";
    /* 号码绑定图集路径 */
    static PHONE_BIND_RES: string = UrlConfig.BASE_ATLAS_URL + "phonebind.atlas";
    /* 组队征战 */
    static BATTLE_TRIAL_RES: string = UrlConfig.BASE_ATLAS_URL + "battleTrial.atlas";
    /**战场主宰 */
    static LARGE_PEAK_RES: string = UrlConfig.BASE_ATLAS_URL + "largePeak.atlas";
    /**战场主宰预告 */
    static LARGE_PEAK_NOTICE_RES: string = UrlConfig.BASE_ATLAS_URL + "largePeak/notice.atlas";
    /**诸神战场 */
    static DOMINATE_PVP_RES: string = UrlConfig.BASE_ATLAS_URL + "dominate_pvp.atlas";
    /**跨服组队 */
    static CROSS_TEAM_RES: string = UrlConfig.BASE_ATLAS_URL + "crossTeam.atlas";
    static MONTHCARD_ACTIVE_RES: string = UrlConfig.BASE_ATLAS_URL + "monthcardactive.atlas";
    /**跨服组队领主战 */
    static TEAM_XSWH_RES: string = UrlConfig.BASE_ATLAS_URL + "team_xswh.atlas";
    /**家族 */
    static CSCLAN: string = UrlConfig.BASE_ATLAS_URL + "csclan.atlas";

    /**任务墙*/
    static TASK_WALL_RES: string = UrlConfig.BASE_ATLAS_URL + "actMods/mission.atlas";

    /* 名将卡升官资源图集 */
    static MASTER_CART_RES_2: string = UrlConfig.BASE_ATLAS_URL + "mastercart/mastercart_2.atlas";
    /**名将卡首充 */
    static MASTER_CARD_FIRST_RECHARGE_RES: string = UrlConfig.BASE_ATLAS_URL + "mastercardfirstRecharge.atlas";

    /**国战 */
    static COUNTRY_WAR_RES: string = UrlConfig.BASE_ATLAS_URL + "countryWar.atlas";
    static COUNTRY_WAR_UI_URL: string = `${UrlConfig.BASE_RES_UI_URL}countryWar/`;

    /**塔防 */
    static TD_MAIN_RES: string = UrlConfig.BASE_ATLAS_URL + "tdMain.atlas";
    static TD_TRIAL_RES: string = UrlConfig.BASE_ATLAS_URL + "tdTrial.atlas";

    static RED_CLIFF_UI_URL: string = "res/ui/redCliff/"
    static isInit: boolean = false;

    /**地图UI */
    static WORLD_MAP_RES: string = UrlConfig.BASE_ATLAS_URL + "worldMap.atlas";
    /**攻城掠地 */
    static SIEGELORD_RES: string = UrlConfig.BASE_ATLAS_URL + "siegelord.atlas";

    /**运河夺宝*/
    static RIVER_TREASURE_RES: string = UrlConfig.BASE_ATLAS_URL + "riverTreasure.atlas";
    static init(): void {
        if (UrlConfig.isInit == false) {
            UrlConfig.isInit = true;
            UrlConfig.COMMON_PATH = "common/";
            UrlConfig.COMMON3_PATH = "common3/";

            UrlConfig.RES_BASE_DIR_2D = "res/2d/";
            UrlConfig.RES_BASE_DIR_3D = "res/3d/";

            // if (GlobalConfig._useTotal || GlobalConfig._useTotal1) {
            //     UrlConfig.SD_CONFIG_URL = UrlConfig.RES_BASE_DIR_3D + "sda.bin";
            // }
            // else {
            //     UrlConfig.SD_CONFIG_URL = UrlConfig.RES_BASE_DIR_3D + "sd.bin";
            // }
            Laya3D.START_WITH = UrlConfig.RES_BASE_DIR_3D;
            // UrlConfig.RES_BASE_DIR_3D = "res/3d/";
            // UrlConfig.EFFECT_PATH = UrlConfig.BASE_RES_URL + "effects/";
            // UrlConfig.D2MODELS_PATH = UrlConfig.BASE_RES_URL + "models/allinone/";

            // UrlConfig.LH_CONFIG_URL = UrlConfig.RES_BASE_DIR_3D + "lh.bin";
            // UrlConfig.D3_CONFIG_URL = UrlConfig.RES_BASE_DIR_3D + "3d.bin";

            // UrlConfig.D3INFO_CONFIG_URL = UrlConfig.RES_BASE_DIR_3D + "info.bin";


            UrlConfig.EFFECT_PATH = UrlConfig.BASE_RES_URL + "effects/";
            UrlConfig.D2MODELS_PATH = UrlConfig.BASE_RES_URL + "models/allinone/";

            UrlConfig.PLATFORM_RES_URL = PUrlConfig.PLATFORM_RES_URL;
            if (GlobalConfig.isYeGame) {
                UrlConfig.PLATFORM_RES_URL = GameUtil.webUrlChange(UrlConfig.PLATFORM_RES_URL);
                UrlConfig.PLATFORM_BASE = GameUtil.webUrlChange(UrlConfig.PLATFORM_BASE);
            }


            //UrlConfig.SPLASH_URL = UrlConfig.PLATFORM_RES_URL + "splash.jpg";
            UrlConfig.LOGIN_BG_URL = UrlConfig.PLATFORM_RES_URL + "login.jpg";
            UrlConfig.SPLASH_URL = UrlConfig.LOGIN_BG_URL;
            UrlConfig.GAME_LOGO_URL = UrlConfig.PLATFORM_RES_URL + "logo.png";
            UrlConfig.LOADING_BG_URL = UrlConfig.PLATFORM_RES_URL + "loading.jpg";
            UrlConfig.SELECT_SERVER_BG_URL = UrlConfig.BASE_RES_UI_URL + "selectServer/pbg.png";
            UrlConfig.PANEL_BG_URL = UrlConfig.BASE_RES_UI_URL + "dialogBg/panelBg1.png";
            UrlConfig.LOGIN_TEST_BTN_URL = UrlConfig.BASE_RES_UI_URL + "login/testBtn.png";
        }
    }

    static initafter() {

        if (GlobalConfig.isMiniGameAll || Browser.onIOS) {
            if (Browser.onIOS && LayaGL.layaGPUInstance._compressedTexturePvrtc) {
                UrlConfig.VER_URL = "res/ver/veri.txt";
            }
        }

    }
    /**二维码 */
    public static get QQ_EWM(): string {
        return "res/ui/vipKefu/" + GlobalConfig.PlatName + "_qqewm.png";
        //return "res/ui/platform/" + GlobalConfig.PlatName + "/qqewm.png";
    }

    static getResCommon(skinName: string): string {
        return UrlConfig.BASE_RES_UI_URL + "common/" + skinName;
    }

    static getResCommon2(skinName: string): string {
        return UrlConfig.BASE_RES_UI_URL + "common2/" + skinName;
    }


    /**
     * 默认头像
     */
    static getDefaultHead(): string {
        return UrlConfig.GOODS_RES_URL + "199901.png";
    }

    /**
     * 默认头像框
     */
    static getDefaultHeadFrame(): string {
        return UrlConfig.getHead(2001);
    }

    /**获取头像 */
    static getHead(head: number): string {
        if (RoleDataCenter.instance.isBaseHead(head) || RoleDataCenter.instance.isBaseBodyShow(head)) {
            return UrlConfig.GOODS_RES_URL + `${head}.png`;
        }

        let cfg: cfg_role_profile = ConfigManager.cfg_profileCache.get(head);
        if (cfg) {
            if (cfg.need_hero_fashion > 0) {
                // let fashion: cfg_fashion = ConfigManager.cfg_fashionCache.get(cfg.need_hero_fashion);
                // if (fashion) {
                // return UrlConfig.GOODS_RES_URL + `${cfg.need_hero_fashion}.png`;
                // }
                let skin: cfg_hero_skin = ConfigManager.cfg_hero_skinCache.get(cfg.need_hero_fashion);
                if (skin) {
                    return UrlConfig.GOODS_RES_URL + `${skin.icon}.png`;
                }
            }
            if (cfg.need_vip != 0) {
                return UrlConfig.GOODS_RES_URL + cfg.fashion_id + ".png";
            }
            return UrlConfig.getHeroHead(cfg.need_hero_id);
        }
        return UrlConfig.GOODS_RES_URL + "199901.png";
    }

    /**获取头像框 */
    static getHeadFrame(head_frame: number): string {
        let cfg: cfg_role_profile = ConfigManager.cfg_profileCache.get(head_frame);
        if (cfg) {
            return UrlConfig.ROLE_TITLE_URL + "head_frame/" + cfg.head_img + ".png";
        }
        return UrlConfig.ROLE_TITLE_URL + "head_frame/2001_1.png";
    }
    /**获取头像框底板 */
    static getHeadFrameBg(head_frame: number): string {
        let cfg: cfg_role_profile = ConfigManager.cfg_profileCache.get(head_frame);
        if (cfg) {
            return UrlConfig.ROLE_TITLE_URL + "head_frame/" + cfg.ext_1 + ".png";
        }
        return UrlConfig.ROLE_TITLE_URL + "head_frame/frame_bg_def.png";
    }

    /** 获取称号资源路径 */
    static getRoleTitle(title_id: number, isBig: boolean = false): string {
        if (isBig == true) {
            return UrlConfig.BASE_RES_UI_URL + "title/big/" + title_id + ".png";
        }
        return UrlConfig.BASE_RES_UI_URL + "title/" + title_id + ".png";

    }

    /** 获取聊天称号资源路径 */
    static getChatRoleTitle(title_id: number): string {
        return UrlConfig.BASE_RES_UI_URL + "title/chat/" + title_id + ".png";

    }

    /** 通过配置，获取英雄头像 */
    static getHeroHeadByCfg(skin: string): string {
        if (skin && ConfigManager.cfg_client_w3_skinCache.has(skin)) {
            let cfg = ConfigManager.cfg_client_w3_skinCache.get(skin);
            return UrlConfig.GOODS_RES_URL + cfg.icon + ".png";
        }
        return UrlConfig.GOODS_RES_URL + "199901.png";
    }

    /** 通过英雄ID获取英雄头像 */
    static getHeroHead(typeId: number): string {
        if (typeId > 0) {
            let cfg = ConfigManager.cfg_hero_baseCache.get(typeId);
            return UrlConfig.getHeroHeadByCfg(cfg.skin);
        }
        return UrlConfig.GOODS_RES_URL + "199901.png";
    }

    /** 通过怪物ID获取怪物头像 */
    static getMonsterHead(typeId: number): string {
        if (typeId > 0) {
            let cfg = ConfigManager.cfg_monsterCache.get(typeId);
            return UrlConfig.getHeroHeadByCfg(cfg.skin);
        }
        return UrlConfig.GOODS_RES_URL + "199901.png";
    }

    /**获取武将卡牌头像 */
    static getHeroHeadBody(skin: string): string {
        if (skin && ConfigManager.cfg_client_w3_skinCache.has(skin)) {
            let cfg = ConfigManager.cfg_client_w3_skinCache.get(skin);
            return UrlConfig.HERO_RES_URL + `heroicon/${cfg.skin_half}.png`;
        }
        return UrlConfig.HERO_RES_URL + "heroicon/0.png";
    }

    /**获取英雄卡牌头像 */
    static getHeroHalfBody(skin: string): string {
        if (skin && ConfigManager.cfg_client_w3_skinCache.has(skin)) {
            let cfg = ConfigManager.cfg_client_w3_skinCache.get(skin);
            return UrlConfig.HERO_RES_URL + `halfBody/${cfg.skin_half}.png`;
        }
        return UrlConfig.HERO_RES_URL + "halfBody/0.png";
    }

    /***获得物品图标 */
    static getGoodsIcon(cfg: cfg_item): string {
        if (cfg) {
            return UrlConfig.GOODS_RES_URL + cfg.icon + ".png";
        }
        return "";
    }
    /***获得坐骑物品图标 */
    static getGoodsRideIcon(typeid: number, star: number = 0): string {
        // let cfg: cfg_ride_star = ConfigManager.getRideStar(typeid, star);
        // if (cfg) {
        //     return UrlConfig.GOODS_RES_URL + cfg.icon + ".png";
        // }
        return "";

    }
    /***通过道具id获得物品图标 */
    static getGoodsIconByTypeId(type_id: number): string {
        let cfg: cfg_item = ConfigManager.cfg_itemCache.get(type_id);
        return UrlConfig.getGoodsIcon(cfg);
    }

    /**获取武将2d模型 w7w8w9废弃了*/
    // static getHero2dModel(hero_type_id: number): string {
    //     let cfgBase: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(hero_type_id);
    //     if (cfgBase) {
    //         return UrlConfig.HERO_MODELS_PATH_2D + cfgBase.skin + ".png";
    //     }
    //     return "";
    // }

    /**根据形象获取2d模型 w7w8w9废弃了 */
    // static getHero2dModelByAppearance(appearanceId: number): string {
    //     let cfg: cfg_role_profile = ConfigManager.cfg_profileCache.get(appearanceId);
    //     if (cfg) {
    //         if (cfg.need_hero_id > 0) {
    //             return UrlConfig.getHero2dModel(cfg.need_hero_id);
    //         } else {
    //             return UrlConfig.HERO_MODELS_PATH_2D + cfg.head_img + ".png";
    //         }
    //     }
    //     return "";
    // }

    static getPrefixUrl(prefixLv: number): string {
        // let cfg = ConfigManager.cfg_prefixCache.get(prefixLv);
        // if (cfg && cfg.icon.length > 2) {
        //     return "common/title_" + cfg.prefix_level + ".png";
        // }
        return "";
    }
    static get RES_2D_ACTION(): string {
        return UrlConfig.RES_BASE_DIR_2D + "action/";
    }

    /**获取骨骼动画资源 */
    static get_res_2d_url(name: string, type: ESkeletonType, suffix = ".sk"): string {
        let resDir = "2d/";
        let animDir = "";
        switch (type) {
            case ESkeletonType.EFFECT: animDir = "effects"; break;
            case ESkeletonType.UI_EFFECT: animDir = "uieffect"; break;
            case ESkeletonType.MODEL_ACTION: animDir = "action"; break;
            case ESkeletonType.MODEL_LIHUI: animDir = "lihui"; break;
        }

        return UrlConfig.BASE_RES_URL + resDir + animDir + "/" + name + suffix;
    }


    static get_sk_url_by_mode(mode: ESkeletonResType, skName: string, type: ESkeletonType) {
        switch (mode) {
            case ESkeletonResType.SPINE_SKELETON:
                return UrlConfig.get_res_2d_spine_url(skName, type);
            case ESkeletonResType.SPINE_SKELETON_ZHAN_JI_SINGLE:
                return UrlConfig.get_res_2d_spine_zhanji_url(skName, type);
            case ESkeletonResType.SPINE_SKELETON_ZHAN_JI:
                return UrlConfig.get_res_2d_spine_zhanji_url(skName, type);
            case ESkeletonResType.SPINE_SKELETON_GROUP:
                return UrlConfig.get_res_2d_spine_group_url(skName, type);
            case ESkeletonResType.LAYA_SKELETON:
                return UrlConfig.get_res_2d_url(skName, type);
            case ESkeletonResType.LAYA_ANI:
                return UrlConfig.get_res_2d_ani_url(skName, type, mode);
            case ESkeletonResType.LAYA_ANI_FRAME:
                return UrlConfig.get_res_2d_ani_url(skName, type, mode);
            case ESkeletonResType.JY_ANI_3D_TO_2D:
                // return UrlConfig.get_res_2d_ani_url(skName, type, mode);
                return UrlConfig.JY_ANI_3D_TO_2D_DIR + skName + ".atlas";
        }
    }

    /**
     * 获取animation的.ani资源
     */
    static get_res_2d_ani_url(name: string, type: ESkeletonType, resType: ESkeletonResType): string {
        let url = "";
        switch (type) {
            case ESkeletonType.EFFECT:
                url = UrlConfig.LAYA_ANIMATION_MODEL_EFFECTS_PATH;
                break;
            case ESkeletonType.MODEL_ACTION:
                url = UrlConfig.LAYA_ANIMATION_MODEL_ACTION_PATH;
                break;
            case ESkeletonType.MODEL_LIHUI:
                url = UrlConfig.LAYA_ANIMATION_MODEL_LIHUI_PATH;
                break;
            case ESkeletonType.UI_EFFECT:
                url = UrlConfig.LAYA_ANIMATION_MODEL_UI_EFFECTS_PATH;
                break;
            default:
                console.error("-------------getLayaAnimatinUrl ,type error. type = " + type);
        }

        if(resType == ESkeletonResType.JY_ANI_3D_TO_2D){
            url = UrlConfig.JY_ANI_3D_TO_2D_DIR;
        }

        let suffix = resType == ESkeletonResType.LAYA_ANI ? ".ani" : ".atlas";
        return url + name + suffix;
    }

    // static getSkeletonPngUrl(name: string, type: ESkeletonType): string {
    //     let url = this.getSkeletonUrl(name, type);
    //     return url.replace(".sk", ".png").replace(".json", ".png").replace(".ani", ".png");
    // }

    static get_skeleton_type_by_url(url: string): ESkeletonType{
        if (url.indexOf(UrlConfig.SPINE_ACTION_PATH) >= 0 || url.indexOf(UrlConfig.LAYA_ANIMATION_MODEL_ACTION_PATH) >= 0) {
            return ESkeletonType.MODEL_ACTION;
        } else if (url.indexOf(UrlConfig.SPINE_EFFECTS_PATH) >= 0 || url.indexOf(UrlConfig.LAYA_ANIMATION_MODEL_EFFECTS_PATH) >= 0) {
            return ESkeletonType.EFFECT;
        } else if (url.indexOf(UrlConfig.SPINE_LIHUI_PATH) >= 0 || url.indexOf(UrlConfig.LAYA_ANIMATION_MODEL_LIHUI_PATH) >= 0) {
            return ESkeletonType.MODEL_LIHUI;
        } else if (url.indexOf(UrlConfig.SPINE_UI_EFFECTS_PATH) >= 0 || url.indexOf(UrlConfig.LAYA_ANIMATION_MODEL_UI_EFFECTS_PATH) >= 0) {
            return ESkeletonType.UI_EFFECT;
        }
        return ESkeletonType.DEFAULT;
    }
    /**
     * 获取spine的json资源
     * 少用这个,尽量用SkeletonManager.ins.getSkUrlByCheckMode
     */
    static get_res_2d_spine_url(name: string, type: ESkeletonType): string {
        let url = "";

        switch (type) {
            case ESkeletonType.EFFECT:
                url = UrlConfig.SPINE_EFFECTS_PATH;
                break;
            case ESkeletonType.MODEL_ACTION:
                url = UrlConfig.SPINE_ACTION_PATH;
                break;
            case ESkeletonType.MODEL_LIHUI:
                /**
                 * 优先读取lihui下的资源,如果找不到,则读取action下的show.json
                 */
                url = UrlConfig.SPINE_LIHUI_PATH;
                // if(name._has("/") == false){

                //     let vo = SkeletonData_Grp.getSkGrpVo(name, type);
                //     if (vo) {
                //         name += "/" + vo.groupSupportActionList[0];
                //     }else{
                //         url = UrlConfig.SPINE_ACTION_PATH;
                //         name += "/show";
                //     }
                // }

                break;
            case ESkeletonType.UI_EFFECT:
                url = UrlConfig.SPINE_UI_EFFECTS_PATH;
                break;
            default:
                ConsoleUtils.warn("-------------get_res_2d_spine_url ,type error. type = " + type);
                return "";

            // console.error("-------------getSkeletonJsonUrl ,type error. type = " + type);
        }

        let fullUrl = url + name + SkeletonData.JSON;
        if (this.checkResExist(fullUrl) == false) {
            fullUrl = url + name + SkeletonData.SKEL;
        }
        if (this.checkResExist(fullUrl) == false) {
            fullUrl = url + name + SkeletonData.BIN;
        }
        return fullUrl;
    }

    static get_res_2d_spine_group_url(name: string, type: ESkeletonType): string {
        return this.get_res_2d_spine_url(name, type).split(".")[0];
    }

    static get_res_2d_spine_zhanji_url(skName: string, type: ESkeletonType, suffix = SkeletonData.JSON): string {

        // let vo = SkeletonData_Grp.getSkGrpVo(skName, type);
        // suffix = vo ? vo.suffix : suffix;

        let url = "";
        switch (type) {
            case ESkeletonType.EFFECT:
                url = UrlConfig.SPINE_EFFECTS_ZHAN_JI_PATH;
                break;
            case ESkeletonType.MODEL_ACTION:
                url = UrlConfig.SPINE_ACTION_ZHAN_JI_PATH;
                break;
            case ESkeletonType.MODEL_LIHUI:
                url = UrlConfig.SPINE_LIHUI_ZHAN_JI_PATH;
                //renfazhanji立绘兼容
                if (skName.indexOf(SkeletonData.tag_slash) < 0) {
                    skName += "/show";
                }
                break;
            case ESkeletonType.UI_EFFECT:
                url = UrlConfig.SPINE_UI_EFFECTS_ZHAN_JI_PATH;
                break;
            default:
                ConsoleUtils.warn("-------------get_res_2d_spine_zhanji_url ,type error. type = " + type);
                return "";

            // console.error("-------------getSkeletonJsonUrl ,type error. type = " + type);
        }

        return url + skName + suffix;
    }
    /**获得英雄皮肤 */
    static getsetSkins(actor: p_fight_actor): string {
        var skin: string;
        var herobase: cfg_hero_base;
        let actor_type = actor.actor_type;
        if (ActorType.Hero == actor_type) {
            herobase = ConfigManager.cfg_hero_baseCache.get(actor.type_id);
            if (!herobase) {
                console.error("配置表 cfg_hero_base 找不到：", actor.type_id);
            }
            if (actor.skin_id == 0) {
                skin = herobase.skin;
                // skin = herobase.skin3d;
            } else {
                let skinId = 0;

                let cfg = ConfigManager.cfg_hero_skin_levelCache.get(actor.skin_id);
                if (cfg) {
                    skinId = cfg.skin_id;
                }
                else if (ConfigManager.cfg_hero_skinCache.has(actor.skin_id)) {
                    skinId = actor.skin_id;
                }
                let skinCfg = ConfigManager.cfg_hero_skinCache.get(skinId);
                skin = skinCfg ? skinCfg.skin : herobase.skin;
            }
        }
        else if (ActorType.Monster == actor_type || ActorType.TDMonster == actor_type || ActorType.TDBoss == actor_type) {
            let isShowBeast = false;
            for (let kv of actor.hero_ext) {
                if (kv.key == HeroExtKey.CLIENT_SHOW_BEAST) {
                    isShowBeast = kv.val == 1;
                }
            }
            var monsterCfg: cfg_monster = ConfigManager.cfg_monsterCache.get(actor.type_id);
            if (!monsterCfg) console.error("配置表 cfg_monster 找不到：", actor.type_id);
            skin = monsterCfg.skin;

            if (isShowBeast && ConfigManager.isBeast()) {//怪物默认显示兽将
                let list = ConfigManager.cfg_hero_skin_listCache.get(monsterCfg.hero_id);
                let heroSkinCfg = list ? list.find(cfg => cfg.is_beast == 1) : null;
                skin = heroSkinCfg && heroSkinCfg.skin || skin;
            }
        }
        else if (ActorType.Summoned == actor_type) {
            let cfg = CfgCacheMapMgr.cfg_skill_summonCache.get(actor.type_id);
            skin = cfg && cfg.skin_id || skin || "";
        }
        else if (ActorType.Lord == actor_type) {
            if (actor.skin_id) {
                skin = actor.skin_id + "";
            } else {
                let cfg = CfgCacheMapMgr.cfg_lord_baseCache.get(actor.type_id);
                if (cfg) {
                    skin = cfg.skin;
                }
            }
        }
        else if (ActorType.SoulCard == actor_type) {
            let cfg = CfgCacheMapMgr.cfg_client_w3_skinCache.get(actor.type_id);
            if (cfg) {
                skin = cfg.skin_id;
            }
        }
        return skin;
    }

    public static getResUIUrl(fileName: string, imgName: string): string {
        if (fileName.endsWith("/")) {
            return UrlConfig.BASE_RES_UI_URL + fileName + imgName;
        } else {
            return UrlConfig.BASE_RES_UI_URL + fileName + "/" + imgName;
        }
    }

    private static getPlatformRes(platform: number): string {
        return UrlConfig.PLATFORM_BASE_RES + platform + ".atlas";
    }

    public static get NowPlatformRes(): string {
        return UrlConfig.getPlatformRes(UrlConfig.platform);
    }



    private static getPlatformUrl(platform: number): string {
        return UrlConfig.PLATFORM_BASE + platform + "/";
    }

    public static get NowPlatformUrl(): string {
        return UrlConfig.getPlatformUrl(UrlConfig.platform);
    }

    public static get NowPlatform(): string {
        return "platform_" + UrlConfig.platform;
    }

    private static _platform: number = -1;
    public static get platform(): number {
        if (UrlConfig._platform == -1) {
            UrlConfig._platform = GlobalConfig.guajiindex < 1 ? 1 : GlobalConfig.guajiindex;
        }
        return UrlConfig._platform;
        // return 2;
    }

    public static get BASE_WORLD_MAP_TILED_URL(): string {
        return UrlConfig.NowPlatformUrl + "worldMap/";
    }

    public static get BASE_WORLD_MAP_TILED_RES_URL(): string {
        return UrlConfig.NowPlatformUrl + "worldMap/res/";
    }

    public static get FIGHT_MAP_URL(): string {
        return UrlConfig.NowPlatformUrl + "fightmap/";
    }

    public static get GUAJI_MAP_URL(): string {
        return UrlConfig.NowPlatformUrl + "guajimap/";
    }

    public static get GUAJI_BUILD_URL(): string {
        return UrlConfig.NowPlatformUrl + "guajimap/guaJiBuild/";
    }

    /**
     * laya原生方式的特效路径
     * @param effName 特效名称
     * @param subDir 子目录
     */
    public static get_effect_laya_way_url(effName: string, subDir: string = "") {
        let url = UrlConfig.EFFECT_LAYA_WAY_PATH;
        if (subDir) {
            url += subDir + "/";
        }

        url += effName + ".atlas";
        return url;
    }

    static getUIUrl(): any {
        return { url: UrlConfig.UI_URL, type: ILaya.Loader.JSON };
    }

    static getUIBinUrl(): any {
        return { url: UrlConfig.UIBIN_URL, type: ILaya.Loader.BUFFER };
    }

    static getUIThemeUrl(): any {
        return { url: UrlConfig.UITHEME_URL, type: ILaya.Loader.BUFFER };
    }

    /**
     * 判断资源是否存在,主要判断Url.version
     */
    public static checkResExist(resUrl): boolean {
        return !!URL.version[resUrl];
    }
}