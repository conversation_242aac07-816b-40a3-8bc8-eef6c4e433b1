import { Event } from "laya/events/Event";
import { ConfigManager } from "../../../managers/ConfigManager";
import { SocketCommand } from "../../../proto/SocketCommand";
import { com } from "../../../ui/layaMaxUI";
import { TipsUtil } from "../../../util/TipsUtil";
import { UIList } from "../../baseModules/UIList";
import { GuanDuFightDataCenter } from "../data/GuanDuFightDataCenter";
import GuanDuHeroItem from "../view/GuanDuHeroItem";
import { EYueKaType, MatchConst } from "../../../auto/ConstAuto";
export default class GuanDuReadyDialog extends com.ui.res.guanDu.GuanDuReadyDialogUI {
    constructor() {
        super();
    }
    initUI() {
        this.navShow = 0;
        this.topIns.titleNameTxt.text = window.iLang.L2_GONG_PO_JU_DIAN.il();
        this.myHeroItem = this.myHeroItemUI;
        this.myHeroItem.setSize(108, 108);
        this.monsterHeroItem = this.monsterHeroItemUI;
        this.monsterHeroItem.setSize(108, 108);
        this.heroList = UIList.SetUIList(this, this.heroListBox, GuanDuHeroItem, this.onSelectHero, false);
        this.heroList.SetSpace(18);
        this.heroList.isBoxCenter = true;
        this.txtBossName.text = "";
        //战斗力
        this.txtPower.text = "0";
    }
    addClick() {
        this.addOnClick(this, this.btnFight, this.onClickBtnFight);
        this.cbSkipFight.on(Event.CHANGE, this, this.onCbSkipFightChange);
    }
    addEvent() {
        this.addEventListener("CHANGE_GUAN_DU_HERO_SELECT" /* CHANGE_GUAN_DU_HERO_SELECT */, this, this.onChangeMySelectHero);
        this.addEventListener(SocketCommand.FIGHT_SIMP_RESULT, this, this.m_fight_simp_result_toc);
        this.addEventListener("UPDATE_GUAN_DU_HERO_LIST" /* UPDATE_GUAN_DU_HERO_LIST */, this, this.updateHeroListView);
        this.addEventListener("UPDATE_GUAN_DU_MONSTER_LIST" /* UPDATE_GUAN_DU_MONSTER_LIST */, this, this.updateMonsterInfo);
    }
    onOpen(param) {
        let info = param.info;
        this.monsterInfo = info;
        if (!info) {
            this.close("none");
            return;
        }
        //怪物信息
        this.updateMonsterInfo();
        this.onChangeMySelectHero();
        //英雄列表
        let dataList = GuanDuFightDataCenter.instance.getHeroList();
        this.heroList.array = dataList;
        let idx = 0;
        for (let i = 0; i < dataList.length; i++) {
            if (dataList[i].hero_id == GuanDuFightDataCenter.instance.selectHeroId) {
                idx = i;
                break;
            }
        }
        this.heroList.selectedIndex = idx;
        //是否勾选跳过战斗
        this.cbSkipFight.selected = GuanDuFightDataCenter.instance.isSelectSkipFight;
        this.cbSkipFight.visible = GuanDuFightDataCenter.instance.isShowSkipFightCheckBox();
        this.txtOpenSkipLimit.visible = !this.cbSkipFight.visible;
        if (this.txtOpenSkipLimit.visible) {
            // let nums = MiscConst.guandu_pass_limit;
            // // this.txtOpenSkipLimit.text = window.i18n(`{0}关之后可跳过战斗`,[nums[0]]);
            // this.txtOpenSkipLimit.text = window.i18n(`VIP{0}可跳过战斗`,[nums[1]]);
            let tip = "";
            if (ConfigManager.cfg_fuli_yuekaCache.has(EYueKaType.TYPE_4_KING)) {
                let cfgYueka = ConfigManager.cfg_fuli_yuekaCache.get(EYueKaType.TYPE_4_KING);
                tip = window.iLang.L2_JI_HUO_P0_KE_TIAO_GUO_ZHAN_DOU.il([cfgYueka.yueka_name]);
            }
            this.txtOpenSkipLimit.text = tip;
        }
    }
    updateMonsterInfo() {
        if (!this.monsterInfo)
            return;
        let info = GuanDuFightDataCenter.instance.getMonsterInfoByLevel(this.monsterInfo.level);
        let cfgGuanDu = GuanDuFightDataCenter.instance.getGuanDuCfg(info.level);
        this.txtBossOrder.text = info.level >= 5 ? "BOSS" : window.iLang.L2_SHOU_WEI.il() + info.level;
        if (cfgGuanDu) {
            let cfgBase = ConfigManager.cfg_hero_baseCache.get(cfgGuanDu.hero_type_id);
            this.monsterHeroItem.setSimpleInfoByCfg(cfgBase, cfgGuanDu.hero_star, cfgGuanDu.hero_level, "");
            this.monsterHeroItem.setHp(info.hp_rate, 100);
            this.txtBossName.text = cfgBase.name;
        }
    }
    updateHeroListView() {
        let list = GuanDuFightDataCenter.instance.getHeroList();
        this.heroList.array = list;
        let idx = -1;
        let aliveIdx = -1; //活着的英雄索引
        for (let i = 0; i < list.length; i++) {
            if (list[i].hp_rate > 0 && list[i].hero_id == GuanDuFightDataCenter.instance.selectHeroId) {
                idx = i;
            }
            if (aliveIdx == -1 && list[i].hp_rate > 0) {
                aliveIdx = i;
            }
        }
        idx = idx != -1 ? idx : aliveIdx;
        if (idx == -1) {
            GuanDuFightDataCenter.instance.selectHeroId = 0;
            this.close(); //全部阵亡 关闭界面
            return;
        }
        this.heroList.selectedIndex = idx;
    }
    onChangeMySelectHero() {
        let selectHero = GuanDuFightDataCenter.instance.getHeroByHeroId(GuanDuFightDataCenter.instance.selectHeroId);
        if (selectHero) {
            let cfgBase = ConfigManager.cfg_hero_baseCache.get(selectHero.hero_type_id);
            this.myHeroItem.setSimpleInfoByCfg(cfgBase, selectHero.star, selectHero.level, "");
            this.myHeroItem.setHp(selectHero.hp_rate, 100);
            //战斗力
            this.txtPower.text = selectHero.power + "";
        }
        else {
            //没有选择英雄
            this.myHeroItem.setDataSource(null, 0, null);
            this.myHeroItem.hpBar.visible = false;
            //战斗力
            this.txtPower.text = "0";
        }
    }
    onSelectHero(itemData) {
        if (!itemData || !itemData.data)
            return;
        let hero = itemData.data;
        let hero_id = 0;
        if (hero) {
            if (hero.hp_rate > 0) {
                hero_id = hero.hero_id;
            }
            else {
                TipsUtil.showTips(window.iLang.L2_HERO_YI_ZHEN_WANG.il());
                hero_id = GuanDuFightDataCenter.instance.selectHeroId;
            }
        }
        GuanDuFightDataCenter.instance.selectHeroId = hero_id;
    }
    onCbSkipFightChange() {
        // let isSelect = this.cbSkipFight.selected;
        // if(isSelect){
        //     //判断是否满足等级条件
        //     let cfg = ConfigManager.cfg_match_typeCache.get(GameConst.MATCH_TYPE_GUAN_DU);
        //     if(cfg && cfg.fast_pass > 0 && DataCenter.myLevel < cfg.fast_pass_lv){
        //         this.cbSkipFight.selected = false;
        //         TipsUtil.showTips(cfg.fast_pass_lv+"级可跳过战斗");
        //         return;
        //     }
        // }
    }
    onClickBtnFight() {
        if (!this.monsterInfo)
            return;
        let isSkip = this.cbSkipFight.selected;
        GuanDuFightDataCenter.guandu_fight_start_tos(this.monsterInfo.level, isSkip, this.txtBossName.text);
    }
    onClosed(type = null) {
        if (type != "none") {
            GuanDuFightDataCenter.instance.isSelectSkipFight = this.cbSkipFight.selected;
        }
        super.onClosed(type);
    }
    m_fight_simp_result_toc(toc) {
        if (toc.is_success == true) {
            if (toc.match_type == MatchConst.MATCH_TYPE_GUAN_DU) {
                //官渡胜利不需要弹窗
                this.close();
                return;
            }
        }
        else {
        }
    }
}
