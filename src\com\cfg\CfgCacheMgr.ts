// gen by exportxlsx.py

import { ILaya } from "ILaya";
import { Event } from "laya/events/Event";
import { EventDispatcher } from "laya/events/EventDispatcher";
import { Byte } from "laya/utils/Byte";
import { CfgCacheBase } from "./CfgCacheBase";

import { cfg_acc_gift } from "./vo/cfg_acc_gift";
import { cfg_achievement } from "./vo/cfg_achievement";
import { cfg_activity_client } from "./vo/cfg_activity_client";
import { cfg_activity_desc } from "./vo/cfg_activity_desc";
import { cfg_activity_icon } from "./vo/cfg_activity_icon";
import { cfg_activity_limit_sign } from "./vo/cfg_activity_limit_sign";
import { cfg_activity_misc } from "./vo/cfg_activity_misc";
import { cfg_activity_mission } from "./vo/cfg_activity_mission";
import { cfg_activity_notice } from "./vo/cfg_activity_notice";
import { cfg_activity_page } from "./vo/cfg_activity_page";
import { cfg_activity_yueka } from "./vo/cfg_activity_yueka";
import { cfg_ad_show } from "./vo/cfg_ad_show";
import { cfg_agent_review } from "./vo/cfg_agent_review";
import { cfg_all_pinyin_dict } from "./vo/cfg_all_pinyin_dict";
import { cfg_arena } from "./vo/cfg_arena";
import { cfg_arena_match } from "./vo/cfg_arena_match";
import { cfg_arena_match_guess } from "./vo/cfg_arena_match_guess";
import { cfg_arena_max_reward } from "./vo/cfg_arena_max_reward";
import { cfg_arena_rank } from "./vo/cfg_arena_rank";
import { cfg_arena_reward } from "./vo/cfg_arena_reward";
import { cfg_arena_skip_limit } from "./vo/cfg_arena_skip_limit";
import { cfg_arena_weekly_reward } from "./vo/cfg_arena_weekly_reward";
import { cfg_ares_palace } from "./vo/cfg_ares_palace";
import { cfg_authorized_gifts } from "./vo/cfg_authorized_gifts";
import { cfg_bag_page } from "./vo/cfg_bag_page";
import { cfg_bai_jiang_gift } from "./vo/cfg_bai_jiang_gift";
import { cfg_battle_fly_name } from "./vo/cfg_battle_fly_name";
import { cfg_battle_trial_buff } from "./vo/cfg_battle_trial_buff";
import { cfg_battle_trial_buff_reset } from "./vo/cfg_battle_trial_buff_reset";
import { cfg_battle_trial_guaji_monster } from "./vo/cfg_battle_trial_guaji_monster";
import { cfg_battle_trial_misc } from "./vo/cfg_battle_trial_misc";
import { cfg_battle_trial_pass_guanqia } from "./vo/cfg_battle_trial_pass_guanqia";
import { cfg_battle_trial_pass_reward } from "./vo/cfg_battle_trial_pass_reward";
import { cfg_beast_platform } from "./vo/cfg_beast_platform";
import { cfg_bingfa } from "./vo/cfg_bingfa";
import { cfg_bingfa_ext } from "./vo/cfg_bingfa_ext";
import { cfg_bingfu } from "./vo/cfg_bingfu";
import { cfg_bingfu_discompose_recast } from "./vo/cfg_bingfu_discompose_recast";
import { cfg_bingfu_recast_lock } from "./vo/cfg_bingfu_recast_lock";
import { cfg_bingfu_refine } from "./vo/cfg_bingfu_refine";
import { cfg_bingfu_upgrade } from "./vo/cfg_bingfu_upgrade";
import { cfg_boat_peak_misc } from "./vo/cfg_boat_peak_misc";
import { cfg_boat_peak_rank } from "./vo/cfg_boat_peak_rank";
import { cfg_boat_peak_time } from "./vo/cfg_boat_peak_time";
import { cfg_boat_racc_rank_gold } from "./vo/cfg_boat_racc_rank_gold";
import { cfg_boat_race_auction_reward_type } from "./vo/cfg_boat_race_auction_reward_type";
import { cfg_boat_race_item } from "./vo/cfg_boat_race_item";
import { cfg_boat_race_misc } from "./vo/cfg_boat_race_misc";
import { cfg_buff } from "./vo/cfg_buff";
import { cfg_buff_type } from "./vo/cfg_buff_type";
import { cfg_building } from "./vo/cfg_building";
import { cfg_building_lv } from "./vo/cfg_building_lv";
import { cfg_building_mission } from "./vo/cfg_building_mission";
import { cfg_building_plot_dialogue } from "./vo/cfg_building_plot_dialogue";
import { cfg_buy_times } from "./vo/cfg_buy_times";
import { cfg_buy_times_type } from "./vo/cfg_buy_times_type";
import { cfg_cast_soul_active } from "./vo/cfg_cast_soul_active";
import { cfg_casting_soul } from "./vo/cfg_casting_soul";
import { cfg_casting_soul_buff } from "./vo/cfg_casting_soul_buff";
import { cfg_chapter_dialog } from "./vo/cfg_chapter_dialog";
import { cfg_chapter_script } from "./vo/cfg_chapter_script";
import { cfg_chat_bullet_msg } from "./vo/cfg_chat_bullet_msg";
import { cfg_chat_channel } from "./vo/cfg_chat_channel";
import { cfg_chat_skin } from "./vo/cfg_chat_skin";
import { cfg_chat_skin_widget } from "./vo/cfg_chat_skin_widget";
import { cfg_client_lang } from "./vo/cfg_client_lang";
import { cfg_client_w3_effect } from "./vo/cfg_client_w3_effect";
import { cfg_client_w3_effect_desc } from "./vo/cfg_client_w3_effect_desc";
import { cfg_client_w3_skill } from "./vo/cfg_client_w3_skill";
import { cfg_client_w3_skill_desc } from "./vo/cfg_client_w3_skill_desc";
import { cfg_client_w3_skin } from "./vo/cfg_client_w3_skin";
import { cfg_cmd } from "./vo/cfg_cmd";
import { cfg_code_cli } from "./vo/cfg_code_cli";
import { cfg_country_war_misc } from "./vo/cfg_country_war_misc";
import { cfg_cross_team_match_type } from "./vo/cfg_cross_team_match_type";
import { cfg_cross_team_misc } from "./vo/cfg_cross_team_misc";
import { cfg_cross_test_tower_open } from "./vo/cfg_cross_test_tower_open";
import { cfg_crush_fight_condition } from "./vo/cfg_crush_fight_condition";
import { cfg_crush_fight_mission } from "./vo/cfg_crush_fight_mission";
import { cfg_crush_fight_mission_type } from "./vo/cfg_crush_fight_mission_type";
import { cfg_csc_fmsolo_buff } from "./vo/cfg_csc_fmsolo_buff";
import { cfg_csc_fmsolo_challenge } from "./vo/cfg_csc_fmsolo_challenge";
import { cfg_csc_fmsolo_etc } from "./vo/cfg_csc_fmsolo_etc";
import { cfg_csc_fmsolo_log } from "./vo/cfg_csc_fmsolo_log";
import { cfg_csc_fmsolo_reward } from "./vo/cfg_csc_fmsolo_reward";
import { cfg_csc_fmsolo_shop_lv } from "./vo/cfg_csc_fmsolo_shop_lv";
import { cfg_csclan } from "./vo/cfg_csclan";
import { cfg_csclan_etc } from "./vo/cfg_csclan_etc";
import { cfg_csclan_solo_buff } from "./vo/cfg_csclan_solo_buff";
import { cfg_csclan_solo_challenge } from "./vo/cfg_csclan_solo_challenge";
import { cfg_csclan_solo_etc } from "./vo/cfg_csclan_solo_etc";
import { cfg_csclan_solo_log } from "./vo/cfg_csclan_solo_log";
import { cfg_csclan_solo_reward } from "./vo/cfg_csclan_solo_reward";
import { cfg_csclan_solo_shop_lv } from "./vo/cfg_csclan_solo_shop_lv";
import { cfg_daily_copy } from "./vo/cfg_daily_copy";
import { cfg_daily_copy_discount } from "./vo/cfg_daily_copy_discount";
import { cfg_daily_copy_monster } from "./vo/cfg_daily_copy_monster";
import { cfg_daily_copy_type } from "./vo/cfg_daily_copy_type";
import { cfg_daily_mission } from "./vo/cfg_daily_mission";
import { cfg_daily_mission_gift } from "./vo/cfg_daily_mission_gift";
import { cfg_daily_new_discount_rebate } from "./vo/cfg_daily_new_discount_rebate";
import { cfg_daily_pay } from "./vo/cfg_daily_pay";
import { cfg_dawanka } from "./vo/cfg_dawanka";
import { cfg_dawanka_tequan } from "./vo/cfg_dawanka_tequan";
import { cfg_day_acc_pay_gift } from "./vo/cfg_day_acc_pay_gift";
import { cfg_deputy } from "./vo/cfg_deputy";
import { cfg_deputy_level } from "./vo/cfg_deputy_level";
import { cfg_deputy_star } from "./vo/cfg_deputy_star";
import { cfg_device_excursion } from "./vo/cfg_device_excursion";
import { cfg_divine } from "./vo/cfg_divine";
import { cfg_divine_activate } from "./vo/cfg_divine_activate";
import { cfg_divine_copy } from "./vo/cfg_divine_copy";
import { cfg_divine_misc } from "./vo/cfg_divine_misc";
import { cfg_divine_strengthen } from "./vo/cfg_divine_strengthen";
import { cfg_divine_wear } from "./vo/cfg_divine_wear";
import { cfg_dominate_pvp } from "./vo/cfg_dominate_pvp";
import { cfg_dominate_pvp_limit } from "./vo/cfg_dominate_pvp_limit";
import { cfg_dominate_pvp_reward } from "./vo/cfg_dominate_pvp_reward";
import { cfg_dominate_pvp_task } from "./vo/cfg_dominate_pvp_task";
import { cfg_dominate_pvp_task_rewards } from "./vo/cfg_dominate_pvp_task_rewards";
import { cfg_drop_group } from "./vo/cfg_drop_group";
import { cfg_eight_login } from "./vo/cfg_eight_login";
import { cfg_epic_battle_tips } from "./vo/cfg_epic_battle_tips";
import { cfg_equip } from "./vo/cfg_equip";
import { cfg_equip_compose } from "./vo/cfg_equip_compose";
import { cfg_equip_suit } from "./vo/cfg_equip_suit";
import { cfg_equip_suit_ext } from "./vo/cfg_equip_suit_ext";
import { cfg_fail_tips } from "./vo/cfg_fail_tips";
import { cfg_family } from "./vo/cfg_family";
import { cfg_family_active_attr } from "./vo/cfg_family_active_attr";
import { cfg_family_active_mission } from "./vo/cfg_family_active_mission";
import { cfg_family_boss } from "./vo/cfg_family_boss";
import { cfg_family_boss_attr } from "./vo/cfg_family_boss_attr";
import { cfg_family_boss_misc } from "./vo/cfg_family_boss_misc";
import { cfg_family_boss_rank } from "./vo/cfg_family_boss_rank";
import { cfg_family_etc } from "./vo/cfg_family_etc";
import { cfg_family_hongbao } from "./vo/cfg_family_hongbao";
import { cfg_family_hongbao_blessing } from "./vo/cfg_family_hongbao_blessing";
import { cfg_family_hongbao_reward } from "./vo/cfg_family_hongbao_reward";
import { cfg_family_log } from "./vo/cfg_family_log";
import { cfg_family_random_name } from "./vo/cfg_family_random_name";
import { cfg_family_science } from "./vo/cfg_family_science";
import { cfg_family_science_times } from "./vo/cfg_family_science_times";
import { cfg_family_sign } from "./vo/cfg_family_sign";
import { cfg_family_sign_active } from "./vo/cfg_family_sign_active";
import { cfg_fight_show } from "./vo/cfg_fight_show";
import { cfg_first_pay } from "./vo/cfg_first_pay";
import { cfg_fish } from "./vo/cfg_fish";
import { cfg_fish_attr } from "./vo/cfg_fish_attr";
import { cfg_fish_color } from "./vo/cfg_fish_color";
import { cfg_fish_drum_level } from "./vo/cfg_fish_drum_level";
import { cfg_fish_fishbowl } from "./vo/cfg_fish_fishbowl";
import { cfg_fish_handbook } from "./vo/cfg_fish_handbook";
import { cfg_fish_help_gift } from "./vo/cfg_fish_help_gift";
import { cfg_fish_level } from "./vo/cfg_fish_level";
import { cfg_fish_map } from "./vo/cfg_fish_map";
import { cfg_fish_misc } from "./vo/cfg_fish_misc";
import { cfg_fish_mission } from "./vo/cfg_fish_mission";
import { cfg_fish_official } from "./vo/cfg_fish_official";
import { cfg_fish_official_position } from "./vo/cfg_fish_official_position";
import { cfg_fish_reshape } from "./vo/cfg_fish_reshape";
import { cfg_fish_resources } from "./vo/cfg_fish_resources";
import { cfg_fish_rod_level } from "./vo/cfg_fish_rod_level";
import { cfg_fish_slot } from "./vo/cfg_fish_slot";
import { cfg_fish_stage } from "./vo/cfg_fish_stage";
import { cfg_fly_font_type } from "./vo/cfg_fly_font_type";
import { cfg_fuli_sign } from "./vo/cfg_fuli_sign";
import { cfg_fuli_sign_acc } from "./vo/cfg_fuli_sign_acc";
import { cfg_fuli_sign_day } from "./vo/cfg_fuli_sign_day";
import { cfg_fuli_token } from "./vo/cfg_fuli_token";
import { cfg_fuli_token_type } from "./vo/cfg_fuli_token_type";
import { cfg_fuli_yueka } from "./vo/cfg_fuli_yueka";
import { cfg_game_desc } from "./vo/cfg_game_desc";
import { cfg_game_desc_2 } from "./vo/cfg_game_desc_2";
import { cfg_game_desc_3 } from "./vo/cfg_game_desc_3";
import { cfg_general_pass_mission } from "./vo/cfg_general_pass_mission";
import { cfg_general_pass_reward } from "./vo/cfg_general_pass_reward";
import { cfg_general_pass_type } from "./vo/cfg_general_pass_type";
import { cfg_gift } from "./vo/cfg_gift";
import { cfg_god_equip } from "./vo/cfg_god_equip";
import { cfg_god_equip_compose } from "./vo/cfg_god_equip_compose";
import { cfg_god_equip_convert } from "./vo/cfg_god_equip_convert";
import { cfg_god_equip_enchant } from "./vo/cfg_god_equip_enchant";
import { cfg_god_equip_enchant_cost } from "./vo/cfg_god_equip_enchant_cost";
import { cfg_god_equip_suit } from "./vo/cfg_god_equip_suit";
import { cfg_god_equip_type } from "./vo/cfg_god_equip_type";
import { cfg_god_trial } from "./vo/cfg_god_trial";
import { cfg_god_trial_buff } from "./vo/cfg_god_trial_buff";
import { cfg_god_weapon } from "./vo/cfg_god_weapon";
import { cfg_god_weapon_level } from "./vo/cfg_god_weapon_level";
import { cfg_god_weapon_mission } from "./vo/cfg_god_weapon_mission";
import { cfg_god_weapon_refine } from "./vo/cfg_god_weapon_refine";
import { cfg_god_weapon_skill } from "./vo/cfg_god_weapon_skill";
import { cfg_god_weapon_skill_attr } from "./vo/cfg_god_weapon_skill_attr";
import { cfg_god_weapon_soul } from "./vo/cfg_god_weapon_soul";
import { cfg_god_weapon_star } from "./vo/cfg_god_weapon_star";
import { cfg_gray_pinyin } from "./vo/cfg_gray_pinyin";
import { cfg_grow_tips } from "./vo/cfg_grow_tips";
import { cfg_guaji_box_time } from "./vo/cfg_guaji_box_time";
import { cfg_guaji_monster } from "./vo/cfg_guaji_monster";
import { cfg_guaji_quick_navigation } from "./vo/cfg_guaji_quick_navigation";
import { cfg_guandu } from "./vo/cfg_guandu";
import { cfg_guandu_answer } from "./vo/cfg_guandu_answer";
import { cfg_guandu_chose } from "./vo/cfg_guandu_chose";
import { cfg_guandu_floor } from "./vo/cfg_guandu_floor";
import { cfg_guandu_mission } from "./vo/cfg_guandu_mission";
import { cfg_guandu_shop } from "./vo/cfg_guandu_shop";
import { cfg_guide_helper } from "./vo/cfg_guide_helper";
import { cfg_guide_helper_1 } from "./vo/cfg_guide_helper_1";
import { cfg_guide_helper_2 } from "./vo/cfg_guide_helper_2";
import { cfg_guide_helper_3 } from "./vo/cfg_guide_helper_3";
import { cfg_guide_helper_4 } from "./vo/cfg_guide_helper_4";
import { cfg_guide_helper_debug } from "./vo/cfg_guide_helper_debug";
import { cfg_guide_helper_game_1 } from "./vo/cfg_guide_helper_game_1";
import { cfg_guide_helper_m2 } from "./vo/cfg_guide_helper_m2";
import { cfg_guide_helper_m3 } from "./vo/cfg_guide_helper_m3";
import { cfg_guide_mission } from "./vo/cfg_guide_mission";
import { cfg_guide_mission_1 } from "./vo/cfg_guide_mission_1";
import { cfg_guide_mission_2 } from "./vo/cfg_guide_mission_2";
import { cfg_guide_mission_3 } from "./vo/cfg_guide_mission_3";
import { cfg_guide_mission_4 } from "./vo/cfg_guide_mission_4";
import { cfg_guide_mission_game_1 } from "./vo/cfg_guide_mission_game_1";
import { cfg_guide_mission_m2 } from "./vo/cfg_guide_mission_m2";
import { cfg_guide_mission_m3 } from "./vo/cfg_guide_mission_m3";
import { cfg_guide_review } from "./vo/cfg_guide_review";
import { cfg_guide_story } from "./vo/cfg_guide_story";
import { cfg_hero_attr_addition } from "./vo/cfg_hero_attr_addition";
import { cfg_hero_attr_source } from "./vo/cfg_hero_attr_source";
import { cfg_hero_bag } from "./vo/cfg_hero_bag";
import { cfg_hero_base } from "./vo/cfg_hero_base";
import { cfg_hero_cheer } from "./vo/cfg_hero_cheer";
import { cfg_hero_cheer_bonus } from "./vo/cfg_hero_cheer_bonus";
import { cfg_hero_cheer_level } from "./vo/cfg_hero_cheer_level";
import { cfg_hero_cheer_unlock } from "./vo/cfg_hero_cheer_unlock";
import { cfg_hero_chip_star } from "./vo/cfg_hero_chip_star";
import { cfg_hero_come_mission } from "./vo/cfg_hero_come_mission";
import { cfg_hero_convert } from "./vo/cfg_hero_convert";
import { cfg_hero_convert_weight } from "./vo/cfg_hero_convert_weight";
import { cfg_hero_cost_plan } from "./vo/cfg_hero_cost_plan";
import { cfg_hero_evolve_skill } from "./vo/cfg_hero_evolve_skill";
import { cfg_hero_handbook_desc } from "./vo/cfg_hero_handbook_desc";
import { cfg_hero_level } from "./vo/cfg_hero_level";
import { cfg_hero_level_limit } from "./vo/cfg_hero_level_limit";
import { cfg_hero_nation } from "./vo/cfg_hero_nation";
import { cfg_hero_pass_mission } from "./vo/cfg_hero_pass_mission";
import { cfg_hero_pass_reward } from "./vo/cfg_hero_pass_reward";
import { cfg_hero_recommend_pre } from "./vo/cfg_hero_recommend_pre";
import { cfg_hero_recycle } from "./vo/cfg_hero_recycle";
import { cfg_hero_recycle_change } from "./vo/cfg_hero_recycle_change";
import { cfg_hero_recycle_change_star_stage } from "./vo/cfg_hero_recycle_change_star_stage";
import { cfg_hero_recycle_res } from "./vo/cfg_hero_recycle_res";
import { cfg_hero_recycle_special_switch } from "./vo/cfg_hero_recycle_special_switch";
import { cfg_hero_resonate_dhyana } from "./vo/cfg_hero_resonate_dhyana";
import { cfg_hero_resonate_dudu } from "./vo/cfg_hero_resonate_dudu";
import { cfg_hero_resonate_dudu_level } from "./vo/cfg_hero_resonate_dudu_level";
import { cfg_hero_resonate_five } from "./vo/cfg_hero_resonate_five";
import { cfg_hero_skin } from "./vo/cfg_hero_skin";
import { cfg_hero_skin_level } from "./vo/cfg_hero_skin_level";
import { cfg_hero_stage } from "./vo/cfg_hero_stage";
import { cfg_hero_stage_limit } from "./vo/cfg_hero_stage_limit";
import { cfg_hero_star } from "./vo/cfg_hero_star";
import { cfg_hero_star_attr } from "./vo/cfg_hero_star_attr";
import { cfg_hero_star_limit } from "./vo/cfg_hero_star_limit";
import { cfg_hero_star_stage } from "./vo/cfg_hero_star_stage";
import { cfg_hero_star_stage_attr } from "./vo/cfg_hero_star_stage_attr";
import { cfg_hero_strengthen } from "./vo/cfg_hero_strengthen";
import { cfg_hero_upgrade_tips } from "./vo/cfg_hero_upgrade_tips";
import { cfg_hero_zhouyin } from "./vo/cfg_hero_zhouyin";
import { cfg_hunt_buy } from "./vo/cfg_hunt_buy";
import { cfg_hunt_cost } from "./vo/cfg_hunt_cost";
import { cfg_hunt_desc } from "./vo/cfg_hunt_desc";
import { cfg_hunt_gift } from "./vo/cfg_hunt_gift";
import { cfg_hunt_rewards_show } from "./vo/cfg_hunt_rewards_show";
import { cfg_huoqutujing } from "./vo/cfg_huoqutujing";
import { cfg_hzzd_achievement } from "./vo/cfg_hzzd_achievement";
import { cfg_hzzd_event } from "./vo/cfg_hzzd_event";
import { cfg_hzzd_kills_reward } from "./vo/cfg_hzzd_kills_reward";
import { cfg_hzzd_misc } from "./vo/cfg_hzzd_misc";
import { cfg_i18n_lang } from "./vo/cfg_i18n_lang";
import { cfg_i18n_ts } from "./vo/cfg_i18n_ts";
import { cfg_i18n_ui } from "./vo/cfg_i18n_ui";
import { cfg_ingenious_plan } from "./vo/cfg_ingenious_plan";
import { cfg_ingenious_plan_compose } from "./vo/cfg_ingenious_plan_compose";
import { cfg_ingenious_plan_convert } from "./vo/cfg_ingenious_plan_convert";
import { cfg_ingenious_plan_level } from "./vo/cfg_ingenious_plan_level";
import { cfg_ingenious_plan_stage } from "./vo/cfg_ingenious_plan_stage";
import { cfg_ingenious_plan_star } from "./vo/cfg_ingenious_plan_star";
import { cfg_ip_set } from "./vo/cfg_ip_set";
import { cfg_item } from "./vo/cfg_item";
import { cfg_item_compose } from "./vo/cfg_item_compose";
import { cfg_item_time_client } from "./vo/cfg_item_time_client";
import { cfg_large_peak_agent } from "./vo/cfg_large_peak_agent";
import { cfg_large_peak_battle_table } from "./vo/cfg_large_peak_battle_table";
import { cfg_large_peak_misc } from "./vo/cfg_large_peak_misc";
import { cfg_large_peak_rank } from "./vo/cfg_large_peak_rank";
import { cfg_large_peak_season } from "./vo/cfg_large_peak_season";
import { cfg_large_peak_time } from "./vo/cfg_large_peak_time";
import { cfg_lazy_load } from "./vo/cfg_lazy_load";
import { cfg_lcqs_acc_star_reward } from "./vo/cfg_lcqs_acc_star_reward";
import { cfg_lcqs_chapter_open } from "./vo/cfg_lcqs_chapter_open";
import { cfg_lcqs_floor_reward } from "./vo/cfg_lcqs_floor_reward";
import { cfg_lcqs_mission } from "./vo/cfg_lcqs_mission";
import { cfg_level } from "./vo/cfg_level";
import { cfg_level_gift } from "./vo/cfg_level_gift";
import { cfg_limit_hero_skin_chip_exchange } from "./vo/cfg_limit_hero_skin_chip_exchange";
import { cfg_lineup_buff } from "./vo/cfg_lineup_buff";
import { cfg_lineup_buff_icon } from "./vo/cfg_lineup_buff_icon";
import { cfg_lineup_career_rule } from "./vo/cfg_lineup_career_rule";
import { cfg_lineup_num } from "./vo/cfg_lineup_num";
import { cfg_lineup_recommend } from "./vo/cfg_lineup_recommend";
import { cfg_lineup_style } from "./vo/cfg_lineup_style";
import { cfg_load_tips } from "./vo/cfg_load_tips";
import { cfg_login_activity } from "./vo/cfg_login_activity";
import { cfg_login_activity_round } from "./vo/cfg_login_activity_round";
import { cfg_lord_activation } from "./vo/cfg_lord_activation";
import { cfg_lord_base } from "./vo/cfg_lord_base";
import { cfg_lord_camp } from "./vo/cfg_lord_camp";
import { cfg_lord_exchange } from "./vo/cfg_lord_exchange";
import { cfg_lord_skill } from "./vo/cfg_lord_skill";
import { cfg_lord_skill_enhance } from "./vo/cfg_lord_skill_enhance";
import { cfg_lord_star } from "./vo/cfg_lord_star";
import { cfg_lord_suit_compose } from "./vo/cfg_lord_suit_compose";
import { cfg_lord_suit_select } from "./vo/cfg_lord_suit_select";
import { cfg_lord_treasure } from "./vo/cfg_lord_treasure";
import { cfg_lord_treasure_entry } from "./vo/cfg_lord_treasure_entry";
import { cfg_lord_treasure_forge } from "./vo/cfg_lord_treasure_forge";
import { cfg_lord_treasure_level } from "./vo/cfg_lord_treasure_level";
import { cfg_lottery_day_limit } from "./vo/cfg_lottery_day_limit";
import { cfg_lottery_ext } from "./vo/cfg_lottery_ext";
import { cfg_lottery_nation } from "./vo/cfg_lottery_nation";
import { cfg_lottery_nation_times } from "./vo/cfg_lottery_nation_times";
import { cfg_lottery_score } from "./vo/cfg_lottery_score";
import { cfg_lottery_show } from "./vo/cfg_lottery_show";
import { cfg_lottery_times } from "./vo/cfg_lottery_times";
import { cfg_lottery_weight } from "./vo/cfg_lottery_weight";
import { cfg_main_battle } from "./vo/cfg_main_battle";
import { cfg_main_battle_box } from "./vo/cfg_main_battle_box";
import { cfg_main_battle_box_level } from "./vo/cfg_main_battle_box_level";
import { cfg_main_battle_box_reward } from "./vo/cfg_main_battle_box_reward";
import { cfg_main_battle_box_tequan } from "./vo/cfg_main_battle_box_tequan";
import { cfg_main_battle_fetch } from "./vo/cfg_main_battle_fetch";
import { cfg_main_battle_hanging } from "./vo/cfg_main_battle_hanging";
import { cfg_main_battle_mission } from "./vo/cfg_main_battle_mission";
import { cfg_map } from "./vo/cfg_map";
import { cfg_map_item } from "./vo/cfg_map_item";
import { cfg_map_item_type } from "./vo/cfg_map_item_type";
import { cfg_map_type } from "./vo/cfg_map_type";
import { cfg_master_card } from "./vo/cfg_master_card";
import { cfg_master_card_attr } from "./vo/cfg_master_card_attr";
import { cfg_master_card_color } from "./vo/cfg_master_card_color";
import { cfg_master_card_drum_level } from "./vo/cfg_master_card_drum_level";
import { cfg_master_card_first_pay } from "./vo/cfg_master_card_first_pay";
import { cfg_master_card_help_gift } from "./vo/cfg_master_card_help_gift";
import { cfg_master_card_misc } from "./vo/cfg_master_card_misc";
import { cfg_master_card_mission } from "./vo/cfg_master_card_mission";
import { cfg_master_card_official } from "./vo/cfg_master_card_official";
import { cfg_master_card_official_position } from "./vo/cfg_master_card_official_position";
import { cfg_master_card_reshape } from "./vo/cfg_master_card_reshape";
import { cfg_master_card_slot } from "./vo/cfg_master_card_slot";
import { cfg_master_card_stage } from "./vo/cfg_master_card_stage";
import { cfg_master_talent_science } from "./vo/cfg_master_talent_science";
import { cfg_master_talent_science_icon } from "./vo/cfg_master_talent_science_icon";
import { cfg_match_type } from "./vo/cfg_match_type";
import { cfg_match_type_team } from "./vo/cfg_match_type_team";
import { cfg_maze } from "./vo/cfg_maze";
import { cfg_maze_diff_rewards } from "./vo/cfg_maze_diff_rewards";
import { cfg_maze_mission_spoils } from "./vo/cfg_maze_mission_spoils";
import { cfg_maze_monster } from "./vo/cfg_maze_monster";
import { cfg_maze_reset } from "./vo/cfg_maze_reset";
import { cfg_maze_revive } from "./vo/cfg_maze_revive";
import { cfg_maze_shop } from "./vo/cfg_maze_shop";
import { cfg_maze_theme } from "./vo/cfg_maze_theme";
import { cfg_medal } from "./vo/cfg_medal";
import { cfg_microterminal_open } from "./vo/cfg_microterminal_open";
import { cfg_microterminal_sign } from "./vo/cfg_microterminal_sign";
import { cfg_misc_config } from "./vo/cfg_misc_config";
import { cfg_mission_shop } from "./vo/cfg_mission_shop";
import { cfg_mission_shop_client } from "./vo/cfg_mission_shop_client";
import { cfg_mock_battle_hero_base } from "./vo/cfg_mock_battle_hero_base";
import { cfg_mock_pvp_database } from "./vo/cfg_mock_pvp_database";
import { cfg_mock_pvp_hero_base } from "./vo/cfg_mock_pvp_hero_base";
import { cfg_mock_pvp_limit } from "./vo/cfg_mock_pvp_limit";
import { cfg_mock_pvp_misc } from "./vo/cfg_mock_pvp_misc";
import { cfg_mock_pvp_mission } from "./vo/cfg_mock_pvp_mission";
import { cfg_modular_activity_bless } from "./vo/cfg_modular_activity_bless";
import { cfg_modular_activity_brick } from "./vo/cfg_modular_activity_brick";
import { cfg_modular_activity_carnival_link } from "./vo/cfg_modular_activity_carnival_link";
import { cfg_modular_activity_client_setting } from "./vo/cfg_modular_activity_client_setting";
import { cfg_modular_activity_compose_list } from "./vo/cfg_modular_activity_compose_list";
import { cfg_modular_activity_customized_gift } from "./vo/cfg_modular_activity_customized_gift";
import { cfg_modular_activity_dice } from "./vo/cfg_modular_activity_dice";
import { cfg_modular_activity_dice_boss } from "./vo/cfg_modular_activity_dice_boss";
import { cfg_modular_activity_dice_client_diff } from "./vo/cfg_modular_activity_dice_client_diff";
import { cfg_modular_activity_dice_misc } from "./vo/cfg_modular_activity_dice_misc";
import { cfg_modular_activity_drop } from "./vo/cfg_modular_activity_drop";
import { cfg_modular_activity_drop_show } from "./vo/cfg_modular_activity_drop_show";
import { cfg_modular_activity_exchange } from "./vo/cfg_modular_activity_exchange";
import { cfg_modular_activity_festival_wish } from "./vo/cfg_modular_activity_festival_wish";
import { cfg_modular_activity_festival_wish_choose } from "./vo/cfg_modular_activity_festival_wish_choose";
import { cfg_modular_activity_festival_wish_cost } from "./vo/cfg_modular_activity_festival_wish_cost";
import { cfg_modular_activity_free_switch } from "./vo/cfg_modular_activity_free_switch";
import { cfg_modular_activity_general_pass_vip } from "./vo/cfg_modular_activity_general_pass_vip";
import { cfg_modular_activity_hero_challenge } from "./vo/cfg_modular_activity_hero_challenge";
import { cfg_modular_activity_holiday_welfare_reward } from "./vo/cfg_modular_activity_holiday_welfare_reward";
import { cfg_modular_activity_hunt_cost } from "./vo/cfg_modular_activity_hunt_cost";
import { cfg_modular_activity_hunt_desc } from "./vo/cfg_modular_activity_hunt_desc";
import { cfg_modular_activity_hunt_misc } from "./vo/cfg_modular_activity_hunt_misc";
import { cfg_modular_activity_huoqutujing } from "./vo/cfg_modular_activity_huoqutujing";
import { cfg_modular_activity_icon } from "./vo/cfg_modular_activity_icon";
import { cfg_modular_activity_login } from "./vo/cfg_modular_activity_login";
import { cfg_modular_activity_lottery_target } from "./vo/cfg_modular_activity_lottery_target";
import { cfg_modular_activity_lottery_times } from "./vo/cfg_modular_activity_lottery_times";
import { cfg_modular_activity_lucky_bag } from "./vo/cfg_modular_activity_lucky_bag";
import { cfg_modular_activity_mission } from "./vo/cfg_modular_activity_mission";
import { cfg_modular_activity_open } from "./vo/cfg_modular_activity_open";
import { cfg_modular_activity_open_preview } from "./vo/cfg_modular_activity_open_preview";
import { cfg_modular_activity_pay_welfare } from "./vo/cfg_modular_activity_pay_welfare";
import { cfg_modular_activity_payment_shop_item } from "./vo/cfg_modular_activity_payment_shop_item";
import { cfg_modular_activity_payment_shop_item_show } from "./vo/cfg_modular_activity_payment_shop_item_show";
import { cfg_modular_activity_preview } from "./vo/cfg_modular_activity_preview";
import { cfg_modular_activity_preview_rewards } from "./vo/cfg_modular_activity_preview_rewards";
import { cfg_modular_activity_rank } from "./vo/cfg_modular_activity_rank";
import { cfg_modular_activity_rank_reward } from "./vo/cfg_modular_activity_rank_reward";
import { cfg_modular_activity_round_mission } from "./vo/cfg_modular_activity_round_mission";
import { cfg_modular_activity_round_mission_reward } from "./vo/cfg_modular_activity_round_mission_reward";
import { cfg_modular_activity_shop_client } from "./vo/cfg_modular_activity_shop_client";
import { cfg_modular_activity_sign } from "./vo/cfg_modular_activity_sign";
import { cfg_modular_activity_six_bless } from "./vo/cfg_modular_activity_six_bless";
import { cfg_modular_activity_star_plan_hero } from "./vo/cfg_modular_activity_star_plan_hero";
import { cfg_modular_activity_star_plan_reward } from "./vo/cfg_modular_activity_star_plan_reward";
import { cfg_modular_activity_story } from "./vo/cfg_modular_activity_story";
import { cfg_modular_activity_story_chapter } from "./vo/cfg_modular_activity_story_chapter";
import { cfg_modular_activity_story_chapter_map } from "./vo/cfg_modular_activity_story_chapter_map";
import { cfg_modular_activity_story_dialogue } from "./vo/cfg_modular_activity_story_dialogue";
import { cfg_modular_activity_sub_type } from "./vo/cfg_modular_activity_sub_type";
import { cfg_modular_activity_target } from "./vo/cfg_modular_activity_target";
import { cfg_modular_activity_time_item } from "./vo/cfg_modular_activity_time_item";
import { cfg_modular_activity_wall } from "./vo/cfg_modular_activity_wall";
import { cfg_modular_activity_war_log_acc_reward } from "./vo/cfg_modular_activity_war_log_acc_reward";
import { cfg_modular_activity_war_log_mission } from "./vo/cfg_modular_activity_war_log_mission";
import { cfg_modular_activity_weekly_card_reward } from "./vo/cfg_modular_activity_weekly_card_reward";
import { cfg_money } from "./vo/cfg_money";
import { cfg_monster } from "./vo/cfg_monster";
import { cfg_monster_group } from "./vo/cfg_monster_group";
import { cfg_monster_skill_tier } from "./vo/cfg_monster_skill_tier";
import { cfg_monster_tips } from "./vo/cfg_monster_tips";
import { cfg_month_fund } from "./vo/cfg_month_fund";
import { cfg_month_fund_type } from "./vo/cfg_month_fund_type";
import { cfg_music } from "./vo/cfg_music";
import { cfg_nation_tower_lineup } from "./vo/cfg_nation_tower_lineup";
import { cfg_nation_tower_open } from "./vo/cfg_nation_tower_open";
import { cfg_noob_pay } from "./vo/cfg_noob_pay";
import { cfg_online_reward } from "./vo/cfg_online_reward";
import { cfg_pass_behead } from "./vo/cfg_pass_behead";
import { cfg_pass_behead_box } from "./vo/cfg_pass_behead_box";
import { cfg_pass_behead_guanqia } from "./vo/cfg_pass_behead_guanqia";
import { cfg_pass_behead_revive } from "./vo/cfg_pass_behead_revive";
import { cfg_pass_check_mission } from "./vo/cfg_pass_check_mission";
import { cfg_pass_check_reward } from "./vo/cfg_pass_check_reward";
import { cfg_pass_check_vip } from "./vo/cfg_pass_check_vip";
import { cfg_pay_vip } from "./vo/cfg_pay_vip";
import { cfg_pay_vip_privilege } from "./vo/cfg_pay_vip_privilege";
import { cfg_pay_vip_privilege_function } from "./vo/cfg_pay_vip_privilege_function";
import { cfg_payment_shop_item } from "./vo/cfg_payment_shop_item";
import { cfg_payment_shop_link } from "./vo/cfg_payment_shop_link";
import { cfg_payment_time_gift } from "./vo/cfg_payment_time_gift";
import { cfg_peak_misc } from "./vo/cfg_peak_misc";
import { cfg_peak_time } from "./vo/cfg_peak_time";
import { cfg_peerless_act_hero_gift } from "./vo/cfg_peerless_act_hero_gift";
import { cfg_platform_ad_id_misc } from "./vo/cfg_platform_ad_id_misc";
import { cfg_player_strategy } from "./vo/cfg_player_strategy";
import { cfg_playing_preview_reward } from "./vo/cfg_playing_preview_reward";
import { cfg_progress_gift } from "./vo/cfg_progress_gift";
import { cfg_pull_words } from "./vo/cfg_pull_words";
import { cfg_pvp_map } from "./vo/cfg_pvp_map";
import { cfg_qq_group } from "./vo/cfg_qq_group";
import { cfg_qq_vip } from "./vo/cfg_qq_vip";
import { cfg_qxzl_misc } from "./vo/cfg_qxzl_misc";
import { cfg_random_box } from "./vo/cfg_random_box";
import { cfg_random_pvp } from "./vo/cfg_random_pvp";
import { cfg_random_pvp_head_frame } from "./vo/cfg_random_pvp_head_frame";
import { cfg_random_pvp_limit } from "./vo/cfg_random_pvp_limit";
import { cfg_random_pvp_reward } from "./vo/cfg_random_pvp_reward";
import { cfg_random_pvp_task } from "./vo/cfg_random_pvp_task";
import { cfg_random_pvp_task_rewards } from "./vo/cfg_random_pvp_task_rewards";
import { cfg_rank_desc } from "./vo/cfg_rank_desc";
import { cfg_rank_mission } from "./vo/cfg_rank_mission";
import { cfg_rank_rewards } from "./vo/cfg_rank_rewards";
import { cfg_rank_worship } from "./vo/cfg_rank_worship";
import { cfg_red_cliff } from "./vo/cfg_red_cliff";
import { cfg_red_cliff_boss } from "./vo/cfg_red_cliff_boss";
import { cfg_red_cliff_open } from "./vo/cfg_red_cliff_open";
import { cfg_red_cliff_refresh } from "./vo/cfg_red_cliff_refresh";
import { cfg_red_cliff_review } from "./vo/cfg_red_cliff_review";
import { cfg_retrieval } from "./vo/cfg_retrieval";
import { cfg_river_text_const } from "./vo/cfg_river_text_const";
import { cfg_role_profile } from "./vo/cfg_role_profile";
import { cfg_san_xiao_actor } from "./vo/cfg_san_xiao_actor";
import { cfg_san_xiao_guanqia } from "./vo/cfg_san_xiao_guanqia";
import { cfg_san_xiao_item } from "./vo/cfg_san_xiao_item";
import { cfg_san_xiao_level } from "./vo/cfg_san_xiao_level";
import { cfg_san_xiao_map } from "./vo/cfg_san_xiao_map";
import { cfg_san_xiao_misc } from "./vo/cfg_san_xiao_misc";
import { cfg_scene } from "./vo/cfg_scene";
import { cfg_sdk_concern_reward } from "./vo/cfg_sdk_concern_reward";
import { cfg_sdk_platform_desc } from "./vo/cfg_sdk_platform_desc";
import { cfg_sdk_rewards } from "./vo/cfg_sdk_rewards";
import { cfg_select_box } from "./vo/cfg_select_box";
import { cfg_seven_goal } from "./vo/cfg_seven_goal";
import { cfg_seven_goal_gift } from "./vo/cfg_seven_goal_gift";
import { cfg_seven_goal_mission } from "./vo/cfg_seven_goal_mission";
import { cfg_share_cycle_reward } from "./vo/cfg_share_cycle_reward";
import { cfg_share_daily_reward } from "./vo/cfg_share_daily_reward";
import { cfg_share_level_reward } from "./vo/cfg_share_level_reward";
import { cfg_shili_preview } from "./vo/cfg_shili_preview";
import { cfg_shop } from "./vo/cfg_shop";
import { cfg_shop_item } from "./vo/cfg_shop_item";
import { cfg_shop_item_tips } from "./vo/cfg_shop_item_tips";
import { cfg_shop_reset_times } from "./vo/cfg_shop_reset_times";
import { cfg_shop_shortcut } from "./vo/cfg_shop_shortcut";
import { cfg_shop_tab_limit } from "./vo/cfg_shop_tab_limit";
import { cfg_show_off } from "./vo/cfg_show_off";
import { cfg_skeleton_adaptive } from "./vo/cfg_skeleton_adaptive";
import { cfg_skill } from "./vo/cfg_skill";
import { cfg_skill_effect } from "./vo/cfg_skill_effect";
import { cfg_skill_event } from "./vo/cfg_skill_event";
import { cfg_skill_level } from "./vo/cfg_skill_level";
import { cfg_skill_summon } from "./vo/cfg_skill_summon";
import { cfg_small_game } from "./vo/cfg_small_game";
import { cfg_soldier_game } from "./vo/cfg_soldier_game";
import { cfg_soldier_game_rewards } from "./vo/cfg_soldier_game_rewards";
import { cfg_soul_hero_link_level } from "./vo/cfg_soul_hero_link_level";
import { cfg_soul_hero_link_limit_unlock } from "./vo/cfg_soul_hero_link_limit_unlock";
import { cfg_soul_hero_link_nation } from "./vo/cfg_soul_hero_link_nation";
import { cfg_stage_breed } from "./vo/cfg_stage_breed";
import { cfg_stage_breed_attr } from "./vo/cfg_stage_breed_attr";
import { cfg_stage_copy } from "./vo/cfg_stage_copy";
import { cfg_stage_copy_boss } from "./vo/cfg_stage_copy_boss";
import { cfg_stage_copy_daily_mission } from "./vo/cfg_stage_copy_daily_mission";
import { cfg_stage_copy_misc } from "./vo/cfg_stage_copy_misc";
import { cfg_stage_copy_story } from "./vo/cfg_stage_copy_story";
import { cfg_stage_map } from "./vo/cfg_stage_map";
import { cfg_stage_mission } from "./vo/cfg_stage_mission";
import { cfg_stage_skill_attr } from "./vo/cfg_stage_skill_attr";
import { cfg_stage_skill_type } from "./vo/cfg_stage_skill_type";
import { cfg_star_plan_gift } from "./vo/cfg_star_plan_gift";
import { cfg_star_plan_hero } from "./vo/cfg_star_plan_hero";
import { cfg_star_plan_reward } from "./vo/cfg_star_plan_reward";
import { cfg_story } from "./vo/cfg_story";
import { cfg_story_action } from "./vo/cfg_story_action";
import { cfg_story_actor } from "./vo/cfg_story_actor";
import { cfg_story_bubble } from "./vo/cfg_story_bubble";
import { cfg_story_maze } from "./vo/cfg_story_maze";
import { cfg_story_maze_mission_spoils } from "./vo/cfg_story_maze_mission_spoils";
import { cfg_story_maze_monster } from "./vo/cfg_story_maze_monster";
import { cfg_story_maze_reset } from "./vo/cfg_story_maze_reset";
import { cfg_story_maze_revive } from "./vo/cfg_story_maze_revive";
import { cfg_story_maze_rewards } from "./vo/cfg_story_maze_rewards";
import { cfg_story_maze_shop } from "./vo/cfg_story_maze_shop";
import { cfg_story_maze_theme } from "./vo/cfg_story_maze_theme";
import { cfg_story_siegelord_city_type } from "./vo/cfg_story_siegelord_city_type";
import { cfg_story_siegelord_level } from "./vo/cfg_story_siegelord_level";
import { cfg_story_siegelord_level_reward } from "./vo/cfg_story_siegelord_level_reward";
import { cfg_story_siegelord_misc } from "./vo/cfg_story_siegelord_misc";
import { cfg_story_siegelord_pass_reward } from "./vo/cfg_story_siegelord_pass_reward";
import { cfg_story_tower_battle } from "./vo/cfg_story_tower_battle";
import { cfg_story_tower_battle_monster } from "./vo/cfg_story_tower_battle_monster";
import { cfg_story_tower_battle_reward } from "./vo/cfg_story_tower_battle_reward";
import { cfg_suit_attr } from "./vo/cfg_suit_attr";
import { cfg_supreme_lottery } from "./vo/cfg_supreme_lottery";
import { cfg_svip_pay_gift } from "./vo/cfg_svip_pay_gift";
import { cfg_sweapon_stage } from "./vo/cfg_sweapon_stage";
import { cfg_sys_open_notice } from "./vo/cfg_sys_open_notice";
import { cfg_sys_openlv } from "./vo/cfg_sys_openlv";
import { cfg_sys_use_times } from "./vo/cfg_sys_use_times";
import { cfg_tax } from "./vo/cfg_tax";
import { cfg_tax_reward } from "./vo/cfg_tax_reward";
import { cfg_td_main } from "./vo/cfg_td_main";
import { cfg_td_main_mission } from "./vo/cfg_td_main_mission";
import { cfg_td_main_monster } from "./vo/cfg_td_main_monster";
import { cfg_td_main_pass_mission } from "./vo/cfg_td_main_pass_mission";
import { cfg_td_map } from "./vo/cfg_td_map";
import { cfg_td_monster_talk } from "./vo/cfg_td_monster_talk";
import { cfg_td_trial } from "./vo/cfg_td_trial";
import { cfg_td_trial_map } from "./vo/cfg_td_trial_map";
import { cfg_td_trial_monster } from "./vo/cfg_td_trial_monster";
import { cfg_team_boss } from "./vo/cfg_team_boss";
import { cfg_team_xswh_boss } from "./vo/cfg_team_xswh_boss";
import { cfg_team_xswh_gift } from "./vo/cfg_team_xswh_gift";
import { cfg_team_xswh_hurt_rewards } from "./vo/cfg_team_xswh_hurt_rewards";
import { cfg_team_xswh_rank_rewards } from "./vo/cfg_team_xswh_rank_rewards";
import { cfg_tequan } from "./vo/cfg_tequan";
import { cfg_test_tower } from "./vo/cfg_test_tower";
import { cfg_test_tower_extra_reward } from "./vo/cfg_test_tower_extra_reward";
import { cfg_test_tower_skin } from "./vo/cfg_test_tower_skin";
import { cfg_theme_act_famous_lottery_reward } from "./vo/cfg_theme_act_famous_lottery_reward";
import { cfg_theme_act_hero_lottery_show } from "./vo/cfg_theme_act_hero_lottery_show";
import { cfg_theme_act_item } from "./vo/cfg_theme_act_item";
import { cfg_theme_act_rare_lottery_reward } from "./vo/cfg_theme_act_rare_lottery_reward";
import { cfg_theme_act_skin_lottery } from "./vo/cfg_theme_act_skin_lottery";
import { cfg_theme_act_skin_lottery_cost } from "./vo/cfg_theme_act_skin_lottery_cost";
import { cfg_theme_act_wish_lottery } from "./vo/cfg_theme_act_wish_lottery";
import { cfg_theme_act_wish_lottery_item } from "./vo/cfg_theme_act_wish_lottery_item";
import { cfg_theme_act_wish_lottery_show } from "./vo/cfg_theme_act_wish_lottery_show";
import { cfg_tiled_effect } from "./vo/cfg_tiled_effect";
import { cfg_tiled_map } from "./vo/cfg_tiled_map";
import { cfg_time_achievement } from "./vo/cfg_time_achievement";
import { cfg_time_activity_drop } from "./vo/cfg_time_activity_drop";
import { cfg_time_activity_shop } from "./vo/cfg_time_activity_shop";
import { cfg_time_activity_week } from "./vo/cfg_time_activity_week";
import { cfg_tips } from "./vo/cfg_tips";
import { cfg_title } from "./vo/cfg_title";
import { cfg_travel } from "./vo/cfg_travel";
import { cfg_travel_ext } from "./vo/cfg_travel_ext";
import { cfg_treasure_box } from "./vo/cfg_treasure_box";
import { cfg_treasure_box_type } from "./vo/cfg_treasure_box_type";
import { cfg_treasure_energy } from "./vo/cfg_treasure_energy";
import { cfg_treasure_gift } from "./vo/cfg_treasure_gift";
import { cfg_treasure_misc } from "./vo/cfg_treasure_misc";
import { cfg_treasure_refresh_cost } from "./vo/cfg_treasure_refresh_cost";
import { cfg_treasure_worker } from "./vo/cfg_treasure_worker";
import { cfg_trig_skill } from "./vo/cfg_trig_skill";
import { cfg_ui_button_style } from "./vo/cfg_ui_button_style";
import { cfg_ui_preload } from "./vo/cfg_ui_preload";
import { cfg_ui_resident } from "./vo/cfg_ui_resident";
import { cfg_up_star_gift } from "./vo/cfg_up_star_gift";
import { cfg_up_star_reward } from "./vo/cfg_up_star_reward";
import { cfg_vip_daily_mission } from "./vo/cfg_vip_daily_mission";
import { cfg_vip_daily_mission_gift } from "./vo/cfg_vip_daily_mission_gift";
import { cfg_vip_kefu } from "./vo/cfg_vip_kefu";
import { cfg_vip_kefu_review } from "./vo/cfg_vip_kefu_review";
import { cfg_war_flag } from "./vo/cfg_war_flag";
import { cfg_war_flag_facade } from "./vo/cfg_war_flag_facade";
import { cfg_war_flag_level } from "./vo/cfg_war_flag_level";
import { cfg_war_flag_link } from "./vo/cfg_war_flag_link";
import { cfg_war_flag_recycle } from "./vo/cfg_war_flag_recycle";
import { cfg_war_flag_stage } from "./vo/cfg_war_flag_stage";
import { cfg_war_log_mission } from "./vo/cfg_war_log_mission";
import { cfg_war_log_mission_pay_reward } from "./vo/cfg_war_log_mission_pay_reward";
import { cfg_war_log_mission_score_reward } from "./vo/cfg_war_log_mission_score_reward";
import { cfg_wars_honor } from "./vo/cfg_wars_honor";
import { cfg_wars_map_camp } from "./vo/cfg_wars_map_camp";
import { cfg_wars_map_city } from "./vo/cfg_wars_map_city";
import { cfg_wars_map_near_city2 } from "./vo/cfg_wars_map_near_city2";
import { cfg_wars_map_type } from "./vo/cfg_wars_map_type";
import { cfg_wars_misc } from "./vo/cfg_wars_misc";
import { cfg_wars_mission } from "./vo/cfg_wars_mission";
import { cfg_wars_state } from "./vo/cfg_wars_state";
import { cfg_wars_text_const } from "./vo/cfg_wars_text_const";
import { cfg_week_target } from "./vo/cfg_week_target";
import { cfg_week_target_level } from "./vo/cfg_week_target_level";
import { cfg_wing_buff } from "./vo/cfg_wing_buff";
import { cfg_wing_hero } from "./vo/cfg_wing_hero";
import { cfg_wing_hero_skin } from "./vo/cfg_wing_hero_skin";
import { cfg_wing_level } from "./vo/cfg_wing_level";
import { cfg_world_boss_hurt_rewards } from "./vo/cfg_world_boss_hurt_rewards";
import { cfg_world_boss_level } from "./vo/cfg_world_boss_level";
import { cfg_world_map } from "./vo/cfg_world_map";
import { cfg_world_map_2 } from "./vo/cfg_world_map_2";
import { cfg_wxShare } from "./vo/cfg_wxShare";
import { cfg_wxTurn_gift } from "./vo/cfg_wxTurn_gift";
import { cfg_wx_game_club } from "./vo/cfg_wx_game_club";
import { cfg_xswh_boss } from "./vo/cfg_xswh_boss";
import { cfg_xswh_gift } from "./vo/cfg_xswh_gift";
import { cfg_xswh_hurt_rewards } from "./vo/cfg_xswh_hurt_rewards";
import { cfg_xswh_rank_rewards } from "./vo/cfg_xswh_rank_rewards";
import { cfg_ybzk_reward } from "./vo/cfg_ybzk_reward";
import { cfg_zero_buy } from "./vo/cfg_zero_buy";
import { errorCode } from "./vo/errorCode";
import { fightAttr } from "./vo/fightAttr";
import { victoryMacro } from "./vo/victoryMacro";


export class CfgCacheMgr extends EventDispatcher {
    private static _ins: CfgCacheMgr;
    static get ins(): CfgCacheMgr {
        if(!CfgCacheMgr._ins) {
            CfgCacheMgr._ins = new CfgCacheMgr;
        }
        return CfgCacheMgr._ins;
    }

    cfg_acc_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_acc_gift, "cfg_acc_gift");
    cfg_achievement_cache: CfgCacheBase = new CfgCacheBase(cfg_achievement, "cfg_achievement");
    cfg_activity_client_cache: CfgCacheBase = new CfgCacheBase(cfg_activity_client, "cfg_activity_client");
    cfg_activity_desc_cache: CfgCacheBase = new CfgCacheBase(cfg_activity_desc, "cfg_activity_desc");
    cfg_activity_icon_cache: CfgCacheBase = new CfgCacheBase(cfg_activity_icon, "cfg_activity_icon");
    cfg_activity_limit_sign_cache: CfgCacheBase = new CfgCacheBase(cfg_activity_limit_sign, "cfg_activity_limit_sign");
    cfg_activity_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_activity_misc, "cfg_activity_misc");
    cfg_activity_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_activity_mission, "cfg_activity_mission");
    cfg_activity_notice_cache: CfgCacheBase = new CfgCacheBase(cfg_activity_notice, "cfg_activity_notice");
    cfg_activity_page_cache: CfgCacheBase = new CfgCacheBase(cfg_activity_page, "cfg_activity_page");
    cfg_activity_yueka_cache: CfgCacheBase = new CfgCacheBase(cfg_activity_yueka, "cfg_activity_yueka");
    cfg_ad_show_cache: CfgCacheBase = new CfgCacheBase(cfg_ad_show, "cfg_ad_show");
    cfg_agent_review_cache: CfgCacheBase = new CfgCacheBase(cfg_agent_review, "cfg_agent_review");
    cfg_all_pinyin_dict_cache: CfgCacheBase = new CfgCacheBase(cfg_all_pinyin_dict, "cfg_all_pinyin_dict");
    cfg_arena_cache: CfgCacheBase = new CfgCacheBase(cfg_arena, "cfg_arena");
    cfg_arena_match_cache: CfgCacheBase = new CfgCacheBase(cfg_arena_match, "cfg_arena_match");
    cfg_arena_match_guess_cache: CfgCacheBase = new CfgCacheBase(cfg_arena_match_guess, "cfg_arena_match_guess");
    cfg_arena_max_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_arena_max_reward, "cfg_arena_max_reward");
    cfg_arena_rank_cache: CfgCacheBase = new CfgCacheBase(cfg_arena_rank, "cfg_arena_rank");
    cfg_arena_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_arena_reward, "cfg_arena_reward");
    cfg_arena_skip_limit_cache: CfgCacheBase = new CfgCacheBase(cfg_arena_skip_limit, "cfg_arena_skip_limit");
    cfg_arena_weekly_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_arena_weekly_reward, "cfg_arena_weekly_reward");
    cfg_ares_palace_cache: CfgCacheBase = new CfgCacheBase(cfg_ares_palace, "cfg_ares_palace");
    cfg_authorized_gifts_cache: CfgCacheBase = new CfgCacheBase(cfg_authorized_gifts, "cfg_authorized_gifts");
    cfg_bag_page_cache: CfgCacheBase = new CfgCacheBase(cfg_bag_page, "cfg_bag_page");
    cfg_bai_jiang_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_bai_jiang_gift, "cfg_bai_jiang_gift");
    cfg_battle_fly_name_cache: CfgCacheBase = new CfgCacheBase(cfg_battle_fly_name, "cfg_battle_fly_name");
    cfg_battle_trial_buff_cache: CfgCacheBase = new CfgCacheBase(cfg_battle_trial_buff, "cfg_battle_trial_buff");
    cfg_battle_trial_buff_reset_cache: CfgCacheBase = new CfgCacheBase(cfg_battle_trial_buff_reset, "cfg_battle_trial_buff_reset");
    cfg_battle_trial_guaji_monster_cache: CfgCacheBase = new CfgCacheBase(cfg_battle_trial_guaji_monster, "cfg_battle_trial_guaji_monster");
    cfg_battle_trial_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_battle_trial_misc, "cfg_battle_trial_misc");
    cfg_battle_trial_pass_guanqia_cache: CfgCacheBase = new CfgCacheBase(cfg_battle_trial_pass_guanqia, "cfg_battle_trial_pass_guanqia");
    cfg_battle_trial_pass_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_battle_trial_pass_reward, "cfg_battle_trial_pass_reward");
    cfg_beast_platform_cache: CfgCacheBase = new CfgCacheBase(cfg_beast_platform, "cfg_beast_platform");
    cfg_bingfa_cache: CfgCacheBase = new CfgCacheBase(cfg_bingfa, "cfg_bingfa");
    cfg_bingfa_ext_cache: CfgCacheBase = new CfgCacheBase(cfg_bingfa_ext, "cfg_bingfa_ext");
    cfg_bingfu_cache: CfgCacheBase = new CfgCacheBase(cfg_bingfu, "cfg_bingfu");
    cfg_bingfu_discompose_recast_cache: CfgCacheBase = new CfgCacheBase(cfg_bingfu_discompose_recast, "cfg_bingfu_discompose_recast");
    cfg_bingfu_recast_lock_cache: CfgCacheBase = new CfgCacheBase(cfg_bingfu_recast_lock, "cfg_bingfu_recast_lock");
    cfg_bingfu_refine_cache: CfgCacheBase = new CfgCacheBase(cfg_bingfu_refine, "cfg_bingfu_refine");
    cfg_bingfu_upgrade_cache: CfgCacheBase = new CfgCacheBase(cfg_bingfu_upgrade, "cfg_bingfu_upgrade");
    cfg_boat_peak_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_boat_peak_misc, "cfg_boat_peak_misc");
    cfg_boat_peak_rank_cache: CfgCacheBase = new CfgCacheBase(cfg_boat_peak_rank, "cfg_boat_peak_rank");
    cfg_boat_peak_time_cache: CfgCacheBase = new CfgCacheBase(cfg_boat_peak_time, "cfg_boat_peak_time");
    cfg_boat_racc_rank_gold_cache: CfgCacheBase = new CfgCacheBase(cfg_boat_racc_rank_gold, "cfg_boat_racc_rank_gold");
    cfg_boat_race_auction_reward_type_cache: CfgCacheBase = new CfgCacheBase(cfg_boat_race_auction_reward_type, "cfg_boat_race_auction_reward_type");
    cfg_boat_race_item_cache: CfgCacheBase = new CfgCacheBase(cfg_boat_race_item, "cfg_boat_race_item");
    cfg_boat_race_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_boat_race_misc, "cfg_boat_race_misc");
    cfg_buff_cache: CfgCacheBase = new CfgCacheBase(cfg_buff, "cfg_buff");
    cfg_buff_type_cache: CfgCacheBase = new CfgCacheBase(cfg_buff_type, "cfg_buff_type");
    cfg_building_cache: CfgCacheBase = new CfgCacheBase(cfg_building, "cfg_building");
    cfg_building_lv_cache: CfgCacheBase = new CfgCacheBase(cfg_building_lv, "cfg_building_lv");
    cfg_building_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_building_mission, "cfg_building_mission");
    cfg_building_plot_dialogue_cache: CfgCacheBase = new CfgCacheBase(cfg_building_plot_dialogue, "cfg_building_plot_dialogue");
    cfg_buy_times_cache: CfgCacheBase = new CfgCacheBase(cfg_buy_times, "cfg_buy_times");
    cfg_buy_times_type_cache: CfgCacheBase = new CfgCacheBase(cfg_buy_times_type, "cfg_buy_times_type");
    cfg_cast_soul_active_cache: CfgCacheBase = new CfgCacheBase(cfg_cast_soul_active, "cfg_cast_soul_active");
    cfg_casting_soul_cache: CfgCacheBase = new CfgCacheBase(cfg_casting_soul, "cfg_casting_soul");
    cfg_casting_soul_buff_cache: CfgCacheBase = new CfgCacheBase(cfg_casting_soul_buff, "cfg_casting_soul_buff");
    cfg_chapter_dialog_cache: CfgCacheBase = new CfgCacheBase(cfg_chapter_dialog, "cfg_chapter_dialog");
    cfg_chapter_script_cache: CfgCacheBase = new CfgCacheBase(cfg_chapter_script, "cfg_chapter_script");
    cfg_chat_bullet_msg_cache: CfgCacheBase = new CfgCacheBase(cfg_chat_bullet_msg, "cfg_chat_bullet_msg");
    cfg_chat_channel_cache: CfgCacheBase = new CfgCacheBase(cfg_chat_channel, "cfg_chat_channel");
    cfg_chat_skin_cache: CfgCacheBase = new CfgCacheBase(cfg_chat_skin, "cfg_chat_skin");
    cfg_chat_skin_widget_cache: CfgCacheBase = new CfgCacheBase(cfg_chat_skin_widget, "cfg_chat_skin_widget");
    cfg_client_lang_cache: CfgCacheBase = new CfgCacheBase(cfg_client_lang, "cfg_client_lang");
    cfg_client_w3_effect_cache: CfgCacheBase = new CfgCacheBase(cfg_client_w3_effect, "cfg_client_w3_effect");
    cfg_client_w3_effect_desc_cache: CfgCacheBase = new CfgCacheBase(cfg_client_w3_effect_desc, "cfg_client_w3_effect_desc");
    cfg_client_w3_skill_cache: CfgCacheBase = new CfgCacheBase(cfg_client_w3_skill, "cfg_client_w3_skill");
    cfg_client_w3_skill_desc_cache: CfgCacheBase = new CfgCacheBase(cfg_client_w3_skill_desc, "cfg_client_w3_skill_desc");
    cfg_client_w3_skin_cache: CfgCacheBase = new CfgCacheBase(cfg_client_w3_skin, "cfg_client_w3_skin");
    cfg_cmd_cache: CfgCacheBase = new CfgCacheBase(cfg_cmd, "cfg_cmd");
    cfg_code_cli_cache: CfgCacheBase = new CfgCacheBase(cfg_code_cli, "cfg_code_cli");
    cfg_country_war_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_country_war_misc, "cfg_country_war_misc");
    cfg_cross_team_match_type_cache: CfgCacheBase = new CfgCacheBase(cfg_cross_team_match_type, "cfg_cross_team_match_type");
    cfg_cross_team_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_cross_team_misc, "cfg_cross_team_misc");
    cfg_cross_test_tower_open_cache: CfgCacheBase = new CfgCacheBase(cfg_cross_test_tower_open, "cfg_cross_test_tower_open");
    cfg_crush_fight_condition_cache: CfgCacheBase = new CfgCacheBase(cfg_crush_fight_condition, "cfg_crush_fight_condition");
    cfg_crush_fight_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_crush_fight_mission, "cfg_crush_fight_mission");
    cfg_crush_fight_mission_type_cache: CfgCacheBase = new CfgCacheBase(cfg_crush_fight_mission_type, "cfg_crush_fight_mission_type");
    cfg_csc_fmsolo_buff_cache: CfgCacheBase = new CfgCacheBase(cfg_csc_fmsolo_buff, "cfg_csc_fmsolo_buff");
    cfg_csc_fmsolo_challenge_cache: CfgCacheBase = new CfgCacheBase(cfg_csc_fmsolo_challenge, "cfg_csc_fmsolo_challenge");
    cfg_csc_fmsolo_etc_cache: CfgCacheBase = new CfgCacheBase(cfg_csc_fmsolo_etc, "cfg_csc_fmsolo_etc");
    cfg_csc_fmsolo_log_cache: CfgCacheBase = new CfgCacheBase(cfg_csc_fmsolo_log, "cfg_csc_fmsolo_log");
    cfg_csc_fmsolo_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_csc_fmsolo_reward, "cfg_csc_fmsolo_reward");
    cfg_csc_fmsolo_shop_lv_cache: CfgCacheBase = new CfgCacheBase(cfg_csc_fmsolo_shop_lv, "cfg_csc_fmsolo_shop_lv");
    cfg_csclan_cache: CfgCacheBase = new CfgCacheBase(cfg_csclan, "cfg_csclan");
    cfg_csclan_etc_cache: CfgCacheBase = new CfgCacheBase(cfg_csclan_etc, "cfg_csclan_etc");
    cfg_csclan_solo_buff_cache: CfgCacheBase = new CfgCacheBase(cfg_csclan_solo_buff, "cfg_csclan_solo_buff");
    cfg_csclan_solo_challenge_cache: CfgCacheBase = new CfgCacheBase(cfg_csclan_solo_challenge, "cfg_csclan_solo_challenge");
    cfg_csclan_solo_etc_cache: CfgCacheBase = new CfgCacheBase(cfg_csclan_solo_etc, "cfg_csclan_solo_etc");
    cfg_csclan_solo_log_cache: CfgCacheBase = new CfgCacheBase(cfg_csclan_solo_log, "cfg_csclan_solo_log");
    cfg_csclan_solo_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_csclan_solo_reward, "cfg_csclan_solo_reward");
    cfg_csclan_solo_shop_lv_cache: CfgCacheBase = new CfgCacheBase(cfg_csclan_solo_shop_lv, "cfg_csclan_solo_shop_lv");
    cfg_daily_copy_cache: CfgCacheBase = new CfgCacheBase(cfg_daily_copy, "cfg_daily_copy");
    cfg_daily_copy_discount_cache: CfgCacheBase = new CfgCacheBase(cfg_daily_copy_discount, "cfg_daily_copy_discount");
    cfg_daily_copy_monster_cache: CfgCacheBase = new CfgCacheBase(cfg_daily_copy_monster, "cfg_daily_copy_monster");
    cfg_daily_copy_type_cache: CfgCacheBase = new CfgCacheBase(cfg_daily_copy_type, "cfg_daily_copy_type");
    cfg_daily_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_daily_mission, "cfg_daily_mission");
    cfg_daily_mission_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_daily_mission_gift, "cfg_daily_mission_gift");
    cfg_daily_new_discount_rebate_cache: CfgCacheBase = new CfgCacheBase(cfg_daily_new_discount_rebate, "cfg_daily_new_discount_rebate");
    cfg_daily_pay_cache: CfgCacheBase = new CfgCacheBase(cfg_daily_pay, "cfg_daily_pay");
    cfg_dawanka_cache: CfgCacheBase = new CfgCacheBase(cfg_dawanka, "cfg_dawanka");
    cfg_dawanka_tequan_cache: CfgCacheBase = new CfgCacheBase(cfg_dawanka_tequan, "cfg_dawanka_tequan");
    cfg_day_acc_pay_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_day_acc_pay_gift, "cfg_day_acc_pay_gift");
    cfg_deputy_cache: CfgCacheBase = new CfgCacheBase(cfg_deputy, "cfg_deputy");
    cfg_deputy_level_cache: CfgCacheBase = new CfgCacheBase(cfg_deputy_level, "cfg_deputy_level");
    cfg_deputy_star_cache: CfgCacheBase = new CfgCacheBase(cfg_deputy_star, "cfg_deputy_star");
    cfg_device_excursion_cache: CfgCacheBase = new CfgCacheBase(cfg_device_excursion, "cfg_device_excursion");
    cfg_divine_cache: CfgCacheBase = new CfgCacheBase(cfg_divine, "cfg_divine");
    cfg_divine_activate_cache: CfgCacheBase = new CfgCacheBase(cfg_divine_activate, "cfg_divine_activate");
    cfg_divine_copy_cache: CfgCacheBase = new CfgCacheBase(cfg_divine_copy, "cfg_divine_copy");
    cfg_divine_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_divine_misc, "cfg_divine_misc");
    cfg_divine_strengthen_cache: CfgCacheBase = new CfgCacheBase(cfg_divine_strengthen, "cfg_divine_strengthen");
    cfg_divine_wear_cache: CfgCacheBase = new CfgCacheBase(cfg_divine_wear, "cfg_divine_wear");
    cfg_dominate_pvp_cache: CfgCacheBase = new CfgCacheBase(cfg_dominate_pvp, "cfg_dominate_pvp");
    cfg_dominate_pvp_limit_cache: CfgCacheBase = new CfgCacheBase(cfg_dominate_pvp_limit, "cfg_dominate_pvp_limit");
    cfg_dominate_pvp_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_dominate_pvp_reward, "cfg_dominate_pvp_reward");
    cfg_dominate_pvp_task_cache: CfgCacheBase = new CfgCacheBase(cfg_dominate_pvp_task, "cfg_dominate_pvp_task");
    cfg_dominate_pvp_task_rewards_cache: CfgCacheBase = new CfgCacheBase(cfg_dominate_pvp_task_rewards, "cfg_dominate_pvp_task_rewards");
    cfg_drop_group_cache: CfgCacheBase = new CfgCacheBase(cfg_drop_group, "cfg_drop_group");
    cfg_eight_login_cache: CfgCacheBase = new CfgCacheBase(cfg_eight_login, "cfg_eight_login");
    cfg_epic_battle_tips_cache: CfgCacheBase = new CfgCacheBase(cfg_epic_battle_tips, "cfg_epic_battle_tips");
    cfg_equip_cache: CfgCacheBase = new CfgCacheBase(cfg_equip, "cfg_equip");
    cfg_equip_compose_cache: CfgCacheBase = new CfgCacheBase(cfg_equip_compose, "cfg_equip_compose");
    cfg_equip_suit_cache: CfgCacheBase = new CfgCacheBase(cfg_equip_suit, "cfg_equip_suit");
    cfg_equip_suit_ext_cache: CfgCacheBase = new CfgCacheBase(cfg_equip_suit_ext, "cfg_equip_suit_ext");
    cfg_fail_tips_cache: CfgCacheBase = new CfgCacheBase(cfg_fail_tips, "cfg_fail_tips");
    cfg_family_cache: CfgCacheBase = new CfgCacheBase(cfg_family, "cfg_family");
    cfg_family_active_attr_cache: CfgCacheBase = new CfgCacheBase(cfg_family_active_attr, "cfg_family_active_attr");
    cfg_family_active_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_family_active_mission, "cfg_family_active_mission");
    cfg_family_boss_cache: CfgCacheBase = new CfgCacheBase(cfg_family_boss, "cfg_family_boss");
    cfg_family_boss_attr_cache: CfgCacheBase = new CfgCacheBase(cfg_family_boss_attr, "cfg_family_boss_attr");
    cfg_family_boss_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_family_boss_misc, "cfg_family_boss_misc");
    cfg_family_boss_rank_cache: CfgCacheBase = new CfgCacheBase(cfg_family_boss_rank, "cfg_family_boss_rank");
    cfg_family_etc_cache: CfgCacheBase = new CfgCacheBase(cfg_family_etc, "cfg_family_etc");
    cfg_family_hongbao_cache: CfgCacheBase = new CfgCacheBase(cfg_family_hongbao, "cfg_family_hongbao");
    cfg_family_hongbao_blessing_cache: CfgCacheBase = new CfgCacheBase(cfg_family_hongbao_blessing, "cfg_family_hongbao_blessing");
    cfg_family_hongbao_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_family_hongbao_reward, "cfg_family_hongbao_reward");
    cfg_family_log_cache: CfgCacheBase = new CfgCacheBase(cfg_family_log, "cfg_family_log");
    cfg_family_random_name_cache: CfgCacheBase = new CfgCacheBase(cfg_family_random_name, "cfg_family_random_name");
    cfg_family_science_cache: CfgCacheBase = new CfgCacheBase(cfg_family_science, "cfg_family_science");
    cfg_family_science_times_cache: CfgCacheBase = new CfgCacheBase(cfg_family_science_times, "cfg_family_science_times");
    cfg_family_sign_cache: CfgCacheBase = new CfgCacheBase(cfg_family_sign, "cfg_family_sign");
    cfg_family_sign_active_cache: CfgCacheBase = new CfgCacheBase(cfg_family_sign_active, "cfg_family_sign_active");
    cfg_fight_show_cache: CfgCacheBase = new CfgCacheBase(cfg_fight_show, "cfg_fight_show");
    cfg_first_pay_cache: CfgCacheBase = new CfgCacheBase(cfg_first_pay, "cfg_first_pay");
    cfg_fish_cache: CfgCacheBase = new CfgCacheBase(cfg_fish, "cfg_fish");
    cfg_fish_attr_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_attr, "cfg_fish_attr");
    cfg_fish_color_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_color, "cfg_fish_color");
    cfg_fish_drum_level_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_drum_level, "cfg_fish_drum_level");
    cfg_fish_fishbowl_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_fishbowl, "cfg_fish_fishbowl");
    cfg_fish_handbook_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_handbook, "cfg_fish_handbook");
    cfg_fish_help_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_help_gift, "cfg_fish_help_gift");
    cfg_fish_level_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_level, "cfg_fish_level");
    cfg_fish_map_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_map, "cfg_fish_map");
    cfg_fish_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_misc, "cfg_fish_misc");
    cfg_fish_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_mission, "cfg_fish_mission");
    cfg_fish_official_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_official, "cfg_fish_official");
    cfg_fish_official_position_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_official_position, "cfg_fish_official_position");
    cfg_fish_reshape_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_reshape, "cfg_fish_reshape");
    cfg_fish_resources_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_resources, "cfg_fish_resources");
    cfg_fish_rod_level_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_rod_level, "cfg_fish_rod_level");
    cfg_fish_slot_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_slot, "cfg_fish_slot");
    cfg_fish_stage_cache: CfgCacheBase = new CfgCacheBase(cfg_fish_stage, "cfg_fish_stage");
    cfg_fly_font_type_cache: CfgCacheBase = new CfgCacheBase(cfg_fly_font_type, "cfg_fly_font_type");
    cfg_fuli_sign_cache: CfgCacheBase = new CfgCacheBase(cfg_fuli_sign, "cfg_fuli_sign");
    cfg_fuli_sign_acc_cache: CfgCacheBase = new CfgCacheBase(cfg_fuli_sign_acc, "cfg_fuli_sign_acc");
    cfg_fuli_sign_day_cache: CfgCacheBase = new CfgCacheBase(cfg_fuli_sign_day, "cfg_fuli_sign_day");
    cfg_fuli_token_cache: CfgCacheBase = new CfgCacheBase(cfg_fuli_token, "cfg_fuli_token");
    cfg_fuli_token_type_cache: CfgCacheBase = new CfgCacheBase(cfg_fuli_token_type, "cfg_fuli_token_type");
    cfg_fuli_yueka_cache: CfgCacheBase = new CfgCacheBase(cfg_fuli_yueka, "cfg_fuli_yueka");
    cfg_game_desc_cache: CfgCacheBase = new CfgCacheBase(cfg_game_desc, "cfg_game_desc");
    cfg_game_desc_2_cache: CfgCacheBase = new CfgCacheBase(cfg_game_desc_2, "cfg_game_desc_2");
    cfg_game_desc_3_cache: CfgCacheBase = new CfgCacheBase(cfg_game_desc_3, "cfg_game_desc_3");
    cfg_general_pass_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_general_pass_mission, "cfg_general_pass_mission");
    cfg_general_pass_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_general_pass_reward, "cfg_general_pass_reward");
    cfg_general_pass_type_cache: CfgCacheBase = new CfgCacheBase(cfg_general_pass_type, "cfg_general_pass_type");
    cfg_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_gift, "cfg_gift");
    cfg_god_equip_cache: CfgCacheBase = new CfgCacheBase(cfg_god_equip, "cfg_god_equip");
    cfg_god_equip_compose_cache: CfgCacheBase = new CfgCacheBase(cfg_god_equip_compose, "cfg_god_equip_compose");
    cfg_god_equip_convert_cache: CfgCacheBase = new CfgCacheBase(cfg_god_equip_convert, "cfg_god_equip_convert");
    cfg_god_equip_enchant_cache: CfgCacheBase = new CfgCacheBase(cfg_god_equip_enchant, "cfg_god_equip_enchant");
    cfg_god_equip_enchant_cost_cache: CfgCacheBase = new CfgCacheBase(cfg_god_equip_enchant_cost, "cfg_god_equip_enchant_cost");
    cfg_god_equip_suit_cache: CfgCacheBase = new CfgCacheBase(cfg_god_equip_suit, "cfg_god_equip_suit");
    cfg_god_equip_type_cache: CfgCacheBase = new CfgCacheBase(cfg_god_equip_type, "cfg_god_equip_type");
    cfg_god_trial_cache: CfgCacheBase = new CfgCacheBase(cfg_god_trial, "cfg_god_trial");
    cfg_god_trial_buff_cache: CfgCacheBase = new CfgCacheBase(cfg_god_trial_buff, "cfg_god_trial_buff");
    cfg_god_weapon_cache: CfgCacheBase = new CfgCacheBase(cfg_god_weapon, "cfg_god_weapon");
    cfg_god_weapon_level_cache: CfgCacheBase = new CfgCacheBase(cfg_god_weapon_level, "cfg_god_weapon_level");
    cfg_god_weapon_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_god_weapon_mission, "cfg_god_weapon_mission");
    cfg_god_weapon_refine_cache: CfgCacheBase = new CfgCacheBase(cfg_god_weapon_refine, "cfg_god_weapon_refine");
    cfg_god_weapon_skill_cache: CfgCacheBase = new CfgCacheBase(cfg_god_weapon_skill, "cfg_god_weapon_skill");
    cfg_god_weapon_skill_attr_cache: CfgCacheBase = new CfgCacheBase(cfg_god_weapon_skill_attr, "cfg_god_weapon_skill_attr");
    cfg_god_weapon_soul_cache: CfgCacheBase = new CfgCacheBase(cfg_god_weapon_soul, "cfg_god_weapon_soul");
    cfg_god_weapon_star_cache: CfgCacheBase = new CfgCacheBase(cfg_god_weapon_star, "cfg_god_weapon_star");
    cfg_gray_pinyin_cache: CfgCacheBase = new CfgCacheBase(cfg_gray_pinyin, "cfg_gray_pinyin");
    cfg_grow_tips_cache: CfgCacheBase = new CfgCacheBase(cfg_grow_tips, "cfg_grow_tips");
    cfg_guaji_box_time_cache: CfgCacheBase = new CfgCacheBase(cfg_guaji_box_time, "cfg_guaji_box_time");
    cfg_guaji_monster_cache: CfgCacheBase = new CfgCacheBase(cfg_guaji_monster, "cfg_guaji_monster");
    cfg_guaji_quick_navigation_cache: CfgCacheBase = new CfgCacheBase(cfg_guaji_quick_navigation, "cfg_guaji_quick_navigation");
    cfg_guandu_cache: CfgCacheBase = new CfgCacheBase(cfg_guandu, "cfg_guandu");
    cfg_guandu_answer_cache: CfgCacheBase = new CfgCacheBase(cfg_guandu_answer, "cfg_guandu_answer");
    cfg_guandu_chose_cache: CfgCacheBase = new CfgCacheBase(cfg_guandu_chose, "cfg_guandu_chose");
    cfg_guandu_floor_cache: CfgCacheBase = new CfgCacheBase(cfg_guandu_floor, "cfg_guandu_floor");
    cfg_guandu_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_guandu_mission, "cfg_guandu_mission");
    cfg_guandu_shop_cache: CfgCacheBase = new CfgCacheBase(cfg_guandu_shop, "cfg_guandu_shop");
    cfg_guide_helper_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_helper, "cfg_guide_helper");
    cfg_guide_helper_1_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_helper_1, "cfg_guide_helper_1");
    cfg_guide_helper_2_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_helper_2, "cfg_guide_helper_2");
    cfg_guide_helper_3_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_helper_3, "cfg_guide_helper_3");
    cfg_guide_helper_4_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_helper_4, "cfg_guide_helper_4");
    cfg_guide_helper_debug_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_helper_debug, "cfg_guide_helper_debug");
    cfg_guide_helper_game_1_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_helper_game_1, "cfg_guide_helper_game_1");
    cfg_guide_helper_m2_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_helper_m2, "cfg_guide_helper_m2");
    cfg_guide_helper_m3_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_helper_m3, "cfg_guide_helper_m3");
    cfg_guide_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_mission, "cfg_guide_mission");
    cfg_guide_mission_1_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_mission_1, "cfg_guide_mission_1");
    cfg_guide_mission_2_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_mission_2, "cfg_guide_mission_2");
    cfg_guide_mission_3_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_mission_3, "cfg_guide_mission_3");
    cfg_guide_mission_4_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_mission_4, "cfg_guide_mission_4");
    cfg_guide_mission_game_1_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_mission_game_1, "cfg_guide_mission_game_1");
    cfg_guide_mission_m2_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_mission_m2, "cfg_guide_mission_m2");
    cfg_guide_mission_m3_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_mission_m3, "cfg_guide_mission_m3");
    cfg_guide_review_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_review, "cfg_guide_review");
    cfg_guide_story_cache: CfgCacheBase = new CfgCacheBase(cfg_guide_story, "cfg_guide_story");
    cfg_hero_attr_addition_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_attr_addition, "cfg_hero_attr_addition");
    cfg_hero_attr_source_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_attr_source, "cfg_hero_attr_source");
    cfg_hero_bag_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_bag, "cfg_hero_bag");
    cfg_hero_base_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_base, "cfg_hero_base");
    cfg_hero_cheer_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_cheer, "cfg_hero_cheer");
    cfg_hero_cheer_bonus_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_cheer_bonus, "cfg_hero_cheer_bonus");
    cfg_hero_cheer_level_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_cheer_level, "cfg_hero_cheer_level");
    cfg_hero_cheer_unlock_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_cheer_unlock, "cfg_hero_cheer_unlock");
    cfg_hero_chip_star_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_chip_star, "cfg_hero_chip_star");
    cfg_hero_come_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_come_mission, "cfg_hero_come_mission");
    cfg_hero_convert_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_convert, "cfg_hero_convert");
    cfg_hero_convert_weight_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_convert_weight, "cfg_hero_convert_weight");
    cfg_hero_cost_plan_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_cost_plan, "cfg_hero_cost_plan");
    cfg_hero_evolve_skill_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_evolve_skill, "cfg_hero_evolve_skill");
    cfg_hero_handbook_desc_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_handbook_desc, "cfg_hero_handbook_desc");
    cfg_hero_level_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_level, "cfg_hero_level");
    cfg_hero_level_limit_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_level_limit, "cfg_hero_level_limit");
    cfg_hero_nation_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_nation, "cfg_hero_nation");
    cfg_hero_pass_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_pass_mission, "cfg_hero_pass_mission");
    cfg_hero_pass_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_pass_reward, "cfg_hero_pass_reward");
    cfg_hero_recommend_pre_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_recommend_pre, "cfg_hero_recommend_pre");
    cfg_hero_recycle_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_recycle, "cfg_hero_recycle");
    cfg_hero_recycle_change_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_recycle_change, "cfg_hero_recycle_change");
    cfg_hero_recycle_change_star_stage_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_recycle_change_star_stage, "cfg_hero_recycle_change_star_stage");
    cfg_hero_recycle_res_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_recycle_res, "cfg_hero_recycle_res");
    cfg_hero_recycle_special_switch_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_recycle_special_switch, "cfg_hero_recycle_special_switch");
    cfg_hero_resonate_dhyana_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_resonate_dhyana, "cfg_hero_resonate_dhyana");
    cfg_hero_resonate_dudu_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_resonate_dudu, "cfg_hero_resonate_dudu");
    cfg_hero_resonate_dudu_level_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_resonate_dudu_level, "cfg_hero_resonate_dudu_level");
    cfg_hero_resonate_five_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_resonate_five, "cfg_hero_resonate_five");
    cfg_hero_skin_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_skin, "cfg_hero_skin");
    cfg_hero_skin_level_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_skin_level, "cfg_hero_skin_level");
    cfg_hero_stage_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_stage, "cfg_hero_stage");
    cfg_hero_stage_limit_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_stage_limit, "cfg_hero_stage_limit");
    cfg_hero_star_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_star, "cfg_hero_star");
    cfg_hero_star_attr_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_star_attr, "cfg_hero_star_attr");
    cfg_hero_star_limit_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_star_limit, "cfg_hero_star_limit");
    cfg_hero_star_stage_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_star_stage, "cfg_hero_star_stage");
    cfg_hero_star_stage_attr_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_star_stage_attr, "cfg_hero_star_stage_attr");
    cfg_hero_strengthen_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_strengthen, "cfg_hero_strengthen");
    cfg_hero_upgrade_tips_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_upgrade_tips, "cfg_hero_upgrade_tips");
    cfg_hero_zhouyin_cache: CfgCacheBase = new CfgCacheBase(cfg_hero_zhouyin, "cfg_hero_zhouyin");
    cfg_hunt_buy_cache: CfgCacheBase = new CfgCacheBase(cfg_hunt_buy, "cfg_hunt_buy");
    cfg_hunt_cost_cache: CfgCacheBase = new CfgCacheBase(cfg_hunt_cost, "cfg_hunt_cost");
    cfg_hunt_desc_cache: CfgCacheBase = new CfgCacheBase(cfg_hunt_desc, "cfg_hunt_desc");
    cfg_hunt_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_hunt_gift, "cfg_hunt_gift");
    cfg_hunt_rewards_show_cache: CfgCacheBase = new CfgCacheBase(cfg_hunt_rewards_show, "cfg_hunt_rewards_show");
    cfg_huoqutujing_cache: CfgCacheBase = new CfgCacheBase(cfg_huoqutujing, "cfg_huoqutujing");
    cfg_hzzd_achievement_cache: CfgCacheBase = new CfgCacheBase(cfg_hzzd_achievement, "cfg_hzzd_achievement");
    cfg_hzzd_event_cache: CfgCacheBase = new CfgCacheBase(cfg_hzzd_event, "cfg_hzzd_event");
    cfg_hzzd_kills_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_hzzd_kills_reward, "cfg_hzzd_kills_reward");
    cfg_hzzd_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_hzzd_misc, "cfg_hzzd_misc");
    cfg_i18n_lang_cache: CfgCacheBase = new CfgCacheBase(cfg_i18n_lang, "cfg_i18n_lang");
    cfg_i18n_ts_cache: CfgCacheBase = new CfgCacheBase(cfg_i18n_ts, "cfg_i18n_ts");
    cfg_i18n_ui_cache: CfgCacheBase = new CfgCacheBase(cfg_i18n_ui, "cfg_i18n_ui");
    cfg_ingenious_plan_cache: CfgCacheBase = new CfgCacheBase(cfg_ingenious_plan, "cfg_ingenious_plan");
    cfg_ingenious_plan_compose_cache: CfgCacheBase = new CfgCacheBase(cfg_ingenious_plan_compose, "cfg_ingenious_plan_compose");
    cfg_ingenious_plan_convert_cache: CfgCacheBase = new CfgCacheBase(cfg_ingenious_plan_convert, "cfg_ingenious_plan_convert");
    cfg_ingenious_plan_level_cache: CfgCacheBase = new CfgCacheBase(cfg_ingenious_plan_level, "cfg_ingenious_plan_level");
    cfg_ingenious_plan_stage_cache: CfgCacheBase = new CfgCacheBase(cfg_ingenious_plan_stage, "cfg_ingenious_plan_stage");
    cfg_ingenious_plan_star_cache: CfgCacheBase = new CfgCacheBase(cfg_ingenious_plan_star, "cfg_ingenious_plan_star");
    cfg_ip_set_cache: CfgCacheBase = new CfgCacheBase(cfg_ip_set, "cfg_ip_set");
    cfg_item_cache: CfgCacheBase = new CfgCacheBase(cfg_item, "cfg_item");
    cfg_item_compose_cache: CfgCacheBase = new CfgCacheBase(cfg_item_compose, "cfg_item_compose");
    cfg_item_time_client_cache: CfgCacheBase = new CfgCacheBase(cfg_item_time_client, "cfg_item_time_client");
    cfg_large_peak_agent_cache: CfgCacheBase = new CfgCacheBase(cfg_large_peak_agent, "cfg_large_peak_agent");
    cfg_large_peak_battle_table_cache: CfgCacheBase = new CfgCacheBase(cfg_large_peak_battle_table, "cfg_large_peak_battle_table");
    cfg_large_peak_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_large_peak_misc, "cfg_large_peak_misc");
    cfg_large_peak_rank_cache: CfgCacheBase = new CfgCacheBase(cfg_large_peak_rank, "cfg_large_peak_rank");
    cfg_large_peak_season_cache: CfgCacheBase = new CfgCacheBase(cfg_large_peak_season, "cfg_large_peak_season");
    cfg_large_peak_time_cache: CfgCacheBase = new CfgCacheBase(cfg_large_peak_time, "cfg_large_peak_time");
    cfg_lazy_load_cache: CfgCacheBase = new CfgCacheBase(cfg_lazy_load, "cfg_lazy_load");
    cfg_lcqs_acc_star_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_lcqs_acc_star_reward, "cfg_lcqs_acc_star_reward");
    cfg_lcqs_chapter_open_cache: CfgCacheBase = new CfgCacheBase(cfg_lcqs_chapter_open, "cfg_lcqs_chapter_open");
    cfg_lcqs_floor_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_lcqs_floor_reward, "cfg_lcqs_floor_reward");
    cfg_lcqs_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_lcqs_mission, "cfg_lcqs_mission");
    cfg_level_cache: CfgCacheBase = new CfgCacheBase(cfg_level, "cfg_level");
    cfg_level_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_level_gift, "cfg_level_gift");
    cfg_limit_hero_skin_chip_exchange_cache: CfgCacheBase = new CfgCacheBase(cfg_limit_hero_skin_chip_exchange, "cfg_limit_hero_skin_chip_exchange");
    cfg_lineup_buff_cache: CfgCacheBase = new CfgCacheBase(cfg_lineup_buff, "cfg_lineup_buff");
    cfg_lineup_buff_icon_cache: CfgCacheBase = new CfgCacheBase(cfg_lineup_buff_icon, "cfg_lineup_buff_icon");
    cfg_lineup_career_rule_cache: CfgCacheBase = new CfgCacheBase(cfg_lineup_career_rule, "cfg_lineup_career_rule");
    cfg_lineup_num_cache: CfgCacheBase = new CfgCacheBase(cfg_lineup_num, "cfg_lineup_num");
    cfg_lineup_recommend_cache: CfgCacheBase = new CfgCacheBase(cfg_lineup_recommend, "cfg_lineup_recommend");
    cfg_lineup_style_cache: CfgCacheBase = new CfgCacheBase(cfg_lineup_style, "cfg_lineup_style");
    cfg_load_tips_cache: CfgCacheBase = new CfgCacheBase(cfg_load_tips, "cfg_load_tips");
    cfg_login_activity_cache: CfgCacheBase = new CfgCacheBase(cfg_login_activity, "cfg_login_activity");
    cfg_login_activity_round_cache: CfgCacheBase = new CfgCacheBase(cfg_login_activity_round, "cfg_login_activity_round");
    cfg_lord_activation_cache: CfgCacheBase = new CfgCacheBase(cfg_lord_activation, "cfg_lord_activation");
    cfg_lord_base_cache: CfgCacheBase = new CfgCacheBase(cfg_lord_base, "cfg_lord_base");
    cfg_lord_camp_cache: CfgCacheBase = new CfgCacheBase(cfg_lord_camp, "cfg_lord_camp");
    cfg_lord_exchange_cache: CfgCacheBase = new CfgCacheBase(cfg_lord_exchange, "cfg_lord_exchange");
    cfg_lord_skill_cache: CfgCacheBase = new CfgCacheBase(cfg_lord_skill, "cfg_lord_skill");
    cfg_lord_skill_enhance_cache: CfgCacheBase = new CfgCacheBase(cfg_lord_skill_enhance, "cfg_lord_skill_enhance");
    cfg_lord_star_cache: CfgCacheBase = new CfgCacheBase(cfg_lord_star, "cfg_lord_star");
    cfg_lord_suit_compose_cache: CfgCacheBase = new CfgCacheBase(cfg_lord_suit_compose, "cfg_lord_suit_compose");
    cfg_lord_suit_select_cache: CfgCacheBase = new CfgCacheBase(cfg_lord_suit_select, "cfg_lord_suit_select");
    cfg_lord_treasure_cache: CfgCacheBase = new CfgCacheBase(cfg_lord_treasure, "cfg_lord_treasure");
    cfg_lord_treasure_entry_cache: CfgCacheBase = new CfgCacheBase(cfg_lord_treasure_entry, "cfg_lord_treasure_entry");
    cfg_lord_treasure_forge_cache: CfgCacheBase = new CfgCacheBase(cfg_lord_treasure_forge, "cfg_lord_treasure_forge");
    cfg_lord_treasure_level_cache: CfgCacheBase = new CfgCacheBase(cfg_lord_treasure_level, "cfg_lord_treasure_level");
    cfg_lottery_day_limit_cache: CfgCacheBase = new CfgCacheBase(cfg_lottery_day_limit, "cfg_lottery_day_limit");
    cfg_lottery_ext_cache: CfgCacheBase = new CfgCacheBase(cfg_lottery_ext, "cfg_lottery_ext");
    cfg_lottery_nation_cache: CfgCacheBase = new CfgCacheBase(cfg_lottery_nation, "cfg_lottery_nation");
    cfg_lottery_nation_times_cache: CfgCacheBase = new CfgCacheBase(cfg_lottery_nation_times, "cfg_lottery_nation_times");
    cfg_lottery_score_cache: CfgCacheBase = new CfgCacheBase(cfg_lottery_score, "cfg_lottery_score");
    cfg_lottery_show_cache: CfgCacheBase = new CfgCacheBase(cfg_lottery_show, "cfg_lottery_show");
    cfg_lottery_times_cache: CfgCacheBase = new CfgCacheBase(cfg_lottery_times, "cfg_lottery_times");
    cfg_lottery_weight_cache: CfgCacheBase = new CfgCacheBase(cfg_lottery_weight, "cfg_lottery_weight");
    cfg_main_battle_cache: CfgCacheBase = new CfgCacheBase(cfg_main_battle, "cfg_main_battle");
    cfg_main_battle_box_cache: CfgCacheBase = new CfgCacheBase(cfg_main_battle_box, "cfg_main_battle_box");
    cfg_main_battle_box_level_cache: CfgCacheBase = new CfgCacheBase(cfg_main_battle_box_level, "cfg_main_battle_box_level");
    cfg_main_battle_box_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_main_battle_box_reward, "cfg_main_battle_box_reward");
    cfg_main_battle_box_tequan_cache: CfgCacheBase = new CfgCacheBase(cfg_main_battle_box_tequan, "cfg_main_battle_box_tequan");
    cfg_main_battle_fetch_cache: CfgCacheBase = new CfgCacheBase(cfg_main_battle_fetch, "cfg_main_battle_fetch");
    cfg_main_battle_hanging_cache: CfgCacheBase = new CfgCacheBase(cfg_main_battle_hanging, "cfg_main_battle_hanging");
    cfg_main_battle_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_main_battle_mission, "cfg_main_battle_mission");
    cfg_map_cache: CfgCacheBase = new CfgCacheBase(cfg_map, "cfg_map");
    cfg_map_item_cache: CfgCacheBase = new CfgCacheBase(cfg_map_item, "cfg_map_item");
    cfg_map_item_type_cache: CfgCacheBase = new CfgCacheBase(cfg_map_item_type, "cfg_map_item_type");
    cfg_map_type_cache: CfgCacheBase = new CfgCacheBase(cfg_map_type, "cfg_map_type");
    cfg_master_card_cache: CfgCacheBase = new CfgCacheBase(cfg_master_card, "cfg_master_card");
    cfg_master_card_attr_cache: CfgCacheBase = new CfgCacheBase(cfg_master_card_attr, "cfg_master_card_attr");
    cfg_master_card_color_cache: CfgCacheBase = new CfgCacheBase(cfg_master_card_color, "cfg_master_card_color");
    cfg_master_card_drum_level_cache: CfgCacheBase = new CfgCacheBase(cfg_master_card_drum_level, "cfg_master_card_drum_level");
    cfg_master_card_first_pay_cache: CfgCacheBase = new CfgCacheBase(cfg_master_card_first_pay, "cfg_master_card_first_pay");
    cfg_master_card_help_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_master_card_help_gift, "cfg_master_card_help_gift");
    cfg_master_card_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_master_card_misc, "cfg_master_card_misc");
    cfg_master_card_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_master_card_mission, "cfg_master_card_mission");
    cfg_master_card_official_cache: CfgCacheBase = new CfgCacheBase(cfg_master_card_official, "cfg_master_card_official");
    cfg_master_card_official_position_cache: CfgCacheBase = new CfgCacheBase(cfg_master_card_official_position, "cfg_master_card_official_position");
    cfg_master_card_reshape_cache: CfgCacheBase = new CfgCacheBase(cfg_master_card_reshape, "cfg_master_card_reshape");
    cfg_master_card_slot_cache: CfgCacheBase = new CfgCacheBase(cfg_master_card_slot, "cfg_master_card_slot");
    cfg_master_card_stage_cache: CfgCacheBase = new CfgCacheBase(cfg_master_card_stage, "cfg_master_card_stage");
    cfg_master_talent_science_cache: CfgCacheBase = new CfgCacheBase(cfg_master_talent_science, "cfg_master_talent_science");
    cfg_master_talent_science_icon_cache: CfgCacheBase = new CfgCacheBase(cfg_master_talent_science_icon, "cfg_master_talent_science_icon");
    cfg_match_type_cache: CfgCacheBase = new CfgCacheBase(cfg_match_type, "cfg_match_type");
    cfg_match_type_team_cache: CfgCacheBase = new CfgCacheBase(cfg_match_type_team, "cfg_match_type_team");
    cfg_maze_cache: CfgCacheBase = new CfgCacheBase(cfg_maze, "cfg_maze");
    cfg_maze_diff_rewards_cache: CfgCacheBase = new CfgCacheBase(cfg_maze_diff_rewards, "cfg_maze_diff_rewards");
    cfg_maze_mission_spoils_cache: CfgCacheBase = new CfgCacheBase(cfg_maze_mission_spoils, "cfg_maze_mission_spoils");
    cfg_maze_monster_cache: CfgCacheBase = new CfgCacheBase(cfg_maze_monster, "cfg_maze_monster");
    cfg_maze_reset_cache: CfgCacheBase = new CfgCacheBase(cfg_maze_reset, "cfg_maze_reset");
    cfg_maze_revive_cache: CfgCacheBase = new CfgCacheBase(cfg_maze_revive, "cfg_maze_revive");
    cfg_maze_shop_cache: CfgCacheBase = new CfgCacheBase(cfg_maze_shop, "cfg_maze_shop");
    cfg_maze_theme_cache: CfgCacheBase = new CfgCacheBase(cfg_maze_theme, "cfg_maze_theme");
    cfg_medal_cache: CfgCacheBase = new CfgCacheBase(cfg_medal, "cfg_medal");
    cfg_microterminal_open_cache: CfgCacheBase = new CfgCacheBase(cfg_microterminal_open, "cfg_microterminal_open");
    cfg_microterminal_sign_cache: CfgCacheBase = new CfgCacheBase(cfg_microterminal_sign, "cfg_microterminal_sign");
    cfg_misc_config_cache: CfgCacheBase = new CfgCacheBase(cfg_misc_config, "cfg_misc_config");
    cfg_mission_shop_cache: CfgCacheBase = new CfgCacheBase(cfg_mission_shop, "cfg_mission_shop");
    cfg_mission_shop_client_cache: CfgCacheBase = new CfgCacheBase(cfg_mission_shop_client, "cfg_mission_shop_client");
    cfg_mock_battle_hero_base_cache: CfgCacheBase = new CfgCacheBase(cfg_mock_battle_hero_base, "cfg_mock_battle_hero_base");
    cfg_mock_pvp_database_cache: CfgCacheBase = new CfgCacheBase(cfg_mock_pvp_database, "cfg_mock_pvp_database");
    cfg_mock_pvp_hero_base_cache: CfgCacheBase = new CfgCacheBase(cfg_mock_pvp_hero_base, "cfg_mock_pvp_hero_base");
    cfg_mock_pvp_limit_cache: CfgCacheBase = new CfgCacheBase(cfg_mock_pvp_limit, "cfg_mock_pvp_limit");
    cfg_mock_pvp_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_mock_pvp_misc, "cfg_mock_pvp_misc");
    cfg_mock_pvp_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_mock_pvp_mission, "cfg_mock_pvp_mission");
    cfg_modular_activity_bless_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_bless, "cfg_modular_activity_bless");
    cfg_modular_activity_brick_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_brick, "cfg_modular_activity_brick");
    cfg_modular_activity_carnival_link_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_carnival_link, "cfg_modular_activity_carnival_link");
    cfg_modular_activity_client_setting_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_client_setting, "cfg_modular_activity_client_setting");
    cfg_modular_activity_compose_list_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_compose_list, "cfg_modular_activity_compose_list");
    cfg_modular_activity_customized_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_customized_gift, "cfg_modular_activity_customized_gift");
    cfg_modular_activity_dice_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_dice, "cfg_modular_activity_dice");
    cfg_modular_activity_dice_boss_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_dice_boss, "cfg_modular_activity_dice_boss");
    cfg_modular_activity_dice_client_diff_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_dice_client_diff, "cfg_modular_activity_dice_client_diff");
    cfg_modular_activity_dice_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_dice_misc, "cfg_modular_activity_dice_misc");
    cfg_modular_activity_drop_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_drop, "cfg_modular_activity_drop");
    cfg_modular_activity_drop_show_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_drop_show, "cfg_modular_activity_drop_show");
    cfg_modular_activity_exchange_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_exchange, "cfg_modular_activity_exchange");
    cfg_modular_activity_festival_wish_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_festival_wish, "cfg_modular_activity_festival_wish");
    cfg_modular_activity_festival_wish_choose_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_festival_wish_choose, "cfg_modular_activity_festival_wish_choose");
    cfg_modular_activity_festival_wish_cost_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_festival_wish_cost, "cfg_modular_activity_festival_wish_cost");
    cfg_modular_activity_free_switch_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_free_switch, "cfg_modular_activity_free_switch");
    cfg_modular_activity_general_pass_vip_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_general_pass_vip, "cfg_modular_activity_general_pass_vip");
    cfg_modular_activity_hero_challenge_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_hero_challenge, "cfg_modular_activity_hero_challenge");
    cfg_modular_activity_holiday_welfare_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_holiday_welfare_reward, "cfg_modular_activity_holiday_welfare_reward");
    cfg_modular_activity_hunt_cost_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_hunt_cost, "cfg_modular_activity_hunt_cost");
    cfg_modular_activity_hunt_desc_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_hunt_desc, "cfg_modular_activity_hunt_desc");
    cfg_modular_activity_hunt_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_hunt_misc, "cfg_modular_activity_hunt_misc");
    cfg_modular_activity_huoqutujing_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_huoqutujing, "cfg_modular_activity_huoqutujing");
    cfg_modular_activity_icon_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_icon, "cfg_modular_activity_icon");
    cfg_modular_activity_login_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_login, "cfg_modular_activity_login");
    cfg_modular_activity_lottery_target_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_lottery_target, "cfg_modular_activity_lottery_target");
    cfg_modular_activity_lottery_times_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_lottery_times, "cfg_modular_activity_lottery_times");
    cfg_modular_activity_lucky_bag_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_lucky_bag, "cfg_modular_activity_lucky_bag");
    cfg_modular_activity_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_mission, "cfg_modular_activity_mission");
    cfg_modular_activity_open_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_open, "cfg_modular_activity_open");
    cfg_modular_activity_open_preview_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_open_preview, "cfg_modular_activity_open_preview");
    cfg_modular_activity_pay_welfare_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_pay_welfare, "cfg_modular_activity_pay_welfare");
    cfg_modular_activity_payment_shop_item_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_payment_shop_item, "cfg_modular_activity_payment_shop_item");
    cfg_modular_activity_payment_shop_item_show_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_payment_shop_item_show, "cfg_modular_activity_payment_shop_item_show");
    cfg_modular_activity_preview_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_preview, "cfg_modular_activity_preview");
    cfg_modular_activity_preview_rewards_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_preview_rewards, "cfg_modular_activity_preview_rewards");
    cfg_modular_activity_rank_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_rank, "cfg_modular_activity_rank");
    cfg_modular_activity_rank_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_rank_reward, "cfg_modular_activity_rank_reward");
    cfg_modular_activity_round_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_round_mission, "cfg_modular_activity_round_mission");
    cfg_modular_activity_round_mission_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_round_mission_reward, "cfg_modular_activity_round_mission_reward");
    cfg_modular_activity_shop_client_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_shop_client, "cfg_modular_activity_shop_client");
    cfg_modular_activity_sign_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_sign, "cfg_modular_activity_sign");
    cfg_modular_activity_six_bless_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_six_bless, "cfg_modular_activity_six_bless");
    cfg_modular_activity_star_plan_hero_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_star_plan_hero, "cfg_modular_activity_star_plan_hero");
    cfg_modular_activity_star_plan_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_star_plan_reward, "cfg_modular_activity_star_plan_reward");
    cfg_modular_activity_story_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_story, "cfg_modular_activity_story");
    cfg_modular_activity_story_chapter_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_story_chapter, "cfg_modular_activity_story_chapter");
    cfg_modular_activity_story_chapter_map_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_story_chapter_map, "cfg_modular_activity_story_chapter_map");
    cfg_modular_activity_story_dialogue_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_story_dialogue, "cfg_modular_activity_story_dialogue");
    cfg_modular_activity_sub_type_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_sub_type, "cfg_modular_activity_sub_type");
    cfg_modular_activity_target_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_target, "cfg_modular_activity_target");
    cfg_modular_activity_time_item_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_time_item, "cfg_modular_activity_time_item");
    cfg_modular_activity_wall_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_wall, "cfg_modular_activity_wall");
    cfg_modular_activity_war_log_acc_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_war_log_acc_reward, "cfg_modular_activity_war_log_acc_reward");
    cfg_modular_activity_war_log_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_war_log_mission, "cfg_modular_activity_war_log_mission");
    cfg_modular_activity_weekly_card_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_modular_activity_weekly_card_reward, "cfg_modular_activity_weekly_card_reward");
    cfg_money_cache: CfgCacheBase = new CfgCacheBase(cfg_money, "cfg_money");
    cfg_monster_cache: CfgCacheBase = new CfgCacheBase(cfg_monster, "cfg_monster");
    cfg_monster_group_cache: CfgCacheBase = new CfgCacheBase(cfg_monster_group, "cfg_monster_group");
    cfg_monster_skill_tier_cache: CfgCacheBase = new CfgCacheBase(cfg_monster_skill_tier, "cfg_monster_skill_tier");
    cfg_monster_tips_cache: CfgCacheBase = new CfgCacheBase(cfg_monster_tips, "cfg_monster_tips");
    cfg_month_fund_cache: CfgCacheBase = new CfgCacheBase(cfg_month_fund, "cfg_month_fund");
    cfg_month_fund_type_cache: CfgCacheBase = new CfgCacheBase(cfg_month_fund_type, "cfg_month_fund_type");
    cfg_music_cache: CfgCacheBase = new CfgCacheBase(cfg_music, "cfg_music");
    cfg_nation_tower_lineup_cache: CfgCacheBase = new CfgCacheBase(cfg_nation_tower_lineup, "cfg_nation_tower_lineup");
    cfg_nation_tower_open_cache: CfgCacheBase = new CfgCacheBase(cfg_nation_tower_open, "cfg_nation_tower_open");
    cfg_noob_pay_cache: CfgCacheBase = new CfgCacheBase(cfg_noob_pay, "cfg_noob_pay");
    cfg_online_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_online_reward, "cfg_online_reward");
    cfg_pass_behead_cache: CfgCacheBase = new CfgCacheBase(cfg_pass_behead, "cfg_pass_behead");
    cfg_pass_behead_box_cache: CfgCacheBase = new CfgCacheBase(cfg_pass_behead_box, "cfg_pass_behead_box");
    cfg_pass_behead_guanqia_cache: CfgCacheBase = new CfgCacheBase(cfg_pass_behead_guanqia, "cfg_pass_behead_guanqia");
    cfg_pass_behead_revive_cache: CfgCacheBase = new CfgCacheBase(cfg_pass_behead_revive, "cfg_pass_behead_revive");
    cfg_pass_check_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_pass_check_mission, "cfg_pass_check_mission");
    cfg_pass_check_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_pass_check_reward, "cfg_pass_check_reward");
    cfg_pass_check_vip_cache: CfgCacheBase = new CfgCacheBase(cfg_pass_check_vip, "cfg_pass_check_vip");
    cfg_pay_vip_cache: CfgCacheBase = new CfgCacheBase(cfg_pay_vip, "cfg_pay_vip");
    cfg_pay_vip_privilege_cache: CfgCacheBase = new CfgCacheBase(cfg_pay_vip_privilege, "cfg_pay_vip_privilege");
    cfg_pay_vip_privilege_function_cache: CfgCacheBase = new CfgCacheBase(cfg_pay_vip_privilege_function, "cfg_pay_vip_privilege_function");
    cfg_payment_shop_item_cache: CfgCacheBase = new CfgCacheBase(cfg_payment_shop_item, "cfg_payment_shop_item");
    cfg_payment_shop_link_cache: CfgCacheBase = new CfgCacheBase(cfg_payment_shop_link, "cfg_payment_shop_link");
    cfg_payment_time_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_payment_time_gift, "cfg_payment_time_gift");
    cfg_peak_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_peak_misc, "cfg_peak_misc");
    cfg_peak_time_cache: CfgCacheBase = new CfgCacheBase(cfg_peak_time, "cfg_peak_time");
    cfg_peerless_act_hero_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_peerless_act_hero_gift, "cfg_peerless_act_hero_gift");
    cfg_platform_ad_id_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_platform_ad_id_misc, "cfg_platform_ad_id_misc");
    cfg_player_strategy_cache: CfgCacheBase = new CfgCacheBase(cfg_player_strategy, "cfg_player_strategy");
    cfg_playing_preview_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_playing_preview_reward, "cfg_playing_preview_reward");
    cfg_progress_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_progress_gift, "cfg_progress_gift");
    cfg_pull_words_cache: CfgCacheBase = new CfgCacheBase(cfg_pull_words, "cfg_pull_words");
    cfg_pvp_map_cache: CfgCacheBase = new CfgCacheBase(cfg_pvp_map, "cfg_pvp_map");
    cfg_qq_group_cache: CfgCacheBase = new CfgCacheBase(cfg_qq_group, "cfg_qq_group");
    cfg_qq_vip_cache: CfgCacheBase = new CfgCacheBase(cfg_qq_vip, "cfg_qq_vip");
    cfg_qxzl_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_qxzl_misc, "cfg_qxzl_misc");
    cfg_random_box_cache: CfgCacheBase = new CfgCacheBase(cfg_random_box, "cfg_random_box");
    cfg_random_pvp_cache: CfgCacheBase = new CfgCacheBase(cfg_random_pvp, "cfg_random_pvp");
    cfg_random_pvp_head_frame_cache: CfgCacheBase = new CfgCacheBase(cfg_random_pvp_head_frame, "cfg_random_pvp_head_frame");
    cfg_random_pvp_limit_cache: CfgCacheBase = new CfgCacheBase(cfg_random_pvp_limit, "cfg_random_pvp_limit");
    cfg_random_pvp_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_random_pvp_reward, "cfg_random_pvp_reward");
    cfg_random_pvp_task_cache: CfgCacheBase = new CfgCacheBase(cfg_random_pvp_task, "cfg_random_pvp_task");
    cfg_random_pvp_task_rewards_cache: CfgCacheBase = new CfgCacheBase(cfg_random_pvp_task_rewards, "cfg_random_pvp_task_rewards");
    cfg_rank_desc_cache: CfgCacheBase = new CfgCacheBase(cfg_rank_desc, "cfg_rank_desc");
    cfg_rank_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_rank_mission, "cfg_rank_mission");
    cfg_rank_rewards_cache: CfgCacheBase = new CfgCacheBase(cfg_rank_rewards, "cfg_rank_rewards");
    cfg_rank_worship_cache: CfgCacheBase = new CfgCacheBase(cfg_rank_worship, "cfg_rank_worship");
    cfg_red_cliff_cache: CfgCacheBase = new CfgCacheBase(cfg_red_cliff, "cfg_red_cliff");
    cfg_red_cliff_boss_cache: CfgCacheBase = new CfgCacheBase(cfg_red_cliff_boss, "cfg_red_cliff_boss");
    cfg_red_cliff_open_cache: CfgCacheBase = new CfgCacheBase(cfg_red_cliff_open, "cfg_red_cliff_open");
    cfg_red_cliff_refresh_cache: CfgCacheBase = new CfgCacheBase(cfg_red_cliff_refresh, "cfg_red_cliff_refresh");
    cfg_red_cliff_review_cache: CfgCacheBase = new CfgCacheBase(cfg_red_cliff_review, "cfg_red_cliff_review");
    cfg_retrieval_cache: CfgCacheBase = new CfgCacheBase(cfg_retrieval, "cfg_retrieval");
    cfg_river_text_const_cache: CfgCacheBase = new CfgCacheBase(cfg_river_text_const, "cfg_river_text_const");
    cfg_role_profile_cache: CfgCacheBase = new CfgCacheBase(cfg_role_profile, "cfg_role_profile");
    cfg_san_xiao_actor_cache: CfgCacheBase = new CfgCacheBase(cfg_san_xiao_actor, "cfg_san_xiao_actor");
    cfg_san_xiao_guanqia_cache: CfgCacheBase = new CfgCacheBase(cfg_san_xiao_guanqia, "cfg_san_xiao_guanqia");
    cfg_san_xiao_item_cache: CfgCacheBase = new CfgCacheBase(cfg_san_xiao_item, "cfg_san_xiao_item");
    cfg_san_xiao_level_cache: CfgCacheBase = new CfgCacheBase(cfg_san_xiao_level, "cfg_san_xiao_level");
    cfg_san_xiao_map_cache: CfgCacheBase = new CfgCacheBase(cfg_san_xiao_map, "cfg_san_xiao_map");
    cfg_san_xiao_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_san_xiao_misc, "cfg_san_xiao_misc");
    cfg_scene_cache: CfgCacheBase = new CfgCacheBase(cfg_scene, "cfg_scene");
    cfg_sdk_concern_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_sdk_concern_reward, "cfg_sdk_concern_reward");
    cfg_sdk_platform_desc_cache: CfgCacheBase = new CfgCacheBase(cfg_sdk_platform_desc, "cfg_sdk_platform_desc");
    cfg_sdk_rewards_cache: CfgCacheBase = new CfgCacheBase(cfg_sdk_rewards, "cfg_sdk_rewards");
    cfg_select_box_cache: CfgCacheBase = new CfgCacheBase(cfg_select_box, "cfg_select_box");
    cfg_seven_goal_cache: CfgCacheBase = new CfgCacheBase(cfg_seven_goal, "cfg_seven_goal");
    cfg_seven_goal_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_seven_goal_gift, "cfg_seven_goal_gift");
    cfg_seven_goal_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_seven_goal_mission, "cfg_seven_goal_mission");
    cfg_share_cycle_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_share_cycle_reward, "cfg_share_cycle_reward");
    cfg_share_daily_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_share_daily_reward, "cfg_share_daily_reward");
    cfg_share_level_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_share_level_reward, "cfg_share_level_reward");
    cfg_shili_preview_cache: CfgCacheBase = new CfgCacheBase(cfg_shili_preview, "cfg_shili_preview");
    cfg_shop_cache: CfgCacheBase = new CfgCacheBase(cfg_shop, "cfg_shop");
    cfg_shop_item_cache: CfgCacheBase = new CfgCacheBase(cfg_shop_item, "cfg_shop_item");
    cfg_shop_item_tips_cache: CfgCacheBase = new CfgCacheBase(cfg_shop_item_tips, "cfg_shop_item_tips");
    cfg_shop_reset_times_cache: CfgCacheBase = new CfgCacheBase(cfg_shop_reset_times, "cfg_shop_reset_times");
    cfg_shop_shortcut_cache: CfgCacheBase = new CfgCacheBase(cfg_shop_shortcut, "cfg_shop_shortcut");
    cfg_shop_tab_limit_cache: CfgCacheBase = new CfgCacheBase(cfg_shop_tab_limit, "cfg_shop_tab_limit");
    cfg_show_off_cache: CfgCacheBase = new CfgCacheBase(cfg_show_off, "cfg_show_off");
    cfg_skeleton_adaptive_cache: CfgCacheBase = new CfgCacheBase(cfg_skeleton_adaptive, "cfg_skeleton_adaptive");
    cfg_skill_cache: CfgCacheBase = new CfgCacheBase(cfg_skill, "cfg_skill");
    cfg_skill_effect_cache: CfgCacheBase = new CfgCacheBase(cfg_skill_effect, "cfg_skill_effect");
    cfg_skill_event_cache: CfgCacheBase = new CfgCacheBase(cfg_skill_event, "cfg_skill_event");
    cfg_skill_level_cache: CfgCacheBase = new CfgCacheBase(cfg_skill_level, "cfg_skill_level");
    cfg_skill_summon_cache: CfgCacheBase = new CfgCacheBase(cfg_skill_summon, "cfg_skill_summon");
    cfg_small_game_cache: CfgCacheBase = new CfgCacheBase(cfg_small_game, "cfg_small_game");
    cfg_soldier_game_cache: CfgCacheBase = new CfgCacheBase(cfg_soldier_game, "cfg_soldier_game");
    cfg_soldier_game_rewards_cache: CfgCacheBase = new CfgCacheBase(cfg_soldier_game_rewards, "cfg_soldier_game_rewards");
    cfg_soul_hero_link_level_cache: CfgCacheBase = new CfgCacheBase(cfg_soul_hero_link_level, "cfg_soul_hero_link_level");
    cfg_soul_hero_link_limit_unlock_cache: CfgCacheBase = new CfgCacheBase(cfg_soul_hero_link_limit_unlock, "cfg_soul_hero_link_limit_unlock");
    cfg_soul_hero_link_nation_cache: CfgCacheBase = new CfgCacheBase(cfg_soul_hero_link_nation, "cfg_soul_hero_link_nation");
    cfg_stage_breed_cache: CfgCacheBase = new CfgCacheBase(cfg_stage_breed, "cfg_stage_breed");
    cfg_stage_breed_attr_cache: CfgCacheBase = new CfgCacheBase(cfg_stage_breed_attr, "cfg_stage_breed_attr");
    cfg_stage_copy_cache: CfgCacheBase = new CfgCacheBase(cfg_stage_copy, "cfg_stage_copy");
    cfg_stage_copy_boss_cache: CfgCacheBase = new CfgCacheBase(cfg_stage_copy_boss, "cfg_stage_copy_boss");
    cfg_stage_copy_daily_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_stage_copy_daily_mission, "cfg_stage_copy_daily_mission");
    cfg_stage_copy_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_stage_copy_misc, "cfg_stage_copy_misc");
    cfg_stage_copy_story_cache: CfgCacheBase = new CfgCacheBase(cfg_stage_copy_story, "cfg_stage_copy_story");
    cfg_stage_map_cache: CfgCacheBase = new CfgCacheBase(cfg_stage_map, "cfg_stage_map");
    cfg_stage_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_stage_mission, "cfg_stage_mission");
    cfg_stage_skill_attr_cache: CfgCacheBase = new CfgCacheBase(cfg_stage_skill_attr, "cfg_stage_skill_attr");
    cfg_stage_skill_type_cache: CfgCacheBase = new CfgCacheBase(cfg_stage_skill_type, "cfg_stage_skill_type");
    cfg_star_plan_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_star_plan_gift, "cfg_star_plan_gift");
    cfg_star_plan_hero_cache: CfgCacheBase = new CfgCacheBase(cfg_star_plan_hero, "cfg_star_plan_hero");
    cfg_star_plan_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_star_plan_reward, "cfg_star_plan_reward");
    cfg_story_cache: CfgCacheBase = new CfgCacheBase(cfg_story, "cfg_story");
    cfg_story_action_cache: CfgCacheBase = new CfgCacheBase(cfg_story_action, "cfg_story_action");
    cfg_story_actor_cache: CfgCacheBase = new CfgCacheBase(cfg_story_actor, "cfg_story_actor");
    cfg_story_bubble_cache: CfgCacheBase = new CfgCacheBase(cfg_story_bubble, "cfg_story_bubble");
    cfg_story_maze_cache: CfgCacheBase = new CfgCacheBase(cfg_story_maze, "cfg_story_maze");
    cfg_story_maze_mission_spoils_cache: CfgCacheBase = new CfgCacheBase(cfg_story_maze_mission_spoils, "cfg_story_maze_mission_spoils");
    cfg_story_maze_monster_cache: CfgCacheBase = new CfgCacheBase(cfg_story_maze_monster, "cfg_story_maze_monster");
    cfg_story_maze_reset_cache: CfgCacheBase = new CfgCacheBase(cfg_story_maze_reset, "cfg_story_maze_reset");
    cfg_story_maze_revive_cache: CfgCacheBase = new CfgCacheBase(cfg_story_maze_revive, "cfg_story_maze_revive");
    cfg_story_maze_rewards_cache: CfgCacheBase = new CfgCacheBase(cfg_story_maze_rewards, "cfg_story_maze_rewards");
    cfg_story_maze_shop_cache: CfgCacheBase = new CfgCacheBase(cfg_story_maze_shop, "cfg_story_maze_shop");
    cfg_story_maze_theme_cache: CfgCacheBase = new CfgCacheBase(cfg_story_maze_theme, "cfg_story_maze_theme");
    cfg_story_siegelord_city_type_cache: CfgCacheBase = new CfgCacheBase(cfg_story_siegelord_city_type, "cfg_story_siegelord_city_type");
    cfg_story_siegelord_level_cache: CfgCacheBase = new CfgCacheBase(cfg_story_siegelord_level, "cfg_story_siegelord_level");
    cfg_story_siegelord_level_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_story_siegelord_level_reward, "cfg_story_siegelord_level_reward");
    cfg_story_siegelord_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_story_siegelord_misc, "cfg_story_siegelord_misc");
    cfg_story_siegelord_pass_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_story_siegelord_pass_reward, "cfg_story_siegelord_pass_reward");
    cfg_story_tower_battle_cache: CfgCacheBase = new CfgCacheBase(cfg_story_tower_battle, "cfg_story_tower_battle");
    cfg_story_tower_battle_monster_cache: CfgCacheBase = new CfgCacheBase(cfg_story_tower_battle_monster, "cfg_story_tower_battle_monster");
    cfg_story_tower_battle_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_story_tower_battle_reward, "cfg_story_tower_battle_reward");
    cfg_suit_attr_cache: CfgCacheBase = new CfgCacheBase(cfg_suit_attr, "cfg_suit_attr");
    cfg_supreme_lottery_cache: CfgCacheBase = new CfgCacheBase(cfg_supreme_lottery, "cfg_supreme_lottery");
    cfg_svip_pay_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_svip_pay_gift, "cfg_svip_pay_gift");
    cfg_sweapon_stage_cache: CfgCacheBase = new CfgCacheBase(cfg_sweapon_stage, "cfg_sweapon_stage");
    cfg_sys_open_notice_cache: CfgCacheBase = new CfgCacheBase(cfg_sys_open_notice, "cfg_sys_open_notice");
    cfg_sys_openlv_cache: CfgCacheBase = new CfgCacheBase(cfg_sys_openlv, "cfg_sys_openlv");
    cfg_sys_use_times_cache: CfgCacheBase = new CfgCacheBase(cfg_sys_use_times, "cfg_sys_use_times");
    cfg_tax_cache: CfgCacheBase = new CfgCacheBase(cfg_tax, "cfg_tax");
    cfg_tax_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_tax_reward, "cfg_tax_reward");
    cfg_td_main_cache: CfgCacheBase = new CfgCacheBase(cfg_td_main, "cfg_td_main");
    cfg_td_main_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_td_main_mission, "cfg_td_main_mission");
    cfg_td_main_monster_cache: CfgCacheBase = new CfgCacheBase(cfg_td_main_monster, "cfg_td_main_monster");
    cfg_td_main_pass_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_td_main_pass_mission, "cfg_td_main_pass_mission");
    cfg_td_map_cache: CfgCacheBase = new CfgCacheBase(cfg_td_map, "cfg_td_map");
    cfg_td_monster_talk_cache: CfgCacheBase = new CfgCacheBase(cfg_td_monster_talk, "cfg_td_monster_talk");
    cfg_td_trial_cache: CfgCacheBase = new CfgCacheBase(cfg_td_trial, "cfg_td_trial");
    cfg_td_trial_map_cache: CfgCacheBase = new CfgCacheBase(cfg_td_trial_map, "cfg_td_trial_map");
    cfg_td_trial_monster_cache: CfgCacheBase = new CfgCacheBase(cfg_td_trial_monster, "cfg_td_trial_monster");
    cfg_team_boss_cache: CfgCacheBase = new CfgCacheBase(cfg_team_boss, "cfg_team_boss");
    cfg_team_xswh_boss_cache: CfgCacheBase = new CfgCacheBase(cfg_team_xswh_boss, "cfg_team_xswh_boss");
    cfg_team_xswh_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_team_xswh_gift, "cfg_team_xswh_gift");
    cfg_team_xswh_hurt_rewards_cache: CfgCacheBase = new CfgCacheBase(cfg_team_xswh_hurt_rewards, "cfg_team_xswh_hurt_rewards");
    cfg_team_xswh_rank_rewards_cache: CfgCacheBase = new CfgCacheBase(cfg_team_xswh_rank_rewards, "cfg_team_xswh_rank_rewards");
    cfg_tequan_cache: CfgCacheBase = new CfgCacheBase(cfg_tequan, "cfg_tequan");
    cfg_test_tower_cache: CfgCacheBase = new CfgCacheBase(cfg_test_tower, "cfg_test_tower");
    cfg_test_tower_extra_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_test_tower_extra_reward, "cfg_test_tower_extra_reward");
    cfg_test_tower_skin_cache: CfgCacheBase = new CfgCacheBase(cfg_test_tower_skin, "cfg_test_tower_skin");
    cfg_theme_act_famous_lottery_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_theme_act_famous_lottery_reward, "cfg_theme_act_famous_lottery_reward");
    cfg_theme_act_hero_lottery_show_cache: CfgCacheBase = new CfgCacheBase(cfg_theme_act_hero_lottery_show, "cfg_theme_act_hero_lottery_show");
    cfg_theme_act_item_cache: CfgCacheBase = new CfgCacheBase(cfg_theme_act_item, "cfg_theme_act_item");
    cfg_theme_act_rare_lottery_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_theme_act_rare_lottery_reward, "cfg_theme_act_rare_lottery_reward");
    cfg_theme_act_skin_lottery_cache: CfgCacheBase = new CfgCacheBase(cfg_theme_act_skin_lottery, "cfg_theme_act_skin_lottery");
    cfg_theme_act_skin_lottery_cost_cache: CfgCacheBase = new CfgCacheBase(cfg_theme_act_skin_lottery_cost, "cfg_theme_act_skin_lottery_cost");
    cfg_theme_act_wish_lottery_cache: CfgCacheBase = new CfgCacheBase(cfg_theme_act_wish_lottery, "cfg_theme_act_wish_lottery");
    cfg_theme_act_wish_lottery_item_cache: CfgCacheBase = new CfgCacheBase(cfg_theme_act_wish_lottery_item, "cfg_theme_act_wish_lottery_item");
    cfg_theme_act_wish_lottery_show_cache: CfgCacheBase = new CfgCacheBase(cfg_theme_act_wish_lottery_show, "cfg_theme_act_wish_lottery_show");
    cfg_tiled_effect_cache: CfgCacheBase = new CfgCacheBase(cfg_tiled_effect, "cfg_tiled_effect");
    cfg_tiled_map_cache: CfgCacheBase = new CfgCacheBase(cfg_tiled_map, "cfg_tiled_map");
    cfg_time_achievement_cache: CfgCacheBase = new CfgCacheBase(cfg_time_achievement, "cfg_time_achievement");
    cfg_time_activity_drop_cache: CfgCacheBase = new CfgCacheBase(cfg_time_activity_drop, "cfg_time_activity_drop");
    cfg_time_activity_shop_cache: CfgCacheBase = new CfgCacheBase(cfg_time_activity_shop, "cfg_time_activity_shop");
    cfg_time_activity_week_cache: CfgCacheBase = new CfgCacheBase(cfg_time_activity_week, "cfg_time_activity_week");
    cfg_tips_cache: CfgCacheBase = new CfgCacheBase(cfg_tips, "cfg_tips");
    cfg_title_cache: CfgCacheBase = new CfgCacheBase(cfg_title, "cfg_title");
    cfg_travel_cache: CfgCacheBase = new CfgCacheBase(cfg_travel, "cfg_travel");
    cfg_travel_ext_cache: CfgCacheBase = new CfgCacheBase(cfg_travel_ext, "cfg_travel_ext");
    cfg_treasure_box_cache: CfgCacheBase = new CfgCacheBase(cfg_treasure_box, "cfg_treasure_box");
    cfg_treasure_box_type_cache: CfgCacheBase = new CfgCacheBase(cfg_treasure_box_type, "cfg_treasure_box_type");
    cfg_treasure_energy_cache: CfgCacheBase = new CfgCacheBase(cfg_treasure_energy, "cfg_treasure_energy");
    cfg_treasure_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_treasure_gift, "cfg_treasure_gift");
    cfg_treasure_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_treasure_misc, "cfg_treasure_misc");
    cfg_treasure_refresh_cost_cache: CfgCacheBase = new CfgCacheBase(cfg_treasure_refresh_cost, "cfg_treasure_refresh_cost");
    cfg_treasure_worker_cache: CfgCacheBase = new CfgCacheBase(cfg_treasure_worker, "cfg_treasure_worker");
    cfg_trig_skill_cache: CfgCacheBase = new CfgCacheBase(cfg_trig_skill, "cfg_trig_skill");
    cfg_ui_button_style_cache: CfgCacheBase = new CfgCacheBase(cfg_ui_button_style, "cfg_ui_button_style");
    cfg_ui_preload_cache: CfgCacheBase = new CfgCacheBase(cfg_ui_preload, "cfg_ui_preload");
    cfg_ui_resident_cache: CfgCacheBase = new CfgCacheBase(cfg_ui_resident, "cfg_ui_resident");
    cfg_up_star_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_up_star_gift, "cfg_up_star_gift");
    cfg_up_star_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_up_star_reward, "cfg_up_star_reward");
    cfg_vip_daily_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_vip_daily_mission, "cfg_vip_daily_mission");
    cfg_vip_daily_mission_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_vip_daily_mission_gift, "cfg_vip_daily_mission_gift");
    cfg_vip_kefu_cache: CfgCacheBase = new CfgCacheBase(cfg_vip_kefu, "cfg_vip_kefu");
    cfg_vip_kefu_review_cache: CfgCacheBase = new CfgCacheBase(cfg_vip_kefu_review, "cfg_vip_kefu_review");
    cfg_war_flag_cache: CfgCacheBase = new CfgCacheBase(cfg_war_flag, "cfg_war_flag");
    cfg_war_flag_facade_cache: CfgCacheBase = new CfgCacheBase(cfg_war_flag_facade, "cfg_war_flag_facade");
    cfg_war_flag_level_cache: CfgCacheBase = new CfgCacheBase(cfg_war_flag_level, "cfg_war_flag_level");
    cfg_war_flag_link_cache: CfgCacheBase = new CfgCacheBase(cfg_war_flag_link, "cfg_war_flag_link");
    cfg_war_flag_recycle_cache: CfgCacheBase = new CfgCacheBase(cfg_war_flag_recycle, "cfg_war_flag_recycle");
    cfg_war_flag_stage_cache: CfgCacheBase = new CfgCacheBase(cfg_war_flag_stage, "cfg_war_flag_stage");
    cfg_war_log_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_war_log_mission, "cfg_war_log_mission");
    cfg_war_log_mission_pay_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_war_log_mission_pay_reward, "cfg_war_log_mission_pay_reward");
    cfg_war_log_mission_score_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_war_log_mission_score_reward, "cfg_war_log_mission_score_reward");
    cfg_wars_honor_cache: CfgCacheBase = new CfgCacheBase(cfg_wars_honor, "cfg_wars_honor");
    cfg_wars_map_camp_cache: CfgCacheBase = new CfgCacheBase(cfg_wars_map_camp, "cfg_wars_map_camp");
    cfg_wars_map_city_cache: CfgCacheBase = new CfgCacheBase(cfg_wars_map_city, "cfg_wars_map_city");
    cfg_wars_map_near_city2_cache: CfgCacheBase = new CfgCacheBase(cfg_wars_map_near_city2, "cfg_wars_map_near_city2");
    cfg_wars_map_type_cache: CfgCacheBase = new CfgCacheBase(cfg_wars_map_type, "cfg_wars_map_type");
    cfg_wars_misc_cache: CfgCacheBase = new CfgCacheBase(cfg_wars_misc, "cfg_wars_misc");
    cfg_wars_mission_cache: CfgCacheBase = new CfgCacheBase(cfg_wars_mission, "cfg_wars_mission");
    cfg_wars_state_cache: CfgCacheBase = new CfgCacheBase(cfg_wars_state, "cfg_wars_state");
    cfg_wars_text_const_cache: CfgCacheBase = new CfgCacheBase(cfg_wars_text_const, "cfg_wars_text_const");
    cfg_week_target_cache: CfgCacheBase = new CfgCacheBase(cfg_week_target, "cfg_week_target");
    cfg_week_target_level_cache: CfgCacheBase = new CfgCacheBase(cfg_week_target_level, "cfg_week_target_level");
    cfg_wing_buff_cache: CfgCacheBase = new CfgCacheBase(cfg_wing_buff, "cfg_wing_buff");
    cfg_wing_hero_cache: CfgCacheBase = new CfgCacheBase(cfg_wing_hero, "cfg_wing_hero");
    cfg_wing_hero_skin_cache: CfgCacheBase = new CfgCacheBase(cfg_wing_hero_skin, "cfg_wing_hero_skin");
    cfg_wing_level_cache: CfgCacheBase = new CfgCacheBase(cfg_wing_level, "cfg_wing_level");
    cfg_world_boss_hurt_rewards_cache: CfgCacheBase = new CfgCacheBase(cfg_world_boss_hurt_rewards, "cfg_world_boss_hurt_rewards");
    cfg_world_boss_level_cache: CfgCacheBase = new CfgCacheBase(cfg_world_boss_level, "cfg_world_boss_level");
    cfg_world_map_cache: CfgCacheBase = new CfgCacheBase(cfg_world_map, "cfg_world_map");
    cfg_world_map_2_cache: CfgCacheBase = new CfgCacheBase(cfg_world_map_2, "cfg_world_map_2");
    cfg_wxShare_cache: CfgCacheBase = new CfgCacheBase(cfg_wxShare, "cfg_wxShare");
    cfg_wxTurn_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_wxTurn_gift, "cfg_wxTurn_gift");
    cfg_wx_game_club_cache: CfgCacheBase = new CfgCacheBase(cfg_wx_game_club, "cfg_wx_game_club");
    cfg_xswh_boss_cache: CfgCacheBase = new CfgCacheBase(cfg_xswh_boss, "cfg_xswh_boss");
    cfg_xswh_gift_cache: CfgCacheBase = new CfgCacheBase(cfg_xswh_gift, "cfg_xswh_gift");
    cfg_xswh_hurt_rewards_cache: CfgCacheBase = new CfgCacheBase(cfg_xswh_hurt_rewards, "cfg_xswh_hurt_rewards");
    cfg_xswh_rank_rewards_cache: CfgCacheBase = new CfgCacheBase(cfg_xswh_rank_rewards, "cfg_xswh_rank_rewards");
    cfg_ybzk_reward_cache: CfgCacheBase = new CfgCacheBase(cfg_ybzk_reward, "cfg_ybzk_reward");
    cfg_zero_buy_cache: CfgCacheBase = new CfgCacheBase(cfg_zero_buy, "cfg_zero_buy");
    errorCode_cache: CfgCacheBase = new CfgCacheBase(errorCode, "errorCode");
    fightAttr_cache: CfgCacheBase = new CfgCacheBase(fightAttr, "fightAttr");
    victoryMacro_cache: CfgCacheBase = new CfgCacheBase(victoryMacro, "victoryMacro");

    caches: {[name: string]: CfgCacheBase} = {};

    step_idx: number = 0
    step_names: string[] = [];
    private static STEP: number = 100;

    private reg(cache: CfgCacheBase): void {
        var name = cache.getCfgName();
        this.caches[name] = cache;
    }

    private get(name: string): CfgCacheBase {
        return this.caches[name];
    }

    private regAll(): void {
        this.reg(this.cfg_acc_gift_cache);
        this.reg(this.cfg_achievement_cache);
        this.reg(this.cfg_activity_client_cache);
        this.reg(this.cfg_activity_desc_cache);
        this.reg(this.cfg_activity_icon_cache);
        this.reg(this.cfg_activity_limit_sign_cache);
        this.reg(this.cfg_activity_misc_cache);
        this.reg(this.cfg_activity_mission_cache);
        this.reg(this.cfg_activity_notice_cache);
        this.reg(this.cfg_activity_page_cache);
        this.reg(this.cfg_activity_yueka_cache);
        this.reg(this.cfg_ad_show_cache);
        this.reg(this.cfg_agent_review_cache);
        this.reg(this.cfg_all_pinyin_dict_cache);
        this.reg(this.cfg_arena_cache);
        this.reg(this.cfg_arena_match_cache);
        this.reg(this.cfg_arena_match_guess_cache);
        this.reg(this.cfg_arena_max_reward_cache);
        this.reg(this.cfg_arena_rank_cache);
        this.reg(this.cfg_arena_reward_cache);
        this.reg(this.cfg_arena_skip_limit_cache);
        this.reg(this.cfg_arena_weekly_reward_cache);
        this.reg(this.cfg_ares_palace_cache);
        this.reg(this.cfg_authorized_gifts_cache);
        this.reg(this.cfg_bag_page_cache);
        this.reg(this.cfg_bai_jiang_gift_cache);
        this.reg(this.cfg_battle_fly_name_cache);
        this.reg(this.cfg_battle_trial_buff_cache);
        this.reg(this.cfg_battle_trial_buff_reset_cache);
        this.reg(this.cfg_battle_trial_guaji_monster_cache);
        this.reg(this.cfg_battle_trial_misc_cache);
        this.reg(this.cfg_battle_trial_pass_guanqia_cache);
        this.reg(this.cfg_battle_trial_pass_reward_cache);
        this.reg(this.cfg_beast_platform_cache);
        this.reg(this.cfg_bingfa_cache);
        this.reg(this.cfg_bingfa_ext_cache);
        this.reg(this.cfg_bingfu_cache);
        this.reg(this.cfg_bingfu_discompose_recast_cache);
        this.reg(this.cfg_bingfu_recast_lock_cache);
        this.reg(this.cfg_bingfu_refine_cache);
        this.reg(this.cfg_bingfu_upgrade_cache);
        this.reg(this.cfg_boat_peak_misc_cache);
        this.reg(this.cfg_boat_peak_rank_cache);
        this.reg(this.cfg_boat_peak_time_cache);
        this.reg(this.cfg_boat_racc_rank_gold_cache);
        this.reg(this.cfg_boat_race_auction_reward_type_cache);
        this.reg(this.cfg_boat_race_item_cache);
        this.reg(this.cfg_boat_race_misc_cache);
        this.reg(this.cfg_buff_cache);
        this.reg(this.cfg_buff_type_cache);
        this.reg(this.cfg_building_cache);
        this.reg(this.cfg_building_lv_cache);
        this.reg(this.cfg_building_mission_cache);
        this.reg(this.cfg_building_plot_dialogue_cache);
        this.reg(this.cfg_buy_times_cache);
        this.reg(this.cfg_buy_times_type_cache);
        this.reg(this.cfg_cast_soul_active_cache);
        this.reg(this.cfg_casting_soul_cache);
        this.reg(this.cfg_casting_soul_buff_cache);
        this.reg(this.cfg_chapter_dialog_cache);
        this.reg(this.cfg_chapter_script_cache);
        this.reg(this.cfg_chat_bullet_msg_cache);
        this.reg(this.cfg_chat_channel_cache);
        this.reg(this.cfg_chat_skin_cache);
        this.reg(this.cfg_chat_skin_widget_cache);
        this.reg(this.cfg_client_lang_cache);
        this.reg(this.cfg_client_w3_effect_cache);
        this.reg(this.cfg_client_w3_effect_desc_cache);
        this.reg(this.cfg_client_w3_skill_cache);
        this.reg(this.cfg_client_w3_skill_desc_cache);
        this.reg(this.cfg_client_w3_skin_cache);
        this.reg(this.cfg_cmd_cache);
        this.reg(this.cfg_code_cli_cache);
        this.reg(this.cfg_country_war_misc_cache);
        this.reg(this.cfg_cross_team_match_type_cache);
        this.reg(this.cfg_cross_team_misc_cache);
        this.reg(this.cfg_cross_test_tower_open_cache);
        this.reg(this.cfg_crush_fight_condition_cache);
        this.reg(this.cfg_crush_fight_mission_cache);
        this.reg(this.cfg_crush_fight_mission_type_cache);
        this.reg(this.cfg_csc_fmsolo_buff_cache);
        this.reg(this.cfg_csc_fmsolo_challenge_cache);
        this.reg(this.cfg_csc_fmsolo_etc_cache);
        this.reg(this.cfg_csc_fmsolo_log_cache);
        this.reg(this.cfg_csc_fmsolo_reward_cache);
        this.reg(this.cfg_csc_fmsolo_shop_lv_cache);
        this.reg(this.cfg_csclan_cache);
        this.reg(this.cfg_csclan_etc_cache);
        this.reg(this.cfg_csclan_solo_buff_cache);
        this.reg(this.cfg_csclan_solo_challenge_cache);
        this.reg(this.cfg_csclan_solo_etc_cache);
        this.reg(this.cfg_csclan_solo_log_cache);
        this.reg(this.cfg_csclan_solo_reward_cache);
        this.reg(this.cfg_csclan_solo_shop_lv_cache);
        this.reg(this.cfg_daily_copy_cache);
        this.reg(this.cfg_daily_copy_discount_cache);
        this.reg(this.cfg_daily_copy_monster_cache);
        this.reg(this.cfg_daily_copy_type_cache);
        this.reg(this.cfg_daily_mission_cache);
        this.reg(this.cfg_daily_mission_gift_cache);
        this.reg(this.cfg_daily_new_discount_rebate_cache);
        this.reg(this.cfg_daily_pay_cache);
        this.reg(this.cfg_dawanka_cache);
        this.reg(this.cfg_dawanka_tequan_cache);
        this.reg(this.cfg_day_acc_pay_gift_cache);
        this.reg(this.cfg_deputy_cache);
        this.reg(this.cfg_deputy_level_cache);
        this.reg(this.cfg_deputy_star_cache);
        this.reg(this.cfg_device_excursion_cache);
        this.reg(this.cfg_divine_cache);
        this.reg(this.cfg_divine_activate_cache);
        this.reg(this.cfg_divine_copy_cache);
        this.reg(this.cfg_divine_misc_cache);
        this.reg(this.cfg_divine_strengthen_cache);
        this.reg(this.cfg_divine_wear_cache);
        this.reg(this.cfg_dominate_pvp_cache);
        this.reg(this.cfg_dominate_pvp_limit_cache);
        this.reg(this.cfg_dominate_pvp_reward_cache);
        this.reg(this.cfg_dominate_pvp_task_cache);
        this.reg(this.cfg_dominate_pvp_task_rewards_cache);
        this.reg(this.cfg_drop_group_cache);
        this.reg(this.cfg_eight_login_cache);
        this.reg(this.cfg_epic_battle_tips_cache);
        this.reg(this.cfg_equip_cache);
        this.reg(this.cfg_equip_compose_cache);
        this.reg(this.cfg_equip_suit_cache);
        this.reg(this.cfg_equip_suit_ext_cache);
        this.reg(this.cfg_fail_tips_cache);
        this.reg(this.cfg_family_cache);
        this.reg(this.cfg_family_active_attr_cache);
        this.reg(this.cfg_family_active_mission_cache);
        this.reg(this.cfg_family_boss_cache);
        this.reg(this.cfg_family_boss_attr_cache);
        this.reg(this.cfg_family_boss_misc_cache);
        this.reg(this.cfg_family_boss_rank_cache);
        this.reg(this.cfg_family_etc_cache);
        this.reg(this.cfg_family_hongbao_cache);
        this.reg(this.cfg_family_hongbao_blessing_cache);
        this.reg(this.cfg_family_hongbao_reward_cache);
        this.reg(this.cfg_family_log_cache);
        this.reg(this.cfg_family_random_name_cache);
        this.reg(this.cfg_family_science_cache);
        this.reg(this.cfg_family_science_times_cache);
        this.reg(this.cfg_family_sign_cache);
        this.reg(this.cfg_family_sign_active_cache);
        this.reg(this.cfg_fight_show_cache);
        this.reg(this.cfg_first_pay_cache);
        this.reg(this.cfg_fish_cache);
        this.reg(this.cfg_fish_attr_cache);
        this.reg(this.cfg_fish_color_cache);
        this.reg(this.cfg_fish_drum_level_cache);
        this.reg(this.cfg_fish_fishbowl_cache);
        this.reg(this.cfg_fish_handbook_cache);
        this.reg(this.cfg_fish_help_gift_cache);
        this.reg(this.cfg_fish_level_cache);
        this.reg(this.cfg_fish_map_cache);
        this.reg(this.cfg_fish_misc_cache);
        this.reg(this.cfg_fish_mission_cache);
        this.reg(this.cfg_fish_official_cache);
        this.reg(this.cfg_fish_official_position_cache);
        this.reg(this.cfg_fish_reshape_cache);
        this.reg(this.cfg_fish_resources_cache);
        this.reg(this.cfg_fish_rod_level_cache);
        this.reg(this.cfg_fish_slot_cache);
        this.reg(this.cfg_fish_stage_cache);
        this.reg(this.cfg_fly_font_type_cache);
        this.reg(this.cfg_fuli_sign_cache);
        this.reg(this.cfg_fuli_sign_acc_cache);
        this.reg(this.cfg_fuli_sign_day_cache);
        this.reg(this.cfg_fuli_token_cache);
        this.reg(this.cfg_fuli_token_type_cache);
        this.reg(this.cfg_fuli_yueka_cache);
        this.reg(this.cfg_game_desc_cache);
        this.reg(this.cfg_game_desc_2_cache);
        this.reg(this.cfg_game_desc_3_cache);
        this.reg(this.cfg_general_pass_mission_cache);
        this.reg(this.cfg_general_pass_reward_cache);
        this.reg(this.cfg_general_pass_type_cache);
        this.reg(this.cfg_gift_cache);
        this.reg(this.cfg_god_equip_cache);
        this.reg(this.cfg_god_equip_compose_cache);
        this.reg(this.cfg_god_equip_convert_cache);
        this.reg(this.cfg_god_equip_enchant_cache);
        this.reg(this.cfg_god_equip_enchant_cost_cache);
        this.reg(this.cfg_god_equip_suit_cache);
        this.reg(this.cfg_god_equip_type_cache);
        this.reg(this.cfg_god_trial_cache);
        this.reg(this.cfg_god_trial_buff_cache);
        this.reg(this.cfg_god_weapon_cache);
        this.reg(this.cfg_god_weapon_level_cache);
        this.reg(this.cfg_god_weapon_mission_cache);
        this.reg(this.cfg_god_weapon_refine_cache);
        this.reg(this.cfg_god_weapon_skill_cache);
        this.reg(this.cfg_god_weapon_skill_attr_cache);
        this.reg(this.cfg_god_weapon_soul_cache);
        this.reg(this.cfg_god_weapon_star_cache);
        this.reg(this.cfg_gray_pinyin_cache);
        this.reg(this.cfg_grow_tips_cache);
        this.reg(this.cfg_guaji_box_time_cache);
        this.reg(this.cfg_guaji_monster_cache);
        this.reg(this.cfg_guaji_quick_navigation_cache);
        this.reg(this.cfg_guandu_cache);
        this.reg(this.cfg_guandu_answer_cache);
        this.reg(this.cfg_guandu_chose_cache);
        this.reg(this.cfg_guandu_floor_cache);
        this.reg(this.cfg_guandu_mission_cache);
        this.reg(this.cfg_guandu_shop_cache);
        this.reg(this.cfg_guide_helper_cache);
        this.reg(this.cfg_guide_helper_1_cache);
        this.reg(this.cfg_guide_helper_2_cache);
        this.reg(this.cfg_guide_helper_3_cache);
        this.reg(this.cfg_guide_helper_4_cache);
        this.reg(this.cfg_guide_helper_debug_cache);
        this.reg(this.cfg_guide_helper_game_1_cache);
        this.reg(this.cfg_guide_helper_m2_cache);
        this.reg(this.cfg_guide_helper_m3_cache);
        this.reg(this.cfg_guide_mission_cache);
        this.reg(this.cfg_guide_mission_1_cache);
        this.reg(this.cfg_guide_mission_2_cache);
        this.reg(this.cfg_guide_mission_3_cache);
        this.reg(this.cfg_guide_mission_4_cache);
        this.reg(this.cfg_guide_mission_game_1_cache);
        this.reg(this.cfg_guide_mission_m2_cache);
        this.reg(this.cfg_guide_mission_m3_cache);
        this.reg(this.cfg_guide_review_cache);
        this.reg(this.cfg_guide_story_cache);
        this.reg(this.cfg_hero_attr_addition_cache);
        this.reg(this.cfg_hero_attr_source_cache);
        this.reg(this.cfg_hero_bag_cache);
        this.reg(this.cfg_hero_base_cache);
        this.reg(this.cfg_hero_cheer_cache);
        this.reg(this.cfg_hero_cheer_bonus_cache);
        this.reg(this.cfg_hero_cheer_level_cache);
        this.reg(this.cfg_hero_cheer_unlock_cache);
        this.reg(this.cfg_hero_chip_star_cache);
        this.reg(this.cfg_hero_come_mission_cache);
        this.reg(this.cfg_hero_convert_cache);
        this.reg(this.cfg_hero_convert_weight_cache);
        this.reg(this.cfg_hero_cost_plan_cache);
        this.reg(this.cfg_hero_evolve_skill_cache);
        this.reg(this.cfg_hero_handbook_desc_cache);
        this.reg(this.cfg_hero_level_cache);
        this.reg(this.cfg_hero_level_limit_cache);
        this.reg(this.cfg_hero_nation_cache);
        this.reg(this.cfg_hero_pass_mission_cache);
        this.reg(this.cfg_hero_pass_reward_cache);
        this.reg(this.cfg_hero_recommend_pre_cache);
        this.reg(this.cfg_hero_recycle_cache);
        this.reg(this.cfg_hero_recycle_change_cache);
        this.reg(this.cfg_hero_recycle_change_star_stage_cache);
        this.reg(this.cfg_hero_recycle_res_cache);
        this.reg(this.cfg_hero_recycle_special_switch_cache);
        this.reg(this.cfg_hero_resonate_dhyana_cache);
        this.reg(this.cfg_hero_resonate_dudu_cache);
        this.reg(this.cfg_hero_resonate_dudu_level_cache);
        this.reg(this.cfg_hero_resonate_five_cache);
        this.reg(this.cfg_hero_skin_cache);
        this.reg(this.cfg_hero_skin_level_cache);
        this.reg(this.cfg_hero_stage_cache);
        this.reg(this.cfg_hero_stage_limit_cache);
        this.reg(this.cfg_hero_star_cache);
        this.reg(this.cfg_hero_star_attr_cache);
        this.reg(this.cfg_hero_star_limit_cache);
        this.reg(this.cfg_hero_star_stage_cache);
        this.reg(this.cfg_hero_star_stage_attr_cache);
        this.reg(this.cfg_hero_strengthen_cache);
        this.reg(this.cfg_hero_upgrade_tips_cache);
        this.reg(this.cfg_hero_zhouyin_cache);
        this.reg(this.cfg_hunt_buy_cache);
        this.reg(this.cfg_hunt_cost_cache);
        this.reg(this.cfg_hunt_desc_cache);
        this.reg(this.cfg_hunt_gift_cache);
        this.reg(this.cfg_hunt_rewards_show_cache);
        this.reg(this.cfg_huoqutujing_cache);
        this.reg(this.cfg_hzzd_achievement_cache);
        this.reg(this.cfg_hzzd_event_cache);
        this.reg(this.cfg_hzzd_kills_reward_cache);
        this.reg(this.cfg_hzzd_misc_cache);
        this.reg(this.cfg_i18n_lang_cache);
        this.reg(this.cfg_i18n_ts_cache);
        this.reg(this.cfg_i18n_ui_cache);
        this.reg(this.cfg_ingenious_plan_cache);
        this.reg(this.cfg_ingenious_plan_compose_cache);
        this.reg(this.cfg_ingenious_plan_convert_cache);
        this.reg(this.cfg_ingenious_plan_level_cache);
        this.reg(this.cfg_ingenious_plan_stage_cache);
        this.reg(this.cfg_ingenious_plan_star_cache);
        this.reg(this.cfg_ip_set_cache);
        this.reg(this.cfg_item_cache);
        this.reg(this.cfg_item_compose_cache);
        this.reg(this.cfg_item_time_client_cache);
        this.reg(this.cfg_large_peak_agent_cache);
        this.reg(this.cfg_large_peak_battle_table_cache);
        this.reg(this.cfg_large_peak_misc_cache);
        this.reg(this.cfg_large_peak_rank_cache);
        this.reg(this.cfg_large_peak_season_cache);
        this.reg(this.cfg_large_peak_time_cache);
        this.reg(this.cfg_lazy_load_cache);
        this.reg(this.cfg_lcqs_acc_star_reward_cache);
        this.reg(this.cfg_lcqs_chapter_open_cache);
        this.reg(this.cfg_lcqs_floor_reward_cache);
        this.reg(this.cfg_lcqs_mission_cache);
        this.reg(this.cfg_level_cache);
        this.reg(this.cfg_level_gift_cache);
        this.reg(this.cfg_limit_hero_skin_chip_exchange_cache);
        this.reg(this.cfg_lineup_buff_cache);
        this.reg(this.cfg_lineup_buff_icon_cache);
        this.reg(this.cfg_lineup_career_rule_cache);
        this.reg(this.cfg_lineup_num_cache);
        this.reg(this.cfg_lineup_recommend_cache);
        this.reg(this.cfg_lineup_style_cache);
        this.reg(this.cfg_load_tips_cache);
        this.reg(this.cfg_login_activity_cache);
        this.reg(this.cfg_login_activity_round_cache);
        this.reg(this.cfg_lord_activation_cache);
        this.reg(this.cfg_lord_base_cache);
        this.reg(this.cfg_lord_camp_cache);
        this.reg(this.cfg_lord_exchange_cache);
        this.reg(this.cfg_lord_skill_cache);
        this.reg(this.cfg_lord_skill_enhance_cache);
        this.reg(this.cfg_lord_star_cache);
        this.reg(this.cfg_lord_suit_compose_cache);
        this.reg(this.cfg_lord_suit_select_cache);
        this.reg(this.cfg_lord_treasure_cache);
        this.reg(this.cfg_lord_treasure_entry_cache);
        this.reg(this.cfg_lord_treasure_forge_cache);
        this.reg(this.cfg_lord_treasure_level_cache);
        this.reg(this.cfg_lottery_day_limit_cache);
        this.reg(this.cfg_lottery_ext_cache);
        this.reg(this.cfg_lottery_nation_cache);
        this.reg(this.cfg_lottery_nation_times_cache);
        this.reg(this.cfg_lottery_score_cache);
        this.reg(this.cfg_lottery_show_cache);
        this.reg(this.cfg_lottery_times_cache);
        this.reg(this.cfg_lottery_weight_cache);
        this.reg(this.cfg_main_battle_cache);
        this.reg(this.cfg_main_battle_box_cache);
        this.reg(this.cfg_main_battle_box_level_cache);
        this.reg(this.cfg_main_battle_box_reward_cache);
        this.reg(this.cfg_main_battle_box_tequan_cache);
        this.reg(this.cfg_main_battle_fetch_cache);
        this.reg(this.cfg_main_battle_hanging_cache);
        this.reg(this.cfg_main_battle_mission_cache);
        this.reg(this.cfg_map_cache);
        this.reg(this.cfg_map_item_cache);
        this.reg(this.cfg_map_item_type_cache);
        this.reg(this.cfg_map_type_cache);
        this.reg(this.cfg_master_card_cache);
        this.reg(this.cfg_master_card_attr_cache);
        this.reg(this.cfg_master_card_color_cache);
        this.reg(this.cfg_master_card_drum_level_cache);
        this.reg(this.cfg_master_card_first_pay_cache);
        this.reg(this.cfg_master_card_help_gift_cache);
        this.reg(this.cfg_master_card_misc_cache);
        this.reg(this.cfg_master_card_mission_cache);
        this.reg(this.cfg_master_card_official_cache);
        this.reg(this.cfg_master_card_official_position_cache);
        this.reg(this.cfg_master_card_reshape_cache);
        this.reg(this.cfg_master_card_slot_cache);
        this.reg(this.cfg_master_card_stage_cache);
        this.reg(this.cfg_master_talent_science_cache);
        this.reg(this.cfg_master_talent_science_icon_cache);
        this.reg(this.cfg_match_type_cache);
        this.reg(this.cfg_match_type_team_cache);
        this.reg(this.cfg_maze_cache);
        this.reg(this.cfg_maze_diff_rewards_cache);
        this.reg(this.cfg_maze_mission_spoils_cache);
        this.reg(this.cfg_maze_monster_cache);
        this.reg(this.cfg_maze_reset_cache);
        this.reg(this.cfg_maze_revive_cache);
        this.reg(this.cfg_maze_shop_cache);
        this.reg(this.cfg_maze_theme_cache);
        this.reg(this.cfg_medal_cache);
        this.reg(this.cfg_microterminal_open_cache);
        this.reg(this.cfg_microterminal_sign_cache);
        this.reg(this.cfg_misc_config_cache);
        this.reg(this.cfg_mission_shop_cache);
        this.reg(this.cfg_mission_shop_client_cache);
        this.reg(this.cfg_mock_battle_hero_base_cache);
        this.reg(this.cfg_mock_pvp_database_cache);
        this.reg(this.cfg_mock_pvp_hero_base_cache);
        this.reg(this.cfg_mock_pvp_limit_cache);
        this.reg(this.cfg_mock_pvp_misc_cache);
        this.reg(this.cfg_mock_pvp_mission_cache);
        this.reg(this.cfg_modular_activity_bless_cache);
        this.reg(this.cfg_modular_activity_brick_cache);
        this.reg(this.cfg_modular_activity_carnival_link_cache);
        this.reg(this.cfg_modular_activity_client_setting_cache);
        this.reg(this.cfg_modular_activity_compose_list_cache);
        this.reg(this.cfg_modular_activity_customized_gift_cache);
        this.reg(this.cfg_modular_activity_dice_cache);
        this.reg(this.cfg_modular_activity_dice_boss_cache);
        this.reg(this.cfg_modular_activity_dice_client_diff_cache);
        this.reg(this.cfg_modular_activity_dice_misc_cache);
        this.reg(this.cfg_modular_activity_drop_cache);
        this.reg(this.cfg_modular_activity_drop_show_cache);
        this.reg(this.cfg_modular_activity_exchange_cache);
        this.reg(this.cfg_modular_activity_festival_wish_cache);
        this.reg(this.cfg_modular_activity_festival_wish_choose_cache);
        this.reg(this.cfg_modular_activity_festival_wish_cost_cache);
        this.reg(this.cfg_modular_activity_free_switch_cache);
        this.reg(this.cfg_modular_activity_general_pass_vip_cache);
        this.reg(this.cfg_modular_activity_hero_challenge_cache);
        this.reg(this.cfg_modular_activity_holiday_welfare_reward_cache);
        this.reg(this.cfg_modular_activity_hunt_cost_cache);
        this.reg(this.cfg_modular_activity_hunt_desc_cache);
        this.reg(this.cfg_modular_activity_hunt_misc_cache);
        this.reg(this.cfg_modular_activity_huoqutujing_cache);
        this.reg(this.cfg_modular_activity_icon_cache);
        this.reg(this.cfg_modular_activity_login_cache);
        this.reg(this.cfg_modular_activity_lottery_target_cache);
        this.reg(this.cfg_modular_activity_lottery_times_cache);
        this.reg(this.cfg_modular_activity_lucky_bag_cache);
        this.reg(this.cfg_modular_activity_mission_cache);
        this.reg(this.cfg_modular_activity_open_cache);
        this.reg(this.cfg_modular_activity_open_preview_cache);
        this.reg(this.cfg_modular_activity_pay_welfare_cache);
        this.reg(this.cfg_modular_activity_payment_shop_item_cache);
        this.reg(this.cfg_modular_activity_payment_shop_item_show_cache);
        this.reg(this.cfg_modular_activity_preview_cache);
        this.reg(this.cfg_modular_activity_preview_rewards_cache);
        this.reg(this.cfg_modular_activity_rank_cache);
        this.reg(this.cfg_modular_activity_rank_reward_cache);
        this.reg(this.cfg_modular_activity_round_mission_cache);
        this.reg(this.cfg_modular_activity_round_mission_reward_cache);
        this.reg(this.cfg_modular_activity_shop_client_cache);
        this.reg(this.cfg_modular_activity_sign_cache);
        this.reg(this.cfg_modular_activity_six_bless_cache);
        this.reg(this.cfg_modular_activity_star_plan_hero_cache);
        this.reg(this.cfg_modular_activity_star_plan_reward_cache);
        this.reg(this.cfg_modular_activity_story_cache);
        this.reg(this.cfg_modular_activity_story_chapter_cache);
        this.reg(this.cfg_modular_activity_story_chapter_map_cache);
        this.reg(this.cfg_modular_activity_story_dialogue_cache);
        this.reg(this.cfg_modular_activity_sub_type_cache);
        this.reg(this.cfg_modular_activity_target_cache);
        this.reg(this.cfg_modular_activity_time_item_cache);
        this.reg(this.cfg_modular_activity_wall_cache);
        this.reg(this.cfg_modular_activity_war_log_acc_reward_cache);
        this.reg(this.cfg_modular_activity_war_log_mission_cache);
        this.reg(this.cfg_modular_activity_weekly_card_reward_cache);
        this.reg(this.cfg_money_cache);
        this.reg(this.cfg_monster_cache);
        this.reg(this.cfg_monster_group_cache);
        this.reg(this.cfg_monster_skill_tier_cache);
        this.reg(this.cfg_monster_tips_cache);
        this.reg(this.cfg_month_fund_cache);
        this.reg(this.cfg_month_fund_type_cache);
        this.reg(this.cfg_music_cache);
        this.reg(this.cfg_nation_tower_lineup_cache);
        this.reg(this.cfg_nation_tower_open_cache);
        this.reg(this.cfg_noob_pay_cache);
        this.reg(this.cfg_online_reward_cache);
        this.reg(this.cfg_pass_behead_cache);
        this.reg(this.cfg_pass_behead_box_cache);
        this.reg(this.cfg_pass_behead_guanqia_cache);
        this.reg(this.cfg_pass_behead_revive_cache);
        this.reg(this.cfg_pass_check_mission_cache);
        this.reg(this.cfg_pass_check_reward_cache);
        this.reg(this.cfg_pass_check_vip_cache);
        this.reg(this.cfg_pay_vip_cache);
        this.reg(this.cfg_pay_vip_privilege_cache);
        this.reg(this.cfg_pay_vip_privilege_function_cache);
        this.reg(this.cfg_payment_shop_item_cache);
        this.reg(this.cfg_payment_shop_link_cache);
        this.reg(this.cfg_payment_time_gift_cache);
        this.reg(this.cfg_peak_misc_cache);
        this.reg(this.cfg_peak_time_cache);
        this.reg(this.cfg_peerless_act_hero_gift_cache);
        this.reg(this.cfg_platform_ad_id_misc_cache);
        this.reg(this.cfg_player_strategy_cache);
        this.reg(this.cfg_playing_preview_reward_cache);
        this.reg(this.cfg_progress_gift_cache);
        this.reg(this.cfg_pull_words_cache);
        this.reg(this.cfg_pvp_map_cache);
        this.reg(this.cfg_qq_group_cache);
        this.reg(this.cfg_qq_vip_cache);
        this.reg(this.cfg_qxzl_misc_cache);
        this.reg(this.cfg_random_box_cache);
        this.reg(this.cfg_random_pvp_cache);
        this.reg(this.cfg_random_pvp_head_frame_cache);
        this.reg(this.cfg_random_pvp_limit_cache);
        this.reg(this.cfg_random_pvp_reward_cache);
        this.reg(this.cfg_random_pvp_task_cache);
        this.reg(this.cfg_random_pvp_task_rewards_cache);
        this.reg(this.cfg_rank_desc_cache);
        this.reg(this.cfg_rank_mission_cache);
        this.reg(this.cfg_rank_rewards_cache);
        this.reg(this.cfg_rank_worship_cache);
        this.reg(this.cfg_red_cliff_cache);
        this.reg(this.cfg_red_cliff_boss_cache);
        this.reg(this.cfg_red_cliff_open_cache);
        this.reg(this.cfg_red_cliff_refresh_cache);
        this.reg(this.cfg_red_cliff_review_cache);
        this.reg(this.cfg_retrieval_cache);
        this.reg(this.cfg_river_text_const_cache);
        this.reg(this.cfg_role_profile_cache);
        this.reg(this.cfg_san_xiao_actor_cache);
        this.reg(this.cfg_san_xiao_guanqia_cache);
        this.reg(this.cfg_san_xiao_item_cache);
        this.reg(this.cfg_san_xiao_level_cache);
        this.reg(this.cfg_san_xiao_map_cache);
        this.reg(this.cfg_san_xiao_misc_cache);
        this.reg(this.cfg_scene_cache);
        this.reg(this.cfg_sdk_concern_reward_cache);
        this.reg(this.cfg_sdk_platform_desc_cache);
        this.reg(this.cfg_sdk_rewards_cache);
        this.reg(this.cfg_select_box_cache);
        this.reg(this.cfg_seven_goal_cache);
        this.reg(this.cfg_seven_goal_gift_cache);
        this.reg(this.cfg_seven_goal_mission_cache);
        this.reg(this.cfg_share_cycle_reward_cache);
        this.reg(this.cfg_share_daily_reward_cache);
        this.reg(this.cfg_share_level_reward_cache);
        this.reg(this.cfg_shili_preview_cache);
        this.reg(this.cfg_shop_cache);
        this.reg(this.cfg_shop_item_cache);
        this.reg(this.cfg_shop_item_tips_cache);
        this.reg(this.cfg_shop_reset_times_cache);
        this.reg(this.cfg_shop_shortcut_cache);
        this.reg(this.cfg_shop_tab_limit_cache);
        this.reg(this.cfg_show_off_cache);
        this.reg(this.cfg_skeleton_adaptive_cache);
        this.reg(this.cfg_skill_cache);
        this.reg(this.cfg_skill_effect_cache);
        this.reg(this.cfg_skill_event_cache);
        this.reg(this.cfg_skill_level_cache);
        this.reg(this.cfg_skill_summon_cache);
        this.reg(this.cfg_small_game_cache);
        this.reg(this.cfg_soldier_game_cache);
        this.reg(this.cfg_soldier_game_rewards_cache);
        this.reg(this.cfg_soul_hero_link_level_cache);
        this.reg(this.cfg_soul_hero_link_limit_unlock_cache);
        this.reg(this.cfg_soul_hero_link_nation_cache);
        this.reg(this.cfg_stage_breed_cache);
        this.reg(this.cfg_stage_breed_attr_cache);
        this.reg(this.cfg_stage_copy_cache);
        this.reg(this.cfg_stage_copy_boss_cache);
        this.reg(this.cfg_stage_copy_daily_mission_cache);
        this.reg(this.cfg_stage_copy_misc_cache);
        this.reg(this.cfg_stage_copy_story_cache);
        this.reg(this.cfg_stage_map_cache);
        this.reg(this.cfg_stage_mission_cache);
        this.reg(this.cfg_stage_skill_attr_cache);
        this.reg(this.cfg_stage_skill_type_cache);
        this.reg(this.cfg_star_plan_gift_cache);
        this.reg(this.cfg_star_plan_hero_cache);
        this.reg(this.cfg_star_plan_reward_cache);
        this.reg(this.cfg_story_cache);
        this.reg(this.cfg_story_action_cache);
        this.reg(this.cfg_story_actor_cache);
        this.reg(this.cfg_story_bubble_cache);
        this.reg(this.cfg_story_maze_cache);
        this.reg(this.cfg_story_maze_mission_spoils_cache);
        this.reg(this.cfg_story_maze_monster_cache);
        this.reg(this.cfg_story_maze_reset_cache);
        this.reg(this.cfg_story_maze_revive_cache);
        this.reg(this.cfg_story_maze_rewards_cache);
        this.reg(this.cfg_story_maze_shop_cache);
        this.reg(this.cfg_story_maze_theme_cache);
        this.reg(this.cfg_story_siegelord_city_type_cache);
        this.reg(this.cfg_story_siegelord_level_cache);
        this.reg(this.cfg_story_siegelord_level_reward_cache);
        this.reg(this.cfg_story_siegelord_misc_cache);
        this.reg(this.cfg_story_siegelord_pass_reward_cache);
        this.reg(this.cfg_story_tower_battle_cache);
        this.reg(this.cfg_story_tower_battle_monster_cache);
        this.reg(this.cfg_story_tower_battle_reward_cache);
        this.reg(this.cfg_suit_attr_cache);
        this.reg(this.cfg_supreme_lottery_cache);
        this.reg(this.cfg_svip_pay_gift_cache);
        this.reg(this.cfg_sweapon_stage_cache);
        this.reg(this.cfg_sys_open_notice_cache);
        this.reg(this.cfg_sys_openlv_cache);
        this.reg(this.cfg_sys_use_times_cache);
        this.reg(this.cfg_tax_cache);
        this.reg(this.cfg_tax_reward_cache);
        this.reg(this.cfg_td_main_cache);
        this.reg(this.cfg_td_main_mission_cache);
        this.reg(this.cfg_td_main_monster_cache);
        this.reg(this.cfg_td_main_pass_mission_cache);
        this.reg(this.cfg_td_map_cache);
        this.reg(this.cfg_td_monster_talk_cache);
        this.reg(this.cfg_td_trial_cache);
        this.reg(this.cfg_td_trial_map_cache);
        this.reg(this.cfg_td_trial_monster_cache);
        this.reg(this.cfg_team_boss_cache);
        this.reg(this.cfg_team_xswh_boss_cache);
        this.reg(this.cfg_team_xswh_gift_cache);
        this.reg(this.cfg_team_xswh_hurt_rewards_cache);
        this.reg(this.cfg_team_xswh_rank_rewards_cache);
        this.reg(this.cfg_tequan_cache);
        this.reg(this.cfg_test_tower_cache);
        this.reg(this.cfg_test_tower_extra_reward_cache);
        this.reg(this.cfg_test_tower_skin_cache);
        this.reg(this.cfg_theme_act_famous_lottery_reward_cache);
        this.reg(this.cfg_theme_act_hero_lottery_show_cache);
        this.reg(this.cfg_theme_act_item_cache);
        this.reg(this.cfg_theme_act_rare_lottery_reward_cache);
        this.reg(this.cfg_theme_act_skin_lottery_cache);
        this.reg(this.cfg_theme_act_skin_lottery_cost_cache);
        this.reg(this.cfg_theme_act_wish_lottery_cache);
        this.reg(this.cfg_theme_act_wish_lottery_item_cache);
        this.reg(this.cfg_theme_act_wish_lottery_show_cache);
        this.reg(this.cfg_tiled_effect_cache);
        this.reg(this.cfg_tiled_map_cache);
        this.reg(this.cfg_time_achievement_cache);
        this.reg(this.cfg_time_activity_drop_cache);
        this.reg(this.cfg_time_activity_shop_cache);
        this.reg(this.cfg_time_activity_week_cache);
        this.reg(this.cfg_tips_cache);
        this.reg(this.cfg_title_cache);
        this.reg(this.cfg_travel_cache);
        this.reg(this.cfg_travel_ext_cache);
        this.reg(this.cfg_treasure_box_cache);
        this.reg(this.cfg_treasure_box_type_cache);
        this.reg(this.cfg_treasure_energy_cache);
        this.reg(this.cfg_treasure_gift_cache);
        this.reg(this.cfg_treasure_misc_cache);
        this.reg(this.cfg_treasure_refresh_cost_cache);
        this.reg(this.cfg_treasure_worker_cache);
        this.reg(this.cfg_trig_skill_cache);
        this.reg(this.cfg_ui_button_style_cache);
        this.reg(this.cfg_ui_preload_cache);
        this.reg(this.cfg_ui_resident_cache);
        this.reg(this.cfg_up_star_gift_cache);
        this.reg(this.cfg_up_star_reward_cache);
        this.reg(this.cfg_vip_daily_mission_cache);
        this.reg(this.cfg_vip_daily_mission_gift_cache);
        this.reg(this.cfg_vip_kefu_cache);
        this.reg(this.cfg_vip_kefu_review_cache);
        this.reg(this.cfg_war_flag_cache);
        this.reg(this.cfg_war_flag_facade_cache);
        this.reg(this.cfg_war_flag_level_cache);
        this.reg(this.cfg_war_flag_link_cache);
        this.reg(this.cfg_war_flag_recycle_cache);
        this.reg(this.cfg_war_flag_stage_cache);
        this.reg(this.cfg_war_log_mission_cache);
        this.reg(this.cfg_war_log_mission_pay_reward_cache);
        this.reg(this.cfg_war_log_mission_score_reward_cache);
        this.reg(this.cfg_wars_honor_cache);
        this.reg(this.cfg_wars_map_camp_cache);
        this.reg(this.cfg_wars_map_city_cache);
        this.reg(this.cfg_wars_map_near_city2_cache);
        this.reg(this.cfg_wars_map_type_cache);
        this.reg(this.cfg_wars_misc_cache);
        this.reg(this.cfg_wars_mission_cache);
        this.reg(this.cfg_wars_state_cache);
        this.reg(this.cfg_wars_text_const_cache);
        this.reg(this.cfg_week_target_cache);
        this.reg(this.cfg_week_target_level_cache);
        this.reg(this.cfg_wing_buff_cache);
        this.reg(this.cfg_wing_hero_cache);
        this.reg(this.cfg_wing_hero_skin_cache);
        this.reg(this.cfg_wing_level_cache);
        this.reg(this.cfg_world_boss_hurt_rewards_cache);
        this.reg(this.cfg_world_boss_level_cache);
        this.reg(this.cfg_world_map_cache);
        this.reg(this.cfg_world_map_2_cache);
        this.reg(this.cfg_wxShare_cache);
        this.reg(this.cfg_wxTurn_gift_cache);
        this.reg(this.cfg_wx_game_club_cache);
        this.reg(this.cfg_xswh_boss_cache);
        this.reg(this.cfg_xswh_gift_cache);
        this.reg(this.cfg_xswh_hurt_rewards_cache);
        this.reg(this.cfg_xswh_rank_rewards_cache);
        this.reg(this.cfg_ybzk_reward_cache);
        this.reg(this.cfg_zero_buy_cache);
        this.reg(this.errorCode_cache);
        this.reg(this.fightAttr_cache);
        this.reg(this.victoryMacro_cache);
    }

    init(): void {
        this.regAll();
    }

    public static PARSE_STEP: boolean = true;

    parse(data: ArrayBuffer): void {
        var reader = new Byte(data);
        var filecount = reader.readUint16();

        for(var i = 0; i < filecount; ++i) {
            var name = reader.readUTFString();
            var size = reader.readUint32();
            var hash = reader.readUint32();
            var offset = reader.pos;

            reader.pos += size;

            if(!(name in this.caches)) {
                // throw new Error("[ERROR]CfgCacheMgr.init {!(name in CfgCacheMgr.caches)}:" + name);
                console.warn("[ERROR]CfgCacheMgr.init {!(name in CfgCacheMgr.caches)}:" + name);
                continue;
            }

            var cache = this.get(name);
            cache.init(data, offset, hash);

            this.step_names.push(name);
        }

        if(CfgCacheMgr.PARSE_STEP) {
            ILaya.timer.frameLoop(1, this, this.step);
        }
        else {
            while(!this._is_complete) {
                this.step();
            }
        }
        
    }

    private _is_complete: boolean = false;
    get is_complete(): boolean {
        return this._is_complete;
    }

    get step_percent(): number {
        return this.step_idx / this.step_names.length;
    }

    private step(): void {
        for(var i = 0; i < CfgCacheMgr.STEP; ++i) {
            let name = this.step_names[this.step_idx];
            let cache = this.caches[name];
            // try {
                cache.parse();
            // }
            // catch(e) {
            //     ILaya.timer.clearAll(this);
            //     throw 'CfgCacheMgr.step cache.parse ERROR!{ name:' + name + ", e.message:" + e.toString();
            // }

            cache.onComplete();

            this.step_idx++;
            if(this.step_idx >= this.step_names.length) {
                this._is_complete = true;
                
                break;
            }
        }

        if(this._is_complete) {
            ILaya.timer.clear(this, this.step);

            // check
            for(let name in this.caches) {
                let cache = this.caches[name];
                if(!cache.is_complete) {
                    console.error("CfgCacheMgr.step [!cache.is_complete]:" + name);
                    // throw "CfgCacheMgr.step [!cache.is_complete]:" + name;
                }
            }

            this.event(Event.COMPLETE);
        }
    }
}