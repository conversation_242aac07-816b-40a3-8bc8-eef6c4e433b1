import { ILaya } from "ILaya";
import { <PERSON><PERSON> } from "Laya";
import { PhpStatUtil } from "../../../../game/a2reg/PhpStatUtil";
import { GlobalConfig } from "../../../../game/GlobalConfig";
import { ConfigManager } from "../../../managers/ConfigManager";
import { DispatchManager } from "../../../managers/DispatchManager";
import { LayerManager } from "../../../managers/LayerManager";
import { Connection } from "../../../net/Connection";
import { p_kv } from "../../../proto/common/p_kv";
import { m_hanging_quick_tos } from "../../../proto/line/m_hanging_quick_tos";
import { m_hanging_reward_tos } from "../../../proto/line/m_hanging_reward_tos";
import { m_main_battle_box_compose_tos } from "../../../proto/line/m_main_battle_box_compose_tos";
import { m_main_battle_box_fetch_tos } from "../../../proto/line/m_main_battle_box_fetch_tos";
import { m_main_battle_box_open_tos } from "../../../proto/line/m_main_battle_box_open_tos";
import { m_main_battle_box_rate_tos } from "../../../proto/line/m_main_battle_box_rate_tos";
import { m_main_battle_box_upgrade_tos } from "../../../proto/line/m_main_battle_box_upgrade_tos";
import { m_main_battle_fetch_pass_tos } from "../../../proto/line/m_main_battle_fetch_pass_tos";
import { m_main_battle_info_tos } from "../../../proto/line/m_main_battle_info_tos";
import { m_main_battle_mission_fetch_tos } from "../../../proto/line/m_main_battle_mission_fetch_tos";
import { m_main_battle_mission_info_tos } from "../../../proto/line/m_main_battle_mission_info_tos";
import { m_main_battle_next_tos } from "../../../proto/line/m_main_battle_next_tos";
import { m_simp_mission_fetch_tos } from "../../../proto/line/m_simp_mission_fetch_tos";
import { m_small_game_info_tos } from "../../../proto/line/m_small_game_info_tos";
import { ArrayUtil } from "../../../util/ArrayUtil";
import { ColorUtil } from "../../../util/ColorUtil";
import { GameUtil } from "../../../util/GameUtil";
import { HtmlUtil } from "../../../util/HtmlUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { DataCenter } from "../../DataCenter";
import { FightType } from "../../fight/data/FightConst";
import { FightDataCenter } from "../../fight/data/FightDataCenter";
import { GoodsVO } from "../../goods/GoodsVO";
import { MiscConst } from "../../misc_config/MiscConst";
import MissionConst from "../../mission/MissionConst";
import { PanelEventConstants } from "../../PanelEventConstants";
import { PaymentDataCenter } from "../../payment/data/PaymentDataCenter";
import { VipTeQuanConst } from "../../payment/VipTeQuanConst";
import VipTeQuanUtil from "../../payment/VipTeQuanUtil";
import { PaymentVO } from "../../payment/vo/PaymentVO";
import { RedPointMgr } from "../../redPoint/RedPointMgr";
import { GoodsManager } from "../../test_bag/GoodsManager";
import { YueKaDataCenter } from "../../welfare/data/YueKaDataCenter";
import { ItemConst } from "../../goods/ItemConst";
import { MatchConst } from "../../../auto/ConstAuto";
import { m_main_battle_auto_tos } from "../../../proto/line/m_main_battle_auto_tos";
import { LineUpDataCenter } from "../../lineUp/data/LineUpDataCenter";
export var BUILD_SHOW_TYPE;
(function (BUILD_SHOW_TYPE) {
    BUILD_SHOW_TYPE[BUILD_SHOW_TYPE["BUILD"] = 1] = "BUILD";
    BUILD_SHOW_TYPE[BUILD_SHOW_TYPE["GUAJI"] = 2] = "GUAJI";
})(BUILD_SHOW_TYPE || (BUILD_SHOW_TYPE = {}));
//数据不要用静态类型的
//可以在本模块引用，不可跨模块引用
//本模块引用的时候不要缓存instance，每次通过instance获取即可
export class GuaJiDataCenter {
    constructor() {
        this.start_time = 0; //开始挂机时间（时间戳）
        this.max_duration = 0; //挂机最高持续时间（默认是12小时）
        this.free_quick_times = 0; //当天免费快速挂机的剩余次数
        this.quick_times = 0; //当天剩余快速挂机的次数
        this.cost_gold = 0; //快速挂机消耗钻石数
        this.rewards_times = 0; //累计的奖励次数（每次重新上线的时候会结算）
        this.is_activity = false;
        this.is_quick_fighting = false;
        this._pass_rewards = []; //通关奖励已领取的列表
        this.show_box_red_point = true;
        this.OPEN_FAIL_PANEL = "";
        /**跳过免费次数 */
        this.finish_times = 0;
        this.mission_status = true; //关卡是否开启 true 表开启
        this.guaji_box_rates = [];
        this.guaji_box_tequan_rates = [];
        this.guaji_box_level_rates = [];
        this.small_soldier_game_fetch_list = [];
        this.guaji_box_hanging_rates = new Map(); // 宝箱概率
        this.main_battle_show_time = 0; //3min
        this.show_main_battle = true;
        this.gainRewards = []; //实际挂机奖励
        this.showFixedItems = []; //显示的每分钟奖励
        this.autoFight = false;
        this.guaJiDialogShowType = 1;
        /**
         * 判断"快速挂机"的按钮红点
         */
        this.loginRedPoint = true;
    }
    static get RECOMMEND_LINE_UP_TYPE_NAME() {
        return ["", window.iLang.L2_ZUI_DI_ZHAN_LI.il(), window.iLang.L2_ZUI_DI_XING_JI.il()];
    }
    static get instance() {
        if (GuaJiDataCenter._instance == null) {
            GuaJiDataCenter._instance = new GuaJiDataCenter();
        }
        return GuaJiDataCenter._instance;
    }
    reset() {
        GuaJiDataCenter._instance = null;
    }
    static set isInGuaJiDialog(value) {
        GuaJiDataCenter._isInGuaJiDialog = value;
        if (value == false) {
            DispatchManager.dispatchEvent("CLOSED_GUA_JI" /* CLOSED_GUA_JI */);
        }
    }
    static get isInGuaJiDialog() {
        return GuaJiDataCenter._isInGuaJiDialog && GuaJiDataCenter.instance.guaJiDialogShowType == BUILD_SHOW_TYPE.GUAJI;
    }
    static get isManuChange() {
        return GuaJiDataCenter._isManuChange;
    }
    static set isManuChange(val) {
        GuaJiDataCenter._isManuChange = val;
    }
    static get firstEnterDialog() {
        return GuaJiDataCenter._firstEnterDialog;
    }
    static set firstEnterDialog(isEnter) {
        if (!GuaJiDataCenter._firstEnterDialog && isEnter) {
            GuaJiDataCenter._firstEnterDialog = isEnter;
            //只有在 没有剩余自动挑战次数，并且当前没有进行主线战斗时，才会在第一次进入挂机界面，发送结束自动推关请求
            if (GuaJiDataCenter.instance.laveAutoPassNum <= 0 && !GuaJiDataCenter.instance.isRunningMainBattle()) {
                GuaJiDataCenter.instance.m_main_battle_auto_tos(false);
            }
        }
    }
    isRunningMainBattle() {
        return LayerManager.runningFight(MatchConst.MATCH_TYPE_MAIN_BATTLE);
    }
    isRunningMasterCardBattle() {
        return LayerManager.runningFight(MatchConst.MATCH_TYPE_MASTER_CARD);
    }
    isCanFightNextPass(isDo = false, isAuto = false) {
        let cfg = GuaJiDataCenter.instance.main_battle;
        let isMaxFight = GuaJiDataCenter.instance.checkIsMaxFightPass();
        if (cfg.need_level > DataCenter.myLevel) {
            if (isDo) {
                if (GuaJiDataCenter.instance.quick_times > 0) {
                    DispatchManager.dispatchEvent("OPEN_GUA_JI_QUICK_FIGHT_DIALOG" /* OPEN_GUA_JI_QUICK_FIGHT_DIALOG */);
                }
                else if (!YueKaDataCenter.instance.isOpenAllQuickFightYueKa()) {
                    TipsUtil.showDialog(this, window.iLang.L2_JI_HUO_YUE_KA_HUO_QU_GENG_DUO_KUAI_SU_GUA_JI_CI_SHU.il(), window.iLang.L2_TI_ch11_SHI.il(), () => {
                        GameUtil.gotoSystemPanelById(PanelEventConstants.PAYMENT, PaymentVO.KEY_WELFARE_CARD);
                    }, { okName: window.iLang.L2_QIAN_WANG_JI_HUO.il() });
                }
                else {
                    TipsUtil.showTips(window.iLang.L2_DENG_JI_BU_ZU_ch31_QING_TI_SHENG_DENG_JI.il());
                }
            }
            return false;
        }
        else if (!isAuto && GuaJiDataCenter.instance.isNeedNewChapter()) {
            if (isDo) {
                DispatchManager.dispatchEvent("OPEN_GUA_JI_WROLD_MAP_DIALOG" /* OPEN_GUA_JI_WROLD_MAP_DIALOG */);
            }
            return false;
        }
        else if (isMaxFight) {
            TipsUtil.showTips(window.iLang.L2_YI_DA_DANG_QIAN_ZUI_DA_GUAN_KA_ch31_JING_QING_QI_DAI_XIN_GUAN_QIA_TUI_CHU.il());
            return false;
        }
        return true;
    }
    onFightMainBattle({ isInBackFight = false, isAuto = false, checkReback = true, isLineUp = true, isDo = true, isCheckTravel = true, lineUpVo = null, } = {}) {
        if (this.isCanFightNextPass(isDo, isAuto)) {
            let fight_type = isInBackFight ? FightType.BACK_FIGHT : FightType.MAKE_WAR;
            let cfg = ConfigManager.cfg_main_battleCache.get(GuaJiDataCenter.instance.curGuajiPass);
            if (isAuto || !isCheckTravel || !this.checkTravelScore(0, this, this._fightMainBattle, [cfg, fight_type, checkReback, isLineUp])) {
                this._fightMainBattle(cfg, fight_type, checkReback, isLineUp, lineUpVo);
            }
        }
        else {
            GuaJiDataCenter.instance.m_main_battle_auto_tos(false);
            GuaJiDataCenter.instance.laveAutoPassNum = 0;
        }
    }
    _fightMainBattle(cfg, fight_type, checkReback, isLineUp, lineUpVo) {
        let isshowSkip = GuaJiDataCenter.instance.curPass >= MiscConst.main_battle_quick_fight_pass;
        FightDataCenter.instance.m_fight_start_tos(MatchConst.MATCH_TYPE_MAIN_BATTLE, cfg.id, {
            scenename: cfg.scenename,
            targetname: cfg.name + window.iLang.L2_DI_P0_GUAN.il([cfg.id]),
            finishevent: "OPEN_FIGHT_RESULT_DIALOG" /* OPEN_FIGHT_RESULT_DIALOG */,
            fightype: fight_type,
            is_show_skip: isshowSkip,
            checkReback: checkReback,
            isLineUp: isLineUp,
            lineUpVo: lineUpVo,
        });
    }
    set main_battle_info(info) {
        GameUtil.endTimeLog("login");
        PhpStatUtil.act2Reg("finish_login");
        this._main_battle_info = info;
        let isRed = false;
        if (info) {
            //排序
            info.pass_status.sort(function (item1, item2) {
                if (item1.val == 1 && item2.val != 1) {
                    return -1;
                }
                else if (item1.val != 1 && item2.val == 1) {
                    return 1;
                }
                else if (item1.val == 2 && item2.val != 2) {
                    return 1;
                }
                else if (item1.val != 2 && item2.val == 2) {
                    return -1;
                }
                else {
                    return item1.key - item2.key;
                }
            });
            //判断红点
            for (let item of info.pass_status) {
                if (item.val == 1) {
                    isRed = true;
                    break;
                }
            }
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.PASS_REWARD, 0, isRed ? 1 : 0);
    }
    get main_battle_info() {
        return this._main_battle_info;
    }
    /** 初始化快速挂机的宝箱概率 */
    set_guaji_box_hanging_rates(rates) {
        rates.forEach((rate) => {
            this.guaji_box_hanging_rates.set(rate.key, rate);
        });
    }
    /** 挂机时间 */
    get guaJiTime() {
        let time = GuaJiDataCenter.instance.rewards_times * 60 + (GlobalConfig.serverTime - GuaJiDataCenter.instance.start_time);
        return time > this.max_duration ? this.max_duration : time;
    }
    /**是否正在进行自动推关 */
    get isAutoPass() {
        if (this.autoBattleInfo) {
            return this.autoBattleInfo.is_open;
        }
        return this.autoFight;
    }
    /**剩余自动推关次数 */
    set laveAutoPassNum(num) {
        if (this.autoBattleInfo) {
            this.autoBattleInfo.lave_auto_pass = num;
        }
        this.autoFight = num > 0;
    }
    /**剩余自动推关次数 */
    get laveAutoPassNum() {
        if (this.autoBattleInfo) {
            return this.autoBattleInfo.lave_auto_pass;
        }
        return this.isAutoPass ? 1 : 0;
    }
    /**当前宝箱等级 */
    get boxLevel() {
        if (this.main_battle_Box_data) {
            return this.main_battle_Box_data.level || 0;
        }
        return 0;
    }
    get lineUpHeroNum() {
        let lineupList = LineUpDataCenter.instance.getLineUpListByType(MatchConst.MATCH_TYPE_MAIN_BATTLE);
        return lineupList.length;
    }
    /**检测是否可以挑战关卡 */
    checkIsCanFightPass(pass) {
        let cfg = ConfigManager.cfg_main_battleCache.get(pass);
        if (cfg) {
            return DataCenter.myLevel >= cfg.need_level;
        }
        return false;
    }
    /**检测是否最大关卡 */
    checkIsMaxFightPass() {
        let max_pass = ConfigManager.cfg_main_battleCache.get_all().length;
        return (GuaJiDataCenter.instance.curPass + 1) == max_pass;
    }
    /**
     * 检查政务值是否会超出限制
     * @param guaJiTime 挂机时间(秒数)
     * @param confirmFun
     */
    checkTravelScore(guaJiTime, caller, confirmFun = null, args = []) {
        let cfgVO = ConfigManager.cfg_main_battleCache.get(this.curGuajiPass);
        if (cfgVO) {
            let fetchScore = Math.floor(guaJiTime / 60) * cfgVO.travel;
            if (fetchScore + DataCenter.myTravelScore > DataCenter.maxTravelScore) {
                let txt = window.iLang.L2_DANG_QIAN_YI_YOU.il() + HtmlUtil.font(DataCenter.myTravelScore + "/" + DataCenter.maxTravelScore, ColorUtil.GREEN) +
                    window.iLang.L2_TAN_SUO_DIAN_ch31_RECEIVE_HOU_CHAO_CHU_SHANG_XIAN_BU_FEN_JIANG_SUN_SHI_ch31_SHI_FOU.il();
                //弹窗提示政务值溢出
                TipsUtil.showDialog(caller, txt, window.iLang.L2_TI_SHI.il(), confirmFun, {
                    args: args,
                });
                return true;
            }
        }
        return false;
    }
    onQuickFight(delay = 0, isShowTips = true, caller = null, callBack = null) {
        if (this.is_quick_fighting) {
            TipsUtil.showTips(window.iLang.L2_KUAI_SU_GUA_JI_ZHONG.il());
            return;
        }
        if (this.quick_times <= 0) {
            this.m_hanging_quick_tos();
            return;
        }
        if (isShowTips) {
            let b = this.checkTravelScore(60 * 60 * 2, this, function () {
                this._quickFightTos(delay, caller, callBack);
            });
            if (b) {
                return;
            }
        }
        this._quickFightTos(delay, caller, callBack);
    }
    _quickFightTos(delay = 0, caller, callBack) {
        if (delay > 0) {
            this.is_quick_fighting = true;
            ILaya.timer.once(delay, this, this.m_hanging_quick_tos);
            if (callBack) {
                callBack.call(caller);
            }
        }
        else {
            this.m_hanging_quick_tos();
        }
    }
    //-------------协议发送 start -------------
    m_hanging_reward_tos(op_type = 2) {
        let tos = new m_hanging_reward_tos();
        tos.op_type = op_type;
        Connection.instance.sendMessage(tos);
    }
    m_hanging_quick_tos() {
        let tos = new m_hanging_quick_tos();
        Connection.instance.sendMessage(tos);
    }
    m_main_battle_info_tos() {
        let tos = new m_main_battle_info_tos();
        Connection.instance.sendMessage(tos);
    }
    m_main_battle_next_tos() {
        let tos = new m_main_battle_next_tos();
        Connection.instance.sendMessage(tos);
    }
    m_main_battle_fetch_pass_tos(pass, isShow = true) {
        let tos = new m_main_battle_fetch_pass_tos();
        tos.pass = pass;
        tos.is_show_reward = isShow;
        Connection.instance.sendMessage(tos);
    }
    /**领取新手通关奖励 */
    m_main_battle_mission_fetch_tos(mission_id) {
        let tos = new m_main_battle_mission_fetch_tos();
        tos.id = mission_id;
        Connection.instance.sendMessage(tos);
    }
    m_main_battle_mission_info_tos() {
        let tos = new m_main_battle_mission_info_tos();
        Connection.instance.sendMessage(tos);
    }
    m_main_battle_auto_tos(is_open) {
        let tos = new m_main_battle_auto_tos();
        tos.is_open = is_open;
        Connection.instance.sendMessage(tos);
        this.autoFight = is_open;
        DispatchManager.dispatchEvent("UPDATE_MAIN_BATTLE_AUTO" /* UPDATE_MAIN_BATTLE_AUTO */);
    }
    /**1=开始自动，2=停止自动，3=领奖，4=达到最大关卡了 */
    // m_main_battle_auto_pass_tos(op_type: number): void {
    //     let tos: m_main_battle_auto_pass_tos = new m_main_battle_auto_pass_tos();
    //     tos.op_type = op_type;
    //     Connection.instance.sendMessage(tos);
    // }
    // m_main_battle_chapter_fetch_tos(id: number): void {
    //     let tos: m_main_battle_chapter_fetch_tos = new m_main_battle_chapter_fetch_tos();
    //     tos.id = id;
    //     Connection.instance.sendMessage(tos);
    // }
    //----支线任务   start----
    /**
     * 支线任务
     * @param op_type 发送类型 1领奖
     * @param id 任务id
     */
    m_simp_mission_fetch_tos(id) {
        let tos = new m_simp_mission_fetch_tos();
        tos.mission_type = MissionConst.MISSION_TYPE_SUB;
        tos.id = id;
        Connection.instance.sendMessage(tos);
    }
    m_main_battle_box_fetch_tos(type) {
        let tos = new m_main_battle_box_fetch_tos();
        tos.type = type;
        Connection.instance.sendMessage(tos);
    }
    m_main_battle_box_open_tos(box_type, num) {
        let tos = new m_main_battle_box_open_tos();
        tos.id = box_type;
        tos.num = num;
        Connection.instance.sendMessage(tos);
    }
    m_main_battle_box_upgrade_tos() {
        let tos = new m_main_battle_box_upgrade_tos();
        Connection.instance.sendMessage(tos);
    }
    m_main_battle_box_rate_tos() {
        let tos = new m_main_battle_box_rate_tos();
        Connection.instance.sendMessage(tos);
    }
    m_small_game_info_tos(id, op_type) {
        let tos = new m_small_game_info_tos();
        tos.id = id;
        tos.op_type = op_type; //0挑战成功
        Connection.instance.sendMessage(tos);
    }
    //----支线任务   end----
    //-------------协议发送 end ---------------
    /**
     * 获取当前关卡所在的章节有多少关
     * @param num 当前关卡
     */
    static getGroupMaxProgress(num) {
        let cfg = ConfigManager.cfg_main_battleCache.get(num);
        if (cfg) {
            let list = ConfigManager.cfg_main_battle_groupCache.get(cfg.group);
            if (list) {
                return list.length;
            }
        }
        return 0;
    }
    /**
     * 获取当前关卡所在的章节进度
     */
    static getGroupCurProgress() {
        let num = GuaJiDataCenter.instance.curGuajiPass;
        let result = 0;
        let cfg = ConfigManager.cfg_main_battleCache.get(num);
        let list = ConfigManager.cfg_main_battle_groupCache.get(cfg.group);
        if (list) {
            list.forEach(cfg => {
                if (num > cfg.id) {
                    result++;
                }
            });
        }
        result = GuaJiDataCenter.instance.isNeedNewChapter() ? result + 1 : result;
        return result;
    }
    getShowPassRewardInfo() {
        if (this.main_battle_info) {
            let len = this.main_battle_info.pass_status.length;
            let info = null;
            for (let i = 0; i < len; i++) {
                info = this.main_battle_info.pass_status[i];
                if (info && info.val != 2) {
                    return info;
                }
            }
        }
        return null;
    }
    makePassKv(id, val) {
        let kv = new p_kv();
        kv.key = id;
        kv.val = val;
        return kv;
    }
    getPassRewardList() {
        return this._pass_rewards;
        // let result: Array<p_kv>;
        // if (this.main_battle_info) {
        //     // let len: number = this.main_battle_info.pass_status.length;
        //     // let info: p_kv = null;
        //     // result = [];
        //     // for (let i: number = 0; i < len; i++) {
        //     //     info = this.main_battle_info.pass_status[i];
        //     //     if (info && info.val != 2) {
        //     //         result.push(info);
        //     //     }
        //     // }
        //     return this.main_battle_info.pass_status;
        // }
        // // return result;
        // return [];
    }
    /**获取指定关卡的通关奖励 */
    getPassReward(round) {
        if (this.main_battle_info) {
            for (let info of this.main_battle_info.pass_status) {
                if (info.key == round) {
                    return info;
                }
            }
        }
        return null;
    }
    /**当前所在关卡 */
    get curPass() {
        if (this.main_battle_info) {
            return this.main_battle_info.cur_pass;
        }
        return 1;
    }
    /**当前挂机显示关卡 */
    get curGuajiPass() {
        if (this.main_battle_info) {
            if (this.main_battle_info.need_new_chapter) {
                return this.main_battle_info.cur_pass;
            }
            else {
                return this.main_battle_info.cur_pass + 1;
            }
        }
        return 1;
    }
    /**当前已过关了的章节 */
    get curPassChapter() {
        let round = GuaJiDataCenter.instance.curPass + 1;
        let cfgBattle = ConfigManager.cfg_main_battleCache.get(round);
        /**通关章节 */
        let chapterFinish = cfgBattle.group - 1;
        return chapterFinish;
    }
    /**当前所在章节 */
    get curChapter() {
        if (this.main_battle_info) {
            let cfg = ConfigManager.cfg_main_battleCache.get(this.curGuajiPass);
            return cfg.group;
        }
        return 1;
    }
    get needLevel() {
        if (this.main_battle_info) {
            let cfg = ConfigManager.cfg_main_battleCache.get(this.curGuajiPass);
            return cfg.need_level;
        }
        return 1;
    }
    get main_battle() {
        return ConfigManager.cfg_main_battleCache.get(this.curGuajiPass);
    }
    getMinLevelByChapter(chapter) {
        let list = ConfigManager.cfg_main_battle_groupCache.get(chapter);
        let minLevel = 9999999999;
        list.forEach(cfg => {
            if (minLevel > cfg.id) {
                minLevel = cfg.id;
            }
        });
        return minLevel;
    }
    getMaxLevelByChapter(chapter) {
        let list = ConfigManager.cfg_main_battle_groupCache.get(chapter);
        let minLevel = -1;
        list.forEach(cfg => {
            if (minLevel < cfg.id) {
                minLevel = cfg.id;
            }
        });
        return minLevel;
    }
    /**是否需要前往下一章节 */
    isNeedNewChapter() {
        if (this.main_battle_info) {
            return this.main_battle_info.need_new_chapter;
        }
        return false;
    }
    checkRedQuickFight() {
        //let bool: boolean = this.free_quick_times > 0 || (this.quick_times > 0 && DataCenter.myGold >= this.cost_gold);
        let count = 0;
        let cfg = GuaJiDataCenter.instance.main_battle;
        if (!cfg || DataCenter.myLevel >= cfg.need_level) {
            if (this.free_quick_times > 0) {
                count++;
            }
            else if (this.loginRedPoint == true && this.quick_times > 0 && DataCenter.myGold >= this.cost_gold) {
                count++;
            }
        }
        if (this.guaJiTime > GuaJiDataCenter.GUA_JI_REDPOINT_TIME) { //挂机1小时以上提示红点
            count++;
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.GUA_JI, GuaJiDataCenter.QUICK_FIGHT_RED_POINT, count);
        return count > 0;
    }
    /**
     * 判断"攻击城池"的按钮红点
     */
    checkRedFightStart() {
        let count = 0;
        if (GuaJiDataCenter.instance.mission_status == true) {
            count = DataCenter.myLevel >= this.needLevel ? 1 : 0;
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.GUA_JI, GuaJiDataCenter.FIGHT_START_RED_POINT, count);
        return count > 0;
    }
    /**自动推图领取奖励红点 */
    // public checkAutoPassRedPoint(): boolean {
    //     let count: number = this.auto_pass_status == 2 ? 1 : 0;
    //     RedPointMgr.ins.SetRedData(PanelEventConstants.GUAJI_AUTO_PASS, 0, count);
    //     return count > 0;
    // }
    /**挂机战斗 */
    isGuaJiRedPoint() {
        this.checkRedQuickFight();
        // this.checkRedFightStart();
        this.checkPassRewardRedPoint();
        // this.isPassRewardRedPoint();
        // this.checkAutoPassRedPoint();
    }
    getAllMonsterUrls(result) {
        // var cfg_base: cfg_monster;
        // var cfg_show: cfg_client_show;
        // var tempclienteffect: cfg_client_effect;
        // var temp: any;
        // var min: number = GuaJiDataCenter.instance.getMinLevelByChapter(GuaJiDataCenter.instance.curChapter);
        // var max: number = GuaJiDataCenter.instance.getMaxLevelByChapter(GuaJiDataCenter.instance.curChapter);
        // for (var i: number = min; i < max; i++) {
        //     let cfg: cfg_main_battle = ConfigManager.cfg_main_battleCache.get(i);
        //     if (cfg) {
        //         let moninfo: string[] = cfg.guaji_monsters.split("|");
        //         for (var j = 0; j < moninfo.length; j++) {
        //             var mono = moninfo[j].split(",");
        //
        //             var mid: number = Number(mono[0]);
        //
        //             if (mid > 0) {
        //                 cfg_base = ConfigManager.cfg_monsterCache.get(mid);
        //
        //                 if (!AnimationManager.cacheskillid[cfg_base.afk_skill]) {
        //                     AnimationManager.cacheskillid[cfg_base.afk_skill] = true;
        //                     cfg_show = ConfigManager.cfg_client_showCache.get(cfg_base.afk_skill);
        //
        //                     if (cfg_show) {
        //                         temp = cfg_show.effects;
        //
        //                         for (var k = 0; k < temp.length; k++) {
        //                             tempclienteffect = ConfigManager.cfg_client_effectCache.get(temp[k]);
        //                             if (tempclienteffect.is2d == 1) {
        //                                 continue;
        //                             }
        //                             result.push(UrlConfig.EFFECT_PATH_3D + tempclienteffect.name + UrlConfig.LH_EXT);
        //                         }
        //                     }
        //
        //                 }
        //
        //             }
        //         }
        //
        //     }
        // }
    }
    getAllMonsterSkins(result) {
        var cfg_base;
        var min = GuaJiDataCenter.instance.getMinLevelByChapter(GuaJiDataCenter.instance.curChapter);
        var max = GuaJiDataCenter.instance.getMaxLevelByChapter(GuaJiDataCenter.instance.curChapter);
        for (var i = min; i < max; i++) {
            let cfg = ConfigManager.cfg_main_battleCache.get(i);
            if (cfg) {
                let moninfo = cfg.guaji_monsters.split("|");
                for (var j = 0; j < moninfo.length; j++) {
                    var mono = moninfo[j].split(",");
                    var mid = Number(mono[0]);
                    if (mid > 0) {
                        cfg_base = ConfigManager.cfg_monsterCache.get(mid);
                        result.push(cfg_base.skin);
                    }
                }
            }
        }
    }
    getNextMonsterSkins(result) {
        var cfg_base;
        let cfg = ConfigManager.cfg_main_battleCache.get(GuaJiDataCenter.instance.curGuajiPass);
        var currentlevel = GuaJiDataCenter.instance.getMinLevelByChapter(cfg.group);
        cfg = ConfigManager.cfg_main_battleCache.get(currentlevel);
        if (!cfg) {
            cfg = ConfigManager.cfg_main_battleCache.get(GuaJiDataCenter.instance.curGuajiPass);
            currentlevel = GuaJiDataCenter.instance.getMinLevelByChapter(cfg.group);
            cfg = ConfigManager.cfg_main_battleCache.get(currentlevel);
        }
        else {
            if (cfg.group != GuaJiDataCenter.instance.curChapter) {
                currentlevel = GuaJiDataCenter.instance.getMinLevelByChapter(GuaJiDataCenter.instance.curChapter);
                cfg = ConfigManager.cfg_main_battleCache.get(currentlevel);
            }
        }
        let moninfo = cfg.guaji_monsters.split("|");
        for (var j = 0; j < moninfo.length; j++) {
            var mono = moninfo[j].split(",");
            var mid = Number(mono[0]);
            if (mid > 0) {
                cfg_base = ConfigManager.cfg_monsterCache.get(mid);
                result.push(cfg_base.skin);
            }
        }
    }
    /**
     * 是否占有；如果占有则有旗子
     */
    isOwn(cityId) {
        // let cfg: cfg_world_city = ConfigManager.cfg_world_cityCache.get(cityId);
        // if (GuaJiDataCenter.instance.curChapter > cfg.group && GuaJiDataCenter.instance.curChapter != cfg.id) {
        //     return true;
        // }
        return false;
    }
    /**通过奖励红点 */
    isPassRewardRedPoint() {
        //通关奖励
        let count = 0;
        let info = this.getShowPassRewardInfo();
        if (info && info.val == 1) {
            count = 1;
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.GUA_JI, GuaJiDataCenter.PASS_REWARD_RED_POINT, count);
        return count > 0;
    }
    isMissionReward2RedPoint() {
        let red_count = 0;
        let stateInfo = GuaJiDataCenter.instance.main_battle_mission_status;
        ConfigManager.cfg_main_battle_missionCache.get_all().forEach(cfg => {
            let status = cfg.id < stateInfo.key ? 2 : cfg.id == stateInfo.key ? stateInfo.val : 0;
            if (status == 1) {
                red_count = 1;
            }
        });
        RedPointMgr.ins.SetRedData(PanelEventConstants.GUA_JI, GuaJiDataCenter.PASS_REWARD_RED_POINT, red_count);
        RedPointMgr.ins.SetRedData(PanelEventConstants.PASS_REWARD, 1, red_count);
        return red_count > 0;
    }
    /**
     * 获取挂机宝箱数量
     */
    getGuajiBoxCount() {
        let boxCount = 0;
        ConfigManager.cfg_main_battle_boxCache.get_all().forEach(cfg => {
            boxCount += GoodsManager.instance.GetGoodsNumByTypeId(cfg.item_id);
        });
        return boxCount;
    }
    static parseGuajiReward(gains) {
        let res = [];
        gains.forEach(info => {
            res.push(GoodsVO.GetVoByTypeId(info.type_id, info.num));
        });
        // if (msg.role_exp != 0) {
        //     res.push(GoodsVO.GetVoByTypeId(GuaJiDataCenter.TYPE_ID_EXP, msg.role_exp));
        // }
        // if (msg.hero_exp != 0) {
        //     res.push(GoodsVO.GetVoByTypeId(GuaJiDataCenter.TYPE_ID_HERO_EXP, msg.hero_exp));
        // }
        // if (msg.silver != 0) {
        //     res.push(GoodsVO.GetVoByTypeId(GuaJiDataCenter.TYPE_ID_SILVER, msg.silver));
        // }
        // if (msg.travel != 0) {
        //     res.push(GoodsVO.GetVoByTypeId(GuaJiDataCenter.TYPE_ID_TRAVEL_SCORE, msg.travel));
        // }
        // if (msg.forage != 0) {
        //     res.push(GoodsVO.GetVoByTypeId(GuaJiDataCenter.TYPE_ID_FORGE, msg.forage));
        // }
        return res;
    }
    /**
     *
     * @param guaji_time 挂机时间 （秒）
     */
    static getGuajiBoxEffName(guaji_time) {
        let res;
        let cfg = this.getCfgGuajiBoxTime(guaji_time);
        if (cfg) {
            res = cfg.eff_name;
        }
        return res;
    }
    static getCfgGuajiBoxTime(guaji_time) {
        let res;
        let list = ConfigManager.cfg_guaji_box_timeCache.get_all();
        if (!list)
            return res;
        guaji_time = guaji_time / 60;
        let cfg;
        for (let i = 0; i < list.length; i++) {
            cfg = list[i];
            if (guaji_time >= cfg.min_time && guaji_time < cfg.max_time) {
                res = cfg;
                break;
            }
        }
        return res;
    }
    /**是否可跳过 */
    static checkPlotSkip(match_type) {
        if (match_type == MatchConst.MATCH_TYPE_MAIN_BATTLE) {
            let cfg = ConfigManager.cfg_main_battleCache.get(GuaJiDataCenter.instance.curGuajiPass);
            if (cfg.is_skip == 1)
                return false;
        }
        return true;
    }
    static isShowFreePass() {
        if (VipTeQuanUtil.isOpenPrivilege(VipTeQuanConst.FINISH_FIGHT))
            return false;
        if (DataCenter.myLevel >= MiscConst.finish_fight_lv_limit || DataCenter.myLevel < MiscConst.free_finish_fight_lv)
            return false;
        return true;
    }
    get subMissionMap() {
        return this._submissionmap;
    }
    SetSubMissionMap(dataList) {
        this._submissionmap = ArrayUtil.getMapByArray(dataList, "id");
        this.checkSubMissionRedPoint();
    }
    UpdateSubMissionMap(data) {
        this._submissionmap.set(data.id, data);
        this.checkSubMissionRedPoint();
    }
    checkSubMissionRedPoint() {
        let count = 0;
        if (this.subMissionMap) {
            let values = ArrayUtil.getArrByMapSortKey(this.subMissionMap);
            for (let data of values) {
                if (data.status == 1) {
                    count++;
                }
            }
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.GUA_JI, GuaJiDataCenter.SUB_MISSION_RED_POINT, count);
    }
    //----支线任务   end----
    checkGuajiBoxTeQuanRedPoint() {
        let vo = PaymentDataCenter.instance.getPaymentVoByID(PaymentVO.KEY_GUAJI_BOX_CODE);
        if (vo && GuaJiDataCenter.instance.boxLevel >= 10) {
            for (let i = 0; i < vo.list.length; i++) {
                let item_vo = vo.list[i];
                let cfg = ConfigManager.cfg_main_battle_box_tequanCache.get(item_vo.item_id);
                if (item_vo.isCanBuy && item_vo.price_type != ItemConst.COST_RMB && DataCenter.myGold >= GameUtil.gold(item_vo.price) && cfg && GuaJiDataCenter.instance.boxLevel >= cfg.level) {
                    return true;
                }
            }
        }
        else {
            return false;
        }
    }
    startShowMainBattleTip() {
        if (GuaJiDataCenter.instance.main_battle_show_time <= 0) {
            Laya.timer.clear(GuaJiDataCenter.instance, GuaJiDataCenter.instance.updateLoop);
            GuaJiDataCenter.instance.main_battle_show_time = MiscConst.main_battle_reward_tips_show_time + DataCenter.serverTimeSeconds;
            GuaJiDataCenter.instance.updateLoop();
            Laya.timer.loop(1000, GuaJiDataCenter.instance, GuaJiDataCenter.instance.updateLoop);
        }
    }
    updateLoop() {
        let t = GuaJiDataCenter.instance.main_battle_show_time - DataCenter.serverTimeSeconds;
        if (t > 0 && t != MiscConst.main_battle_reward_tips_show_time) {
            GuaJiDataCenter.instance.show_main_battle = false;
        }
        else {
            GuaJiDataCenter.instance.show_main_battle = true;
        }
        if (t <= 0) {
            Laya.timer.clear(GuaJiDataCenter.instance, GuaJiDataCenter.instance.updateLoop);
            GuaJiDataCenter.instance.main_battle_show_time = 0;
        }
    }
    m_main_battle_box_compose_tos(targetId, num) {
        let tos = new m_main_battle_box_compose_tos;
        tos.target_type_id = targetId;
        tos.num = num;
        Connection.instance.sendMessage(tos);
    }
    updatePassRewards(pass_rewards) {
        let isRed = false;
        let allPassList = ConfigManager.cfg_main_battle_fetchCache.get_all();
        //已通关的关卡ID列表
        let fetchPassIdList = [];
        let newPassRewards = [];
        for (const passItem of pass_rewards) {
            fetchPassIdList.push(passItem.key);
            newPassRewards.push(passItem);
        }
        for (const item of allPassList) {
            if (fetchPassIdList.indexOf(item.pass_id) === -1) {
                if (this.curPass >= item.pass_id) {
                    isRed = true;
                    //加上可领奖的
                    newPassRewards.push(this.makePassKv(item.pass_id, 1));
                }
                else {
                    //加上未通关的
                    newPassRewards.push(this.makePassKv(item.pass_id, 0));
                }
            }
        }
        this._pass_rewards = newPassRewards;
        this.checkPassRewardRedPoint();
        //排序
        this._pass_rewards.sort(function (item1, item2) {
            if (item1.val == 1 && item2.val != 1) {
                return -1;
            }
            else if (item1.val != 1 && item2.val == 1) {
                return 1;
            }
            else if (item1.val == 2 && item2.val != 2) {
                return 1;
            }
            else if (item1.val != 2 && item2.val == 2) {
                return -1;
            }
            else {
                return item1.key - item2.key;
            }
        });
        RedPointMgr.ins.SetRedData(PanelEventConstants.PASS_REWARD, 0, isRed ? 1 : 0);
    }
    //通关奖励的红点
    checkPassRewardRedPoint() {
        //通关奖励
        let count = 0;
        for (const item of this._pass_rewards) {
            if (item.val == 1) {
                count = 1;
                break;
            }
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.GUA_JI, GuaJiDataCenter.PASS_REWARD_RED_POINT, count);
        return count > 0;
    }
}
GuaJiDataCenter.TYPE_ID_EXP = 1001; //经验
GuaJiDataCenter.TYPE_ID_GOLD = 1002; //钻石
GuaJiDataCenter.TYPE_ID_SILVER = 1004; //金币
GuaJiDataCenter.TYPE_ID_HERO_EXP = 1011; //英雄经验
GuaJiDataCenter.TYPE_ID_TRAVEL_SCORE = 1013; //政务值
GuaJiDataCenter.TYPE_ID_FORGE = 1026; //粮草
GuaJiDataCenter.PASS_REWARD_RED_POINT = 1;
GuaJiDataCenter.FIGHT_START_RED_POINT = 2;
GuaJiDataCenter.QUICK_FIGHT_RED_POINT = 3;
GuaJiDataCenter.TRAVEL_RED_POINT = 4;
GuaJiDataCenter.SUB_MISSION_RED_POINT = 5;
GuaJiDataCenter.CHAPTER_RED_POINT = 5;
GuaJiDataCenter.FIGHT_STEP_TIME = 1500;
GuaJiDataCenter.MULTI_FIGHT_OPREA1 = 1;
/**触发技能的时机 */
GuaJiDataCenter.ACTION_TYPE1 = 1; //行动前的
GuaJiDataCenter.ACTION_TYPE15 = 15; //行动后的（触发伤害时）
GuaJiDataCenter.TRIG_SKILL_TYPE1 = 1; //主动技能
GuaJiDataCenter.TRIG_SKILL_TYPE2 = 2; //追加技能
GuaJiDataCenter.TRIG_SKILL_TYPE10 = 10; //触发技能
GuaJiDataCenter.TRIG_SKILL_TYPE11 = 11;
GuaJiDataCenter.TRIG_SKILL_TYPE12 = 12;
GuaJiDataCenter.TRIG_SKILL_TYPE13 = 13;
GuaJiDataCenter.TRIG_SKILL_TYPE14 = 14;
GuaJiDataCenter.TRIG_SKILL_TYPE51 = 51; //领主反击
GuaJiDataCenter.GUA_JI_REDPOINT_TIME = 3600;
GuaJiDataCenter.RECOMMEND_LINE_UP_TYPE_POWER = 1;
GuaJiDataCenter.RECOMMEND_LINE_UP_TYPE_STAR = 2;
GuaJiDataCenter.FIGHT_TIPS_JUMP = "fight_tips_jump";
GuaJiDataCenter._isInFight = false;
GuaJiDataCenter.isfixcamea = false;
GuaJiDataCenter.isautorotateback = true;
GuaJiDataCenter.manulrotate = false;
GuaJiDataCenter.isFirstPopNavigation = true;
GuaJiDataCenter.cachetest = new Map();
GuaJiDataCenter.guajiSkyBgX = 0;
GuaJiDataCenter.guajiFloorBgX = 0;
GuaJiDataCenter.isNeedChangeScene = false;
GuaJiDataCenter.changeSceneType = 0;
GuaJiDataCenter._instance = null;
GuaJiDataCenter._isInGuaJiDialog = false;
GuaJiDataCenter._isManuChange = false;
GuaJiDataCenter._firstEnterDialog = false;
