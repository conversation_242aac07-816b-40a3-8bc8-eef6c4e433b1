import { Text } from "laya/display/Text";
import { com } from "../../ui/layaMaxUI";
import { ColorUtil } from "../../util/ColorUtil";
import { GSkeleton } from "../baseModules/skeleton/GSkeleton";
import { ESkeletonType } from "../baseModules/skeleton/SkeletonData";
import { SkeletonManager } from "../baseModules/skeleton/SkeletonManager";
import { UIHTMLDiv } from "../baseModules/UIHTMLDiv";
import { StringUtil } from "../../util/StringUtil";
import { HtmlUtil } from "../../util/HtmlUtil";

//界面类都是每次打开每次新建的
export default class CommonTalkView extends com.ui.res.common.CommonTalkViewUI {
  
    private troop_id: number = 0;
    private type: number = 2;

    private _tempText:Text;
    private _isFlipX = false;
    private sk:GSkeleton;

    constructor() {
        super();
    }
    initUI(): void {
        UIHTMLDiv.SetUIHTMLDiv(this.html, 20, ColorUtil.FONT_NORAML, 5);
        this.zOrder = 1000;
        this.mouseEnabled = false;
        this.mouseThrough = true;
    }

    addEvent(): void {
    }

    get bottom(){
        return super.bottom;
    }
    set bottom(value:number){
        super.bottom = value;
        // this._onSize();
    }

    set_width(value: number): void {
        super.set_width(value);
        this._onSize();
    }

    set_height(value: number): void {
        super.set_height(value);
        this._onSize();
    }

    size(width: number, height: number) {
        super.size(width, height);
        this._onSize();
        return this;
    }

    private _onSize(){
        let width = this.width;
        let height = this.height;
        this.bg.size(width, height);
        this.bg.visible = this._isFlipX == false;

        this.bgFlipX.x = width;
        this.bgFlipX.size(width, height);
        this.bgFlipX.visible = this._isFlipX;
        this.html.width = width - 30;
        this.html.height = height - 40;

        //居中
        this.html.x = (width - this.html.width) / 2;
        this.html.y = (height - this.html.height) / 2 - 5;

        if (isNaN(this.bottom) == false){
            let oldBottom = this.bottom;
            this.bottom = 0;
            // this.bottom = oldBottom;
            this.callLater(()=>{
                if(!this.destroyed){
                    this.bottom = oldBottom;
                }
            })
        }
    }

    public setTalkText(talk: string, duration = -1, isAutoSize = false, isFlipX:boolean = false): void {
        this.timer.clear(this, this.onTimer);

        if(!talk){
            this.visible = false;
            return;
        }

        this.visible = true;

        this._isFlipX = isFlipX;

        //自适应宽高
        if(isAutoSize){
            if(!this._tempText){
                this._tempText = new Text();
                this._tempText.fontSize = 20;
            }
           
            this._tempText.text = HtmlUtil.ReplaceSpHtml(talk);

            let maxWidth = 300;
            let minHeight = 100;
            let rowWidth = 250;
            let rowHeight = 30;
            let totalWidth = this._tempText.width;
            if(totalWidth > rowWidth){
                var rowNum = Math.ceil(totalWidth / rowWidth);
                var toWidth = maxWidth;
                var toHeight = rowHeight * rowNum + 40;
            }else{
                var toWidth = Math.min(maxWidth, totalWidth + 50);
                var toHeight = rowHeight + 40;
            }

            toHeight = Math.max(minHeight, toHeight);
            this.size(toWidth, toHeight);
        }
        else{
            this._onSize();
        }

        this.html.innerHTML = talk;
        // this.html.height = this.html.contextHeight;
        // this.bg.height = this.html.contextHeight + 35;
        UIHTMLDiv.setAlignCenterAndValignMid(this.html);
        if (duration > 0) {
            this.timer.once(duration, this, this.onTimer);
        }else{
            this.timer.clear(this, this.onTimer);
        }
    }

    public get htmlHeight(): number {
        return this.html?.contextHeight; 
    }

    public showSk(skName: string, type: ESkeletonType){

        if(this.sk){
            this.sk.destroy();
            this.sk = null;
        }

        this.sk = SkeletonManager.ins.createSkeleton(skName, type);
        this.addChildAt(this.sk, 0);
        return this.sk;
    }

    private onTimer(): void {
        this.visible = false;
    }

    destroy(destroyChild?: boolean): void {
        if(this._tempText){
            this._tempText.destroy();
            this._tempText = null;
        }
        super.destroy(destroyChild);
    }

    // private remove(): void {
    //     this.removeSelf();
    //     this.destroy(true);
    // }
}