{"x": 0, "type": "BaseDialog", "selectedBox": 88, "selecteID": 20, "searchKey": "BaseDialog", "props": {"width": 720, "sceneColor": "#000000", "sceneBg": "laya/views/res/hzzd/HZZDDialog.png", "height": 1280}, "nodeParent": -1, "maxID": 95, "label": "BaseDialog", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 15, "type": "Box", "searchKey": "Box,boxMap", "props": {"y": 0, "x": 0, "width": 1280, "var": "boxMap", "height": 1280}, "nodeParent": 2, "label": "boxMap", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"x": 30, "type": "Box", "searchKey": "<PERSON>,mapLayer", "props": {"y": 0, "x": 0, "width": 1280, "var": "map<PERSON>ayer", "height": 1280}, "nodeParent": 3, "label": "map<PERSON>ayer", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"x": 45, "type": "Image", "searchKey": "Image,imgMaze", "props": {"y": 0, "x": 0, "width": 1280, "var": "imgMaze", "skin": "hzzd/map.jpg", "height": 1280}, "nodeParent": 6, "label": "imgMaze", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 45, "type": "Box", "searchKey": "Box,boxEntity", "props": {"var": "boxEntity"}, "nodeParent": 6, "label": "boxEntity", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}, {"x": 45, "type": "Box", "searchKey": "Box,boxMyLeftCamp", "props": {"y": 791, "x": 184, "width": 123, "var": "boxMyLeftCamp", "height": 66}, "nodeParent": 6, "label": "boxMyLeftCamp", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 14, "child": [{"x": 60, "type": "Label", "searchKey": "Label,labMyLFCamp", "props": {"width": 140, "var": "labMyLFCamp", "text": "[{s_id}]\\n上路堡垒", "stroke": 2, "leading": 2, "fontSize": 22, "color": "#00b7ff", "centerX": 0, "align": "center"}, "nodeParent": 14, "label": "labMyLFCamp", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}, {"x": 60, "type": "Text", "searchKey": "Text,labLFCampNum", "props": {"y": -22, "x": 8, "width": 86, "var": "labLFCampNum", "text": "（22人）", "stroke": 2, "height": 22, "color": "#ff4931", "align": "center"}, "nodeParent": 14, "label": "labLFCampNum", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 63, "child": []}, {"x": 60, "type": "Image", "searchKey": "Image,imgLFJoined", "props": {"y": -42.5, "x": 106, "visible": false, "var": "imgLFJoined", "skin": "hzzd/img_not_join.png", "mouseEnabled": true}, "nodeParent": 14, "label": "imgLFJoined", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 68, "child": [], "$HIDDEN": false}, {"x": 60, "type": "Image", "searchKey": "Image,imgLFFailReward", "props": {"zOrder": 10, "y": -81, "x": 77, "var": "imgLFFailReward", "skin": "hzzd/img_failed_reward.png", "scaleY": 1.1, "scaleX": 1.1}, "nodeParent": 14, "label": "imgLFFailReward", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 75, "child": [{"type": "Image", "searchKey": "Image", "props": {"y": 0, "x": 49, "skin": "common/red_point.png"}, "nodeParent": 75, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 81, "child": []}], "$HIDDEN": false}]}, {"x": 45, "type": "Box", "searchKey": "Box,boxMyMiddleCamp", "props": {"y": 869, "x": 212, "width": 121, "var": "boxMyMiddleCamp", "height": 73}, "nodeParent": 6, "label": "boxMyMiddleCamp", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 15, "child": [{"x": 60, "type": "Label", "searchKey": "Label,labMyMIDCamp", "props": {"y": 0, "x": 0, "width": 140, "var": "labMyMIDCamp", "text": "[{s_id}]\\n中路堡垒", "stroke": 2, "leading": 2, "fontSize": 22, "color": "#00b7ff", "centerX": 0, "align": "center"}, "nodeParent": 15, "label": "labMyMIDCamp", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 23, "child": []}, {"x": 60, "type": "Text", "searchKey": "Text,labMIDCampNum", "props": {"y": -22, "x": 20.5, "width": 80, "var": "labMIDCampNum", "text": "text", "stroke": 2, "height": 22, "color": "#ff4931", "align": "center"}, "nodeParent": 15, "label": "labMIDCampNum", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 66, "child": []}, {"x": 60, "type": "Image", "searchKey": "Image,imgMIDJoined", "props": {"y": -42.5, "x": 105, "visible": false, "var": "imgMIDJoined", "skin": "hzzd/img_not_join.png", "mouseEnabled": true}, "nodeParent": 15, "label": "imgMIDJoined", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 69, "child": []}, {"x": 60, "type": "Image", "searchKey": "Image,imgMIDFailReward", "props": {"zOrder": 10, "y": -55, "x": 85, "var": "imgMIDFailReward", "skin": "hzzd/img_failed_reward.png", "scaleY": 1.1, "scaleX": 1.1}, "nodeParent": 15, "label": "imgMIDFailReward", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 76, "child": [{"x": 75, "type": "Image", "searchKey": "Image", "props": {"y": 0, "x": 49, "skin": "common/red_point.png"}, "nodeParent": 76, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 82, "child": []}]}]}, {"x": 45, "type": "Box", "searchKey": "Box,boxMyRightCamp", "props": {"y": 890, "x": 343, "width": 124, "var": "boxMyRightCamp", "height": 72}, "nodeParent": 6, "label": "boxMyRightCamp", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 16, "child": [{"x": 60, "type": "Label", "searchKey": "Label,labMyRTCamp", "props": {"y": 21, "x": 0, "width": 140, "var": "labMyRTCamp", "text": "[{s_id}]\\n下路堡垒", "stroke": 2, "leading": 2, "fontSize": 22, "color": "#00b7ff", "centerX": 0, "align": "center"}, "nodeParent": 16, "label": "labMyRTCamp", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 24, "child": []}, {"x": 60, "type": "Label", "searchKey": "Label,labRTCampNum", "props": {"y": -1, "x": 19, "width": 86, "var": "labRTCampNum", "text": "label", "stroke": 2, "height": 22, "color": "#ff4931", "align": "center"}, "nodeParent": 16, "label": "labRTCampNum", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 67, "child": []}, {"x": 60, "type": "Image", "searchKey": "Image,imgRTJoined", "props": {"y": -42.5, "x": 106, "visible": false, "var": "imgRTJoined", "skin": "hzzd/img_not_join.png", "mouseEnabled": true}, "nodeParent": 16, "label": "imgRTJoined", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 70, "child": []}, {"x": 60, "type": "Image", "searchKey": "Image,imgRTFailReward", "props": {"zOrder": 10, "y": -55, "x": 85, "var": "imgRTFailReward", "skin": "hzzd/img_failed_reward.png", "scaleY": 1.1, "scaleX": 1.1}, "nodeParent": 16, "label": "imgRTFailReward", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 77, "child": [{"x": 75, "type": "Image", "searchKey": "Image", "props": {"y": 0, "x": 49, "skin": "common/red_point.png"}, "nodeParent": 77, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 83, "child": []}]}]}, {"x": 45, "type": "Box", "searchKey": "Box,boxOppLeftCamp", "props": {"y": 178, "x": 820, "width": 112, "var": "boxOppLeftCamp", "height": 62}, "nodeParent": 6, "label": "boxOppLeftCamp", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 17, "child": [{"x": 60, "type": "Label", "searchKey": "Label,labOppLFCamp", "props": {"y": 0, "x": 0, "width": 140, "var": "labOppLFCamp", "text": "[{s_id}]\\n上路堡垒", "stroke": 2, "leading": 2, "fontSize": 22, "color": "#ff6262", "centerX": 0, "align": "center"}, "nodeParent": 17, "label": "labOppLFCamp", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 25, "child": []}, {"x": 60, "type": "Image", "searchKey": "Image,imgLFWinReward", "props": {"zOrder": 10, "y": 53, "x": -48, "var": "imgLFWinReward", "skin": "hzzd/img_win_reward.png", "scaleY": 1.1, "scaleX": 1.1}, "nodeParent": 17, "label": "imgLFWinReward", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 78, "child": [{"x": 75, "type": "Image", "searchKey": "Image", "props": {"y": 31, "x": -10, "skin": "common/red_point.png"}, "nodeParent": 78, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 84, "child": []}]}]}, {"x": 45, "type": "Box", "searchKey": "Box,boxOppMiddleCamp", "props": {"y": 229, "x": 939, "width": 116, "var": "boxOppMiddleCamp", "height": 61}, "nodeParent": 6, "label": "boxOppMiddleCamp", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 18, "child": [{"x": 60, "type": "Label", "searchKey": "Label,labOppMIDCamp", "props": {"y": 0, "x": 0, "width": 140, "var": "labOppMIDCamp", "text": "[{s_id}]\\n中路堡垒", "stroke": 2, "leading": 2, "fontSize": 22, "color": "#ff6262", "centerX": 0, "align": "center"}, "nodeParent": 18, "label": "labOppMIDCamp", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": []}, {"x": 60, "type": "Image", "searchKey": "Image,imgMIDWinReward", "props": {"zOrder": 10, "y": 49, "x": -53, "var": "imgMIDWinReward", "skin": "hzzd/img_win_reward.png", "scaleY": 1.1, "scaleX": 1.1}, "nodeParent": 18, "label": "imgMIDWinReward", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 79, "child": [{"x": 75, "type": "Image", "searchKey": "Image", "props": {"y": 31, "x": -10, "skin": "common/red_point.png"}, "nodeParent": 79, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 85, "child": []}]}]}, {"x": 45, "type": "Box", "searchKey": "Box,boxOppRightCamp", "props": {"y": 300, "x": 983, "width": 139, "var": "boxOppRightCamp", "height": 65}, "nodeParent": 6, "label": "boxOppRightCamp", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 19, "child": [{"x": 60, "type": "Label", "searchKey": "Label,labOppRTCamp", "props": {"y": 0, "x": 0, "width": 140, "var": "labOppRTCamp", "text": "[{s_id}]\\n下路堡垒", "stroke": 2, "leading": 2, "fontSize": 22, "color": "#ff6262", "centerX": 0, "align": "center"}, "nodeParent": 19, "label": "labOppRTCamp", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": []}, {"x": 60, "type": "Image", "searchKey": "Image,imgRTWinReward", "props": {"zOrder": 10, "y": 57, "x": -20, "var": "imgRTWinReward", "skin": "hzzd/img_win_reward.png", "scaleY": 1.1, "scaleX": 1.1}, "nodeParent": 19, "label": "imgRTWinReward", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 80, "child": [{"x": 75, "type": "Image", "searchKey": "Image", "props": {"y": 31, "x": -10, "skin": "common/red_point.png"}, "nodeParent": 80, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 86, "child": []}]}]}, {"x": 45, "type": "UIView", "source": "res/hzzd/HZZDTeamNumItem.scene", "searchKey": "UIView,itemNum1", "props": {"y": 188, "x": 242, "visible": false, "var": "itemNum1", "pivotY": 39, "pivotX": 71}, "nodeParent": 6, "label": "itemNum1", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 49, "child": [], "$HIDDEN": false}, {"x": 45, "type": "UIView", "source": "res/hzzd/HZZDTeamNumItem.scene", "searchKey": "UIView,itemNum2", "props": {"y": 489, "x": 662, "visible": false, "var": "itemNum2", "pivotY": 39, "pivotX": 71}, "nodeParent": 6, "label": "itemNum2", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 50, "child": [], "$HIDDEN": false}, {"x": 45, "type": "UIView", "source": "res/hzzd/HZZDTeamNumItem.scene", "searchKey": "UIView,itemNum3", "props": {"y": 894, "x": 1023, "visible": false, "var": "itemNum3", "pivotY": 39, "pivotX": 71}, "nodeParent": 6, "label": "itemNum3", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 51, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Image", "searchKey": "Image,imgWu", "props": {"y": 82.5, "x": 627, "var": "imgWu", "skin": "hzzd/img_wu.png"}, "nodeParent": 6, "label": "imgWu", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 53, "child": [], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 30, "type": "HScrollBar", "searchKey": "HScrollBar,barMap", "props": {"visible": false, "var": "barMap", "skin": "common/hscroll.png", "right": 0, "left": 0, "bottom": 0}, "nodeParent": 3, "label": "barMap", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": [], "$HIDDEN": true}], "$HIDDEN": false}, {"x": 15, "type": "Box", "searchKey": "Box,boxTop", "props": {"y": 83, "x": 149, "width": 416, "var": "boxTop"}, "nodeParent": 2, "label": "boxTop", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 35, "child": [{"type": "Image", "searchKey": "Image", "props": {"y": 0, "x": 0, "skin": "hzzd/img_bg_result.png"}, "nodeParent": 35, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 52, "child": []}, {"type": "Image", "searchKey": "Image,imgResult1_1", "props": {"y": 39, "x": 36, "visible": false, "var": "imgResult1_1", "skin": "common/jiedian2.png"}, "nodeParent": 35, "label": "imgResult1_1", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 38, "child": [], "$HIDDEN": false}, {"type": "Image", "searchKey": "Image,imgResult1_2", "props": {"y": 39, "x": 78, "visible": false, "var": "imgResult1_2", "skin": "common/jiedian2.png"}, "nodeParent": 35, "label": "imgResult1_2", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 39, "child": []}, {"type": "Image", "searchKey": "Image,imgResult1_3", "props": {"y": 39, "x": 120, "visible": false, "var": "imgResult1_3", "skin": "common/jiedian2.png"}, "nodeParent": 35, "label": "imgResult1_3", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 42, "child": []}, {"type": "Image", "searchKey": "Image,imgResult2_1", "props": {"y": 39, "x": 272, "visible": false, "var": "imgResult2_1", "skin": "common/jiedian2.png"}, "nodeParent": 35, "label": "imgResult2_1", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 43, "child": [], "$HIDDEN": false}, {"type": "Image", "searchKey": "Image,imgResult2_2", "props": {"y": 39, "x": 314, "visible": false, "var": "imgResult2_2", "skin": "common/jiedian2.png"}, "nodeParent": 35, "label": "imgResult2_2", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 44, "child": []}, {"type": "Image", "searchKey": "Image,imgResult2_3", "props": {"y": 39, "x": 356, "visible": false, "var": "imgResult2_3", "skin": "common/jiedian2.png"}, "nodeParent": 35, "label": "imgResult2_3", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 45, "child": []}, {"type": "Image", "searchKey": "Image,imgLFResult", "props": {"y": 24, "x": -83, "var": "imgLFResult", "skin": "hzzd/img_win2.png"}, "nodeParent": 35, "label": "imgLFResult", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 58, "child": []}, {"type": "Image", "searchKey": "Image,imgRTResult", "props": {"y": 24, "x": 415, "var": "imgRTResult", "skin": "hzzd/img_win2.png"}, "nodeParent": 35, "label": "imgRTResult", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 73, "child": []}, {"type": "Image", "searchKey": "Image,imgTopTipBg", "props": {"y": 113, "x": 57, "width": 302, "var": "imgTopTipBg", "skin": "common/title_bg.png", "sizeGrid": "0,30,0,50", "height": 34, "centerX": -3}, "nodeParent": 35, "label": "imgTopTipBg", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 62, "child": []}, {"type": "Label", "searchKey": "Label,labTopTip", "props": {"y": 121, "x": 109, "var": "labTopTip", "text": "09:27:49后截止报名", "fontSize": 22, "color": "#0cff00", "centerX": -3}, "nodeParent": 35, "label": "labTopTip", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 56, "child": []}, {"type": "Image", "searchKey": "Image,imgBattleTime", "props": {"y": 154, "x": 48, "width": 321, "var": "imgBattleTime", "skin": "common/title_bg.png", "sizeGrid": "0,30,0,50", "height": 37, "centerX": -3}, "nodeParent": 35, "label": "imgBattleTime", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 71, "child": [], "$HIDDEN": false}, {"type": "Label", "searchKey": "Label,labBattleTime", "props": {"y": 163, "x": 84, "var": "labBattleTime", "text": "第1轮战斗倒计时：01:59", "fontSize": 22, "color": "#ffffff", "centerX": -4}, "nodeParent": 35, "label": "labBattleTime", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 72, "child": [], "$HIDDEN": false}, {"type": "<PERSON><PERSON>", "searchKey": "Button,helpbtn", "props": {"y": 28, "x": 512, "width": 46, "stateNum": "1", "skin": "common3/wenhao.png", "name": "helpbtn", "label": "WZXG", "height": 46}, "nodeParent": 35, "label": "helpbtn", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": [], "$LOCKED": false, "$HIDDEN": false}], "$HIDDEN": false}, {"x": 15, "type": "Box", "searchKey": "Box,boxBottom", "props": {"y": 1083, "x": 0, "width": 720, "var": "boxBottom", "height": 194}, "nodeParent": 2, "label": "boxBottom", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 74, "child": [{"x": 30, "type": "Image", "searchKey": "Image", "props": {"y": 0, "x": 0, "width": 720, "skin": "hzzd/img_bg2.png", "sizeGrid": "10,10,10,10", "height": 194}, "nodeParent": 74, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 54, "child": []}, {"x": 30, "type": "Label", "searchKey": "Label,labBottomTip1", "props": {"y": 15, "x": 252, "var": "labBottomTip1", "text": "09:27:49后截止报名", "fontSize": 24, "color": "#ffb94b", "centerX": 0}, "nodeParent": 74, "label": "labBottomTip1", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 30, "type": "Label", "searchKey": "Label,labBottomTip2", "props": {"y": 44, "width": 400, "var": "labBottomTip2", "text": "报名截止后，未报名的本服玩家无法参加战斗,\\n报名阶段退出家族将取消报名，需加入家族重新报名\\n其他阶段退出家族将按原家族进行参赛", "height": 19, "fontSize": 19, "color": "#0cff00", "centerX": 0, "align": "center"}, "nodeParent": 74, "label": "labBottomTip2", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "searchKey": "Button,btnSign", "props": {"y": -37, "x": 360, "var": "btnSign", "skin": "common/btnYellow.png", "label": "点击报名"}, "nodeParent": 74, "label": "btnSign", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "searchKey": "Button,close", "props": {"y": 62, "x": 88, "stateNum": "1", "skin": "common3/btn_back3.png", "name": "close"}, "nodeParent": 74, "label": "close", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": [], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 15, "type": "Box", "searchKey": "Box,boxLeftBottom", "props": {"y": 650, "x": 10, "width": 100, "var": "boxLeftBottom", "mouseThrough": true, "height": 435}, "nodeParent": 2, "label": "boxLeftBottom", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 87, "child": [{"type": "<PERSON><PERSON>", "searchKey": "<PERSON><PERSON>,btnStrategy", "props": {"y": 281, "x": -2, "var": "btnStrategy", "stateNum": "1", "skin": "hzzd/btn_strategy.png"}, "nodeParent": 87, "label": "btnStrategy", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": [], "$HIDDEN": false}, {"type": "<PERSON><PERSON>", "searchKey": "Button,btnTravel", "props": {"y": 190, "x": -2, "var": "btnTravel", "stateNum": "1", "skin": "hzzd/btn_travel.png"}, "nodeParent": 87, "label": "btnTravel", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 31, "child": [], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 15, "type": "Box", "searchKey": "Box,boxRightBottom", "props": {"y": 650, "x": 610, "width": 100, "var": "boxRightBottom", "height": 435}, "nodeParent": 2, "label": "boxRightBottom", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 88, "child": [{"x": 30, "type": "<PERSON><PERSON>", "searchKey": "Button,btnAchievement", "props": {"y": -3, "x": 2, "var": "btnAchievement", "stateNum": "1", "skin": "hzzd/btn_achievement.png"}, "nodeParent": 88, "label": "btnAchievement", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 28, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "searchKey": "Button,btnShop", "props": {"y": 99, "x": 6, "var": "btnShop", "stateNum": "1", "skin": "hzzd/btn_shop.png"}, "nodeParent": 88, "label": "btnShop", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 32, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "searchKey": "Button,btnGuess", "props": {"y": -86, "x": 8, "var": "btnGuess", "stateNum": "1", "skin": "hzzd/btn_guess.png"}, "nodeParent": 88, "label": "btnGuess", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "searchKey": "Button,btnLineUp", "props": {"y": 190, "x": 2, "var": "btnLineUp", "stateNum": "1", "skin": "hzzd/btn_lineup.png"}, "nodeParent": 88, "label": "btnLineUp", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "searchKey": "Button,btnSchedule", "props": {"y": 281, "x": 2, "var": "btnSchedule", "stateNum": "1", "skin": "hzzd/btn_schedule.png"}, "nodeParent": 88, "label": "btnSchedule", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 33, "child": [], "$HIDDEN": false}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}