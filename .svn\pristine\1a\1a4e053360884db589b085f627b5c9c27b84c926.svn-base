import {CCMessage} from "../CCMessage";
import { Byte } from "laya/utils/Byte";


export class p_lord_skill extends CCMessage
    {
        id:number = 0;
        level:number = 0;
        rate:number[] = [];

       constructor()
       {
         super();
         
       }

       pack(result:Byte):void 
       {
         result.writeInt32(this.id);
         result.writeInt32(this.level);
         let ratelen:number = this.rate.length;
         result.writeUint16(ratelen);
         for(let rateItem of this.rate)
         {
            result.writeInt32(rateItem);
         }
       }

       unpack(result:Byte):void 
       {
         this.id = result.readInt32();
         this.level = result.readInt32();
         let ratelen:number = result.readUint16();
         for (let rateIndex:number = 0; rateIndex<ratelen; rateIndex++)
         {
            this.rate.push(result.readInt32());
         }
       }
}
