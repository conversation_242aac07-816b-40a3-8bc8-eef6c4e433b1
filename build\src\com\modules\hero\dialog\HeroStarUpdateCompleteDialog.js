import { ConfigManager } from "../../../managers/ConfigManager";
import { com } from "../../../ui/layaMaxUI";
import { UIList } from "../../baseModules/UIList";
import { FightAttrVO } from "../../role/vo/FightAttrVO";
import { SkillItemVo } from "../../skill/vo/SkillItemVo";
import { HeroConsts } from "../data/HeroConsts";
import { HeroDataCenter } from "../data/HeroDataCenter";
import { HeroUtil } from "../util/HeroUtil";
import HeroStageItem from "../view/stageUpdate/HeroStageItem";
import { HeroStarStageItem } from "../view/starUpdate/HeroStarStageItem";
//界面类都是每次打开每次新建的
export default class HeroStarUpdateCompleteDialog extends com.ui.res.heroInfo.HeroStarUpdateCompleteDialogUI {
    constructor() {
        super();
        // this.resName = UrlConfig.HERO_UPDATE_RES;
        //有大底图的都要单独加载，不要打包到图集
        this.navShow = 0 /* NONE */;
    }
    initUI() {
        this._topIns = this.topIns;
        this._topIns.setTitleIcon("img_tips_title3.png");
        this._oldHeroItem = this.oldHeroItem;
        this._newHeroItem = this.newHeroItem;
        this._heroItem = this.heroItem;
        this._itemList = [
            this.attrItem1,
            this.attrItem2,
            this.attrItem3,
            this.attrItem4,
        ];
        this._skillItem = [
            this.skillItem1,
            this.skillItem2,
            this.skillItem3,
            this.skillItem4,
        ];
        this._curSkillItem = this.curSkillItem;
        this._nextSkillItem = this.nextSkillItem;
        this._newSkillItem = this.newSkillItem;
        this._stageList = UIList.SetUIList(this, this.stageBox, HeroStageItem, null);
        this._stageList.SetRepeat(10, 1);
        this._stageList.SetSpace(5, 0);
        this._stageList.scrollBarHide = true;
        this._stageList.isBoxCenter = true;
        this._starStageList = UIList.SetUIList(this, this.starStageBox, HeroStarStageItem, null);
        this._starStageList.SetRepeat(10, 1);
        this._starStageList.SetSpace(5, 0);
        this._starStageList.scrollBarHide = true;
        this._starStageList.isBoxCenter = true;
    }
    onOpen(param) {
        this.msg = param[0];
        let oldStage = param[1];
        let oldStar = param[2];
        let oldSkillList = param[3];
        let oldStarStage = param[4];
        let isActiveSkill = !!param[5];
        let heroId = this.msg.hero_id;
        this._heroInfo = HeroDataCenter.instance.getHero(heroId);
        //edit by lyj 23/07/18
        if (!this._heroInfo) {
            this.close();
            return;
        }
        //edit by lyj 23/07/18
        let typeId = this._heroInfo.type_id;
        let heroBaseCfg = ConfigManager.cfg_hero_baseCache.get(typeId);
        let curLevel = this._heroInfo.level;
        let curStage = this._heroInfo.stage;
        let curStar = this._heroInfo.star;
        let curStarStage = this._heroInfo.star_stage;
        this._oldHeroItem.setSimpleInfoByCfg(heroBaseCfg, oldStar, curLevel, heroBaseCfg.name);
        this._newHeroItem.setSimpleInfoByCfg(heroBaseCfg, curStar, curLevel, heroBaseCfg.name);
        this._heroItem.setSimpleInfoByCfg(heroBaseCfg, curStar, curLevel, heroBaseCfg.name);
        //战力
        this.curLabel.text = this.msg.old_power.toString();
        this.nextLabel.text = this.msg.new_power.toString();
        //等级上限
        let maxStage = Math.floor(ConfigManager.getHeroMaxUpgradeStage(curStar));
        let curMaxLevel = ConfigManager.GetHeroMaxUpgradeLevel(oldStar, oldStage, oldStarStage);
        let nextMaxLevel = ConfigManager.GetHeroMaxUpgradeLevel(curStar, curStage, curStarStage);
        if (oldStar >= HeroConsts.openStarStageLV) {
            this.setStarStage([this.msg.new_level], [ConfigManager.getCfgHeroStarStage(typeId, oldStar + 1, this.msg.new_level)], oldStar + 1);
        }
        else {
            this.setStarLv(this._stageList, curStage, maxStage, true);
        }
        this.levelItem.curLabel.text = curMaxLevel.toString();
        this.levelItem.nextLabel.text = nextMaxLevel.toString();
        this.levelItem.visible = isActiveSkill;
        this.levelItem.bgImg.visible = false;
        let startY = 97;
        if (isActiveSkill) {
            startY += 47;
            this.skillBox.y += 47;
            this.topIns.height += 47;
        }
        //属性
        let cur_attrs = HeroUtil.getHeroFinalAttr(typeId, curLevel, oldStage, oldStar, oldStarStage);
        let next_attrs = HeroUtil.getHeroFinalAttr(typeId, curLevel, curStage, curStar, curStarStage);
        for (let i = 0; i < cur_attrs.length; i++) {
            let curAttrVo = cur_attrs[i];
            let nextAttrVo = next_attrs[i];
            let item = this._itemList[i];
            if (item) {
                item.y = startY;
                item.bgImg.visible = false;
                item.descLabel.text = curAttrVo.name + "：";
                item.curLabel.text = FightAttrVO.formatValLimitLen(curAttrVo.val, curAttrVo.val_type, { fractionDigits: 0 });
                item.nextLabel.text = FightAttrVO.formatValLimitLen(nextAttrVo.val, nextAttrVo.val_type, { fractionDigits: 0 });
                startY += 47;
            }
        }
        //技能
        let newSkillList = this.msg.skill_list;
        let oldSkillKVMap = {};
        oldSkillList.forEach(function (kv) {
            oldSkillKVMap[kv.key] = kv.val;
        });
        let activeSkillKV = null;
        let updateSkillList = [];
        newSkillList.forEach(function (newKV) {
            if (oldSkillKVMap[newKV.key] != newKV.val) {
                updateSkillList.push(newKV);
            }
            if (!activeSkillKV) {
                for (let i = 0; i < oldSkillList.length; ++i) {
                    let oldKV = oldSkillList[i];
                    if (oldKV.key == newKV.key) {
                        return;
                    }
                }
                activeSkillKV = newKV;
            }
        });
        this.allSkillBox.visible = false;
        this.singleSkillBox.visible = false;
        this.emptyLabel.visible = false;
        this.newSkillItem.visible = false;
        this._stageList.visible = false;
        this._starStageList.visible = !isActiveSkill && oldStar >= HeroConsts.openStarStageLV;
        if (!isActiveSkill && oldStar >= HeroConsts.openStarStageLV) {
            this.heroItem.visible = true;
            this.oldHeroItem.visible = false;
            this.newHeroItem.visible = false;
            this._topIns.setTitleIcon("img_tips_title6.png");
        }
        if (isActiveSkill) {
            this._topIns.setTitleIcon("img_tips_title4.png");
            this.heroItem.visible = true;
            this.oldHeroItem.visible = false;
            this.newHeroItem.visible = false;
            this.newSkillItem.visible = true;
            this._stageList.visible = true;
            this.titleLabel2.text = window.iLang.L2_JI_NENG_JIE_SUO.il();
            this.refreshSkillItem(this.newSkillItem, activeSkillKV.key, activeSkillKV.val, this._heroInfo, true);
        }
        else if (updateSkillList.length > 1) {
            this.allSkillBox.visible = true;
            let totalWidth = 0;
            let skillLen = Math.max(this._skillItem.length, updateSkillList.length);
            for (let i = 0; i < skillLen; ++i) {
                let skillItem = this._skillItem[i];
                let skillKV = updateSkillList[i];
                if (skillItem && skillKV) {
                    this.refreshSkillItem(skillItem, skillKV.key, skillKV.val, this._heroInfo);
                    totalWidth += skillItem.width * skillItem.scaleX + 7;
                }
                else if (skillItem) {
                    skillItem.visible = false;
                }
            }
            let startX = (this.allSkillBox.width - (totalWidth - 7)) / 2;
            for (let i = 0; i < this._skillItem.length; ++i) {
                let skillItem = this._skillItem[i];
                if (skillItem.visible) {
                    skillItem.x = startX;
                    startX += skillItem.width * skillItem.scaleX + 7;
                }
            }
        }
        else if (updateSkillList.length > 0) {
            this.singleSkillBox.visible = true;
            let skillKV = updateSkillList[0];
            let skill_lv = oldSkillKVMap[skillKV.key] || 0;
            this.refreshSkillItem(this._curSkillItem, skillKV.key, skill_lv, this._heroInfo);
            this.refreshSkillItem(this._nextSkillItem, skillKV.key, skillKV.val, this._heroInfo);
        }
        else {
            this.emptyLabel.visible = true;
        }
    }
    addClick() {
        // this.addOnClick(this, this, this.onClick);
    }
    onClick() {
        this.close();
    }
    onClose() {
        if (this.msg.up_type == 3) {
            // this.dispatchEvent(ModuleCommand.SHOW_HERO_UPGRADE_STAR_LEGENDS_TIPS, this.msg);
        }
        else if (this.msg.up_type == 5) {
            this.dispatchEvent("CLOSE_HERO_STAR_UP_GROWTH_ROAD_DIALOG" /* CLOSE_HERO_STAR_UP_GROWTH_ROAD_DIALOG */);
        }
    }
    close(type = null) {
        super.close(type);
    }
    /**设置英雄星级 */
    setStarLv(list, level = 0, maxStage = 0, showEffect) {
        let arr = [];
        for (let i = 0; i < maxStage; i++) {
            arr.push(i + 1);
        }
        list.visible = true;
        list.AddOtherParamete("curStage", level);
        list.array = arr;
    }
    /**设置英雄星阶 */
    setStarStage(starStageList, starStageCfg, star = 0) {
        this._starStageList.parameter = {
            starStageList: starStageList,
            star: star,
        };
        this._starStageList.array = starStageCfg;
    }
    refreshSkillItem(skill_item, skill_id, skill_lv, hero_info, isNew = false) {
        let skill_item_vo = new SkillItemVo();
        skill_item_vo.skill_id = skill_id;
        skill_item_vo.skill_level = skill_lv;
        skill_item_vo.heroInfo = hero_info;
        skill_item_vo.id_level_map[skill_id] = skill_lv;
        skill_item_vo.isGrayWhenNotActive = false;
        skill_item_vo.isShowLevel = true;
        skill_item_vo.isCanClick = true;
        skill_item_vo.isShowName = false;
        skill_item_vo.isNew = isNew;
        skill_item.setDataBySkillItemVO(skill_item_vo);
    }
}
