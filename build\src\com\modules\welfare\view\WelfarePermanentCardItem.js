import { UrlConfig } from "../../../../game/UrlConfig";
import { com } from "../../../ui/layaMaxUI";
import { GameUtil } from "../../../util/GameUtil";
import { YueKaDataCenter } from "../data/YueKaDataCenter";
import { UIList } from "../../baseModules/UIList";
import GoodsItem from "../../goods/GoodsItem";
import { GoodsVO } from "../../goods/GoodsVO";
import { TipsUtil } from "../../../util/TipsUtil";
import { PaymentDataCenter } from "../../payment/data/PaymentDataCenter";
import { WelfareDataCenter } from "../data/WelfareDataCenter";
import { StringUtil } from "../../../util/StringUtil";
import { MiscConstAuto } from "../../../auto/MiscConstAuto";
import { ItemConst } from "../../goods/ItemConst";
import { RedPointMgr } from "../../redPoint/RedPointMgr";
import { ShopDataCenter } from "../../shop/data/ShopDataCenter";
import { EYueKaType } from "../../../auto/ConstAuto";
export class WelfarePermanentCardItem extends com.ui.res.welfare.WelfarePermanentCardItemUI {
    constructor() {
        super(...arguments);
        this.state = 0;
    }
    initUI() {
        this.rewardList = UIList.SetUIList(this, this.rewardBox, GoodsItem);
        this.rewardList.SetRepeat(3, 1);
        this.rewardList.SetSpace(10);
        this.rewardList.isBoxCenter = true;
    }
    setDataSource(data, index, parameter) {
        this.UpdateItem(data);
    }
    UpdateItem(itemData) {
        if (!itemData)
            return;
        let cfg = itemData;
        let yuekaInfo = YueKaDataCenter.instance.getFuliYuekaInfo(cfg.yueka_type);
        if (!yuekaInfo)
            return;
        this.permanentCard = yuekaInfo;
        this.lbTitle.text = "超值永久特权";
        this.lbTime.text = "每周周一可领取";
        this.lbTips.text = "购买立即获得";
        this.lbMaxTime.text = StringUtil.Format("未领取奖励最多存储{0}周", MiscConstAuto.permanent_yueka_save_weeks);
        this.imgBg.skin = UrlConfig.BASE_RES_UI_URL + "welfare/card_" + this.permanentCard.type + ".png";
        let dataList = [];
        this.paymentItemVo = PaymentDataCenter.instance.getPaymentVoByID(this.permanentCard.shop_id).list.filter(payMentItem => payMentItem.item_id == this.permanentCard.goods_id)[0];
        this.state = YueKaDataCenter.instance.getPremanentCardState(this.permanentCard.type);
        this.lbTips.visible = this.state == 0;
        RedPointMgr.ins.SetRedPoint(this.btnOP, this.state == 1);
        if (this.state == 0) {
            this.btnOP.label = window.iLang.L_P0_RMB.il([this.paymentItemVo.price]);
            dataList = GoodsVO.GetPItemToVos(this.paymentItemVo.item_list);
        }
        else {
            this.btnOP.label = this.state == 1 ? window.iLang.L2_LING_ch11_QU.il() : "周一再来";
            this.btnOP.disabled = this.state == 2;
            dataList = GameUtil.parseRewards(cfg, "extra_reward_");
            for (const goods of dataList) {
                if (goods.typeId == ItemConst.ITEM_LOTTERY_MISSION) {
                    goods.num = goods.num * this.permanentCard.weeks;
                    break;
                }
            }
        }
        this.rewardList.array = dataList;
        let isSupportVideoAdReward = ShopDataCenter.instance.isSupportVideoAdReward() && cfg.yueka_type == EYueKaType.TYPE_101_FOREVER;
        this.bgDesc4.visible = isSupportVideoAdReward;
    }
    addClick() {
        this.addOnClick(this, this.btnOP, this.onClickOP);
    }
    onClickOP() {
        if (this.state == 0) {
            TipsUtil.showDialog(this, "是否购买永久招募卡", window.iLang.L2_TI_ch11_SHI.il(), () => {
                PaymentDataCenter.instance.do_coupon_buy(this.paymentItemVo.price, this.permanentCard.shop_id, this.permanentCard.goods_id);
            });
        }
        else if (this.state == 1) {
            WelfareDataCenter.instance.m_fuli_yueka_fetch_tos(this.permanentCard.type);
        }
    }
    Clean() {
    }
}
