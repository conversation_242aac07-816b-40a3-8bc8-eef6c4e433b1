export class MatchConst {
}
MatchConst.MATCH_TYPE_MAIN_BATTLE = 101; //征战关卡
MatchConst.MATCH_TYPE_ARENA = 102; //竞技场
MatchConst.MATCH_TYPE_CONTEST = 103; //切磋
MatchConst.MATCH_TYPE_GM_MONSTER = 110; //GM怪物组战斗
MatchConst.MATCH_TYPE_GM_EXPORT_ROLE = 111; //GM挑战镜像
MatchConst.MATCH_TYPE_GM_EXPORT_ROLE_MULTI = 112; //GM挑战多阵容镜像
MatchConst.MATCH_TYPE_TEST_TOWER = 115; //试练塔
MatchConst.MATCH_TYPE_FAMILY_BOSS = 116; //公会副本
MatchConst.MATCH_TYPE_RED_CLIFF = 117; //元素圣殿
MatchConst.MATCH_TYPE_GUAN_DU = 118; //暗黑地牢
MatchConst.MATCH_TYPE_CROSS_FAMILY_SOLO = 119; //公会战
MatchConst.MATCH_TYPE_WORLD_BOSS = 120; //深渊入侵
MatchConst.MATCH_TYPE_GHOST_TOWER = 121; //神魔塔
MatchConst.MATCH_TYPE_CROSS_CONTEST = 122; //战区争霸切磋
MatchConst.MATCH_TYPE_SKY_TOWER = 123; //天选塔
MatchConst.MATCH_TYPE_CROSS_FRIEND_CONTEST = 124; //战区好友切磋
MatchConst.MATCH_TYPE_CROSS_DOMINATE_CONTEST = 125; //诸神战场切磋
MatchConst.MATCH_TYPE_DAILY_FB = 201; //日常副本
MatchConst.MATCH_TYPE_GOD_TRIAL = 501; //无尽试炼
MatchConst.MATCH_TYPE_ARES_PALACE = 502; //神位争夺
MatchConst.MATCH_TYPE_PASS_BEHEAD = 503; //勇闯异境
MatchConst.MATCH_TYPE_QXZL = 504; //冠军赛
MatchConst.MATCH_TYPE_HZZD = 505; //王者峡谷战
MatchConst.MATCH_TYPE_PEAK = 506; //巅峰赛
MatchConst.MATCH_TYPE_CROSS_LADDER = 507; //荣耀天梯赛
MatchConst.MATCH_TYPE_BATTLE_TRIAL = 508; //组队征战
MatchConst.MATCH_TYPE_LARGE_PEAK = 509; //战场主宰
MatchConst.MATCH_TYPE_NATION_WAR = 510; //尾兽争夺战
MatchConst.MATCH_TYPE_FIGHT_SHOW = 601; //战斗展示
MatchConst.MATCH_TYPE_NEW_STORY = 602; //新手剧情动画演示
MatchConst.MATCH_TYPE_LCQS = 701; //神装纷争
MatchConst.MATCH_TYPE_CS_RANDOM_PVP = 801; //战区争霸
MatchConst.MATCH_TYPE_MAZE = 802; //主题玩法
MatchConst.MATCH_TYPE_MAZE_ACT = 803; //主题玩法
MatchConst.MATCH_TYPE_DICE_ACT = 804; //密境藏宝殿
MatchConst.MATCH_TYPE_XSWH = 805; //跨服首领战
MatchConst.MATCH_TYPE_HERO_CHALLENGE = 806; //主题英雄挑战
MatchConst.MATCH_TYPE_STORY_ACT = 807; //剧情活动战斗
MatchConst.MATCH_TYPE_TEAM_XSWH = 808; //组队首领战
MatchConst.MATCH_TYPE_DOMINATE_PVP = 811; //诸神战场
MatchConst.MATCH_TYPE_NATION_TOWER_1 = 901; //跨服试炼场・冰
MatchConst.MATCH_TYPE_NATION_TOWER_2 = 902; //跨服试炼场・森
MatchConst.MATCH_TYPE_NATION_TOWER_3 = 903; //跨服试炼场・炎
MatchConst.MATCH_TYPE_NATION_TOWER_4 = 904; //跨服试炼场・神
MatchConst.MATCH_TYPE_NATION_TOWER_5 = 905; //跨服试炼场・魔
MatchConst.MATCH_TYPE_MASTER_CARD = 1001; //魂卡晋升考核
MatchConst.MATCH_TYPE_FIGHT_PLOT = 9999; //剧情战斗
MatchConst.MATCH_TYPE_BOAT_RACE = 551; //公会竞渡
MatchConst.MATCH_TYPE_STORY_MAZE = 552; //争霸副本
MatchConst.MATCH_TYPE_STAGE_COPY = 553; //成神之路副本
MatchConst.MATCH_TYPE_MOCK_BATTLE = 554; //模拟对战
MatchConst.MATCH_TYPE_TD_TRIAL = 555; //一个都不能跑
MatchConst.MATCH_TYPE_TD_MAIN = 556; //征战塔防
MatchConst.MATCH_TYPE_TD_DAILY_COPY = 557; //日常副本
MatchConst.MATCH_TYPE_DIVINE_COPY = 1101; //神临副本
MatchConst.MATCH_TYPE_CROSS_CLAN_SOLO = 1102; //家族战
export class RankConst {
}
RankConst.RANK_KEY_FAST_POWER = 803; //快速排行榜
RankConst.RANK_KEY_MAIN_BATTLE = 202; //普通排行榜
RankConst.RANK_KEY_SKY_TOWER = 813; //快速排行榜
RankConst.RANK_KEY_TEST_TOWER = 801; //快速排行榜
RankConst.RANK_KEY_GHOST_TOWER = 800; //快速排行榜
RankConst.RANK_KEY_ARENA = 101; //特殊排行榜
RankConst.RANK_KEY_FAMILY_POWER = 701; //公会排行榜
RankConst.RANK_KEY_GOD_TRIAL = 802; //快速排行榜
RankConst.RANK_KEY_FAMILY_BOSS = 901; //公会副本排行榜（写死占用key，不走通用）
RankConst.RANK_KEY_RED_CLIFF_FAST = 804; //元素圣殿排行榜
RankConst.RANK_KEY_FAMILY_INTERIOR_HONGBAO = 301; //公会内部红包排行榜
RankConst.RANK_KEY_GUANDU_PASS_FAST = 805; //黄巾之战排行榜
RankConst.RANK_KEY_QXZL_FAST = 806; //冠军赛排行榜
RankConst.RANK_KEY_CSC_FMSOLO = 807; //公会战战绩排行榜
RankConst.RANK_KEY_CSC_CLANSOLO = 8071; //家族战战绩排行榜
RankConst.RANK_KEY_LCQS = 808; //快速排行榜
RankConst.RANK_KEY_QXZL_CROSS_FAST = 809; //冠军赛排行榜
RankConst.RANK_KEY_PEAK_FAST = 810; //冠军赛排行榜
RankConst.RANK_KEY_CSC_LADDER = 812; //跨服天梯排行榜
RankConst.RANK_KEY_BATTLE_TRIAL = 821; //跨服天梯排行榜
RankConst.RANK_KEY_LARGE_AUDITION_PEAK_1 = 83101; //百服主宰海选赛排行榜
RankConst.RANK_KEY_LARGE_AUDITION_PEAK_2 = 83102; //百服主宰海选赛排行榜
RankConst.RANK_KEY_LARGE_AUDITION_PEAK_3 = 83103; //百服主宰海选赛排行榜
RankConst.RANK_KEY_LARGE_AUDITION_PEAK_4 = 83104; //百服主宰海选赛排行榜
RankConst.RANK_KEY_LARGE_AUDITION_PEAK_5 = 83105; //百服主宰海选赛排行榜
RankConst.RANK_KEY_LARGE_AUDITION_PEAK_6 = 83106; //百服主宰海选赛排行榜
RankConst.RANK_KEY_LARGE_AUDITION_PEAK_7 = 83107; //百服主宰海选赛排行榜
RankConst.RANK_KEY_LARGE_PEAK_1 = 83201; //百服主宰排行榜
RankConst.RANK_KEY_LARGE_PEAK_2 = 83202; //百服主宰排行榜
RankConst.RANK_KEY_LARGE_PEAK_3 = 83203; //百服主宰排行榜
RankConst.RANK_KEY_LARGE_PEAK_4 = 83204; //百服主宰排行榜
RankConst.RANK_KEY_LARGE_PEAK_5 = 83205; //百服主宰排行榜
RankConst.RANK_KEY_LARGE_PEAK_6 = 83206; //百服主宰排行榜
RankConst.RANK_KEY_LARGE_PEAK_7 = 83207; //百服主宰排行榜
RankConst.RANK_KEY_MASTER_CARD_POWER = 833; //快速排行榜
RankConst.RANK_KEY_FISH_POWER = 834; //快速排行榜
RankConst.RANK_KEY_NORMAL_WAR = 851; //尾兽战个人积分排行
RankConst.RANK_KEY_NORMAL_CAMP_WAR = 852; //尾兽战阵营积分排行
RankConst.RANK_KEY_DAY_NORMAL_WAR = 3002; //尾兽战个人每日积分排行
RankConst.RANK_KEY_DAY_NORMAL_CAMP_WAR = 3003; //尾兽战阵营每日积分排行
RankConst.RANK_KEY_ACTIVITY_QUICK_PASS = 1010; //速战排行榜
RankConst.RANK_KEY_ACTIVITY_TRAVEL = 1011; //探索排行榜
RankConst.RANK_KEY_ACTIVITY_TAX = 1012; //点金排行榜
RankConst.RANK_KEY_ACTIVITY_BEHEAD = 1013; //异境排行榜
RankConst.RANK_KEY_ACTIVITY_HUANG_JIN = 1014; //黄巾排行榜
RankConst.RANK_KEY_ACTIVITY_DRUM = 1015; //开箱排行榜
RankConst.RANK_KEY_WORLD_BOSS_ROLE_HURT = 1020; //南蛮入侵排行榜
RankConst.RANK_KEY_RANDOM_PVP = 406; //战前争霸排行
RankConst.RANK_KEY_DOMINATE_PVP_1 = 40701; //百服争霸排行
RankConst.RANK_KEY_DOMINATE_PVP_2 = 40702; //百服争霸排行
RankConst.RANK_KEY_DOMINATE_PVP_3 = 40703; //百服争霸排行
RankConst.RANK_KEY_DOMINATE_PVP_4 = 40704; //百服争霸排行
RankConst.RANK_KEY_DOMINATE_PVP_5 = 40705; //百服争霸排行
RankConst.RANK_KEY_DOMINATE_PVP_6 = 40706; //百服争霸排行
RankConst.RANK_KEY_DOMINATE_PVP_7 = 40707; //百服争霸排行
RankConst.RANK_KEY_MERGE_ACTIVE_SCORE = 5001; //合服活动：活跃积分排行榜
RankConst.RANK_KEY_XSWH_FAST = 1030; //跨服首领战七天总伤害排行榜
RankConst.RANK_KEY_XSWH_BEST_FAST_1 = 1031; //跨服领主名将堂排行榜
RankConst.RANK_KEY_XSWH_BEST_FAST_2 = 1032; //跨服领主名将堂排行榜
RankConst.RANK_KEY_XSWH_BEST_FAST_3 = 1033; //跨服领主名将堂排行榜
RankConst.RANK_KEY_XSWH_BEST_FAST_4 = 1034; //跨服领主名将堂排行榜
RankConst.RANK_KEY_XSWH_BEST_FAST_5 = 1035; //跨服领主名将堂排行榜
RankConst.RANK_KEY_XSWH_BEST_FAST_6 = 1036; //跨服领主名将堂排行榜
RankConst.RANK_KEY_XSWH_BEST_FAST_7 = 1037; //跨服领主名将堂排行榜
RankConst.RANK_KEY_TEAM_XSWH_SEASON_FAST = 1040; //组队跨服领主战赛季排行榜
RankConst.RANK_KEY_TEAM_XSWH_FAST = 1041; //组队跨服领主战每日排行榜
RankConst.RANK_KEY_TEAM_XSWH_BEST_FAST = 1042; //组队跨服领主战名将堂排行榜
RankConst.RANK_KEY_NATION_TOWER_1 = 1101; //快速排行榜
RankConst.RANK_KEY_NATION_TOWER_2 = 1102; //快速排行榜
RankConst.RANK_KEY_NATION_TOWER_3 = 1103; //快速排行榜
RankConst.RANK_KEY_NATION_TOWER_4 = 1104; //快速排行榜
RankConst.RANK_KEY_NATION_TOWER_5 = 1105; //快速排行榜
RankConst.RANK_KEY_BOAT_RACE = 2055; //公会竞渡
RankConst.RANK_KEY_BOAT_PEAK_PREVIEW = 2056; //巅峰竞渡入围赛
RankConst.RANK_KEY_BOAT_PEAK = 2057; //巅峰竞渡
RankConst.RANK_KEY_STAGE_COPY = 3001; //普通排行榜
RankConst.RANK_KEY_CSCLAN_POWER = 3004; //家族平均战力排行榜
RankConst.RANK_KEY_MOCK_PVP = 3005; //模拟对战排行榜
RankConst.RANK_KEY_TD_TRIAL = 3006; //塔防副本
RankConst.RANK_KEY_CSCLAN_INTERIOR_HONGBAO = 3007; //家族内部红包排行榜
export var EBuyTimesType;
(function (EBuyTimesType) {
    EBuyTimesType[EBuyTimesType["FAMILY_BOSS"] = 1] = "FAMILY_BOSS";
    EBuyTimesType[EBuyTimesType["RED_CLIFF"] = 2] = "RED_CLIFF";
    EBuyTimesType[EBuyTimesType["TEST_TOWER"] = 3] = "TEST_TOWER";
    EBuyTimesType[EBuyTimesType["WORLD_BOSS"] = 4] = "WORLD_BOSS";
    EBuyTimesType[EBuyTimesType["FAMILY_WAR"] = 5] = "FAMILY_WAR";
    EBuyTimesType[EBuyTimesType["CHALLENGE_CHAPTER"] = 6] = "CHALLENGE_CHAPTER";
    EBuyTimesType[EBuyTimesType["RANDOM_PVP"] = 7] = "RANDOM_PVP";
    EBuyTimesType[EBuyTimesType["CROSS_LADDER"] = 8] = "CROSS_LADDER";
    EBuyTimesType[EBuyTimesType["XSWH"] = 9] = "XSWH";
    EBuyTimesType[EBuyTimesType["SP_TEST_TOWER"] = 10] = "SP_TEST_TOWER";
    EBuyTimesType[EBuyTimesType["SKY_TEST_TOWER"] = 11] = "SKY_TEST_TOWER";
    EBuyTimesType[EBuyTimesType["NATION_TEST_TOWER"] = 12] = "NATION_TEST_TOWER";
    EBuyTimesType[EBuyTimesType["NATION_TEST_TOWER_1"] = 13] = "NATION_TEST_TOWER_1";
    EBuyTimesType[EBuyTimesType["NATION_TEST_TOWER_2"] = 14] = "NATION_TEST_TOWER_2";
    EBuyTimesType[EBuyTimesType["NATION_TEST_TOWER_3"] = 15] = "NATION_TEST_TOWER_3";
    EBuyTimesType[EBuyTimesType["NATION_TEST_TOWER_4"] = 16] = "NATION_TEST_TOWER_4";
    EBuyTimesType[EBuyTimesType["NATION_TEST_TOWER_5"] = 17] = "NATION_TEST_TOWER_5";
    EBuyTimesType[EBuyTimesType["NATION_TEAM_XSWH"] = 18] = "NATION_TEAM_XSWH";
    EBuyTimesType[EBuyTimesType["MOCK_PVP"] = 19] = "MOCK_PVP";
    EBuyTimesType[EBuyTimesType["MOCK_PVP_SPARRING"] = 20] = "MOCK_PVP_SPARRING";
    EBuyTimesType[EBuyTimesType["DOMINATE_PVP"] = 21] = "DOMINATE_PVP";
    EBuyTimesType[EBuyTimesType["DIVINE_COPY"] = 22] = "DIVINE_COPY";
    EBuyTimesType[EBuyTimesType["CSCLAN_SOLO"] = 23] = "CSCLAN_SOLO";
})(EBuyTimesType || (EBuyTimesType = {}));
export var EShopId;
(function (EShopId) {
    EShopId[EShopId["ITEM_SHOP"] = 101] = "ITEM_SHOP";
    EShopId[EShopId["BING_FA_SHOP"] = 402] = "BING_FA_SHOP";
    EShopId[EShopId["HERO_SHOP"] = 102] = "HERO_SHOP";
    EShopId[EShopId["ELIT_HERO_SHOP"] = 401] = "ELIT_HERO_SHOP";
    EShopId[EShopId["ARENA_SHOP"] = 302] = "ARENA_SHOP";
    EShopId[EShopId["FAMILY_SHOP"] = 303] = "FAMILY_SHOP";
    EShopId[EShopId["HUNT_SHOP"] = 304] = "HUNT_SHOP";
    EShopId[EShopId["HZZD_SHOP"] = 1601] = "HZZD_SHOP";
    EShopId[EShopId["RANDOM_BOSS_SHOP"] = 801] = "RANDOM_BOSS_SHOP";
    EShopId[EShopId["RANDOM_SHOP"] = 201] = "RANDOM_SHOP";
    EShopId[EShopId["HUFU_SHOP"] = 501] = "HUFU_SHOP";
    EShopId[EShopId["LOTTERY_NATION_SHOP"] = 601] = "LOTTERY_NATION_SHOP";
    EShopId[EShopId["YUNTAI_CONVERT_SHOP"] = 701] = "YUNTAI_CONVERT_SHOP";
    EShopId[EShopId["FMSOLO_SHOP_1"] = 901] = "FMSOLO_SHOP_1";
    EShopId[EShopId["FMSOLO_SHOP_2"] = 902] = "FMSOLO_SHOP_2";
    EShopId[EShopId["CLANSOLO_SHOP_1"] = 903] = "CLANSOLO_SHOP_1";
    EShopId[EShopId["CLANSOLO_SHOP_2"] = 904] = "CLANSOLO_SHOP_2";
    EShopId[EShopId["TEST_TOWER_SHOP"] = 1101] = "TEST_TOWER_SHOP";
    EShopId[EShopId["GOD_EQUIP_SHOP"] = 1201] = "GOD_EQUIP_SHOP";
    EShopId[EShopId["GOD_EQUIP_TREASURE_SHOP"] = 1202] = "GOD_EQUIP_TREASURE_SHOP";
    EShopId[EShopId["HERO_SKIN_SHOP"] = 1302] = "HERO_SKIN_SHOP";
    EShopId[EShopId["MAZE_OLD_SHOP"] = 1501] = "MAZE_OLD_SHOP";
    EShopId[EShopId["MODULAR_ACT_SHOP_MAZE"] = 1502] = "MODULAR_ACT_SHOP_MAZE";
    EShopId[EShopId["MODULAR_ACT_SHOP_DICE"] = 1503] = "MODULAR_ACT_SHOP_DICE";
    EShopId[EShopId["CROSS_LADDER_SHOP"] = 1701] = "CROSS_LADDER_SHOP";
    EShopId[EShopId["CROSS_XSWH_SHOP"] = 1801] = "CROSS_XSWH_SHOP";
    EShopId[EShopId["CROSS_TEST_TOWER_SHOP"] = 2101] = "CROSS_TEST_TOWER_SHOP";
    EShopId[EShopId["RANDOM_PVP_SHOP"] = 1901] = "RANDOM_PVP_SHOP";
    EShopId[EShopId["SAN_XIAO_SHOP"] = 2001] = "SAN_XIAO_SHOP";
    EShopId[EShopId["BATTLE_TRIAL_SHOP"] = 2201] = "BATTLE_TRIAL_SHOP";
    EShopId[EShopId["RANDOM_DOMINATE_1"] = 2301] = "RANDOM_DOMINATE_1";
    EShopId[EShopId["DOMINATE_PVP_SHOP"] = 2501] = "DOMINATE_PVP_SHOP";
    EShopId[EShopId["BOAT_RACE_SHOP"] = 5101] = "BOAT_RACE_SHOP";
    EShopId[EShopId["STAGE_COPY_SHOP"] = 5102] = "STAGE_COPY_SHOP";
    EShopId[EShopId["RIVER_TREASURE_SHOP"] = 5103] = "RIVER_TREASURE_SHOP";
    EShopId[EShopId["BOAT_PEAK_SHOP"] = 5104] = "BOAT_PEAK_SHOP";
    EShopId[EShopId["SUPREME_LOTTERY_SHOP"] = 5105] = "SUPREME_LOTTERY_SHOP";
    EShopId[EShopId["ITINERANT_SHOP"] = 2601] = "ITINERANT_SHOP";
})(EShopId || (EShopId = {}));
export var EBuildingType;
(function (EBuildingType) {
    EBuildingType[EBuildingType["BUILD_TYPE_BASE"] = 1] = "BUILD_TYPE_BASE";
    EBuildingType[EBuildingType["BUILD_TYPE_ALTAR"] = 2] = "BUILD_TYPE_ALTAR";
    EBuildingType[EBuildingType["BUILD_TYPE_GOLD_MINE"] = 3] = "BUILD_TYPE_GOLD_MINE";
    EBuildingType[EBuildingType["BUILD_TYPE_GUILD"] = 4] = "BUILD_TYPE_GUILD";
    EBuildingType[EBuildingType["BUILD_TYPE_BARRACKS"] = 5] = "BUILD_TYPE_BARRACKS";
    EBuildingType[EBuildingType["BUILD_TYPE_FORGE"] = 6] = "BUILD_TYPE_FORGE";
    EBuildingType[EBuildingType["BUILD_TYPE_CRYSTAL_MINE"] = 7] = "BUILD_TYPE_CRYSTAL_MINE";
})(EBuildingType || (EBuildingType = {}));
export var EYueKaType;
(function (EYueKaType) {
    EYueKaType[EYueKaType["TYPE_1"] = 1] = "TYPE_1";
    EYueKaType[EYueKaType["TYPE_2"] = 2] = "TYPE_2";
    EYueKaType[EYueKaType["TYPE_3"] = 3] = "TYPE_3";
    EYueKaType[EYueKaType["TYPE_4_KING"] = 4] = "TYPE_4_KING";
    EYueKaType[EYueKaType["TYPE_101_FOREVER"] = 101] = "TYPE_101_FOREVER";
})(EYueKaType || (EYueKaType = {}));
