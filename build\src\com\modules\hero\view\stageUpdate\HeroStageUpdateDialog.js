import { UrlConfig } from "../../../../../game/UrlConfig";
import { ConfigManager } from "../../../../managers/ConfigManager";
import { com } from "../../../../ui/layaMaxUI";
import { ColorUtil } from "../../../../util/ColorUtil";
import { GameUtil } from "../../../../util/GameUtil";
import { GuideMgr } from "../../../guide/GuideMgr";
import { FightAttrVO } from "../../../role/vo/FightAttrVO";
import { SkillDataCenter } from "../../../skill/data/SkillDataCenter";
import { SkillItemVo } from "../../../skill/vo/SkillItemVo";
import { HeroDataCenter } from "../../data/HeroDataCenter";
import { HeroUtil } from "../../util/HeroUtil";
export class HeroStageUpdateDialog extends com.ui.res.heroInfo.HeroStageUpdateDialogUI {
    constructor() {
        super();
        this.resName = [UrlConfig.FIGHT_ATTR_RES];
        this.navShow = 0 /* NONE */;
    }
    getClassName() {
        return "HeroStageUpdateDialog";
    }
    initUI() {
        this.topIns.setTitle(window.iLang.L2_JIN_JIE.il());
        this._skillItem = this.skillItem;
        // this._skillItem.mouseEnabled = false;
        this._updateItemList = [
            this.updateItem2,
            this.updateItem3,
            this.updateItem4,
            this.updateItem5,
        ];
        // this._list = UIList.SetUIList(this, this.valueBox, HeroLevelUpdateItem);
        // this._list.SetRepeat(1, 4);
        // this._list.SetSpace(0, 5);
    }
    onOpen(param) {
        // let curCfg = GuideMgr.ins.getCurCfg();
        // if(curCfg && curCfg.ui_name != this.name){
        //     GuideMgr.ins.setHideGuideUI(true, this.name);
        // }
        this.UpdateInfo();
    }
    checkGuide() {
        let curCfg = GuideMgr.ins.getCurCfg();
        if (curCfg && curCfg.ui_name != this.name) {
            GuideMgr.ins.setHideGuideUI(true, this.name);
        }
        super.checkGuide();
    }
    addClick() {
        this.addOnClick(this, this.updateBtn, this.OnClick);
    }
    addEvent() {
    }
    onGoodsUpdate() {
        this.refreshCost();
    }
    UpdateInfo() {
        this._select_hero_id = HeroDataCenter.instance.select_hero_id;
        this._hero_info = HeroDataCenter.instance.getHero(this._select_hero_id);
        if (!this._hero_info) {
            return;
        }
        this._cfg = ConfigManager.cfg_hero_baseCache.get(this._hero_info.type_id);
        //设置星级属性
        if (this._hero_info != null) {
            let init_star = this._cfg.init_star;
            let cur_level = this._hero_info.level;
            let cur_star = this._hero_info.star;
            let cur_stage = this._hero_info.stage;
            //当前英雄可升至的最大阶级
            let cur_max_stage = ConfigManager.getHeroMaxUpgradeStage(this._hero_info.star);
            //当前英雄可升至的最大等级
            let cur_max_level = ConfigManager.GetHeroMaxUpgradeLevel(this._hero_info.star, cur_stage, this._hero_info.star_stage);
            let next_stage = Math.min(this._hero_info.stage + 1, cur_max_stage);
            let next_max_level = ConfigManager.GetHeroMaxUpgradeLevel(this._hero_info.star, next_stage, this._hero_info.star_stage);
            let skill_id_lv_map = {};
            this._hero_info.skill_list.forEach(function (kv) {
                skill_id_lv_map[kv.key] = kv.val;
            });
            let skill_cfg = null;
            let act_skill_ids = ConfigManager.getHeroActSkills(this._hero_info.type_id);
            for (let i = 0; i < act_skill_ids.length; ++i) {
                let skill_id = act_skill_ids[i];
                let skill_lv = skill_id_lv_map[skill_id] || 0;
                let next_skill_lv = skill_lv + 1;
                if (SkillDataCenter.ins.checkSkillActiveCondition(skill_id, next_skill_lv, 3, next_stage)) {
                    skill_cfg = ConfigManager.cfg_skillCache.get(skill_id);
                    break;
                }
            }
            this.updateItem1.curLabel.text = cur_max_level.toString();
            let is_max_stage = this._hero_info.stage >= cur_max_stage;
            if (is_max_stage) {
                this.updateBtn.visible = false;
                this.emptyImg.visible = false;
                this.skillBox.visible = false;
                this.updateItem1.jiantouImg.visible = false;
                this.updateItem1.nextLabel.text = "";
            }
            else {
                this.updateBtn.visible = true;
                this.emptyImg.visible = skill_cfg == null;
                this.skillBox.visible = skill_cfg != null;
                this.updateItem1.jiantouImg.visible = true;
                this.updateItem1.nextLabel.text = next_max_level.toString();
                if (skill_cfg != null) {
                    // this.skillImg.skin = UrlConfig.SKILL_RES_URL + skill_cfg.icon + ".png";
                    let skill_item_vo = new SkillItemVo();
                    skill_item_vo.skill_id = skill_cfg.skill_id;
                    skill_item_vo.skill_level = skill_id_lv_map[skill_cfg.skill_id];
                    skill_item_vo.heroInfo = this._hero_info;
                    skill_item_vo.id_level_map = skill_id_lv_map;
                    skill_item_vo.isShowLevel = true;
                    skill_item_vo.isShowName = false;
                    skill_item_vo.isGrayWhenNotActive = false;
                    this._skillItem.setDataBySkillItemVO(skill_item_vo);
                    this.skillNameLabel.text = skill_cfg.name;
                }
            }
            this.cur_cfg = ConfigManager.cfg_hero_stageCache.get(this._hero_info.stage);
            this.next_cfg = ConfigManager.cfg_hero_stageCache.get(next_stage);
            // w7 to do
            let cur_attrs = HeroUtil.getHeroFinalAttr(this._hero_info.type_id, cur_level, cur_stage, cur_star);
            let next_attrs = HeroUtil.getHeroFinalAttr(this._hero_info.type_id, cur_level, next_stage, cur_star);
            for (let i = 0; i < cur_attrs.length; i++) {
                let curAttrVo = cur_attrs[i];
                let nextAttrVo = next_attrs[i];
                let item = this._updateItemList[i];
                if (item) {
                    item.iconImg.skin = curAttrVo.iconUrl;
                    item.descLabel.text = curAttrVo.name + "：";
                    item.curLabel.text = FightAttrVO.formatValLimitLen(curAttrVo.val, curAttrVo.val_type, { fractionDigits: 0 });
                    item.nextLabel.text = FightAttrVO.formatValLimitLen(nextAttrVo.val, nextAttrVo.val_type, { fractionDigits: 0 });
                }
            }
            this.refreshCost();
        }
    }
    refreshCost() {
        if (this.next_cfg.cost_1) {
            GameUtil.setItemCostIcon(this.iconImg1, this.next_cfg.cost_1);
            GameUtil.setItemCostLabel(this.numLabel1, this.next_cfg.cost_1, this.next_cfg.cost_num_1, { greenColor: ColorUtil.FONT_WHITE2 });
        }
        if (this.next_cfg.cost_2) {
            GameUtil.setItemCostIcon(this.iconImg2, this.next_cfg.cost_2);
            GameUtil.setItemCostLabel(this.numLabel2, this.next_cfg.cost_2, this.next_cfg.cost_num_2, { greenColor: ColorUtil.FONT_WHITE2 });
        }
    }
    OnClick() {
        if (!this._hero_info) {
            return;
        }
        HeroDataCenter.instance.m_hero_upgrade_tos(this._hero_info.hero_id, HeroDataCenter.STAGE_UPDATE);
        this.close();
    }
    /**
    * 英雄提升
    * @param up_type 提升类型：1=升级，2=升阶，3=升星
    */
    onHeroUpgrade(up_type) {
        if (up_type != HeroDataCenter.STAR_UPDATE || this._hero_info == null) {
            return;
        }
    }
    destroy(destroyChild = true) {
        GuideMgr.ins.setHideGuideUI(false, this.name);
        super.destroy(destroyChild);
    }
}
