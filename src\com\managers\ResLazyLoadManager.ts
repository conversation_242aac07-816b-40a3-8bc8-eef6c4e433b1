import { Laya } from "Laya";
import { Handler } from "laya/utils/Handler";
import { GlobalConfig } from "../../game/GlobalConfig";
import { ConfigManager } from "./ConfigManager";
import { MiscConstAuto } from "../auto/MiscConstAuto";
import { UrlConfig } from "../../game/UrlConfig";
import { Loader } from "laya/net/Loader";
import { ConsoleUtils } from "../util/ConsoleUtils";
import { DataCenter } from "../modules/DataCenter";
import { MemMonitor } from "./MemMonitor";
import { PerformanceManager } from "./PerformanceManager";
import { TimerUtils } from "../util/TimerUtils";
import { SkeletonManager } from "../modules/baseModules/skeleton/SkeletonManager";
import { GameUtil } from "../util/GameUtil";
import { AnimationManager } from "./AnimationManager";
import { SentryUtils } from "../../game/SentryUtils";
import { ResStatisticsManager } from "./ResStatisticsManager";

/**
 * 网络空闲的时候,根据等级预加载下一等级所需的资源
 */
export class ResLazyLoadManager {

    private level_res_map: Map<number, string[]>;
    private waitLoadList: string[];
    private preLevel: number = 0;

    public get nowLevel() {
        return DataCenter.myLevel;
    }

    public get isLoad() {
        //TODO 先debug测试
        // if (GlobalConfig.IsDebug) {
        //     return true;
        // } else {
        //     return false;
        // }
        return this.nowLevel <= +MiscConstAuto.res_lazy_load_open_level;
    }

    private static _ins: ResLazyLoadManager = null;
    public static get ins(): ResLazyLoadManager {
        if (!ResLazyLoadManager._ins) {
            ResLazyLoadManager._ins = new ResLazyLoadManager;
        }
        return ResLazyLoadManager._ins;
    }

    private getJsonUrl(): string {
        return ResStatisticsManager.getJsonUrl();
    }

    public init() {
        if (!this.isLoad) {
            return;
        }

        this.level_res_map = new Map<number, string[]>();
        this.waitLoadList = [];

        //读取\res\level_res_map.json
        if (GlobalConfig.IsDebug == false) {
            if (UrlConfig.checkResExist(this.getJsonUrl()) == false) {
                return;
            }
        }

        Laya.loader.load(this.getJsonUrl(), Handler.create(this, (data) => {
            try {
                if (data) {
                    for (let level in data) {
                        let resUrlList = data[level];
                        this.level_res_map.set(Number(level), resUrlList);
                    }

                    this.onLvChanged();
                }
                else {
                    console.error("--------ResLazyLoadManager load LEVEL_RES_MAP_JSON fail!");
                }
            } catch (error) {
                SentryUtils.sendWarningMsg("------ResLazyLoadManager init initLvChanged error: " + error);
            }

        }), null, Loader.JSON);

    }

    private isLoaderIdle() {
        return Laya.loader["_loaderCount"] == 0;
    }

    private SecondTimer(): void {

        if (this.waitLoadList.length <= 0) {
            Laya.timer.clear(this, this.SecondTimer);
            return;
        }

        // if(!TimerUtils.checkIsFreeFrame()){
        //     return;
        // }

        if (!this.isLoaderIdle()) {
            return;
        }

        //检测内存
        if (!MemMonitor.instance.mem_engough) {
            return;
        }

        var toLoadUrl = "";
        while (this.waitLoadList.length > 0) {
            toLoadUrl = this.waitLoadList.shift();
            if (this.checkCanLoadRes(toLoadUrl)) {
                break;
            } else {
                toLoadUrl = "";
            }
        }

        if (!toLoadUrl) {
            return;
        }

        if (toLoadUrl && UrlConfig.checkResExist(toLoadUrl)) {
            Laya.loader.load(toLoadUrl, Handler.create(this, (data) => {
                if (data) {
                    this.printLoadSucc(toLoadUrl);
                }
                else {
                    ConsoleUtils.warn("------ResLazyLoadManager load fail! name = " + toLoadUrl);
                }
            }), null, null, 4);
        }

    }

    /*
        NOTE:
        目前只加载UI相关，假如需要加载3D模型，需要使用Sprite3D相关加载接口
    */
    private checkCanLoadRes(toLoadUrl: string): boolean {

        if (!toLoadUrl || Laya.loader.getRes(toLoadUrl)) {
            return false;
        }

        if (UrlConfig.checkResExist(toLoadUrl) == false) {
            return false;
        }

        let toCheckUrl = toLoadUrl;
        if (toCheckUrl.startsWith(UrlConfig.BASE_ATLAS_URL)) {

        }
        else if (toCheckUrl.startsWith(UrlConfig.BASE_ATLAS_URL)) {

        }
        else if (toCheckUrl.startsWith(UrlConfig.EFFECT_PATH)) {
            //effect
            let aniName = this.getAniName(toLoadUrl);
            let eff = AnimationManager.ShowEffect(aniName);
            eff.destroy();

            this.printLoadSucc(toLoadUrl);
            toLoadUrl = "";
        }
        else if (toCheckUrl.startsWith(UrlConfig.SPINE_BASE_PATH)
            || toCheckUrl.startsWith(UrlConfig.SPINE_ANI_BASE_PATH)) {

            //spine动画
            let skName = this.getAniName(toLoadUrl);
            let skType = UrlConfig.get_skeleton_type_by_url(toLoadUrl);
            if (skName && skType) {
                let sk = SkeletonManager.ins.createSkeleton(skName, skType);
                sk?.destroy();
            }

            this.printLoadSucc(toLoadUrl);
            toLoadUrl = "";
        }
        else if (toCheckUrl.startsWith(UrlConfig.RES_BASE_DIR_3D)) {
            toLoadUrl = "";
        }
        //音效不加载
        else if (toCheckUrl.indexOf("/sounds_") >= 0) {
            toLoadUrl = "";
        }
        else {

            //TODO 先不加载其他的,晚点再测试,完善
            // toLoadUrl = "";
        }

        return toCheckUrl == toLoadUrl;
    }

    private printLoadSucc(toLoadUrl:string){
        // if(GlobalConfig.is_LOCAL_DEBUG){
        //     ConsoleUtils.log("------ResLazyLoadManager load success. name = " + toLoadUrl);
        // }
    }

    private getAniName(toLoadUrl: string) {
        let pathList = toLoadUrl.split("/");
        let aniName = pathList[pathList.length - 1].split(".")[0];
        return aniName;
    }

    public onLvChanged(): void {
        if (!this.isLoad) {
            return;
        }

        if (!this.level_res_map || this.level_res_map.size <= 0) {
            return;
        }

        if (this.preLevel == this.nowLevel) {
            return;
        }

        if(this.preLevel == 0){
            var next_lv = this.nowLevel;
        }else{
            var next_lv = this.preLevel + 1;
        }
        
        this.preLevel = this.nowLevel;

        // let lv_res_list = this.level_res_map.get(next_lv) || [];
        // lv_res_list.forEach(res => {
        //     this.waitLoadList.push(res);
        // })

        let resList = this.level_res_map.get(next_lv) || [];
        resList.forEach(res => {
            this.waitLoadList.push(res);
        })

        if (this.waitLoadList.length) {
            Laya.timer.loop(1000, this, this.SecondTimer);
        }

    }

}