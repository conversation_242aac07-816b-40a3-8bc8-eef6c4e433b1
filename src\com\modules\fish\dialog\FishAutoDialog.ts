import { Event } from "laya/events/Event";
import { LocalStorage } from "laya/net/LocalStorage";
import { ComboBox2 } from "laya/ui/ComboBox2";
import { Image } from "laya/ui/Image";
import { Label } from "laya/ui/Label";
import { Handler } from "laya/utils/Handler";
import { GlobalConfig } from "../../../../game/GlobalConfig";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { com } from "../../../ui/layaMaxUI";
import { StringUtil } from "../../../util/StringUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import UIFactory from "../../../util/UIFactory";
import { UICombBoxItem, UIComboBox } from "../../baseModules/UIComboBox";
import { UIListItemData } from "../../baseModules/UIListItemData";
import { DataCenter } from "../../DataCenter";
import { FishTimeComboBox } from "../../menu/view/FishTimeComboBox";
import { ModuleCommand } from "../../ModuleCommand";
import { FightAttrMgr } from "../../role/data/FightAttrMgr";
import { SettingDataCenter } from "../../setting/data/SettingDataCenter";
import { YueKaDataCenter} from "../../welfare/data/YueKaDataCenter";
import { FishDataCenter } from "../data/FishDataCenter";
import { ItemConst } from "../../goods/ItemConst";
import { GameUtil } from "../../../util/GameUtil";
import { PanelEventConstants } from "../../PanelEventConstants";
import { PaymentVO } from "../../payment/vo/PaymentVO";
import {EYueKaType} from "../../../auto/ConstAuto";
// import { NumberKeyframeTrack } from "../../../../../node_modules/three/src/Three";

export default class FishAutoDialog extends com.ui.res.fish.FishAutoDialogUI {

    private _comboColor: UIComboBox;

    private _comboTime: UIComboBox;

    private _comboAttri1: UIComboBox;

    private _comboAttri2: UIComboBox;

    private _comboAttri3: UIComboBox;

    private _comboAttr4: UIComboBox;

    private cdnArr: any[] = [];

    private cdnArr2: any[] = [];

    private timeArr: number[];

    private maxselectIndex: number;

    constructor() {

        super();

        this.navShow = 0;
        this.drawCallOptimize = false;
    }

    initUI(): void {

        let drum = FishDataCenter.instance.rod;

        let level: number = drum?.key ? drum.key : 1;

        let levelCfg = CfgCacheMapMgr.cfg_fish_rod_levelCache.get(level);

        let maxcolor = parseInt(levelCfg.lottery_color.split("|")[levelCfg.lottery_color.split("|").length - 1]);

        let colorArr = levelCfg.lottery_color.split("|").map(item => parseInt(item));

        let maxLevelArr = CfgCacheMapMgr.cfg_fish_rod_levelCache.get_all().map(item => {

            return item.lottery_num

        }).sort((next, last) => {

            return next - last;

        });

        /* 数据结构特性去重 */
        maxLevelArr = [...new Set(maxLevelArr)];

        let maxindex = maxLevelArr?.indexOf(CfgCacheMapMgr.cfg_fish_rod_levelCache.get(FishDataCenter.instance.rod?.key)?.lottery_num);

        this.maxselectIndex = maxindex;

        this.timeArr = maxLevelArr;

        for (let i = 0; i < maxLevelArr.length; i++) {

            this.cdnArr2.push(UIComboBox.GetItemData(i, window.iLang.L_P0_TIMES.il([maxLevelArr[i]]), window.iLang.L_P0_TIMES.il([maxLevelArr[i]])));

        }

        for (let i = parseInt(levelCfg.lottery_color.split("|")[0]); i <= maxcolor; i++) {

            let colorCfg = CfgCacheMapMgr.cfg_fish_colorCache.get(i);

            this.cdnArr.push(UIComboBox.GetItemData(i, StringUtil.Format(window.iLang.L2_P0_ch11_ch11_JI_YI_SHANG.il(), [colorCfg?.color_name]), StringUtil.Format(window.iLang.L2_P0_ch11_ch11_JI_YI_SHANG.il(), [colorCfg?.color_name])));

        }

        let maxSelectTimeIndex = maxLevelArr?.indexOf(CfgCacheMapMgr.cfg_fish_rod_levelCache.get(FishDataCenter.instance.rod?.key)?.lottery_num);

        var sumStr: string = this.cdnArr.join(",");

        this.colorSelectBox.selectHandler = new Handler(this, this.onSelectColor, [this.colorSelectBox]);

        this.colorSelectBox.labels = sumStr;

        this.colorSelectBox.selectedIndex = colorArr?.indexOf(FishDataCenter.autoStopColor(FishDataCenter.TAG_COLOR)) == -1 ? 0 : colorArr.indexOf(FishDataCenter.autoStopColor(FishDataCenter.TAG_COLOR));

        var sumStr2: string = this.cdnArr2.join(",");

        this.timeSelectBox.selectHandler = new Handler(this, this.onSelectTime, [this.timeSelectBox]);

        this.timeSelectBox.labels = sumStr2;

        this.timeSelectBox.selectedIndex = maxLevelArr?.indexOf(FishDataCenter.autoStopColor(FishDataCenter.TAG_Time)) == -1 ? 0 : maxLevelArr.indexOf(FishDataCenter.autoStopColor(FishDataCenter.TAG_Time));

        this._comboTime = UIFactory.createUIComboBox(this, this.comboTime, {

            onSelect: this.onSelectComboTime, isSelectOne: false,

            rowNum: 6, colNum: 1,

            itemRender: FishTimeComboBox

        });


        this._comboTime.AddOtherParamete("maxLevelIndex", maxSelectTimeIndex);

        this._comboTime.array = this.cdnArr2

        this._comboTime.selectedIndex = maxLevelArr?.indexOf(FishDataCenter.autoStopColor(FishDataCenter.TAG_Time)) == -1 ? 0 : maxLevelArr.indexOf(FishDataCenter.autoStopColor(FishDataCenter.TAG_Time));

        let tempArr = []
        let attriArr = CfgCacheMapMgr.cfg_fish_slotCache.get_all().filter(item => {
            if (tempArr.indexOf(item.premium_attrs_1) == -1 && item.slot != 1) {
                tempArr.push(item.premium_attrs_1);
                return true;
            } else {
                return false;
            }


        })
        for (let i = 1; i <= 4; i++) {

            let attriArr2;
            if (i <= 2) {
                attriArr2 = attriArr[0][`premium_attrs_${i}`].split("|").map(item => {
                    return parseInt(item)
                })
            } else {
                attriArr2 = attriArr[1][`premium_attrs_${i - 2}`].split("|").map(item => {
                    return parseInt(item)
                })
            }
            let arr = [];

            arr.push(UIComboBox.GetItemData(0, window.iLang.L2_REN_YI.il(), window.iLang.L2_REN_YI.il()));

            for (let i = 1; i <= attriArr2.length; i++) {
                let master_attriCfg = CfgCacheMapMgr.cfg_fish_attrCache.get(attriArr2[i - 1]);
                let fightVo = FightAttrMgr.instance.getAttrVO(master_attriCfg?.attr_id, 0);
                arr.push(UIComboBox.GetItemData(i, `${fightVo.name}`, `${fightVo.name}`));
            }

            var sumStr2: string = arr.join(",");

            this[`comboSelect${i}`].selectHandler = new Handler(this, this.onSelectTime, [this.comboSelect4]);

            this[`comboSelect${i}`].labels = sumStr2;

            this[`comboSelect${i}`].selectedIndex = [0, ...attriArr2]?.indexOf(FishDataCenter.autoStopColor(FishDataCenter[`TAG_EXTREME_ATTRI_${i}`])) == -1 ? 0 : [0, ...attriArr2].indexOf(FishDataCenter.autoStopColor(FishDataCenter[`TAG_EXTREME_ATTRI_${i}`]));

            this[`_comboAttri${i}`] = UIFactory.createUIComboBox(this, this[`comboCommon${i}`], {

                onSelect: this[`onSelectComboAttri${i}`], isSelectOne: false,

                rowNum: 10, colNum: 1,

                itemRender: UICombBoxItemAuto2

            });

            this[`_comboAttri${i}`].AddOtherParamete("maxLevelIndex", maxSelectTimeIndex);

            this[`_comboAttri${i}`].array = arr

            this[`_comboAttri${i}`].selectedIndex = [0, ...attriArr2]?.indexOf(FishDataCenter.autoStopColor(FishDataCenter[`TAG_EXTREME_ATTRI_${i}`])) == -1 ? 0 : [0, ...attriArr2].indexOf(FishDataCenter.autoStopColor(FishDataCenter[`TAG_EXTREME_ATTRI_${i}`]));

        }


        this._comboColor = UIFactory.createUIComboBox(this, this.comboColor, {

            onSelect: this.onSelectComboColor, isSelectOne: false,

            rowNum: 10, colNum: 1,

            itemRender: UICombBoxItemAuto

        });

        this._comboColor.array = this.cdnArr;

        this._comboColor.selectedIndex = colorArr?.indexOf(FishDataCenter.autoStopColor(FishDataCenter.TAG_COLOR)) == -1 ? 0 : colorArr.indexOf(FishDataCenter.autoStopColor(FishDataCenter.TAG_COLOR));

        // FishDataCenter.setBooleanSkip(FishDataCenter.TAG_STAOP_ON_HIGHTER, true);

        // FishDataCenter.asyncFishSkip(FishDataCenter.TAG_STAOP_ON_HIGHTER, true);

        this.commonHigher.selected = FishDataCenter.checkIsSkip(FishDataCenter.TAG_STAOP_ON_HIGHTER);

        this.qulitySelect.selected = FishDataCenter.checkIsSkip(FishDataCenter.TAG_SWITCH);

        if (!(YueKaDataCenter.instance.getFuliYuekaState(EYueKaType.TYPE_4_KING) == 1 || YueKaDataCenter.instance.getFuliYuekaState(EYueKaType.TYPE_4_KING) == 2)) {

            FishDataCenter.setBooleanSkip(FishDataCenter.TAG_FASTER, false);

            FishDataCenter.asyncFishSkip(FishDataCenter.TAG_FASTER, false);

        }

        this.btnFaster.selected = FishDataCenter.checkIsSkip(FishDataCenter.TAG_FASTER);

        this.fasterLimit.text = (YueKaDataCenter.instance.getFuliYuekaState(EYueKaType.TYPE_4_KING) == 1 || YueKaDataCenter.instance.getFuliYuekaState(EYueKaType.TYPE_4_KING) == 2) ? window.iLang.L2_ch19_YI_JI_HUO_ch20.il() : window.iLang.L2_ch19_JI_HUO_WANG_ZHE_YUE_KA_JIE_SUO_ch20.il();

        this.fasterLimit.color = (YueKaDataCenter.instance.getFuliYuekaState(EYueKaType.TYPE_4_KING) == 1 || YueKaDataCenter.instance.getFuliYuekaState(EYueKaType.TYPE_4_KING) == 2) ? "#107118" : "#ef231f";

        this.superLimit.text = DataCenter.vipLevel >= 15 ? window.iLang.L2_ch19_YI_JI_HUO_ch20.il() : window.iLang.L2_ch19_VIP15_JI_HUO_ch20.il();

        this.superLimit.color = DataCenter.vipLevel >= 15 ? "#107118" : "#ef231f";

        this.btnCard.visible = !(YueKaDataCenter.instance.getFuliYuekaState(EYueKaType.TYPE_4_KING) == 1 || YueKaDataCenter.instance.getFuliYuekaState(EYueKaType.TYPE_4_KING) == 2)

        if (DataCenter.vipLevel < 15) {

            FishDataCenter.setBooleanSkip(FishDataCenter.TAG_SUPER_FASTER, false);

            FishDataCenter.asyncFishSkip(FishDataCenter.TAG_SUPER_FASTER, false);

        }


        this.superFaster.selected = FishDataCenter.checkIsSkip(FishDataCenter.TAG_SUPER_FASTER);

        this.clikTimes.selected = FishDataCenter.checkIsSkip(FishDataCenter.Tag_TIME_SWITCH);

        this.extremeCheckBox.selected = FishDataCenter.checkIsSkip(FishDataCenter.Tag_EXTREME_ATTRI_SWITCH);

        if (!this.extremeCheckBox.selected) {

            this.satisfyAll.selected = this.satisfyRandom.selected = false;

        } else {

            let bg_type = SettingDataCenter.instance.getVal(FishDataCenter.Tag_EXTREME_ATTRI_SWITCH_SATISFY);

            if (bg_type != 1 && bg_type != 2) {

                bg_type = 1;

            }

            this.satisfyRandom.selected = bg_type == 1;

            this.satisfyAll.selected = bg_type == 2;

        }

    }

    addClick(): void {

        this.addOnClick(this, this.topPanelUI.closeBtn, this.close);

        this.addOnClick(this, this.btnOpen, this.onClickOpen);

        this.addOnClick(this, this.btnCard, this.onClickCard);

        this.addOnClick(this, this.backBtn, this.close)

        this.commonHigher.on(Event.CHANGE, this, this.onClickHigherCommentStop);

        this.qulitySelect.on(Event.CHANGE, this, this.onClickOnOnceColor);

        this.clikTimes.on(Event.CHANGE, this, this.onClickOnOnceTime);

        this.superFaster.on(Event.CHANGE, this, this.onClickSuperFaster);

        this.btnFaster.on(Event.CHANGE, this, this.onClickFasterCart);

        this.extremeCheckBox.on(Event.CHANGE, this, this.onClickExtremeAttri);

        this.addOnClick(this, this.satisfyRandom, this.onCheckSernierClick, Event.CLICK, [1], 0);

        this.addOnClick(this, this.satisfyAll, this.onCheckSernierClick, Event.CLICK, [2], 0);

    }

    addEvent(): void {

        this.addEventListener(ModuleCommand.UPDATE_FISH_EXTREME_SATISFY_DATA, this, this.updateExtremeSwitch);


        // this.addEventListener(ModuleCommand.UPDATE_FISH_EXTREME_SATISFY_DATA,this,this.updateExtremeSwitch);

    }

    onOpen(param: any): void {
        this.imgVip.visible = GlobalConfig.showRecharge;
    }

    private onSelectColor(comboBox: ComboBox2): void {

    }

    private onSelectTime(comboBox: ComboBox2): void {

    }

    onClickHigherCommentStop() {

        FishDataCenter.setBooleanSkip(FishDataCenter.TAG_STAOP_ON_HIGHTER, this.commonHigher.selected);

        FishDataCenter.asyncFishSkip(FishDataCenter.TAG_STAOP_ON_HIGHTER, this.commonHigher.selected);
    }


    onClickOnOnceColor() {

        FishDataCenter.setBooleanSkip(FishDataCenter.TAG_SWITCH, this.qulitySelect.selected);

        FishDataCenter.asyncFishSkip(FishDataCenter.TAG_SWITCH, this.qulitySelect.selected);

    }

    onClickOnOnceTime() {

        FishDataCenter.setBooleanSkip(FishDataCenter.Tag_TIME_SWITCH, this.clikTimes.selected);

        FishDataCenter.asyncFishSkip(FishDataCenter.Tag_TIME_SWITCH, this.clikTimes.selected);

    }

    onClickExtremeAttri() {

        if (this.extremeCheckBox.selected == false) {

            this.satisfyRandom.selected = this.satisfyAll.selected = false;

        } else {
            let bg_type = SettingDataCenter.instance.getVal(FishDataCenter.Tag_EXTREME_ATTRI_SWITCH_SATISFY);

            if (bg_type != 1 && bg_type == 2) {

                this.onCheckSernierClick(1);

            }

        }

        FishDataCenter.setBooleanSkip(FishDataCenter.Tag_EXTREME_ATTRI_SWITCH, this.extremeCheckBox.selected);

        FishDataCenter.asyncFishSkip(FishDataCenter.Tag_EXTREME_ATTRI_SWITCH, this.extremeCheckBox.selected);

    }

    onClickSuperFaster() {

        if (DataCenter.vipLevel < 15) {

            if (this.superFaster.selected != false) {

                TipsUtil.showTips(window.iLang.L2_V15_JI_HUO_CHAO_JI_JIA_SU.il());

                this.superFaster.selected = false;

            }

            return;
        }

        FishDataCenter.setBooleanSkip(FishDataCenter.TAG_SUPER_FASTER, this.superFaster.selected);

        FishDataCenter.asyncFishSkip(FishDataCenter.TAG_SUPER_FASTER, this.superFaster.selected);

    }

    private onCheckSernierClick(type: number) {

        if (!this.extremeCheckBox.selected) {

            this.satisfyRandom.selected = this.satisfyAll.selected = false;

            return

        }

        SettingDataCenter.instance.m_role_setting_tos(FishDataCenter.Tag_EXTREME_ATTRI_SWITCH_SATISFY, type);
        SettingDataCenter.instance.updateSetting(FishDataCenter.Tag_EXTREME_ATTRI_SWITCH_SATISFY, type);
        LocalStorage.setItem(FishDataCenter.Tag_EXTREME_ATTRI_SWITCH_SATISFY, type.toString());

        this.updateExtremeSwitch();

      
        TipsUtil.showTips(window.iLang.L2_SHE_ZHI_CHENG_GONG.il());

    }

    private updateExtremeSwitch() {

        let bg_type = SettingDataCenter.instance.getVal(FishDataCenter.Tag_EXTREME_ATTRI_SWITCH_SATISFY);

        if (!this.extremeCheckBox.selected) {

            bg_type = 0;

        }

        this.satisfyRandom.selected = bg_type == 1;
        this.satisfyAll.selected = bg_type == 2;
    }


    onSelectComboColor(itemData: UIListItemData) {

        let sel_color = itemData.index;

        FishDataCenter.setNumberSkip(FishDataCenter.TAG_COLOR, sel_color);

        FishDataCenter.asyncFishColor(FishDataCenter.TAG_COLOR, sel_color);

    }

    onSelectComboTime(itemData: UIListItemData) {

        if (itemData.index > this.maxselectIndex) {

            this._comboTime.selectedIndex = this.maxselectIndex;

            let masterCfgArr = CfgCacheMapMgr.cfg_fish_rod_levelCache.get_all();

            for (let i = 0; i <= masterCfgArr?.length; i++) {
                if (masterCfgArr[i]?.lottery_num == this.timeArr[itemData.index]) {
                    TipsUtil.showTips(StringUtil.Format(window.iLang.L2_YU_GAN_DENG_JI_P0_JI_HOU_KE_JIE_SUO.il(), [masterCfgArr[i].level]));
                    break;
                }
            }

            return

        }

        let sel_color = this.timeArr[itemData.index];

        FishDataCenter.setNumberSkip(FishDataCenter.TAG_Time, sel_color);

        FishDataCenter.asyncFishColor(FishDataCenter.TAG_Time, sel_color);

    }

    onSelectComboAttri1(itemData: UIListItemData) {
        this.onSelectComboAttrFun(1, itemData.index)
    }

    onSelectComboAttri2(itemData: UIListItemData) {
        this.onSelectComboAttrFun(2, itemData.index)
    }

    onSelectComboAttri3(itemData: UIListItemData) {
        this.onSelectComboAttrFun(3, itemData.index)
    }

    onSelectComboAttri4(itemData: UIListItemData) {
        this.onSelectComboAttrFun(4, itemData.index)
    }

    onSelectComboAttrFun(attriIndex: number, index: number) {

        let tempArr = []

        let attriArr = CfgCacheMapMgr.cfg_fish_slotCache.get_all().filter(item => {
            if (tempArr.indexOf(item.premium_attrs_1) == -1 && item.slot != 1) {
                tempArr.push(item.premium_attrs_1);
                return true;
            } else {
                return false;
            }


        })

        let attriArr2;
        if (attriIndex <= 2) {
            attriArr2 = attriArr[0][`premium_attrs_${attriIndex}`].split("|").map(item => {
                return parseInt(item)
            })
        } else {
            attriArr2 = attriArr[1][`premium_attrs_${attriIndex - 2}`].split("|").map(item => {
                return parseInt(item)
            })
        }

        let selAttri = [0, ...attriArr2][index];

        FishDataCenter.setNumberSkip(FishDataCenter[`TAG_EXTREME_ATTRI_${attriIndex}`], selAttri);

        FishDataCenter.asyncFishColor(FishDataCenter[`TAG_EXTREME_ATTRI_${attriIndex}`], selAttri);

    }

    onClickOpen() {

        if ((FishDataCenter.checkIsSkip(FishDataCenter.TAG_SWITCH) || FishDataCenter.checkIsSkip(FishDataCenter.Tag_EXTREME_ATTRI_SWITCH) || FishDataCenter.checkIsSkip(FishDataCenter.TAG_STAOP_ON_HIGHTER)) && FishDataCenter.instance.getHaveFishToolItem()) {

            FishDataCenter.instance.isAutoFish = 1;

            this.dispatchEvent(ModuleCommand.OPEN_FISH_AUTO_OPERATE);

            this.close();

        } else if (FishDataCenter.instance.getHaveFishToolItem() <= 0) {

            TipsUtil.ShowPathTypeIdToTips(ItemConst.FISH_COST_ITEM);

        } else if (!(FishDataCenter.checkIsSkip(FishDataCenter.TAG_SWITCH) || FishDataCenter.checkIsSkip(FishDataCenter.TAG_STAOP_ON_HIGHTER))) {

            TipsUtil.showTips(window.iLang.L2_QING_XUAN_ZE_SHAI_XUAN_CAO_ZUO_ch26.il())
        }

    }

    onClickFasterCart() {

        if (YueKaDataCenter.instance.getFuliYuekaState(EYueKaType.TYPE_4_KING) == 1 || YueKaDataCenter.instance.getFuliYuekaState(EYueKaType.TYPE_4_KING) == 2) {

            FishDataCenter.setBooleanSkip(FishDataCenter.TAG_FASTER, this.btnFaster.selected);

            FishDataCenter.asyncFishSkip(FishDataCenter.TAG_FASTER, this.btnFaster.selected);

        } else {

            if (this.btnFaster.selected != false) {

                this.btnFaster.selected = false;

                TipsUtil.showTips(window.iLang.L2_XU_KAI_TONG_WANG_ZHE_YUE_KA.il());

            }

        }

    }

    onClickCard() {
        if (GameUtil.isSysOpen(PanelEventConstants.PAYMENT, PaymentVO.KEY_WELFARE_CARD, true)) {
            this.dispatchEvent(ModuleCommand.OPEN_PAYMENT_DIALOG, { child_id: PaymentVO.KEY_WELFARE_CARD })
        }
    }
}

class UICombBoxItemAuto extends UICombBoxItem {
    protected _bg: Image;

    constructor() {
        super();


    }

    protected initUI(): void {
        this._bg = new Image();
        this._bg.width = 250;
        this._bg.height = 25;
        this._bg.skin = "common/bg25.png";
        this._bg.sizeGrid = "12,17,14,17";
        this.width = 250;
        this.height = 28;
        this._label = new Label();
        this._label.size(this.width, this.height);
        this._label.align = "center";
        this._label.valign = "middle";
        this._label.padding = "3,3,3,3";
        this._label.color = "#ffffff";
        this._label.bgColor = "#947763";
        this.addChild(this._bg);
        this.addChild(this._label);

    }
}

class UICombBoxItemAuto2 extends UICombBoxItem {
    protected _bg: Image;

    constructor() {
        super();


    }

    protected initUI(): void {
        this._bg = new Image();
        this._bg.width = 250;
        this._bg.height = 25;
        this._bg.skin = "common/bg25.png";
        this._bg.sizeGrid = "12,17,14,17";
        this.width = 200;
        this.height = 28;
        this._label = new Label();
        this._label.size(this.width, this.height);
        this._label.align = "center";
        this._label.valign = "middle";
        this._label.padding = "3,3,3,3";
        this._label.color = "#ffffff";
        this._label.bgColor = "#947763";
        this.addChild(this._bg);
        this.addChild(this._label);

    }
}
