import {CfgCacheMapMgr} from "../../../cfg/CfgCacheMapMgr";
import {TipsUtil} from "../../../util/TipsUtil";
import {CrossTeamDataCenter} from "../../crossTeam/data/CrossTeamDataCenter";
import {CrossTeamLineUpVo} from "../../crossTeam/vo/CrossTeamLineUpVo";
import {DataCenter} from "../../DataCenter";
import {BattleTrialModel} from "../const/BattleTrialConst";
import {BattleTrialDataCenter} from "../data/BattleTrialDataCenter";
import {BattleTrialMiscConst} from "../data/BattleTrialMiscConst";

export class BattleTrialLineUpVo extends CrossTeamLineUpVo {

    public static init() {
        console.log("--------------BattleTrialLineUpVo init");
    }

    /**最大共享英雄数量 */
    get maxShareHeroNum(): number {
        return BattleTrialMiscConst.battle_trial_max_hero_len;
    }

    /**共享英雄冷却时间 */
    get shareHeroCD(): number {
        return BattleTrialMiscConst.battle_trial_share_hero_cd;
    }

    /**判断是否显示自动战斗 */
    get isShowAuto(): boolean {
        return this.teamVo.isCaptainSelf && super.isShowAuto;
    }

    /**判断是否可以选择自动战斗 */
    checkIsCanClickAuto(isTip: boolean = false): boolean {
        let crossTeamVo = CrossTeamDataCenter.instance.getTeamVo(this.matchType);
        if (!crossTeamVo.isCaptainSelf) {
            if (isTip) {
                TipsUtil.showTips(window.iLang.L2_DUI_ZHANG_CAI_NENG_TIAO_ZHAN.il());
            }
            return false;
        }

        let curModel = BattleTrialDataCenter.instance.cur_model
        if (curModel != BattleTrialModel.CHALLENGE) {
            if (isTip) {
                TipsUtil.showTips(window.iLang.L2_TIAO_ZHAN_JIE_DUAN_CAI_NENG_TIAO_ZHAN.il());
            }
            return false;
        }

        // 策划需求：去掉月卡限制
        // if (!YueKaDataCenter.instance.isOpenFuliYueKa(EYueKaType.TYPE_3)
        //     && !YueKaDataCenter.instance.isOpenFuliYueKa(EYueKaType.TYPE_4_KING)
        // ) {
        //     if (isTip) {
        //         TipsUtil.showTips("需激活尊贵月卡才可开启自动战斗");
        //     }
        //     return false;
        // }

        let cfg = CfgCacheMapMgr.cfg_match_typeCache.get(this.matchType);
        if (cfg.is_can_auto != 1) {
            if (isTip) {
                TipsUtil.showTips(window.iLang.L2_DANG_QIAN_WAN_FA_BU_ZHI_CHI_ZI_DONG_ZHAN_DOU.il());
            }
            return false;
        }

        if (DataCenter.myLevel < cfg.auto_lv_limit) {
            if (isTip) {
                TipsUtil.showTips(window.iLang.L2_P0_JI_KAI_QI_ZI_DONG_ZHAN_DOU.il([cfg.auto_lv_limit]));
            }
            return false;
        }
        return true;
    }
}