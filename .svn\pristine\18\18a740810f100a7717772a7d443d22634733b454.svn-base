import {Handler} from "laya/utils/Handler";
import {MathUtil2} from "../../../../util/MathUtil2";
import {BaseScript} from "../../../BaseScript";
import {TDRoleBase} from "../actor/TDRoleBase";
import {TDPathPointVo} from "../vo/TDPathPointVo";
import { Point } from "laya/maths/Point";
import { ESkeletonAction } from "../../../baseModules/skeleton/SkeletonData";
import { GuajiBuildWorker } from "../../../guajiBuild/game/GuajiBuildWorker";
import { GuajiBuildPathPointVo } from "../../../guajiBuild/vo/GuajiBuildPathPointVo";

/**
 * 沿路点移动
 */
export class TDRolePathPointScript extends BaseScript {
    // 路径相关
    public pathPointList: TDPathPointVo[] | GuajiBuildPathPointVo[] = [];
    protected currPathPointIndex: number = -1;
    protected nextTargetPoint: TDPathPointVo | GuajiBuildPathPointVo = null;
    protected isLoop: boolean = false;

    // 移动相关
    public baseSpeed: number = 0;
    protected startDeltaX: number = 0;
    protected startDeltaY: number = 0;
    protected isMovingToTarget: boolean = false; // 是否正在移动到目标点

    // 更新相关
    protected readonly UPDATE_INTERVAL: number = 100;
    protected updateInterval: number = 0;
    protected readonly ARRIVE_DISTANCE: number = 3; // 到达距离阈值

    // 回调
    protected endHandler: Handler = null;
    public isEnd: boolean = false;


    get roleBase(): TDRoleBase | GuajiBuildWorker {
        return this.ownerSpr as TDRoleBase;
    }

    /**
     * 开始路径移动
     * @param startIndex 起始路径点索引
     * @param pathPointList 路径点列表
     * @param isLoop 是否循环
     * @param baseSpeed 基础移动速度
     * @param endHandler 结束回调
     */
    public startPathPoint(
        pathPointList: TDPathPointVo[] | GuajiBuildPathPointVo[],
        isLoop: boolean = false,
        baseSpeed: number = 0,
        endHandler: Handler = null
    ): void {

        this.onReset();

        this.currPathPointIndex = 0;
        this.isEnd = false;
        //克隆一份
        let list: any[] = [];
        pathPointList.forEach((vo: TDPathPointVo | GuajiBuildPathPointVo) => {
            list.push(vo.clone());
        })
        this.pathPointList = list as TDPathPointVo[] | GuajiBuildPathPointVo[];

        // this.pathPointList = pathPointList;
        this.isLoop = isLoop;
        this.baseSpeed = baseSpeed;

        this.endHandler = endHandler;

    }

    // /**
    //  * 反向寻路移动
    //  */
    // public startReversePathPoint(): void {
    //     this.pathPointList = this.pathPointList.reverse();
    //     this.startPathPoint(this.pathPointList, this.isLoop, this.baseSpeed, this.endHandler);
    // }

    /**
     * 设置初始位置偏移
     * @param delta 时间偏移量(毫秒)
     */
    public setStartDeltaPos(delta: number): void {
        if(!this.pathPointList || this.pathPointList.length < 2) {
            return;
        }

        const startPoint = this.pathPointList[0];
        const toPoint = this.pathPointList[1];

        // 计算方向向量
        const direction = this.calculateDirection(startPoint, toPoint);

        // 计算位移
        const deltaDistance = (this.baseSpeed / 1000 * delta);
        this.startDeltaX = deltaDistance * direction.x;
        this.startDeltaY = deltaDistance * direction.y;

        // 设置位置
        this.updateRolePosition(
            startPoint.rangeTargetX + this.startDeltaX,
            startPoint.rangeTargetY + this.startDeltaY
        );
    }

    /**
     * 更新帧
     */
    public _updateFrame(interval: number): void {
        this.updateInterval -= interval;
        if (this.updateInterval > 0) {
            return;
        }

        // this.updateInterval = this.UPDATE_INTERVAL / this.roleBase._playSpeed;
        this.updateInterval = this.UPDATE_INTERVAL;
        this.checkPathPoint();
    }

    /**
     * 检查路径点
     */
    protected checkPathPoint(): void {
        if (!this.canMove()) {
            return;
        }

        if (this.isEnd) {
            return;
        }

        // 处理起始点的特殊情况
        if(this.currPathPointIndex === 0) {
            this.handleStartPoint();
            this.moveToFirstPoint();
            return;
        }

        // 如果正在移动到目标点，检查是否已经到达
        if (this.isMovingToTarget && this.nextTargetPoint) {
            const distance = MathUtil2.getDistance(
                this.roleBase.x,
                this.roleBase.y,
                this.nextTargetPoint.rangeTargetX,
                this.nextTargetPoint.rangeTargetY
            );

            // 如果已经靠近目标点，移动到下一个点
            if (distance <= this.ARRIVE_DISTANCE) {
                this.isMovingToTarget = false;
                this.moveToNextPoint();
            }
        } else if (!this.isMovingToTarget) {
            // 如果没有在移动，开始移动到下一个点
            this.moveToNextPoint();
        }
    }

    /**
     * 检查是否可以移动
     */
    protected canMove(): boolean {
        return this.currPathPointIndex >= 0
            && this.pathPointList?.length > 0
            && this.roleBase.checkCanMove();
    }

    /**
     * 获取下一个路径点索引
     */
    protected getNextPathPointIndex(): number {
        let nextIndex = this.currPathPointIndex + 1;

        if (nextIndex >= this.pathPointList.length && this.isLoop) {
            nextIndex = 1;
            this.updateRolePosition(
                this.pathPointList[0].rangeTargetX,
                this.pathPointList[0].rangeTargetY
            );
        }

        return nextIndex;
    }

    /**
     * 处理路径结束
     */
    protected handlePathEnd(): void {
        this.isEnd = true;
        if (this.endHandler) {
            this.endHandler.runWith(this.roleBase);
        }

    }

    /**
     * 处理起始点
     */
    protected handleStartPoint(): void {
        if(this.startDeltaX !== 0 || this.startDeltaY !== 0) {
            this.updateRolePosition(
                this.pathPointList[0].rangeTargetX + this.startDeltaX,
                this.pathPointList[0].rangeTargetY + this.startDeltaY
            );
            this.startDeltaX = 0;
            this.startDeltaY = 0;
        }
    }

    /**
     * 移动到第一个点（起始点处理）
     */
    protected moveToFirstPoint(): void {
        if (this.pathPointList.length > 1) {
            const firstPoint = this.pathPointList[1];
            this.roleBase.move(firstPoint.rangeTargetX, firstPoint.rangeTargetY);
            this.currPathPointIndex = 1;
            this.nextTargetPoint = firstPoint;
            this.isMovingToTarget = true;
        }
    }

    /**
     * 移动到下一个点
     */
    protected moveToNextPoint(): void {
        const nextIndex = this.getNextPathPointIndex();

        if (nextIndex >= this.pathPointList.length) {
            this.handlePathEnd();
            return;
        }

        const nextPoint = this.pathPointList[nextIndex];
        if (nextPoint) {
            this.roleBase.move(nextPoint.rangeTargetX, nextPoint.rangeTargetY);
            this.currPathPointIndex = nextIndex;
            this.nextTargetPoint = nextPoint;
            this.isMovingToTarget = true;
        } else {
            this.handlePathEnd();
        }
    }

    /**
     * 计算方向向量
     */
    protected calculateDirection(from: TDPathPointVo | GuajiBuildPathPointVo, to: TDPathPointVo | GuajiBuildPathPointVo): Point {
        const direction = new Point();
        direction.x = to.rangeTargetX - from.rangeTargetX;
        direction.y = to.rangeTargetY - from.rangeTargetY;
        direction.normalize();
        return direction;
    }


    /**
     * 更新角色位置
     */
    protected updateRolePosition(x: number, y: number): void {
        this.roleBase.pos(x, y);
    }

    /**
     * 重置
     */
    public onReset(): void {

        this.updateInterval = 0;
        this.currPathPointIndex = -1;
        this.isLoop = false;
        this.isEnd = false;
        this.isMovingToTarget = false;

        if(this.pathPointList){
            this.pathPointList.forEach((vo: TDPathPointVo | GuajiBuildPathPointVo) => {
                vo.recover();
            })
        }
        this.pathPointList = null;
        this.nextTargetPoint = null;
        this.startDeltaX = 0;
        this.startDeltaY = 0;

        if (this.endHandler) {
            this.endHandler.clear();
            this.endHandler = null;
        }
    }
}