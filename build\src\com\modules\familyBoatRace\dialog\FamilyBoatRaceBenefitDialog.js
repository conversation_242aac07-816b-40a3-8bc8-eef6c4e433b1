import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { com } from "../../../ui/layaMaxUI";
import { UIHTMLDiv } from "../../baseModules/UIHTMLDiv";
import { FamilyBoatRaceDataCenter } from "../data/FamilyBoatRaceDataCenter";
import { TipsUtil } from "../../../util/TipsUtil";
import { YueKaDataCenter } from "../../welfare/data/YueKaDataCenter";
import { XmlFormatVo } from "../../../util/XmlFormatVo";
import { HtmlUtil } from "../../../util/HtmlUtil";
import { EYueKaType } from "../../../auto/ConstAuto";
export class FamilyBoatRaceBenefitDialog extends com.ui.res.familyboatrace.FamilyBoatRaceBenefitDialogUI {
    constructor() {
        super();
    }
    initUI() {
        this.topPanelUI.titleNameTxt.text = window.iLang.L2_FEN_HONG_CHI.il();
        this.navShow = 0 /* NONE */;
        this.contentBox.vScrollBar.visible = false;
        UIHTMLDiv.SetUIHTMLDiv(this.content, 24, "#551511", 6);
    }
    btnClick(name) {
        super.btnClick(name);
        if (name == "btnget") {
            if (FamilyBoatRaceDataCenter.instance.bonus && FamilyBoatRaceDataCenter.instance.bonus.all_auction > 0) {
                let curYueka = YueKaDataCenter.instance.getMaxYuekaType();
                if (curYueka > 0) {
                    this.showConfirmTip(curYueka);
                }
                else {
                    TipsUtil.showTips(window.iLang.L2_XU_YAO_JI_HUO_YUE_KA_CAI_KE_RECEIVE_FEN_HONG.il());
                }
            }
        }
        else if (name == "btnlog") {
            this.dispatchEvent("OPEN_FAMILY_BOAT_RACE_BONUS_LOG_DIALOG" /* OPEN_FAMILY_BOAT_RACE_BONUS_LOG_DIALOG */);
        }
    }
    showConfirmTip(curYueka) {
        let curCardName = YueKaDataCenter.instance.getYuekaName(curYueka);
        let allBonus = FamilyBoatRaceDataCenter.instance.bonus.all_auction;
        let myGetBonus = FamilyBoatRaceDataCenter.instance.bonus.get_auction;
        let curRate = this.getYuekaBonusRate(curYueka) / 100;
        // let curGetBonus = Math.ceil(allBonus * curRate / 100);
        let strFetch = (curYueka == EYueKaType.TYPE_4_KING) ? window.iLang.L2_KE_RECEIVE.il() : window.iLang.L2_ZHI_NENG_RECEIVE.il();
        let tip1 = window.iLang.L2_DANG_QIAN_FEN_HONG_CHI_ZHONG_JIANG_LI_ch10_P0_ZUAN_SHI_BR_NIN_DE_ZUI_GAO_YUE.il([HtmlUtil.font(allBonus, "#2da332"), HtmlUtil.font(curCardName, "#2da332")]);
        let tip2 = strFetch + window.iLang.L2_P0_ch22_FEN_HONG_ch10_P1_ZUAN_SHI_BR.il([HtmlUtil.font(curRate, "#2da332"), HtmlUtil.font(myGetBonus, "#2da332")]);
        let confirmTip = tip1 + tip2 + CfgCacheMapMgr.cfg_boat_race_miscCache.get("bonus_confirm_tip").value;
        console.log(confirmTip);
        TipsUtil.showDialog(this, confirmTip, window.iLang.L2_TI_SHI.il(), function () {
            FamilyBoatRaceDataCenter.instance.m_boat_race_op_tos(4 /* BONUS */);
        }, { isFlipBtn: true });
    }
    //当前月卡的分红比例
    getYuekaBonusRate(curYueka) {
        let arr = CfgCacheMapMgr.cfg_boat_race_miscCache.get("auction_gold_fetch_yueka_type").value.split("|");
        for (let strType of arr) {
            let arrYuekaBonus = XmlFormatVo.GetArr(strType);
            if (arrYuekaBonus && arrYuekaBonus.length > 1 && arrYuekaBonus[0] == curYueka) {
                return Number(arrYuekaBonus[1]);
            }
        }
        return 0;
    }
    addEvent() {
        this.addEventListener("FAMILY_BOAT_RACE_BONUS" /* FAMILY_BOAT_RACE_BONUS */, this, this.updateData);
    }
    onOpen() {
        this.content.innerHTML = CfgCacheMapMgr.cfg_boat_race_miscCache.get("bonus_tip").value;
        this.content.height = this.content.contextHeight;
        this.updateData();
    }
    updateData() {
        if (FamilyBoatRaceDataCenter.instance.bonus) {
            this.benefitnum.text = FamilyBoatRaceDataCenter.instance.bonus.all_auction.toString();
            this.btnget.visible = FamilyBoatRaceDataCenter.instance.bonus.all_auction > 0;
        }
        else {
            this.close();
        }
    }
}
