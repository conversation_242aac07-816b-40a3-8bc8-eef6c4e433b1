import { ILaya } from "ILaya";
import { URL } from "laya/net/URL";
import { LayaGL } from "../../libs/laya/layagl/LayaGL";
import { Browser } from "../../libs/laya/utils/Browser";
import { ConfigManager } from "../com/managers/ConfigManager";
import { ESkeletonResType, ESkeletonType, SkeletonData } from "../com/modules/baseModules/skeleton/SkeletonData";
import { RoleDataCenter } from "../com/modules/role/data/RoleDataCenter";
import { GameUtil } from "../com/util/GameUtil";
import { GlobalConfig } from "./GlobalConfig";
import { Laya3D } from "../../libs/Laya3D";
import { HeroExtKey } from "../com/scene2d/BattleConst";
import { ActorType } from "../com/scene2d/SceneConst";
import { CfgCacheMapMgr } from "../com/cfg/CfgCacheMapMgr";
import { PDataCenter } from "../com/preload/PDataCenter";
import { PUrlConfig } from "../com/preload/PUrlConfig";
import { ConsoleUtils } from "../com/util/ConsoleUtils";
export class UrlConfig {
    //获取mainui中的图片地址
    static getMainuiResUrl(iconName) {
        if (iconName.endsWith(".png") == false) {
            iconName += ".png";
        }
        let pngUrl = "res/ui/mainui/" + iconName;
        if (this.checkResExist(pngUrl)) {
            return pngUrl;
        }
        else {
            return "mainui/" + iconName;
        }
        // if (Loader.textureMap[URL.formatURL("mainui/" + iconName)]){
        //     return "mainui/" + iconName;
        // }else{
        //     return pngUrl;
        // }
    }
    static get MENU2_RES() {
        return UrlConfig.BASE_ATLAS_URL + UrlConfig.COMMON_UI_SKIN("mainui2");
    }
    static get COMMON_RES() {
        return UrlConfig.BASE_ATLAS_URL + UrlConfig.COMMON_UI_SKIN("common");
    }
    static get COMMON2_RES() {
        return UrlConfig.BASE_ATLAS_URL + UrlConfig.COMMON_UI_SKIN("common2");
    }
    static get COMMON3_RES() {
        return UrlConfig.BASE_ATLAS_URL + UrlConfig.COMMON_UI_SKIN("common3");
    }
    static get COMMON4_RES() {
        return UrlConfig.BASE_ATLAS_URL + UrlConfig.COMMON_UI_SKIN("common4");
    }
    static get COMMON5_RES() {
        return UrlConfig.BASE_ATLAS_URL + UrlConfig.COMMON_UI_SKIN("common5");
    }
    static get COMMON_RAW_RES() {
        return UrlConfig.BASE_ATLAS_URL + UrlConfig.COMMON_UI_SKIN("common_raw");
    }
    static COMMON_UI_SKIN(url) {
        if (GlobalConfig.common_ui != null && GlobalConfig.common_ui.length > 0 && GlobalConfig.common_ui != "0") {
            return "common_" + GlobalConfig.common_ui + "/" + url + ".atlas";
        }
        return url + ".atlas";
    }
    static get D3MODELS_MANIFEST() {
        return UrlConfig.BASE_RES_URL + "3d/models/Conventional/manifest.json";
    }
    static init() {
        if (UrlConfig.isInit == false) {
            UrlConfig.isInit = true;
            UrlConfig.COMMON_PATH = "common/";
            UrlConfig.COMMON3_PATH = "common3/";
            UrlConfig.RES_BASE_DIR_2D = "res/2d/";
            UrlConfig.RES_BASE_DIR_3D = "res/3d/";
            // if (GlobalConfig._useTotal || GlobalConfig._useTotal1) {
            //     UrlConfig.SD_CONFIG_URL = UrlConfig.RES_BASE_DIR_3D + "sda.bin";
            // }
            // else {
            //     UrlConfig.SD_CONFIG_URL = UrlConfig.RES_BASE_DIR_3D + "sd.bin";
            // }
            Laya3D.START_WITH = UrlConfig.RES_BASE_DIR_3D;
            // UrlConfig.RES_BASE_DIR_3D = "res/3d/";
            // UrlConfig.EFFECT_PATH = UrlConfig.BASE_RES_URL + "effects/";
            // UrlConfig.D2MODELS_PATH = UrlConfig.BASE_RES_URL + "models/allinone/";
            // UrlConfig.LH_CONFIG_URL = UrlConfig.RES_BASE_DIR_3D + "lh.bin";
            // UrlConfig.D3_CONFIG_URL = UrlConfig.RES_BASE_DIR_3D + "3d.bin";
            // UrlConfig.D3INFO_CONFIG_URL = UrlConfig.RES_BASE_DIR_3D + "info.bin";
            UrlConfig.EFFECT_PATH = UrlConfig.BASE_RES_URL + "effects/";
            UrlConfig.D2MODELS_PATH = UrlConfig.BASE_RES_URL + "models/allinone/";
            UrlConfig.PLATFORM_RES_URL = PUrlConfig.PLATFORM_RES_URL;
            if (GlobalConfig.isYeGame) {
                UrlConfig.PLATFORM_RES_URL = GameUtil.webUrlChange(UrlConfig.PLATFORM_RES_URL);
                UrlConfig.PLATFORM_BASE = GameUtil.webUrlChange(UrlConfig.PLATFORM_BASE);
            }
            //UrlConfig.SPLASH_URL = UrlConfig.PLATFORM_RES_URL + "splash.jpg";
            UrlConfig.LOGIN_BG_URL = UrlConfig.PLATFORM_RES_URL + "login.jpg";
            UrlConfig.SPLASH_URL = UrlConfig.LOGIN_BG_URL;
            UrlConfig.GAME_LOGO_URL = UrlConfig.PLATFORM_RES_URL + "logo.png";
            UrlConfig.LOADING_BG_URL = UrlConfig.PLATFORM_RES_URL + "loading.jpg";
            UrlConfig.SELECT_SERVER_BG_URL = UrlConfig.BASE_RES_UI_URL + "selectServer/pbg.png";
            UrlConfig.PANEL_BG_URL = UrlConfig.BASE_RES_UI_URL + "dialogBg/panelBg1.png";
            UrlConfig.LOGIN_TEST_BTN_URL = UrlConfig.BASE_RES_UI_URL + "login/testBtn.png";
        }
    }
    static initafter() {
        if (GlobalConfig.isMiniGameAll || Browser.onIOS) {
            if (Browser.onIOS && LayaGL.layaGPUInstance._compressedTexturePvrtc) {
                UrlConfig.VER_URL = "res/ver/veri.txt";
            }
        }
    }
    /**二维码 */
    static get QQ_EWM() {
        return "res/ui/vipKefu/" + GlobalConfig.PlatName + "_qqewm.png";
        //return "res/ui/platform/" + GlobalConfig.PlatName + "/qqewm.png";
    }
    static getResCommon(skinName) {
        return UrlConfig.BASE_RES_UI_URL + "common/" + skinName;
    }
    static getResCommon2(skinName) {
        return UrlConfig.BASE_RES_UI_URL + "common2/" + skinName;
    }
    /**
     * 默认头像
     */
    static getDefaultHead() {
        return UrlConfig.GOODS_RES_URL + "199901.png";
    }
    /**
     * 默认头像框
     */
    static getDefaultHeadFrame() {
        return UrlConfig.getHead(2001);
    }
    /**获取头像 */
    static getHead(head) {
        if (RoleDataCenter.instance.isBaseHead(head) || RoleDataCenter.instance.isBaseBodyShow(head)) {
            return UrlConfig.GOODS_RES_URL + `${head}.png`;
        }
        let cfg = ConfigManager.cfg_profileCache.get(head);
        if (cfg) {
            if (cfg.need_hero_fashion > 0) {
                // let fashion: cfg_fashion = ConfigManager.cfg_fashionCache.get(cfg.need_hero_fashion);
                // if (fashion) {
                // return UrlConfig.GOODS_RES_URL + `${cfg.need_hero_fashion}.png`;
                // }
                let skin = ConfigManager.cfg_hero_skinCache.get(cfg.need_hero_fashion);
                if (skin) {
                    return UrlConfig.GOODS_RES_URL + `${skin.icon}.png`;
                }
            }
            if (cfg.need_vip != 0) {
                return UrlConfig.GOODS_RES_URL + cfg.fashion_id + ".png";
            }
            return UrlConfig.getHeroHead(cfg.need_hero_id);
        }
        return UrlConfig.GOODS_RES_URL + "199901.png";
    }
    /**获取头像框 */
    static getHeadFrame(head_frame) {
        let cfg = ConfigManager.cfg_profileCache.get(head_frame);
        if (cfg) {
            return UrlConfig.ROLE_TITLE_URL + "head_frame/" + cfg.head_img + ".png";
        }
        return UrlConfig.ROLE_TITLE_URL + "head_frame/2001_1.png";
    }
    /**获取头像框底板 */
    static getHeadFrameBg(head_frame) {
        let cfg = ConfigManager.cfg_profileCache.get(head_frame);
        if (cfg) {
            return UrlConfig.ROLE_TITLE_URL + "head_frame/" + cfg.ext_1 + ".png";
        }
        return UrlConfig.ROLE_TITLE_URL + "head_frame/frame_bg_def.png";
    }
    /** 获取称号资源路径 */
    static getRoleTitle(title_id, isBig = false) {
        if (isBig == true) {
            return UrlConfig.BASE_RES_UI_URL + "title/big/" + title_id + ".png";
        }
        return UrlConfig.BASE_RES_UI_URL + "title/" + title_id + ".png";
    }
    /** 获取聊天称号资源路径 */
    static getChatRoleTitle(title_id) {
        return UrlConfig.BASE_RES_UI_URL + "title/chat/" + title_id + ".png";
    }
    /** 通过配置，获取英雄头像 */
    static getHeroHeadByCfg(skin) {
        if (skin && ConfigManager.cfg_client_w3_skinCache.has(skin)) {
            let cfg = ConfigManager.cfg_client_w3_skinCache.get(skin);
            return UrlConfig.GOODS_RES_URL + cfg.icon + ".png";
        }
        return UrlConfig.GOODS_RES_URL + "199901.png";
    }
    /** 通过英雄ID获取英雄头像 */
    static getHeroHead(typeId) {
        if (typeId > 0) {
            let cfg = ConfigManager.cfg_hero_baseCache.get(typeId);
            return UrlConfig.getHeroHeadByCfg(cfg.skin);
        }
        return UrlConfig.GOODS_RES_URL + "199901.png";
    }
    /** 通过怪物ID获取怪物头像 */
    static getMonsterHead(typeId) {
        if (typeId > 0) {
            let cfg = ConfigManager.cfg_monsterCache.get(typeId);
            return UrlConfig.getHeroHeadByCfg(cfg.skin);
        }
        return UrlConfig.GOODS_RES_URL + "199901.png";
    }
    /**获取武将卡牌头像 */
    static getHeroHeadBody(skin) {
        if (skin && ConfigManager.cfg_client_w3_skinCache.has(skin)) {
            let cfg = ConfigManager.cfg_client_w3_skinCache.get(skin);
            return UrlConfig.HERO_RES_URL + `heroicon/${cfg.skin_half}.png`;
        }
        return UrlConfig.HERO_RES_URL + "heroicon/0.png";
    }
    /**获取英雄卡牌头像 */
    static getHeroHalfBody(skin) {
        if (skin && ConfigManager.cfg_client_w3_skinCache.has(skin)) {
            let cfg = ConfigManager.cfg_client_w3_skinCache.get(skin);
            return UrlConfig.HERO_RES_URL + `halfBody/${cfg.skin_half}.png`;
        }
        return UrlConfig.HERO_RES_URL + "halfBody/0.png";
    }
    /***获得物品图标 */
    static getGoodsIcon(cfg) {
        if (cfg) {
            return UrlConfig.GOODS_RES_URL + cfg.icon + ".png";
        }
        return "";
    }
    /***获得坐骑物品图标 */
    static getGoodsRideIcon(typeid, star = 0) {
        // let cfg: cfg_ride_star = ConfigManager.getRideStar(typeid, star);
        // if (cfg) {
        //     return UrlConfig.GOODS_RES_URL + cfg.icon + ".png";
        // }
        return "";
    }
    /***通过道具id获得物品图标 */
    static getGoodsIconByTypeId(type_id) {
        let cfg = ConfigManager.cfg_itemCache.get(type_id);
        return UrlConfig.getGoodsIcon(cfg);
    }
    /**获取武将2d模型 w7w8w9废弃了*/
    // static getHero2dModel(hero_type_id: number): string {
    //     let cfgBase: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(hero_type_id);
    //     if (cfgBase) {
    //         return UrlConfig.HERO_MODELS_PATH_2D + cfgBase.skin + ".png";
    //     }
    //     return "";
    // }
    /**根据形象获取2d模型 w7w8w9废弃了 */
    // static getHero2dModelByAppearance(appearanceId: number): string {
    //     let cfg: cfg_role_profile = ConfigManager.cfg_profileCache.get(appearanceId);
    //     if (cfg) {
    //         if (cfg.need_hero_id > 0) {
    //             return UrlConfig.getHero2dModel(cfg.need_hero_id);
    //         } else {
    //             return UrlConfig.HERO_MODELS_PATH_2D + cfg.head_img + ".png";
    //         }
    //     }
    //     return "";
    // }
    static getPrefixUrl(prefixLv) {
        // let cfg = ConfigManager.cfg_prefixCache.get(prefixLv);
        // if (cfg && cfg.icon.length > 2) {
        //     return "common/title_" + cfg.prefix_level + ".png";
        // }
        return "";
    }
    static get RES_2D_ACTION() {
        return UrlConfig.RES_BASE_DIR_2D + "action/";
    }
    /**获取骨骼动画资源 */
    static get_res_2d_url(name, type, suffix = ".sk") {
        let resDir = "2d/";
        let animDir = "";
        switch (type) {
            case ESkeletonType.EFFECT:
                animDir = "effects";
                break;
            case ESkeletonType.UI_EFFECT:
                animDir = "uieffect";
                break;
            case ESkeletonType.MODEL_ACTION:
                animDir = "action";
                break;
            case ESkeletonType.MODEL_LIHUI:
                animDir = "lihui";
                break;
        }
        return UrlConfig.BASE_RES_URL + resDir + animDir + "/" + name + suffix;
    }
    static get_sk_url_by_mode(mode, skName, type) {
        switch (mode) {
            case ESkeletonResType.SPINE_SKELETON:
                return UrlConfig.get_res_2d_spine_url(skName, type);
            case ESkeletonResType.SPINE_SKELETON_ZHAN_JI_SINGLE:
                return UrlConfig.get_res_2d_spine_zhanji_url(skName, type);
            case ESkeletonResType.SPINE_SKELETON_ZHAN_JI:
                return UrlConfig.get_res_2d_spine_zhanji_url(skName, type);
            case ESkeletonResType.SPINE_SKELETON_GROUP:
                return UrlConfig.get_res_2d_spine_group_url(skName, type);
            case ESkeletonResType.LAYA_SKELETON:
                return UrlConfig.get_res_2d_url(skName, type);
            case ESkeletonResType.LAYA_ANI:
                return UrlConfig.get_res_2d_ani_url(skName, type, mode);
            case ESkeletonResType.LAYA_ANI_FRAME:
                return UrlConfig.get_res_2d_ani_url(skName, type, mode);
            case ESkeletonResType.JY_ANI_3D_TO_2D:
                // return UrlConfig.get_res_2d_ani_url(skName, type, mode);
                return UrlConfig.JY_ANI_3D_TO_2D_DIR + skName + ".atlas";
        }
    }
    /**
     * 获取animation的.ani资源
     */
    static get_res_2d_ani_url(name, type, resType) {
        let url = "";
        switch (type) {
            case ESkeletonType.EFFECT:
                url = UrlConfig.LAYA_ANIMATION_MODEL_EFFECTS_PATH;
                break;
            case ESkeletonType.MODEL_ACTION:
                url = UrlConfig.LAYA_ANIMATION_MODEL_ACTION_PATH;
                break;
            case ESkeletonType.MODEL_LIHUI:
                url = UrlConfig.LAYA_ANIMATION_MODEL_LIHUI_PATH;
                break;
            case ESkeletonType.UI_EFFECT:
                url = UrlConfig.LAYA_ANIMATION_MODEL_UI_EFFECTS_PATH;
                break;
            default:
                console.error("-------------getLayaAnimatinUrl ,type error. type = " + type);
        }
        if (resType == ESkeletonResType.JY_ANI_3D_TO_2D) {
            url = UrlConfig.JY_ANI_3D_TO_2D_DIR;
        }
        let suffix = resType == ESkeletonResType.LAYA_ANI ? ".ani" : ".atlas";
        return url + name + suffix;
    }
    // static getSkeletonPngUrl(name: string, type: ESkeletonType): string {
    //     let url = this.getSkeletonUrl(name, type);
    //     return url.replace(".sk", ".png").replace(".json", ".png").replace(".ani", ".png");
    // }
    static get_skeleton_type_by_url(url) {
        if (url.indexOf(UrlConfig.SPINE_ACTION_PATH) >= 0 || url.indexOf(UrlConfig.LAYA_ANIMATION_MODEL_ACTION_PATH) >= 0) {
            return ESkeletonType.MODEL_ACTION;
        }
        else if (url.indexOf(UrlConfig.SPINE_EFFECTS_PATH) >= 0 || url.indexOf(UrlConfig.LAYA_ANIMATION_MODEL_EFFECTS_PATH) >= 0) {
            return ESkeletonType.EFFECT;
        }
        else if (url.indexOf(UrlConfig.SPINE_LIHUI_PATH) >= 0 || url.indexOf(UrlConfig.LAYA_ANIMATION_MODEL_LIHUI_PATH) >= 0) {
            return ESkeletonType.MODEL_LIHUI;
        }
        else if (url.indexOf(UrlConfig.SPINE_UI_EFFECTS_PATH) >= 0 || url.indexOf(UrlConfig.LAYA_ANIMATION_MODEL_UI_EFFECTS_PATH) >= 0) {
            return ESkeletonType.UI_EFFECT;
        }
        return ESkeletonType.DEFAULT;
    }
    /**
     * 获取spine的json资源
     * 少用这个,尽量用SkeletonManager.ins.getSkUrlByCheckMode
     */
    static get_res_2d_spine_url(name, type) {
        let url = "";
        switch (type) {
            case ESkeletonType.EFFECT:
                url = UrlConfig.SPINE_EFFECTS_PATH;
                break;
            case ESkeletonType.MODEL_ACTION:
                url = UrlConfig.SPINE_ACTION_PATH;
                break;
            case ESkeletonType.MODEL_LIHUI:
                /**
                 * 优先读取lihui下的资源,如果找不到,则读取action下的show.json
                 */
                url = UrlConfig.SPINE_LIHUI_PATH;
                // if(name._has("/") == false){
                //     let vo = SkeletonData_Grp.getSkGrpVo(name, type);
                //     if (vo) {
                //         name += "/" + vo.groupSupportActionList[0];
                //     }else{
                //         url = UrlConfig.SPINE_ACTION_PATH;
                //         name += "/show";
                //     }
                // }
                break;
            case ESkeletonType.UI_EFFECT:
                url = UrlConfig.SPINE_UI_EFFECTS_PATH;
                break;
            default:
                ConsoleUtils.warn("-------------get_res_2d_spine_url ,type error. type = " + type);
                return "";
            // console.error("-------------getSkeletonJsonUrl ,type error. type = " + type);
        }
        let fullUrl = url + name + SkeletonData.JSON;
        if (this.checkResExist(fullUrl) == false) {
            fullUrl = url + name + SkeletonData.SKEL;
        }
        if (this.checkResExist(fullUrl) == false) {
            fullUrl = url + name + SkeletonData.BIN;
        }
        return fullUrl;
    }
    static get_res_2d_spine_group_url(name, type) {
        return this.get_res_2d_spine_url(name, type).split(".")[0];
    }
    static get_res_2d_spine_zhanji_url(skName, type, suffix = SkeletonData.JSON) {
        // let vo = SkeletonData_Grp.getSkGrpVo(skName, type);
        // suffix = vo ? vo.suffix : suffix;
        let url = "";
        switch (type) {
            case ESkeletonType.EFFECT:
                url = UrlConfig.SPINE_EFFECTS_ZHAN_JI_PATH;
                break;
            case ESkeletonType.MODEL_ACTION:
                url = UrlConfig.SPINE_ACTION_ZHAN_JI_PATH;
                break;
            case ESkeletonType.MODEL_LIHUI:
                url = UrlConfig.SPINE_LIHUI_ZHAN_JI_PATH;
                //renfazhanji立绘兼容
                if (skName.indexOf(SkeletonData.tag_slash) < 0) {
                    skName += "/show";
                }
                break;
            case ESkeletonType.UI_EFFECT:
                url = UrlConfig.SPINE_UI_EFFECTS_ZHAN_JI_PATH;
                break;
            default:
                ConsoleUtils.warn("-------------get_res_2d_spine_zhanji_url ,type error. type = " + type);
                return "";
            // console.error("-------------getSkeletonJsonUrl ,type error. type = " + type);
        }
        return url + skName + suffix;
    }
    /**获得英雄皮肤 */
    static getsetSkins(actor) {
        var skin;
        var herobase;
        let actor_type = actor.actor_type;
        if (ActorType.Hero == actor_type) {
            herobase = ConfigManager.cfg_hero_baseCache.get(actor.type_id);
            if (!herobase) {
                console.error("配置表 cfg_hero_base 找不到：", actor.type_id);
            }
            if (actor.skin_id == 0) {
                skin = herobase.skin;
                // skin = herobase.skin3d;
            }
            else {
                let skinId = 0;
                let cfg = ConfigManager.cfg_hero_skin_levelCache.get(actor.skin_id);
                if (cfg) {
                    skinId = cfg.skin_id;
                }
                else if (ConfigManager.cfg_hero_skinCache.has(actor.skin_id)) {
                    skinId = actor.skin_id;
                }
                let skinCfg = ConfigManager.cfg_hero_skinCache.get(skinId);
                skin = skinCfg ? skinCfg.skin : herobase.skin;
            }
        }
        else if (ActorType.Monster == actor_type || ActorType.TDMonster == actor_type || ActorType.TDBoss == actor_type) {
            let isShowBeast = false;
            for (let kv of actor.hero_ext) {
                if (kv.key == HeroExtKey.CLIENT_SHOW_BEAST) {
                    isShowBeast = kv.val == 1;
                }
            }
            var monsterCfg = ConfigManager.cfg_monsterCache.get(actor.type_id);
            if (!monsterCfg)
                console.error("配置表 cfg_monster 找不到：", actor.type_id);
            skin = monsterCfg.skin;
            if (isShowBeast && ConfigManager.isBeast()) { //怪物默认显示兽将
                let list = ConfigManager.cfg_hero_skin_listCache.get(monsterCfg.hero_id);
                let heroSkinCfg = list ? list.find(cfg => cfg.is_beast == 1) : null;
                skin = heroSkinCfg && heroSkinCfg.skin || skin;
            }
        }
        else if (ActorType.Summoned == actor_type) {
            let cfg = CfgCacheMapMgr.cfg_skill_summonCache.get(actor.type_id);
            skin = cfg && cfg.skin_id || skin || "";
        }
        else if (ActorType.Lord == actor_type) {
            if (actor.skin_id) {
                skin = actor.skin_id + "";
            }
            else {
                let cfg = CfgCacheMapMgr.cfg_lord_baseCache.get(actor.type_id);
                if (cfg) {
                    skin = cfg.skin;
                }
            }
        }
        else if (ActorType.SoulCard == actor_type) {
            let cfg = CfgCacheMapMgr.cfg_client_w3_skinCache.get(actor.type_id);
            if (cfg) {
                skin = cfg.skin_id;
            }
        }
        return skin;
    }
    static getResUIUrl(fileName, imgName) {
        if (fileName.endsWith("/")) {
            return UrlConfig.BASE_RES_UI_URL + fileName + imgName;
        }
        else {
            return UrlConfig.BASE_RES_UI_URL + fileName + "/" + imgName;
        }
    }
    static getPlatformRes(platform) {
        return UrlConfig.PLATFORM_BASE_RES + platform + ".atlas";
    }
    static get NowPlatformRes() {
        return UrlConfig.getPlatformRes(UrlConfig.platform);
    }
    static getPlatformUrl(platform) {
        return UrlConfig.PLATFORM_BASE + platform + "/";
    }
    static get NowPlatformUrl() {
        return UrlConfig.getPlatformUrl(UrlConfig.platform);
    }
    static get NowPlatform() {
        return "platform_" + UrlConfig.platform;
    }
    static get platform() {
        if (UrlConfig._platform == -1) {
            UrlConfig._platform = GlobalConfig.guajiindex < 1 ? 1 : GlobalConfig.guajiindex;
        }
        return UrlConfig._platform;
        // return 2;
    }
    static get BASE_WORLD_MAP_TILED_URL() {
        return UrlConfig.NowPlatformUrl + "worldMap/";
    }
    static get BASE_WORLD_MAP_TILED_RES_URL() {
        return UrlConfig.NowPlatformUrl + "worldMap/res/";
    }
    static get FIGHT_MAP_URL() {
        return UrlConfig.NowPlatformUrl + "fightmap/";
    }
    static get GUAJI_MAP_URL() {
        return UrlConfig.NowPlatformUrl + "guajimap/";
    }
    static get GUAJI_BUILD_URL() {
        return UrlConfig.NowPlatformUrl + "guajimap/guaJiBuild/";
    }
    /**
     * laya原生方式的特效路径
     * @param effName 特效名称
     * @param subDir 子目录
     */
    static get_effect_laya_way_url(effName, subDir = "") {
        let url = UrlConfig.EFFECT_LAYA_WAY_PATH;
        if (subDir) {
            url += subDir + "/";
        }
        url += effName + ".atlas";
        return url;
    }
    static getUIUrl() {
        return { url: UrlConfig.UI_URL, type: ILaya.Loader.JSON };
    }
    static getUIBinUrl() {
        return { url: UrlConfig.UIBIN_URL, type: ILaya.Loader.BUFFER };
    }
    static getUIThemeUrl() {
        return { url: UrlConfig.UITHEME_URL, type: ILaya.Loader.BUFFER };
    }
    /**
     * 判断资源是否存在,主要判断Url.version
     */
    static checkResExist(resUrl) {
        return !!URL.version[resUrl];
    }
}
UrlConfig.CONFIG_URL = "res/config/w7cfg.bin";
UrlConfig.VER_URL = "res/ver/ver.txt";
// static LEVEL_RES_MAP_JSON: string = "res/level_res_map.json";
// static LEVEL_RES_MAP_JSON_GUA_JI: string = "res/level_res_map_guaji.json";
UrlConfig.RES_LAZY_LOAD_GUA_JI_M2 = "res/res_lazy_load_guaji_m2.json";
UrlConfig.RES_LAZY_LOAD_GUA_JI_BUILDING_M3 = "res/res_lazy_load_guaji_building_m3.json";
UrlConfig.RES_LAZY_LOAD_TD_MAIN = "res/res_lazy_load_td_main.json";
UrlConfig.BASE_RES_URL = "res/";
UrlConfig.WEB_URL = "web_";
UrlConfig.ACTIVITY_RES_URL = "res/ui/activity/";
UrlConfig.LOGIN_ACTIVITY_RES_URL = "res/ui/loginActivity/";
UrlConfig.PLATFORM_RES_URL = "res/ui/platform/";
UrlConfig.GOODS_RES_URL = "res/goods/";
UrlConfig.TAB_ICON_RES_URL = "res/ui/tabIcon/";
UrlConfig.HERO_RES_URL = "res/hero/";
UrlConfig.SKILL_RES_URL = "res/skill/";
UrlConfig.SKILL_RES_V2_URL = "res/v2_skill/";
UrlConfig.FIGHT_RES_URL = "res/ui/fight/";
UrlConfig.FISH_RES_URL = "res/ui/fish/";
UrlConfig.FISH_HANDBOOK_RES_URL = "res/ui/fish/handbook/";
UrlConfig.BASE_RES_UI_URL = "res/ui/";
//实力预览
UrlConfig.HERO_ACTUAL_PREVIEW_RES_URL = "res/ui/heroactualpreview/";
UrlConfig.BASE_RES_ATLAS_URL = "res/atlas/";
UrlConfig.BASE_RES_I18N_URL = "res/_i18n/";
UrlConfig.VIDEO_RES_URL = "res/videos/";
UrlConfig.WEB_BASE_RES_UI_URL = "res/web_ui/";
UrlConfig.ROLE_TITLE_URL = "res/ui/roleTitle/";
UrlConfig.RIDE_URL = "res/ui/ride/";
UrlConfig.RANK_URL = "res/ui/rank/";
UrlConfig.FORGE_URL = "res/ui/forge/";
UrlConfig.EQUIP_URL = "res/ui/equip/";
UrlConfig.WAR_RES_URL = "res/ui/war/";
UrlConfig.MAP_RES_URL = "res/ui/map/";
UrlConfig.TIME_ACTIVITY_RES_URL = "res/ui/timeActivity/";
UrlConfig.FLY_FONT_URL = "res/ui/flyFont/";
UrlConfig.ARENA_URL = "res/ui/arena/";
UrlConfig.ARENA_TYPE_URL = "res/ui/arenaType/";
UrlConfig.GUIDE_URL = "res/ui/guide/";
UrlConfig.TREASURE_URL = "res/ui/treasure/";
UrlConfig.CREATE_PLAYER_URL = "res/ui/createPlayer/";
UrlConfig.STORY_URL = "res/ui/story/";
UrlConfig.SPECIALFUNDS_URL = "res/ui/specialFunds/";
UrlConfig.WX_SHARE_PATH = "res/ui/wxShare/";
UrlConfig.VIP_KEFU_URL = "res/ui/vipKefu/";
UrlConfig.QQ_URL = "res/ui/qq/";
UrlConfig.MATCH_URL = "res/ui/match/";
UrlConfig.BAG_URL = "res/ui/bag/";
UrlConfig.WIN_URL = "res/config/win.bin";
UrlConfig.WAITING_URL = "res/ui/loading.png";
UrlConfig.DIVINE_URL = "res/ui/divine/";
UrlConfig.DIVINE_HEAD_URL = "res/ui/divine/head/";
UrlConfig.HERO_MODELS_PATH_2D = "res/hero/2d/";
UrlConfig.SKELETON_MODEL_ACTION_PATH = "res/2d/action/";
UrlConfig.SKELETON_MODEL_LIHUI_PATH = "res/2d/lihui/";
UrlConfig.SKELETON_MODEL_EFFECTS_PATH = "res/2d/effects/";
UrlConfig.SKELETON_MODEL_UI_EFFECTS_PATH = "res/2d/uieffect/";
UrlConfig.SPINE_BASE_PATH = "res/2d_spine/";
UrlConfig.SPINE_ACTION_PATH = "res/2d_spine/action/";
UrlConfig.SPINE_LIHUI_PATH = "res/2d_spine/lihui/";
UrlConfig.SPINE_EFFECTS_PATH = "res/2d_spine/effects/";
UrlConfig.SPINE_UI_EFFECTS_PATH = "res/2d_spine/uieffect/";
UrlConfig.SPINE_SKELETON_MODEL_ACTION_PATH = "res/2d_spine/action/";
UrlConfig.SPINE_SKELETON_MODEL_LIHUI_PATH = "res/2d_spine/lihui/";
UrlConfig.SPINE_SKELETON_MODEL_EFFECTS_PATH = "res/2d_spine/effects/";
UrlConfig.SPINE_SKELETON_MODEL_UI_EFFECTS_PATH = "res/2d_spine/uieffect/";
/**忍法战姬的spine资源路径 */
UrlConfig.SPINE_ACTION_ZHAN_JI_PATH = "res/2d_spine/action_chongsheng/";
UrlConfig.SPINE_LIHUI_ZHAN_JI_PATH = "res/2d_spine/lihui_chongsheng/";
UrlConfig.SPINE_EFFECTS_ZHAN_JI_PATH = "res/2d_spine/effects_chongsheng/";
UrlConfig.SPINE_UI_EFFECTS_ZHAN_JI_PATH = "res/2d_spine/uieffects_chongsheng/";
// static SPINE_ACTION_ZHAN_JI_PATH: string = "";
// static SPINE_LIHUI_ZHAN_JI_PATH: string = "";
// static SPINE_EFFECTS_ZHAN_JI_PATH: string = "";
// static SPINE_UI_EFFECTS_ZHAN_JI_PATH: string = "";
UrlConfig.SPINE_ANI_BASE_PATH = "res/2d_ani/";
UrlConfig.LAYA_ANIMATION_MODEL_ACTION_PATH = "res/2d_ani/action/";
UrlConfig.LAYA_ANIMATION_MODEL_LIHUI_PATH = "res/2d_ani/lihui/";
UrlConfig.LAYA_ANIMATION_MODEL_EFFECTS_PATH = "res/2d_ani/effects/";
UrlConfig.LAYA_ANIMATION_MODEL_UI_EFFECTS_PATH = "res/2d_ani/uieffect/";
/**laya原生方式特效的主目录 */
UrlConfig.EFFECT_LAYA_WAY_PATH = "res/effects_laya/";
UrlConfig.JY_ANI_3D_TO_2D_DIR = UrlConfig.SPINE_ANI_BASE_PATH + "action_3to2_dir5/";
UrlConfig._3to2_tag_JSON_PATH = UrlConfig.SPINE_ANI_BASE_PATH + "3to2_frame_tag_info.json";
UrlConfig._3to2_SK_JSON_PATH = UrlConfig.SPINE_ANI_BASE_PATH + "3to2_frame_sk_info.json";
UrlConfig.GUA_JI_URL = "res/ui/guaJi/";
UrlConfig.DIALOG_BG_URL = "res/ui/dialogBg/";
UrlConfig.FAMILY_REDPACK_URL = "res/ui/familyRedPack/";
UrlConfig.MASTER_UI_URL = "res/ui/master/";
UrlConfig.LIE_ZHUAN_UI_URL = "res/ui/liezhuan/";
UrlConfig.HERO_SKIN_UI_URL = "res/ui/heroskin/";
UrlConfig.HERO_INFO_UI_URL = "res/ui/heroInfo/";
UrlConfig.HERO_INFO_V2_UI_URL = "res/ui/v2_heroInfo/";
UrlConfig.CROSSREAL_UI_URL = "res/ui/crossrealmwar/";
UrlConfig.PASS_BEHEAD_UI_URL = "res/ui/passBehead/";
UrlConfig.MAZE_UI_URL = "res/ui/maze/";
UrlConfig.BATTLE_TRIAL_UI_URL = "res/ui/battleTrial/";
UrlConfig.DOMINATE_PVP_UI_URL = `${UrlConfig.BASE_RES_UI_URL}dominate_pvp/`;
UrlConfig.FAMILY_BOAT_RACE_URL = "res/ui/familyboatrace/";
UrlConfig.BOAT_PEAK_URL = "res/ui/boatPeak/";
UrlConfig.PLATFORM_BASE = "res/ui/platform_";
UrlConfig.Lord_UI_URL = "res/ui/lord/";
UrlConfig.V2_Lord_UI_URL = "res/ui/v2_lord/";
UrlConfig.TD_TRIAL_UI_URL = "res/ui/tdTrial/";
//static COMP_URL: string = "res/atlas/comp.atlas";
UrlConfig.UN_PACK_URL = "res/ui/unpack.json";
// static   BASE_DIR:string = "res/";
UrlConfig.EFFECT_PATH = "res/effects/";
UrlConfig.BASE_ATLAS_URL = UrlConfig.BASE_RES_URL + "atlas/";
UrlConfig.RES_BASE_DIR_2D = "res/2d/";
UrlConfig.RES_BASE_DIR_3D = "res/3d/";
UrlConfig.DEFAUL_UPDATE_URL = `http://${PDataCenter.gameProjectName}.kkyou.cn/debugserverlists`;
// stat module start
UrlConfig.DEFAUL_STAT_URL = `http://central.${PDataCenter.gameProjectName}debug.kkyou.cn/api/`;
// stat module end
UrlConfig.DEFAUL_PLATFORM = "debug";
UrlConfig.PLATFORM_BASE_RES = UrlConfig.BASE_ATLAS_URL + "platform_";
UrlConfig.LOGIN_RES = UrlConfig.BASE_ATLAS_URL + "login.atlas";
UrlConfig.UI_URL = "res/ui.json";
UrlConfig.UIBIN_URL = "res/ui.bin";
UrlConfig.UITHEME_URL = "res/uitheme.bin";
UrlConfig.FILECONFIG_URL_W7 = "res/fileconfig_w7.json";
UrlConfig.HERO_ACTUAL_PREVIEW_RES = UrlConfig.BASE_ATLAS_URL + "heroactualpreview.atlas";
UrlConfig.FILECONFIG_URL_ORIGIN = "res/ui/fileconfig.json";
UrlConfig.MENU_RES = UrlConfig.BASE_ATLAS_URL + "mainui.atlas";
//static MENU2_RES: string = UrlConfig.BASE_ATLAS_URL + "mainui2.atlas";
UrlConfig.SELECT_SERVER_RES = UrlConfig.BASE_ATLAS_URL + "selectServer.atlas";
//static COMMON_RES: string = UrlConfig.BASE_ATLAS_URL + "common.atlas";
// static COMMON2_RES: string = UrlConfig.BASE_ATLAS_URL + "common2.atlas";
UrlConfig.STAGECOPY_RES = UrlConfig.BASE_ATLAS_URL + "stagecopy.atlas";
UrlConfig.STAGECOPY_UI_URL = "res/ui/stagecopy/";
UrlConfig.STAGECOPY_ASSETS_URL = "stagecopy/";
UrlConfig.FISH_RES = UrlConfig.BASE_ATLAS_URL + "fish.atlas";
UrlConfig.MASTER_TALENT_SCI_UI_URL = "res/ui/masterTalentScience/";
//塔楼之战
UrlConfig.TOWER_BATTLE = UrlConfig.BASE_ATLAS_URL + "towerBattle.atlas";
UrlConfig.TOWER_BATTLE_URL = "res/ui/towerBattle/";
/**政务系统 */
UrlConfig.TRAVEL_RES = UrlConfig.BASE_ATLAS_URL + "travel.atlas";
/**无双试炼系统 */
UrlConfig.TRIAL_RES = UrlConfig.BASE_ATLAS_URL + "trial.atlas";
/**活动开启提示界面 */
UrlConfig.SYS_OPEN_RES = UrlConfig.BASE_ATLAS_URL + "sysOpen.atlas";
UrlConfig.WAR_RES = UrlConfig.BASE_ATLAS_URL + "war.atlas";
/**加载页 */
UrlConfig.LOADING_RES = UrlConfig.BASE_ATLAS_URL + "loading.atlas";
/** 寻宝 */
UrlConfig.TREASURE_RES = UrlConfig.BASE_ATLAS_URL + "treasure.atlas";
UrlConfig.ROLE_RES = UrlConfig.BASE_ATLAS_URL + "role.atlas";
/** 七日目标 */
UrlConfig.SEVEN_GOAL_RES = UrlConfig.BASE_ATLAS_URL + "sevenGoal.atlas";
UrlConfig.ROLE_OTHER_INFO_RES = UrlConfig.BASE_ATLAS_URL + "roleOtherInfo.atlas";
UrlConfig.SETTING_RES = UrlConfig.BASE_ATLAS_URL + "setting.atlas";
UrlConfig.PAYMENT_VIP_RES = UrlConfig.BASE_ATLAS_URL + "paymentVip.atlas";
/**选服 */
// static DIALOG_RES: string = UrlConfig.BASE_ATLAS_URL + "dialogBox.atlas";
/**创角*/
UrlConfig.CREATE_PLAYER_RES = UrlConfig.BASE_ATLAS_URL + "createPlayer.atlas";
/**男 */
UrlConfig.CREATE_PLAYER_MAN_SPINE_RES = "20000002";
/**女 */
UrlConfig.CREATE_PLAYER_WOMAN_SPINE_RES = "10000006";
UrlConfig.TEST_RES = UrlConfig.BASE_ATLAS_URL + "test.atlas";
UrlConfig.DAILY_MISSION_RES = UrlConfig.BASE_ATLAS_URL + "dailyMission.atlas";
UrlConfig.FRIEND_RES = UrlConfig.BASE_ATLAS_URL + "friend.atlas";
UrlConfig.TAX_RES = UrlConfig.BASE_ATLAS_URL + "tax.atlas";
UrlConfig.LETTER_RES = UrlConfig.BASE_ATLAS_URL + "letter.atlas";
UrlConfig.FLY_FONT_RES = UrlConfig.BASE_ATLAS_URL + "flyFont.atlas";
UrlConfig.MASTER_HALO_RES = UrlConfig.BASE_ATLAS_URL + "masterHalo.atlas";
UrlConfig.WAR_FLAG_RES = UrlConfig.BASE_ATLAS_URL + "warflag.atlas";
/**铁匠铺 */
UrlConfig.FORGE_RES = UrlConfig.BASE_ATLAS_URL + "forge.atlas";
/**装备列表界面 */
UrlConfig.EQUIP_RES = UrlConfig.BASE_ATLAS_URL + "equip.atlas";
/**神装界面 */
UrlConfig.GOD_EQUIP_RES = UrlConfig.BASE_ATLAS_URL + "godEquip.atlas";
/**聊天 */
UrlConfig.CHAT_RES = UrlConfig.BASE_ATLAS_URL + "chat.atlas";
/**手册 */
UrlConfig.BOOK_MISSION_RES = UrlConfig.BASE_ATLAS_URL + "bookmission.atlas";
/**聊天 */
UrlConfig.CHAT_FACE_RES = UrlConfig.BASE_ATLAS_URL + "chatFace.atlas";
/**聊天气泡 */
UrlConfig.CHAT_SKIN_RES = UrlConfig.BASE_ATLAS_URL + "chatSkin.atlas";
/**充值 */
UrlConfig.RECHARGE_RES = UrlConfig.BASE_ATLAS_URL + "recharge.atlas";
/**新手豪礼 */
UrlConfig.NEW_PAY_RES = UrlConfig.BASE_ATLAS_URL + "newPay.atlas";
/**每日充值 */
UrlConfig.DAILY_PAY_RES = UrlConfig.BASE_ATLAS_URL + "dailyPay.atlas";
/**首充 */
UrlConfig.FIRST_RECHARGE_RES = UrlConfig.BASE_ATLAS_URL + "firstRecharge.atlas";
/**商城 */
UrlConfig.SHOP_RES = UrlConfig.BASE_ATLAS_URL + "shop.atlas";
/**历练主界面 */
UrlConfig.GROW_RES = UrlConfig.BASE_ATLAS_URL + "grow.atlas";
/**历练日常副本 */
UrlConfig.GROW_DAILY_MISSION_RES = UrlConfig.BASE_ATLAS_URL + "growDailyMission.atlas";
/**公会 */
UrlConfig.FAMILY_RES = UrlConfig.BASE_ATLAS_URL + "family.atlas";
/**公会科技 */
UrlConfig.FAMILY_SCIENCE_RES = UrlConfig.BASE_ATLAS_URL + "familyScience.atlas";
/**公会敌将 */
UrlConfig.FAMILY_BOSS_RES = UrlConfig.BASE_ATLAS_URL + "familyBoss.atlas";
/**公会战 */
UrlConfig.FAMILY_WAR_RES = UrlConfig.BASE_ATLAS_URL + "familyWar.atlas";
/**联盟赛艇 */
UrlConfig.FAMILY_BOATRACE_RES = UrlConfig.BASE_ATLAS_URL + "familyboatrace.atlas";
/**英雄技能TIPS */
UrlConfig.HERO_SKILL_RES = UrlConfig.BASE_ATLAS_URL + "heroSkill.atlas";
/**酒馆抽卡 */
UrlConfig.LOTTERY_RES = UrlConfig.BASE_ATLAS_URL + "lottery.atlas";
/**将军府抽卡阵营选择 */
UrlConfig.LOTTERY_NATION_SELECT_RES = UrlConfig.BASE_ATLAS_URL + "lottery_nation_select.atlas";
/**抽卡获得 */
UrlConfig.LOTTERY_GAIN_RES = UrlConfig.BASE_ATLAS_URL + "lottery_gain.atlas";
/**将军府抽卡 */
UrlConfig.LOTTERY_BETTER_RES = UrlConfig.BASE_ATLAS_URL + "lotteryBetter.atlas";
/**征战界面UI */
UrlConfig.GUA_JI_RES = UrlConfig.BASE_ATLAS_URL + "guaJi.atlas";
UrlConfig.GUA_JI_MINI_MAP_URL = UrlConfig.BASE_RES_URL + "ui/guaJi/scene13.jpg";
UrlConfig.GUA_JI_BUILD_RES = UrlConfig.BASE_ATLAS_URL + "guaJiBuild.atlas";
//萌将卡装扮资源路径
UrlConfig.MASTER_CARD_BG_RES_UI_URL = "res/ui/mastercardBg/";
UrlConfig.MASTER_CARD_DECORATION = "res/ui/mastercardecoration/";
/* 名将卡资源图集 */
UrlConfig.MASTER_CARD_RES = UrlConfig.BASE_ATLAS_URL + "mastercart.atlas";
UrlConfig.FIGHT_SUCC_RES = UrlConfig.BASE_ATLAS_URL + "fightsucc.atlas";
UrlConfig.FIGHT_FAIL_RES = UrlConfig.BASE_ATLAS_URL + "fightfail.atlas";
/**征战界面UI */
UrlConfig.FIGHT_RES = UrlConfig.BASE_ATLAS_URL + "fight.atlas";
UrlConfig.FIGHT2_RES = UrlConfig.BASE_ATLAS_URL + "fight2.atlas";
UrlConfig.FIGHTUI_RES = UrlConfig.BASE_ATLAS_URL + "fightUI.atlas";
/**推荐阵容 */
UrlConfig.RECOMMEND_LINE_UP_RES = UrlConfig.BASE_ATLAS_URL + "recommend_line_up.atlas";
/**排行榜 */
UrlConfig.RANK_RES = UrlConfig.BASE_ATLAS_URL + "rank.atlas";
/**英雄*/
UrlConfig.HERO_RES = UrlConfig.BASE_ATLAS_URL + "hero.atlas";
/**英雄继承 */
UrlConfig.HERO_INHERIT_RES = UrlConfig.BASE_ATLAS_URL + "heroInherit.atlas";
/**英雄属性TIPS*/
UrlConfig.HERO_ATTR_TIPS_RES = UrlConfig.BASE_ATLAS_URL + "heroAttrTips.atlas";
/**英雄图鉴*/
UrlConfig.HERO_TU_JIAN_RES = UrlConfig.BASE_ATLAS_URL + "heroTujian.atlas";
/**英雄图鉴*/
UrlConfig.HERO_TU_JIAN_HERO_CARD_RES = UrlConfig.BASE_RES_UI_URL + "heroTujian/hero/";
/**英雄总览*/
UrlConfig.HERO_INFO_RES = UrlConfig.BASE_ATLAS_URL + "heroInfo.atlas";
/**工坊*/
UrlConfig.EQUIP_WORK_RES = UrlConfig.BASE_ATLAS_URL + "equipwork.atlas";
/**英灵堡*/
UrlConfig.DUDUFU_RES = UrlConfig.BASE_ATLAS_URL + "dudufu.atlas";
/**守护灵*/
UrlConfig.DEPUTY_RES = UrlConfig.BASE_ATLAS_URL + "deputy.atlas";
/**英雄进阶升星*/
UrlConfig.HERO_UPDATE_RES = UrlConfig.BASE_ATLAS_URL + "heroUpdate.atlas";
/**英雄天赋预览*/
UrlConfig.HERO_TALENT_RES = UrlConfig.BASE_ATLAS_URL + "heroTalent.atlas";
/**英雄评论*/
UrlConfig.HERO_REMARK_RES = UrlConfig.BASE_ATLAS_URL + "heroRemark.atlas";
/**英雄布阵*/
UrlConfig.LINE_UP_RES = UrlConfig.BASE_ATLAS_URL + "lineup.atlas";
/**阵营buff */
UrlConfig.LINE_UP_BUFF_RES = UrlConfig.BASE_ATLAS_URL + "line_up_buff.atlas";
/**英雄展示 */
UrlConfig.HERO_SHOW_RES = UrlConfig.BASE_ATLAS_URL + "hero_show.atlas";
/**竞技场 */
UrlConfig.ARENA_RES = UrlConfig.BASE_ATLAS_URL + "arena.atlas";
/**竞技场选择界面 */
UrlConfig.ARENA_TYPE_RES = UrlConfig.BASE_ATLAS_URL + "arenaType.atlas";
/**充值礼包商店 */
UrlConfig.PAYMENT_RES = UrlConfig.BASE_ATLAS_URL + "payment.atlas";
/**超值礼包 */
UrlConfig.PROGRESS_GIFT_RES = UrlConfig.BASE_ATLAS_URL + "progressGift.atlas";
/**特惠直购 */
UrlConfig.PAYMENT_TIP_RES = UrlConfig.BASE_ATLAS_URL + "paymentTip.atlas";
/**vip商店限购跳转 */
UrlConfig.PAYMENT_LINK_RES = UrlConfig.BASE_ATLAS_URL + "paymentLink.atlas";
/**新服限购 */
UrlConfig.NWE_SERVER_RES = UrlConfig.BASE_ATLAS_URL + "newServer.atlas";
/**三系密令/征战日志 */
UrlConfig.SECRET_ORDER_RES = UrlConfig.BASE_ATLAS_URL + "secretOrder.atlas";
/**超值基金 */
UrlConfig.FUNDS_RES = UrlConfig.BASE_ATLAS_URL + "specialFunds.atlas";
/**南蛮入侵 */
UrlConfig.RANDOM_BOSS_RES = UrlConfig.BASE_ATLAS_URL + "randomBoss.atlas";
/**坐骑 */
UrlConfig.RIDE_RES = [UrlConfig.BASE_ATLAS_URL + "ride.atlas"];
/**擂台 */
UrlConfig.MATCH_RES = UrlConfig.BASE_ATLAS_URL + "match.atlas";
/**战斗回放 */
UrlConfig.FIGHT_PLAY_BACK_RES = UrlConfig.BASE_ATLAS_URL + "fightPlayBack.atlas";
UrlConfig.QQ_RES = UrlConfig.BASE_ATLAS_URL + "qq.atlas";
/**指引 */
// static GUIDE_RES: string = UrlConfig.BASE_ATLAS_URL + "guide.atlas";
/**指引 */
UrlConfig.PLOT_RES = UrlConfig.BASE_ATLAS_URL + "plot.atlas";
UrlConfig.GUIDE_GIRL = UrlConfig.BASE_RES_URL + "ui/guide/g_girl.png";
/**充值福利 */
UrlConfig.WELFARE_RES = UrlConfig.BASE_ATLAS_URL + "welfare.atlas";
/**等级礼包 */
UrlConfig.LEVEL_GIFT_RES = UrlConfig.BASE_ATLAS_URL + "levelGift.atlas";
/**条件充值礼包 */
UrlConfig.MISSION_SHOP_RES = UrlConfig.BASE_ATLAS_URL + "missionShop.atlas";
/**VIP客服*/
UrlConfig.VIP_KEFU = UrlConfig.BASE_ATLAS_URL + "vipKefu.atlas";
/**活动 */
UrlConfig.ACTIVITY = UrlConfig.BASE_ATLAS_URL + "activity.atlas";
/**活动 */
UrlConfig.LOGINACTIVITY = UrlConfig.BASE_ATLAS_URL + "loginActivity.atlas";
/**限时活动 */
UrlConfig.TIME_ACTIVITY = UrlConfig.BASE_ATLAS_URL + "timeActivity.atlas";
UrlConfig.DAY_SHOP = UrlConfig.BASE_ATLAS_URL + "activitydayshop.atlas";
/**主题玩法 */
UrlConfig.MAZE = UrlConfig.BASE_ATLAS_URL + "maze.atlas";
/**主题玩法模块化通行证 */
UrlConfig.MAZE_PASS = UrlConfig.BASE_ATLAS_URL + "actMods/maze_pass.atlas";
/**神器 */
UrlConfig.WEAPON_RES = UrlConfig.BASE_ATLAS_URL + "weapon.atlas";
/**武神庙 */
UrlConfig.HERO_TEMPLE_RES = UrlConfig.BASE_ATLAS_URL + "heroTemple.atlas";
/**英雄收集 */
UrlConfig.HERO_COLLECT_RES = UrlConfig.BASE_ATLAS_URL + "heroCollect.atlas";
/**战斗统计 */
UrlConfig.FIGHT_STAT_RES = UrlConfig.BASE_ATLAS_URL + "fightStat.atlas";
UrlConfig.GUA_JI_RES_URL = UrlConfig.BASE_RES_URL + "map/guaji/";
/**国战托管 */
UrlConfig.WAR_AUTO_RES = UrlConfig.BASE_ATLAS_URL + "warAuto.atlas";
UrlConfig.FAMILY_REDPACK_RES = UrlConfig.BASE_ATLAS_URL + "familyRedPack.atlas";
/** 我要变强 */
UrlConfig.STRONG_RES = UrlConfig.BASE_ATLAS_URL + "strong.atlas";
/**功能预告 */
UrlConfig.FUNCTION_PRE_RES = UrlConfig.BASE_ATLAS_URL + "functionPre.atlas";
/**属性图标 */
UrlConfig.FIGHT_ATTR_RES = UrlConfig.BASE_ATLAS_URL + "fightAttr.atlas";
UrlConfig.FIGHT_BUFF_RES = UrlConfig.BASE_ATLAS_URL + "fightBuff.atlas";
/**战魂 */
UrlConfig.HERO_SOUL_RES = UrlConfig.BASE_ATLAS_URL + "hero_soul.atlas";
/**冠军赛 */
UrlConfig.RANDOM_PVP_RES = UrlConfig.BASE_ATLAS_URL + "random_pvp.atlas";
/**英雄售卖限时广告 */
UrlConfig.HERO_ACT_AD_RES = UrlConfig.BASE_ATLAS_URL + "hero_act_ad.atlas";
/**英雄售卖 */
UrlConfig.HERO_ACT_RES = UrlConfig.BASE_ATLAS_URL + "hero_act.atlas";
UrlConfig.LIE_ZHUAN_RES = UrlConfig.BASE_ATLAS_URL + "liezhuan.atlas";
UrlConfig.LINEUP_VS = UrlConfig.BASE_ATLAS_URL + "lineupVs.atlas";
UrlConfig.FAMILY_TRIAL_RES = UrlConfig.BASE_ATLAS_URL + "familyTrial.atlas";
/** 三军备战 */
UrlConfig.PREPARE_RES = UrlConfig.BASE_ATLAS_URL + "prepare.atlas";
UrlConfig.MASTER_COMMON_RES = UrlConfig.BASE_ATLAS_URL + "master_common.atlas";
UrlConfig.MASTER_RES = [UrlConfig.BASE_ATLAS_URL + "master.atlas", UrlConfig.MASTER_COMMON_RES];
UrlConfig.HERO_SKIN_RES = UrlConfig.BASE_ATLAS_URL + "heroskin.atlas";
UrlConfig.HERO_HALF_SKIN_RES = UrlConfig.BASE_ATLAS_URL + "heroskin" + "/half.atlas";
/** qq蓝钻 */
UrlConfig.HERO_UP_ACT_RES = UrlConfig.BASE_ATLAS_URL + "heroUpAct.atlas";
UrlConfig.QQ_BLUE_DIAMOND_RES = UrlConfig.BASE_ATLAS_URL + "qqBlueDiamond.atlas";
/** qq大厅 */
UrlConfig.QQ_GAME_RES = UrlConfig.BASE_ATLAS_URL + "qqGame.atlas";
/**sdk分享(邀请) */
UrlConfig.SHARE_RES = UrlConfig.BASE_ATLAS_URL + "wxShare.atlas";
UrlConfig.WX_TURN_RES = UrlConfig.BASE_ATLAS_URL + "wxTurn.atlas";
UrlConfig.SIX_TURN_RES = UrlConfig.BASE_ATLAS_URL + "sixturn.atlas";
/**战斗编辑器 */
UrlConfig.BATTLE_EDITOR_RES = UrlConfig.BASE_ATLAS_URL + "battleEditor.atlas";
UrlConfig.CROSSREAL_RES = UrlConfig.BASE_ATLAS_URL + "crossrealmwar.atlas";
UrlConfig.HERO_ROLL_CALL_RES = UrlConfig.BASE_ATLAS_URL + "heroRollCall.atlas";
/** 兽灵系统 */
UrlConfig.HERO_TU_TENG = UrlConfig.BASE_ATLAS_URL + "heroTuTeng.atlas";
/**勇闯异境 */
UrlConfig.PASS_BEHEAD = UrlConfig.BASE_ATLAS_URL + "passBehead.atlas";
/** 赤壁之战 */
UrlConfig.RED_CLIFF_RES = UrlConfig.BASE_ATLAS_URL + "redCliff.atlas";
/** 将军府 */
UrlConfig.GENERAL_MANSION_RES = UrlConfig.BASE_ATLAS_URL + "generalMansion.atlas";
/**试炼塔 */
UrlConfig.TEST_TOWER_RES = UrlConfig.BASE_ATLAS_URL + "testTower.atlas";
/**种族塔 */
UrlConfig.TEST_TOWER_RES2 = UrlConfig.BASE_ATLAS_URL + "testtower2.atlas";
/**神器 */
UrlConfig.GOD_WEAPON_RES = UrlConfig.BASE_ATLAS_URL + "godWeapon.atlas";
/**英雄图鉴 */
UrlConfig.HERO_TUJIAN_RES = UrlConfig.BASE_ATLAS_URL + "heroTuJian.atlas";
/**神位争夺 */
UrlConfig.ARES_PALACE_RES = UrlConfig.BASE_ATLAS_URL + "aresPalace.atlas";
/**勇闯异境 */
UrlConfig.GOD_TRIAL = UrlConfig.BASE_ATLAS_URL + "godTrial.atlas";
/**暗黑地牢（官渡之战） */
UrlConfig.GUAN_DU_RES = UrlConfig.BASE_ATLAS_URL + "guanDu.atlas";
/**公会活跃 */
UrlConfig.FAMILY_ACTIVE_RES = UrlConfig.BASE_ATLAS_URL + "familyActive.atlas";
/**冠军赛 */
UrlConfig.QXZL_RES = UrlConfig.BASE_ATLAS_URL + "qxzl.atlas";
/**神装纷争 */
UrlConfig.CHALLENGE_CHAPTER_RES = UrlConfig.BASE_ATLAS_URL + "challengeChapter.atlas";
/**神装秘宝 */
UrlConfig.GOD_EQUIP_TREASURE_RES = UrlConfig.BASE_ATLAS_URL + "godEquipTreasure.atlas";
/**千抽活动 */
UrlConfig.OPTION_LOTTERY_RES = UrlConfig.BASE_ATLAS_URL + "optionLottery.atlas";
/**开场剧情 */
UrlConfig.STORY_RES = UrlConfig.BASE_ATLAS_URL + "story.atlas";
/* 剧情总览资源 */
UrlConfig.ACT_MODS_STORY_DRAMA_RES = UrlConfig.BASE_ATLAS_URL + "actMods/drama.atlas";
/**跨服争霸入口 */
UrlConfig.CROSS_ENTER_RES = UrlConfig.BASE_ATLAS_URL + "crossEnter.atlas";
/**三系通行证 */
UrlConfig.PASSPORT_RES = UrlConfig.BASE_ATLAS_URL + "passports.atlas";
/**名将招募-限时皮肤 */
UrlConfig.THEME_ACT_SKIN_LOTTERY_RES = UrlConfig.BASE_ATLAS_URL + "themeActSkinLottery.atlas";
/**名将招募-名将招募 */
UrlConfig.THEME_ACT_HERO_LOTTERY_RES = UrlConfig.BASE_ATLAS_URL + "themeActHeroLottery.atlas";
/**汉中争夺战 */
UrlConfig.HZZD_RES = UrlConfig.BASE_ATLAS_URL + "hzzd.atlas";
/**冠军赛 */
UrlConfig.PEAK_RES = UrlConfig.BASE_ATLAS_URL + "peak.atlas";
/**跨服天梯赛 */
UrlConfig.CROSS_LADDER_RES = UrlConfig.BASE_ATLAS_URL + "crossLadder.atlas";
/**仙山外海 */
UrlConfig.XSWH_RES = UrlConfig.BASE_ATLAS_URL + "xswh.atlas";
/**转端活动 */
UrlConfig.MINI_DUAN_RES = UrlConfig.BASE_ATLAS_URL + "actMods/miniDuan.atlas";
UrlConfig.MINI_DUAN_RES2 = UrlConfig.BASE_ATLAS_URL + "actMods/miniDuan/skin1.atlas";
/**龙魂 */
UrlConfig.QI_MOU_RES = UrlConfig.BASE_ATLAS_URL + "qiMou.atlas";
/**剧情活动 */
UrlConfig.ACT_MODS_STORY_RES = UrlConfig.BASE_ATLAS_URL + "actMods/story.atlas";
/**活动预览 */
UrlConfig.ACT_MODS_OPEN_PREVIEW_RES = UrlConfig.BASE_ATLAS_URL + "actMods/open_preview.atlas";
/**勋章 */
UrlConfig.XUN_ZHANG_RES = UrlConfig.BASE_ATLAS_URL + "xunzhang.atlas";
/* 号码绑定图集路径 */
UrlConfig.PHONE_BIND_RES = UrlConfig.BASE_ATLAS_URL + "phonebind.atlas";
/* 组队征战 */
UrlConfig.BATTLE_TRIAL_RES = UrlConfig.BASE_ATLAS_URL + "battleTrial.atlas";
/**战场主宰 */
UrlConfig.LARGE_PEAK_RES = UrlConfig.BASE_ATLAS_URL + "largePeak.atlas";
/**战场主宰预告 */
UrlConfig.LARGE_PEAK_NOTICE_RES = UrlConfig.BASE_ATLAS_URL + "largePeak/notice.atlas";
/**诸神战场 */
UrlConfig.DOMINATE_PVP_RES = UrlConfig.BASE_ATLAS_URL + "dominate_pvp.atlas";
/**跨服组队 */
UrlConfig.CROSS_TEAM_RES = UrlConfig.BASE_ATLAS_URL + "crossTeam.atlas";
UrlConfig.MONTHCARD_ACTIVE_RES = UrlConfig.BASE_ATLAS_URL + "monthcardactive.atlas";
/**跨服组队领主战 */
UrlConfig.TEAM_XSWH_RES = UrlConfig.BASE_ATLAS_URL + "team_xswh.atlas";
/**家族 */
UrlConfig.CSCLAN = UrlConfig.BASE_ATLAS_URL + "csclan.atlas";
/**任务墙*/
UrlConfig.TASK_WALL_RES = UrlConfig.BASE_ATLAS_URL + "actMods/mission.atlas";
/* 名将卡升官资源图集 */
UrlConfig.MASTER_CART_RES_2 = UrlConfig.BASE_ATLAS_URL + "mastercart/mastercart_2.atlas";
/**名将卡首充 */
UrlConfig.MASTER_CARD_FIRST_RECHARGE_RES = UrlConfig.BASE_ATLAS_URL + "mastercardfirstRecharge.atlas";
/**国战 */
UrlConfig.COUNTRY_WAR_RES = UrlConfig.BASE_ATLAS_URL + "countryWar.atlas";
UrlConfig.COUNTRY_WAR_UI_URL = `${UrlConfig.BASE_RES_UI_URL}countryWar/`;
/**塔防 */
UrlConfig.TD_MAIN_RES = UrlConfig.BASE_ATLAS_URL + "tdMain.atlas";
UrlConfig.TD_TRIAL_RES = UrlConfig.BASE_ATLAS_URL + "tdTrial.atlas";
UrlConfig.RED_CLIFF_UI_URL = "res/ui/redCliff/";
UrlConfig.isInit = false;
/**地图UI */
UrlConfig.WORLD_MAP_RES = UrlConfig.BASE_ATLAS_URL + "worldMap.atlas";
/**攻城掠地 */
UrlConfig.SIEGELORD_RES = UrlConfig.BASE_ATLAS_URL + "siegelord.atlas";
/**运河夺宝*/
UrlConfig.RIVER_TREASURE_RES = UrlConfig.BASE_ATLAS_URL + "riverTreasure.atlas";
UrlConfig._platform = -1;
