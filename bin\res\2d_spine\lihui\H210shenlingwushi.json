{"skeleton": {"hash": "pG/RXvek1j+iMihAl2fQi1LgXfI=", "spine": "3.8.75", "x": -560, "y": -68.46, "width": 931, "height": 992.46, "images": "./images/", "audio": "G:/杨启帆/W6/动画/H210神灵武士/images"}, "bones": [{"name": "root"}, {"name": "ya<PERSON>i", "parent": "root", "x": 15.74, "y": 340.79}, {"name": "leg2", "parent": "ya<PERSON>i", "length": 160.15, "rotation": -41.22, "x": 21.35, "y": -53.47}, {"name": "leg3", "parent": "leg2", "length": 176.42, "rotation": -79.68, "x": 160.15}, {"name": "bone", "parent": "root", "y": 20}, {"name": "foot2", "parent": "bone", "length": 161.33, "rotation": -13.76, "x": 66.93, "y": 11.48, "color": "ff3f00ff"}, {"name": "foot1banner2", "parent": "leg3", "length": 16.76, "rotation": -120.21, "x": 93.44, "y": -58.45}, {"name": "foot1banner3", "parent": "leg3", "length": 19.05, "rotation": -159.81, "x": 87.22, "y": -36.23}, {"name": "foot1banner4", "parent": "leg3", "length": 22.47, "rotation": 175.07, "x": 77.44, "y": -9.05}, {"name": "foot1banner5", "parent": "leg3", "length": 21.85, "rotation": 163.09, "x": 67.74, "y": 11.09}, {"name": "leg1", "parent": "ya<PERSON>i", "length": 210.6, "rotation": -106.04, "x": -82.33, "y": 5.38}, {"name": "leg4", "parent": "leg1", "length": 126.29, "rotation": -24.74, "x": 210.6}, {"name": "foot1", "parent": "bone", "length": 66.63, "rotation": -99.18, "x": -207.77, "y": 28.64, "color": "ff3f00ff"}, {"name": "body", "parent": "ya<PERSON>i", "length": 138.75, "rotation": 98.47, "x": -39.41, "y": 105.7}, {"name": "arm1", "parent": "body", "rotation": -98.47, "x": 177.33, "y": 80.61, "color": "abe323ff"}, {"name": "arm2", "parent": "body", "rotation": -98.47, "x": 124.05, "y": -94.54}, {"name": "arm4", "parent": "arm1", "length": 182.99, "rotation": -141.3, "x": -38.38, "y": -11.82, "transform": "noScale"}, {"name": "hand1", "parent": "arm4", "length": 183.75, "rotation": 49.08, "x": 182.44, "y": -0.69}, {"name": "hand11", "parent": "hand1", "length": 78.95, "rotation": 29.64, "x": 191.69, "y": 1.19}, {"name": "bandbanner1", "parent": "hand1", "length": 48.65, "rotation": -169.86, "x": 75.35, "y": -27.15}, {"name": "bandbanner2", "parent": "hand1", "length": 45.03, "rotation": 173.64, "x": 78.65, "y": 13.88}, {"name": "bandbanner3", "parent": "hand1", "length": 41.98, "rotation": 127.75, "x": 95.92, "y": 40.79}, {"name": "bandbanner4", "parent": "hand1", "length": 39.8, "rotation": 107.31, "x": 112.94, "y": 58.54}, {"name": "bandbanner6", "parent": "hand1", "length": 42.77, "rotation": -1.06, "x": 176.08, "y": -42.54}, {"name": "bandbanner7", "parent": "hand1", "length": 39.72, "rotation": 12.83, "x": 175.98, "y": -8.36}, {"name": "bandbanner8", "parent": "hand1", "length": 45.46, "rotation": 42.31, "x": 174.48, "y": 30.65}, {"name": "arm3", "parent": "arm2", "length": 207.74, "rotation": -47.18, "x": 21.39, "y": -24.7, "transform": "noScale"}, {"name": "arm6", "parent": "arm3", "length": 125.05, "rotation": 2.41, "x": 203.79, "y": -0.15}, {"name": "hand22", "parent": "arm6", "length": 96.6, "rotation": -26.66, "x": 117.14, "y": -0.96}, {"name": "banner1", "parent": "ya<PERSON>i", "length": 29.41, "rotation": -139.9, "x": -132.52, "y": 1.36}, {"name": "banner2", "parent": "banner1", "length": 45.78, "rotation": 0.18, "x": 29.79, "y": 0.45}, {"name": "banner3", "parent": "ya<PERSON>i", "length": 36.96, "rotation": -144.78, "x": -91.08, "y": -8.7}, {"name": "banner4", "parent": "banner3", "length": 56.36, "rotation": 0.81, "x": 36.96}, {"name": "banner5", "parent": "ya<PERSON>i", "length": 23.81, "rotation": -145.12, "x": -57.33, "y": -9.89}, {"name": "banner6", "parent": "banner5", "length": 40.1, "rotation": 24.02, "x": 23.81}, {"name": "banner7", "parent": "ya<PERSON>i", "length": 41.1, "rotation": -138.5, "x": -14.12, "y": -14.62}, {"name": "banner8", "parent": "banner7", "length": 59.5, "rotation": 17.34, "x": 41.1}, {"name": "banner9", "parent": "ya<PERSON>i", "length": 36.69, "rotation": -100.22, "x": 14.89, "y": -5.74}, {"name": "banner10", "parent": "banner9", "length": 54.99, "rotation": -4.11, "x": 36.69}, {"name": "banner11", "parent": "ya<PERSON>i", "length": 31.52, "rotation": -61.99, "x": 36.8, "y": -6.34}, {"name": "banner12", "parent": "banner11", "length": 52.45, "rotation": 8.73, "x": 31.52}, {"name": "banner13", "parent": "ya<PERSON>i", "length": 31.08, "rotation": -49.64, "x": 52.78, "y": 0.77}, {"name": "banner14", "parent": "banner13", "length": 49.26, "rotation": 16.9, "x": 31.08}, {"name": "head", "parent": "body", "length": 38.81, "rotation": -47.71, "x": 150.15, "y": -39.04, "transform": "noScale"}, {"name": "mouth", "parent": "head", "length": 63.09, "rotation": -93.83, "x": -1.89, "y": -22.24}, {"name": "eye", "parent": "head", "length": 8.26, "rotation": 25.2, "x": 111.5, "y": -70}, {"name": "ear", "parent": "head", "length": 34.99, "rotation": 68.37, "x": 51.97, "y": 38.66}, {"name": "ear2", "parent": "ear", "length": 52.55, "rotation": 31.82, "x": 34.99}, {"name": "ear3", "parent": "ear2", "length": 77.17, "rotation": -16.82, "x": 53.21, "y": 0.75}, {"name": "hair1", "parent": "head", "length": 37.1, "rotation": 73.79, "x": 124.44, "y": 37.04}, {"name": "hair2", "parent": "hair1", "length": 40.41, "rotation": 8.44, "x": 37.1}, {"name": "hair3", "parent": "hair2", "length": 59.15, "rotation": 8.2, "x": 40.75, "y": 0.37}, {"name": "hair4", "parent": "head", "length": 24.88, "rotation": 64.25, "x": 136.04, "y": 35.52}, {"name": "hair5", "parent": "hair4", "length": 26.4, "rotation": 8.37, "x": 24.88}, {"name": "hair6", "parent": "hair5", "length": 38.29, "rotation": 13.73, "x": 26.82, "y": -0.28}, {"name": "hair7", "parent": "head", "length": 55.43, "rotation": 49.13, "x": 158.39, "y": -0.57}, {"name": "hair8", "parent": "hair7", "length": 61.25, "rotation": 13.74, "x": 55.43}, {"name": "hair9", "parent": "hair8", "length": 77.59, "rotation": 22.16, "x": 61.91, "y": 0.26}, {"name": "hair10", "parent": "head", "length": 27.95, "rotation": 24.7, "x": 159.74, "y": -33.1}, {"name": "hair11", "parent": "hair10", "length": 29.66, "rotation": -1.15, "x": 27.95}, {"name": "hair12", "parent": "hair11", "length": 40.89, "rotation": -20.35, "x": 29.66}, {"name": "hair13", "parent": "head", "length": 12.75, "rotation": -5.76, "x": 149.39, "y": -47.37}, {"name": "hair14", "parent": "hair13", "length": 17.96, "rotation": -14.86, "x": 12.75}, {"name": "hair15", "parent": "hair14", "length": 19.04, "rotation": -28.63, "x": 17.96}, {"name": "dai1", "parent": "body", "length": 84.52, "rotation": -154.39, "x": 166.38, "y": 91.18, "transform": "noScale"}, {"name": "mao2", "parent": "dai1", "length": 146.28, "rotation": -179.11, "x": 20.54, "y": 1.13}, {"name": "mao1", "parent": "dai1", "length": 129.23, "rotation": -170.13, "x": 14.95, "y": -11.85}, {"name": "mao3", "parent": "dai1", "length": 134.31, "rotation": 170.57, "x": 15.27, "y": 11.09}, {"name": "foot1banner", "parent": "leg4", "length": 30.55, "rotation": -147.57, "x": 76.63, "y": -27.21}, {"name": "foot1banner6", "parent": "leg4", "length": 37.67, "rotation": -166.61, "x": 71.43, "y": 2.89}, {"name": "foot1banner7", "parent": "leg4", "length": 33.05, "rotation": -176.79, "x": 59.69, "y": 25.76}, {"name": "hand2banner1", "parent": "arm6", "length": 36.49, "rotation": -119.75, "x": 26.5, "y": -42.54}, {"name": "hand2banner2", "parent": "arm6", "length": 32.7, "rotation": -151.56, "x": 10.14, "y": -19.9}, {"name": "hand2banner3", "parent": "arm6", "length": 34.7, "rotation": 165.74, "x": 2.61, "y": 10.73}, {"name": "hand2banner4", "parent": "arm6", "length": 38.58, "rotation": 149.39, "x": 9.24, "y": 43.22}, {"name": "hand2banner5", "parent": "arm6", "length": 33.82, "rotation": -52.58, "x": 79.72, "y": -29.36}, {"name": "hand2banner6", "parent": "arm6", "length": 38.24, "rotation": -20.11, "x": 76.39, "y": -1.42}, {"name": "hand2banner7", "parent": "arm6", "length": 44.13, "rotation": 4.25, "x": 80.3, "y": 20.75}, {"name": "hand2banner8", "parent": "arm6", "length": 40.38, "rotation": 9.59, "x": 87.64, "y": 38.7}], "slots": [{"name": "dai2", "bone": "dai1", "attachment": "dai2"}, {"name": "mao1", "bone": "mao1", "attachment": "mao1"}, {"name": "mao3", "bone": "mao3", "attachment": "mao3"}, {"name": "mao2", "bone": "mao2", "attachment": "mao2"}, {"name": "dai1", "bone": "dai1", "attachment": "dai1"}, {"name": "hand22", "bone": "hand22", "attachment": "hand22"}, {"name": "arm2", "bone": "arm6", "attachment": "arm2"}, {"name": "hand2banner1", "bone": "hand2banner1", "attachment": "hand2banner1"}, {"name": "hand2banner2", "bone": "hand2banner5", "attachment": "hand2banner2"}, {"name": "hand2", "bone": "arm6", "attachment": "hand2"}, {"name": "yaodai2", "bone": "ya<PERSON>i", "attachment": "yaodai2"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "leg2", "bone": "leg2", "attachment": "leg2"}, {"name": "foot1banner2", "bone": "foot1banner2", "attachment": "foot1banner2"}, {"name": "foot2", "bone": "foot2", "attachment": "foot2"}, {"name": "leg1", "bone": "leg1", "attachment": "leg1"}, {"name": "foot1banner", "bone": "foot1banner", "attachment": "foot1banner"}, {"name": "foot1", "bone": "foot1", "attachment": "foot1"}, {"name": "banner1", "bone": "banner1", "attachment": "banner1"}, {"name": "banner7", "bone": "banner13", "attachment": "banner7"}, {"name": "banner4", "bone": "banner7", "attachment": "banner4"}, {"name": "banner6", "bone": "banner11", "attachment": "banner6"}, {"name": "banner5", "bone": "banner9", "attachment": "banner5"}, {"name": "banner2", "bone": "banner3", "attachment": "banner2"}, {"name": "banner3", "bone": "banner5", "attachment": "banner3"}, {"name": "ya<PERSON>i", "bone": "ya<PERSON>i", "attachment": "ya<PERSON>i"}, {"name": "hand11", "bone": "hand11", "attachment": "hand11"}, {"name": "arm1", "bone": "arm4", "attachment": "arm1"}, {"name": "bandbanner2", "bone": "bandbanner6", "attachment": "bandbanner2"}, {"name": "bandbanner1", "bone": "bandbanner1", "attachment": "bandbanner1"}, {"name": "hand1", "bone": "hand1", "attachment": "hand1"}, {"name": "tooth", "bone": "mouth", "attachment": "tooth"}, {"name": "hair1", "bone": "hair1", "attachment": "hair1"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "ear", "bone": "ear", "attachment": "ear"}, {"name": "eye", "bone": "eye", "attachment": "eye"}, {"name": "hair3", "bone": "hair7", "attachment": "hair3"}, {"name": "hair2", "bone": "hair4", "attachment": "hair2"}, {"name": "hair4", "bone": "hair10", "attachment": "hair4"}, {"name": "hair5", "bone": "hair13", "attachment": "hair5"}, {"name": "mouth", "bone": "mouth", "attachment": "mouth"}], "ik": [{"name": "foot1", "bones": ["leg1", "leg4"], "target": "foot1", "bendPositive": false}, {"name": "foot2", "order": 1, "bones": ["leg2", "leg3"], "target": "foot2", "bendPositive": false}], "transform": [{"name": "arm1", "order": 2, "bones": ["arm2"], "target": "arm1", "x": 181.08, "y": -26.9, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"hand2banner1": {"hand2banner1": {"type": "mesh", "uvs": [0, 0.99958, 0.12003, 0.97019, 0.30101, 0.92612, 0.482, 0.88204, 0.61606, 0.79683, 0.81868, 0.65437, 0.92593, 0.55447, 1, 0.4575, 1, 0.3194, 0.95275, 0.23712, 0.90582, 0.11959, 0.86225, 0.03438, 0.77511, 0.00499, 0.63435, 0, 0.66116, 0.04907, 0.66301, 0.09608, 0.59374, 0.07454, 0.45744, 0.05495, 0.5133, 0.15093, 0.52001, 0.20774, 0.45074, 0.20578, 0.35466, 0.18619, 0.34349, 0.25084, 0.30327, 0.32919, 0.27646, 0.39971, 0.26976, 0.4526, 0.20719, 0.48395, 0.09995, 0.54271, 0.12005, 0.6054, 0.15357, 0.65241, 0.09101, 0.67592, 0.01727, 0.73077, 0.05973, 0.77778, 0.10888, 0.80521, 0.0642, 0.86397, 0.02398, 0.92666, 0.30134, 0.68615, 0.46095, 0.72968, 0.41129, 0.49024, 0.62056, 0.56487, 0.62765, 0.29745, 0.77662, 0.41561], "triangles": [0, 35, 1, 35, 34, 1, 2, 1, 33, 1, 34, 33, 2, 37, 3, 2, 36, 37, 2, 33, 36, 3, 37, 4, 32, 30, 33, 33, 29, 36, 33, 30, 29, 4, 39, 5, 4, 37, 39, 32, 31, 30, 36, 38, 37, 37, 38, 39, 29, 26, 36, 26, 25, 36, 36, 25, 38, 6, 5, 41, 29, 28, 26, 28, 27, 26, 39, 38, 40, 40, 38, 20, 5, 39, 41, 39, 40, 41, 40, 20, 19, 6, 41, 7, 25, 24, 38, 24, 23, 38, 38, 23, 20, 20, 23, 22, 41, 8, 7, 41, 40, 9, 41, 9, 8, 10, 40, 15, 10, 15, 11, 9, 40, 10, 15, 12, 11, 40, 19, 15, 16, 15, 18, 22, 21, 20, 15, 19, 18, 18, 17, 16, 15, 14, 12, 14, 13, 12], "vertices": [1, 71, 57.49, 11.99, 1, 1, 71, 41.53, 11.96, 1, 1, 71, 17.49, 11.94, 1, 4, 71, -6.55, 11.92, 0.96313, 72, -15.46, 34.37, 0.0337, 73, -49.51, 42.61, 0.00282, 74, -70.34, 58.3, 0.00036, 4, 71, -26.41, 4.51, 0.49486, 72, -28.43, 17.6, 0.38078, 73, -47.67, 21.49, 0.09743, 74, -62.63, 38.55, 0.02693, 4, 71, -56.96, -8.61, 0.0528, 72, -47.46, -9.65, 0.24378, 73, -43.18, -11.45, 0.37379, 74, -49.05, 8.2, 0.32964, 4, 71, -74.08, -19.01, 0.00597, 72, -56.54, -27.51, 0.0764, 73, -37.74, -30.72, 0.24807, 74, -38.4, -8.76, 0.66957, 4, 71, -86.99, -30.12, 4e-05, 72, -61.65, -43.76, 0.0188, 73, -30.48, -46.14, 0.09541, 74, -27.1, -21.51, 0.88575, 3, 72, -55.98, -63.11, 0.00053, 73, -13.19, -56.51, 0.00485, 74, -7.59, -26.6, 0.99462, 1, 74, 5.56, -23.78, 1, 1, 74, 23.68, -22.3, 1, 1, 74, 37.13, -20.04, 1, 1, 74, 44.1, -10.33, 1, 1, 74, 49.35, 6.92, 1, 2, 73, 42.97, -39.63, 0.00368, 74, 41.55, 5.41, 0.99632, 2, 73, 36.96, -36.3, 0.05694, 74, 34.85, 6.91, 0.94306, 2, 73, 44.22, -30.31, 0.19375, 74, 40.13, 14.7, 0.80625, 2, 73, 55.65, -16.82, 0.2395, 74, 47.3, 30.86, 0.7605, 2, 73, 39.96, -15.75, 0.336, 74, 31.94, 27.47, 0.664, 2, 73, 32.4, -12.21, 0.65632, 74, 23.7, 28.74, 0.34368, 2, 73, 37.21, -4.76, 0.96762, 74, 26.21, 37.24, 0.03238, 1, 73, 45.99, 4.32, 1, 2, 72, 27.48, -49.08, 0.02824, 73, 38.63, 10.4, 0.97176, 2, 72, 29.2, -36.65, 0.19064, 73, 31.47, 20.7, 0.80936, 2, 72, 29.6, -25.81, 0.42931, 73, 24.41, 28.94, 0.57069, 2, 72, 28.25, -18.16, 0.69002, 73, 18.23, 33.65, 0.30998, 2, 72, 34.64, -11.51, 0.92663, 73, 18.42, 42.87, 0.07337, 2, 71, 27.35, -48.88, 0.00106, 72, 45.4, 0.58, 0.99894, 2, 71, 27.32, -39.37, 0.05927, 72, 40.36, 8.64, 0.94073, 2, 71, 25.01, -31.61, 0.30697, 72, 34.31, 14.02, 0.69303, 2, 71, 33.65, -30.44, 0.58618, 72, 41.03, 19.57, 0.41382, 2, 71, 44.88, -25.24, 0.69902, 72, 47.84, 29.91, 0.30098, 2, 71, 41.48, -17.18, 0.74241, 72, 40.69, 34.96, 0.25759, 2, 71, 36.48, -11.64, 0.86505, 72, 33.53, 37.04, 0.13495, 2, 71, 44.28, -4.9, 0.99395, 72, 36.6, 46.88, 0.00605, 1, 71, 51.69, 2.55, 1, 2, 71, 8.1, -21.82, 0.3379, 72, 14.78, 13.43, 0.6621, 4, 71, -9.89, -10.24, 0.50687, 72, -6.62, 13.78, 0.47941, 73, -29.05, 33.47, 0.01162, 74, -48.13, 55.29, 0.0021, 2, 72, 9.32, -17.98, 0.55484, 73, 4.19, 20.94, 0.44516, 4, 71, -36, -27.97, 0.03011, 72, -19.45, -15.06, 0.35128, 73, -18.93, 3.58, 0.52695, 74, -30.01, 29.45, 0.09166, 2, 73, 14.08, -17.29, 0.53311, 74, 7.55, 18.71, 0.46689, 4, 71, -61.07, -43.65, 0.00033, 72, -32.49, -41.59, 0.01824, 73, -10.52, -24.77, 0.27179, 74, -13.96, 4.62, 0.70965], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70, 58, 72, 72, 74, 74, 8, 50, 76, 76, 78, 78, 10, 38, 80, 80, 82, 82, 12], "width": 128, "height": 146}}, "hand2banner2": {"hand2banner2": {"type": "mesh", "uvs": [0.14866, 0.47238, 0.11489, 0.6083, 0.05965, 0.73984, 0.01054, 0.85384, 0, 0.98099, 0.08727, 0.87576, 0.164, 1, 0.25608, 0.93276, 0.32053, 0.83192, 0.35122, 0.76615, 0.47706, 0.85384, 0.57528, 0.71792, 0.63359, 0.58199, 0.75329, 0.57761, 0.91903, 0.53376, 0.86685, 0.43292, 0.94052, 0.44607, 1, 0.41976, 0.95586, 0.29261, 0.90062, 0.16545, 0.8423, 0.07776, 0.75329, 0.02076, 0.64894, 0.00322, 0.52003, 0.03392, 0.3611, 0.14722, 0.31439, 0.19615, 0.24073, 0.29699, 0.19469, 0.3803, 0.28063, 0.39785, 0.3144, 0.55569, 0.33895, 0.67408, 0.57528, 0.48554, 0.5139, 0.40223, 0.4433, 0.28385, 0.59063, 0.126, 0.66736, 0.20931, 0.7533, 0.30577], "triangles": [6, 5, 7, 4, 3, 5, 5, 1, 7, 8, 7, 30, 30, 7, 1, 1, 0, 29, 3, 2, 5, 5, 2, 1, 11, 10, 30, 0, 28, 29, 0, 27, 28, 8, 30, 9, 29, 30, 1, 10, 9, 30, 11, 31, 12, 11, 30, 31, 30, 32, 31, 30, 29, 32, 13, 12, 36, 13, 15, 14, 13, 36, 15, 36, 12, 31, 29, 28, 33, 29, 33, 32, 33, 28, 25, 31, 35, 36, 31, 32, 35, 17, 16, 18, 16, 15, 18, 18, 15, 19, 32, 34, 35, 32, 33, 34, 25, 24, 33, 27, 26, 28, 28, 26, 25, 36, 35, 20, 15, 36, 19, 36, 20, 19, 20, 35, 21, 33, 23, 34, 33, 24, 23, 34, 22, 35, 35, 22, 21, 34, 23, 22], "vertices": [2, 75, -3.13, -8.41, 0.98499, 76, 5.58, -30.5, 0.01501, 1, 75, 9.69, -11.18, 1, 1, 75, 22.48, -16.77, 1, 1, 75, 33.59, -21.78, 1, 1, 75, 45.24, -21.66, 1, 1, 75, 34.29, -11.63, 1, 1, 75, 44.23, -0.29, 1, 2, 75, 36.63, 10.8, 0.97742, 76, 49.44, -35.64, 0.02258, 2, 75, 26.45, 17.93, 0.80074, 76, 44.68, -24.16, 0.19926, 2, 75, 20.01, 21.13, 0.47861, 76, 40.96, -18.01, 0.52139, 2, 75, 25.83, 38.37, 0.02216, 76, 55.13, -6.58, 0.97784, 2, 76, 49.35, 10.23, 0.88948, 77, 43.63, -32.86, 0.11052, 2, 76, 41.36, 22.34, 0.40182, 77, 41.35, -18.53, 0.59818, 3, 76, 47.61, 36.6, 0.02183, 77, 52.92, -8.11, 0.96831, 78, 41.71, -29.48, 0.00987, 2, 77, 66.71, 8.92, 0.74088, 78, 57.02, -13.81, 0.25912, 2, 77, 55.59, 11.49, 0.42682, 78, 46.19, -10.22, 0.57318, 2, 77, 63.64, 16.8, 0.03819, 78, 54.7, -5.68, 0.96181, 1, 78, 59.65, 0.73, 1, 1, 78, 48.29, 6.89, 1, 1, 78, 35.75, 12.2, 1, 1, 78, 24.96, 14.36, 1, 1, 78, 12.51, 11.93, 1, 1, 78, 0.5, 5.42, 1, 2, 77, -2.28, 9.79, 0.70654, 78, -11.58, -6.52, 0.29346, 2, 76, -9.5, 7.06, 0.65829, 77, -11.28, -11.47, 0.34171, 3, 75, -30.82, 9.74, 0.0073, 76, -8.04, -0.33, 0.92332, 77, -13, -18.8, 0.06937, 2, 75, -20.5, 1.42, 0.27825, 76, -3.8, -12.89, 0.72175, 2, 75, -12.21, -3.55, 0.68478, 76, 0.53, -21.53, 0.31522, 2, 75, -12.06, 7.74, 0.36854, 76, 6.72, -12.09, 0.63146, 2, 75, 1.63, 13.93, 0.47688, 76, 21.58, -14.21, 0.52312, 2, 75, 11.9, 18.47, 0.45743, 76, 32.69, -15.9, 0.54257, 2, 76, 30.2, 19.2, 0.44311, 77, 29.89, -16.78, 0.55689, 2, 76, 19.95, 15.2, 0.51172, 77, 18.9, -16.21, 0.48828, 2, 76, 6.3, 11.46, 0.55692, 77, 4.92, -13.98, 0.44308, 2, 77, 10.14, 9.38, 0.48868, 78, 0.75, -8.08, 0.51132, 2, 77, 22.65, 10.1, 0.44642, 78, 13.27, -8.53, 0.55358, 2, 77, 36.85, 10.69, 0.44893, 78, 27.46, -9.27, 0.55107], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 50, 52, 52, 54, 0, 54, 52, 56, 56, 58, 58, 60, 60, 18, 24, 62, 62, 64, 64, 66, 48, 50, 66, 48, 46, 48, 46, 68, 68, 70, 70, 72, 72, 30], "width": 130, "height": 91}}, "foot2": {"foot2": {"type": "mesh", "uvs": [0.01796, 0, 0.11532, 0.06522, 0.21628, 0.11155, 0.31545, 0.13239, 0.42723, 0.13239, 0.48312, 0.10923, 0.48492, 0.15787, 0.45608, 0.23895, 0.44886, 0.31308, 0.46329, 0.36636, 0.53, 0.42658, 0.61834, 0.52851, 0.79471, 0.67293, 0.93714, 0.80728, 1, 0.87909, 1, 0.9787, 0.85962, 1, 0.70637, 1, 0.55852, 0.98333, 0.41609, 0.88604, 0.29349, 0.87446, 0.18892, 0.86519, 0.08435, 0.84203, 0.06271, 0.71462, 0.06452, 0.57332, 0.10057, 0.45055, 0.09336, 0.34167, 0.05911, 0.21659, 0.02124, 0.09613, 0.18531, 0.48066, 0.34758, 0.42738, 0.14565, 0.6266, 0.31332, 0.55247, 0.44494, 0.47834, 0.42691, 0.75864, 0.47198, 0.62197, 0.53689, 0.54552], "triangles": [14, 15, 16, 13, 16, 12, 16, 17, 12, 17, 18, 12, 19, 34, 18, 34, 35, 18, 18, 35, 11, 18, 11, 12, 11, 35, 36, 16, 13, 14, 19, 20, 34, 34, 20, 32, 21, 23, 31, 32, 20, 31, 20, 21, 31, 21, 22, 23, 34, 32, 35, 23, 24, 31, 31, 24, 29, 31, 29, 32, 29, 24, 25, 32, 33, 35, 35, 33, 36, 32, 30, 33, 32, 29, 30, 33, 10, 36, 36, 10, 11, 30, 29, 26, 33, 9, 10, 33, 30, 9, 3, 30, 2, 2, 30, 26, 25, 26, 29, 30, 8, 9, 8, 30, 7, 7, 30, 3, 2, 27, 1, 2, 26, 27, 4, 7, 3, 7, 4, 6, 27, 28, 1, 4, 5, 6, 28, 0, 1], "vertices": [1, 3, 91.78, -71.97, 1, 1, 3, 90.47, -49.72, 1, 1, 3, 86.23, -28.38, 1, 1, 3, 78.71, -9.41, 1, 2, 3, 67.06, 10.06, 0.99956, 5, 42.1, 100.5, 0.00044, 1, 3, 58.09, 17.91, 1, 2, 3, 64.5, 22.17, 0.99903, 5, 54.43, 99.38, 0.00097, 2, 3, 78.5, 23.73, 0.97329, 5, 51.79, 85.54, 0.02671, 2, 3, 89.3, 28.49, 0.87521, 5, 53.16, 73.82, 0.12479, 2, 3, 95.02, 35.32, 0.71507, 5, 58, 66.34, 0.28493, 2, 3, 96.23, 51.83, 0.42925, 5, 73.42, 60.32, 0.57075, 2, 3, 100.83, 75.49, 0.152, 5, 94.67, 48.94, 0.848, 2, 3, 102.02, 117.93, 0.00434, 5, 134.87, 35.29, 0.99566, 1, 5, 168.01, 21.55, 1, 1, 5, 183.1, 13.57, 1, 1, 5, 186.84, -1.72, 1, 1, 5, 159.96, -11.77, 1, 1, 5, 129.75, -19.17, 1, 1, 5, 99.97, -23.75, 1, 1, 5, 68.23, -15.69, 1, 2, 3, 181.6, 46.98, 0.0009, 5, 43.62, -19.84, 0.9991, 2, 3, 191.25, 28.01, 0.15451, 5, 22.65, -23.46, 0.84549, 2, 3, 199.01, 7.92, 0.7553, 5, 1.16, -24.96, 0.2447, 2, 3, 184, -6.19, 0.91978, 5, -7.89, -6.45, 0.08022, 2, 3, 164.65, -17.34, 0.53641, 5, -12.85, 15.32, 0.46359, 2, 3, 144.25, -21.02, 0.9793, 5, -10.35, 35.9, 0.0207, 1, 3, 130.24, -31.11, 1, 1, 3, 116.85, -47.23, 1, 1, 3, 104.47, -63.6, 1, 1, 3, 139.5, -3.82, 1, 2, 3, 115.35, 20.12, 0.82324, 5, 37.48, 51.38, 0.17676, 2, 3, 163.42, 1.12, 0.97744, 5, 5.15, 11.06, 0.02256, 2, 3, 135.88, 24.31, 0.5938, 5, 35.43, 30.53, 0.4062, 2, 3, 112.11, 41.22, 0.51867, 5, 58.59, 48.27, 0.48133, 2, 3, 151.99, 60.82, 0.01198, 5, 65.57, 4.38, 0.98802, 2, 3, 128.76, 57.58, 0.18526, 5, 69.32, 27.53, 0.81474, 2, 3, 111.63, 62.68, 0.24243, 5, 79.25, 42.4, 0.75757], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 50, 58, 58, 60, 60, 18, 48, 62, 62, 64, 64, 66, 66, 20, 38, 68, 68, 70, 70, 72, 72, 22, 36, 38], "width": 203, "height": 158}}, "banner1": {"banner1": {"type": "mesh", "uvs": [0, 0.93425, 0.0984, 0.81031, 0.21598, 0.61201, 0.34891, 0.38272, 0.48695, 0.14724, 0.66588, 0, 0.84993, 0, 0.98797, 0.09766, 1, 0.30216, 0.97263, 0.50046, 0.86527, 0.61201, 0.74257, 0.74215, 0.60453, 0.8413, 0.45627, 0.92806, 0.3029, 0.99622, 0.11885, 1], "triangles": [0, 1, 15, 15, 1, 14, 13, 14, 2, 14, 1, 2, 13, 2, 12, 11, 12, 3, 12, 2, 3, 3, 4, 11, 11, 4, 10, 8, 10, 5, 5, 10, 4, 9, 10, 8, 8, 6, 7, 5, 6, 8], "vertices": [1, 30, 50.76, -4.64, 1, 1, 30, 39.46, -5.79, 1, 2, 29, 53.65, -9.17, 0.01339, 30, 23.83, -9.7, 0.98661, 2, 29, 35.77, -13.89, 0.65772, 30, 5.93, -14.37, 0.34228, 1, 29, 17.31, -18.67, 1, 1, 29, 0.1, -16.88, 1, 1, 29, -11.16, -7.4, 1, 2, 29, -15.46, 4.65, 0.99928, 30, -45.24, 4.33, 0.00072, 2, 29, -7.5, 15.59, 0.95401, 30, -37.25, 15.25, 0.04599, 2, 29, 2.6, 24.19, 0.80578, 30, -27.11, 23.82, 0.19422, 2, 29, 13.92, 24.29, 0.55441, 30, -15.8, 23.89, 0.44559, 2, 29, 26.96, 24.54, 0.14345, 30, -2.76, 24.09, 0.85655, 2, 29, 39.62, 22.43, 0.00156, 30, 9.9, 21.95, 0.99844, 1, 30, 22.65, 18.65, 1, 1, 30, 34.92, 14.14, 1, 1, 30, 46.31, 4.82, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 80, "height": 66}}, "banner2": {"banner2": {"type": "mesh", "uvs": [0, 0.94908, 0.09782, 1, 0.23415, 0.99516, 0.3994, 0.92604, 0.57292, 0.83963, 0.70512, 0.7417, 0.88276, 0.61497, 0.99431, 0.44215, 1, 0.22901, 0.95713, 0.07923, 0.85798, 0.01011, 0.70925, 0, 0.54813, 0.00435, 0.39114, 0.10804, 0.27959, 0.28085, 0.18457, 0.44215, 0.09782, 0.66105, 0.01932, 0.85115], "triangles": [16, 2, 1, 1, 0, 17, 3, 2, 16, 16, 1, 17, 4, 3, 15, 3, 16, 15, 15, 14, 4, 4, 14, 5, 14, 13, 5, 13, 12, 5, 8, 6, 11, 6, 5, 11, 5, 12, 11, 7, 6, 8, 10, 8, 11, 9, 8, 10], "vertices": [1, 32, 58.92, -3.09, 1, 1, 32, 53.22, 5.53, 1, 1, 32, 42.1, 13.19, 1, 1, 32, 25.98, 18.85, 1, 2, 31, 45.1, 24.11, 0.13818, 32, 8.48, 23.99, 0.86182, 2, 31, 30.39, 25.97, 0.59573, 32, -6.19, 26.06, 0.40427, 2, 31, 10.84, 28.76, 0.95358, 32, -25.71, 29.13, 0.04642, 2, 31, -5.26, 25.11, 0.99917, 32, -41.86, 25.7, 0.00083, 1, 31, -14.45, 13.07, 1, 1, 31, -17.11, 1.93, 1, 1, 31, -11.92, -7.74, 1, 2, 31, -0.31, -16.81, 0.99875, 32, -37.5, -16.29, 0.00125, 2, 31, 12.9, -25.76, 0.93124, 32, -24.42, -25.42, 0.06876, 2, 31, 29.84, -28.71, 0.6424, 32, -7.52, -28.61, 0.3576, 2, 31, 45.94, -25.05, 0.1963, 32, 8.63, -25.18, 0.8037, 2, 31, 60.23, -21.12, 0.00899, 32, 22.97, -21.45, 0.99101, 1, 32, 39.06, -13.93, 1, 1, 32, 53.29, -7.59, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 99, "height": 71}}, "banner3": {"banner3": {"type": "mesh", "uvs": [0.0027, 1, 0.1037, 1, 0.26318, 0.89299, 0.44391, 0.74947, 0.66717, 0.60063, 0.89044, 0.46773, 1, 0.32952, 1, 0.16473, 0.96486, 0.06373, 0.75754, 0, 0.57149, 0, 0.36417, 0.01058, 0.22596, 0.06373, 0.07712, 0.186, 0.01865, 0.36673, 0, 0.59, 0, 0.81326], "triangles": [2, 1, 16, 1, 0, 16, 3, 2, 15, 2, 16, 15, 15, 14, 3, 3, 14, 4, 12, 4, 14, 12, 14, 13, 4, 11, 10, 11, 4, 12, 5, 10, 9, 5, 4, 10, 6, 5, 7, 7, 5, 9, 7, 9, 8], "vertices": [1, 34, 41.43, -0.36, 1, 1, 34, 38.46, 4.57, 1, 1, 34, 28.54, 9.2, 1, 2, 33, 33.01, 19.2, 0.06893, 34, 16.21, 13.79, 0.93107, 2, 33, 17.71, 19.51, 0.62764, 34, 2.37, 20.31, 0.37236, 2, 33, 2.94, 20.58, 0.96195, 34, -10.69, 27.29, 0.03805, 2, 33, -6.69, 17.68, 0.99819, 34, -20.66, 28.56, 0.00181, 1, 33, -12.06, 9.98, 1, 1, 33, -13.7, 4.11, 1, 1, 33, -6.09, -5.63, 1, 1, 33, 2.61, -11.69, 1, 2, 33, 12.65, -17.95, 0.93772, 34, -17.5, -11.86, 0.06228, 2, 33, 20.85, -19.97, 0.7349, 34, -10.84, -17.04, 0.2651, 2, 33, 31.79, -19.11, 0.34006, 34, -0.49, -20.7, 0.65994, 2, 33, 40.42, -12.56, 0.05616, 34, 10.06, -18.23, 0.94384, 1, 34, 21.5, -12.57, 1, 1, 34, 32.4, -5.99, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 57, "height": 57}}, "banner4": {"banner4": {"type": "mesh", "uvs": [0.11944, 1, 0.18526, 1, 0.27458, 0.9158, 0.40151, 0.79774, 0.54255, 0.67124, 0.70239, 0.55318, 0.84812, 0.46042, 0.98916, 0.32549, 1, 0.16948, 0.97975, 0.03034, 0.82932, 0, 0.63187, 0.00082, 0.42032, 0.0472, 0.26518, 0.11888, 0.12415, 0.23273, 0.02072, 0.35922, 0, 0.53632, 0, 0.70498, 0.03953, 0.8905], "triangles": [2, 1, 18, 1, 0, 18, 3, 2, 17, 2, 18, 17, 4, 3, 16, 3, 17, 16, 16, 15, 4, 15, 14, 4, 14, 13, 4, 4, 13, 5, 13, 12, 5, 12, 11, 5, 8, 6, 11, 6, 5, 11, 7, 6, 8, 10, 8, 11, 9, 8, 10], "vertices": [1, 36, 64.81, 4.78, 1, 1, 36, 61.85, 9.68, 1, 1, 36, 50.84, 12.1, 1, 1, 36, 35.33, 15.63, 1, 2, 35, 52.84, 24.39, 0.04215, 36, 18.48, 19.78, 0.95785, 2, 35, 34.84, 25.02, 0.5258, 36, 1.48, 25.75, 0.4742, 2, 35, 19.38, 26.69, 0.89279, 36, -12.78, 31.95, 0.10721, 2, 35, 1.52, 25.01, 0.99482, 36, -30.33, 35.68, 0.00518, 1, 35, -9.22, 14.3, 1, 1, 35, -16.84, 3.03, 1, 1, 35, -8.99, -7.85, 1, 2, 35, 3.93, -19.17, 0.99894, 36, -41.19, -7.22, 0.00106, 2, 35, 20.7, -28, 0.90132, 36, -27.82, -20.64, 0.09868, 2, 35, 35.41, -31.73, 0.63026, 36, -14.89, -28.59, 0.36974, 2, 35, 51.92, -31.59, 0.25661, 36, 0.91, -33.38, 0.74339, 2, 35, 66.79, -28.36, 0.06048, 36, 16.07, -34.73, 0.93952, 2, 35, 79.52, -16.69, 0.00108, 36, 31.7, -27.38, 0.99892, 1, 36, 45.7, -18.92, 1, 1, 36, 59.32, -6.67, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 87, "height": 97}}, "banner5": {"banner5": {"type": "mesh", "uvs": [0.23246, 1, 0.12859, 0.90301, 0.07016, 0.77288, 0.02472, 0.64274, 0, 0.49773, 0, 0.36016, 0.07016, 0.21515, 0.2, 0.07014, 0.37529, 0, 0.62199, 0, 0.79727, 0.00693, 0.94659, 0.09245, 1, 0.204, 1, 0.39362, 0.94659, 0.51261, 0.86219, 0.72826, 0.71287, 0.63531, 0.57005, 0.74685, 0.42073, 0.85096, 0.31686, 0.94391], "triangles": [0, 1, 19, 19, 1, 18, 1, 2, 18, 18, 2, 17, 2, 3, 17, 17, 3, 16, 15, 16, 14, 5, 13, 16, 13, 5, 6, 13, 6, 7, 9, 13, 7, 16, 3, 4, 14, 16, 13, 8, 9, 7, 10, 13, 9, 12, 13, 10, 16, 4, 5, 10, 11, 12], "vertices": [1, 38, 66.74, 1.09, 1, 1, 38, 58.03, -7.89, 1, 1, 38, 45.07, -15, 1, 2, 37, 66.99, -23.56, 0.00239, 38, 31.91, -21.32, 0.99761, 2, 37, 51.57, -27.92, 0.10569, 38, 16.84, -26.78, 0.89431, 2, 37, 36.68, -30.6, 0.42214, 38, 2.18, -30.53, 0.57786, 2, 37, 20.2, -29.09, 0.80556, 38, -14.37, -30.19, 0.19444, 2, 37, 3.05, -23.87, 0.98144, 38, -31.85, -26.22, 0.01856, 2, 37, -6.51, -14.37, 0.99993, 38, -42.06, -17.43, 7e-05, 1, 37, -9.27, 0.93, 1, 1, 37, -10.47, 11.93, 1, 1, 37, -2.89, 22.86, 1, 2, 37, 8.59, 28.35, 0.99601, 38, -30.06, 26.26, 0.00399, 2, 37, 29.12, 32.05, 0.79568, 38, -9.85, 31.42, 0.20432, 2, 37, 42.6, 31.06, 0.44552, 38, 3.66, 31.4, 0.55448, 2, 37, 66.89, 30.04, 0.11149, 38, 27.96, 32.12, 0.88851, 2, 37, 58.49, 18.96, 0.08617, 38, 20.38, 20.48, 0.91383, 2, 37, 72.16, 12.29, 0.00028, 38, 34.5, 14.8, 0.99972, 1, 38, 47.92, 8.52, 1, 1, 38, 59.45, 4.71, 1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38], "width": 63, "height": 110}}, "banner6": {"banner6": {"type": "mesh", "uvs": [0.121, 0.07599, 0.00672, 0.19358, 0, 0.36741, 0.03078, 0.55657, 0.15107, 0.72017, 0.33753, 0.82242, 0.54805, 0.91956, 0.79465, 0.99624, 1, 1, 0.99313, 0.84798, 0.92697, 0.61792, 0.82472, 0.39297, 0.75856, 0.24471, 0.64428, 0.10667, 0.45782, 0, 0.25934, 0], "triangles": [7, 9, 8, 7, 6, 9, 9, 6, 10, 6, 5, 10, 5, 11, 10, 11, 4, 12, 4, 11, 5, 13, 12, 3, 13, 3, 15, 15, 14, 13, 3, 12, 4, 0, 15, 2, 15, 3, 2, 0, 2, 1], "vertices": [1, 39, -6.96, -5.7, 1, 2, 39, -2.3, -16.98, 0.99887, 40, -36.01, -11.65, 0.00113, 2, 39, 9.76, -23.92, 0.93557, 40, -25.14, -20.34, 0.06443, 2, 39, 24.1, -29.17, 0.66394, 40, -11.76, -27.71, 0.33606, 2, 39, 39.5, -28.1, 0.2837, 40, 3.63, -28.99, 0.7163, 2, 39, 52.67, -20.75, 0.03943, 40, 17.76, -23.72, 0.96057, 1, 40, 32.56, -16.9, 1, 1, 40, 47.5, -7.13, 1, 1, 40, 56.1, 3.88, 1, 1, 40, 46.07, 10.78, 1, 2, 39, 57.05, 22.32, 0.00417, 40, 28.63, 18.19, 0.99583, 2, 39, 37.9, 24.64, 0.22652, 40, 10.05, 23.38, 0.77348, 2, 39, 25.32, 26.23, 0.56889, 40, -2.14, 26.87, 0.43111, 2, 39, 11.92, 24.56, 0.85057, 40, -15.64, 27.25, 0.14943, 2, 39, -1.57, 17.37, 0.98179, 40, -30.07, 22.19, 0.01821, 1, 39, -7.91, 5.46, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 68, "height": 80}}, "banner7": {"banner7": {"type": "mesh", "uvs": [0.37206, 0.06006, 0.27468, 0, 0.15782, 0, 0.06044, 0.06591, 0, 0.20029, 0.01175, 0.36389, 0.09939, 0.55086, 0.22112, 0.70862, 0.38667, 0.84301, 0.59603, 0.94818, 0.81514, 1, 1, 0.97155, 1, 0.86054, 0.89305, 0.79042, 0.76645, 0.65019, 0.67881, 0.46906, 0.57656, 0.31131, 0.46457, 0.16524], "triangles": [10, 13, 11, 13, 10, 14, 13, 12, 11, 10, 9, 14, 9, 8, 14, 8, 15, 14, 15, 8, 16, 17, 7, 6, 8, 7, 16, 7, 17, 16, 17, 6, 0, 1, 5, 2, 0, 5, 1, 0, 6, 5, 3, 2, 4, 2, 5, 4], "vertices": [2, 41, 2.28, 20.03, 0.99265, 42, -21.73, 27.54, 0.00735, 1, 41, -6.22, 16.52, 1, 1, 41, -12.58, 9.04, 1, 1, 41, -14.36, -0.18, 1, 1, 41, -10.48, -10.14, 1, 1, 41, -1.12, -16.8, 1, 2, 41, 13.62, -19.67, 0.9889, 42, -22.42, -13.75, 0.0111, 2, 41, 28.66, -19.03, 0.71826, 42, -7.85, -17.51, 0.28174, 2, 41, 44.83, -14.53, 0.08742, 42, 8.93, -17.9, 0.91258, 2, 41, 61.83, -5.9, 0, 42, 27.71, -14.58, 1, 1, 42, 45.15, -7.68, 1, 1, 42, 57.14, 2.39, 1, 1, 42, 52.94, 8.93, 1, 1, 42, 42.72, 8.2, 1, 1, 42, 28.47, 10.7, 1, 2, 41, 40.78, 21.12, 0.09589, 42, 15.42, 17.39, 0.90411, 2, 41, 26.8, 21.73, 0.54507, 42, 2.23, 22.03, 0.45493, 2, 41, 12.92, 21.18, 0.91671, 42, -11.22, 25.55, 0.08329], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 84, "height": 70}}, "yaodai2": {"yaodai2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [78.26, 15.21, -151.74, 15.21, -151.74, 57.21, 78.26, 57.21], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 230, "height": 42}}, "head": {"head": {"type": "mesh", "uvs": [0.42328, 0.34973, 0.31828, 0.44629, 0.25145, 0.57125, 0.25384, 0.7, 0.33021, 0.80792, 0.41135, 0.8988, 0.49011, 0.95939, 0.57364, 0.984, 0.67149, 1, 0.74308, 0.94613, 0.82184, 0.88933, 0.8815, 0.83443, 0.89344, 0.78899, 0.87845, 0.76956, 0.91815, 0.7368, 0.92768, 0.71034, 0.92191, 0.67858, 0.93562, 0.63965, 0.97525, 0.63468, 1, 0.62214, 1, 0.59568, 0.97692, 0.5478, 0.95786, 0.4974, 0.95151, 0.43692, 0.92927, 0.3588, 0.89592, 0.29202, 0.82604, 0.2202, 0.76886, 0.1761, 0.70089, 0.13072, 0.58177, 0.0904, 0.63259, 0.14332, 0.64847, 0.18616, 0.64371, 0.23404, 0.61989, 0.27562, 0.56589, 0.30082, 0.50077, 0.31846, 0.58787, 0.58267, 0.64759, 0.57855, 0.70991, 0.58885, 0.75925, 0.61563, 0.80079, 0.62696, 0.83974, 0.60636, 0.8696, 0.60945, 0.89167, 0.62696, 0.76054, 0.64138, 0.73847, 0.67125, 0.73198, 0.70627, 0.75405, 0.73614, 0.78521, 0.75159, 0.80599, 0.77837, 0.83585, 0.78043, 0.78521, 0.67434, 0.85532, 0.68773, 0.90076, 0.69803, 0.8707, 0.63689, 0.89077, 0.66209, 0.57304, 0.56525, 0.61317, 0.5407, 0.68508, 0.54933, 0.74277, 0.57652, 0.78124, 0.5951, 0.81468, 0.60438, 0.84813, 0.58183, 0.83057, 0.53805, 0.79629, 0.49294, 0.74612, 0.44651, 0.8891, 0.58515, 0.90749, 0.53739, 0.91836, 0.49029, 0.9192, 0.4412], "triangles": [8, 7, 9, 9, 7, 47, 47, 7, 6, 37, 45, 46, 4, 36, 46, 46, 36, 37, 5, 47, 6, 9, 49, 10, 49, 47, 48, 49, 9, 47, 46, 47, 5, 46, 5, 4, 11, 49, 50, 11, 10, 49, 11, 50, 12, 12, 50, 13, 36, 3, 56, 36, 4, 3, 52, 13, 50, 50, 49, 48, 50, 48, 52, 13, 53, 14, 13, 52, 53, 48, 51, 52, 48, 47, 51, 14, 53, 15, 47, 46, 51, 51, 46, 45, 53, 16, 15, 45, 37, 38, 3, 2, 56, 52, 55, 53, 53, 55, 16, 51, 40, 52, 52, 40, 54, 52, 54, 55, 54, 41, 42, 41, 54, 40, 16, 55, 17, 45, 44, 51, 51, 44, 40, 44, 38, 39, 44, 45, 38, 55, 43, 17, 55, 54, 43, 44, 39, 40, 18, 17, 20, 54, 42, 43, 19, 18, 20, 42, 66, 43, 43, 20, 17, 21, 66, 67, 66, 20, 43, 40, 61, 41, 39, 60, 40, 40, 60, 61, 20, 66, 21, 38, 59, 39, 39, 59, 60, 41, 62, 42, 42, 62, 66, 41, 61, 62, 61, 60, 62, 62, 60, 63, 37, 58, 38, 38, 58, 59, 66, 62, 67, 37, 36, 57, 60, 59, 63, 62, 63, 67, 36, 56, 57, 37, 57, 58, 63, 59, 64, 2, 1, 56, 57, 56, 0, 59, 58, 64, 64, 58, 65, 67, 22, 21, 56, 1, 0, 0, 35, 57, 35, 34, 57, 58, 57, 65, 57, 34, 65, 67, 63, 68, 67, 68, 22, 68, 63, 64, 22, 68, 23, 68, 69, 23, 68, 64, 69, 69, 64, 65, 34, 33, 65, 25, 65, 33, 26, 33, 32, 69, 65, 24, 25, 33, 26, 27, 32, 31, 32, 27, 26, 69, 24, 23, 24, 65, 25, 31, 28, 27, 31, 30, 28, 30, 29, 28], "vertices": [1, 43, 108.6, 60.4, 1, 1, 43, 70.36, 61.43, 1, 1, 43, 31.26, 50.04, 1, 1, 43, 1.71, 25.17, 1, 1, 43, -11.87, -9.39, 1, 1, 43, -20.77, -41.59, 1, 1, 43, -22.99, -67.61, 1, 1, 43, -16.14, -87.67, 1, 1, 43, -5.13, -108.75, 1, 1, 43, 18.17, -111.72, 1, 1, 43, 43.22, -115.46, 1, 1, 43, 64.96, -116.04, 1, 1, 43, 77.32, -109.62, 1, 1, 43, 79.58, -103.17, 1, 1, 43, 93.17, -104.27, 1, 1, 43, 100.75, -101.01, 1, 1, 43, 107.26, -93.92, 1, 1, 43, 118.37, -89.06, 1, 1, 43, 125.49, -95.42, 1, 1, 43, 132.13, -97.6, 1, 1, 43, 138.28, -92.58, 1, 1, 43, 145.93, -79.24, 1, 1, 43, 154.77, -66.16, 1, 1, 43, 167.87, -53.52, 1, 1, 43, 182.67, -34.59, 1, 1, 43, 193.17, -15.77, 1, 1, 43, 199.33, 10.74, 1, 1, 43, 200.97, 29.65, 1, 1, 43, 201.28, 50.79, 1, 1, 43, 192.72, 80.4, 1, 1, 43, 188.08, 60.99, 1, 1, 43, 180.51, 49.93, 1, 1, 43, 168.67, 41.72, 1, 1, 43, 155.42, 38.22, 1, 1, 43, 141.44, 43.39, 1, 1, 43, 127.54, 52.05, 1, 1, 43, 79.26, -14.14, 1, 1, 43, 89.21, -24.37, 1, 1, 43, 96.19, -37.81, 1, 1, 43, 97.4, -51.99, 1, 1, 43, 101.02, -61.79, 1, 1, 43, 111.67, -65.07, 1, 1, 43, 115.45, -71.16, 1, 1, 43, 114.7, -78.55, 1, 1, 43, 91.61, -57.11, 1, 1, 43, 81.35, -58.71, 1, 1, 43, 72.23, -64.16, 1, 1, 43, 68.61, -73.9, 1, 1, 43, 69.72, -82.57, 1, 1, 43, 66.62, -91.48, 1, 1, 43, 70.64, -97.38, 1, 1, 43, 87.67, -67.91, 1, 1, 43, 95.11, -83.38, 1, 1, 43, 99.56, -93.71, 1, 1, 43, 109.24, -76.57, 1, 1, 43, 106.4, -85.05, 1, 1, 43, 81.07, -8.1, 1, 1, 43, 92.82, -10.84, 1, 1, 43, 101.64, -25.73, 1, 1, 43, 104, -41.53, 1, 1, 43, 105.48, -52.14, 1, 1, 43, 108.36, -60.07, 1, 1, 43, 118.63, -61.96, 1, 1, 43, 126.16, -50.41, 1, 1, 43, 131.48, -35.53, 1, 1, 43, 134.72, -17.47, 1, 1, 43, 124.03, -70.14, 1, 1, 43, 137.9, -64.47, 1, 1, 43, 150.48, -57.53, 1, 1, 43, 162.01, -48.37, 1], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 34, 80, 88, 88, 90, 88, 78, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 26, 90, 102, 102, 104, 104, 106, 106, 30, 84, 108, 108, 110, 110, 32, 72, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 82, 82, 124, 124, 126, 126, 128, 128, 130, 84, 132, 132, 134, 134, 136, 136, 138], "width": 238, "height": 300}}, "hair1": {"hair1": {"type": "mesh", "uvs": [0.00876, 0.44241, 0.07146, 0.47235, 0.12946, 0.5266, 0.17336, 0.61267, 0.22038, 0.7137, 0.26428, 0.81847, 0.33795, 0.91015, 0.47276, 0.97189, 0.61384, 1, 0.7706, 1, 0.88346, 0.94944, 0.96341, 0.85589, 0.99319, 0.72867, 1, 0.62202, 0.99946, 0.49105, 0.96341, 0.34325, 0.87719, 0.22351, 0.75179, 0.13931, 0.60914, 0.0757, 0.47119, 0.0308, 0.32071, 0.00647, 0.17492, 0, 0.07303, 0.00834, 0, 0.0177, 0.05736, 0.07009, 0.1326, 0.15802, 0.17963, 0.23847, 0.23449, 0.3526, 0.14671, 0.36383, 0.05422, 0.39938, 0.30973, 0.40872, 0.4367, 0.49666, 0.55897, 0.60331, 0.67027, 0.76608, 0.73454, 0.88021], "triangles": [12, 34, 33, 34, 8, 33, 11, 34, 12, 10, 34, 11, 9, 34, 10, 8, 34, 9, 24, 23, 22, 21, 24, 22, 25, 24, 21, 20, 25, 21, 26, 25, 20, 27, 26, 20, 30, 27, 20, 30, 20, 19, 1, 29, 28, 0, 29, 1, 31, 30, 19, 2, 1, 28, 27, 2, 28, 30, 2, 27, 30, 3, 2, 31, 4, 3, 31, 3, 30, 5, 4, 31, 31, 19, 18, 17, 32, 31, 17, 31, 18, 32, 17, 16, 5, 31, 32, 32, 16, 15, 32, 15, 14, 33, 32, 14, 14, 13, 33, 12, 33, 13, 6, 5, 32, 7, 6, 32, 33, 7, 32, 8, 7, 33], "vertices": [1, 51, 36.28, 46.57, 1, 3, 49, 90.46, 55.82, 0.0032, 50, 60.97, 47.38, 0.00123, 51, 26.72, 43.65, 0.99557, 3, 49, 80.05, 52.56, 0.02823, 50, 50.2, 45.69, 0.02238, 51, 15.81, 43.51, 0.94939, 3, 49, 67.58, 53.26, 0.10281, 50, 37.96, 48.21, 0.09226, 51, 4.06, 47.75, 0.80493, 3, 49, 53.31, 54.63, 0.25229, 50, 24.05, 51.66, 0.169, 51, -9.21, 53.15, 0.57871, 3, 49, 38.93, 56.65, 0.43475, 50, 10.12, 55.77, 0.17739, 51, -22.42, 59.2, 0.38787, 3, 49, 23.38, 54.12, 0.6174, 50, -5.63, 55.55, 0.13661, 51, -38.04, 61.23, 0.246, 3, 49, 5.76, 42.03, 0.84814, 50, -24.83, 46.17, 0.05141, 51, -58.38, 54.69, 0.10044, 1, 45, 31.27, 102.37, 1, 1, 45, 36.89, 79.86, 1, 1, 45, 47.03, 65.17, 1, 1, 45, 61.15, 56.51, 1, 1, 45, 77.52, 56.06, 1, 2, 49, -2.76, -46.85, 0.91369, 50, -46.31, -40.49, 0.08631, 2, 49, 10.66, -55.99, 0.7912, 50, -34.37, -51.5, 0.2088, 2, 49, 28.79, -61.99, 0.61143, 50, -17.33, -60.1, 0.38857, 2, 49, 48.25, -59.9, 0.40941, 50, 2.23, -60.89, 0.59059, 2, 49, 67.38, -50.54, 0.18824, 50, 22.52, -54.43, 0.81176, 3, 49, 85.85, -37.62, 0.03793, 50, 42.69, -44.37, 0.93144, 51, -4.46, -44.55, 0.03064, 3, 49, 102.01, -23.96, 0.00079, 50, 60.68, -33.23, 0.68708, 51, 14.94, -36.1, 0.31213, 2, 50, 78.08, -19, 0.18189, 51, 34.18, -24.49, 0.81811, 2, 50, 93.38, -3.76, 0.00541, 51, 51.5, -11.59, 0.99459, 1, 51, 62.6, -1.33, 1, 1, 51, 70.29, 6.35, 1, 1, 51, 59.61, 6.09, 1, 1, 51, 44.1, 7.6, 1, 1, 51, 32.42, 11.02, 1, 2, 49, 89.01, 27.52, 0.00229, 51, 17.22, 16.95, 0.99771, 2, 49, 95.23, 39.01, 0.00219, 51, 26.48, 26.18, 0.99781, 2, 49, 99.36, 52.79, 7e-05, 51, 34.38, 38.2, 0.99993, 3, 49, 76.96, 22.3, 0.00904, 50, 42.7, 16.2, 0.03157, 51, 4.19, 15.4, 0.95939, 3, 49, 57.33, 13, 0.02972, 50, 21.91, 9.89, 0.75999, 51, -17.29, 12.12, 0.21029, 3, 49, 36.17, 5.6, 0.57807, 50, -0.1, 5.67, 0.40759, 51, -39.68, 11.08, 0.01434, 2, 49, 10.21, 3.48, 0.99821, 51, -65.17, 16.48, 0.00179, 1, 45, 50.01, 88.64, 1], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 54, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 18], "width": 148, "height": 124}}, "foot1banner": {"foot1banner": {"type": "mesh", "uvs": [0.091, 0.81297, 0.03295, 0.67138, 0.00562, 0.50255, 0.00562, 0.31739, 0.02611, 0.15946, 0.03636, 0, 0.09572, 0.03693, 0.12524, 0.13472, 0.13887, 0, 0.20928, 0.07677, 0.26151, 0.21439, 0.30921, 0.39547, 0.32965, 0.2325, 0.38643, 0.32666, 0.38643, 0.40634, 0.46592, 0.23974, 0.55222, 0.15645, 0.61582, 0.12023, 0.59311, 0.27234, 0.59538, 0.4498, 0.60446, 0.58018, 0.67487, 0.46428, 0.73392, 0.40634, 0.85202, 0.35564, 0.83612, 0.48963, 0.81568, 0.61639, 0.877, 0.64536, 1, 0.7685, 0.91107, 0.83369, 0.79524, 0.87715, 0.69758, 0.94596, 0.59311, 1, 0.41141, 1, 0.25208, 0.97481, 0.15704, 0.91336, 0.27514, 0.52585, 0.2388, 0.7178, 0.23653, 0.88439, 0.59765, 0.74315, 0.58629, 0.90612], "triangles": [7, 8, 9, 4, 5, 6, 4, 6, 7, 18, 16, 17, 15, 16, 18, 11, 12, 13, 11, 13, 14, 24, 22, 23, 7, 2, 3, 35, 10, 11, 25, 22, 24, 4, 7, 3, 2, 35, 1, 7, 9, 10, 7, 35, 2, 10, 35, 7, 36, 1, 35, 35, 11, 14, 20, 15, 19, 0, 1, 36, 28, 26, 27, 25, 21, 22, 29, 25, 26, 29, 26, 28, 25, 20, 21, 25, 38, 20, 29, 38, 25, 36, 34, 0, 14, 15, 38, 19, 15, 18, 20, 38, 15, 30, 39, 38, 38, 35, 14, 38, 36, 35, 38, 32, 36, 32, 37, 36, 37, 34, 36, 29, 30, 38, 32, 33, 37, 34, 37, 33, 32, 38, 39, 31, 39, 30, 32, 39, 31], "vertices": [2, 68, -9.98, 1.39, 0.8892, 69, -7.98, 28.54, 0.1108, 1, 68, -0.61, 9.69, 1, 1, 68, 11.29, 14.69, 1, 1, 68, 24.84, 16.68, 1, 1, 68, 36.76, 15.98, 1, 1, 68, 48.61, 16.5, 1, 1, 68, 46.92, 9.17, 1, 1, 68, 40.27, 4.67, 1, 1, 68, 50.36, 4.53, 1, 2, 68, 45.95, -4.51, 0.99943, 69, 46.81, 41.21, 0.00057, 2, 68, 36.77, -12.09, 0.95239, 69, 40.61, 31.06, 0.04761, 2, 68, 24.33, -19.6, 0.47076, 69, 31.3, 19.89, 0.52924, 2, 68, 36.61, -20.24, 0.27261, 69, 43.11, 23.3, 0.72739, 2, 68, 30.69, -27.88, 0.25662, 69, 40.01, 14.15, 0.74338, 2, 68, 24.85, -28.74, 0.16245, 69, 34.77, 11.43, 0.83755, 2, 68, 38.41, -36.23, 0.00041, 69, 50.03, 8.78, 0.99959, 1, 69, 60.19, 2.57, 1, 1, 69, 66.02, -2.86, 1, 2, 69, 54.8, -5.66, 0.99881, 70, 44.49, 27.61, 0.00119, 2, 69, 43.26, -11.94, 0.88917, 70, 34.25, 19.39, 0.11083, 2, 69, 35.19, -17.33, 0.38225, 70, 27.25, 12.65, 0.61775, 2, 69, 46.62, -20.76, 0.01807, 70, 39.12, 11.3, 0.98193, 2, 69, 53.64, -24.97, 0.00016, 70, 46.76, 8.39, 0.99984, 1, 70, 58.23, -0.37, 1, 1, 70, 49.23, -4.93, 1, 1, 70, 40.33, -8.73, 1, 1, 70, 43.04, -15.78, 1, 1, 70, 44.66, -32.84, 1, 1, 70, 34.44, -27.46, 1, 1, 70, 23.56, -18.59, 1, 1, 70, 12.5, -12.56, 1, 1, 70, 1.81, -5.22, 1, 2, 69, -2.88, -11.39, 0.7357, 70, -11.26, 11.77, 0.2643, 2, 68, -19.07, -19.16, 0.17931, 69, -9.87, 6.16, 0.82069, 2, 68, -16.2, -7.4, 0.56041, 69, -10.99, 18.21, 0.43959, 2, 68, 14.2, -17.03, 0.52005, 69, 20.88, 19.03, 0.47995, 2, 68, -0.48, -14.85, 0.48355, 69, 6.3, 16.3, 0.51645, 2, 68, -12.72, -16.37, 0.29701, 69, -4.77, 10.87, 0.70299, 2, 69, 24.11, -22.16, 0.13373, 70, 17.2, 5.94, 0.86627, 1, 70, 6.83, -0.35, 1], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 68, 22, 70, 70, 72, 72, 74, 66, 68, 74, 66, 64, 66, 40, 76, 76, 78, 78, 62], "width": 118, "height": 74}}, "hair3": {"hair3": {"type": "mesh", "uvs": [0.29804, 0.73982, 0.3691, 0.79853, 0.4899, 0.88006, 0.63913, 0.94856, 0.78481, 0.99748, 0.90561, 1, 0.98378, 0.95182, 1, 0.86049, 1, 0.76591, 0.93048, 0.63871, 0.88785, 0.50825, 0.80257, 0.39409, 0.71019, 0.29625, 0.60716, 0.20493, 0.47214, 0.12013, 0.35489, 0.06468, 0.195, 0.01902, 0, 0, 0.08841, 0.08425, 0.21276, 0.21145, 0.26251, 0.30929, 0.29449, 0.4528, 0.29804, 0.56696, 0.28027, 0.65502], "triangles": [7, 5, 4, 8, 4, 3, 3, 2, 9, 10, 2, 1, 13, 21, 20, 13, 20, 14, 14, 19, 15, 14, 20, 19, 18, 16, 19, 19, 16, 15, 18, 17, 16, 12, 11, 21, 12, 21, 13, 21, 11, 22, 10, 22, 11, 9, 2, 10, 10, 1, 22, 8, 7, 4, 3, 9, 8, 0, 23, 1, 1, 23, 22, 6, 5, 7], "vertices": [4, 55, 34.27, 53.01, 0.20988, 56, -7.96, 56.52, 0.16049, 57, -43.49, 78.45, 0.02469, 45, 81.82, 105.22, 0.60494, 1, 45, 73.8, 90.1, 1, 1, 45, 63.62, 65.27, 1, 1, 45, 57.14, 36.11, 1, 1, 45, 54.21, 8.5, 1, 1, 45, 58.98, -12.6, 1, 3, 55, -27.53, -60.82, 0.31276, 56, -95.03, -39.37, 0.0823, 45, 71.49, -23.89, 0.60494, 3, 55, -10.49, -66.74, 0.49383, 56, -79.88, -49.17, 0.20988, 45, 89.47, -22.39, 0.2963, 4, 55, 7.68, -69.9, 0.49383, 56, -62.99, -56.56, 0.39095, 57, -137.1, -5.52, 0.00412, 45, 107.36, -17.92, 0.11111, 4, 55, 34.26, -61.9, 0.36626, 56, -35.27, -55.1, 0.58025, 57, -110.88, -14.62, 0.02469, 45, 128.41, 0.17, 0.02881, 4, 55, 60.63, -58.75, 0.20576, 56, -8.91, -58.31, 0.7037, 57, -87.67, -27.53, 0.08642, 45, 151.24, 13.75, 0.00412, 3, 55, 85.18, -47.54, 0.08642, 56, 17.6, -53.24, 0.7037, 57, -61.21, -32.84, 0.20988, 3, 55, 106.82, -34.53, 0.02469, 56, 41.71, -45.74, 0.58025, 57, -36.05, -34.99, 0.39506, 3, 55, 127.53, -19.41, 0.00412, 56, 65.42, -35.98, 0.39095, 57, -10.41, -34.89, 0.60494, 2, 56, 90.26, -20.47, 0.20988, 57, 18.44, -29.89, 0.79012, 2, 56, 108.57, -5.57, 0.08642, 57, 41.02, -23, 0.91358, 2, 56, 128.2, 17.08, 0.02881, 57, 67.74, -9.42, 0.97119, 2, 56, 145.59, 47.57, 0.02881, 57, 95.35, 12.26, 0.97119, 3, 55, 166.66, 68.02, 0.00412, 56, 124.2, 39.66, 0.0823, 57, 72.55, 13, 0.91358, 3, 55, 138.4, 50.35, 0.02469, 56, 92.55, 29.2, 0.18519, 57, 39.3, 15.25, 0.79012, 4, 55, 118.07, 44.86, 0.0823, 56, 71.5, 28.69, 0.30864, 57, 19.61, 22.72, 0.60494, 45, 161.73, 131.75, 0.00412, 4, 55, 89.52, 44.02, 0.18107, 56, 43.57, 34.67, 0.39506, 57, -4.01, 38.78, 0.39506, 45, 135.97, 119.41, 0.02881, 4, 55, 67.48, 47.22, 0.28395, 56, 22.92, 43.01, 0.39506, 57, -19.99, 54.29, 0.20988, 45, 114.53, 113.39, 0.11111, 4, 55, 51.11, 53.3, 0.31276, 56, 8.46, 52.8, 0.30453, 57, -29.68, 68.82, 0.08642, 45, 97.09, 112.31, 0.2963], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46], "width": 179, "height": 195}}, "hair4": {"hair4": {"type": "mesh", "uvs": [0, 0.80385, 0.08878, 0.87414, 0.17922, 0.95321, 0.26242, 1, 0.37457, 0.97517, 0.49395, 0.90269, 0.6278, 0.80385, 0.71825, 0.66109, 0.74719, 0.51173, 0.72548, 0.37775, 0.76889, 0.23937, 0.84486, 0.13175, 1, 0.01314, 1, 0, 0.75442, 0.01534, 0.5663, 0.05926, 0.39989, 0.12955, 0.21901, 0.23278, 0.10686, 0.33821, 0.03813, 0.47219, 0, 0.63912], "triangles": [12, 14, 13, 11, 14, 12, 11, 15, 14, 10, 15, 11, 16, 15, 10, 9, 16, 10, 17, 16, 9, 18, 9, 8, 9, 18, 17, 8, 19, 18, 7, 19, 8, 20, 19, 7, 20, 1, 0, 7, 6, 20, 6, 1, 20, 5, 1, 6, 2, 1, 5, 4, 2, 5, 3, 2, 4], "vertices": [1, 58, -1.29, 14.65, 1, 1, 58, -7.39, 6.83, 1, 1, 58, -14.42, -1.34, 1, 1, 58, -18.07, -8.14, 1, 1, 58, -13.47, -14.82, 1, 2, 58, -3.57, -20.64, 0.99386, 59, -31.1, -21.27, 0.00614, 2, 58, 9.43, -26.67, 0.90086, 59, -17.98, -27.04, 0.09914, 3, 58, 26.45, -28.61, 0.53952, 59, -0.93, -28.64, 0.45623, 60, -18.72, -37.49, 0.00425, 3, 58, 43.14, -26.32, 0.12759, 59, 15.71, -26.01, 0.76412, 60, -4.03, -29.24, 0.10829, 3, 58, 57.29, -21.13, 0.00467, 59, 29.76, -20.54, 0.45477, 60, 7.23, -19.22, 0.54057, 2, 59, 45.48, -19.19, 0.02493, 60, 21.5, -12.49, 0.97507, 1, 60, 34.29, -9.58, 1, 1, 60, 51.24, -10.3, 1, 1, 60, 52.43, -9.43, 1, 1, 60, 41.22, 3.06, 1, 1, 60, 29.71, 10.51, 1, 2, 59, 50.54, 8.29, 0.00051, 60, 16.69, 15.03, 0.99949, 2, 59, 36.08, 17.01, 0.37536, 60, 0.11, 18.18, 0.62464, 3, 58, 51.02, 20.7, 0.00687, 59, 22.65, 21.16, 0.87773, 60, -13.93, 17.4, 0.11541, 3, 58, 35.32, 21.46, 0.21386, 59, 6.94, 21.61, 0.78588, 60, -28.81, 12.36, 0.00026, 2, 58, 16.57, 19.28, 0.8645, 59, -11.76, 19.05, 0.1355], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40], "width": 68, "height": 112}}, "hair5": {"hair5": {"type": "mesh", "uvs": [0.01212, 0.37995, 0.00773, 0.63824, 0.05605, 0.89039, 0.13073, 1, 0.26251, 0.95804, 0.3943, 0.79814, 0.54366, 0.62594, 0.68862, 0.54599, 0.81601, 0.51525, 1, 0.49064, 0.92583, 0.30614, 0.79844, 0.13394, 0.65348, 0, 0.48655, 0, 0.32841, 0.02325, 0.17466, 0.1647, 0.09119, 0.28154], "triangles": [4, 2, 1, 3, 2, 4, 8, 11, 10, 8, 10, 9, 7, 12, 11, 7, 11, 8, 7, 13, 12, 7, 6, 13, 14, 6, 15, 6, 14, 13, 5, 15, 6, 16, 15, 5, 1, 0, 16, 5, 1, 16, 5, 4, 1], "vertices": [4, 61, 5.63, 14.59, 0.20988, 62, -10.63, 12.28, 0.16049, 63, -30.97, -2.92, 0.02469, 45, 56.26, 13.96, 0.60494, 1, 45, 46.17, 11.69, 1, 1, 45, 37.05, 6.62, 1, 1, 45, 33.81, 1.5, 1, 4, 61, -0.8, -11.68, 0.08642, 62, -10.11, -14.76, 0.22634, 63, -17.56, -26.41, 0.0823, 45, 37.22, -5.25, 0.60494, 4, 61, 8.94, -12.37, 0.12346, 62, -0.52, -12.94, 0.37037, 63, -10.02, -20.21, 0.20988, 45, 45.22, -10.86, 0.2963, 4, 61, 19.72, -13.42, 0.10288, 62, 10.17, -11.18, 0.39095, 63, -1.47, -13.54, 0.39506, 45, 53.93, -17.3, 0.11111, 4, 61, 27.72, -16.89, 0.05761, 62, 18.8, -12.49, 0.30864, 63, 6.72, -10.56, 0.60494, 45, 59, -24.4, 0.02881, 4, 61, 33.64, -21.07, 0.02058, 62, 25.59, -15.01, 0.18519, 63, 13.89, -9.52, 0.79012, 45, 61.92, -31.03, 0.00412, 3, 61, 41.62, -27.66, 0.00412, 62, 34.99, -19.33, 0.08642, 63, 24.21, -8.81, 0.90947, 2, 62, 35.11, -10.86, 0.04938, 63, 20.26, -1.32, 0.95062, 3, 61, 43.73, -9.59, 0.00412, 62, 32.4, -1.33, 0.08642, 63, 13.31, 5.75, 0.90947, 3, 61, 41.77, -0.06, 0.02469, 62, 28.07, 7.38, 0.18519, 63, 5.33, 11.32, 0.79012, 4, 61, 35.16, 6.55, 0.0823, 62, 19.98, 12.08, 0.30864, 63, -4.01, 11.57, 0.60494, 45, 77.44, -8.13, 0.00412, 4, 61, 28.24, 12.15, 0.18107, 62, 11.86, 15.72, 0.39506, 63, -12.89, 10.87, 0.39506, 45, 74.39, 0.24, 0.02881, 4, 61, 18.16, 14.24, 0.28395, 62, 1.57, 15.15, 0.39506, 63, -21.65, 5.44, 0.20988, 45, 66.82, 7.22, 0.11111, 4, 61, 11.55, 14.24, 0.31276, 62, -4.82, 13.46, 0.30453, 63, -26.44, 0.89, 0.08642, 45, 61.15, 10.62, 0.2963], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 56, "height": 40}}, "arm1": {"arm1": {"type": "mesh", "uvs": [0.58765, 0, 0.51508, 0.0186, 0.42931, 0.08613, 0.38312, 0.15816, 0.34134, 0.22119, 0.26217, 0.28196, 0.19839, 0.37425, 0.13021, 0.47104, 0.06643, 0.56108, 0.01145, 0.66238, 0, 0.74341, 0, 0.85146, 0.02465, 0.9415, 0.12361, 0.98877, 0.22478, 1, 0.32814, 1, 0.42271, 0.97751, 0.4469, 0.90999, 0.4469, 0.84696, 0.47109, 0.78168, 0.55246, 0.75017, 0.64483, 0.7074, 0.724, 0.65112, 0.76799, 0.60385, 0.80538, 0.52507, 0.85156, 0.45754, 0.92414, 0.3765, 1, 0.30897, 0.99891, 0.27071, 0.92853, 0.20768, 0.84716, 0.14465, 0.7394, 0.07487, 0.65803, 0.0276, 0.34794, 0.29547, 0.40292, 0.39451, 0.51948, 0.41927, 0.66243, 0.45754, 0.76799, 0.49355, 0.36113, 0.77268, 0.25777, 0.70965, 0.12581, 0.65337], "triangles": [1, 32, 2, 32, 1, 0, 9, 8, 40, 40, 11, 10, 9, 40, 10, 39, 11, 40, 11, 13, 12, 17, 15, 38, 39, 13, 11, 14, 13, 39, 38, 14, 39, 38, 15, 14, 17, 16, 15, 33, 4, 3, 5, 4, 33, 26, 30, 29, 26, 29, 28, 26, 28, 27, 3, 35, 33, 34, 33, 35, 35, 32, 31, 35, 2, 32, 35, 3, 2, 25, 30, 26, 36, 35, 31, 36, 31, 30, 36, 30, 25, 37, 36, 25, 24, 37, 25, 23, 36, 37, 23, 37, 24, 22, 36, 23, 40, 8, 7, 21, 35, 36, 21, 36, 22, 7, 6, 34, 33, 6, 5, 34, 6, 33, 39, 7, 34, 40, 7, 39, 20, 35, 21, 38, 39, 34, 38, 34, 35, 19, 38, 35, 20, 19, 35, 18, 38, 19, 38, 18, 17], "vertices": [1, 15, -229.19, 60.89, 1, 3, 16, -3.32, -49.97, 0.22465, 17, -158.9, 108.09, 7e-05, 15, -248.13, 56.15, 0.77528, 3, 16, 24.92, -50.53, 0.44328, 17, -140.83, 86.39, 0.00015, 15, -270.51, 38.93, 0.55658, 3, 16, 45.81, -43.74, 0.65214, 17, -122.01, 75.05, 0.00022, 15, -282.57, 20.56, 0.34764, 3, 16, 64.37, -38.01, 0.85108, 17, -105.53, 64.77, 0.00029, 15, -293.47, 4.49, 0.14863, 3, 16, 90.19, -38.84, 0.82747, 17, -89.24, 44.72, 0.07222, 15, -314.14, -11.01, 0.10031, 3, 16, 117.9, -30.88, 0.77951, 17, -65.08, 29, 0.16196, 15, -330.78, -34.54, 0.05854, 3, 16, 147.22, -22.75, 0.69709, 17, -39.73, 12.17, 0.2769, 15, -348.58, -59.22, 0.02601, 2, 16, 174.56, -15.24, 0.59029, 17, -16.15, -3.58, 0.40971, 1, 17, 10.22, -16.92, 1, 1, 17, 30.98, -19.11, 1, 2, 16, 234.4, 31.71, 0, 17, 58.51, -18.04, 1, 2, 16, 243.73, 53.65, 2e-05, 17, 81.21, -10.73, 0.99998, 2, 16, 231.11, 79.21, 0.02816, 17, 92.25, 15.55, 0.97184, 2, 16, 212.3, 97.95, 0.13766, 17, 94.1, 42.04, 0.86234, 2, 16, 191.24, 114.82, 0.26636, 17, 93.05, 69, 0.73364, 2, 16, 168.4, 125.78, 0.35107, 17, 86.37, 93.44, 0.64893, 2, 16, 152.7, 116.29, 0.41274, 17, 68.92, 99.09, 0.58726, 2, 16, 142.65, 103.74, 0.52621, 17, 52.86, 98.47, 0.47379, 2, 16, 127.32, 94.7, 0.72638, 17, 35.98, 104.13, 0.27362, 2, 16, 105.72, 101.71, 0.88697, 17, 27.13, 125.05, 0.11303, 2, 16, 80.08, 108.27, 0.95809, 17, 15.3, 148.71, 0.04191, 2, 16, 54.98, 110, 0.98621, 17, 0.17, 168.81, 0.01379, 2, 16, 38.49, 107.77, 0.99433, 17, -12.32, 179.82, 0.00567, 3, 16, 18.31, 98.19, 0.9424, 17, -32.77, 188.79, 0.00317, 15, -172.36, -73, 0.05444, 3, 16, -1.86, 92.29, 0.84306, 17, -50.45, 200.17, 0.00186, 15, -160.31, -55.78, 0.15508, 3, 16, -29.57, 88.01, 0.72151, 17, -71.83, 218.3, 0.00083, 15, -141.36, -35.11, 0.27766, 2, 16, -55.79, 86.95, 0.584, 15, -121.56, -17.89, 0.416, 2, 16, -61.67, 79.16, 0.51699, 15, -121.85, -8.14, 0.48301, 2, 16, -57.38, 55.13, 0.44186, 15, -140.22, 7.94, 0.55814, 2, 16, -50.86, 29.31, 0.35266, 15, -161.45, 24.01, 0.64734, 2, 16, -40.03, -2.17, 0.24721, 15, -189.58, 41.8, 0.75279, 2, 16, -31, -24.85, 0.12767, 15, -210.82, 53.86, 0.87233, 3, 16, 74.87, -22.15, 0.89227, 17, -86.67, 67.23, 0.00065, 15, -291.75, -14.45, 0.10709, 3, 16, 79.47, 6.53, 0.92628, 17, -61.98, 82.54, 0.001, 15, -277.4, -39.71, 0.07272, 3, 16, 59.67, 30.48, 0.95443, 17, -56.85, 113.18, 0.00135, 15, -246.98, -46.02, 0.04422, 3, 16, 36.66, 61.42, 0.97932, 17, -48.54, 150.84, 0.0017, 15, -209.67, -55.78, 0.01898, 3, 16, 20.9, 85.82, 0.96866, 17, -40.43, 178.73, 0.00229, 15, -182.12, -64.96, 0.02905, 2, 16, 148.28, 74.96, 0.60874, 17, 34.8, 75.37, 0.39126, 2, 16, 159.28, 45.55, 0.65864, 17, 19.78, 47.79, 0.34136, 2, 16, 177.18, 12.82, 0.71518, 17, 6.77, 12.82, 0.28482], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 8, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 48, 36, 76, 76, 78, 78, 80, 80, 18], "width": 261, "height": 255}}, "hair2": {"hair2": {"type": "mesh", "uvs": [0, 0, 0, 0.03164, 0.06586, 0.14764, 0.15357, 0.30608, 0.24694, 0.47584, 0.31767, 0.66257, 0.39406, 0.80403, 0.52137, 0.94832, 0.6883, 0.99076, 0.99669, 1, 0.97972, 0.86345, 0.86655, 0.61447, 0.68547, 0.33437, 0.47611, 0.16179, 0.2724, 0.06276, 0.09698, 0.01184], "triangles": [8, 7, 11, 8, 10, 9, 5, 4, 12, 1, 0, 15, 2, 1, 15, 2, 15, 14, 3, 2, 14, 13, 4, 3, 13, 3, 14, 4, 13, 12, 5, 12, 11, 6, 5, 11, 7, 6, 11, 10, 8, 11], "vertices": [3, 52, 93.5, 19.67, 0.00274, 53, 70.76, 9.47, 0.01646, 54, 45, -0.96, 0.9808, 4, 52, 91.15, 20.77, 0.00823, 53, 68.59, 10.9, 0.03018, 54, 43.23, 0.94, 0.96022, 45, 124.83, 158.59, 0.00137, 4, 52, 80.25, 19.9, 0.02743, 53, 57.68, 11.62, 0.06859, 54, 32.8, 4.23, 0.893, 45, 116.91, 151.04, 0.01097, 4, 52, 65.44, 18.88, 0.06036, 53, 42.87, 12.77, 0.12209, 54, 18.69, 8.86, 0.76955, 45, 106.05, 140.91, 0.04801, 4, 52, 49.58, 17.82, 0.09465, 53, 27.04, 14.04, 0.16461, 54, 3.61, 13.85, 0.59534, 45, 94.41, 130.11, 0.1454, 4, 52, 33.26, 19.04, 0.10425, 53, 11.06, 17.62, 0.16461, 54, -11.06, 21.13, 0.39369, 45, 80.96, 120.77, 0.33745, 4, 52, 20.1, 18.27, 0.06996, 53, -2.07, 18.77, 0.10425, 54, -23.54, 25.36, 0.19204, 45, 71.22, 111.88, 0.63374, 1, 45, 62.28, 98.88, 1, 1, 45, 62.22, 84.76, 1, 1, 45, 67.62, 60.04, 1, 4, 52, -4.63, -23.19, 0.06996, 53, -32.57, -18.64, 0.10425, 54, -62.05, -3.74, 0.19204, 45, 78.14, 64.11, 0.63374, 4, 52, 17.8, -23.41, 0.10425, 53, -10.42, -22.13, 0.16461, 54, -41.36, -12.39, 0.39369, 45, 95.7, 78.06, 0.33745, 4, 52, 44.89, -19.67, 0.09465, 53, 16.93, -22.37, 0.16461, 54, -14.85, -19.12, 0.59534, 45, 114.38, 98.04, 0.1454, 4, 52, 64.97, -10.1, 0.06036, 53, 38.2, -15.83, 0.12209, 54, 7.36, -17.81, 0.76955, 45, 123.95, 118.12, 0.04801, 4, 52, 79.4, 1.61, 0.02743, 53, 54.17, -6.35, 0.06859, 54, 25.13, -12.39, 0.893, 45, 127.77, 136.3, 0.01097, 4, 52, 89.26, 12.88, 0.00823, 53, 65.57, 3.37, 0.03018, 54, 38.51, -5.66, 0.96022, 45, 128.33, 151.27, 0.00137], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 82, "height": 82}}, "hand11": {"hand11": {"type": "mesh", "uvs": [0.03448, 0, 0.26687, 0.23, 0.30007, 0.18016, 0.35713, 0.15985, 0.42145, 0.17831, 0.46088, 0.23185, 0.47955, 0.29277, 0.4837, 0.35185, 0.47229, 0.43677, 0.6414, 0.59185, 0.74669, 0.63251, 1, 0.99436, 0.71557, 0.84667, 0.61804, 0.71928, 0.44168, 0.53097, 0.3981, 0.56974, 0.34312, 0.5679, 0.28813, 0.51251, 0.25908, 0.42574, 0.24352, 0.33528, 0, 0.0842, 0, 0], "triangles": [20, 21, 0, 1, 20, 0, 19, 20, 1, 18, 19, 1, 5, 7, 4, 7, 5, 6, 2, 18, 1, 3, 17, 18, 2, 3, 18, 3, 14, 17, 14, 3, 4, 4, 8, 14, 7, 8, 4, 16, 17, 14, 15, 16, 14, 9, 14, 8, 13, 14, 9, 10, 13, 9, 12, 13, 10, 12, 10, 11], "vertices": [-187, -149.86, -33.08, -48.03, -39.73, -18.62, -28.51, 20.17, -1.56, 56.48, 29.58, 71.14, 56.63, 71.7, 78.41, 63.64, 104.16, 41.36, 211.9, 117.66, 259.64, 175.22, 465.88, 266.25, 323.82, 117.57, 248.55, 80.39, 126.98, 5.58, 126.47, -28.23, 108.25, -61.77, 71.5, -85.69, 32.18, -87.99, -4.11, -81.33, -168.87, -186.22, -198.02, -171.1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42], "width": 694, "height": 390}}, "leg1": {"leg1": {"type": "mesh", "uvs": [0.12735, 0.72417, 0.03977, 0.80039, 0, 0.87661, 0.02119, 0.94574, 0.11939, 0.9936, 0.25475, 1, 0.3901, 0.98651, 0.483, 0.95106, 0.50423, 0.89788, 0.49627, 0.85357, 0.51485, 0.80394, 0.55731, 0.76494, 0.66878, 0.7224, 0.75371, 0.66213, 0.82537, 0.55755, 0.89438, 0.44056, 0.97665, 0.32712, 1, 0.22076, 0.98196, 0.11973, 0.89703, 0.04174, 0.76167, 0, 0.54404, 0, 0.39807, 0.04883, 0.29456, 0.15518, 0.19901, 0.2828, 0.18574, 0.40334, 0.17247, 0.55223, 0.17513, 0.62314, 0.16186, 0.67631], "triangles": [9, 6, 5, 1, 5, 4, 8, 7, 6, 9, 5, 0, 4, 2, 1, 8, 6, 9, 0, 5, 1, 4, 3, 2, 9, 0, 10, 0, 28, 10, 10, 28, 11, 26, 12, 11, 11, 28, 27, 13, 12, 26, 14, 13, 26, 27, 26, 11, 26, 25, 14, 14, 25, 15, 15, 25, 24, 15, 24, 23, 15, 23, 16, 21, 16, 23, 21, 23, 22, 17, 16, 20, 20, 16, 21, 18, 20, 19, 20, 18, 17], "vertices": [2, 10, 216.04, -57.39, 0.30333, 11, 28.95, -49.85, 0.69667, 2, 10, 240.8, -67.12, 0.06087, 11, 55.52, -48.33, 0.93913, 2, 10, 263.13, -68.36, 0.00793, 11, 76.31, -40.11, 0.99207, 1, 11, 88.25, -24.63, 1, 1, 11, 86.43, -2.22, 1, 1, 11, 71.42, 17.9, 1, 1, 11, 52.23, 34.42, 1, 1, 11, 33.57, 41.02, 1, 1, 11, 19.85, 34.38, 1, 2, 10, 231.63, 18.11, 0.02167, 11, 11.52, 25.25, 0.97833, 2, 10, 217.46, 17.61, 0.41304, 11, -1.14, 18.87, 0.58696, 2, 10, 204.91, 22.18, 0.91154, 11, -14.45, 17.77, 0.08846, 1, 10, 187.89, 38.74, 1, 1, 10, 167.5, 49.23, 1, 1, 10, 136, 53.97, 1, 1, 10, 101.33, 57.28, 1, 1, 10, 66.92, 63.23, 1, 1, 10, 37.41, 59.24, 1, 1, 10, 11.44, 48.3, 1, 1, 10, -4.98, 27.23, 1, 1, 10, -9.18, -0.03, 1, 1, 10, 1.95, -38.73, 1, 1, 10, 22.41, -60.94, 1, 1, 10, 56.01, -71.21, 1, 1, 10, 94.87, -78.43, 1, 2, 10, 127.64, -71.56, 0.99999, 11, -45.4, -99.71, 1e-05, 2, 10, 167.96, -62.52, 0.95037, 11, -12.57, -74.63, 0.04963, 2, 10, 186.7, -56.62, 0.80827, 11, 1.98, -61.43, 0.19173, 2, 10, 201.53, -54.91, 0.57289, 11, 14.74, -53.67, 0.42711], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56], "width": 185, "height": 277}}, "hand1": {"hand1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [174.9, 74.52, 179.92, -55.38, 69.01, -59.67, 63.98, 70.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 130, "height": 111}}, "body": {"body": {"type": "mesh", "uvs": [0.19502, 0.05468, 0.08747, 0.09792, 0.0675, 0.14266, 0.02448, 0.19038, 0, 0.25897, 0, 0.34248, 0.0242, 0.44937, 0.05828, 0.53782, 0.11052, 0.63177, 0.16429, 0.73167, 0.22882, 0.82711, 0.26415, 0.91509, 0.35537, 0.96034, 0.474, 0.98352, 0.56332, 0.99776, 0.66822, 1, 0.76337, 0.99477, 0.82938, 0.96951, 0.88139, 0.9403, 0.92479, 0.87035, 0.97703, 0.83904, 1, 0.78536, 0.99999, 0.73453, 0.99999, 0.68363, 0.95558, 0.6375, 0.96627, 0.56466, 0.96012, 0.49159, 0.91711, 0.44089, 0.90021, 0.39914, 0.90482, 0.31564, 0.89252, 0.19485, 0.83261, 0.09643, 0.72967, 0.02187, 0.59447, 0, 0.43008, 0.00696, 0.29334, 0.05021, 0.24751, 0.06919, 0.12386, 0.16018, 0.2443, 0.23081, 0.35344, 0.299, 0.41617, 0.36597, 0.5341, 0.45486, 0.6169, 0.53522, 0.6997, 0.62776, 0.7687, 0.70569, 0.84899, 0.79336, 0.33839, 0.09809, 0.43875, 0.15775, 0.53284, 0.23324, 0.62443, 0.31117, 0.71726, 0.40006, 0.80257, 0.49139, 0.88913, 0.58393, 0.10294, 0.47728, 0.22174, 0.5086, 0.30094, 0.54276, 0.3816, 0.61109, 0.47107, 0.67373, 0.5664, 0.70647, 0.6632, 0.71074, 0.39395, 0.70714, 0.42204, 0.82347, 0.452, 0.92707, 0.66924, 0.79802, 0.67111, 0.9198, 0.8434, 0.89617, 0.351, 0.46797], "triangles": [2, 1, 37, 3, 37, 4, 37, 3, 2, 38, 6, 5, 37, 5, 4, 63, 59, 44, 45, 24, 22, 24, 45, 44, 24, 44, 52, 22, 24, 23, 59, 43, 44, 59, 58, 43, 43, 58, 42, 44, 43, 52, 24, 52, 25, 43, 51, 52, 43, 42, 51, 52, 26, 25, 52, 27, 26, 52, 51, 27, 42, 50, 51, 42, 41, 50, 51, 28, 27, 51, 50, 28, 41, 49, 50, 28, 50, 29, 30, 29, 50, 50, 49, 30, 49, 48, 32, 49, 31, 30, 49, 32, 31, 32, 48, 33, 63, 58, 59, 65, 63, 45, 20, 45, 21, 63, 44, 45, 45, 22, 21, 37, 1, 0, 61, 57, 58, 9, 55, 56, 55, 9, 8, 60, 56, 57, 58, 57, 42, 42, 57, 41, 41, 40, 49, 40, 48, 49, 48, 47, 33, 47, 34, 33, 55, 8, 54, 8, 7, 54, 54, 7, 53, 55, 66, 56, 57, 56, 41, 56, 66, 41, 55, 54, 66, 7, 6, 53, 66, 54, 39, 53, 6, 38, 38, 5, 37, 38, 39, 54, 66, 40, 41, 66, 39, 40, 38, 54, 53, 40, 39, 48, 38, 46, 39, 39, 47, 48, 39, 46, 47, 38, 37, 36, 38, 36, 46, 36, 37, 0, 46, 34, 47, 36, 35, 46, 46, 35, 34, 58, 63, 61, 10, 60, 61, 10, 9, 60, 61, 60, 57, 9, 56, 60, 15, 64, 16, 15, 14, 64, 64, 14, 62, 17, 16, 65, 12, 62, 13, 64, 62, 63, 62, 14, 13, 17, 65, 18, 65, 16, 64, 62, 12, 61, 18, 65, 19, 12, 11, 61, 63, 62, 61, 64, 63, 65, 11, 10, 61, 65, 45, 19, 19, 45, 20], "vertices": [1, 15, -195.21, 90.3, 1, 1, 15, -230.7, 75.6, 1, 1, 15, -237.29, 60.39, 1, 1, 15, -251.48, 44.17, 1, 1, 15, -259.56, 20.84, 1, 1, 15, -259.56, -7.55, 1, 1, 15, -251.58, -43.89, 1, 1, 14, -59.25, -100.87, 1, 1, 14, -42.01, -132.81, 1, 3, 1, -169.52, 102.44, 0.30674, 14, -24.26, -166.78, 0.69323, 15, -205.35, -139.88, 3e-05, 3, 1, -148.23, 69.99, 0.56938, 14, -2.97, -199.22, 0.43032, 15, -184.05, -172.32, 0.0003, 3, 1, -136.57, 40.08, 0.75747, 14, 8.69, -229.14, 0.24079, 15, -172.39, -202.24, 0.00174, 3, 1, -106.46, 24.7, 0.85859, 14, 38.79, -244.52, 0.13438, 15, -142.29, -217.62, 0.00704, 3, 1, -67.31, 16.81, 0.87235, 14, 77.94, -252.4, 0.10552, 15, -103.14, -225.5, 0.02213, 3, 1, -37.84, 11.97, 0.88238, 14, 107.41, -257.24, 0.03881, 15, -73.67, -230.34, 0.07881, 3, 1, -3.22, 11.21, 0.81175, 14, 142.03, -258.01, 0.00858, 15, -39.05, -231.11, 0.17967, 3, 1, 28.18, 12.99, 0.8508, 14, 173.43, -256.23, 0.00201, 15, -7.65, -229.33, 0.14719, 3, 1, 49.96, 21.58, 0.80164, 14, 195.22, -247.64, 0.00024, 15, 14.13, -220.74, 0.19812, 3, 1, 67.12, 31.51, 0.7565, 14, 212.38, -237.71, 2e-05, 15, 31.3, -210.81, 0.24347, 3, 1, 81.44, 55.29, 0.56966, 14, 226.7, -213.93, 0.00207, 15, 45.62, -187.03, 0.42827, 2, 1, 98.68, 65.94, 0.48404, 15, 62.86, -176.38, 0.51596, 2, 1, 106.26, 84.19, 0.26967, 15, 70.44, -158.13, 0.73033, 1, 15, 70.43, -140.85, 1, 1, 15, 70.43, -123.54, 1, 1, 15, 55.78, -107.86, 1, 1, 15, 59.31, -83.09, 1, 1, 15, 57.28, -58.25, 1, 1, 15, 43.08, -41.01, 1, 1, 15, 37.51, -26.81, 1, 1, 15, 39.03, 1.58, 1, 1, 15, 34.97, 42.64, 1, 1, 15, 15.2, 76.11, 1, 1, 15, -18.77, 101.46, 1, 1, 15, -63.39, 108.89, 1, 1, 14, 63.45, 79.63, 1, 1, 14, 18.32, 64.92, 1, 2, 14, 3.2, 58.47, 0.45915, 15, -177.89, 85.37, 0.54085, 2, 14, -37.61, 27.53, 0.15615, 15, -218.69, 54.43, 0.84385, 2, 14, 2.14, 3.52, 0.30685, 15, -178.94, 30.42, 0.69315, 2, 14, 38.16, -19.67, 0.44887, 15, -142.93, 7.23, 0.55113, 2, 14, 58.86, -42.44, 0.58256, 15, -122.23, -15.54, 0.41744, 3, 1, -47.48, 196.56, 0.0196, 14, 97.77, -72.66, 0.39945, 15, -83.31, -45.76, 0.58095, 3, 1, -20.16, 169.24, 0.05285, 14, 125.1, -99.98, 0.24127, 15, -55.99, -73.08, 0.70588, 3, 1, 7.16, 137.77, 0.10888, 14, 152.42, -131.45, 0.12142, 15, -28.66, -104.55, 0.7697, 3, 1, 29.93, 111.28, 0.28203, 14, 175.19, -157.94, 0.10107, 15, -5.89, -131.04, 0.6169, 3, 1, 56.43, 81.47, 0.47292, 14, 201.69, -187.75, 0.00957, 15, 20.6, -160.85, 0.51751, 2, 14, 33.19, 48.64, 0.36844, 15, -147.89, 75.54, 0.63156, 2, 14, 66.31, 28.36, 0.26161, 15, -114.77, 55.26, 0.73839, 2, 14, 97.36, 2.69, 0.13668, 15, -83.72, 29.59, 0.86332, 1, 15, -53.5, 3.1, 1, 1, 15, -22.87, -27.13, 1, 1, 15, 5.29, -58.18, 1, 1, 15, 33.85, -89.64, 1, 3, 1, -189.77, 188.94, 0.01931, 14, -44.51, -80.28, 0.27085, 15, -225.59, -53.38, 0.70984, 3, 1, -150.56, 178.29, 0.04285, 14, -5.31, -90.93, 0.52087, 15, -186.39, -64.03, 0.43628, 3, 1, -124.43, 166.67, 0.07342, 14, 20.83, -102.55, 0.74038, 15, -160.25, -75.64, 0.1862, 3, 1, -97.81, 143.44, 0.18717, 14, 47.45, -125.78, 0.66801, 15, -133.63, -98.88, 0.14482, 3, 1, -68.28, 122.14, 0.19546, 14, 76.97, -147.07, 0.53664, 15, -104.11, -120.17, 0.2679, 3, 1, -36.82, 111.01, 0.22314, 14, 108.43, -158.21, 0.36715, 15, -72.65, -131.3, 0.40971, 3, 1, -4.88, 109.56, 0.26976, 14, 140.38, -159.66, 0.16348, 15, -40.7, -132.76, 0.56676, 3, 1, -93.73, 110.78, 0.30736, 14, 51.52, -158.43, 0.69234, 15, -129.56, -131.53, 0.0003, 3, 1, -84.46, 71.23, 0.57168, 14, 60.79, -197.99, 0.42659, 15, -120.29, -171.08, 0.00174, 3, 1, -74.58, 36.01, 0.76378, 14, 70.68, -233.21, 0.22918, 15, -110.4, -206.31, 0.00704, 3, 1, -2.89, 79.88, 0.31706, 14, 142.37, -189.33, 0.00033, 15, -38.71, -162.43, 0.68261, 3, 1, -2.27, 38.48, 0.59653, 14, 142.99, -230.74, 0.00202, 15, -38.1, -203.84, 0.40145, 3, 1, 54.59, 46.51, 0.6709, 14, 199.84, -222.71, 2e-05, 15, 18.76, -195.8, 0.32908, 1, 14, 37.35, -77.12, 1], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 4, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 38, 72, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 48, 12, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 88, 112, 120, 120, 122, 122, 124, 24, 26, 26, 28, 124, 26, 118, 126, 126, 128, 128, 30, 90, 130, 32, 34, 34, 36, 130, 34, 80, 132, 132, 110], "width": 330, "height": 340}}, "hand2": {"hand2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [131.89, 9.96, 46, -75.26, -34.3, 5.67, 51.6, 90.89], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 114}}, "leg2": {"leg2": {"type": "mesh", "uvs": [0, 0.38761, 0, 0.26625, 0.04084, 0.14275, 0.15892, 0.04694, 0.32639, 0, 0.53466, 0, 0.69354, 0.09378, 0.77942, 0.22579, 0.84948, 0.31998, 0.91767, 0.4347, 0.93645, 0.54056, 0.96258, 0.60761, 0.99999, 0.68589, 0.99052, 0.76487, 0.93381, 0.82955, 0.88463, 0.93268, 0.81378, 1, 0.66348, 1, 0.56042, 0.98378, 0.51679, 0.91348, 0.51903, 0.84241, 0.57092, 0.77261, 0.61995, 0.72051, 0.60119, 0.69958, 0.51913, 0.70307, 0.47928, 0.63564, 0.43004, 0.56473, 0.3726, 0.50428, 0.31399, 0.45081, 0.24482, 0.41826, 0.18035, 0.40779, 0.10884, 0.40198, 0.04905, 0.40663, 0.70213, 0.64929, 0.82556, 0.54602, 0.70448, 0.76134, 0.84824, 0.77544], "triangles": [15, 16, 35, 15, 35, 36, 35, 16, 17, 21, 17, 20, 17, 21, 35, 35, 21, 22, 17, 18, 19, 20, 17, 19, 15, 36, 14, 14, 36, 13, 36, 33, 34, 13, 36, 12, 11, 34, 10, 11, 36, 34, 12, 36, 11, 22, 33, 35, 36, 35, 33, 22, 23, 33, 24, 25, 23, 23, 25, 33, 34, 33, 26, 33, 25, 26, 34, 26, 7, 26, 27, 7, 34, 9, 10, 34, 8, 9, 34, 7, 8, 6, 27, 28, 27, 6, 7, 6, 28, 5, 5, 28, 4, 28, 29, 4, 4, 29, 3, 30, 2, 3, 29, 30, 3, 31, 32, 1, 30, 31, 1, 2, 30, 1, 1, 32, 0], "vertices": [1, 2, -58.38, -92.82, 1, 1, 2, -77.58, -70.92, 1, 1, 2, -89.8, -42.22, 1, 1, 2, -83.81, -6.4, 1, 1, 2, -61.26, 28.34, 1, 1, 2, -23.97, 61, 1, 1, 2, 19.3, 68.99, 1, 1, 2, 55.55, 58.63, 1, 1, 2, 82.99, 52.61, 1, 1, 2, 113.34, 42.6, 1, 1, 2, 133.45, 26.44, 1, 2, 2, 148.73, 18.43, 0.99917, 3, -20.18, -7.94, 0.00083, 2, 2, 167.81, 10.17, 0.46979, 3, -8.63, 9.35, 0.53021, 2, 2, 178.6, -5.58, 0.00114, 3, 8.79, 17.15, 0.99886, 1, 3, 29.04, 13.55, 1, 1, 3, 56.29, 16.22, 1, 1, 3, 78.82, 10.05, 1, 2, 2, 157.24, -99.31, 0.01653, 3, 97.19, -20.65, 0.98347, 2, 2, 136.23, -112.55, 0.04187, 3, 106.44, -43.69, 0.95813, 2, 2, 117.3, -106.7, 0.07407, 3, 97.3, -61.27, 0.92593, 2, 2, 106.46, -93.52, 0.12157, 3, 82.39, -69.57, 0.87843, 2, 2, 104.71, -72.78, 0.25592, 3, 61.68, -67.58, 0.74408, 2, 2, 105.25, -55.69, 0.55692, 3, 44.95, -63.99, 0.44308, 2, 2, 98.58, -54.85, 0.73975, 3, 42.94, -70.4, 0.26025, 2, 2, 84.44, -68.35, 0.8903, 3, 53.69, -86.73, 0.1097, 2, 2, 66.64, -62.43, 0.9429, 3, 44.67, -103.18, 0.0571, 2, 2, 46.61, -57.35, 0.98612, 3, 36.09, -121.97, 0.01388, 2, 2, 26.77, -55.45, 0.99882, 3, 30.66, -141.15, 0.00118, 1, 2, 7.82, -54.99, 1, 1, 2, -9.71, -59.96, 1, 1, 2, -22.91, -68.18, 1, 1, 2, -36.63, -78.35, 1, 1, 2, -46.59, -88.56, 1, 2, 2, 108.7, -29.94, 0.7943, 3, 20.24, -55.98, 0.2057, 1, 2, 114.46, 8.06, 1, 2, 2, 126.84, -49.8, 0.37867, 3, 43.03, -41.69, 0.62133, 2, 2, 154.8, -29.8, 0.12653, 3, 28.36, -10.6, 0.87347], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 46, 66, 66, 68, 16, 18, 68, 18, 18, 20, 20, 22, 44, 70, 70, 72, 72, 26], "width": 238, "height": 240}}, "dai1": {"dai1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43.33, 25.45, -39.61, -97.12, -158.04, -16.98, -75.1, 105.59], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 148, "height": 143}}, "dai2": {"dai2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-19.13, 17.01, -74.05, -64.16, -139.48, -19.88, -84.56, 61.28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 98, "height": 79}}, "foot1banner2": {"foot1banner2": {"type": "mesh", "uvs": [0.15027, 0, 0.14632, 0.11298, 0.15422, 0.31285, 0.1885, 0.50336, 0.24386, 0.66264, 0.29791, 0.82504, 0.37568, 0.93434, 0.49695, 1, 0.61295, 1, 0.71972, 0.96557, 0.7889, 0.84377, 0.84294, 0.69699, 0.92731, 0.58768, 1, 0.50024, 0.95894, 0.4409, 0.88381, 0.45964, 0.95235, 0.32222, 0.85744, 0.32534, 0.77044, 0.33159, 0.70717, 0.36282, 0.6439, 0.42528, 0.68476, 0.29411, 0.62676, 0.31285, 0.57799, 0.36907, 0.52394, 0.45651, 0.52262, 0.2785, 0.47912, 0.12234, 0.42376, 0.00367, 0.39212, 0, 0.38685, 0.12234, 0.3684, 0.21291, 0.34862, 0.26913, 0.32753, 0.13796, 0.28272, 0.04114, 0.22735, 0, 0.24053, 0.1161, 0.24713, 0.20667, 0.20231, 0.08174, 0.3539, 0.39717, 0.36972, 0.60642, 0.37631, 0.81566, 0.50417, 0.62203, 0.49626, 0.83753, 0.82317, 0.53459, 0.76121, 0.63765, 0.70585, 0.77194, 0.65049, 0.90936], "triangles": [8, 46, 9, 46, 8, 42, 6, 42, 7, 46, 42, 41, 42, 8, 7, 46, 45, 9, 9, 45, 10, 45, 46, 41, 41, 24, 20, 24, 23, 20, 6, 40, 42, 6, 5, 40, 45, 41, 20, 45, 44, 10, 10, 44, 11, 42, 40, 41, 39, 5, 4, 5, 39, 40, 39, 4, 38, 38, 36, 31, 31, 36, 32, 38, 3, 36, 40, 39, 41, 44, 20, 19, 44, 45, 20, 44, 43, 11, 12, 43, 15, 12, 11, 43, 38, 4, 3, 44, 18, 43, 44, 19, 18, 24, 38, 25, 39, 38, 24, 30, 26, 25, 30, 29, 26, 39, 24, 41, 30, 25, 38, 12, 14, 13, 12, 15, 14, 43, 17, 15, 43, 18, 17, 3, 2, 36, 15, 17, 16, 23, 22, 20, 20, 22, 21, 38, 31, 30, 2, 37, 36, 2, 1, 37, 36, 33, 32, 36, 35, 33, 29, 27, 26, 29, 28, 27, 35, 34, 33, 1, 0, 37], "vertices": [2, 6, 41.93, -3.43, 1, 7, 36.32, 47.09, 0, 1, 6, 35.8, 0.65, 1, 1, 6, 23.83, 5.86, 1, 1, 6, 10.44, 7.22, 1, 2, 6, -2.74, 4.76, 0.96646, 7, -3.32, 24.92, 0.03354, 3, 6, -16, 2.57, 0.45795, 7, -12.14, 14.78, 0.54161, 8, -29.35, 34.46, 0.00045, 3, 6, -28.01, -4.48, 0.08243, 7, -16.9, 1.7, 0.83083, 8, -28.1, 20.59, 0.08675, 3, 7, -17.62, -17.45, 0.42004, 8, -20.62, 2.95, 0.57884, 9, -35.92, 15.06, 0.00112, 3, 7, -14.3, -35, 0.02399, 8, -10.16, -11.53, 0.77764, 9, -22.69, 3.06, 0.19837, 2, 8, 1.28, -23.55, 0.15495, 9, -9, -6.32, 0.84505, 1, 9, 4.21, -7.61, 1, 1, 9, 16.78, -6.13, 1, 1, 9, 31.18, -9.59, 1, 1, 9, 43.29, -12.89, 1, 1, 9, 41.2, -5.79, 1, 2, 8, 42.73, -24.78, 0.01555, 9, 31.81, 1.08, 0.98445, 1, 9, 45.63, 0.61, 1, 2, 8, 47.43, -16.38, 0.06998, 9, 34.66, 10.27, 0.93002, 2, 8, 39.26, -5.76, 0.42479, 9, 24.46, 18.97, 0.57521, 2, 8, 31.91, 0.96, 0.82076, 9, 15.88, 24.01, 0.17924, 2, 7, 23.29, -32.74, 0.01999, 8, 22.91, 6.48, 0.98001, 2, 7, 32.84, -37.34, 0.00155, 8, 33.51, 6.37, 0.99845, 2, 7, 29.98, -28.79, 0.0273, 8, 27.29, 12.9, 0.9727, 2, 7, 25, -22.09, 0.13277, 8, 19.93, 16.85, 0.86723, 2, 7, 17.86, -14.97, 0.58947, 8, 10.45, 20.26, 0.41053, 2, 7, 29.2, -12.62, 0.95593, 8, 19.71, 27.2, 0.04407, 2, 7, 37.92, -4.15, 0.99943, 8, 24.02, 38.58, 0.00057, 1, 7, 43.92, 5.66, 1, 2, 6, 23.94, -36.04, 0.00033, 7, 43.25, 10.5, 0.99967, 2, 6, 17.37, -31.49, 0.02731, 7, 35.28, 9.82, 0.97269, 2, 6, 13.59, -26.15, 0.1457, 7, 28.97, 11.51, 0.8543, 2, 6, 11.86, -21.72, 0.4345, 7, 24.81, 13.83, 0.5655, 2, 6, 20.89, -23, 0.79324, 7, 32.59, 18.6, 0.20676, 2, 6, 29.74, -19.99, 0.88572, 7, 37.49, 26.55, 0.11428, 2, 6, 36.2, -13.82, 0.90602, 7, 38.53, 35.43, 0.09398, 2, 6, 28.61, -11.95, 0.89899, 7, 31.49, 32.03, 0.10101, 2, 6, 22.97, -10, 0.92515, 7, 25.9, 29.94, 0.07485, 2, 6, 33.41, -7.88, 0.99832, 7, 32.59, 38.23, 0.00168, 2, 6, 4.18, -18.41, 0.35907, 7, 16.79, 11.48, 0.64093, 2, 6, -8.91, -13.98, 0.19324, 7, 3.87, 6.56, 0.80676, 3, 6, -21.3, -8.29, 0.11826, 7, -9.3, 3.03, 0.84128, 8, -21.79, 25.03, 0.04046, 2, 7, 6.73, -13.97, 0.56806, 8, -0.06, 16.43, 0.43194, 2, 7, -7.26, -15.38, 0.47566, 8, -12.12, 9.22, 0.52434, 2, 8, 33.32, -20.06, 0.05627, 9, 21.62, 3.74, 0.94373, 2, 8, 22.3, -16.25, 0.15388, 9, 10.05, 5.18, 0.84612, 2, 8, 10.23, -14.45, 0.25113, 9, -2.13, 4.44, 0.74887, 3, 7, -7.44, -39.59, 0.00024, 8, -2, -12.77, 0.59801, 9, -14.45, 3.55, 0.40175], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 0, 74, 62, 76, 76, 78, 78, 80, 80, 12, 48, 82, 82, 84, 84, 14, 30, 86, 86, 88, 88, 90, 90, 92, 92, 16], "width": 154, "height": 65}}, "yaodai": {"yaodai": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [82.26, -46.79, -157.74, -46.79, -157.74, 53.21, 82.26, 53.21], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 240, "height": 100}}, "eye": {"eye": {"type": "mesh", "uvs": [0, 0.06544, 0.06364, 0.02032, 0.18196, 0.02677, 0.32116, 0.12988, 0.43716, 0.3361, 0.47196, 0.55521, 0.79444, 0.42632, 0.83981, 0.46944, 0.8826, 0.5101, 0.92878, 0.55409, 0.9638, 0.58744, 1, 0.70988, 0.96844, 0.8581, 0.91508, 1, 0.86172, 1, 0.8246, 0.81299, 0.79444, 0.58744, 0.45108, 0.64544, 0.35828, 0.74855, 0.21212, 0.76143, 0.09612, 0.63899, 0.02652, 0.41344], "triangles": [21, 0, 1, 16, 6, 7, 5, 6, 16, 2, 21, 1, 20, 21, 2, 17, 4, 5, 17, 5, 16, 18, 3, 4, 18, 4, 17, 19, 2, 3, 19, 3, 18, 20, 2, 19, 7, 8, 15, 7, 15, 16, 12, 10, 11, 13, 14, 9, 8, 14, 15, 14, 8, 9, 12, 9, 10, 12, 13, 9], "vertices": [-4.12, 59.35, -1.78, 55.01, 0.2, 46.36, 0.03, 35.56, -3.26, 25.77, -8.37, 21.8, 0.87, -0.82, 0.57, -4.4, 0.28, -7.78, -0.03, -11.43, -0.27, -14.2, -2.81, -17.63, -7.27, -16.31, -11.96, -13.35, -12.93, -9.47, -8.71, -5.55, -3.35, -1.87, -11.11, 22.73, -15.5, 28.81, -18.5, 39.36, -17.4, 48.6, -12.76, 55.14], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 38, 40, 40, 42, 0, 42, 2, 42, 4, 40, 6, 38, 36, 38, 36, 8, 34, 10, 32, 12, 16, 28, 12, 14, 14, 16, 30, 14, 16, 18, 18, 20, 26, 18, 24, 20], "width": 75, "height": 27}}, "hand22": {"hand22": {"type": "mesh", "uvs": [0, 0, 0, 0.05295, 0, 0.17009, 0, 0.27855, 0.05096, 0.43039, 0.13157, 0.57355, 0, 0.66466, 0.06562, 0.8165, 0.24148, 0.95098, 0.54925, 0.99871, 0.78373, 1, 0.91563, 0.8512, 1, 0.6126, 1, 0.46509, 0.88632, 0.34796, 0.68847, 0.21781, 0.54925, 0.07898, 0.36605, 0.02259, 0.19019, 0, 0.14622, 0.26553, 0.3514, 0.1484], "triangles": [10, 9, 11, 9, 8, 11, 7, 5, 8, 8, 5, 12, 11, 8, 12, 15, 4, 19, 15, 19, 20, 5, 13, 12, 14, 4, 15, 7, 6, 5, 20, 16, 15, 5, 14, 13, 14, 5, 4, 4, 3, 19, 3, 2, 19, 19, 2, 20, 2, 18, 20, 2, 1, 18, 20, 17, 16, 20, 18, 17, 1, 0, 18], "vertices": [2, 27, 61.83, -4.61, 0.9979, 28, -47.79, -28.08, 0.0021, 2, 27, 69.33, -12.16, 0.95489, 28, -37.7, -31.47, 0.04511, 2, 27, 85.91, -28.88, 0.51965, 28, -15.38, -38.96, 0.48035, 2, 27, 101.26, -44.35, 0.10461, 28, 5.28, -45.91, 0.89539, 1, 28, 36.15, -49.87, 1, 1, 28, 66.48, -49.94, 1, 1, 28, 78.85, -70.62, 1, 1, 28, 110.27, -72.93, 1, 1, 28, 142.56, -61.7, 1, 1, 28, 163.31, -30.04, 1, 1, 28, 172.45, -3.67, 1, 1, 28, 149.09, 20.74, 1, 2, 27, 233.03, -8.21, 0.00341, 28, 106.82, 45.52, 0.99659, 2, 27, 212.15, 12.84, 0.03545, 28, 78.72, 54.96, 0.96455, 2, 27, 185.96, 20.03, 0.13539, 28, 52.09, 49.64, 0.86461, 2, 27, 150.83, 22.02, 0.62849, 28, 19.8, 35.65, 0.37151, 1, 27, 119.41, 30.16, 1, 1, 27, 95.95, 22.85, 1, 1, 27, 77.9, 11.33, 1, 2, 27, 111.77, -30.24, 0.07356, 28, 8.35, -28.58, 0.92644, 1, 27, 112.52, 3.67, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 8, 38, 38, 40, 40, 32], "width": 119, "height": 201}}, "bandbanner1": {"bandbanner1": {"type": "mesh", "uvs": [0.03674, 0.7457, 0.01361, 0.64045, 0.00435, 0.51218, 0, 0.40693, 0, 0.2655, 0.01823, 0.15696, 0.07375, 0.06487, 0.11539, 0, 0.12233, 0.06487, 0.14083, 0.13723, 0.17322, 0.055, 0.24492, 0, 0.26343, 0.07803, 0.28888, 0.19314, 0.33977, 0.14381, 0.38603, 0.04842, 0.42767, 0.13723, 0.49938, 0.21617, 0.56183, 0.27866, 0.58728, 0.33457, 0.65436, 0.33786, 0.73763, 0.33786, 0.75151, 0.4168, 0.75383, 0.49245, 0.80934, 0.49245, 0.89262, 0.51547, 0.86717, 0.57467, 0.84404, 0.66348, 0.90881, 0.70295, 0.97358, 0.75557, 1, 0.80162, 1, 0.86082, 0.925, 0.89371, 0.83016, 0.94305, 0.7492, 1, 0.63354, 1, 0.4855, 0.99567, 0.34439, 0.93976, 0.17784, 0.86411, 0.08763, 0.80491, 0.28887, 0.31155, 0.29812, 0.43654, 0.31431, 0.59441, 0.32588, 0.749, 0.56876, 0.46614, 0.53638, 0.64704, 0.504, 0.79834, 0.5757, 0.89043, 0.66592, 0.80162, 0.76307, 0.72269], "triangles": [33, 34, 48, 36, 47, 35, 34, 35, 48, 35, 47, 48, 36, 46, 47, 36, 37, 46, 48, 49, 33, 33, 28, 32, 28, 49, 27, 28, 33, 49, 38, 43, 37, 37, 43, 46, 31, 32, 30, 32, 28, 29, 47, 46, 48, 43, 38, 42, 32, 29, 30, 42, 38, 39, 46, 45, 48, 49, 48, 45, 49, 45, 23, 22, 23, 20, 44, 23, 45, 23, 44, 20, 20, 21, 22, 46, 43, 45, 42, 39, 1, 43, 42, 45, 42, 1, 41, 40, 41, 9, 0, 1, 39, 27, 23, 24, 27, 49, 23, 27, 24, 26, 18, 44, 17, 42, 41, 44, 9, 41, 1, 4, 5, 9, 9, 1, 2, 17, 44, 41, 40, 16, 17, 40, 14, 16, 45, 42, 44, 26, 24, 25, 40, 9, 13, 9, 10, 12, 9, 2, 3, 44, 19, 20, 44, 18, 19, 17, 41, 40, 14, 40, 13, 9, 3, 4, 13, 9, 12, 5, 6, 9, 6, 8, 9, 14, 15, 16, 10, 11, 12, 6, 7, 8], "vertices": [2, 19, -11.76, 30.47, 0.99951, 20, -21.19, 67.02, 0.00049, 1, 19, 2.16, 32.78, 1, 1, 19, 18.66, 32.19, 1, 1, 19, 32.11, 31.11, 1, 1, 19, 50.04, 28.62, 1, 1, 19, 63.34, 23.41, 1, 1, 19, 73.62, 11.78, 1, 1, 19, 80.8, 3.13, 1, 1, 19, 72.4, 3.02, 1, 2, 19, 62.77, 0.97, 0.99701, 20, 58.64, 59.89, 0.00299, 2, 19, 72.38, -6.32, 0.98765, 20, 69.93, 55.63, 0.01235, 2, 19, 77.55, -20.22, 0.98188, 20, 78.84, 43.77, 0.01812, 2, 19, 67.19, -22.18, 0.95056, 20, 69.46, 38.96, 0.04944, 2, 19, 51.96, -24.73, 0.631, 20, 55.58, 32.18, 0.369, 2, 19, 56.94, -34.78, 0.24535, 20, 63.21, 23.96, 0.75465, 2, 19, 67.87, -44.8, 0.13177, 20, 76.53, 17.46, 0.86823, 2, 19, 55.57, -50.74, 0.10032, 20, 66.42, 8.27, 0.89968, 3, 19, 43.76, -62.27, 0.00592, 20, 58.38, -6.14, 0.98133, 21, 34.33, 67.78, 0.01275, 2, 20, 52.16, -18.57, 0.87863, 21, 38.93, 54.66, 0.12137, 2, 20, 45.77, -24.22, 0.70399, 21, 38.54, 46.15, 0.29601, 2, 20, 47.18, -36.36, 0.41426, 21, 48.23, 38.71, 0.58574, 2, 20, 49.43, -51.34, 0.27118, 21, 60.56, 29.9, 0.72882, 2, 20, 39.82, -55.35, 0.21854, 21, 56.75, 20.21, 0.78146, 2, 20, 30.31, -57.2, 0.09512, 21, 51.46, 12.08, 0.90488, 2, 20, 31.81, -67.2, 0.01159, 21, 59.68, 6.21, 0.98841, 2, 21, 70.3, -5, 0.99881, 22, 55.74, 41.4, 0.00119, 2, 21, 62.13, -8.47, 0.96352, 22, 49.3, 35.29, 0.03648, 2, 21, 52.1, -15.27, 0.63473, 22, 42.27, 25.41, 0.36527, 2, 21, 58.75, -26.24, 0.1712, 22, 52.33, 17.46, 0.8288, 2, 21, 64.43, -38.57, 0.02315, 22, 61.96, 7.89, 0.97685, 2, 21, 64.92, -46.16, 0.00366, 22, 65.07, 0.94, 0.99634, 1, 22, 63.09, -6.37, 1, 1, 22, 48.82, -6.88, 1, 1, 22, 30.51, -8.48, 1, 1, 22, 14.38, -11.68, 1, 3, 20, -37.2, -45.24, 0.00012, 21, -4.11, -28.06, 0.06401, 22, -5.94, -6.2, 0.93586, 4, 19, -54.72, -46.01, 0.02087, 20, -40.66, -18.51, 0.1698, 21, -25.72, -11.94, 0.62879, 22, -31.81, 1.36, 0.18053, 4, 19, -44.09, -21.56, 0.23589, 20, -37.41, 7.95, 0.48344, 21, -42.45, 8.81, 0.27435, 22, -54.74, 14.96, 0.00632, 3, 19, -30.31, 7.13, 0.81431, 20, -32.35, 39.37, 0.16797, 21, -61.49, 34.3, 0.01772, 3, 19, -20.54, 22.34, 0.97939, 20, -27.31, 56.73, 0.02024, 21, -70.45, 50.01, 0.00037, 2, 19, 36.95, -22.64, 0.59439, 20, 40.59, 29.92, 0.40561, 2, 19, 20.87, -22.1, 0.54116, 20, 25.03, 25.87, 0.45884, 2, 19, 0.45, -22.24, 0.42814, 20, 5.48, 19.95, 0.57186, 3, 19, -19.44, -21.59, 0.34985, 20, -13.77, 14.92, 0.59062, 21, -31, 30.63, 0.05953, 2, 20, 28.62, -23.4, 0.61574, 21, 26.01, 34.4, 0.38426, 2, 20, 4.84, -21.02, 0.43811, 21, 7.76, 18.99, 0.56189, 4, 19, -30.16, -52.83, 0.00512, 20, -15.18, -18.08, 0.24647, 21, -8.3, 6.65, 0.74066, 22, -21.98, 24.87, 0.00775, 4, 19, -43.64, -64.13, 0.00031, 20, -24.9, -32.74, 0.00985, 21, -4.53, -10.53, 0.64102, 22, -12.45, 10.08, 0.34882, 2, 21, 15.44, -10.82, 0.65541, 22, 6.36, 16.78, 0.34459, 2, 21, 35.7, -12.87, 0.6676, 22, 26.07, 21.93, 0.3324], "hull": 40, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 0, 78, 26, 80, 80, 82, 82, 84, 84, 86, 86, 74, 38, 88, 88, 90, 90, 92, 92, 72, 72, 94, 94, 96, 96, 98, 98, 54], "width": 182, "height": 128}}, "bandbanner2": {"bandbanner2": {"type": "mesh", "uvs": [0.13502, 0.06474, 0.04407, 0.17718, 0, 0.36271, 0, 0.55385, 0, 0.75624, 0, 0.86868, 0, 1, 0.05348, 0.93052, 0.09425, 0.85744, 0.11306, 1, 0.18833, 0.96425, 0.26359, 0.89117, 0.342, 0.76749, 0.4204, 0.82371, 0.49566, 0.8743, 0.58347, 0.94177, 0.62424, 0.81808, 0.67128, 0.67754, 0.76536, 0.72251, 0.86885, 0.76186, 0.95039, 0.76749, 0.94725, 0.63256, 0.9253, 0.48639, 1, 0.43017, 0.97861, 0.31773, 0.89708, 0.22778, 0.82808, 0.14907, 0.74968, 0.07599, 0.64933, 0.0029, 0.50193, 0, 0.41099, 0, 0.28868, 0, 0.12561, 0.67191, 0.12874, 0.47514, 0.13502, 0.23902, 0.31377, 0.16594, 0.33259, 0.34584, 0.34513, 0.53136, 0.64619, 0.50887, 0.59602, 0.32897, 0.53643, 0.14907], "triangles": [40, 29, 28, 35, 31, 30, 35, 34, 0, 35, 0, 31, 1, 0, 34, 39, 40, 28, 39, 28, 27, 40, 36, 35, 34, 35, 36, 34, 2, 1, 33, 34, 36, 33, 2, 34, 22, 25, 24, 22, 24, 23, 26, 38, 39, 26, 39, 27, 40, 37, 36, 33, 36, 37, 3, 2, 33, 32, 3, 33, 32, 33, 37, 17, 38, 18, 26, 22, 38, 25, 22, 26, 22, 18, 38, 22, 19, 18, 4, 3, 32, 21, 19, 22, 12, 32, 37, 19, 21, 20, 39, 38, 37, 16, 38, 17, 40, 35, 30, 40, 30, 29, 39, 37, 40, 38, 14, 13, 37, 38, 13, 12, 37, 13, 8, 4, 32, 5, 4, 8, 16, 14, 38, 11, 32, 12, 10, 8, 32, 7, 5, 8, 15, 14, 16, 11, 10, 32, 6, 5, 7, 9, 8, 10], "vertices": [2, 23, -8.1, -4.6, 0.98849, 24, -16.46, -35.87, 0.01151, 1, 23, 1.87, -17.42, 1, 1, 23, 17.42, -23.02, 1, 1, 23, 33.07, -22.13, 1, 1, 23, 49.64, -21.18, 1, 1, 23, 58.85, -20.66, 1, 1, 23, 69.6, -20.04, 1, 1, 23, 63.46, -12.52, 1, 2, 23, 57.13, -6.88, 0.9997, 24, 46.32, -53.74, 0.0003, 2, 23, 68.65, -3.45, 0.99888, 24, 58.32, -53.18, 0.00112, 2, 23, 65.09, 7.43, 0.9653, 24, 57.48, -41.76, 0.0347, 2, 23, 58.48, 18.13, 0.82699, 24, 53.63, -29.78, 0.17301, 2, 23, 47.69, 29.06, 0.36418, 24, 45.78, -16.59, 0.63582, 3, 23, 51.64, 40.83, 0.04065, 24, 52.44, -6.11, 0.95447, 25, 17.49, -60.99, 0.00488, 3, 23, 55.15, 52.11, 4e-05, 24, 58.55, 4, 0.94516, 25, 27.79, -55.2, 0.0548, 2, 24, 66.37, 15.67, 0.88869, 25, 40.34, -48.89, 0.11131, 2, 24, 57.5, 23.43, 0.79863, 25, 36.44, -37.77, 0.20137, 2, 24, 47.45, 32.35, 0.41996, 25, 32.08, -25.06, 0.58004, 2, 24, 53.63, 45.26, 0.06926, 25, 43.81, -16.85, 0.93074, 2, 24, 59.6, 59.62, 0.00122, 25, 56.08, -7.29, 0.99878, 1, 25, 64.15, 1.58, 1, 1, 25, 55.39, 8.35, 1, 1, 25, 44.14, 13.6, 1, 1, 25, 47.69, 24.97, 1, 1, 25, 38.61, 28.51, 1, 1, 25, 25.25, 24.09, 1, 1, 25, 13.78, 20.49, 1, 1, 25, 1.77, 15.53, 1, 2, 24, -7.52, 39.37, 0.07672, 25, -12.31, 8.11, 0.92328, 2, 24, -11.74, 18.12, 0.65122, 25, -26.45, -8.31, 0.34878, 3, 23, -15.72, 35.6, 0.02884, 24, -14.21, 4.98, 0.90115, 25, -35.06, -18.54, 0.07001, 2, 23, -14.69, 17.65, 0.4088, 24, -17.52, -12.69, 0.5912, 1, 23, 41.68, -3.14, 1, 1, 23, 25.55, -3.6, 1, 1, 23, 6.17, -3.79, 1, 2, 23, -1.32, 22.11, 0.31085, 24, -3.47, -11.57, 0.68915, 2, 23, 13.25, 25.71, 0.2438, 24, 11.54, -11.57, 0.7562, 2, 23, 28.34, 28.42, 0.24321, 24, 26.84, -12.57, 0.75679, 2, 24, 33.18, 31.27, 0.34388, 25, 19.13, -18.97, 0.65612, 2, 24, 17.32, 26.74, 0.35303, 25, 3.09, -15.11, 0.64697, 2, 24, 1.21, 20.85, 0.52151, 25, -13.84, -12.3, 0.47849], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 16, 64, 64, 66, 66, 68, 68, 0, 62, 70, 70, 72, 72, 74, 74, 24, 34, 76, 76, 78, 78, 80, 80, 58], "width": 147, "height": 82}}, "mouth": {"mouth": {"type": "mesh", "uvs": [0.44882, 0.42242, 0.37023, 0.4319, 0.29163, 0.47457, 0.19775, 0.51565, 0.05366, 0.54725, 0, 0.59939, 0.08204, 0.69104, 0.2174, 0.80955, 0.35058, 0.9012, 0.49249, 0.96282, 0.65788, 1, 0.80852, 1, 0.87402, 0.94734, 0.90022, 0.87939, 1, 0.82725, 1, 0.75456, 0.99628, 0.67082, 0.90677, 0.66766, 0.81944, 0.6645, 0.75394, 0.6803, 0.74521, 0.62499, 0.64042, 0.65502, 0.67753, 0.55863, 0.6688, 0.43064, 0.6557, 0.27895, 0.58584, 0.11146, 0.52034, 0.01981, 0.47231, 0, 0.39808, 0, 0.44611, 0.05457, 0.45921, 0.13358, 0.46576, 0.26631, 0.5553, 0.69767, 0.47015, 0.70241, 0.39592, 0.66449, 0.40902, 0.57442, 0.43959, 0.48435, 0.65791, 0.72295, 0.74523, 0.74349, 0.84785, 0.75771, 0.94172, 0.76403, 0.27803, 0.60128, 0.29331, 0.68661, 0.36972, 0.77509, 0.50945, 0.83356, 0.68629, 0.86042, 0.35444, 0.69925, 0.43304, 0.75455, 0.56184, 0.78458, 0.73432, 0.80354, 0.90679, 0.82566], "triangles": [29, 28, 27, 29, 27, 26, 29, 26, 25, 30, 29, 25, 31, 30, 25, 31, 25, 24, 0, 31, 24, 0, 24, 23, 36, 1, 0, 36, 2, 1, 35, 2, 36, 41, 2, 35, 36, 23, 22, 23, 36, 0, 22, 35, 36, 21, 35, 22, 34, 41, 35, 35, 33, 34, 21, 20, 19, 46, 42, 41, 6, 41, 42, 21, 32, 35, 34, 46, 41, 32, 33, 35, 37, 21, 19, 32, 21, 37, 38, 37, 19, 33, 46, 34, 47, 46, 33, 39, 18, 17, 19, 18, 39, 38, 19, 39, 40, 17, 16, 40, 16, 15, 39, 17, 40, 43, 46, 47, 42, 46, 43, 48, 32, 37, 33, 32, 48, 47, 33, 48, 49, 37, 38, 49, 38, 39, 48, 37, 49, 7, 6, 42, 7, 42, 43, 50, 39, 40, 49, 39, 50, 40, 15, 14, 50, 40, 14, 44, 47, 48, 43, 47, 44, 45, 48, 49, 44, 48, 45, 13, 49, 50, 45, 49, 13, 13, 50, 14, 8, 7, 43, 8, 43, 44, 12, 45, 13, 9, 8, 44, 9, 44, 45, 10, 9, 45, 11, 10, 45, 12, 11, 45, 41, 3, 2, 41, 6, 4, 41, 4, 3, 5, 4, 6], "vertices": [1, 44, 0.18, 55.36, 1, 2, 43, 45.49, -19.23, 0.32948, 44, -6.17, 47.07, 0.67052, 2, 43, 33, -16.14, 0.66281, 44, -8.42, 34.4, 0.33719, 1, 43, 19.46, -11.32, 1, 1, 43, 3.09, -0.32, 1, 1, 43, -8.67, -0.84, 1, 2, 43, -14.72, -19.66, 0.82897, 44, -1.72, -12.97, 0.17103, 2, 43, -20.12, -46.96, 0.66503, 44, 25.88, -16.54, 0.33497, 2, 43, -21.93, -70.97, 0.51414, 44, 49.96, -16.74, 0.48586, 2, 43, -18.81, -92.42, 0.3802, 44, 71.15, -12.19, 0.6198, 2, 43, -10.32, -113.46, 0.2645, 44, 91.58, -2.31, 0.7355, 2, 43, 2.17, -128.74, 0.16555, 44, 105.99, 11.16, 0.83445, 2, 43, 14.98, -129.36, 0.07934, 44, 105.75, 23.99, 0.92066, 1, 44, 99.86, 35.31, 1, 1, 44, 102.96, 51.13, 1, 1, 44, 93.98, 60.75, 1, 1, 44, 83.27, 71.49, 1, 1, 44, 74.32, 63.9, 1, 1, 44, 65.57, 56.5, 1, 1, 44, 61.25, 48.55, 1, 1, 44, 53.58, 55.08, 1, 1, 44, 47.27, 41.74, 1, 1, 44, 38.9, 57.81, 1, 1, 44, 22.25, 73.95, 1, 1, 44, 2.25, 92.83, 1, 1, 44, -25.14, 108.73, 1, 1, 44, -42.74, 114.99, 1, 1, 44, -49.78, 113.31, 1, 1, 44, -56.89, 106.67, 1, 1, 44, -45.55, 103.75, 1, 1, 44, -34.53, 94.48, 1, 1, 44, -17.49, 77.51, 1, 1, 44, 44.39, 28.49, 1, 1, 44, 36.83, 20.24, 1, 1, 44, 25.04, 18.62, 1, 1, 44, 15.16, 31.7, 1, 1, 44, 6.95, 46.34, 1, 1, 44, 57.34, 34.32, 1, 1, 44, 68.23, 39.42, 1, 1, 44, 79.81, 46.72, 1, 1, 44, 89.57, 54.28, 1, 2, 43, 14.1, -29.27, 0.504, 44, 5.94, 16.43, 0.496, 2, 43, 3.41, -40.59, 0.37954, 44, 17.95, 6.51, 0.62046, 2, 43, -2.66, -58.47, 0.26414, 44, 36.2, 1.65, 0.73586, 2, 43, 0.72, -79.34, 0.16335, 44, 56.8, 6.42, 0.83665, 2, 43, 11.6, -100.36, 0.07717, 44, 77.04, 18.68, 0.92283, 2, 43, 6.7, -48.24, 0.37954, 44, 25.36, 10.31, 0.62046, 2, 43, 5.46, -62.54, 0.26414, 44, 39.72, 10.03, 0.73586, 2, 43, 11.93, -79.05, 0.16335, 44, 55.76, 17.58, 0.83665, 2, 43, 23.56, -98.72, 0.07717, 44, 74.61, 30.5, 0.92283, 1, 44, 93.85, 43.01, 1], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 42, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 0, 64, 74, 74, 76, 76, 78, 78, 80, 80, 30, 82, 84, 84, 86, 86, 88, 88, 90, 90, 26, 82, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 28], "width": 131, "height": 181}}, "mao1": {"mao1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-27.13, -1.34, 164.43, 197.35, 360.97, 7.87, 169.41, -190.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 276, "height": 273}}, "mao2": {"mao2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-12.77, -5.61, 114.47, 176.3, 373.41, -4.81, 246.17, -186.73], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 222, "height": 316}}, "mao3": {"mao3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-16.27, -8.62, 57.98, 153.16, 370.63, 9.66, 296.37, -152.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 178, "height": 344}}, "arm2": {"arm2": {"type": "mesh", "uvs": [0.5587, 0.35222, 0.53968, 0.22655, 0.46901, 0.12141, 0.38475, 0.05473, 0.3168, 0.00601, 0.1945, 0, 0.1048, 0.02652, 0.02327, 0.10602, 0, 0.22912, 0, 0.40094, 0.06404, 0.5343, 0.16732, 0.62149, 0.28962, 0.71638, 0.37388, 0.77023, 0.46629, 0.87794, 0.58045, 0.98309, 0.70547, 1, 0.86039, 0.98052, 0.9528, 0.89333, 1, 0.79331, 1, 0.67022, 0.96368, 0.58046, 0.9066, 0.51891, 0.79788, 0.50609, 0.68916, 0.46505, 0.61578, 0.42402], "triangles": [17, 16, 18, 23, 18, 16, 23, 14, 24, 14, 16, 15, 20, 18, 23, 16, 14, 23, 19, 18, 20, 23, 21, 20, 24, 13, 25, 24, 14, 13, 21, 23, 22, 13, 12, 25, 12, 0, 25, 12, 11, 0, 11, 10, 0, 0, 10, 1, 1, 10, 2, 2, 10, 3, 4, 3, 5, 5, 3, 6, 3, 9, 6, 3, 10, 9, 7, 6, 8, 6, 9, 8], "vertices": [1, 26, 89.83, 29.63, 1, 1, 26, 63.95, 47.54, 1, 1, 26, 33.58, 53.14, 1, 1, 26, 8.05, 49.91, 1, 1, 26, -11.62, 46.46, 1, 1, 26, -32.17, 26.48, 1, 1, 26, -41.61, 6.62, 1, 1, 26, -40.11, -20.78, 1, 1, 26, -21.42, -45.52, 1, 1, 26, 9.84, -74.48, 1, 2, 26, 44.28, -85.97, 0.99777, 27, -162.98, -79.04, 0.00223, 2, 26, 76.57, -82.94, 0.97919, 27, -130.59, -77.37, 0.02081, 2, 26, 113.28, -77.94, 0.89257, 27, -93.7, -73.92, 0.10743, 2, 26, 136.48, -72.56, 0.74341, 27, -70.3, -69.51, 0.25659, 2, 26, 170.77, -74.85, 0.37396, 27, -36.13, -73.25, 0.62604, 2, 26, 208.05, -72.98, 0.06812, 27, 1.2, -72.94, 0.93188, 2, 26, 231.01, -54.37, 0.00124, 27, 24.92, -55.32, 0.99876, 1, 27, 47.25, -26.36, 1, 2, 26, 250.94, 6.06, 0.00471, 27, 47.37, 4.23, 0.99529, 2, 26, 240.26, 31.02, 0.23353, 27, 37.74, 29.61, 0.76647, 2, 26, 217.86, 51.77, 0.7148, 27, 16.24, 51.28, 0.2852, 2, 26, 195.76, 60.67, 0.93129, 27, -5.47, 61.1, 0.06871, 2, 26, 175.48, 61.24, 0.99132, 27, -25.7, 62.53, 0.00868, 1, 26, 155.86, 44.74, 1, 1, 26, 131.11, 33, 1, 1, 26, 111.97, 27.32, 1], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50], "width": 234, "height": 248}}, "foot1": {"foot1": {"type": "mesh", "uvs": [0.48771, 0, 0.51849, 0, 0.57127, 0.04348, 0.65482, 0.10094, 0.76917, 0.13074, 0.87911, 0.135, 1, 0.10975, 0.99599, 0.14434, 0.91477, 0.19465, 0.85792, 0.23395, 0.78807, 0.30627, 0.74422, 0.36759, 0.73154, 0.42877, 0.7235, 0.52015, 0.71139, 0.61087, 0.68574, 0.70561, 0.68249, 0.75749, 0.71336, 0.8251, 0.71336, 0.91157, 0.67437, 0.97131, 0.53956, 0.99175, 0.40799, 0.98861, 0.27317, 0.96659, 0.16596, 0.94616, 0.07825, 0.92572, 0, 0.89427, 0, 0.8251, 0.05389, 0.74334, 0.13835, 0.67259, 0.21794, 0.61757, 0.26889, 0.53027, 0.31916, 0.46394, 0.37601, 0.37396, 0.41448, 0.30313, 0.44859, 0.2371, 0.47133, 0.13176, 0.46971, 0.04686, 0.32595, 0.56774, 0.43481, 0.54702, 0.55259, 0.55738, 0.62754, 0.61093, 0.64181, 0.69383, 0.48299, 0.30174, 0.57579, 0.34493, 0.67215, 0.36738, 0.41696, 0.39847, 0.5276, 0.4192, 0.63289, 0.45969], "triangles": [20, 18, 19, 22, 37, 21, 41, 16, 20, 40, 41, 39, 39, 41, 38, 38, 41, 21, 21, 37, 38, 17, 18, 20, 41, 20, 21, 22, 28, 29, 22, 29, 37, 23, 27, 28, 22, 23, 28, 24, 26, 27, 23, 24, 27, 17, 20, 16, 24, 25, 26, 16, 41, 15, 15, 41, 14, 41, 40, 14, 29, 30, 37, 14, 40, 13, 47, 13, 40, 47, 40, 39, 38, 37, 31, 38, 46, 39, 39, 46, 47, 37, 30, 31, 31, 45, 38, 38, 45, 46, 13, 47, 12, 31, 32, 45, 46, 43, 47, 47, 44, 12, 47, 43, 44, 12, 44, 11, 45, 42, 46, 46, 42, 43, 32, 33, 45, 45, 33, 42, 11, 44, 10, 10, 44, 3, 3, 43, 42, 2, 36, 1, 36, 0, 1, 10, 3, 4, 3, 44, 43, 3, 42, 2, 10, 4, 9, 33, 34, 42, 34, 35, 42, 2, 42, 35, 9, 5, 8, 9, 4, 5, 8, 5, 7, 7, 5, 6, 2, 35, 36], "vertices": [2, 11, 73.79, -44.56, 0.99987, 12, -67.65, -9.87, 0.00013, 1, 11, 70.15, -40.35, 1, 2, 11, 70.07, -27.8, 0.99937, 12, -62.03, 6.36, 0.00063, 1, 11, 68.33, -9.33, 1, 1, 11, 59.03, 9.98, 1, 1, 11, 46.64, 25.57, 1, 1, 11, 28.77, 39.06, 1, 1, 11, 34.14, 42.73, 1, 1, 11, 50.87, 37.74, 1, 1, 11, 63.15, 34.75, 1, 2, 11, 81.65, 34.01, 0.98635, 12, -19.78, 52.94, 0.01365, 2, 11, 95.52, 35.49, 0.89344, 12, -7.19, 46.93, 0.10656, 2, 11, 105.68, 41.22, 0.70158, 12, 4.47, 46.49, 0.29842, 2, 11, 119.57, 51.28, 0.38404, 12, 21.57, 47.78, 0.61596, 2, 11, 133.85, 60.7, 0.1669, 12, 38.67, 48.32, 0.8331, 2, 11, 150.3, 68.75, 0.04593, 12, 56.9, 46.56, 0.95407, 2, 11, 158.03, 74.65, 0.01516, 12, 66.57, 47.53, 0.98484, 2, 11, 163.96, 87.13, 0.00191, 12, 78.16, 55.06, 0.99809, 1, 12, 94.12, 57.64, 1, 1, 12, 106.28, 52.45, 1, 1, 12, 113.94, 28.97, 1, 1, 12, 117.16, 5.37, 1, 1, 12, 116.98, -19.38, 1, 1, 12, 116.31, -39.14, 1, 1, 12, 115.06, -55.43, 1, 1, 12, 111.52, -70.35, 1, 1, 12, 98.75, -72.41, 1, 1, 12, 82.1, -65.22, 1, 1, 12, 66.6, -52.24, 1, 1, 12, 54.15, -39.65, 1, 1, 12, 36.56, -33.15, 1, 1, 12, 22.86, -26.15, 1, 1, 12, 4.61, -18.67, 1, 2, 11, 125.37, -17.58, 0.06768, 12, -9.57, -13.91, 0.93232, 2, 11, 111.99, -20.97, 0.59505, 12, -22.75, -9.79, 0.40495, 2, 11, 94.38, -30.72, 0.94494, 12, -42.85, -8.87, 0.05506, 2, 11, 82.55, -41.31, 0.99518, 12, -58.48, -11.69, 0.00482, 1, 12, 41.83, -21.84, 1, 1, 12, 34.86, -3.01, 1, 2, 11, 145.05, 32.4, 0.09175, 12, 33.38, 18.35, 0.90825, 2, 11, 143.77, 49.21, 0.11661, 12, 41.1, 33.34, 0.88339, 2, 11, 153.82, 61.3, 0.04509, 12, 55.99, 38.36, 0.95491, 2, 11, 117.08, -8.36, 0.47522, 12, -11.81, -1.71, 0.52478, 2, 11, 112.22, 9.63, 0.95971, 12, -6.51, 16.16, 0.04029, 2, 11, 104.01, 25.58, 0.86891, 12, -5.15, 34.04, 0.13109, 1, 12, 7.95, -10.63, 1, 2, 11, 128.43, 12.1, 0.36579, 12, 8.59, 9.76, 0.63421, 2, 11, 121.72, 31.48, 0.50473, 12, 13.02, 29.78, 0.49527], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 58, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 32, 68, 84, 84, 86, 86, 88, 88, 22, 62, 90, 90, 92, 92, 94, 24, 26, 94, 26, 26, 28], "width": 181, "height": 187}}, "ear": {"ear": {"type": "mesh", "uvs": [0.01716, 0.03877, 0.12775, 0.13074, 0.23629, 0.24418, 0.29973, 0.36543, 0.3147, 0.50199, 0.35245, 0.63549, 0.26314, 0.72927, 0.37874, 0.87061, 0.42889, 0.88846, 0.54108, 0.99494, 0.6182, 0.94446, 0.77404, 0.99831, 0.84973, 0.97665, 0.88524, 0.89549, 0.89887, 0.80219, 0.9719, 0.70833, 0.99999, 0.5985, 0.98413, 0.50984, 0.8915, 0.41819, 0.82198, 0.31372, 0.73434, 0.22207, 0.65724, 0.14488, 0.57387, 0.09185, 0.45198, 0.05164, 0.3132, 0.02268, 0.17135, 0, 0.05572, 0, 0.43835, 0.59171, 0.52, 0.7208, 0.55036, 0.75746, 0.65778, 0.87924], "triangles": [21, 3, 22, 1, 26, 25, 0, 26, 1, 24, 1, 25, 2, 1, 24, 23, 3, 2, 23, 2, 24, 22, 3, 23, 27, 4, 3, 18, 28, 27, 28, 7, 5, 6, 5, 7, 18, 27, 19, 20, 3, 21, 27, 20, 19, 20, 27, 3, 28, 5, 27, 5, 4, 27, 8, 7, 28, 29, 28, 18, 18, 14, 29, 30, 29, 14, 29, 8, 28, 10, 8, 29, 30, 10, 29, 9, 8, 10, 10, 30, 11, 16, 15, 18, 15, 14, 18, 13, 30, 14, 12, 11, 13, 13, 11, 30, 16, 18, 17], "vertices": [4, 43, 58.37, 211.59, 2e-05, 46, 163.11, 57.79, 0.00178, 47, 137.34, 29.89, 0.10257, 48, 88.55, 9.29, 0.89563, 4, 43, 58.65, 187.97, 2e-05, 46, 141.26, 48.82, 0.00667, 47, 114.11, 25.66, 0.17064, 48, 64.96, 10.44, 0.82267, 4, 43, 56.06, 162.45, 0.00022, 46, 116.59, 41.83, 0.02109, 47, 88.51, 23.95, 0.27451, 48, 39.64, 14.58, 0.70418, 4, 43, 47.73, 141.98, 0.00137, 46, 94.48, 42.02, 0.05413, 47, 66.93, 28.73, 0.3958, 48, 19.71, 24.13, 0.5487, 4, 43, 32.39, 126.22, 0.00598, 46, 74.18, 50.47, 0.11593, 47, 48.83, 41.22, 0.50629, 48, 4.91, 40.41, 0.3718, 4, 43, 19.84, 107.83, 0.02017, 46, 52.45, 55.37, 0.21083, 47, 28.59, 50.52, 0.57555, 48, -12.68, 54.06, 0.19344, 4, 43, -1.22, 109.89, 0.0414, 46, 46.61, 75.7, 0.27364, 47, 27.1, 71.63, 0.56911, 48, -9.35, 74.95, 0.11585, 4, 43, -6.53, 80.63, 0.09336, 46, 17.45, 69.85, 0.34161, 47, -2.63, 71.97, 0.504, 48, -38.23, 82.02, 0.06103, 4, 43, -3.44, 72.34, 0.18876, 46, 10.88, 63.92, 0.38225, 47, -10.29, 67.54, 0.40117, 48, -46.69, 79.44, 0.02782, 4, 43, -4.78, 47.05, 0.33755, 46, -13.12, 55.85, 0.36297, 47, -35.44, 64.63, 0.28848, 48, -71.85, 82.32, 0.011, 4, 43, 9.62, 42.18, 0.54019, 46, -12.34, 40.67, 0.26903, 47, -37.83, 49.62, 0.18617, 48, -77.58, 68.24, 0.00461, 1, 43, 19.41, 16.57, 1, 1, 43, 30.09, 8.98, 1, 1, 43, 43.89, 12.6, 1, 1, 43, 56.9, 20.28, 1, 1, 43, 76.24, 20.33, 1, 1, 43, 92.82, 27.81, 1, 1, 43, 102.13, 38.84, 1, 4, 43, 103.71, 60.09, 0.70697, 46, 38.99, -40.19, 0.03705, 47, -4.44, -40.15, 0.18312, 48, -65.42, -26.76, 0.07285, 4, 43, 109.31, 79.66, 0.451, 46, 59.25, -38.19, 0.06114, 47, 15.79, -42.4, 0.32432, 48, -46.23, -33.54, 0.16354, 4, 43, 111.41, 100.27, 0.25616, 46, 79.18, -32.54, 0.0663, 47, 36.46, -41.02, 0.39587, 48, -25.79, -36.89, 0.28166, 4, 43, 112.83, 118.06, 0.12762, 46, 96.24, -27.31, 0.05568, 47, 54.23, -39.45, 0.39271, 48, -8.12, -39.39, 0.42399, 4, 43, 110.59, 134.21, 0.05477, 46, 110.43, -19.28, 0.03785, 47, 69.78, -34.54, 0.33178, 48, 8.13, -38.14, 0.57561, 4, 43, 102.7, 154.04, 0.01976, 46, 125.96, -4.63, 0.02103, 47, 88.02, -23.44, 0.2431, 48, 28.41, -31.46, 0.7161, 4, 43, 91.63, 174.92, 0.00579, 46, 141.29, 13.35, 0.00948, 47, 106.75, -9.04, 0.15762, 48, 49.93, -21.68, 0.82711, 4, 43, 79.46, 195.57, 0.0013, 46, 155.99, 32.28, 0.00341, 47, 125.07, 6.42, 0.09837, 48, 71.27, -10.79, 0.89692, 4, 43, 67.24, 210.52, 0.0002, 46, 165.39, 49.15, 0.00119, 47, 137.77, 20.96, 0.07828, 48, 86.94, 0.5, 0.92033, 4, 43, 34.33, 101.15, 0.0414, 46, 51.59, 39.43, 0.27364, 47, 24.43, 35.11, 0.56911, 48, -20.23, 39.99, 0.11585, 4, 43, 26.96, 77.52, 0.09336, 46, 26.91, 37.57, 0.34161, 47, -0.09, 38.43, 0.504, 48, -43.37, 48.79, 0.06103, 4, 43, 25.63, 69.88, 0.18876, 46, 19.31, 36, 0.38225, 47, -7.85, 38.47, 0.40117, 48, -50.91, 50.58, 0.02782, 4, 43, 21.88, 43.66, 0.33755, 46, -6.44, 29.81, 0.36297, 47, -34.32, 37.78, 0.28848, 48, -76.85, 55.91, 0.011], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 10, 54, 54, 56, 56, 58, 58, 60, 60, 20, 22, 24, 24, 26, 26, 28, 28, 30], "width": 167, "height": 160}}, "tooth": {"tooth": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [136.64, 100.05, 78.2, 45.41, 0.35, 128.69, 58.79, 183.33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 114}}}}], "animations": {"animation": {"bones": {"yaodai": {"translate": [{"y": -6.29, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 6.29, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": -6.29, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": 6.29, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": -6.29, "curve": 0.25, "c3": 0.75}, {"time": 5, "y": 6.29, "curve": 0.25, "c3": 0.75}, {"time": 6, "y": -6.29}]}, "body": {"rotate": [{"angle": 0.09, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 3.85, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 2, "angle": 0.09, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "angle": 3.85, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 4, "angle": 0.09, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 4.0667, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": 3.85, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 6, "angle": 0.09}], "translate": [{"x": 12.15, "y": -11.81, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "x": 12.3, "y": -12.3, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 5.52, "y": 9.39, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 2, "x": 12.15, "y": -11.81, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 2.0667, "x": 12.3, "y": -12.3, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "x": 5.52, "y": 9.39, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 4, "x": 12.15, "y": -11.81, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 4.0667, "x": 12.3, "y": -12.3, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "x": 5.52, "y": 9.39, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 6, "x": 12.15, "y": -11.81}]}, "head": {"rotate": [{"angle": 0.33, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 4.8, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": 0.33, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 4.8, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "angle": 0.33, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.1333, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": 4.8, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 6, "angle": 0.33}], "translate": [{"x": 0.65, "y": 0.36, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 9.4, "y": 5.23, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "x": 0.65, "y": 0.36, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 9.4, "y": 5.23, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "x": 0.65, "y": 0.36, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.1333, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "x": 9.4, "y": 5.23, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 6, "x": 0.65, "y": 0.36}]}, "mouth": {"rotate": [{"angle": -2.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -3.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -0.49, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -2.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.2, "angle": -3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": -0.49, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4, "angle": -2.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.2, "angle": -3.2, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": -0.49, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6, "angle": -2.84}], "translate": [{"x": -5.26, "y": -2.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "x": -6.05, "y": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "x": -5.26, "y": -2.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.2, "x": -6.05, "y": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4, "x": -5.26, "y": -2.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.2, "x": -6.05, "y": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6, "x": -5.26, "y": -2.48}]}, "arm1": {"translate": [{"x": -3.12, "y": -3.64, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "x": -3.61, "y": -5.15, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 3.37, "y": 16.53, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "x": -3.12, "y": -3.64, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.1333, "x": -3.61, "y": -5.15, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 3.37, "y": 16.53, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "x": -3.12, "y": -3.64, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.1333, "x": -3.61, "y": -5.15, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "x": 3.37, "y": 16.53, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 6, "x": -3.12, "y": -3.64}]}, "hand1": {"rotate": [{"angle": 8.42, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": 13.35, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -10.99, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": 8.42, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.2667, "angle": 13.35, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -10.99, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4, "angle": 8.42, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.2667, "angle": 13.35, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": -10.99, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 6, "angle": 8.42}]}, "hand11": {"rotate": [{"angle": -7.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -12.35, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.46, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -7.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -12.35, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 5.46, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -7.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": -12.35, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 5.46, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -7.3}]}, "arm3": {"rotate": [{"angle": -16.54, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -18.07, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -7.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.2, "angle": -6.31, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -16.54, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.2, "angle": -18.07, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3, "angle": -7.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.2, "angle": -6.31, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4, "angle": -16.54, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.2, "angle": -18.07, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "angle": -7.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.2, "angle": -6.31, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6, "angle": -16.54}]}, "arm6": {"rotate": [{"angle": -3.43, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": -8.43, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 11.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 1.2667, "angle": 16.22, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -3.43, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.2667, "angle": -8.43, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 3, "angle": 11.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 3.2667, "angle": 16.22, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4, "angle": -3.43, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.2667, "angle": -8.43, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 5, "angle": 11.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 5.2667, "angle": 16.22, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 6, "angle": -3.43}]}, "hand22": {"rotate": [{"angle": 15.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 8.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": 25.69, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.3333, "angle": 32.69, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 15.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 8.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3, "angle": 25.69, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.3333, "angle": 32.69, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 15.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": 8.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 5, "angle": 25.69, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 5.3333, "angle": 32.69, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 15.04}]}, "dai1": {"rotate": [{"angle": -2.87, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 7.74, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -2.87, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.2, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": 7.74, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4, "angle": -2.87, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.2, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 7.74, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6, "angle": -2.87}], "translate": [{"x": 22.05, "y": 7.58, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "x": 25.58, "y": 8.36, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": -1.53, "y": 2.39, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "x": 22.05, "y": 7.58, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.2, "x": 25.58, "y": 8.36, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": -1.53, "y": 2.39, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4, "x": 22.05, "y": 7.58, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.2, "x": 25.58, "y": 8.36, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "x": -1.53, "y": 2.39, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6, "x": 22.05, "y": 7.58}]}, "mao2": {"rotate": [{"angle": 2.29, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 0.56, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.29, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 0.56, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 6.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 2.29, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": 0.56, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 6.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 2.29}]}, "mao1": {"rotate": [{"angle": -1.02, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 1.84, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -1.02, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.2667, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 1.84, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4, "angle": -1.02, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.2667, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 1.84, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 6, "angle": -1.02}]}, "mao3": {"rotate": [{"angle": 4.57, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 1.35, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 10.12, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": 4.57, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "angle": 1.35, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": 10.12, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 4.57, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "angle": 1.35, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": 10.12, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": 4.57}]}, "ear": {"rotate": [{"angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 4.78, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.2667, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 4.78, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.2667, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 4.78, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 6, "angle": -1.85}]}, "ear2": {"rotate": [{"angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.98, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 8.98, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 8.98, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -2.66}]}, "ear3": {"rotate": [{"angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 14.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": 14.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": 14.41, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": -5.99}]}, "hair1": {"rotate": [{"angle": -1.2, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.0667, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.3333, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.2, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 2.0667, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.3333, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 4.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -1.2, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 4.0667, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.3333, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -1.2}]}, "hair2": {"rotate": [{"angle": -1.3, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 0.0667, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 8.98, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -1.3, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 2.0667, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.4, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": 8.98, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -1.3, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 4.0667, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.4, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": 8.98, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": -1.3}]}, "hair3": {"rotate": [{"angle": -3.17, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.0667, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4667, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 14.41, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": -3.17, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 2.0667, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4667, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": 14.41, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "angle": -3.17, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 4.0667, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4667, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "angle": 14.41, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 6, "angle": -3.17}]}, "hair4": {"rotate": [{"angle": -0.5, "curve": 0.342, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.1333, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.4, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 4.78, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -0.5, "curve": 0.342, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 2.1333, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.4, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": 4.78, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -0.5, "curve": 0.342, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 4.1333, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.4, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": 4.78, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": -0.5}]}, "hair5": {"rotate": [{"angle": 0.12, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.1333, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 8.98, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 0.12, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 2.1333, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.4667, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": 8.98, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "angle": 0.12, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 4.1333, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.4667, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "angle": 8.98, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 6, "angle": 0.12}]}, "hair6": {"rotate": [{"angle": -0.32, "curve": 0.334, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.1333, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 14.41, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -0.32, "curve": 0.334, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 2.1333, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.5333, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": 14.41, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "angle": -0.32, "curve": 0.334, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 4.1333, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.5333, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": 14.41, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "angle": -0.32}]}, "hair7": {"rotate": [{"angle": 0.22, "curve": 0.342, "c2": 0.36, "c3": 0.682, "c4": 0.72}, {"time": 0.2, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.4667, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 4.78, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 0.22, "curve": 0.342, "c2": 0.36, "c3": 0.682, "c4": 0.72}, {"time": 2.2, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.4667, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": 4.78, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "angle": 0.22, "curve": 0.342, "c2": 0.36, "c3": 0.682, "c4": 0.72}, {"time": 4.2, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.4667, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "angle": 4.78, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 6, "angle": 0.22}]}, "hair8": {"rotate": [{"angle": 1.55, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.2, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5333, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 8.98, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": 1.55, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 2.2, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5333, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": 8.98, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "angle": 1.55, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 4.2, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.5333, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": 8.98, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "angle": 1.55}]}, "hair9": {"rotate": [{"angle": 2.51, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.2, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 14.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": 2.51, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.2, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.6, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 14.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": 2.51, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 4.2, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.6, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 14.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": 2.51}]}, "hair10": {"rotate": [{"angle": 0.96, "curve": 0.34, "c2": 0.35, "c3": 0.684, "c4": 0.72}, {"time": 0.2667, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5333, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 4.78, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": 0.96, "curve": 0.34, "c2": 0.35, "c3": 0.684, "c4": 0.72}, {"time": 2.2667, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.5333, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": 4.78, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "angle": 0.96, "curve": 0.34, "c2": 0.35, "c3": 0.684, "c4": 0.72}, {"time": 4.2667, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.5333, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": 4.78, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "angle": 0.96}]}, "hair11": {"rotate": [{"angle": 2.97, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 8.98, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": 2.97, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 2.2667, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.6, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 8.98, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": 2.97, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 4.2667, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 8.98, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": 2.97}]}, "hair12": {"rotate": [{"angle": 5.26, "curve": 0.324, "c2": 0.3, "c3": 0.668, "c4": 0.67}, {"time": 0.2667, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 14.41, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 5.26, "curve": 0.324, "c2": 0.3, "c3": 0.668, "c4": 0.67}, {"time": 2.2667, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.6667, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 14.41, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 5.26, "curve": 0.324, "c2": 0.3, "c3": 0.668, "c4": 0.67}, {"time": 4.2667, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.6667, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 14.41, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": 5.26}]}, "hair13": {"rotate": [{"angle": 1.69, "curve": 0.335, "c2": 0.34, "c3": 0.685, "c4": 0.72}, {"time": 0.3333, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.6, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 4.78, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": 1.69, "curve": 0.335, "c2": 0.34, "c3": 0.685, "c4": 0.72}, {"time": 2.3333, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.6, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 4.78, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": 1.69, "curve": 0.335, "c2": 0.34, "c3": 0.685, "c4": 0.72}, {"time": 4.3333, "angle": -1.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.6, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 4.78, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": 1.69}]}, "hair14": {"rotate": [{"angle": 4.35, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 8.98, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 4.35, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 2.3333, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.6667, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 8.98, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 4.35, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 4.3333, "angle": -2.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6667, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 8.98, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": 4.35}]}, "hair15": {"rotate": [{"angle": 7.86, "curve": 0.315, "c2": 0.28, "c3": 0.665, "c4": 0.66}, {"time": 0.3333, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7333, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 14.41, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 7.86, "curve": 0.315, "c2": 0.28, "c3": 0.665, "c4": 0.66}, {"time": 2.3333, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.7333, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": 14.41, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 4, "angle": 7.86, "curve": 0.315, "c2": 0.28, "c3": 0.665, "c4": 0.66}, {"time": 4.3333, "angle": -5.99, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.7333, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 5.7333, "angle": 14.41, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 6, "angle": 7.86}]}, "hand2banner1": {"rotate": [{"angle": -16.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -23.42, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -16.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -23.42, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 0.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -16.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": -23.42, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -16.58}]}, "hand2banner2": {"rotate": [{"angle": -21.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -35.12, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 0.67, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -21.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "angle": -35.12, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": 0.67, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -21.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "angle": -35.12, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": 0.67, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": -21.95}]}, "hand2banner3": {"rotate": [{"angle": -12.95, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": -24.36, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 0.67, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": -12.95, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.4667, "angle": -24.36, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": 0.67, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "angle": -12.95, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 4.4667, "angle": -24.36, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "angle": 0.67, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 6, "angle": -12.95}]}, "hand2banner4": {"rotate": [{"angle": -11.43, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "angle": -25.87, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 0.67, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -11.43, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 2.5333, "angle": -25.87, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": 0.67, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "angle": -11.43, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 4.5333, "angle": -25.87, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": 0.67, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "angle": -11.43}]}, "hand2banner5": {"rotate": [{"angle": 14.25, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 23.12, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.15, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 14.25, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 23.12, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -8.15, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 14.25, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": 23.12, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -8.15, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 14.25}]}, "hand2banner6": {"rotate": [{"angle": 11.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 23.12, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -8.15, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": 11.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "angle": 23.12, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": -8.15, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 11.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "angle": 23.12, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -8.15, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": 11.62}]}, "hand2banner7": {"rotate": [{"angle": 8.86, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": 23.12, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -8.15, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 8.86, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.4667, "angle": 23.12, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -8.15, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "angle": 8.86, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 4.4667, "angle": 23.12, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "angle": -8.15, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 6, "angle": 8.86}]}, "hand2banner8": {"rotate": [{"angle": 6.11, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "angle": 23.12, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -8.15, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": 6.11, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 2.5333, "angle": 23.12, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": -8.15, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "angle": 6.11, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 4.5333, "angle": 23.12, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": -8.15, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "angle": 6.11}]}, "bandbanner1": {"rotate": [{"angle": -1.63, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "angle": 21.31, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -20.85, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -1.63, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 2.5333, "angle": 21.31, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": -20.85, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "angle": -1.63, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 4.5333, "angle": 21.31, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": -20.85, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "angle": -1.63}]}, "bandbanner2": {"rotate": [{"angle": 2.09, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": 21.31, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -20.85, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 2.09, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.4667, "angle": 21.31, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -20.85, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "angle": 2.09, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 4.4667, "angle": 21.31, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "angle": -20.85, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 6, "angle": 2.09}]}, "bandbanner3": {"rotate": [{"angle": 5.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 21.31, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -20.85, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": 5.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "angle": 21.31, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": -20.85, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 5.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "angle": 21.31, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -20.85, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": 5.8}]}, "bandbanner4": {"rotate": [{"angle": 9.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 21.31, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -20.85, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 9.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 21.31, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -20.85, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 9.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": 21.31, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -20.85, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 9.34}]}, "bandbanner6": {"rotate": [{"angle": -12.18, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "angle": -33.32, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 5.54, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -12.18, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 2.5333, "angle": -33.32, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": 5.54, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "angle": -12.18, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 4.5333, "angle": -33.32, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": 5.54, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "angle": -12.18}], "translate": [{"x": 0.05, "y": 4.38, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "x": 0.14, "y": 9.61, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": -0.02, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "x": 0.05, "y": 4.38, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 2.5333, "x": 0.14, "y": 9.61, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "x": -0.02, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "x": 0.05, "y": 4.38, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 4.5333, "x": 0.14, "y": 9.61, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "x": -0.02, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "x": 0.05, "y": 4.38}]}, "bandbanner7": {"rotate": [{"angle": -15.6, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": -33.32, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 5.54, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": -15.6, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.4667, "angle": -33.32, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": 5.54, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "angle": -15.6, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 4.4667, "angle": -33.32, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "angle": 5.54, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 6, "angle": -15.6}], "translate": [{"x": 0.07, "y": 5.23, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "x": 0.14, "y": 9.61, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": -0.02, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "x": 0.07, "y": 5.23, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.4667, "x": 0.14, "y": 9.61, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "x": -0.02, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "x": 0.07, "y": 5.23, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 4.4667, "x": 0.14, "y": 9.61, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "x": -0.02, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 6, "x": 0.07, "y": 5.23}]}, "bandbanner8": {"rotate": [{"angle": -22.29, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -33.32, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.54, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -22.29, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -33.32, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 5.54, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -22.29, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": -33.32, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 5.54, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -22.29}], "translate": [{"x": 0.09, "y": 6.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 0.14, "y": 9.61}, {"time": 1.3333, "x": -0.02, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.09, "y": 6.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 0.14, "y": 9.61}, {"time": 3.3333, "x": -0.02, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.09, "y": 6.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "x": 0.14, "y": 9.61}, {"time": 5.3333, "x": -0.02, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": 0.09, "y": 6.89}]}, "banner9": {"rotate": [{"angle": 2.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 3.36}, {"time": 1.1667, "angle": -2.41, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 2.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 3.36}, {"time": 3.1667, "angle": -2.41, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 2.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": 3.36}, {"time": 5.1667, "angle": -2.41, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": 2.8}]}, "banner10": {"rotate": [{"angle": 8.72, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 18.19}, {"time": 1.3333, "angle": -15.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 8.72, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 18.19}, {"time": 3.3333, "angle": -15.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 8.72, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": 18.19}, {"time": 5.3333, "angle": -15.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 8.72}]}, "banner11": {"rotate": [{"angle": 2.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 3.36}, {"time": 1.1667, "angle": -2.41, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 2.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 3.36}, {"time": 3.1667, "angle": -2.41, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 2.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": 3.36}, {"time": 5.1667, "angle": -2.41, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": 2.8}]}, "banner12": {"rotate": [{"angle": 8.72, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 18.19}, {"time": 1.3333, "angle": -15.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 8.72, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 18.19}, {"time": 3.3333, "angle": -15.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 8.72, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": 18.19}, {"time": 5.3333, "angle": -15.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 8.72}]}, "banner13": {"rotate": [{"angle": 2.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 3.36}, {"time": 1.1667, "angle": -2.41, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 2.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 3.36}, {"time": 3.1667, "angle": -2.41, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 2.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": 3.36}, {"time": 5.1667, "angle": -2.41, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": 2.8}]}, "banner14": {"rotate": [{"angle": 8.72, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 18.19}, {"time": 1.3333, "angle": -15.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 8.72, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 18.19}, {"time": 3.3333, "angle": -15.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 8.72, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": 18.19}, {"time": 5.3333, "angle": -15.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 8.72}]}, "banner1": {"rotate": [{"angle": -1.95, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -2.49}, {"time": 1.1667, "angle": 3.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -1.95, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": -2.49}, {"time": 3.1667, "angle": 3.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -1.95, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": -2.49}, {"time": 5.1667, "angle": 3.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": -1.95}]}, "banner2": {"rotate": [{"angle": -9.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -17.35}, {"time": 1.3333, "angle": 10.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -9.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -17.35}, {"time": 3.3333, "angle": 10.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -9.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": -17.35}, {"time": 5.3333, "angle": 10.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -9.53}]}, "banner3": {"rotate": [{"angle": -1.95, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -2.49}, {"time": 1.1667, "angle": 3.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -1.95, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": -2.49}, {"time": 3.1667, "angle": 3.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -1.95, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": -2.49}, {"time": 5.1667, "angle": 3.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": -1.95}]}, "banner4": {"rotate": [{"angle": -9.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -17.35}, {"time": 1.3333, "angle": 10.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -9.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -17.35}, {"time": 3.3333, "angle": 10.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -9.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": -17.35}, {"time": 5.3333, "angle": 10.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -9.53}]}, "banner5": {"rotate": [{"angle": -1.95, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -2.49}, {"time": 1.1667, "angle": 3.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -1.95, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": -2.49}, {"time": 3.1667, "angle": 3.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -1.95, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": -2.49}, {"time": 5.1667, "angle": 3.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": -1.95}]}, "banner6": {"rotate": [{"angle": -9.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -17.35}, {"time": 1.3333, "angle": 10.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -9.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -17.35}, {"time": 3.3333, "angle": 10.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -9.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": -17.35}, {"time": 5.3333, "angle": 10.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -9.53}]}, "banner7": {"rotate": [{"angle": -1.95, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -2.49, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 3.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -1.95, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": -2.49, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 3.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -1.95, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": -2.49, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": 3.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": -1.95}]}, "banner8": {"rotate": [{"angle": -9.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -17.35, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -9.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -17.35, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 10.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -9.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": -17.35, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 10.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -9.53}]}, "foot1banner2": {"rotate": [{"angle": 11.13, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 13.78, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -13.7, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 11.13, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 13.78, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -13.7, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 11.13, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": 13.78, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -13.7, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": 11.13}]}, "foot1banner3": {"rotate": [{"angle": 5.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 13.78}, {"time": 1.3333, "angle": -13.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 5.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 13.78}, {"time": 3.3333, "angle": -13.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 5.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": 13.78}, {"time": 5.3333, "angle": -13.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 5.98}]}, "foot1banner4": {"rotate": [{"angle": 0.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 13.78, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -13.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 0.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 13.78, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -13.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 0.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "angle": 13.78, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -13.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": 0.04}]}, "foot1banner5": {"rotate": [{"angle": -5.9, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 13.78, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -13.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -5.9, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 13.78, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -13.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -5.9, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": 13.78, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -13.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": -5.9}]}, "foot1banner": {"rotate": [{"angle": 5.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 16.93}, {"time": 1.5, "angle": -5.43, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 5.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 16.93}, {"time": 3.5, "angle": -5.43, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 5.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "angle": 16.93}, {"time": 5.5, "angle": -5.43, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": 5.75}]}, "foot1banner6": {"rotate": [{"angle": 10.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 16.93, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.43, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 10.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 16.93, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -5.43, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 10.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": 16.93, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -5.43, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 10.58}]}, "foot1banner7": {"rotate": [{"angle": 14.77, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 16.93}, {"time": 1.1667, "angle": -5.43, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 14.77, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 16.93}, {"time": 3.1667, "angle": -5.43, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 14.77, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": 16.93}, {"time": 5.1667, "angle": -5.43, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": 14.77}]}}, "deform": {"default": {"eye": {"eye": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "vertices": [-0.99402, 0.46267, -9.37305, 0.14852, -15.09583, 1.53118, -16.33154, 3.38587, -12.02521, 2.89403, -2.38324, 0.9922, -3.82874, -1.02407, -6.28442, -2.26724, -10.28986, -1.93209, -12.05469, -2.21343, -6.78033, -2.03637, -0.40863, 1.18595, 0, 0, 0, 0, 2.86273, -0.26548, 2.86761, -1.21013, 0, 0, 0, 0, 0, 0, 2.11493, -0.34421, 2.33197, -0.60496, 1.22485, 0.03454], "curve": "stepped"}, {"time": 1.0333, "vertices": [-0.99402, 0.46267, -9.37305, 0.14852, -15.09583, 1.53118, -16.33154, 3.38587, -12.02521, 2.89403, -2.38324, 0.9922, -3.82874, -1.02407, -6.28442, -2.26724, -10.28986, -1.93209, -12.05469, -2.21343, -6.78033, -2.03637, -0.40863, 1.18595, 0, 0, 0, 0, 2.86273, -0.26548, 2.86761, -1.21013, 0, 0, 0, 0, 0, 0, 2.11493, -0.34421, 2.33197, -0.60496, 1.22485, 0.03454], "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}}}}}}