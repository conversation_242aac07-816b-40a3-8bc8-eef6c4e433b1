import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { cfg_fuli_yueka } from "../../../cfg/vo/cfg_fuli_yueka";
import { ConfigManager } from "../../../managers/ConfigManager";
import { p_fuli_yueka } from "../../../proto/common/p_fuli_yueka";
import { m_daily_gift_tehui_info_toc } from "../../../proto/line/m_daily_gift_tehui_info_toc";
import { com } from "../../../ui/layaMaxUI";
import { ColorUtil } from "../../../util/ColorUtil";
import { DateUtil } from "../../../util/DateUtil";
import { HtmlUtil } from "../../../util/HtmlUtil";
import { StringUtil } from "../../../util/StringUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { DataCenter } from "../../DataCenter";
import { ModuleCommand } from "../../ModuleCommand";
import { UIHTMLDiv } from "../../baseModules/UIHTMLDiv";
import { UIList } from "../../baseModules/UIList";
import GoodsItem from "../../goods/GoodsItem";
import { GoodsVO } from "../../goods/GoodsVO";
import { RechargeDataCenter } from "../../recharge/data/RechargeDataCenter";
import { YueKaDataCenter} from "../../welfare/data/YueKaDataCenter";
import { PaymentDataCenter } from "../data/PaymentDataCenter";
import { PaymentItemVO } from "../vo/PaymentItemVO";
import { PaymentVO } from "../vo/PaymentVO";
import { DailyDiscountItem } from "./DailyDiscountItem";

//界面类都是每次打开每次新建的
export default class DailyDiscountView extends com.ui.res.payment.DailyDiscountViewUI {
    private biz_type: number = PaymentVO.KEY_DAILY_DISCOUNT_SHOP;
    private uiList: UIList;
    private goods: GoodsItem;
    private goodsYueka: GoodsItem;
    private info: m_daily_gift_tehui_info_toc;
    private isBuy = false;
    private pYueka: p_fuli_yueka;
    private cfgYueka: cfg_fuli_yueka;
    constructor() {
        super();

    }

    onOpen(param: any): void {
        this.txtOldPrice.text = window.iLang.L_OLD_PRICE_P0.il([ConfigManager.cfg_misc_configCache.get('daily_gify_all_buy_origin').value]);
        this.txtNowPrice.text = window.iLang.L_NOW_PRICE_P0_RMB.il([ConfigManager.cfg_misc_configCache.get('daily_gify_all_buy_discount').value]);
        this.txtOldPrice.graphics.drawLine(0, this.txtOldPrice.textHeight / 2 - 2, this.txtOldPrice.textWidth, this.txtOldPrice.textHeight / 2 - 2, this.txtOldPrice.color, 2);
        this.updateView();
    }

    initUI(): void {
        this.uiList = UIList.SetUIList(this, this.listBox, DailyDiscountItem);
        this.uiList.SetRepeat(1, 4);
        this.uiList.SetSpace(0, 10);
        this.uiList.isBoxCenter = true;

        this.goods = GoodsItem.create();
        this.goodsYueka = GoodsItem.create();
        this.boxReward.addChild(this.goods);
        this.boxYueka.addChild(this.goodsYueka);

        UIHTMLDiv.SetUIHTMLDiv(this.htmlTotalDay, 20, ColorUtil.FONT_WHITE, 3, "center", null, ColorUtil.BLACK);
        UIHTMLDiv.setAlignCenter(this.htmlTotalDay);
        UIHTMLDiv.SetUIHTMLDiv(this.htmlYueka, 20, ColorUtil.FONT_WHITE, 3, "center", null, ColorUtil.BLACK);
        UIHTMLDiv.setAlignCenter(this.htmlYueka);
    }

    addClick(): void {
        this.addOnClick(this, this.btnGo, this.onClick);
    }

    addEvent(): void {
        this.addEventListener(ModuleCommand.UPDATE_PAYMENT_SHOP_INFO, this, this.updateView);
    }

    private onClick(): void {
        // PaymentDataCenter.instance.m_daily_gift_buy_tos(this.biz_type, true, 0, RechargeDataCenter.USE_COUPON);
        let showPrice = ConfigManager.cfg_misc_configCache.get('daily_gify_all_buy_discount').value;
        TipsUtil.showReplaceBuyDialog(this,Number(showPrice), () => {
            PaymentDataCenter.instance.m_daily_gift_buy_tos(this.biz_type, true, 0, RechargeDataCenter.USE_COUPON);
        }, () => {
            PaymentDataCenter.instance.m_daily_gift_buy_tos(this.biz_type, true, 0, RechargeDataCenter.USE_NO_COUPON);
        });
    }

    private updateView(): void {
        let vo: PaymentVO = PaymentDataCenter.instance.getPaymentVoByID(this.biz_type);
        if (!vo) {
            return;
        }
        this.info = vo.toc as m_daily_gift_tehui_info_toc;

        let isShowBuyAllBox: boolean = true;
        if (vo) {
            let list: PaymentItemVO[] = [];
            for (let info of vo.list) {
                list.push(info);
                if (info.is_rmb_price) {
                    if (!info.isCanBuy) {
                        isShowBuyAllBox = false;
                    }
                    if (info.buy_num > 0) {
                        this.isBuy = true
                    }
                }
                list.sort(this.sortListById);
            }
            this.uiList.parameter = { isShowBuyAllBox: isShowBuyAllBox, isBuy: this.isBuy };
            this.uiList.array = list;
        }

        this.BuyAllBox.visible = isShowBuyAllBox;

        let buyDays = this.info.role_buy_days;
        let wholeBuyDays = this.info.whole_buy_days;
        let goodsVOList: GoodsVO[] = GoodsVO.GetPItemToVos(this.info.add_rewards);
        this.htmlTotalDay.innerHTML = window.iLang.L2_RI_LI_BAO_QUAN_MAI_P0_TIAN_ch36_P1_ch04_P2_ch37.il([wholeBuyDays,buyDays,wholeBuyDays]);
        if (goodsVOList && goodsVOList.length > 0) {
            this.goods.SetGoodsVoData(goodsVOList[0]);
            this.goodsYueka.SetGoodsVoData(goodsVOList[0]);
        }

        let cfgMisc = ConfigManager.cfg_misc_configCache.get("boat_privilege_discount_need_yeuka_type");
        let type = Number(cfgMisc.value);
        this.cfgYueka = ConfigManager.cfg_fuli_yuekaCache.get(type);
        this.htmlYueka.innerHTML = StringUtil.Format(window.iLang.L2_P0_ZENG_SONG.il(), this.cfgYueka.yueka_name);
        let status = YueKaDataCenter.instance.getFuliYuekaState(type);
        if(status == 0) {
            this.txtYuekaStatus.text = StringUtil.Format(window.iLang.L2_DANG_QIAN_WEI_JI_HUO_P0.il(), this.cfgYueka.yueka_name);;
            this.txtYuekaStatus.color = ColorUtil.RED_DIAMOND;
            this.goodsYueka.gray = true;
        } else {
            this.pYueka = YueKaDataCenter.instance.getFuliYuekaInfo(type);
            if (this.pYueka) {
                if (this.pYueka.end_time > DataCenter.serverTimeSeconds) {
                    this.updateYuekaTime();
                    this.timerLoop(1000, this, this.updateYuekaTime);
                } else {
                    this.clearTimer(this, this.updateYuekaTime);
                }
            } else {
                this.clearTimer(this, this.updateYuekaTime);
            }
            this.goodsYueka.gray = false;
        }
    }

    updateYuekaTime() {
        let date: Date = DateUtil.GetDate(DataCenter.serverTimeSeconds * 1000);
        let targetDate: Date = DateUtil.GetDate(this.pYueka.end_time * 1000);
        let count = DateUtil.getSubTimeToSeconds(targetDate, date);
        if (count <= 0) {
            this.clearTimer(this, this.updateYuekaTime);
        }
        let d: number = Math.floor(count / 86400);
        let useDay = this.cfgYueka.duration - d;
        this.txtYuekaStatus.text = StringUtil.Format(window.iLang.L2_DANG_QIAN_YI_JI_HUO_RECEIVE_P0_ch05_P1_TIAN.il(), this.cfgYueka.yueka_name, useDay);
        this.txtYuekaStatus.color = ColorUtil.FONT_GREEN_5;
    }

    private sortListById(vo1: PaymentItemVO, vo2: PaymentItemVO): number {
        let buy1: number = !vo1.isCanBuy ? 1 : 0;
        let buy2: number = !vo2.isCanBuy ? 1 : 0;

        if (buy1 == 1 || buy2 == 1) {
            return buy1 - buy2;
        }
        return vo1.price - vo2.price;
    }
}
