
    import {ConfigManager} from "../managers/ConfigManager";
    import {XmlFormatVo} from "../util/XmlFormatVo";

    /**杂项配置 */
    export class MiscConstAuto {
        private static valueCahce: any = {};
        /**获取数字类型数据 */
        public static getNumberValue(key: string): number {
            if (ConfigManager.cfg_misc_configCache.has(key)) {
                let res: number = this.valueCahce[key];
                if (!res) {
                    res = Number(ConfigManager.cfg_misc_configCache.get(key).value);
                    this.valueCahce[key] = res;
                }
                return res;
            }
            return 0;
        }

        /** 获取字符串类型 */
        public static getStringValue(key: string): string {
            if (ConfigManager.cfg_misc_configCache.has(key)) {
                let res: string = this.valueCahce[key];
                if (!res) {
                    res = ConfigManager.cfg_misc_configCache.get(key).value;
                    this.valueCahce[key] = res;
                }
                return res;
            }
            return "";
        }

        /**获取数字列表数据 */
        public static getNumberListValue(key: string): number[] {
            if (ConfigManager.cfg_misc_configCache.has(key)) {
                let res: number[] = this.valueCahce[key];
                if (!res) {
                    res = XmlFormatVo.GetArr(ConfigManager.cfg_misc_configCache.get(key).value, true);
                    this.valueCahce[key] = res;
                }
                return res;
            }
            return [];
        }

        /**获取字符串列表数据 */
        public static getStringListValue(key: string): string[] {
            if (ConfigManager.cfg_misc_configCache.has(key)) {
                let res: string[] = this.valueCahce[key];
                if (!res) {
                    res = XmlFormatVo.GetArr(ConfigManager.cfg_misc_configCache.get(key).value, false);
                    this.valueCahce[key] = res;
                }
                return res;
            }
            return [];
        }

        /**获取数字列表数据 */
        public static getMapValue(key: string): Map<any,number>{
            if (ConfigManager.cfg_misc_configCache.has(key)) {
                let res: Map<any, number> = this.valueCahce[key];
                if (!res) {
                    let anyList = XmlFormatVo.GetArr(ConfigManager.cfg_misc_configCache.get(key).value, true);
                    for (let i: number = 0; i < anyList.length; i+= 2) { 
                        const key = anyList[i];
                        res.set(key, Number(anyList[i+1]));
                    }
                    this.valueCahce[key] = res;
                }
                return res;
            }
            return new Map();
        }


    	/** 最大可以拥有的角色数 */
	public static get max_remain_role_num(): number { 
		return this.getNumberValue("max_remain_role_num");
	}
	/** 聊天最多发送道具个数 */
	public static get chat_goods_max_num(): number { 
		return this.getNumberValue("chat_goods_max_num");
	}
	/** 限制玩家投诉/反馈的时间/秒数 */
	public static get role_complaints_limit(): number { 
		return this.getNumberValue("role_complaints_limit");
	}
	/** 排行榜默认处理数据的玩家等级 */
	public static get rank_min_level(): number { 
		return this.getNumberValue("rank_min_level");
	}
	/** 最大的好友个数 */
	public static get max_friend_list(): number { 
		return this.getNumberValue("max_friend_list");
	}
	/** 最大的好友申请个数 */
	public static get max_friend_request_list(): number { 
		return this.getNumberValue("max_friend_request_list");
	}
	/** 最大的黑名单个数 */
	public static get max_black_num(): number { 
		return this.getNumberValue("max_black_num");
	}
	/** 背包初始格子配置(行|列) */
	public static get role_bag_basic(): number[] { 
		return this.getNumberListValue("role_bag_basic");
	}
	/** 最大的体力值 */
	public static get max_energy(): number { 
		return this.getNumberValue("max_energy");
	}
	/** 签约卡道具ID */
	public static get sign_card(): number { 
		return this.getNumberValue("sign_card");
	}
	/** 每周签约次数 */
	public static get max_sign_count(): number { 
		return this.getNumberValue("max_sign_count");
	}
	/** 每周最多有3个出售中的球员 */
	public static get max_sale_count(): number { 
		return this.getNumberValue("max_sale_count");
	}
	/** 每周成功签约次数 */
	public static get max_succ_sign_count(): number { 
		return this.getNumberValue("max_succ_sign_count");
	}
	/** 每次喝水加的体力值 */
	public static get energy_activity_value(): number { 
		return this.getNumberValue("energy_activity_value");
	}
	/** 喝水所获得体力，最高不超过500 */
	public static get energy_activity_max_value(): number { 
		return this.getNumberValue("energy_activity_max_value");
	}
	/** 赛季全部球员状态集训次数 */
	public static get train_all_position_max_value(): number { 
		return this.getNumberValue("train_all_position_max_value");
	}
	/** 秒CD扣钻石 */
	public static get clear_cd_hour_gold(): number { 
		return this.getNumberValue("clear_cd_hour_gold");
	}
	/** 每日自动比赛的周数（默认|付费） */
	public static get week_auto_count(): number[] { 
		return this.getNumberListValue("week_auto_count");
	}
	/** 比赛开始前国际比赛日的引导 */
	public static get ready_guide_add_plv_lv_limit(): number { 
		return this.getNumberValue("ready_guide_add_plv_lv_limit");
	}
	/** 比赛开始前状态太差的引导 */
	public static get ready_guide_bad_status_lv_limit(): number { 
		return this.getNumberValue("ready_guide_bad_status_lv_limit");
	}
	/** 玩家改名需要的钻石 */
	public static get role_rename_cost_gold(): number { 
		return this.getNumberValue("role_rename_cost_gold");
	}
	/** 英雄回收消耗钻石 */
	public static get hero_recycle_cost_gold(): number { 
		return this.getNumberValue("hero_recycle_cost_gold");
	}
	/** 一键强化开放等级 */
	public static get auto_equips_reinforce_lv_limit(): number { 
		return this.getNumberValue("auto_equips_reinforce_lv_limit");
	}
	/** 通过XX关弹首充弹窗（首充过，不弹） */
	public static get first_pay_show(): number[] { 
		return this.getNumberListValue("first_pay_show");
	}
	/** 通过X关弹出首充弹窗（首充过，不弹） */
	public static get first_recharge_show(): number { 
		return this.getNumberValue("first_recharge_show");
	}
	/** 引导对话框出现时禁止切换其他界面 */
	public static get mainUIBottomLayer_guide_girl_change(): number { 
		return this.getNumberValue("mainUIBottomLayer_guide_girl_change");
	}
	/** 1级后可在战斗时切换至其他界面 */
	public static get mainUIBottomLayer_fight_change(): number { 
		return this.getNumberValue("mainUIBottomLayer_fight_change");
	}
	/**  */
	public static get mainUIBottomLayer_fight_change_tip(): string { 
		return this.getStringValue("mainUIBottomLayer_fight_change_tip");
	}
	/** rmb和钻石比例 */
	public static get rmb_exchange_gold(): number { 
		return this.getNumberValue("rmb_exchange_gold");
	}
	/** 20点显灵 */
	public static get family_cast_box_time(): number { 
		return this.getNumberValue("family_cast_box_time");
	}
	/** 前20名才有奖励 */
	public static get family_boss_score_rank(): number { 
		return this.getNumberValue("family_boss_score_rank");
	}
	/** 平定中原每日的免费道具次数 */
	public static get maze_free_use_item(): number { 
		return this.getNumberValue("maze_free_use_item");
	}
	/** 平定中原一键讨伐最低vip等级 */
	public static get maze_auto_sweep_vip_limit(): number { 
		return this.getNumberValue("maze_auto_sweep_vip_limit");
	}
	/** 法宝装备开放等级 */
	public static get fabao_equip_open_lv(): number { 
		return this.getNumberValue("fabao_equip_open_lv");
	}
	/** 英雄一键进阶开放等级 */
	public static get equip_refine_onekey_open_lv(): number { 
		return this.getNumberValue("equip_refine_onekey_open_lv");
	}
	/** 无双试炼本层扫荡开放等级 */
	public static get trial_sweep_open_lv(): number { 
		return this.getNumberValue("trial_sweep_open_lv");
	}
	/** 无双试炼三星扫荡开放等级 */
	public static get trial_all_sweep_open_lv(): number { 
		return this.getNumberValue("trial_all_sweep_open_lv");
	}
	/** 竞技场一键扫荡开放等级 */
	public static get arena_all_sweep_open_lv(): number { 
		return this.getNumberValue("arena_all_sweep_open_lv");
	}
	/** 竞技场扫荡5次开放等级 */
	public static get arena_sweep5_open_lv(): number { 
		return this.getNumberValue("arena_sweep5_open_lv");
	}
	/** 当玩家等级小于20，有未上阵英雄时（未上满6个英雄且拥有英雄>已上阵英雄）;点战斗弹窗提示 */
	public static get line_up_tip_level(): number { 
		return this.getNumberValue("line_up_tip_level");
	}
	/** 战斗跳过等级限制 */
	public static get finish_fight_lv_limit(): number { 
		return this.getNumberValue("finish_fight_lv_limit");
	}
	/** 战斗加速最高倍率（前端显示使用，整数） */
	public static get fight_play_speed_max_rate(): number { 
		return this.getNumberValue("fight_play_speed_max_rate");
	}
	/** 战斗加速2倍速等级限制 */
	public static get quick_fight_lv_limit_2(): number { 
		return this.getNumberValue("quick_fight_lv_limit_2");
	}
	/** 战斗加速1倍速实际倍率 */
	public static get fight_play_speed_rate_1(): number { 
		return this.getNumberValue("fight_play_speed_rate_1");
	}
	/** 战斗加速2倍速实际倍率 */
	public static get fight_play_speed_rate_2(): number { 
		return this.getNumberValue("fight_play_speed_rate_2");
	}
	/** 战斗加速3倍速实际倍率 */
	public static get fight_play_speed_rate_3(): number { 
		return this.getNumberValue("fight_play_speed_rate_3");
	}
	/** 战斗加速4倍速实际倍率 */
	public static get fight_play_speed_rate_4(): number { 
		return this.getNumberValue("fight_play_speed_rate_4");
	}
	/** 战斗加速5倍速实际倍率 */
	public static get fight_play_speed_rate_5(): number { 
		return this.getNumberValue("fight_play_speed_rate_5");
	}
	/** 战斗默认播放倍速 */
	public static get fight_default_play_speed(): number { 
		return this.getNumberValue("fight_default_play_speed");
	}
	/** 英雄评论置顶的数量 */
	public static get hero_comment_top_num(): number { 
		return this.getNumberValue("hero_comment_top_num");
	}
	/** 英雄评论的时间限制，单位：秒 */
	public static get hero_comment_send_interval(): number { 
		return this.getNumberValue("hero_comment_send_interval");
	}
	/** 英雄评论的最大数量 */
	public static get hero_comment_max_num(): number { 
		return this.getNumberValue("hero_comment_max_num");
	}
	/** 英雄评论的最大文字数量 */
	public static get hero_comment_content_max_length(): number { 
		return this.getNumberValue("hero_comment_content_max_length");
	}
	/** 英雄评论的最少文字数量 */
	public static get hero_comment_content_min_length(): number { 
		return this.getNumberValue("hero_comment_content_min_length");
	}
	/** 英雄评论的等级限制 */
	public static get hero_comment_lv_limit(): number { 
		return this.getNumberValue("hero_comment_lv_limit");
	}
	/** 加载页数量 */
	public static get loading_bg_num(): number { 
		return this.getNumberValue("loading_bg_num");
	}
	/** 出现免费跳过的等级 */
	public static get free_finish_fight_lv(): number { 
		return this.getNumberValue("free_finish_fight_lv");
	}
	/** 免费跳过最大的免费次数 */
	public static get free_finish_fight_times(): number { 
		return this.getNumberValue("free_finish_fight_times");
	}
	/** 英雄神殿开启需要的高级招募次数 */
	public static get faction_lottery_open_limit(): number { 
		return this.getNumberValue("faction_lottery_open_limit");
	}
	/** 30元特惠礼包跳转 */
	public static get payment_tips_go(): number[] { 
		return this.getNumberListValue("payment_tips_go");
	}
	/** 战斗回放开启等级 */
	public static get fight_review_limit(): number { 
		return this.getNumberValue("fight_review_limit");
	}
	/** 免费次数剩余提示 */
	public static get free_finish_tips_times(): number[] { 
		return this.getNumberListValue("free_finish_tips_times");
	}
	/** 军团红包每天最大领取次数 */
	public static get family_hongbao_max_fetch_times(): number { 
		return this.getNumberValue("family_hongbao_max_fetch_times");
	}
	/** 自动推关的结束关卡 */
	public static get main_auto_pass_max_pass(): string[] { 
		return this.getStringListValue("main_auto_pass_max_pass");
	}
	/** 自动推关的循环间隔 */
	public static get main_auto_pass_loop_interval(): number { 
		return this.getNumberValue("main_auto_pass_loop_interval");
	}
	/** 战斗跳过倒计时秒数 */
	public static get fight_jump_time_down(): number { 
		return this.getNumberValue("fight_jump_time_down");
	}
	/** 挂机界面英雄收集banner开启关数 */
	public static get hero_collect_main_icon_open_round(): number { 
		return this.getNumberValue("hero_collect_main_icon_open_round");
	}
	/** 支线任务的开放关卡 */
	public static get sub_mission_open_pass(): number { 
		return this.getNumberValue("sub_mission_open_pass");
	}
	/** 组队BOSS消耗精力值 */
	public static get team_boss_cost_vigor(): number { 
		return this.getNumberValue("team_boss_cost_vigor");
	}
	/** 【兵器系统】兵器宝石开启最低等级 */
	public static get bingfu_gem_open_min_level(): number { 
		return this.getNumberValue("bingfu_gem_open_min_level");
	}
	/** 兵器打造限制，3阶前（含3阶），必须将上一阶的4种兵器都打造一遍才能解锁下一阶的兵器 */
	public static get bingfu_make_stage(): number { 
		return this.getNumberValue("bingfu_make_stage");
	}
	/** 军团试炼救援令受赠次数上限 */
	public static get family_trial_max_help_num(): number { 
		return this.getNumberValue("family_trial_max_help_num");
	}
	/** 组队boss队长的跳过战斗时间限制 */
	public static get team_boss_fight_cd_time(): number { 
		return this.getNumberValue("team_boss_fight_cd_time");
	}
	/** 通用提示日志最大数量 */
	public static get max_hit_log_num(): number { 
		return this.getNumberValue("max_hit_log_num");
	}
	/** 高级招募心愿单开启需要的次数 */
	public static get high_lottery_wish_open_limit(): number { 
		return this.getNumberValue("high_lottery_wish_open_limit");
	}
	/** 阵营招募心愿单开启需要的次数 */
	public static get faction_lottery_wish_open_limit(): number { 
		return this.getNumberValue("faction_lottery_wish_open_limit");
	}
	/** 兵器合成阶段，即从第几阶开始 */
	public static get bingfu_stone_compose_stage(): number { 
		return this.getNumberValue("bingfu_stone_compose_stage");
	}
	/** 普通招募跳过开启等级 */
	public static get lottery_normal_skip_open_lv(): number { 
		return this.getNumberValue("lottery_normal_skip_open_lv");
	}
	/** 友情招募跳过开启等级 */
	public static get lottery_friendship_skip_open_lv(): number { 
		return this.getNumberValue("lottery_friendship_skip_open_lv");
	}
	/** 高级招募跳过开启等级 */
	public static get lottery_hight_skip_open_lv(): number { 
		return this.getNumberValue("lottery_hight_skip_open_lv");
	}
	/** 阵营招募跳过开启等级 */
	public static get lottery_nation_skip_open_lv(): number { 
		return this.getNumberValue("lottery_nation_skip_open_lv");
	}
	/** 将魂招募跳过开启等级 */
	public static get lottery_soul_skip_open_lv(): number { 
		return this.getNumberValue("lottery_soul_skip_open_lv");
	}
	/** 无双试炼选择难度跳过战斗开启关卡 */
	public static get trial_choose_pass_open_guan(): number { 
		return this.getNumberValue("trial_choose_pass_open_guan");
	}
	/** 主线可以跳过战斗的关卡数 */
	public static get main_battle_quick_fight_pass(): number { 
		return this.getNumberValue("main_battle_quick_fight_pass");
	}
	/** 征战关卡开启倍数播放的关卡 */
	public static get main_battle_rate(): number { 
		return this.getNumberValue("main_battle_rate");
	}
	/** 【蛮族入侵】蛮族入侵的攻击间隔，单位：秒 */
	public static get random_boss_attack_cd(): number { 
		return this.getNumberValue("random_boss_attack_cd");
	}
	/** 邮件数大于多少弹出提示 */
	public static get letter_max_num_tips(): number { 
		return this.getNumberValue("letter_max_num_tips");
	}
	/** XX经验值对应|XX道具一个 */
	public static get equips_star_exp_to_item(): number[] { 
		return this.getNumberListValue("equips_star_exp_to_item");
	}
	/** 英雄7星后才能开始觉醒 */
	public static get hero_star_break_limit(): number { 
		return this.getNumberValue("hero_star_break_limit");
	}
	/** 英雄8星后的广播 */
	public static get hero_star_break_show_off(): number[] { 
		return this.getNumberListValue("hero_star_break_show_off");
	}
	/** VIP每日任务（VIP任务）开启条件 */
	public static get vip_daily_mission_limit(): number { 
		return this.getNumberValue("vip_daily_mission_limit");
	}
	/** 每日最大转军团次数 */
	public static get max_family_transfer_count(): number { 
		return this.getNumberValue("max_family_transfer_count");
	}
	/** 技能按照冷却时间释放 */
	public static get skill_cast_type_cooldown(): number { 
		return this.getNumberValue("skill_cast_type_cooldown");
	}
	/** 技能按照配置顺序释放 */
	public static get skill_cast_type_index(): number { 
		return this.getNumberValue("skill_cast_type_index");
	}
	/** 美人重生消耗钻石 */
	public static get sweapon_recycle_cost_gold(): number { 
		return this.getNumberValue("sweapon_recycle_cost_gold");
	}
	/** 【征战挂机】普通挂机战斗时间段：移动时间|攻击时间|出怪间隔（最小值）|出怪间隔（最大值）|删除怪物时间（删除怪物时间不需要改动），毫秒 */
	public static get guaji_time(): string[] { 
		return this.getStringListValue("guaji_time");
	}
	/** 【征战挂机】快速挂机战斗时间段：移动时间|攻击时间|出怪间隔（最小值）|出怪间隔（最大值）|删除怪物时间（删除怪物时间不需要改动），毫秒 */
	public static get guaji_time2(): string[] { 
		return this.getStringListValue("guaji_time2");
	}
	/** 【征战挂机】挂机背景移动速度（像素/每秒），普通模式|快速模式 */
	public static get guaji_move(): string[] { 
		return this.getStringListValue("guaji_move");
	}
	/** 【征战挂机】快速挂机战斗持续总时间，毫秒（用作显示和延迟请求快速挂机结果，和快速挂机战斗实际的持续时间没有关系） */
	public static get guaji_quick_total_time(): number { 
		return this.getNumberValue("guaji_quick_total_time");
	}
	/** 【征战挂机】快速挂机战斗每一列出怪的数量 */
	public static get guaji_col_max_num(): number { 
		return this.getNumberValue("guaji_col_max_num");
	}
	/** 【征战挂机】挂机战斗掉落物参数-普通模式，散落数量|散开时间（毫秒）|停留时间（毫秒）|移动到宝箱的速度（像素/每秒） */
	public static get guaji_fly_icon_param1(): string[] { 
		return this.getStringListValue("guaji_fly_icon_param1");
	}
	/** 【征战挂机】挂机战斗掉落物参数-快速模式，散落数量|散开时间（毫秒）|停留时间（毫秒）|移动到宝箱的速度（像素/每秒） */
	public static get guaji_fly_icon_param2(): string[] { 
		return this.getStringListValue("guaji_fly_icon_param2");
	}
	/** "【征战挂机】挂机英雄喊话时间,喊完才会继续攻击" */
	public static get guaji_talk_time(): number { 
		return this.getNumberValue("guaji_talk_time");
	}
	/** 挂机快速挂机动画跳过等级限制 */
	public static get guaji_quick_fight_skip_lv_limit(): number { 
		return this.getNumberValue("guaji_quick_fight_skip_lv_limit");
	}
	/** 挂机快速挂机二次提示等级限制 */
	public static get guaji_quick_fight_tip_lv_limit(): number { 
		return this.getNumberValue("guaji_quick_fight_tip_lv_limit");
	}
	/** 阵营类型列表 */
	public static get hero_nation_list(): string { 
		return this.getStringValue("hero_nation_list");
	}
	/** 职业类型列表 */
	public static get hero_career_list(): string { 
		return this.getStringValue("hero_career_list");
	}
	/** 星图激活石的ID */
	public static get xingtu_activate_item(): number { 
		return this.getNumberValue("xingtu_activate_item");
	}
	/** 显示战斗小兵（1显示 0不显示） */
	public static get show_fight_soldier(): number { 
		return this.getNumberValue("show_fight_soldier");
	}
	/** 显示英雄神殿开启的国家 */
	public static get show_Lotter_Nation(): string[] { 
		return this.getStringListValue("show_Lotter_Nation");
	}
	/** 英雄回收分解消耗 */
	public static get hero_recycle_cost(): number[] { 
		return this.getNumberListValue("hero_recycle_cost");
	}
	/** 显示跳过战斗按钮等级 */
	public static get show_fight_skip_btn(): number { 
		return this.getNumberValue("show_fight_skip_btn");
	}
	/** 国战超过该击杀数时，强制结束 */
	public static get war_spike_num(): number { 
		return this.getNumberValue("war_spike_num");
	}
	/** 皇城战超过该击杀数时，强制结束 */
	public static get king_war_spike_num(): number { 
		return this.getNumberValue("king_war_spike_num");
	}
	/** 跨服战超过该击杀数时，强制结束 */
	public static get cross_war_spike_num(): number { 
		return this.getNumberValue("cross_war_spike_num");
	}
	/** 世界等级加成大于等于该等级才生效 */
	public static get world_level_add_effect(): number { 
		return this.getNumberValue("world_level_add_effect");
	}
	/** 挂机界面英雄点名banner开启关卡数 */
	public static get hero_rollcall_main_icon_open_round(): number { 
		return this.getNumberValue("hero_rollcall_main_icon_open_round");
	}
	/** 群雄逐鹿向上匹配权重 */
	public static get random_pvp_up_weight(): number { 
		return this.getNumberValue("random_pvp_up_weight");
	}
	/** 群雄逐鹿向下匹配权重 */
	public static get random_pvp_down_weight(): number { 
		return this.getNumberValue("random_pvp_down_weight");
	}
	/** 世界boss开启时间(不支持秒) */
	public static get world_boss_start_time(): number[] { 
		return this.getNumberListValue("world_boss_start_time");
	}
	/** 世界boss结束时间(不支持秒) */
	public static get world_boss_end_time(): number[] { 
		return this.getNumberListValue("world_boss_end_time");
	}
	/** 每日免费次数 */
	public static get world_boss_free_times(): number { 
		return this.getNumberValue("world_boss_free_times");
	}
	/** 每日可购买次数 */
	public static get world_boss_can_buy_times(): number { 
		return this.getNumberValue("world_boss_can_buy_times");
	}
	/** 购买消耗的钻石 */
	public static get world_boss_consume_gold(): string[] { 
		return this.getStringListValue("world_boss_consume_gold");
	}
	/** 南蛮入侵幸运大礼包 */
	public static get world_boss_lucky_rewards(): number[] { 
		return this.getNumberListValue("world_boss_lucky_rewards");
	}
	/** 南蛮入侵发放奖励时间 */
	public static get world_boss_send_rewards_time(): number[] { 
		return this.getNumberListValue("world_boss_send_rewards_time");
	}
	/** 大于x级后，上阵未养成英雄弹窗提示 */
	public static get hero_recycle_tips_lv(): number { 
		return this.getNumberValue("hero_recycle_tips_lv");
	}
	/** VIP大于等于x级的玩家每次登录游戏弹出天公赐福的广告推送 */
	public static get banner_shop_vip(): number { 
		return this.getNumberValue("banner_shop_vip");
	}
	/** 英雄等级大于等于x级的英雄才能进行英雄继承操作 */
	public static get inherit_level_limit(): number { 
		return this.getNumberValue("inherit_level_limit");
	}
	/** 分享活动取好友数据的时间间隔（秒） */
	public static get share_refresh_role_time(): number { 
		return this.getNumberValue("share_refresh_role_time");
	}
	/** 挂机攻击城池时间 */
	public static get guaji_atk_time(): number { 
		return this.getNumberValue("guaji_atk_time");
	}
	/** 挂机怪物生成范围 */
	public static get guaji_range(): number { 
		return this.getNumberValue("guaji_range");
	}
	/** 杜康酒获得数量支持倍数的道具列表 */
	public static get lottery_mul_rate_items(): string[] { 
		return this.getStringListValue("lottery_mul_rate_items");
	}
	/** 杜康酒获得数量支持倍数参数 */
	public static get lottery_mul_rate_num(): number { 
		return this.getNumberValue("lottery_mul_rate_num");
	}
	/** 官渡之战队伍英雄的 最大体力 */
	public static get war_team_max_tili(): number { 
		return this.getNumberValue("war_team_max_tili");
	}
	/** 官渡之战队伍行动的 消耗体力 */
	public static get war_team_cost_tili(): number { 
		return this.getNumberValue("war_team_cost_tili");
	}
	/** 官渡之战队伍行动的每次恢复体力 */
	public static get war_team_recover_tili(): number { 
		return this.getNumberValue("war_team_recover_tili");
	}
	/** 官渡之战队伍行动的每次恢复体力的间隔 */
	public static get war_team_recover_time(): number { 
		return this.getNumberValue("war_team_recover_time");
	}
	/** 自动回答当前题消耗x钻石 */
	public static get spring_activity_exam_cost_gold(): number { 
		return this.getNumberValue("spring_activity_exam_cost_gold");
	}
	/** 春节年兽每次消耗的道具 */
	public static get spring_activity_boss_cost(): number[] { 
		return this.getNumberListValue("spring_activity_boss_cost");
	}
	/** 每题获得活跃度 */
	public static get spring_activity_exam_scores(): number { 
		return this.getNumberValue("spring_activity_exam_scores");
	}
	/** 高级祭坛英雄回退到9星 */
	public static get hero_recycle_advance_back_star(): number { 
		return this.getNumberValue("hero_recycle_advance_back_star");
	}
	/** 神将试炼援助战力限制，不超过当前英雄战力的万分比 */
	public static get generals_trial_assistance_power_limit(): number { 
		return this.getNumberValue("generals_trial_assistance_power_limit");
	}
	/** 神将试炼起点波数偏移 */
	public static get generals_trial_begin_trun_offset(): number { 
		return this.getNumberValue("generals_trial_begin_trun_offset");
	}
	/** 试炼塔每日免费次数 */
	public static get test_tower_free_times(): number { 
		return this.getNumberValue("test_tower_free_times");
	}
	/** 试炼塔每日可购买次数 */
	public static get test_tower_can_buy_times(): number { 
		return this.getNumberValue("test_tower_can_buy_times");
	}
	/** 战神殿失败1200秒CD */
	public static get ares_palace_cd_second(): number { 
		return this.getNumberValue("ares_palace_cd_second");
	}
	/** 神位争夺保存战报数量 */
	public static get ares_palace_save_logs(): number { 
		return this.getNumberValue("ares_palace_save_logs");
	}
	/** 试炼塔购买次数消耗钻石 */
	public static get test_tower_consume_gold(): string[] { 
		return this.getStringListValue("test_tower_consume_gold");
	}
	/** 试炼塔开放等级 */
	public static get test_tower_open_lv(): number { 
		return this.getNumberValue("test_tower_open_lv");
	}
	/** 招募积分上限 */
	public static get lottery_max_score(): number { 
		return this.getNumberValue("lottery_max_score");
	}
	/** 英雄进阶石 */
	public static get hero_rock_item_id(): number { 
		return this.getNumberValue("hero_rock_item_id");
	}
	/** 许愿获得积分 */
	public static get lottery_nation_gains_score(): number[] { 
		return this.getNumberListValue("lottery_nation_gains_score");
	}
	/** 许愿获得额外物品：转换神石 */
	public static get lottery_nation_gains_item(): number[] { 
		return this.getNumberListValue("lottery_nation_gains_item");
	}
	/** 许愿消耗物品 */
	public static get lottery_nation_cost_item(): number[] { 
		return this.getNumberListValue("lottery_nation_cost_item");
	}
	/** 英雄升星替换材料，英魂卡列表 */
	public static get hero_upstar_material_cards(): string[] { 
		return this.getStringListValue("hero_upstar_material_cards");
	}
	/** 纹章熔炼值上限 */
	public static get bingfu_compose_score_up_limit(): number { 
		return this.getNumberValue("bingfu_compose_score_up_limit");
	}
	/** 战斗飘字震屏参数，格式：总时间|震动次数|水平震动幅度|垂直震动幅度|阻尼系数|类型 */
	public static get battle_hurt_shock(): string { 
		return this.getStringValue("battle_hurt_shock");
	}
	/** 战斗飘字间隔，单位：毫秒 */
	public static get battle_hurt_interval(): number { 
		return this.getNumberValue("battle_hurt_interval");
	}
	/** 战斗状态飘字间隔，单位：毫秒 */
	public static get battle_status_interval(): number { 
		return this.getNumberValue("battle_status_interval");
	}
	/** 战斗胜利界面关闭倒计时，单位：秒 */
	public static get battle_success_close_tick(): number { 
		return this.getNumberValue("battle_success_close_tick");
	}
	/** 关卡奖励20分钟挂机奖励 */
	public static get main_battle_pass_times(): number { 
		return this.getNumberValue("main_battle_pass_times");
	}
	/** 关卡奖励固定100情报 */
	public static get main_battle_pass_travel_score(): number { 
		return this.getNumberValue("main_battle_pass_travel_score");
	}
	/** 关卡奖励20%概率3.5倍 */
	public static get main_battle_pass_crit(): number[] { 
		return this.getNumberListValue("main_battle_pass_crit");
	}
	/** 官渡之战跳过战斗限制或关系(层数|vip) */
	public static get guandu_pass_limit(): number[] { 
		return this.getNumberListValue("guandu_pass_limit");
	}
	/** 官渡之战扫荡限制或关系（层数|vip） */
	public static get guandu_sweep_limit(): number[] { 
		return this.getNumberListValue("guandu_sweep_limit");
	}
	/** 神装洗练最大次数 */
	public static get god_equip_shift_max_count(): number { 
		return this.getNumberValue("god_equip_shift_max_count");
	}
	/** 神装转换消耗道具类型 */
	public static get god_equip_covert_item(): number { 
		return this.getNumberValue("god_equip_covert_item");
	}
	/** 神装转换消耗钻石数 */
	public static get god_equip_covert_cost(): number { 
		return this.getNumberValue("god_equip_covert_cost");
	}
	/** 神装洗练锁定1件消耗钻石数 */
	public static get god_equip_lock_cost(): number { 
		return this.getNumberValue("god_equip_lock_cost");
	}
	/** 神装附魔洗练锁定属性消耗钻石数 */
	public static get god_equip_enchant_lock_cost(): number { 
		return this.getNumberValue("god_equip_enchant_lock_cost");
	}
	/** 探索开启快速接取的传说任务数 */
	public static get travel_quick_open_mission_num(): number { 
		return this.getNumberValue("travel_quick_open_mission_num");
	}
	/** 积分招募所需vip等级 */
	public static get lottery_type_score_need_vip(): number { 
		return this.getNumberValue("lottery_type_score_need_vip");
	}
	/** 聊天框VIP图标显示 */
	public static get chat_vip_tag_need_vip(): number { 
		return this.getNumberValue("chat_vip_tag_need_vip");
	}
	/** 图鉴一键领取等级限制 */
	public static get handbook_one_key_fetch_limit(): number { 
		return this.getNumberValue("handbook_one_key_fetch_limit");
	}
	/** 推荐好友显示数量 */
	public static get friend_recommend_num(): number { 
		return this.getNumberValue("friend_recommend_num");
	}
	/** 推荐好友等级限制 */
	public static get friend_recommend_level(): number { 
		return this.getNumberValue("friend_recommend_level");
	}
	/** 推荐好友日常任务活跃值限制 */
	public static get friend_recommend_daily_score(): number { 
		return this.getNumberValue("friend_recommend_daily_score");
	}
	/** 最后一个指引id */
	public static get last_guide_id(): number { 
		return this.getNumberValue("last_guide_id");
	}
	/** 送vip6界面展示奖励 */
	public static get give_vip_reward(): number[] { 
		return this.getNumberListValue("give_vip_reward");
	}
	/** 勇闯异境布阵跳过战斗，需战力超出关卡敌人战力的万分比 */
	public static get pass_behead_skip_fight_limit(): number { 
		return this.getNumberValue("pass_behead_skip_fight_limit");
	}
	/** 勇闯异境未通关时，匹配的排名增加3 */
	public static get pass_behead_not_pass_add_rank(): number { 
		return this.getNumberValue("pass_behead_not_pass_add_rank");
	}
	/** 勇闯异境未阵亡了4个算未通关 */
	public static get pass_behead_not_pass_dead_num(): number { 
		return this.getNumberValue("pass_behead_not_pass_dead_num");
	}
	/** 根据关卡数显示下一关按钮(大于等于) */
	public static get show_next_btn_pass(): number { 
		return this.getNumberValue("show_next_btn_pass");
	}
	/** 重置6星及以下英雄，可退回星级和英雄材料 */
	public static get hero_recycle_complete_star(): number { 
		return this.getNumberValue("hero_recycle_complete_star");
	}
	/** 魏蜀吴5升6星消耗精锐点(初始星级：4) */
	public static get hero_great_point_cost_normal_4(): number { 
		return this.getNumberValue("hero_great_point_cost_normal_4");
	}
	/** 神魔5升6星消耗精锐点(初始星级：4) */
	public static get hero_great_point_cost_special_4(): number { 
		return this.getNumberValue("hero_great_point_cost_special_4");
	}
	/** 魏蜀吴5升6星消耗精锐点(初始星级：5) */
	public static get hero_great_point_cost_normal_5(): number { 
		return this.getNumberValue("hero_great_point_cost_normal_5");
	}
	/** 神魔5升6星消耗精锐点(初始星级：5) */
	public static get hero_great_point_cost_special_5(): number { 
		return this.getNumberValue("hero_great_point_cost_special_5");
	}
	/** 冠军赛开启需要大于等于该创角人数 */
	public static get qxzl_start_need_create_roles(): number { 
		return this.getNumberValue("qxzl_start_need_create_roles");
	}
	/** 试练塔商店开放层数限制 */
	public static get test_tower_shop_open_limit(): number { 
		return this.getNumberValue("test_tower_shop_open_limit");
	}
	/** 纹章重铸可以锁定技能的纹章ID列表 */
	public static get bingfu_recast_lock_limit(): string[] { 
		return this.getStringListValue("bingfu_recast_lock_limit");
	}
	/** "神装纷争开启限制,形式{玩家等级|世界等级|其他开启条件玩家等级}" */
	public static get lcqs_open_limit(): number[] { 
		return this.getNumberListValue("lcqs_open_limit");
	}
	/** 神装秘宝每周最大抽奖次数 */
	public static get god_equip_hunt_day_max_times(): number { 
		return this.getNumberValue("god_equip_hunt_day_max_times");
	}
	/** 跨服段位赛匹配到对手后的自动开战时间(单位秒) */
	public static get random_pvp_auto_start_time(): number { 
		return this.getNumberValue("random_pvp_auto_start_time");
	}
	/** 百服争霸匹配到对手后的自动开战时间(单位秒) */
	public static get dominate_pvp_auto_start_time(): number { 
		return this.getNumberValue("dominate_pvp_auto_start_time");
	}
	/** 平定中原布阵跳过战斗条件1，需战力超出关卡敌人战力的万分比 */
	public static get maze_skip_fight_limit_1(): number { 
		return this.getNumberValue("maze_skip_fight_limit_1");
	}
	/** 平定中原布阵跳过战斗条件2，需上阵英雄数量5人 */
	public static get maze_skip_fight_limit_2(): number { 
		return this.getNumberValue("maze_skip_fight_limit_2");
	}
	/** 平定中原布阵跳过战斗条件3，需上阵英雄总体血量达到90% */
	public static get maze_skip_fight_limit_3(): number { 
		return this.getNumberValue("maze_skip_fight_limit_3");
	}
	/** 共鸣英雄总星级55级解锁英灵 */
	public static get hero_resonate_dudu_need_star(): number { 
		return this.getNumberValue("hero_resonate_dudu_need_star");
	}
	/** 共鸣等级达到340级（不含英灵加成）后解锁赋能 */
	public static get hero_resonate_dhyana_need_level(): number { 
		return this.getNumberValue("hero_resonate_dhyana_need_level");
	}
	/** 共鸣英雄总星级65级解锁赋能 */
	public static get hero_resonate_dhyana_need_star(): number { 
		return this.getNumberValue("hero_resonate_dhyana_need_star");
	}
	/** 上半赛季持续时间(单位：天) */
	public static get random_pvp_first_half_days(): number { 
		return this.getNumberValue("random_pvp_first_half_days");
	}
	/** 下半赛季持续时间(单位：天) */
	public static get random_pvp_second_half_days(): number { 
		return this.getNumberValue("random_pvp_second_half_days");
	}
	/** 共鸣英雄共鸣cd */
	public static get hero_resonate_five_cd(): number { 
		return this.getNumberValue("hero_resonate_five_cd");
	}
	/** 英灵晋升cd */
	public static get hero_resonate_dudu_cd(): number { 
		return this.getNumberValue("hero_resonate_dudu_cd");
	}
	/** 布阵界面页签列表：征战、竞技场、冠军赛、公会战 */
	public static get line_up_tab_list(): string[] { 
		return this.getStringListValue("line_up_tab_list");
	}
	/** 跨服段位开启双队伍的段位限制 */
	public static get random_pvp_multi_lineup_limit(): number { 
		return this.getNumberValue("random_pvp_multi_lineup_limit");
	}
	/** 诸雄争霸一键扫荡开启关卡 */
	public static get red_cilff_auto_sweep_pass(): number { 
		return this.getNumberValue("red_cilff_auto_sweep_pass");
	}
	/** 挂机宝箱积分列表 */
	public static get guaji_box_score_list(): string[] { 
		return this.getStringListValue("guaji_box_score_list");
	}
	/** 挂机宝箱在线300秒，结算一次 */
	public static get guaji_box_online_second(): number { 
		return this.getNumberValue("guaji_box_online_second");
	}
	/** 精灵重置消耗 */
	public static get wing_reset_cost(): number[] { 
		return this.getNumberListValue("wing_reset_cost");
	}
	/** 传承需要的最低英雄星级 */
	public static get inherit_need_star(): number { 
		return this.getNumberValue("inherit_need_star");
	}
	/** 传承需要的领主最低等级 */
	public static get inherit_need_level(): number { 
		return this.getNumberValue("inherit_need_level");
	}
	/** 传承需要的钻石倍率（次数*钻石倍率） */
	public static get inherit_need_cost(): number[] { 
		return this.getNumberListValue("inherit_need_cost");
	}
	/** 精灵每天最大重置次数 */
	public static get wing_max_reset_times(): number { 
		return this.getNumberValue("wing_max_reset_times");
	}
	/** 英雄长按升级间隔，毫秒 */
	public static get hero_upgrade_lv_interval(): number { 
		return this.getNumberValue("hero_upgrade_lv_interval");
	}
	/** 天梯赛周1-5开启 */
	public static get cross_ladder_start_weeks(): string[] { 
		return this.getStringListValue("cross_ladder_start_weeks");
	}
	/** 天梯赛最后一天23点结束 */
	public static get cross_ladder_end_time(): number { 
		return this.getNumberValue("cross_ladder_end_time");
	}
	/** 跨服天梯赛积分道具ID */
	public static get cross_ladder_item_id(): number { 
		return this.getNumberValue("cross_ladder_item_id");
	}
	/** 开启英雄星阶所需的星级 */
	public static get open_hero_star_stage_lv(): number { 
		return this.getNumberValue("open_hero_star_stage_lv");
	}
	/** 平定中原开启难度选择层数（大于等于） */
	public static get maze_open_choose_diff_floor(): number { 
		return this.getNumberValue("maze_open_choose_diff_floor");
	}
	/** 跨服天梯赛刷新间隔，毫秒 */
	public static get cross_ladder_refresh_interval(): number { 
		return this.getNumberValue("cross_ladder_refresh_interval");
	}
	/** 跨服天梯赛参与排名 */
	public static get cross_ladder_join_rank(): number { 
		return this.getNumberValue("cross_ladder_join_rank");
	}
	/** 最大自动推关次数 */
	public static get guaji_max_auto_pass_num(): number { 
		return this.getNumberValue("guaji_max_auto_pass_num");
	}
	/** 首次绿宝箱固定奖励 */
	public static get hanging_box_first_green_item(): string[] { 
		return this.getStringListValue("hanging_box_first_green_item");
	}
	/** 首次蓝宝箱固定奖励 */
	public static get hanging_box_first_blue_item(): string[] { 
		return this.getStringListValue("hanging_box_first_blue_item");
	}
	/** 玩法导航显示等级限制 */
	public static get guaji_quick_navigation_limit(): number { 
		return this.getNumberValue("guaji_quick_navigation_limit");
	}
	/** 好友援助英雄最多显示前20 */
	public static get max_rent_hero_show(): number { 
		return this.getNumberValue("max_rent_hero_show");
	}
	/** 试练塔前5层不进行碾压战斗 */
	public static get test_tower_crush_fight_limit(): number { 
		return this.getNumberValue("test_tower_crush_fight_limit");
	}
	/** 每天22点结束 */
	public static get xswh_end_time(): number { 
		return this.getNumberValue("xswh_end_time");
	}
	/** 每周在线奖励每日领取次数 */
	public static get online_week_reward_limit(): number { 
		return this.getNumberValue("online_week_reward_limit");
	}
	/** 征战布阵最高战力英雄红点等级限制 */
	public static get line_up_max_power_red_point_lv_limit(): number { 
		return this.getNumberValue("line_up_max_power_red_point_lv_limit");
	}
	/** 征战布阵神器装配红点等级限制 */
	public static get line_up_god_weapon_red_point_lv_limit(): number { 
		return this.getNumberValue("line_up_god_weapon_red_point_lv_limit");
	}
	/** 征战挂机显示通关奖励时间 */
	public static get main_battle_reward_tips_show_time(): number { 
		return this.getNumberValue("main_battle_reward_tips_show_time");
	}
	/** 征战挂机显示通关奖励最小等级 */
	public static get main_battle_reward_tips_show_min_level(): number { 
		return this.getNumberValue("main_battle_reward_tips_show_min_level");
	}
	/** 征战挂机显示通关奖励最大等级 */
	public static get main_battle_reward_tips_show_max_level(): number { 
		return this.getNumberValue("main_battle_reward_tips_show_max_level");
	}
	/** 勇闯异境开启难度选择的等级限制 */
	public static get pass_behead_open_model_select_lv_limit(): number { 
		return this.getNumberValue("pass_behead_open_model_select_lv_limit");
	}
	/** 勇闯异境默认难度选择 */
	public static get pass_behead_default_model_select(): number { 
		return this.getNumberValue("pass_behead_default_model_select");
	}
	/** 弹出名将卷轴等级 */
	public static get show_hero_pass_unlock_level(): number { 
		return this.getNumberValue("show_hero_pass_unlock_level");
	}
	/** 神装秘宝星期一重置 */
	public static get god_equip_hunt_reset_weekday(): number { 
		return this.getNumberValue("god_equip_hunt_reset_weekday");
	}
	/** 战斗中是否可以查看英雄信息 */
	public static get battle_is_can_look_hero(): number { 
		return this.getNumberValue("battle_is_can_look_hero");
	}
	/** 神装背包最大容量 */
	public static get god_equip_bag_max_len(): number { 
		return this.getNumberValue("god_equip_bag_max_len");
	}
	/** 新手豪礼开启的开服日期 */
	public static get noob_pay_start_open_day(): number { 
		return this.getNumberValue("noob_pay_start_open_day");
	}
	/** 新手豪礼结束的开服日期 */
	public static get noob_pay_end_open_day(): number { 
		return this.getNumberValue("noob_pay_end_open_day");
	}
	/** 异能英雄链源消耗，钻石 */
	public static get soul_hero_link_cost(): number { 
		return this.getNumberValue("soul_hero_link_cost");
	}
	/** 异能英雄取消链源消耗，钻石 */
	public static get soul_hero_cancel_link_cost(): number { 
		return this.getNumberValue("soul_hero_cancel_link_cost");
	}
	/** 龙魂装备槽2所需龙魂等级 */
	public static get ingenious_plan_load_slot_limit(): number { 
		return this.getNumberValue("ingenious_plan_load_slot_limit");
	}
	/** 开启龙魂装备槽2所需材料（x星级的龙魂|数量） */
	public static get ingenious_plan_open_load_cost(): number[] { 
		return this.getNumberListValue("ingenious_plan_open_load_cost");
	}
	/** 龙魂首次解锁奖励 */
	public static get ingenious_plan_first_reward(): number[] { 
		return this.getNumberListValue("ingenious_plan_first_reward");
	}
	/** 奇谋解锁11星消耗 */
	public static get ingenious_plan_unlock_cost(): number[] { 
		return this.getNumberListValue("ingenious_plan_unlock_cost");
	}
	/** "异能英雄开启限制,格式：玩家等级|世界等级" */
	public static get soul_hero_open_limit(): number[] { 
		return this.getNumberListValue("soul_hero_open_limit");
	}
	/** 异能英雄布阵上阵名额 */
	public static get soul_hero_line_up_num(): number { 
		return this.getNumberValue("soul_hero_line_up_num");
	}
	/** 战旗链接消耗战旗共鸣石 */
	public static get war_flag_link_cost(): number[] { 
		return this.getNumberListValue("war_flag_link_cost");
	}
	/** 战旗旗链功能解锁的阶数 */
	public static get war_flag_link_unlock_stage(): number { 
		return this.getNumberValue("war_flag_link_unlock_stage");
	}
	/** 战旗置换消耗 */
	public static get war_flag_exchange_cost(): number[] { 
		return this.getNumberListValue("war_flag_exchange_cost");
	}
	/** 战旗每天置换最大次数 */
	public static get war_flag_exchange_max_times(): number { 
		return this.getNumberValue("war_flag_exchange_max_times");
	}
	/** 平定中原噩梦难度选择限制，VIP8可选择 */
	public static get maze_diffucult_select_limit_3(): number { 
		return this.getNumberValue("maze_diffucult_select_limit_3");
	}
	/** 平定中原地狱难度选择限制，VIP11可选择 */
	public static get maze_diffucult_select_limit_4(): number { 
		return this.getNumberValue("maze_diffucult_select_limit_4");
	}
	/** 神魔塔开放所需试练塔层数 */
	public static get ghost_tower_open_limit(): number { 
		return this.getNumberValue("ghost_tower_open_limit");
	}
	/** 天选塔开放所需条件（形式：试练塔层数|神魔塔层数） */
	public static get sky_tower_open_limit(): number[] { 
		return this.getNumberListValue("sky_tower_open_limit");
	}
	/** 角色等级（不再提示的勾选项）|角色等级（继承弹窗）|英雄等级（继承弹窗），角色和英雄等级都满足才会弹窗 */
	public static get hero_down_legends_tips(): number[] { 
		return this.getNumberListValue("hero_down_legends_tips");
	}
	/** 挂机道具每日使用上限 */
	public static get item_hanging_card_max_num(): number { 
		return this.getNumberValue("item_hanging_card_max_num");
	}
	/** 段位赛消耗挑战道具 */
	public static get random_pvp_cost_item_id(): number { 
		return this.getNumberValue("random_pvp_cost_item_id");
	}
	/** 代金券道具ID */
	public static get riche_act_shop_replace_rmb_item(): number { 
		return this.getNumberValue("riche_act_shop_replace_rmb_item");
	}
	/** 阵容副队开启条件1，玩家等级 */
	public static get line_up_assistant_open_limit_1(): number { 
		return this.getNumberValue("line_up_assistant_open_limit_1");
	}
	/** 阵容副队开启条件2，共鸣槽位数量 */
	public static get line_up_assistant_open_limit_2(): number { 
		return this.getNumberValue("line_up_assistant_open_limit_2");
	}
	/** 勋章最大穿戴数量 */
	public static get medal_max_dress(): number { 
		return this.getNumberValue("medal_max_dress");
	}
	/** 勋章第四插槽开启星级 */
	public static get medal_four_slot_open_star(): number { 
		return this.getNumberValue("medal_four_slot_open_star");
	}
	/** 战后疲劳BUFF类型列表 */
	public static get winning_streak_buff_type(): string[] { 
		return this.getStringListValue("winning_streak_buff_type");
	}
	/** 哀兵必胜BUFF类型列表 */
	public static get losing_streak_buff_type(): string[] { 
		return this.getStringListValue("losing_streak_buff_type");
	}
	/** 英雄一次升级最大级数 */
	public static get hero_up_level_step(): number { 
		return this.getNumberValue("hero_up_level_step");
	}
	/** 英雄X级前一次升级到30级 */
	public static get hero_up_level_step_limit(): number { 
		return this.getNumberValue("hero_up_level_step_limit");
	}
	/** 开启跨服借用英雄开服天数 */
	public static get cross_rent_hero_open_day(): number { 
		return this.getNumberValue("cross_rent_hero_open_day");
	}
	/** 邀请好友达到80级的特别好礼 */
	public static get sllang_wxshare_special_gift(): number[] { 
		return this.getNumberListValue("sllang_wxshare_special_gift");
	}
	/** 支援英雄开启跨服大于等于该天数 */
	public static get rent_hero_cross_open_days(): number { 
		return this.getNumberValue("rent_hero_cross_open_days");
	}
	/** 龙魂装备槽1所需龙魂等级 */
	public static get ingenious_plan_load_slot1_limit(): number { 
		return this.getNumberValue("ingenious_plan_load_slot1_limit");
	}
	/** 玩家需要vip15才可看见vip19-vip20的vip内容 */
	public static get show_vip_19_20_limit(): number { 
		return this.getNumberValue("show_vip_19_20_limit");
	}
	/** 11星及以上不出现在献祭列表 */
	public static get hero_recycle_star_limit(): number { 
		return this.getNumberValue("hero_recycle_star_limit");
	}
	/** 异能英雄开启标识，1：开启，0：关闭 */
	public static get soul_hero_open_tag(): number { 
		return this.getNumberValue("soul_hero_open_tag");
	}
	/** 不开启异能英雄的平台列表 */
	public static get soul_hero_not_open_platform(): string[] { 
		return this.getStringListValue("soul_hero_not_open_platform");
	}
	/** 设置显示注销账号按钮的平台 */
	public static get show_disAccount_platform(): string { 
		return this.getStringValue("show_disAccount_platform");
	}
	/** 审核服显示首充图标 */
	public static get show_shenhe_first_pay_platform(): string[] { 
		return this.getStringListValue("show_shenhe_first_pay_platform");
	}
	/** 竞技场和冠军赛进入跨服模式时间，开服天数 */
	public static get arena_start_cross_open_day(): number { 
		return this.getNumberValue("arena_start_cross_open_day");
	}
	/** 竞技场和冠军赛进入跨服模式前3天提示 */
	public static get arena_cross_tip_time(): number { 
		return this.getNumberValue("arena_cross_tip_time");
	}
	/** 纹章祝福重铸额外消耗 */
	public static get bingfu_blessing_recast_cost(): number[] { 
		return this.getNumberListValue("bingfu_blessing_recast_cost");
	}
	/** 英雄12星开启神装 */
	public static get open_god_equip_role_limit_star(): number { 
		return this.getNumberValue("open_god_equip_role_limit_star");
	}
	/** 英雄升十四星开启限制（开服天数|英雄精灵等级） */
	public static get hero_fourteen_star_limit(): number[] { 
		return this.getNumberListValue("hero_fourteen_star_limit");
	}
	/** 三国英雄激活十四星消耗 */
	public static get hero_fourteen_star_act_cost_1(): number[] { 
		return this.getNumberListValue("hero_fourteen_star_act_cost_1");
	}
	/** 神魔英雄激活十四星消耗 */
	public static get hero_fourteen_star_act_cost_2(): number[] { 
		return this.getNumberListValue("hero_fourteen_star_act_cost_2");
	}
	/** 三系英雄激活14星消耗读取神魔的英雄列表 */
	public static get sanxi_hero_fourteen_star_act_cost_2_list(): string[] { 
		return this.getStringListValue("sanxi_hero_fourteen_star_act_cost_2_list");
	}
	/** 英雄星级资源显示划分范围 */
	public static get hero_star_show_extent(): string[] { 
		return this.getStringListValue("hero_star_show_extent");
	}
	/** 英雄置换星级限制范围 */
	public static get hero_change_star_extent(): string[] { 
		return this.getStringListValue("hero_change_star_extent");
	}
	/** 英雄终极升星预览开启限制，开服天数 */
	public static get hero_final_star_preview_limit(): number { 
		return this.getNumberValue("hero_final_star_preview_limit");
	}
	/** 异能英雄重置消耗1000钻石 */
	public static get soul_hero_reset_cost(): number { 
		return this.getNumberValue("soul_hero_reset_cost");
	}
	/** 英雄材料消耗限制，11星及以上英雄，存在星阶时，不可被消耗 */
	public static get hero_material_cost_limit(): number { 
		return this.getNumberValue("hero_material_cost_limit");
	}
	/** 平定中原解锁一键碾压需要通关的层数 */
	public static get maze_auto_rolling_floor_limit(): number { 
		return this.getNumberValue("maze_auto_rolling_floor_limit");
	}
	/** 平定中原解锁碾压战斗需要通关的层数 */
	public static get maze_rolling_floor_limit(): number { 
		return this.getNumberValue("maze_rolling_floor_limit");
	}
	/** 平台禁止输入（配置需要禁止输入的平台名） */
	public static get textinput_limit_platform(): string { 
		return this.getStringValue("textinput_limit_platform");
	}
	/** 禁止输入文字提示 */
	public static get textinput_limit_show_tips(): string { 
		return this.getStringValue("textinput_limit_show_tips");
	}
	/** 平定中原一键碾压时间间隔，（战斗间隔|跳过间隔） */
	public static get maze_auto_rolling_interval(): string[] { 
		return this.getStringListValue("maze_auto_rolling_interval");
	}
	/** 异能英雄同调替换消耗钻石 */
	public static get soul_hero_replace_link_cost(): number { 
		return this.getNumberValue("soul_hero_replace_link_cost");
	}
	/** 战区争霸限制类型变更列表 */
	public static get random_pvp_limit_change_type(): string[] { 
		return this.getStringListValue("random_pvp_limit_change_type");
	}
	/** 百服争霸限制类型变更列表 */
	public static get dominate_pvp_limit_change_type(): string[] { 
		return this.getStringListValue("dominate_pvp_limit_change_type");
	}
	/** 夏令月份 */
	public static get summer_month(): number[] { 
		return this.getNumberListValue("summer_month");
	}
	/** 冬令月份 */
	public static get winter_month(): number[] { 
		return this.getNumberListValue("winter_month");
	}
	/** 主城开启夜景等级 */
	public static get main_city_night_level(): number { 
		return this.getNumberValue("main_city_night_level");
	}
	/** 冬令主城开启黄昏景时间 */
	public static get main_city_normal_winter_hour(): number[] { 
		return this.getNumberListValue("main_city_normal_winter_hour");
	}
	/** 冬令主城开启黄昏景时间 */
	public static get main_city_normal2_winter_hour(): number[] { 
		return this.getNumberListValue("main_city_normal2_winter_hour");
	}
	/** 冬令主城开启夜景时间 */
	public static get main_city_night_winter_hour(): number[] { 
		return this.getNumberListValue("main_city_night_winter_hour");
	}
	/** 主城开启日景时间 */
	public static get main_city_sunny_hour(): number[] { 
		return this.getNumberListValue("main_city_sunny_hour");
	}
	/** 夏令主城开启黄昏景时间 */
	public static get main_city_normal_summer_hour(): number[] { 
		return this.getNumberListValue("main_city_normal_summer_hour");
	}
	/** 夏令主城开启黄昏景时间 */
	public static get main_city_normal2_summer_hour(): number[] { 
		return this.getNumberListValue("main_city_normal2_summer_hour");
	}
	/** 夏令主城开启夜景时间 */
	public static get main_city_night_summer_hour(): number[] { 
		return this.getNumberListValue("main_city_night_summer_hour");
	}
	/** 战斗BUFF特效显示数量上限 */
	public static get battle_buff_eff_num_limit(): number { 
		return this.getNumberValue("battle_buff_eff_num_limit");
	}
	/** 守护灵每次卸下消耗钻石数量 */
	public static get deputy_unload_cost(): number[] { 
		return this.getNumberListValue("deputy_unload_cost");
	}
	/** 守护灵每天重置次数 */
	public static get deputy_reset_times(): number { 
		return this.getNumberValue("deputy_reset_times");
	}
	/** 守护灵重置消耗 */
	public static get deputy_reset_cost(): number[] { 
		return this.getNumberListValue("deputy_reset_cost");
	}
	/** 守护灵属性御灵等级限制 */
	public static get deputy_attr_level_limit(): string[] { 
		return this.getStringListValue("deputy_attr_level_limit");
	}
	/** 守护灵技能御灵等级限制 */
	public static get deputy_skill_level_limit(): string[] { 
		return this.getStringListValue("deputy_skill_level_limit");
	}
	/** 守护灵属性3守护灵星级限制 */
	public static get deputy_attr3_hero_star_limit(): number { 
		return this.getNumberValue("deputy_attr3_hero_star_limit");
	}
	/** 守护灵技能守护灵星级限制 */
	public static get deputy_skill_hero_star_limit(): string[] { 
		return this.getStringListValue("deputy_skill_hero_star_limit");
	}
	/** 跨服好友功能需要月卡类型 */
	public static get cross_pvp_chat_need_yeuka_type(): number { 
		return this.getNumberValue("cross_pvp_chat_need_yeuka_type");
	}
	/** 跨服段位赛每日匹配相同角色次数 */
	public static get cross_random_pvp_match_same_role_count(): number { 
		return this.getNumberValue("cross_random_pvp_match_same_role_count");
	}
	/** 跨服段位赛48小时未登录无法匹配到 */
	public static get cross_random_pvp_no_match_hour(): number { 
		return this.getNumberValue("cross_random_pvp_no_match_hour");
	}
	/** 玩家信息神兵开启等级 */
	public static get user_page_show_qimou_shenbing_level(): number { 
		return this.getNumberValue("user_page_show_qimou_shenbing_level");
	}
	/** 玩家信息龙魂开启等级 */
	public static get user_page_qimou_level(): number { 
		return this.getNumberValue("user_page_qimou_level");
	}
	/** 专属福利每日活动每天最大重置次数 */
	public static get spec_daily_acc_pay_act_max_times(): number { 
		return this.getNumberValue("spec_daily_acc_pay_act_max_times");
	}
	/** 自动发放转端奖励的平台列表 */
	public static get auto_send_rewards_zd_platform(): string[] { 
		return this.getStringListValue("auto_send_rewards_zd_platform");
	}
	/** 转端累充活动额外支持平台 */
	public static get wx_to_app_acc_pay_other_platform(): string[] { 
		return this.getStringListValue("wx_to_app_acc_pay_other_platform");
	}
	/** "过关斩将解锁副队条件,格式：最小难度|最大难度" */
	public static get pass_behead_unlock_assistant_contition(): string[] { 
		return this.getStringListValue("pass_behead_unlock_assistant_contition");
	}
	/** 组队跨服领主每天22点结束 */
	public static get team_xswh_end_time(): number { 
		return this.getNumberValue("team_xswh_end_time");
	}
	/** 战斗回放保留天数 */
	public static get replay_reserve_days(): number { 
		return this.getNumberValue("replay_reserve_days");
	}
	/** 设置页面隐私协议支持的平台列表 */
	public static get setting_privacy_show_platform(): string[] { 
		return this.getStringListValue("setting_privacy_show_platform");
	}
	/** 跟主服相差一天则送12个快速挂机券 */
	public static get after_merge_hanging_gains(): number[] { 
		return this.getNumberListValue("after_merge_hanging_gains");
	}
	/** 勋章终极勋章开启星级 */
	public static get xunzhang_sp_tap_star(): number { 
		return this.getNumberValue("xunzhang_sp_tap_star");
	}
	/** 兵法互换消耗 */
	public static get bingfa_exchange_cost(): number[] { 
		return this.getNumberListValue("bingfa_exchange_cost");
	}
	/** 自动抽卡开启等级 */
	public static get master_card_auto_level_limit(): number { 
		return this.getNumberValue("master_card_auto_level_limit");
	}
	/** 自动抽卡开启vip等级 */
	public static get master_card_auto_vip_limit(): number { 
		return this.getNumberValue("master_card_auto_vip_limit");
	}
	/** 魂卡初始道具 */
	public static get master_card_init_reward(): number[] { 
		return this.getNumberListValue("master_card_init_reward");
	}
	/** 魂卡视频加速限制（形式：加速减少秒数|每日次数|每次间隔秒数） */
	public static get master_card_quicken_video(): number[] { 
		return this.getNumberListValue("master_card_quicken_video");
	}
	/** 魂卡免费加速所需月卡 */
	public static get master_card_quicken_free_yueka(): number { 
		return this.getNumberValue("master_card_quicken_free_yueka");
	}
	/** 魂卡加速消耗道具 */
	public static get master_card_quicken_cost_item(): number[] { 
		return this.getNumberListValue("master_card_quicken_cost_item");
	}
	/** 魂卡超级加速开放vip等级 */
	public static get master_card_super_faster_vip(): number { 
		return this.getNumberValue("master_card_super_faster_vip");
	}
	/** 魂卡自动开启等级 */
	public static get master_card_auto_open_level(): number { 
		return this.getNumberValue("master_card_auto_open_level");
	}
	/** 魂卡加速道具减少时间（单位/s） */
	public static get master_card_quick_item_time(): number { 
		return this.getNumberValue("master_card_quick_item_time");
	}
	/** 天赋树升级加速消耗钻石|数量|每日次数|减少时间|每次间隔 */
	public static get master_talent_science_quicken_cost_gold(): number[] { 
		return this.getNumberListValue("master_talent_science_quicken_cost_gold");
	}
	/** 天赋加速消耗加速时间道具 */
	public static get master_talent_science_quicken_cost_item(): number { 
		return this.getNumberValue("master_talent_science_quicken_cost_item");
	}
	/** 天赋树重置消耗钻石数量 */
	public static get master_talent_science_reset_cost(): number[] { 
		return this.getNumberListValue("master_talent_science_reset_cost");
	}
	/** 天赋重置按钮冷却时间（单位/s） */
	public static get master_talent_science_quick_reset_time(): number { 
		return this.getNumberValue("master_talent_science_quick_reset_time");
	}
	/** 天赋树免费加速所需月卡 */
	public static get master_talent_science_quicken_free_yueka(): number { 
		return this.getNumberValue("master_talent_science_quicken_free_yueka");
	}
	/** 挂机奖励开始计算的卡牌阶级 */
	public static get hanging_start_master_card_stage(): number { 
		return this.getNumberValue("hanging_start_master_card_stage");
	}
	/** 魂卡冲冲冲激励广告id */
	public static get silang_silangwxgame_mjccc_rewarded_ad_id(): string { 
		return this.getStringValue("silang_silangwxgame_mjccc_rewarded_ad_id");
	}
	/** 抽卡展示动画必现颜色（含以上） */
	public static get master_lottery_must_show_color(): number { 
		return this.getNumberValue("master_lottery_must_show_color");
	}
	/** 抽卡第一次显示最大颜色 */
	public static get master_lottry_first_show_up(): number { 
		return this.getNumberValue("master_lottry_first_show_up");
	}
	/** 抽卡第一次显示最小颜色 */
	public static get master_lottry_first_show_down(): number { 
		return this.getNumberValue("master_lottry_first_show_down");
	}
	/** 初始赠送魂卡装饰道具 */
	public static get init_give_decoration_item(): string[] { 
		return this.getStringListValue("init_give_decoration_item");
	}
	/** 该值表示单次开箱的时间的某个时间点播放鼓震和鼓声，例如2为开箱时间的一半播放，值越大播放时机越早 */
	public static get master_card_music_effect_play_position(): number { 
		return this.getNumberValue("master_card_music_effect_play_position");
	}
	/** 每次魂卡开箱奖励 */
	public static get master_card_drum_rewards(): number[] { 
		return this.getNumberListValue("master_card_drum_rewards");
	}
	/** 魂卡首充开放开服天数 */
	public static get master_card_first_pay_open_day(): number { 
		return this.getNumberValue("master_card_first_pay_open_day");
	}
	/** 返还这个兵符80%精炼消耗的素材 */
	public static get bingfu_refine_return_cost_rate(): number { 
		return this.getNumberValue("bingfu_refine_return_cost_rate");
	}
	/** 勋章退役开放平台 */
	public static get medal_retire_open_plat(): string[] { 
		return this.getStringListValue("medal_retire_open_plat");
	}
	/** 奇谋解锁十星道具 */
	public static get qi_mou_unlock_good_id(): number { 
		return this.getNumberValue("qi_mou_unlock_good_id");
	}
	/** 奇谋不满足开服天数显示最高星级 */
	public static get ingenious_plan_show_max_star(): number { 
		return this.getNumberValue("ingenious_plan_show_max_star");
	}
	/** 活动攻略的子分类，改动顺序可改图标位置 */
	public static get strategy_type_list(): string[] { 
		return this.getStringListValue("strategy_type_list");
	}
	/** 获取游戏圈权限时间间隔太短 */
	public static get wx_game_club_anth_tips_1(): string { 
		return this.getStringValue("wx_game_club_anth_tips_1");
	}
	/** 请前往右上角-设置-打开游戏圈功能之后，再尝试 */
	public static get wx_game_club_anth_tips_2(): string { 
		return this.getStringValue("wx_game_club_anth_tips_2");
	}
	/** 获取游戏圈数据其他异常行为 */
	public static get wx_game_club_anth_tips_3(): string { 
		return this.getStringValue("wx_game_club_anth_tips_3");
	}
	/** 境界副本体力药水的购买递增消耗：购买次数，货币消耗 */
	public static get stage_copy_shop_cost(): string[] { 
		return this.getStringListValue("stage_copy_shop_cost");
	}
	/** 境界副本递增消耗道具id */
	public static get stage_copy_shop_cost_item(): number { 
		return this.getNumberValue("stage_copy_shop_cost_item");
	}
	/** 显示皮肤兑换的按钮的等级 */
	public static get show_change_skin_btn_level(): number { 
		return this.getNumberValue("show_change_skin_btn_level");
	}
	/** 仅初始5星英雄支持兑换皮肤碎片 */
	public static get hero_recycle_skin_star_limit(): number { 
		return this.getNumberValue("hero_recycle_skin_star_limit");
	}
	/** 福利签到额外再领取1次奖励当天累计充值需要金额元 */
	public static get fuli_sign_extra_gold(): number { 
		return this.getNumberValue("fuli_sign_extra_gold");
	}
	/** 纹章插槽1星级限制 */
	public static get bingfu_slot1_star_limit(): number { 
		return this.getNumberValue("bingfu_slot1_star_limit");
	}
	/** 纹章插槽2星级限制 */
	public static get bingfu_slot2_star_limit(): number { 
		return this.getNumberValue("bingfu_slot2_star_limit");
	}
	/** 神装穿戴星级限制 */
	public static get god_equip_star_load_limit(): number { 
		return this.getNumberValue("god_equip_star_load_limit");
	}
	/** 天赋插槽1星级限制 */
	public static get bingfa_slot1_star_limit(): number { 
		return this.getNumberValue("bingfa_slot1_star_limit");
	}
	/** 天赋插槽2星级限制 */
	public static get bingfa_slot2_star_limit(): number { 
		return this.getNumberValue("bingfa_slot2_star_limit");
	}
	/** 天赋插槽3星级限制 */
	public static get bingfa_slot3_star_limit(): number { 
		return this.getNumberValue("bingfa_slot3_star_limit");
	}
	/** 铸魂开启限制 */
	public static get casting_soul_limit(): number { 
		return this.getNumberValue("casting_soul_limit");
	}
	/** 精灵开启限制 */
	public static get wing_limit(): number { 
		return this.getNumberValue("wing_limit");
	}
	/** 英雄成长之路显示的星级范围 */
	public static get hero_growth_road_star_range(): number[] { 
		return this.getNumberListValue("hero_growth_road_star_range");
	}
	/** 兵符插槽1等级限制 */
	public static get bingfu_slot_1_level_limit(): number { 
		return this.getNumberValue("bingfu_slot_1_level_limit");
	}
	/** 铸魂激活需要武将本身的星级限制：大于10星 */
	public static get hero_star_open_casting_soul(): number { 
		return this.getNumberValue("hero_star_open_casting_soul");
	}
	/** 实力预览开启星级 */
	public static get shili_preview_open_star(): number { 
		return this.getNumberValue("shili_preview_open_star");
	}
	/** 每日特惠全买原价 */
	public static get daily_gify_all_buy_origin(): number { 
		return this.getNumberValue("daily_gify_all_buy_origin");
	}
	/** 每日特惠全买折扣后 */
	public static get daily_gify_all_buy_discount(): number { 
		return this.getNumberValue("daily_gify_all_buy_discount");
	}
	/** 领主阵营转换消耗钻石数量 */
	public static get lord_camp_convert_cost(): number[] { 
		return this.getNumberListValue("lord_camp_convert_cost");
	}
	/** 领主技能重置消耗钻石数量 */
	public static get lord_skill_level_reset_cost(): number[] { 
		return this.getNumberListValue("lord_skill_level_reset_cost");
	}
	/** 领主阵营的名称 */
	public static get lord_camp_1(): string { 
		return this.getStringValue("lord_camp_1");
	}
	/** 领主阵营的名称 */
	public static get lord_camp_2(): string { 
		return this.getStringValue("lord_camp_2");
	}
	/** 领主装备类型名 */
	public static get lord_equip_type_5001(): string { 
		return this.getStringValue("lord_equip_type_5001");
	}
	/** 领主装备类型名 */
	public static get lord_equip_type_5002(): string { 
		return this.getStringValue("lord_equip_type_5002");
	}
	/** 领主装备类型名 */
	public static get lord_equip_type_5003(): string { 
		return this.getStringValue("lord_equip_type_5003");
	}
	/** 领主装备类型名 */
	public static get lord_equip_type_5004(): string { 
		return this.getStringValue("lord_equip_type_5004");
	}
	/** 领主技能品质对应 */
	public static get lord_quality_type(): string[] { 
		return this.getStringListValue("lord_quality_type");
	}
	/** 领主技能触发类型对应 */
	public static get lord_trigger_type(): string[] { 
		return this.getStringListValue("lord_trigger_type");
	}
	/** 领主技能伤害类型对应 */
	public static get lord_hurt_type(): string[] { 
		return this.getStringListValue("lord_hurt_type");
	}
	/** 领主品质对应 */
	public static get lord_base_type(): string[] { 
		return this.getStringListValue("lord_base_type");
	}
	/** 战斗每天分享次数上限 */
	public static get fight_share_times_max_limit(): number { 
		return this.getNumberValue("fight_share_times_max_limit");
	}
	/** 领主阵营更新后免费转换次数 */
	public static get lord_camp_swap_free_times(): number { 
		return this.getNumberValue("lord_camp_swap_free_times");
	}
	/** 自动抽卡开启等级 */
	public static get fish_auto_level_limit(): number { 
		return this.getNumberValue("fish_auto_level_limit");
	}
	/** 自动抽卡开启vip等级 */
	public static get fish_auto_vip_limit(): number { 
		return this.getNumberValue("fish_auto_vip_limit");
	}
	/** 钓鱼初始道具 */
	public static get fish_init_reward(): number[] { 
		return this.getNumberListValue("fish_init_reward");
	}
	/** 审核服钓鱼初始道具 */
	public static get fish_review_init_reward(): number[] { 
		return this.getNumberListValue("fish_review_init_reward");
	}
	/** 钓鱼视频加速限制（形式：加速减少秒数|每日次数|每次间隔秒数） */
	public static get fish_quicken_video(): number[] { 
		return this.getNumberListValue("fish_quicken_video");
	}
	/** 钓鱼首充开放开服天数 */
	public static get fish_first_pay_open_day(): number { 
		return this.getNumberValue("fish_first_pay_open_day");
	}
	/** 钓鱼卡超级加速开放vip等级 */
	public static get fish_super_faster_vip(): number { 
		return this.getNumberValue("fish_super_faster_vip");
	}
	/** 钓鱼卡自动开启等级 */
	public static get fish_auto_open_level(): number { 
		return this.getNumberValue("fish_auto_open_level");
	}
	/** 钓鱼卡加速道具减少时间（单位/s） */
	public static get fish_quick_item_time(): number { 
		return this.getNumberValue("fish_quick_item_time");
	}
	/** 挂机奖励开始计算的卡牌阶级 */
	public static get hanging_start_fish_stage(): number { 
		return this.getNumberValue("hanging_start_fish_stage");
	}
	/** 每次钓鱼击鼓奖励 */
	public static get fish_drum_rewards(): number[] { 
		return this.getNumberListValue("fish_drum_rewards");
	}
	/** 鱼竿加速货币消耗（货币|数值） */
	public static get fish_rod_quicken_gold(): number[] { 
		return this.getNumberListValue("fish_rod_quicken_gold");
	}
	/** 鱼竿加速货币减少时间（单位/s） */
	public static get fish_rod_quicken_gold_time(): number { 
		return this.getNumberValue("fish_rod_quicken_gold_time");
	}
	/** 鱼竿加速货币冷却时间（单位/小时） */
	public static get fish_rod_quicken_gold_cd(): number { 
		return this.getNumberValue("fish_rod_quicken_gold_cd");
	}
	/** 渔场加速消耗（货币|数值），默认1秒消耗X钻石 */
	public static get fish_map_quicken_gold(): number[] { 
		return this.getNumberListValue("fish_map_quicken_gold");
	}
	/** 钓鱼系统计算战力的乘法系数 */
	public static get fish_power_ratio(): number { 
		return this.getNumberValue("fish_power_ratio");
	}
	/** 审核服底部系统图标开启-主公阶级限制 */
	public static get fish_review_open_stage(): number { 
		return this.getNumberValue("fish_review_open_stage");
	}
	/** 抽卡展示动画必现颜色（含以上） */
	public static get fish_lottery_must_show_color(): number { 
		return this.getNumberValue("fish_lottery_must_show_color");
	}
	/** 抽卡第一次显示最大颜色 */
	public static get fish_lottry_first_show_up(): number { 
		return this.getNumberValue("fish_lottry_first_show_up");
	}
	/** 抽卡第一次显示最小颜色 */
	public static get fish_lottry_first_show_down(): number { 
		return this.getNumberValue("fish_lottry_first_show_down");
	}
	/** 钓鱼界面主公X阶级前弹出（默认最优先）＜ */
	public static get fish_page_appear_limit(): number { 
		return this.getNumberValue("fish_page_appear_limit");
	}
	/** 钓鱼背包最大存储限制 */
	public static get fish_bag_num_limit(): number { 
		return this.getNumberValue("fish_bag_num_limit");
	}
	/** 钓鱼失败获得熟练度 */
	public static get fish_failed_get_exp(): number { 
		return this.getNumberValue("fish_failed_get_exp");
	}
	/** "渔场固定显示鱼的资源：资源id,比例,朝向（1右2左）" */
	public static get fish_fixed_res(): string { 
		return this.getStringValue("fish_fixed_res");
	}
	/** X颜色以上出售鱼会有二次确认 */
	public static get fish_sell_tips(): number { 
		return this.getNumberValue("fish_sell_tips");
	}
	/** 至尊召唤十连消耗物品 */
	public static get supreme_lottery_cost_item(): number[] { 
		return this.getNumberListValue("supreme_lottery_cost_item");
	}
	/** 至尊召唤十连保底出5星核心英雄数量（不配配置为0，心愿英雄有用到） */
	public static get supreme_lottery_min_core_num(): number { 
		return this.getNumberValue("supreme_lottery_min_core_num");
	}
	/** 至尊召唤十连最多出5星英雄数量（包含核心英雄数量） */
	public static get supreme_lottery_max_num(): number { 
		return this.getNumberValue("supreme_lottery_max_num");
	}
	/** 至尊召唤十连获得万灵融魂剂 */
	public static get supreme_lottery_gains_score(): number[] { 
		return this.getNumberListValue("supreme_lottery_gains_score");
	}
	/** 至尊召唤心愿英雄十连抽增加核心英雄库权重 */
	public static get supreme_lottery_wish_hero(): number { 
		return this.getNumberValue("supreme_lottery_wish_hero");
	}
	/** 至尊召唤心愿英雄十连抽多少次必出心愿英雄 */
	public static get supreme_lottery_wish_hero_times(): number { 
		return this.getNumberValue("supreme_lottery_wish_hero_times");
	}
	/** 鱼品质对应 */
	public static get fish_slot_type(): string { 
		return this.getStringValue("fish_slot_type");
	}
	/** 鱼颜色对应 */
	public static get fish_color_type(): string { 
		return this.getStringValue("fish_color_type");
	}
	/** 鱼泡中每个种类鱼的比例 */
	public static get fish_bubble_type_rate(): string { 
		return this.getStringValue("fish_bubble_type_rate");
	}
	/** 成神之路立绘对话文本内容 */
	public static get cszl_talk(): string { 
		return this.getStringValue("cszl_talk");
	}
	/** "1=逐字显示,2=直接显示" */
	public static get six_bless_show_type(): number { 
		return this.getNumberValue("six_bless_show_type");
	}
	/** 若没有开启自动闯关，则显示手指引导 */
	public static get auto_fight_guide_minlv(): number { 
		return this.getNumberValue("auto_fight_guide_minlv");
	}
	/** 若没有开启自动闯关，则显示手指引导 */
	public static get auto_fight_guide_maxlv(): number { 
		return this.getNumberValue("auto_fight_guide_maxlv");
	}
	/** (buff)技能1回合数转换毫秒数 */
	public static get td_trial_round_switch(): number { 
		return this.getNumberValue("td_trial_round_switch");
	}
	/** 英雄上阵限制星级（初始） */
	public static get td_trial_hero_star_limit(): number { 
		return this.getNumberValue("td_trial_hero_star_limit");
	}
	/** 小怪移速 */
	public static get td_trial_monster_speed(): number { 
		return this.getNumberValue("td_trial_monster_speed");
	}
	/** boss移速 */
	public static get td_trial_boss_speed(): number { 
		return this.getNumberValue("td_trial_boss_speed");
	}
	/** "小怪显示比例(0.3.2废弃,用地图编辑器配置)" */
	public static get td_trial_monster_show_rate(): number { 
		return this.getNumberValue("td_trial_monster_show_rate");
	}
	/** "boss显示比例(0.3.2废弃,用地图编辑器配置)" */
	public static get td_trial_boss_show_rate(): number { 
		return this.getNumberValue("td_trial_boss_show_rate");
	}
	/** 每波怪物中的出怪间隔（毫秒） */
	public static get td_trial_monster_interval(): number { 
		return this.getNumberValue("td_trial_monster_interval");
	}
	/** 塔防玩法每日闯关次数 */
	public static get td_trial_daily_challenge_times(): number { 
		return this.getNumberValue("td_trial_daily_challenge_times");
	}
	/** 塔防玩法最多累计次数 */
	public static get td_trial_max_challenge_times(): number { 
		return this.getNumberValue("td_trial_max_challenge_times");
	}
	/** 塔防玩法技能属性类14和24的减速比例（万分比） */
	public static get td_trial_skill_moderate_rate(): number { 
		return this.getNumberValue("td_trial_skill_moderate_rate");
	}
	/** 塔防玩法是否开启后台运行 */
	public static get td_trial_is_open_back_mode_new(): number { 
		return this.getNumberValue("td_trial_is_open_back_mode_new");
	}
	/** 塔防玩法如果开启后台运行，每帧间隔多久 */
	public static get td_trial_back_mode_frame_interval(): number { 
		return this.getNumberValue("td_trial_back_mode_frame_interval");
	}
	/** "塔防玩法战场最大速率,1则为不可调整(当前未开发完,请勿修改)" */
	public static get td_trial_max_speed_rate_new(): number { 
		return this.getNumberValue("td_trial_max_speed_rate_new");
	}
	/** 特惠庆典的图标ID（控制图标时间显示的问题） */
	public static get special_shop_icon_list(): string[] { 
		return this.getStringListValue("special_shop_icon_list");
	}
	/** 积分招募可选英雄数 */
	public static get lottery_type_score_select_hero_num(): number { 
		return this.getNumberValue("lottery_type_score_select_hero_num");
	}
	/** 免费找回50%资源（百分比） */
	public static get free_retrieval_rate(): number { 
		return this.getNumberValue("free_retrieval_rate");
	}
	/** 塔防/回合制X关后自动闯关开启 */
	public static get td_main_auto_fight_limit(): number { 
		return this.getNumberValue("td_main_auto_fight_limit");
	}
	/** 探索地图X关自动闯关开启 */
	public static get building_auto_fight_limit(): number { 
		return this.getNumberValue("building_auto_fight_limit");
	}
	/** X关后开启退出战斗 */
	public static get td_main_exit_limit(): number { 
		return this.getNumberValue("td_main_exit_limit");
	}
	/** X关后出现招募图标 */
	public static get td_main_lottery_open(): number { 
		return this.getNumberValue("td_main_lottery_open");
	}
	/** 征战塔防闯关任务倒计时（废弃） */
	public static get td_main_pass_mission_countdown(): number { 
		return this.getNumberValue("td_main_pass_mission_countdown");
	}
	/** 征战塔防闯关任务出现条件：X关后 */
	public static get td_main_pass_mission_icon_limit(): number { 
		return this.getNumberValue("td_main_pass_mission_icon_limit");
	}
	/** X关开启战斗加速显示 */
	public static get td_main_fight_speed_open(): number { 
		return this.getNumberValue("td_main_fight_speed_open");
	}
	/** 征战第1关中X秒弹出领主对话框 */
	public static get td_main_lord_talk(): string { 
		return this.getStringValue("td_main_lord_talk");
	}
	/** 征战X关后才出现下阵按钮 */
	public static get td_main_replac_limit(): number { 
		return this.getNumberValue("td_main_replac_limit");
	}
	/** 征战X关后取消战斗中的技能图标（征战和副本都要取消） */
	public static get td_main_skill_icon_limit(): number { 
		return this.getNumberValue("td_main_skill_icon_limit");
	}
	/** "征战地图比例(0.3.2废弃,移到cfg_td_map)" */
	public static get td_main_map_rate(): number { 
		return this.getNumberValue("td_main_map_rate");
	}
	/** "征战地图字体比例(0.3.2废弃,移到cfg_td_map)" */
	public static get td_main_map_word_rate(): number { 
		return this.getNumberValue("td_main_map_word_rate");
	}
	/** "征战地图Y轴偏移(0.3.2废弃,移到cfg_td_map)" */
	public static get td_main_map_pos_y(): number { 
		return this.getNumberValue("td_main_map_pos_y");
	}
	/** 军衔界面功能预览自动旋转开启军衔阶级（大于） */
	public static get functionsysItem_autorotate_opencardstage(): number { 
		return this.getNumberValue("functionsysItem_autorotate_opencardstage");
	}
	/** 百服好友功能需要月卡类型 */
	public static get cross_dominate_chat_need_yeuka_type(): number { 
		return this.getNumberValue("cross_dominate_chat_need_yeuka_type");
	}
	/** 百服频道需要vip等级 */
	public static get cross_dominate_chat_need_vip(): number { 
		return this.getNumberValue("cross_dominate_chat_need_vip");
	}
	/** 百服好友添加限制提示 */
	public static get cross_dominate_add_friend_tip(): string { 
		return this.getStringValue("cross_dominate_add_friend_tip");
	}
	/** 百服争霸消耗挑战道具 */
	public static get dominate_pvp_cost_item_id(): number { 
		return this.getNumberValue("dominate_pvp_cost_item_id");
	}
	/** 百服争霸每日匹配相同角色次数 */
	public static get cross_dominate_pvp_match_same_role_count(): number { 
		return this.getNumberValue("cross_dominate_pvp_match_same_role_count");
	}
	/** 百服争霸48小时未登录无法匹配到 */
	public static get cross_dominate_pvp_no_match_hour(): number { 
		return this.getNumberValue("cross_dominate_pvp_no_match_hour");
	}
	/** 百服争霸上半赛季持续时间(单位：天) */
	public static get dominate_pvp_first_half_days(): number { 
		return this.getNumberValue("dominate_pvp_first_half_days");
	}
	/** 百服争霸中赛季持续时间(单位：天) */
	public static get dominate_pvp_second_half_days(): number { 
		return this.getNumberValue("dominate_pvp_second_half_days");
	}
	/** 百服争霸下半赛季持续时间(单位：天) */
	public static get dominate_pvp_three_half_days(): number { 
		return this.getNumberValue("dominate_pvp_three_half_days");
	}
	/** 百服争霸休战赛季持续时间(单位：天) */
	public static get dominate_pvp_stop_days(): number { 
		return this.getNumberValue("dominate_pvp_stop_days");
	}
	/** 百服争霸空队伍提示 */
	public static get dominate_pvp_line_up_empty_tip(): string { 
		return this.getStringValue("dominate_pvp_line_up_empty_tip");
	}
	/** 百服争霸报名时间持续时间(单位：天) */
	public static get dominate_sign_days(): number { 
		return this.getNumberValue("dominate_sign_days");
	}
	/** 百服争霸战斗时间持续时间(单位：天) */
	public static get dominate_fight_days(): number { 
		return this.getNumberValue("dominate_fight_days");
	}
	/** 百服争霸商店开启时间持续时间(单位：天) */
	public static get dominate_shop_days(): number { 
		return this.getNumberValue("dominate_shop_days");
	}
	/** 百服争霸休战点击匹配按钮时提示 */
	public static get dominate_truce_tips(): string { 
		return this.getStringValue("dominate_truce_tips");
	}
	/** 百服服务器分组底部描述 */
	public static get large_peak_server_list_desc(): string { 
		return this.getStringValue("large_peak_server_list_desc");
	}
	/** 下赛季剩余几天时，每日首次进入百服争霸都弹出百服预告 */
	public static get dominate_show_large_peak_notice_days(): number { 
		return this.getNumberValue("dominate_show_large_peak_notice_days");
	}
	/** 百服争霸连续失败2次，下次必定匹配同段位机器人 */
	public static get dominate_pvp_number_of_consecutive_failures(): number { 
		return this.getNumberValue("dominate_pvp_number_of_consecutive_failures");
	}
	/** 英雄-献祭剩余多少个背包格子弹窗提示 */
	public static get hero_recycle_remaining_bag(): number { 
		return this.getNumberValue("hero_recycle_remaining_bag");
	}
	/** 通用领主可升星最大星级 */
	public static get niversal_lord_max_star(): number { 
		return this.getNumberValue("niversal_lord_max_star");
	}
	/** 王者月卡竞渡特权直购享受2折优惠 */
	public static get boat_privilege_discount(): number { 
		return this.getNumberValue("boat_privilege_discount");
	}
	/** 王者月卡，直接给福利礼包x2 竞渡特权直购享受6折优惠 */
	public static get boat_privilege_discount_need_yeuka_type(): number { 
		return this.getNumberValue("boat_privilege_discount_need_yeuka_type");
	}
	/** 征战X关后显示领主预览 */
	public static get lord_preview_display(): number { 
		return this.getNumberValue("lord_preview_display");
	}
	/** 主线征战战斗失败的提示:  间隔毫秒|显示时长毫秒 */
	public static get td_main_fail_lord_talk_interval(): string[] { 
		return this.getStringListValue("td_main_fail_lord_talk_interval");
	}
	/** 主线征战战斗失败的提示 */
	public static get td_main_fail_lord_talk_text(): string { 
		return this.getStringValue("td_main_fail_lord_talk_text");
	}
	/** 主线征战的领主喊话的间隔秒数 */
	public static get td_monster_talk_interval(): number { 
		return this.getNumberValue("td_monster_talk_interval");
	}
	/** 塔防扩展配置 */
	public static get td_ext(): string { 
		return this.getStringValue("td_ext");
	}
	/** >=10级，显示招募下方的标签按钮 */
	public static get lottery_btn_tab_show_lv(): number { 
		return this.getNumberValue("lottery_btn_tab_show_lv");
	}
	/** 英雄操作窗口X秒后关闭 */
	public static get td_main_icon_disappear(): number { 
		return this.getNumberValue("td_main_icon_disappear");
	}
	/** 英雄攻击范围显示时长 */
	public static get td_main_atk_range_duration(): number { 
		return this.getNumberValue("td_main_atk_range_duration");
	}
	/** 主线征战在21关之后，显示dps战斗面板 */
	public static get td_main_show_dps_pass_limit(): number { 
		return this.getNumberValue("td_main_show_dps_pass_limit");
	}
	/** 限时悬赏一键完成剩余X天开启 */
	public static get activity_wall_countdown(): number { 
		return this.getNumberValue("activity_wall_countdown");
	}
	/** 战场数量 */
	public static get dominate_pvp_battlefield_list(): number { 
		return this.getNumberValue("dominate_pvp_battlefield_list");
	}
	/** 战场名字 */
	public static get dominate_pvp_battlefield_name(): string[] { 
		return this.getStringListValue("dominate_pvp_battlefield_name");
	}
	/** 新翼战场成神等级要求区间 */
	public static get dominate_pvp_battlefield_1_god_stage(): string[] { 
		return this.getStringListValue("dominate_pvp_battlefield_1_god_stage");
	}
	/** 砺刃战场成神等级要求区间 */
	public static get dominate_pvp_battlefield_2_god_stage(): string[] { 
		return this.getStringListValue("dominate_pvp_battlefield_2_god_stage");
	}
	/** 破空战场成神等级要求区间 */
	public static get dominate_pvp_battlefield_3_god_stage(): string[] { 
		return this.getStringListValue("dominate_pvp_battlefield_3_god_stage");
	}
	/** 银辉战场成神等级要求区间 */
	public static get dominate_pvp_battlefield_4_god_stage(): string[] { 
		return this.getStringListValue("dominate_pvp_battlefield_4_god_stage");
	}
	/** 金芒战场成神等级要求区间 */
	public static get dominate_pvp_battlefield_5_god_stage(): string[] { 
		return this.getStringListValue("dominate_pvp_battlefield_5_god_stage");
	}
	/** 圣耀战场成神等级要求区间 */
	public static get dominate_pvp_battlefield_6_god_stage(): string[] { 
		return this.getStringListValue("dominate_pvp_battlefield_6_god_stage");
	}
	/** 巅峰战场成神等级要求区间 */
	public static get dominate_pvp_battlefield_7_god_stage(): string[] { 
		return this.getStringListValue("dominate_pvp_battlefield_7_god_stage");
	}
	/** 低战场变高战场积分转化系数（万分比） */
	public static get dominate_pvp_battlefield_change_scroe_reset_rate(): number { 
		return this.getNumberValue("dominate_pvp_battlefield_change_scroe_reset_rate");
	}
	/** 领主庆典（鬼刹犀角魔cfg_lord_base） */
	public static get lord_celebration_1(): number { 
		return this.getNumberValue("lord_celebration_1");
	}
	/** 领主庆典终极大奖次数（鬼刹犀角魔） */
	public static get lord_celebration_times_1(): number { 
		return this.getNumberValue("lord_celebration_times_1");
	}
	/** 神临装备开启条件：该英雄X星 */
	public static get divine_equip_open(): number { 
		return this.getNumberValue("divine_equip_open");
	}
	/** 神临装备激活条件：成神X阶 */
	public static get divine_equip_activation(): number { 
		return this.getNumberValue("divine_equip_activation");
	}
	/** 神临装备的颜色配置（特殊，99就代表神临装备） */
	public static get divine_equip_color(): number { 
		return this.getNumberValue("divine_equip_color");
	}
	/** 神临装备分解列表显示道具(套装id) */
	public static get divine_equip_break_list(): string[] { 
		return this.getStringListValue("divine_equip_break_list");
	}
	/** 神临副本扫荡道具 */
	public static get divine_copy_mop_up_item(): number { 
		return this.getNumberValue("divine_copy_mop_up_item");
	}
	/** 神临装备主属性列表（筛选用） */
	public static get divine_equip_main_attrs(): string[] { 
		return this.getStringListValue("divine_equip_main_attrs");
	}
	/** 神临装备副属性列表（筛选用） */
	public static get divine_equip_assist_attrs(): string[] { 
		return this.getStringListValue("divine_equip_assist_attrs");
	}
	/** 神临装备熔炼所需件数 */
	public static get divine_smelt_need_items(): number { 
		return this.getNumberValue("divine_smelt_need_items");
	}
	/** 神临装备高级熔炼去除1条属性额外新增消耗件数 */
	public static get divine_smelt_remove_cost(): number { 
		return this.getNumberValue("divine_smelt_remove_cost");
	}
	/** 神临装备高级熔炼可去除属性条数 */
	public static get divine_senior_smelt_remove_attrs(): number { 
		return this.getNumberValue("divine_senior_smelt_remove_attrs");
	}
	/** 神临装备高级熔炼免费次数 */
	public static get divine_senior_smelt_free_times(): number { 
		return this.getNumberValue("divine_senior_smelt_free_times");
	}
	/** 神临装备道具增加高级熔炼次数 */
	public static get divine_senior_smelt_item(): number { 
		return this.getNumberValue("divine_senior_smelt_item");
	}
	/** 神临装备熔炼特定道具 */
	public static get divine_senior_smelt_special_item(): number { 
		return this.getNumberValue("divine_senior_smelt_special_item");
	}
	/** 王者月卡类型|增加次数 */
	public static get divine_senior_smelt_yueka_times(): number[] { 
		return this.getNumberListValue("divine_senior_smelt_yueka_times");
	}
	/** 神临装备分解获得经验比例 */
	public static get divine_break_exp_rate(): number { 
		return this.getNumberValue("divine_break_exp_rate");
	}
	/** 神临装备分解获得英雄本体比例 */
	public static get divine_break_hero_rate(): number { 
		return this.getNumberValue("divine_break_hero_rate");
	}
	/** 神临副本空队伍提示 */
	public static get divine_copy_line_up_empty_tip(): string { 
		return this.getStringValue("divine_copy_line_up_empty_tip");
	}
	/** 神临装备确认窗口描述 */
	public static get divine_copy_reset_desc(): string { 
		return this.getStringValue("divine_copy_reset_desc");
	}
	/** 神临装备重置消耗钻石（实际*10） */
	public static get divine_copy_reset_cost(): number { 
		return this.getNumberValue("divine_copy_reset_cost");
	}
	/** 资源懒加载开关(角色等级小于等于这个值则开启) */
	public static get lazy_load_res_open_level(): number { 
		return this.getNumberValue("lazy_load_res_open_level");
	}
	/** UI弹出动效开关 */
	public static get is_ui_pop_effect_open(): number { 
		return this.getNumberValue("is_ui_pop_effect_open");
	}
	/** 王者月卡跳过广告特权需要月卡类型 */
	public static get ad_shop_skip_ad_need_yeuka_type(): number { 
		return this.getNumberValue("ad_shop_skip_ad_need_yeuka_type");
	}
	/** 王者月卡跳过广告特权描述 */
	public static get yueka_skip_ad_desc(): string { 
		return this.getStringValue("yueka_skip_ad_desc");
	}
	/** 广告商品冷却时间/秒 */
	public static get ad_shop_goods_cooling(): number { 
		return this.getNumberValue("ad_shop_goods_cooling");
	}
	/** 开启广告商店和王者月卡跳过广告特权的平台列表 */
	public static get ad_shop_open_platforms(): string[] { 
		return this.getStringListValue("ad_shop_open_platforms");
	}
	/** 死骑来了英雄展示的坐标(立绘ID|X|Y|缩放比率|英雄描述图片) */
	public static get hero_come_mission_1(): string[] { 
		return this.getStringListValue("hero_come_mission_1");
	}
	/** 道具id|开服天数|打开的界面 */
	public static get silver_special_link(): string[] { 
		return this.getStringListValue("silver_special_link");
	}
	/** 道具id|开服天数|打开的界面 */
	public static get hero_exp_special_link(): string[] { 
		return this.getStringListValue("hero_exp_special_link");
	}
	/** 试练塔自动挑战开启层数 */
	public static get test_tower_auto_need_floor(): number { 
		return this.getNumberValue("test_tower_auto_need_floor");
	}
	/** 超值首充开启的开服日期 */
	public static get first_pay_start_open_day(): number { 
		return this.getNumberValue("first_pay_start_open_day");
	}
	/** 超值首充结束的开服日期 */
	public static get first_pay_end_open_day(): number { 
		return this.getNumberValue("first_pay_end_open_day");
	}
	/** 积分招募抵扣道具 */
	public static get lottery_type_score_cost_item(): number[] { 
		return this.getNumberListValue("lottery_type_score_cost_item");
	}
	/** 日常副本-中心塔防怪物攻击范围 */
	public static get td_daily_copy_monster_range(): number { 
		return this.getNumberValue("td_daily_copy_monster_range");
	}
	/** 新每日特惠返利双倍奖励的月卡类型 */
	public static get daily_new_discount_rebate_need_yeuka_type(): number { 
		return this.getNumberValue("daily_new_discount_rebate_need_yeuka_type");
	}
	/** 游商商人上架商品数量 */
	public static get itinerant_shop_goods_num(): number { 
		return this.getNumberValue("itinerant_shop_goods_num");
	}
	/** 游商刷新商品时间 */
	public static get itinerant_shop_start_weeks(): string[] { 
		return this.getStringListValue("itinerant_shop_start_weeks");
	}
	/** 挂机建造扩展参数(通过游戏内编辑器配置和解析) */
	public static get guaji_building_misc(): string { 
		return this.getStringValue("guaji_building_misc");
	}
	/** 领主装备合成所需数量 */
	public static get lord_compose_need_num(): number { 
		return this.getNumberValue("lord_compose_need_num");
	}
	/** 新手引导获得第一个武将 */
	public static get new_guide_first_heroes(): string[] { 
		return this.getStringListValue("new_guide_first_heroes");
	}
	/** 新手引导获得其他武将 */
	public static get new_guide_other_heroes(): string[] { 
		return this.getStringListValue("new_guide_other_heroes");
	}
	/** "探索剧情的武将显示（从左到右,前3个是混沌阵营,后3个是秩序阵营）" */
	public static get build_plot_heros(): string[] { 
		return this.getStringListValue("build_plot_heros");
	}
	/** 探索地图工人工作时间（毫秒） */
	public static get build_work_time(): number { 
		return this.getNumberValue("build_work_time");
	}
	/** 探索地图工人原地类型飘字（毫秒） */
	public static get build_work_item_time(): number { 
		return this.getNumberValue("build_work_item_time");
	}
	/** 探索地图建筑获取途径id（兽族|人族） */
	public static get build_worker_get_way(): string[] { 
		return this.getStringListValue("build_worker_get_way");
	}
	/** 永久招募卡最大存储周数 */
	public static get permanent_yueka_save_weeks(): number { 
		return this.getNumberValue("permanent_yueka_save_weeks");
	}
	/** 领主宝物重置消耗 */
	public static get lord_treasure_reset_cost(): string { 
		return this.getStringValue("lord_treasure_reset_cost");
	}
}