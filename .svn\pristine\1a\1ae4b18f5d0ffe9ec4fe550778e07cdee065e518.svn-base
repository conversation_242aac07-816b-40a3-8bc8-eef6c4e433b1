import { Laya } from "Laya";
import { Node } from "laya/display/Node";
import { Sprite } from "laya/display/Sprite";
import { Event } from "laya/events/Event";
import { Point } from "laya/maths/Point";
import { Button } from "laya/ui/Button";
import { Tween } from "laya/utils/Tween";
import { GlobalConfig } from "../../../../game/GlobalConfig";
import { UrlConfig } from "../../../../game/UrlConfig";
import { MatchConst } from "../../../auto/ConstAuto";
import { MiscConstAuto } from "../../../auto/MiscConstAuto";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { cfg_activity_icon } from "../../../cfg/vo/cfg_activity_icon";
import { cfg_main_battle } from "../../../cfg/vo/cfg_main_battle";
import { ConfigManager } from "../../../managers/ConfigManager";
import { DispatchManager } from "../../../managers/DispatchManager";
import { LayerManager } from "../../../managers/LayerManager";
import { p_first_pay_info } from "../../../proto/common/p_first_pay_info";
import { p_simp_mission } from "../../../proto/common/p_simp_mission";
import { m_building_info_toc } from "../../../proto/line/m_building_info_toc";
import { m_fight_simp_result_toc } from "../../../proto/line/m_fight_simp_result_toc";
import { com } from "../../../ui/layaMaxUI";
import { ColorUtil } from "../../../util/ColorUtil";
import { ConsoleUtils } from "../../../util/ConsoleUtils";
import { DateUtil } from "../../../util/DateUtil";
import { GameUtil } from "../../../util/GameUtil";
import { StringUtil } from "../../../util/StringUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { TweenUtil } from "../../../util/TweenUtil";
import { BaseDialog, CommonButton, DialogNavShow } from "../../BaseDialog";
import { GSkeleton } from "../../baseModules/skeleton/GSkeleton";
import { ESkeletonType } from "../../baseModules/skeleton/SkeletonData";
import { SkeletonManager } from "../../baseModules/skeleton/SkeletonManager";
import { UIHTMLDiv } from "../../baseModules/UIHTMLDiv";
import { HandEff } from "../../common/HandEff";
import { DataCenter } from "../../DataCenter";
import { FightDataCenter } from "../../fight/data/FightDataCenter";
import GoodsItem from "../../goods/GoodsItem";
import { GoodsVO } from "../../goods/GoodsVO";
import { BUILD_SHOW_TYPE, GuaJiDataCenter } from "../../guaJi/data/GuaJiDataCenter";
import { MainBattleView } from "../../guaJi/view/MainBattleView";
import { GJFlyIcon, GJFlyType } from "../../guajiMap/view/GJFlyIcon";
import { GuideMgr } from "../../guide/GuideMgr";
import { LineUpVO } from "../../lineUp/vo/LineUpVO";
import { LordDataCenter } from "../../Lord/data/LordDataCenter";
import LordPreviewView from "../../Lord/view/LordPreviewView";
import { MainIconConfig } from "../../menu/MainIconConfig";
import { EMainUIBottomIndex } from "../../menu/MainUIManager";
import MainTopIconLayer2 from "../../menu/view/MainTopIconLayer2";
import MenuActivityIconView from "../../menu/view/MenuActivityIconView";
import { MiscConst } from "../../misc_config/MiscConst";
import MissionConst, { MissionStatus } from "../../mission/MissionConst";
import { ModuleCommand } from "../../ModuleCommand";
import { OnlineRewardDataCenter } from "../../onlineReward/data/OnlineRewardDataCenter";
import { MainIconOnlineRewardView } from "../../onlineReward/view/MainIconOnlineRewardView";
import { PanelEventConstants } from "../../PanelEventConstants";
import VipTeQuanUtil from "../../payment/VipTeQuanUtil";
import { RechargeDataCenter } from "../../recharge/data/RechargeDataCenter";
import { RedPointMgr } from "../../redPoint/RedPointMgr";
import RedPointVo from "../../redPoint/RedPointVo";
import { SettingDataCenter } from "../../setting/data/SettingDataCenter";
import { TdMainFingerMoveScript } from "../../tdMain/scripts/TdMainFingerMoveScript";
import { TdMainGuajiScript } from "../../tdMain/scripts/TdMainGuajiScript";
import { TdMainMissionLotteryScript } from "../../tdMain/scripts/TdMainMissionLotteryScript";
import { TdMainLeftIconLayer2 } from "../../tdMain/view/TdMainLeftIconLayer2";
import TdMainRightBottomIconLayer from "../../tdMain/view/TdMainRightBottomIconLayer";
import { BuildMissionDataCenter } from "../data/BuildMissionDataCenter";
import { GuaJiBuildDataCenter } from "../data/GuaJiBuildDataCenter";
import { GuajiBuildEditorView } from "../editor/GuajiBuildEditorView";
import { GuajiBuildGuideScript } from "../guide/GuajiBuildGuideScript";
import { GuajiBuildGuideScript_guide_story_3000011 } from "../guide/GuajiBuildGuideScript_guide_story_3000011";
import { GuajiBuildGuideScript_guide_wakeup_300001 } from "../guide/GuajiBuildGuideScript_guide_wakeup_300001";
import { GuajiBuildMissionGuideScript } from "../guide/GuajiBuildMissionGuideScript";
import GuaJiBuildView from "../view/GuaJiBuildView";
import { UIUtil } from "../../../util/UIUtil";

//界面类都是每次打开每次新建的
export default class GuaJiDialog extends com.ui.res.guaJiBuild.GuaJiDialogUI {

    private static isBoxLeftTopExpand = true;

    private _rate: number = 1;
    private gjFlyIconList: GJFlyIcon[] = [];

    protected onlineRewardView: MainIconOnlineRewardView;
    public leftIconLayer: TdMainLeftIconLayer2;
    protected rightBottomIconLayer: TdMainRightBottomIconLayer;
    protected rightTopIconLayer: MainTopIconLayer2;
    // protected uiProgress: UIProgressBarEx;
    protected itemMission: GoodsItem;
    protected skGuajiReward: GSkeleton;
    //宝箱
    protected skBox: GSkeleton;

    /**非战斗挂机脚本 */
    public guajiScript: TdMainGuajiScript;
    /**任务引导脚本 */
    public missionGuideScript: GuajiBuildMissionGuideScript;
    /**手指滑动脚本 */
    public fingerMoveScript: TdMainFingerMoveScript;
    /**千抽动画脚本 */
    public missionLotteryScript: TdMainMissionLotteryScript;
    /** 新英雄登场引导脚本 */
    public newGuideScript: GuajiBuildGuideScript;
    /**通关奖励按钮 */
    public btnPassReward: MenuActivityIconView;
    public _LordPreviewView: LordPreviewView;

    public buildView: GuaJiBuildView;

    public editorView: GuajiBuildEditorView;
    /** 自动按钮特效 */
	private autoLiuGunang: GSkeleton;
    //------------挂机状态切换--------
    private _nowShowType: number = 1;
    private _oldShowType: number = -1;
    public get nowShowType(): number {
        return this._nowShowType;
    }
    public set nowShowType(value: number) {
        GuaJiDataCenter.instance.guaJiDialogShowType = value;
        this._nowShowType = value;
    }
    private battleView: MainBattleView;

    private oldLeftTopExpand:boolean = null;

    // protected guidePass = 0;
    /**探索按钮 */
    private btnTravel: MenuActivityIconView;
    public get isGuideAllFinish() {
        return GuaJiBuildDataCenter.instance.isGuideAllFinish;
    }

    constructor(param: any = null) {
        super();
        this.navShow = DialogNavShow.BOTH;
        // this.width = this.relativeWidth;
        this.isFullScreen = true;
        this.isShowDebugTitle = false//GlobalConfig.is_LOCAL_DEBUG;
        // this["_hold_"] = true;
        // this.isSingle = true;

        if(GlobalConfig.IsDebug){
            this.drawCallOptimize = false;
        }
    }

    protected _setParent(value: Node): void {
        super._setParent(value);

        // if(!this.destroyed){
        //     if (value) {
        //         this.timer.clear(this, this.checkIsBackEndGame);
        //     } else {
        //         this.timer.once(5000, this, this.checkIsBackEndGame);
        //     }
        //     this.dispatchEvent(ModuleCommand.GUAJI_BUILD_ON_BACK_MODE, !value);
        // }
    }

    private checkIsBackEndGame() {
        if (!this.parent) {
            this.close();
        }
    }


    /** 现在显示的关卡 */
    public get curShowPass(): number {
        return GuaJiDataCenter.instance.curGuajiPass;
    }

    /** 真实通关的关卡 */
    public get curPass(): number {
        return GuaJiDataCenter.instance.curPass;
    }

    initUI(): void {

        UIHTMLDiv.SetUIHTMLDiv(this.htmlMissionDesc, 20, ColorUtil.GOLD_COLOR);
        UIHTMLDiv.setAlignCenter(this.htmlMissionDesc);

        UIHTMLDiv.SetUIHTMLDiv(this.htmlMissionProgress, 20, ColorUtil.GOLD_COLOR);
        UIHTMLDiv.setAlignCenter(this.htmlMissionProgress);

        // UIHTMLDiv.SetUIHTMLDiv(this.htmlGuideFinger, 20, ColorUtil.FONT_NORAML, 5);
        // this.htmlGuideFinger.innerHTML = window.iLang.L2_AN_ZHU_HERO_TUO_DONG_ch31_KE_JIN_XING_ZHAN_WEI_TIAO_ZHENG_O_ch01.il();
        // UIHTMLDiv.setAlignCenterAndValignMid(this.htmlGuideFinger);
        // this.boxGuideFinger.visible = false;

        // this.boxGuideFinger.mouseEnabled = false;

        this.y = 0;
        this.height = this.relativeHeight;
        this.mouseThrough = false;

        this.topbox.top = DataCenter.excursion_y + 170;
        this.topbox.mouseThrough = true;
        // this.btnQuickFight.visible = GlobalConfig.showRecharge;

        this.boxLeftTop.mouseThrough = true;

        this.btnDebug.visible = GlobalConfig.IsDebug;

        this.battleView = new MainBattleView();
        this.battleView.visible = false;
        this.battleView.zOrder = -1;
        this.addChild(this.battleView);
    }

    
    onOpen() {

        //千抽预告
        // if (GuaJiDataCenter.firstEnterDialog == false && this.curShowPass <= 1) {
        //     this.dispatchEvent(ModuleCommand.OPEN_MISSION_LOTTRY_PREVIEW_DIALOG);
        // }

        let isNewHeroGuide = this.isGuideAllFinish == false;
        if (isNewHeroGuide) {
            this.newGuideScript = this.addComponent(GuajiBuildGuideScript);
            this.newGuideScript.init(GuaJiDataCenter.firstEnterDialog == false, this);

            let isFinish = GuideMgr.ins.isFinishMission(GuajiBuildGuideScript.guide_wakeup_300001);
            if(isFinish == false){
                GuideMgr.ins.setGranchGuideId(GuajiBuildGuideScript.guide_wakeup_300001);
            }else{
                isFinish = GuideMgr.ins.isFinishMission(GuajiBuildGuideScript.guide_story_3000011);
                if(isFinish == false){
                    GuideMgr.ins.setGranchGuideId(GuajiBuildGuideScript.guide_story_3000011);
                }
            }
        }
       
        GuaJiDataCenter.firstEnterDialog = true;

        this.missionLotteryScript = this.addComponent(TdMainMissionLotteryScript);
        this.missionGuideScript = this.addComponent(GuajiBuildMissionGuideScript);

        this.dispatchEvent(ModuleCommand.CLOSE_GUA_JI_WROLD_MAP_DIALOG);  //关闭挂机界面
        if (!GuaJiDataCenter.instance.main_battle_info) {
            GuaJiDataCenter.instance.m_main_battle_info_tos();
        }

        this.initViewShow();
        this.checkOpenState();
        this.updateMissionProgress();
        // this.showGuaJiPlayer();
        this.updateGuaJiInfo();
        this.showRedPoint();

        this.checkGuideViewShow();

        this.timer.once(200, this, this.checkGuide);
        this.timer.frameLoop(1, this, this.updateFrame);
        //添加倒计时
        this.timer.loop(1000, this, this.updateTime).runImmediately();

        this.changeShowType(BUILD_SHOW_TYPE.BUILD);
        this.showBottomMainIcon();
        //默认两倍速
        // FightDataCenter.instance.m_fight_set_speed_tos(2);

        this.checkFirstRecharge();
    }

    addClick(): void {
        this.addOnClick(this, this.startgamebtn, this.onStartGameClick);
        this.addOnClick(this, this.stopgamebtn, this.onStopGameClick);
        this.addOnClick(this, this.ratebtn, this.onClickRateBtn);
        this.addOnClick(this, this.btnQuickFight, this.onClickQuickFight);
        this.addOnClick(this, this.btnBox, this.onClickMainBattleBox);
        this.addOnClick(this, this.lotterbtn, this.onClickLotteryBtn);

        //this.cbFightMode.visible = false;
        this.addOnClick(this, this.guajiBtn, this.onGuajiBtnClick);
        this.addOnClick(this, this.backBtn, this.changeShowType, Event.CLICK, [BUILD_SHOW_TYPE.BUILD]);

        this.addOnClick(this, this.boxMission, this.onClickMission);
        this.addOnClick(this, this.lineupBtn, this.onClickShow);
        this.addOnClick(this, this.btnLeftTopExpand, this.onClickLeftTopExpand);
    }

    addEvent() {

        this.addEventListener(ModuleCommand.UPDATE_GUA_JI_INFO, this, this.updateGuaJiInfo);
        this.addEventListener(ModuleCommand.LEVEL_UP, this, this.checkOpenState);
        this.addEventListener(ModuleCommand.UPDATE_XUNZHANG_DIALOG, this, this.checkOpenState);
        this.addEventListener(ModuleCommand.UPDATE_MAIN_BATTLE_INFO, this, this.updateMainBattleInfo);

        this.addEventListener(ModuleCommand.MAIN_MISSION_UPDATE + MissionConst.MISSION_TYPE_BUILDING, this, this.updateMissionProgress);
        this.addEventListener(ModuleCommand.ON_MAIN_BATTLE_FIGHT_SUCC, this, this.onFightResult);
        this.addEventListener(ModuleCommand.RED_CHILD_CHANGE, this, this.refreshRedPoint);

        this.addEventListener(ModuleCommand.GUIDE_END_ID, this, this.onGuideEnd);
        // this.addEventListener(ModuleCommand.ON_CLOSE_TD_GUIDE_NEW_HERO_DIALOG, this, this.onCloseNewHeroDialog);

        this.addEventListener(ModuleCommand.ON_OPEN_OR_CLOSE_DIALOG, this, this.onOpenOrCloseDialog);
        this.addEventListener(ModuleCommand.ON_CLOSE_MISSION_LOTTRY_PREVIEW_DIALOG, this, this.onCloseLotteryPreview);
        this.addEventListener(ModuleCommand.UPDATE_MAIN_BATTLE_MISSION_STATUS, this, this.refBtnPassReward);
        this.addEventListener(ModuleCommand.UPDATE_LORD_PREVIEW_COMPLETE, this, this.onRefreshLordPreviewInfo);

        this.addEventListener(ModuleCommand.UPDATE_MAIN_BATTLE_AUTO, this, this.refreshStartGameBtn);
        this.addEventListener(ModuleCommand.BEGAN_RUN_GUAJI, this, this.startGuaJi);
       
        this.addEventListener(ModuleCommand.BEGAN_RUN_MAIN_BATTLE, this, this.startBattle);
        this.addEventListener(ModuleCommand.GUA_JI_TOWER_AWARD, this, this.onFlyIcon);
        this.addEventListener(ModuleCommand.UPDATE_RATE, this, this.updateRate);
        this.addEventListener(ModuleCommand.GUAJI_BUILD_INFO, this, this.onGuaJiBuildInfo);
        this.addEventListener(ModuleCommand.GUIDE_SHIP_CHANGE, this, this.checkGuide);
        this.addEventListener(ModuleCommand.MAIN_BATTLE_FIGHT_RESULT, this, this.mainBattleFightResult);
        this.addEventListener(ModuleCommand.UPDATE_MASTER_CART_STAGE, this, this.refBtnTravel);
        this.addEventListener(ModuleCommand.ON_MAIN_TAB_CHANGER, this, this.openBottomModule);
    }

    private onGuaJiBuildInfo(info: m_building_info_toc): void {

    }

    //---------------------------显示逻辑------------
    private initViewShow() {

        if(this.destroyed){
            return;
        }

        this.boxMissionCacheAs.autoCacheAsBitMap = true;

        this.btnDebug.visible = false;
        this.stopgamebtn.visible = false;

        this.boxLeftTop.pos(10, DataCenter.excursion_y + 140);
        this.lineupBtn.pos(700, -40);
        // let rateBtnY = Math.min(this.rightTopIconLayer.y + 480, )

        let y = Math.max(this.relativeHeight - this.bottombox.height - 250, this.relativeHeight / 2);
        this.ratebtn.pos(660, y);
        this.ratebtn.zOrder = 999;

        //没用了
        // this.onekeyBtn.visible = false;//this.nowPass >= 1;
        // this.onekeyBtn.y = -185;

        //在线奖励
        this.onlineRewardView = OnlineRewardDataCenter.addOnlineRewardView(this.boxLeftTop, 0, 0);
        // this.TdMainLeftIconLayer2 = new TdMainLeftIconLayer2();

        this.leftIconLayer = new TdMainLeftIconLayer2();
        // this.leftIconLayer.zOrder = LayerManager.STAGE_ZORDE_TOPEST;
        // this.leftIconLayer.pos(0, 150);
        //iconLayer.top = 120;
        this.boxLeftTop.addChild(this.leftIconLayer);

        /**招募 */
        //this.lotterbtn.pos(80, 290);
        // this.lotterbtn.pos(90, this.boxLeftTop.y + 290);
        let eff_lotter = this.lotterbtn.getChildByName("zhengzhan_recruit") as GSkeleton;
        if (!eff_lotter) {
            eff_lotter = SkeletonManager.ins.createSkeleton("zhengzhan_recruit", ESkeletonType.UI_EFFECT, { isLoop: true });
            eff_lotter.pos(this.lotterbtn.width / 2, this.lotterbtn.height / 2);
            eff_lotter.name = "zhengzhan_recruit";
            this.lotterbtn.addChild(eff_lotter);
        }

        /**右下 */
        this.rightBottomIconLayer = new TdMainRightBottomIconLayer();
        this.rightBottomIconLayer.zOrder = LayerManager.STAGE_ZORDE_TOPEST;
        this.rightBottomIconLayer.updateView();
        this.rightBottomIconLayer.bottom = 130;
        this.addChild(this.rightBottomIconLayer);

        this.rightBottomIconLayer.setIconVisible(true, false);
        //右侧
        this.rightTopIconLayer = new MainTopIconLayer2(1, 4);
        this.rightTopIconLayer.name = "rightTopIconLayer";
        this.rightTopIconLayer.anchorX = 0;
        this.rightTopIconLayer.anchorY = 0;
        this.rightTopIconLayer.zOrder = LayerManager.STAGE_ZORDE_TOPEST;
        this.rightTopIconLayer.updateView();

        this.rightTopIconLayer.pos(630, DataCenter.excursion_y + 180);
        this.addChild(this.rightTopIconLayer);
        this.fixRightTopView();

        Tween.clearAll(this.imgTips);
        TweenUtil.onLight(this.imgTips, 1000);

        this.buildView = new GuaJiBuildView();
        
        this.addChildAt(this.buildView,0);

        /**通关奖励按钮 */
        this.createBtnPassReward();
        this.creatBtnTravel();
        // this.checkExpandState();

        this.initEditor();
    }
    initEditor(): void {
        if (GlobalConfig.IsDebug == false) {
            return;
        }
        if (this.editorView) {
            return;
        }

        let btnEditor = new Button();
        btnEditor.skin = CommonButton.BtnYellow;
        btnEditor.name = "btnEditor";
        btnEditor.label = "显/隐编辑器";
        btnEditor.size(200, 80);
        btnEditor.right = 0;
        btnEditor.top = 10;
        this.addChild(btnEditor);
        this.addOnClick(this, btnEditor, () => {
            this.editorView.visible = !this.editorView.visible;
            this.buildView.checkGuide();
        });

        this.editorView = new GuajiBuildEditorView();
        this.editorView.init(this.buildView, this.buildView.boxMap);
        this.editorView.visible = false;
        this.editorView.pos(0, -200);
        this.buildView.editor = this.editorView;

        this.addChild(this.editorView);
    }

    private checkExpandState() {

        // let isGuideFinish = this.isGuideAllFinish;
        // this.btnLeftTopExpand.visible = isGuideFinish;
        if (this.btnLeftTopExpand.visible == false) {
            return true;
        }

        let forceExpand = false;
        let cfgGuide = GuideMgr.ins.curCfg;
        if(cfgGuide && cfgGuide.ui_name == this.uiName){

            if (cfgGuide.btn_name._has(this.lotterbtn.name)
            || cfgGuide.btn_name._has(this.btnPassReward.name)
            || cfgGuide.btn_name._has(this.leftIconLayer.name)
            ){
                forceExpand = true;
            }
        }

        let isExpand = (forceExpand || GuaJiDialog.isBoxLeftTopExpand) && this.nowShowType == BUILD_SHOW_TYPE.BUILD ;
        // this.leftIconLayer.visible = isExpand;
        // this.lotterbtn.visible = this.lotterbtn.visible && isExpand;
        // this.btnPassReward.visible = this.btnPassReward.visible  && isExpand;
        return isExpand;
    }

    private onClickLeftTopExpand(e: Event) {
        GuaJiDialog.isBoxLeftTopExpand = !GuaJiDialog.isBoxLeftTopExpand;
        // this.checkExpandState();
        this.changeShowTypeUi();
    }

    protected onCloseLotteryPreview(pos) {
        this.missionLotteryScript.createFlyItem(pos);
    }

    protected onRefreshLordPreviewInfo() {
        let isEnough = this.curShowPass >= LordDataCenter.instance.showLordPreview;
        let isClick = LordDataCenter.instance.isClickLordPreview;

        if (isEnough && !isClick) {
            if (!this._LordPreviewView) {
                this._LordPreviewView = new LordPreviewView();
                this._LordPreviewView.anchorX = 0.5;
                this._LordPreviewView.anchorY = 0.5;
                this._LordPreviewView.scale(0.4, 0.4);
                this.addChild(this._LordPreviewView);

                // if (this.leftIconLayer) {
                //     var y = this.leftIconLayer.y + 570;
                // } else {
                //     var y = this.bottombox.y - 150;
                // }
                var y = this.bottombox.y - 200;
                this._LordPreviewView.pos(75, y);
            } else {
                this._LordPreviewView.update();
            }
        } else {
            if (this._LordPreviewView) {
                this._LordPreviewView.destroy();
                this._LordPreviewView = null;
            }
        }
    }



    protected updateAllPoint(): void {
        let dataCenter = GuaJiDataCenter.instance;
        this.SetRedPoint(this.lbBoxNum, false, 70, -20);
        if (dataCenter.show_box_red_point) {
            dataCenter.show_box_red_point = false;
            this.SetRedPoint(this.lbBoxNum, dataCenter.getGuajiBoxCount() > 0, 70, -20);
        }
    }

    private onClickRateBtn(e: Event) {
        e.stopPropagation();
        let rate = (this._rate % MiscConst.fight_play_speed_max_rate) + 1;
        let reviewCfg = ConfigManager.cfg_guide_reviewCache.get(GlobalConfig.PlatName);
        let isShowTip = reviewCfg ? reviewCfg.is_fight_rate_tips == 1 : true;
        if (!VipTeQuanUtil.checkFightRate(rate, isShowTip)) {
            rate = 1;
        }

        this._rate = rate;
        FightDataCenter.instance.m_fight_set_speed_tos(this._rate);
    }
    private updateRate(rate: number): void {

        if (rate == 0) {
            rate = MiscConst.fight_default_play_speed;
        }
        this._rate = rate > 0 ? rate : 1;
        this.timer.callLater(this, () => {
            // this.ratebtn.skin = "fight/rate" + this._rate + ".png";
            this.ratebtn.label = "X" + this._rate;
        });
        DispatchManager.dispatchEvent(ModuleCommand.FIGHT_RATE, this._rate);
    }

    protected checkOpenState(): void {
        this.leftIconLayer?.updateView();
        this.rightBottomIconLayer?.updateView();
        if (this.rightTopIconLayer) {
            this.rightTopIconLayer.visible = GlobalConfig.showRecharge && this.nowShowType == BUILD_SHOW_TYPE.BUILD;
            this.rightTopIconLayer.updateView();
        }

        this.refBtnPassReward();
        this.refBtnTravel();
        this.refreshStartGameBtn();
    }

    public onClose(): void {
        // this.onFront();
        super.onClose();
    }

    onDestroy() {
        super.onDestroy();

        this.isCurrHideGuide = false;
    }

    nowplayUIMusic(): void {
        super.playUIMusic();
    }


    showRedPoint(): void {
        this.SetRedPoint(this.lotterbtn, RedPointMgr.ins.IsCheckRedPoint(PanelEventConstants.LOTTERY_NORMAL), 55, 0);
    }

    refreshRedPoint(vo: RedPointVo): void {
        if (vo == null) {
            return;
        }
        if (vo && vo.eventId == PanelEventConstants.LOTTERY_NORMAL) {
            this.showRedPoint();
        }
    }
    private createBtnMenActivityIcon(sys_id: number,x:number = 0,y:number = 0):MenuActivityIconView{
        let vo: cfg_activity_icon = CfgCacheMapMgr.cfg_activity_iconCache.get(sys_id);
        let btn = new MenuActivityIconView(Number(vo.show_tag));
        btn.InitInfo(vo);
        let funObj = MainIconConfig.getIconCfg(vo.id);
        if (funObj && funObj["custom"]) {
            btn.updateCustomView();
        }
        return btn;
    }
    private creatBtnTravel(){
        if (!GameUtil.isSysOpen(PanelEventConstants.TRAVEL,0,false)) {
            return;
        }
        this.btnTravel = this.createBtnMenActivityIcon(PanelEventConstants.TRAVEL);
        this.boxLeftTop.addChild(this.btnPassReward);
        this.btnTravel.pos(30,-8);
        this.btnBox2.addChild(this.btnTravel);
    }
    private refBtnTravel(){
        const isTravelOpen = GameUtil.isSysOpen(PanelEventConstants.TRAVEL, 0, false);
        if (!isTravelOpen) {
            return;
        }
        if (!this.btnTravel) {
            this.creatBtnTravel();
        }
        this.btnTravel.visible = isTravelOpen;
    }
    protected createBtnPassReward() {
        // let vo: cfg_activity_icon = CfgCacheMapMgr.cfg_activity_iconCache.get(PanelEventConstants.PASS_REWARD);
        // this.btnPassReward = new MenuActivityIconView(Number(vo.show_tag));
        // this.btnPassReward.InitInfo(vo);
        // this.btnPassReward.visible = true// step >= ETdMainGuideStep.missionBtn;
        // let funObj = MainIconConfig.getIconCfg(vo.id);
        // if (funObj && funObj["custom"]) {
        //     this.btnPassReward.updateCustomView();
        // }
        // // this.boxLeftTop.addChild(this.btnPassReward);
        // // this.btnPassReward.pos(-5, 370);

        // this.addChild(this.btnPassReward);
        // this.btnPassReward.pos(5, this.boxLeftTop.y + 370);
        this.btnPassReward = this.createBtnMenActivityIcon(PanelEventConstants.PASS_REWARD);
        this.boxLeftTop.addChild(this.btnPassReward);
        // this.btnPassReward.pos(-5, 370);
    }

    protected refBtnPassReward() {
        this.btnPassReward?.reflash(PanelEventConstants.PASS_REWARD);
        if (!this.leftIconLayer) {
            return;
        }
        let vo: cfg_activity_icon = CfgCacheMapMgr.cfg_activity_iconCache.get(PanelEventConstants.MISSION_LOTTERY);
        let icon: MenuActivityIconView = this.leftIconLayer.getIconView(vo);
        icon && icon.updateCustomView();
    }

    protected updateGuaJiInfo(): void {
        this.checkRedQuickFight();
    }

    public onClickMission(): void {
        this.checkGuideMission(true);
        let mission = BuildMissionDataCenter.ins.get_curr_mission();
        if (!mission) return;

        let cfg = CfgCacheMapMgr.cfg_building_missionCache.get(mission?.id);
        if (!cfg) {
            return;
        }
        if (mission.status == MissionStatus.ACCEPT) {

        } else if (mission.status == MissionStatus.ACC_REWARD) {
            //完成任务
            //领取奖励
            BuildMissionDataCenter.ins.m_simp_mission_finish_tos(mission.id);
        } else if (mission.status == MissionStatus.FINISH_REWARD) {

        }
    }

    public checkGuideMission(isClick: boolean = false): void {
        if(this.isGuideAllFinish == false){
            return;
        }
        this.checkLastFinishGuideMission();
        this.checkCurrGuideMission(isClick);
        this.missionGuideScript.updateMissionEff();
    }

    protected checkCurrGuideMission(isClick: boolean): void {

        if (!this.boxMission.visible || !this.bottombox.visible) {
            return;
        }
        
        if (GuideMgr.ins.curGuideId){
            return;
        }

        let mission = BuildMissionDataCenter.ins.get_curr_mission();
        let cfg = CfgCacheMapMgr.cfg_building_missionCache.get(mission?.id);
        let isAutoGuide = isClick || !!cfg?.is_auto_guide;

        //---------当前任务
        mission = BuildMissionDataCenter.ins.get_curr_mission();
        if (!mission || !cfg || !cfg.guide_id || !isAutoGuide) {
            return;
        }

        if (mission.status == MissionStatus.ACCEPT) {

            // if (!isAutoGuide) {
            //     return;
            // }

            if (!cfg.guide_id) {
                return;
            }

            let finishList = BuildMissionDataCenter.ins.tdMissionIdFinishList;
            if (finishList.indexOf(cfg.id) != -1) {
                if (cfg.is_repeat != 1 && isClick == false){
                    return;
                }
            }

            // if (cfg.guide_id != 0) {
            //     GuideMgr.ins.setGranchGuideId(cfg.guide_id);
            //     return;
            // }
            
            //当前引导
            let progress = mission.cur_progress;
            let maxProgress = cfg.max_progress;
            let isGuide = false;
            if (progress < maxProgress) {
                isGuide = true;
            }

            if (isGuide) {

                // if (!GuideMgr.ins.curGuideId) {
                    // BuildMissionDataCenter.ins.currMissionId = cfg.id;
                    let finishList = BuildMissionDataCenter.ins.tdMissionIdFinishList;
                    if (finishList.indexOf(cfg.id) == -1) {
                        finishList.push(cfg.id);
                    }
                    GuideMgr.ins.setGranchGuideId(cfg.guide_id, false, { missionId: cfg.id });
                // }

            } else {
            }

        } else if (mission.status == MissionStatus.ACC_REWARD) {

        } else if (mission.status == MissionStatus.FINISH_REWARD) {

        }
    }

    protected checkLastFinishGuideMission(): void {

        if (!this.boxMission.visible || !this.bottombox.visible) {
            return;
        }

        let mission = BuildMissionDataCenter.ins.get_last_acc_reward_mission();
        let cfg = CfgCacheMapMgr.cfg_building_missionCache.get(mission?.id);
        if (!mission || !cfg || !cfg.guide_id || !cfg.is_auto_guide) {
            return;
        }

        //完成任务
        let finishList = BuildMissionDataCenter.ins.tdMissionIdFinishList;
        if (finishList.indexOf(cfg.id) == -1) {
            finishList.push(cfg.id);
        }

        //任务刚开始,那就强制完成
        let cfgGuide = GuideMgr.ins.curCfg;
        if (cfg.guide_id && cfgGuide && cfgGuide.guide_id == cfg.guide_id && cfgGuide.step == 0) {
            let tempData = GuideMgr.ins.granchTempData;
            if (tempData?.missionId == cfg.id) {
                ConsoleUtils.log("------强制完成任务引导, cfg = ", cfg);
                GuideMgr.ins.clearGranchGuide(true);
            }
        }
    }

    public updateMissionProgress(updateMission: p_simp_mission = null): void {

        // if (this.uiProgress == null) {
        //     this.uiProgress = UIProgressBarEx.SetUIProgressBar(this, this.proMission);
        // }

        // this.showBoxMission();

        let mission = BuildMissionDataCenter.ins.get_curr_mission();
        let cfg = CfgCacheMapMgr.cfg_building_missionCache.get(mission?.id);
        if (cfg) {
            let progress = mission.cur_progress;
            let maxProgress = cfg.max_progress;
            let progressRate = progress / maxProgress;

            // let tempParam = this.uiProgress.getTempParam();
            // let tempId = tempParam?.id;
            // let isChange = tempId && tempId != mission.id;
            // this.uiProgress.SetValueLerp(progress, maxProgress, isChange, 500);
            // this.uiProgress.setTempParam({id:mission.id});

            this.htmlMissionDesc.innerHTML = StringUtil.FormatCfgField(cfg, "event_name", ColorUtil.TOTAL_GREEN);

            let colorProgress = progress >= maxProgress ? ColorUtil.TOTAL_GREEN : ColorUtil.FONT_RED_ITEM;
            let strProgress = "{0}/{1}".Format(progress.toString(), maxProgress.toString()).toColor(colorProgress);
            this.htmlMissionProgress.innerHTML = strProgress;
            // if (isChange) {
            //     this.uiProgress.ShowEffectWhenLvUp("lottery_box0", 88, 8, 0.5, 0.8);
            //     // let eff = this.ShowEffect("lottery_box0", this.proMission, true);
            //     // eff.pos(0, 0);
            //     // eff.scale(1.4, 1);
            // }
            if (this.itemMission == null) {
                this.itemMission = GoodsItem.create();
                this.itemMission.name = "itemMission";
                this.itemMission.scale(0.7, 0.7);
                this.itemMission.pos(2, 2);
                this.boxMissionCacheAs.addChild(this.itemMission);
            }

            let p_item = mission.rewards[0];
            if (p_item) {
                let vo = GoodsVO.GetPItemToVos(mission.rewards)[0];
                this.itemMission.SetGoodsVoData(vo);
            }

            this.missionGuideScript.updateMissionEff();
            this.checkGuideMission();
        } else {
            this.boxMission.visible = this.isShowBoxMission;
        }
    }

    protected updateTime(): void {

        //更新挂机时长
        let time: number = GuaJiDataCenter.instance.guaJiTime > GuaJiDataCenter.instance.max_duration ? GuaJiDataCenter.instance.max_duration : GuaJiDataCenter.instance.guaJiTime;
        this.lbTime.text = DateUtil.GetHMS(time);
        //宝箱图片
        // let effectName: string = GuaJiDataCenter.getGuajiBoxEffName(time);
        // this.showbox.skin = "guaJi/" + effectName + ".png"

        //cfg_guaji_box_time中配置不同的显示状态
        let cfg = GuaJiDataCenter.getCfgGuajiBoxTime(time);
        let effName = GuaJiDataCenter.getGuajiBoxEffName(time);
        if (effName == "td_main_gold1") {
            var offsetX = 30;
            var offsetY = 50;
        } else {
            var offsetX = 50;
            var offsetY = 50;
        }

        if (!this.skGuajiReward || this.skGuajiReward.name != effName) {

            this.skGuajiReward = this.showGSkeleton(this.btnQuickFight, effName, this.skGuajiReward, { type: ESkeletonType.UI_EFFECT });

            if (this.skGuajiReward) {
                this.skGuajiReward.name = effName;
                this.skGuajiReward.pos(offsetX, offsetY);
                this.btnQuickFight.addChildAt(this.skGuajiReward, 0);
            }
        }

        // if (!this.skGuajiReward) {
        //     this.skGuajiReward = this.showGSkeleton(this.btnQuickFight, "ji_ji", null, { type: ESkeletonType.MODEL_ACTION });
        // }

        if (!this.skBox) {
            this.skBox = this.showGSkeleton(this.btnBox, "007jinxiangzi", null);
            this.skBox.scale(0.3, 0.3);
            this.skBox.pos(45, 80);
            this.skBox.playAction("idle_1");
        }

        this.lbBoxNum.text = "X" + GuaJiDataCenter.instance.getGuajiBoxCount();
        this.updateAllPoint();
    }

    protected onClickMainBattleBox() {
        if (this.checkGuideLevel()) {
            return;
        }
        this.dispatchEvent(ModuleCommand.OPEN_GUAJI_BOX_DIALOG)
    }


    private testEyesWakeup: GuajiBuildGuideScript_guide_wakeup_300001;
    private testGuideStory1: GuajiBuildGuideScript_guide_story_3000011;
    protected onClickLotteryBtn() {
        this.dispatchEvent(ModuleCommand.OPEN_LOTTERY_DIALOG);
        // if(this.testEyesWakeup){
        //     this.testEyesWakeup.destroy();
        //     this.testEyesWakeup = null;
        // }
        // this.testEyesWakeup = new GuajiBuildGuideScript_guide_wakeup_999999();
        // this.testEyesWakeup.init(true, this);
        // this.addChild(this.testEyesWakeup);

        // if(this.testGuideStory1){
        //     this.testGuideStory1.destroy();
        //     this.testGuideStory1 = null;
        // }
        // this.testGuideStory1 = new GuajiBuildGuideScript_guide_story_1();
        // this.testGuideStory1.init(true, this);
        // this.addChild(this.testGuideStory1);


        // GuideMgr.ins.setGranchGuideId(GuajiBuildGuideScript.guide_story1_3000011);
    }

    onGuajiBtnClick() {
        if (this.nowShowType == BUILD_SHOW_TYPE.BUILD) {
            this.changeShowType(BUILD_SHOW_TYPE.GUAJI);
        } else if (this.nowShowType == BUILD_SHOW_TYPE.GUAJI) {
            this.changeShowType(BUILD_SHOW_TYPE.BUILD);
        }
    }

    //----------------------引导--------------------------/

    private _isGuideScriptHide = false;
    public get isScriptHideGuide(){
        return this._isGuideScriptHide;
    }
    public set isScriptHideGuide(isHide:boolean){
        this._isGuideScriptHide = isHide;
        this.setHideGuide();
    }

    private _isCurrHideGuide = false;
    public get isCurrHideGuide(){
        return this._isCurrHideGuide;
    }

    public set isCurrHideGuide(isHide:boolean){
        this._isCurrHideGuide = isHide;
        this.setHideGuide();
    }

    private setHideGuide(){
        let isHide = this.isCurrHideGuide || this.isScriptHideGuide;
        let oldHide = GuideMgr.ins.getIsHideGuideUI(this.uiName);
        if (oldHide != isHide) {
            GuideMgr.ins.setHideGuideUI(isHide, this.uiName);
            this.changeShowTypeUi();
        }
    }

    public checkGuide(force = false): void {

        this.checkGuideBtnShow();
        let isHide = false;
        let currCfg = GuideMgr.ins.curCfg;
        let isAllFinish = this.isGuideAllFinish;
        if (isAllFinish || GuideMgr.ins.curGuideId == 0) {
            isHide = false;
        } else {
            if (currCfg && currCfg.ui_name && currCfg.ui_name == this.uiName) {
                let btn = GuideMgr.ins.getUINameGuide(currCfg.btn_name, this) as Sprite;
                //特殊处理
                if (this._checkIsGuideInFight(btn)) {
                    isHide = false;
                }
                else if (FightDataCenter.isInFight) {
                    isHide = true;
                    // isHide = this.isScriptHideGuide;
                }
            }
        }

        this.isCurrHideGuide = isHide;

        super.checkGuide();

        // this.missionGuideScript.updateMissionEff();
        this.checkGuideMission();

        this.buildView?.checkGuide();
        this.newGuideScript?.checkGuide(currCfg);

        if (GuideMgr.ins.isGuideing == true) {
            this.timer.callLater(this, function (): void {
                HandEff.removeEff(this.btnFight);
            });
        }


        //移动到建筑
        if (isAllFinish && !this.isCurrHideGuide && !this.isScriptHideGuide){
            let itemName = GuaJiBuildDataCenter.BUILDING_ITEM_NAME;
            if (currCfg && currCfg.btn_name._has(itemName)){
                let btnNameList = currCfg.btn_name.split("_");
                let buildingType = 0;
                for(let btnName of btnNameList){
                    if(btnName._has(itemName)){
                        buildingType = +btnName.replace(itemName, "") || 0;
                        break;
                    }
                }
                if(buildingType){
                    // console.log("---")
                    this.buildView.moveToBuilding(buildingType, 1);
                }
            }
        };
    }

    private onGuideEnd(guideId) {
        this.newGuideScript?.onGuideEnd(guideId);
    }

    protected checkGuideBtnShow() {

        let isFinish = this.isGuideAllFinish;

        //TODO 前期引导结束了
        if (isFinish) {
            return;
        }

        let cfg = GuideMgr.ins.curCfg;
        if (!cfg || cfg.ui_name != this.uiName || !cfg.btn_name) {
            return;
        }

        let btn = GuideMgr.ins.getUINameGuide(cfg.btn_name, this) as Sprite;

        if (this._checkIsGuideBtnShowSkip(btn)) {
            return;
        }

        if (btn) {
            //战斗中, 不显示引导
            if (this._checkIsGuideInFight(btn) == false && FightDataCenter.isInFight) {
                return;
            }

            if (!btn.visible) {
                // btn.alpha = 0;
                // btn._tweenTo({alpha:1}, 500);
                this.setIsGuideViewVisible(btn.name, true);
            }

            btn.visible = true;
        }
        return btn;
    }

    protected _checkIsGuideInFight(btn: Sprite) {
        return btn == this.ratebtn;
    }

    protected _checkIsGuideBtnShowSkip(btn: Sprite) {
        return btn == this.startgamebtn;
    }

    onClickQuickFight(): void {
        if (this.checkGuideLevel()) {
            return;
        }
        GuaJiDataCenter.instance.loginRedPoint = false;
        this.dispatchEvent(ModuleCommand.OPEN_GUA_JI_QUICK_FIGHT_DIALOG);
        this.checkRedQuickFight();
    }

    protected checkGuideLevel(): boolean {
        if (MiscConstAuto.mainUIBottomLayer_fight_change == DataCenter.myLevel) {
            TipsUtil.showTips(MiscConstAuto.mainUIBottomLayer_fight_change_tip);
            return true;
        }
        return false;
    }

    //设置"快速挂机"的按钮红点
    protected checkRedQuickFight(): void {
        let redPointX = (this.btnQuickFight.width * this.btnQuickFight.scaleX) + 20;
        let redPointY = 10;
        this.SetRedPoint(this.btnQuickFight, GuaJiDataCenter.instance.checkRedQuickFight(), redPointX, redPointY);
    }


    protected setSpeedRate(rate: number) {
    }


    protected onClickBack() {

    }

    public onClickFightMode() {
    }

    protected onOpenOrCloseDialog(dialog: BaseDialog) {
    }

    public onFront(param?: any): void {
    }

    public onBack(): void {
    }

    public onOneKeyClick() {
    }

    public autoLineUp() {
    }

    protected onFightResult(toc: m_fight_simp_result_toc) {
        this.newGuideScript?.onFightResult(toc);
        this.checkGuideViewShow();
        this.checkGuide();
    }

    protected updateMainBattleInfo() {
        this.checkOpenState();
        this.checkFirstRecharge();
        this.refreshStartGameBtn();
    }

    protected checkFirstRecharge(openFlag: boolean = false): void {
        if (GuaJiDataCenter.instance.curGuajiPass - 1 == MiscConstAuto.first_recharge_show && this.nowShowType == BUILD_SHOW_TYPE.BUILD) {
            ConfigManager.cfg_first_payCache.forEach(cfg => {
                let info: p_first_pay_info = RechargeDataCenter.instance.getFirstPayTaskInfoByGiftId(cfg.gift_id);
                if (info && info.status == 0) {
                    openFlag = true;
                }
            });
            let key = "OPEN_FIRST_RECHARGE_DIALOG" + (GuaJiDataCenter.instance.curGuajiPass - 1);
            let num: number = SettingDataCenter.instance.getVal(key);
            if (num < 1 && openFlag && GlobalConfig.showRecharge && GameUtil.isSysOpen(PanelEventConstants.FIRST_RECHARG, 0, false)) {
                this.dispatchEvent(ModuleCommand.OPEN_FIRST_RECHARGE_DIALOG);
                SettingDataCenter.instance.m_role_setting_tos(key, 1);
            }
        } else {
            if (UIUtil.checkIsDialogOpened("FirstRechargeDialog")) {
                this.dispatchEvent(ModuleCommand.OPEN_FIRST_RECHARGE_DIALOG);
            }
        }
    }

    protected checkGuideViewShow(): void {
        // this.btnQuickFight.visible = GlobalConfig.showRecharge && this.curShowPass >= 3;
        // this.ratebtn.visible = this.curShowPass >= 3;// || GlobalConfig.is_LOCAL_DEBUG;
        if(this.isGuideAllFinish){
            return;
        }
       
        this.changeShowTypeUi();
      
    }

    private get_guaji_guide_view_visible_key(viewName: string): string {
        return "GUAJI_BUILD_GUIDE_VIEW_VISIBLE_" + DataCenter.myRoleID + "_" + viewName;
    }

    public _CheckGuideBtnVisible(view: Sprite): boolean {
        let isVisible = false;
        let viewName = view.name;
        if (this.isGuideAllFinish) {
            isVisible = true;
            // localStorage.removeItem(this.get_guaji_guide_view_visible_key(viewName));
        }

        if (!viewName) {
            isVisible = true;
        }
        if (!isVisible) {
            // let localSave = localStorage.getItem(this.get_guaji_guide_view_visible_key(viewName)) || "false";
            // isVisible = localSave == "true";
            isVisible = this.getIsGuideViewVisible(viewName);
        }
        view.visible = isVisible;
        return isVisible;
    }

    public getIsGuideViewVisible(viewName: string): boolean {
        return localStorage.getItem(this.get_guaji_guide_view_visible_key(viewName)) == "true";
    }

    public setIsGuideViewVisible(viewName: string, value: boolean): void {
        if (!viewName) {
            return;
        }

        localStorage.setItem(this.get_guaji_guide_view_visible_key(viewName), value + "");
    }

    private showGuaJiPlayer() {
        GuaJiDataCenter.isInGuaJiDialog = true;
        if (!LayerManager.isInBattle2(MatchConst.MATCH_TYPE_MAIN_BATTLE)) {
            this.dispatchEvent(ModuleCommand.BEGAN_RUN_GUAJI);
            if (GuaJiDataCenter.instance.laveAutoPassNum > 0) {
                GuaJiDataCenter.instance.onFightMainBattle({ isLineUp: false, isCheckTravel: false });
            }
        }
        else {
            //如果有晋阶的战斗,那么就只显示晋阶的战斗
            if (!LayerManager.isFightRecordInFight(MatchConst.MATCH_TYPE_MASTER_CARD) && !LayerManager.runningFight(MatchConst.MATCH_TYPE_MASTER_CARD)) {
                LayerManager.rebackFight(MatchConst.MATCH_TYPE_MAIN_BATTLE, false);
            }
            this.startBattle();
        }
    }

    private _fight(isCheck: boolean = false): void {
        // GuaJiDataCenter.instance.m_main_battle_auto_add_tos(addTimes);

        if (this.isCanAutoFight) {
            GuaJiDataCenter.instance.m_main_battle_auto_tos(true);
            if (FightDataCenter.isInFight == false) {
                DispatchManager.addEventListenerOnce(ModuleCommand.UPDATE_MAIN_BATTLE_AUTO, this, function () {
                    GuaJiDataCenter.instance.onFightMainBattle({ isLineUp: false, isCheckTravel: isCheck });
                });
            }
        } else {
            GuaJiDataCenter.instance.onFightMainBattle({ isLineUp: false, isCheckTravel: isCheck });
        }

        HandEff.removeEff(this.startgamebtn);
    }

    protected get isCanAutoFight() {
        //判断等级,下一个关卡
        let cfg: cfg_main_battle = GuaJiDataCenter.instance.main_battle;
        if (cfg && DataCenter.myLevel < cfg.need_level) {
            return false;
        }
        return this.curShowPass >= MiscConstAuto.building_auto_fight_limit;
    }

    private startBattle(): void {
        FightDataCenter.isInFight = true;
        this.refreshStartGameBtn();
        this.mainBattleShow();
    }
    private startGuaJi(): void {
        FightDataCenter.isInFight = false;
        this.refreshStartGameBtn();
    }

    private mainBattleFightResult(is_success:boolean): void {
        if (!GuaJiDataCenter.instance.isAutoPass) {
            FightDataCenter.isInFight = false;
            if (this.nowShowType == BUILD_SHOW_TYPE.GUAJI) {
                this.changeShowType(BUILD_SHOW_TYPE.BUILD);
                this.checkFirstRecharge();
            }
        }
    }


    protected refreshStartGameBtn(): void {
        let cfg: cfg_main_battle = GuaJiDataCenter.instance.main_battle;
        this.startgamebtn.visible = true;
        if (cfg && cfg.need_level > DataCenter.myLevel) {
            this.startgamebtn.label = cfg.need_level + window.iLang.L2_JI_KE_TIAO_ZHAN.il();
            this.startgamebtn.skin = "guaJi/btn_fight.png";
            this.startgamepic.skin = ""
            this.imgTips.visible = false;
        }
        else if (GuaJiDataCenter.instance.isAutoPass) {
            this.startgamebtn.label = "";
            this.startgamebtn.skin = "guaJi/btn_auto.png";
            this.startgamepic.skin = "guaJi/guaji_auto.png"
            this.imgTips.skin = "guaJi/img_tips2.png";
            this.imgTips.visible = true;
        }
        else {
            if (FightDataCenter.isInFight) {
                if (this.isCanAutoFight) {
                    // this.startgamebtn.skin = "guaJi/btn_auto.png";
                    // this.startgamepic.skin = "tdMain/zztf_21.png";

                    this.startgamebtn.label = "";
                    this.startgamebtn.skin = "guaJi/btn_fight.png";
                    this.startgamepic.skin = "tdMain/zztf_22.png";
                    let isMaxFight = GuaJiDataCenter.instance.checkIsMaxFightPass();
                    this.imgTips.visible = GuaJiDataCenter.instance.curPass >= 1 && !isMaxFight;//通过第一关才会显示连续挑战TIP
                    this.imgTips.skin = "guaJi/img_tips.png";
                } else {
                    this.startgamebtn.visible = false;
                }
            } else {
                this.startgamebtn.label = "";
                this.startgamebtn.skin = "guaJi/btn_fight.png";
                if (this.isCanAutoFight) {
                    this.startgamepic.skin = "tdMain/zztf_22.png";
                    let isMaxFight = GuaJiDataCenter.instance.checkIsMaxFightPass();
                    this.imgTips.visible = GuaJiDataCenter.instance.curPass >= 1 && !isMaxFight;//通过第一关才会显示连续挑战TIP
                    this.imgTips.skin = "guaJi/img_tips.png";
                } else {
                    this.startgamepic.skin = "tdMain/zztf_2.png";
                    this.imgTips.visible = false;
                }
            }
        }

        this.updatePassInfo();
        this.addHandEff();
        this.setGuaJiBtnEff();
        this._delayUpdateRate();
        this.timer.once(200, this, this._delayUpdateRate).runImmediately();
    }
    private setGuaJiBtnEff(){
        if (this.guajiBtn.visible) {
            if (GuaJiDataCenter.instance.isAutoPass) {
                this.autoLiuGunang = this.showGSkeleton(this.guajiBtn, "card_ui_4", this.autoLiuGunang, { isLoop: true, x: 35, y: 35, type: ESkeletonType.UI_EFFECT });
            } else {
                if (this.autoLiuGunang) {
                    this.autoLiuGunang.removeSelf();
                    this.autoLiuGunang.destroy();
                    this.autoLiuGunang = null;
                }
            }
        }
    }
    private _delayUpdateRate(): void {

        let data = FightDataCenter.FIGHT_INFO_CACHE.get(MatchConst.MATCH_TYPE_MAIN_BATTLE);
        if (this.ratebtn.visible && data) {
            this.updateRate(data.play_speed);
        }
    }

    private updatePassInfo(): void {
        let curPass: number = GuaJiDataCenter.instance.curGuajiPass;
        let cfg = ConfigManager.cfg_main_battleCache.get(curPass);
        if (!cfg) {
            return;
        }

        let pass_str = curPass.toString();

        this.passtext.text = window.iLang.L2_DI_P0_GUAN.il([pass_str]);
        this.lbCurrentPass.text = window.iLang.L2_DI_P0_GUAN.il([pass_str]);
    }

    //---道具飞入
    private onFlyIcon(fromX: number, fromY: number, isQuick: boolean = false): void {
        let from: Point = Point.create().setTo(fromX, fromY);
        // from = this.gjScene.localToGlobal(from, false, this);
        from = this.globalToLocal(from, false);

        // let to: Point = Point.create().setTo(50, 50);
        // to = this.showbox.localToGlobal(to, false, this)
        let to = Point.create().setTo(this.relativeWidth / 2, this.relativeHeight - 80);

        let params = isQuick ? MiscConst.guajiFlyIconParam2 : MiscConst.guajiFlyIconParam1;
        let count = ConsoleUtils.getConsoleCmdVal("GUAJI_FLY_ICON_COUNT", params[0]);
        for (let i = 0; i < count; i++) {
            let gjFlyIcon = GJFlyIcon.create(GJFlyType.GJ_GAIN);
            gjFlyIcon.isQuick = isQuick;
            gjFlyIcon.isActive = true;
            gjFlyIcon.visible = true;
            gjFlyIcon.zOrder = LayerManager.STAGE_ZORDE_TOPEST;
            gjFlyIcon.pos(from.x, from.y - this.offsetY);
            gjFlyIcon.setGainMoveData(from.x, from.y - this.offsetY, to.x, to.y, i);
            this.addChild(gjFlyIcon);
            this.gjFlyIconList.push(gjFlyIcon);
        }

        from.reset();
        to.reset();
    }

    private updateFrame(): void {
        let interval = Laya.timer.delta;//毫秒
        // if (this.gjScene) {
        //     this.gjScene.updateFrame(interval);
        // }

        if (this.gjFlyIconList && this.gjFlyIconList.length > 0) {
            let nowList = this.gjFlyIconList;
            this.gjFlyIconList = [];
            for (let i = 0; i < nowList.length; ++i) {
                let icon = nowList[i];
                if (icon) {
                    icon.update(interval);
                    icon.updateZOrder();
                    if (icon.isActive) {
                        this.gjFlyIconList.push(icon);
                    }
                }
            }
        }

        if(this.editorView){
            this.editorView.updateFrame(interval);
        }

        if (this.buildView) {
            this.buildView.updateFrame(interval);
        }
    }

    public onStartGameClick() {
        if (FightDataCenter.isInFight) {
            if (GuaJiDataCenter.instance.isAutoPass) {
                GuaJiDataCenter.instance.m_main_battle_auto_tos(false);
                return;
            }
        }

        //点击时，应判断是否能够继续挑战下一关
        if (!GuaJiDataCenter.instance.isCanFightNextPass(true)) {
            return;
        }

        if (!GuaJiDataCenter.instance.checkTravelScore(0, this, this._fight)) {
            this._fight(true);
        }

        this.checkGuideViewShow();
    }

    protected onStopGameClick() {
        GuaJiDataCenter.instance.m_main_battle_auto_tos(false);
    }

    onClickShow(): void {
        let vo = new LineUpVO();
        vo.isShowRedPoint = true;
        vo.saveTips = window.iLang.L2_BAO_CUN_CHENG_GONG_ch31_XIA_CHANG_ZHAN_DOU_SHENG_XIAO.il();
        TipsUtil.openHeroLineUpDialog(MatchConst.MATCH_TYPE_MAIN_BATTLE, vo);
    }

    private addHandEff(): void {
        if (DataCenter.myLevel >= MiscConstAuto.auto_fight_guide_minlv && DataCenter.myLevel <= MiscConstAuto.auto_fight_guide_maxlv && GuaJiDataCenter.instance.isAutoPass == false) {
            HandEff.addEff(this.startgamebtn);
        } else {
            HandEff.removeEff(this.startgamebtn);
        }
    }



    //-------------------显示状态切换---------------------
    openBottomModule(index: number) {
        if (index == EMainUIBottomIndex.index_4_guaji && this.nowShowType == BUILD_SHOW_TYPE.GUAJI) {
            this.changeShowType(BUILD_SHOW_TYPE.BUILD);
        }
    }

    private changeShowType(type: number): void {
        this.nowShowType = type;
        if (this.nowShowType == BUILD_SHOW_TYPE.BUILD) {
            this.dispatchEvent(ModuleCommand.CLOSED_GUA_JI);
            LayerManager.destroyMap(1);
        } else if (this.nowShowType == BUILD_SHOW_TYPE.GUAJI) {
            this.showGuaJiPlayer();
        }
        this.changeShowTypeUi();
    }


    private showBottomMainIcon(){
        if (DataCenter.isM3Building) {
            this.dispatchEvent(ModuleCommand.BOTTOM_GUAJI_ICON_UPDATE, "v2_mainui/tab_guaji.png");
        } else {
            this.dispatchEvent(ModuleCommand.BOTTOM_GUAJI_ICON_UPDATE, "v2_mainui/zhengzhan_icon.png");
        }
    }

    private changeShowTypeUi(): void {
        this.callLater(this._changeShowTypeUi);
    }

    private _changeShowTypeUi(): void {

        if(this.destroyed){
            return;
        }

        // if (this._oldShowType == this.nowShowType){
        //     if (this.curShowPass > GuaJiBuildDataCenter.instance.allGuideFinishPass) {
        //         return;
        //     }
        // }

        // let topDialog = BaseDialog.getTopShowDialog();
        // if (topDialog != this && topDialog.getClassName() != "FirstRechargeDialog"){
        //     return;
        // }
    
        this.checkShowTypeChange();
        this.fixLeftTopView();
        // this.fixRightTopView();

        this._oldShowType = this.nowShowType;
    }

    private checkShowTypeChange(){
        // if(this._oldShowType == this.nowShowType){
        //     if (this.curShowPass > GuaJiBuildDataCenter.instance.allGuideFinishPass){
        //         return;
        //     }
        // }
        if (this.nowShowType == BUILD_SHOW_TYPE.BUILD) {
            this.topbox.visible = false;
            this.guaJiBottomBox.visible = false;
            this.ratebtn.visible = false;
            this.bottombox.visible = true;
            this.lotterbtn.visible = true;
            this.boxLeftTop.visible = true;
            this.rightBottomIconLayer.visible = true;
            this.rightTopIconLayer.visible = true;
            this.buildView.visible = true;
            this.battleView.visible = false;
            this.guajiBtn.visible = true;
            this.btnPassReward.visible = true;

        } else if (this.nowShowType == BUILD_SHOW_TYPE.GUAJI) {
            this.topbox.visible = true;
            this.guaJiBottomBox.visible = true;
            this.ratebtn.visible = true;
            this.bottombox.visible = false;
            this.lotterbtn.visible = false;
            this.boxLeftTop.visible = false;
            this.rightBottomIconLayer.visible = false;
            this.rightTopIconLayer.visible = false;
            this.buildView.visible = false;
            this.guajiBtn.visible = false;
            this.btnPassReward.visible = false;
            this.dispatchEvent(ModuleCommand.HIDE_ALL_UI, false);
        }
        //根据关卡显示相关按钮
        if (this.curShowPass <= GuaJiBuildDataCenter.instance.allGuideFinishPass) {

            let curGuide = GuideMgr.ins.curCfg;
            let curStep = GuideMgr.ins.curStep;

            this.boxLeftTop.visible = this.boxLeftTop.visible && this.curShowPass > 1;
            this.btnPassReward.visible = this.btnPassReward.visible && this.curShowPass > 1;
            this.lotterbtn.visible = this.lotterbtn.visible && this.curShowPass > 2;
            this.ratebtn.visible = this.ratebtn.visible && this.curShowPass >= MiscConstAuto.td_main_fight_speed_open;
            this.boxMission.visible = this.isShowBoxMission;
            this.btnQuickFight.visible = this.btnQuickFight.visible && this.curShowPass > 4;
            this.btnBox.visible = this.curShowPass > 4;

            if (this.guajiBtn.visible && curGuide) {
                let isVisible = true;
                if (this.isGuideAllFinish == false) {
                    if (curGuide.guide_id == GuajiBuildGuideScript.guide_story_3000011) {
                        if (this.isScriptHideGuide || this.isCurrHideGuide) {
                            isVisible = false;
                        }
                        else if (curGuide.btn_name._has(this.guajiBtn.name) == false) {
                            isVisible = false;
                        }
                        else if (this.getIsGuideViewVisible(this.guajiBtn.name) == false) {
                            isVisible = false;
                        }
                    } else {
                        isVisible = false;
                    }
                }

                this.guajiBtn.visible = isVisible;
            }
        }
    }

    private fixLeftTopView(){
        if(this.boxLeftTop.destroyed){
            return;
        }

        let isExpand = this.checkExpandState();
        // if(this.oldLeftTopExpand === isExpand){
        //     if (this.curShowPass > GuaJiBuildDataCenter.instance.allGuideFinishPass) {
        //         return;
        //     }
        // }

        this.oldLeftTopExpand = isExpand;
        this.leftIconLayer.visible = isExpand;
        this.lotterbtn.visible = this.lotterbtn.visible && isExpand;
        this.btnPassReward.visible = this.btnPassReward.visible && isExpand;

        this.leftIconLayer.pos(5, 140);
        this.lotterbtn.pos(85, 266);
        this.btnPassReward.pos(0, 350);

        let expandY = 150;
        if (isExpand) {
            this.btnLeftTopExpand.skin = UrlConfig.getMainuiResUrl("img_bg_3");
            if (this.btnPassReward.visible) {
                expandY = 470;
            }else{
                expandY = 380;
            }

            this.btnLeftTopExpand.pos(90, expandY);

        } else {
            this.btnLeftTopExpand.skin = UrlConfig.getMainuiResUrl("img_bg_2");
            this.btnLeftTopExpand.pos(100, expandY);
        }

        this.btnLeftTopExpand.visible = this.curShowPass >= GuaJiBuildDataCenter.instance.allGuideFinishPass;
        this.btnLeftTopExpand.zOrder = 9999;
        // }
    }

    private fixRightTopView() {
        if (this.rightTopIconLayer && this.rightTopIconLayer.destroyed == false && this.rightTopIconLayer.visible) {
            let has = this.hasUiSaveData(this.rightBottomIconLayer.name);
            if (has) {
                let isExpand2 = !!this.getUiSaveData(this.rightTopIconLayer.name);
                this.rightTopIconLayer.setIconVisible(isExpand2);
            }
        }
    }

    private get isShowBoxMission() {
        let isAllFinish = this.isGuideAllFinish;
        let showPass = this.curShowPass > 4;
        return isAllFinish && showPass && BuildMissionDataCenter.ins.hasMainMissions();
    }

    private mainBattleShow() {
        if (this.battleView) {
            this.battleView.visible = true;
            let data = null;
            let fightRecord = FightDataCenter.FIGHT_RECORD_DATA_MAP.get(MatchConst.MATCH_TYPE_MAIN_BATTLE);
            if (fightRecord) {
                data = {
                    matchType: fightRecord.info.match_type,
                    fightData: fightRecord.info,
                    trunIndex: fightRecord.turnIdx,
                    roundIndex: fightRecord.roundIdx,
                    stepIndex: fightRecord.stepIdx,
                };
            }
            this.battleView.onOpen(data);
        }
    }

    //----------------------切换关闭------------------------

    destroy(destroyChild: boolean = true): void {
        // this.showBottomMainIcon();
        if (this.boxLeftTop) {
           this.setUiSaveData(this.boxLeftTop.name, GuaJiDialog.isBoxLeftTopExpand);
        }
        if (this.rightTopIconLayer) {
            this.setUiSaveData(this.rightTopIconLayer.name, this.rightTopIconLayer.expandFlag);
        }
        if (this.rightBottomIconLayer) {
            this.setUiSaveData(this.rightBottomIconLayer.name, this.rightBottomIconLayer.expandFlag);
        }

        if(this.newGuideScript){
            this.newGuideScript.destroy();
            this.newGuideScript = null;
        }

        super.destroy(destroyChild);
        GuaJiDataCenter.isInGuaJiDialog = false;
    }
}