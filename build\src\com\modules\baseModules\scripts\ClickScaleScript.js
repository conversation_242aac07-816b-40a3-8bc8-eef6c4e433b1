import { BaseScript } from "../../BaseScript";
import { Handler } from "laya/utils/Handler";
/**
 * 通用点击缩放
 */
export class ClickScaleScript extends BaseScript {
    constructor() {
        super(...arguments);
        this.oriScaleX = 1;
        this.oriScaleY = 1;
        this.oriX = 0;
        this.oriY = 0;
        this.deltaX = 0;
        this.deltaY = 0;
        this.deltaScale = 0.15;
        this.isDown = false;
        this.isUp = false;
        this.isAnchorCenter = false;
    }
    initUI() {
        this.handlerUp = Handler.create(this, this._onMouseUpComplete, null, false);
        this.handlerDown = Handler.create(this, this._onMouseDownComplete, null, false);
    }
    _initData() {
        if (!this.ownUi.anchorX && !this.ownUi.anchorY && !this.ownUi.pivotX && !this.ownUi.pivotY) {
            this.isAnchorCenter = true;
        }
        else {
            this.isAnchorCenter = false;
        }
        this.oriScaleX = this.ownUi.scaleX;
        this.oriScaleY = this.ownUi.scaleY;
        this.oriX = this.ownUi.x;
        this.oriY = this.ownUi.y;
        this.deltaScale = 0.15;
        let maxScale = 30;
        if (this.ownUi.width * this.deltaScale > maxScale) {
            this.deltaScale = maxScale / this.ownUi.width;
        }
        this.deltaScale *= this.oriScaleX;
    }
    onMouseDown(e) {
        if (this.isDown == true) {
            return;
        }
        this.isDown = true;
        this._initData();
        if (this.isAnchorCenter) {
            this.ownUi.anchorX = 0.5;
            this.ownUi.anchorY = 0.5;
            this.deltaX = Math.round(this.ownUi.width * this.oriScaleX * this.ownUi.anchorX);
            this.deltaY = Math.round(this.ownUi.height * this.oriScaleY * this.ownUi.anchorY);
            this.ownUi.x = this.oriX + this.deltaX;
            this.ownUi.y = this.oriY + this.deltaY;
        }
        this.currTween = this.ownerSpr._tweenTo({ scaleX: this.oriScaleX - this.deltaScale, scaleY: this.oriScaleY - this.deltaScale }, 100, null, this.handlerDown);
        // this.currTween._usedPool = false;
    }
    _onMouseDownComplete() {
        this.currTween = null;
        if (this.isUp) {
            this.onMouseUp(null);
        }
    }
    onMouseUp(e) {
        if (this.isDown) {
            this.isUp = true;
        }
        if (this.isDown == false || this.currTween) {
            return;
        }
        this.currTween = this.ownerSpr._tweenTo({ scaleX: this.oriScaleX, scaleY: this.oriScaleY }, 100, null, this.handlerUp, 0, true, false);
        // this.currTween._usedPool = false;
    }
    _onMouseUpComplete() {
        if (this.isAnchorCenter) {
            this.ownUi.anchorX = 0;
            this.ownUi.anchorY = 0;
            if (this.ownUi.x == this.oriX + this.deltaX) {
                this.ownUi.x = this.oriX;
            }
            if (this.ownUi.y == this.oriY + this.deltaY) {
                this.ownUi.y = this.oriY;
            }
        }
        this.isDown = false;
        this.isUp = false;
        this.currTween = null;
    }
    onMouseOut(e) {
        this.onMouseUp(e);
    }
    onReset() {
        this.isDown = false;
        this.isUp = false;
        this.isAnchorCenter = false;
        this.handlerDown.clear();
        this.handlerDown = null;
        this.handlerUp.clear();
        this.handlerUp = null;
        if (this.currTween) {
            this.currTween.clear();
            this.currTween = null;
        }
    }
}
