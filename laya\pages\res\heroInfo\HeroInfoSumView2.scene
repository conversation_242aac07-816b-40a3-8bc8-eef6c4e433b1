{"x": 0, "type": "BaseView", "selectedBox": 37, "selecteID": 50, "searchKey": "BaseView", "props": {"width": 720, "sceneColor": "#000000", "height": 1056}, "nodeParent": -1, "maxID": 100, "label": "BaseView", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 15, "type": "Box", "searchKey": "Box,topBox,topBox", "props": {"width": 720, "var": "topBox", "top": 0, "name": "topBox", "height": 160}, "nodeParent": 2, "label": "topBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"x": 30, "type": "Box", "searchKey": "Box,btnBox,btnBox", "props": {"y": 0, "width": 400, "var": "btnBox", "right": 0, "name": "btnBox", "height": 160}, "nodeParent": 3, "label": "btnBox", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 88, "child": [{"type": "<PERSON><PERSON>", "searchKey": "Button,btnPowerPre,btnPowerPre", "props": {"y": 19, "x": 100, "var": "btnPowerPre", "stateNum": "1", "skin": "v2_heroInfo/btn_power_preview.png", "name": "btnPowerPre"}, "nodeParent": 88, "label": "btnPowerPre", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"type": "<PERSON><PERSON>", "searchKey": "<PERSON><PERSON>,btn<PERSON>nlock,btnUnlock", "props": {"y": 19, "x": 174, "var": "btnUnlock", "stateNum": "1", "skin": "v2_heroInfo/btn_unlock.png", "name": "btnUnlock"}, "nodeParent": 88, "label": "btnUnlock", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}, {"type": "<PERSON><PERSON>", "searchKey": "Button,btnGet,btnGet", "props": {"y": 19, "x": 246, "var": "btnGet", "stateNum": "1", "skin": "v2_heroInfo/btn_get.png", "name": "btnGet"}, "nodeParent": 88, "label": "btnGet", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}, {"type": "<PERSON><PERSON>", "searchKey": "<PERSON><PERSON>,btnSkin,btnSkin", "props": {"y": 19, "x": 320, "var": "btnSkin", "stateNum": "1", "skin": "v2_heroInfo/btn_skin.png", "name": "btnSkin"}, "nodeParent": 88, "label": "btnSkin", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}, {"type": "<PERSON><PERSON>", "searchKey": "Button,btnCastSoul,btnCastSoul", "props": {"y": 92, "x": 323, "var": "btnCastSoul", "stateNum": "1", "skin": "v2_heroInfo/btn_cast_soul.png", "name": "btnCastSoul"}, "nodeParent": 88, "label": "btnCastSoul", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"type": "<PERSON><PERSON>", "searchKey": "Button,btnSoulReset,btnSoulReset", "props": {"y": 92, "x": 100, "var": "btnSoulReset", "stateNum": "1", "skin": "v2_heroInfo/btn_soul_reset.png", "name": "btnSoulReset"}, "nodeParent": 88, "label": "btnSoulReset", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 82, "child": [], "$HIDDEN": false}, {"type": "<PERSON><PERSON>", "searchKey": "Button,btnSameheart", "props": {"y": 92, "x": 26, "visible": false, "var": "btnSameheart", "stateNum": "1", "skin": "v2_heroInfo/btn_sameheart.png", "mouseEnabled": true}, "nodeParent": 88, "label": "btnSameheart", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 78, "child": [], "$HIDDEN": false}, {"type": "<PERSON><PERSON>", "searchKey": "Button,btnReset,btnReset", "props": {"y": 92, "x": 250, "var": "btnReset", "stateNum": "1", "skin": "v2_heroInfo/btn_reset.png", "name": "btnReset"}, "nodeParent": 88, "label": "btnReset", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}, {"type": "CheckBox", "searchKey": "CheckBox,fiveHeroResonateCheck", "props": {"y": 92, "x": 174, "width": 75, "var": "fiveHeroResonateCheck", "scaleY": 1, "scaleX": 1, "height": 65}, "nodeParent": 88, "label": "fiveHeroResonateCheck", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 93, "child": [{"type": "<PERSON><PERSON>", "searchKey": "Button,btnfiveHeroResonate,btnfiveHeroResonate", "props": {"y": 1, "x": 0, "var": "btnfiveHeroResonate", "stateNum": "1", "skin": "v2_heroInfo/btn_hztx_open.png", "name": "btnfiveHeroResonate"}, "nodeParent": 93, "label": "btnfiveHeroResonate", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 92, "child": []}]}]}, {"x": 30, "type": "Image", "searchKey": "Image,limithero,limithero", "props": {"y": 9.5, "x": 6, "var": "limithero", "skin": "common/limit_hero2.png", "name": "limithero"}, "nodeParent": 3, "label": "limithero", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 96, "child": []}, {"x": 30, "type": "Image", "searchKey": "Image,imgDeputy,imgDeputy", "props": {"y": 24.5, "x": 50, "visible": false, "var": "imgDeputy", "skin": "common/deputy_logo_1.png", "name": "imgDeputy"}, "nodeParent": 3, "label": "imgDeputy", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 97, "child": []}, {"x": 30, "type": "Image", "searchKey": "Image,imgDudu,imgDudu", "props": {"y": 9.5, "x": 95, "var": "imgDudu", "name": "imgDudu"}, "nodeParent": 3, "label": "imgDudu", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 98, "child": [], "$HIDDEN": false}]}, {"x": 15, "type": "Box", "searchKey": "Box,skillBox,skillBox", "props": {"x": 0, "width": 720, "var": "skillBox", "top": 220, "name": "skillBox", "height": 216}, "nodeParent": 2, "label": "skillBox", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 10, "child": [{"type": "UIView", "source": "res/base/SkillItem.scene", "searchKey": "UIView,skillItem1", "props": {"y": 0, "var": "skillItem1", "scaleY": 0.8, "scaleX": 0.8, "left": 20}, "nodeParent": 10, "label": "skillItem1", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": [], "$HIDDEN": false}, {"type": "UIView", "source": "res/base/SkillItem.scene", "searchKey": "UIView,skillItem2", "props": {"y": 0, "var": "skillItem2", "scaleY": 0.8, "scaleX": 0.8, "right": 20}, "nodeParent": 10, "label": "skillItem2", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": [], "$HIDDEN": false}, {"type": "UIView", "source": "res/base/SkillItem.scene", "searchKey": "UIView,skillItem3", "props": {"var": "skillItem3", "scaleY": 0.8, "scaleX": 0.8, "left": 20, "bottom": 0}, "nodeParent": 10, "label": "skillItem3", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": [], "$HIDDEN": false}, {"type": "UIView", "source": "res/base/SkillItem.scene", "searchKey": "UIView,skillItem4", "props": {"var": "skillItem4", "scaleY": 0.8, "scaleX": 0.8, "right": 20, "bottom": 0}, "nodeParent": 10, "label": "skillItem4", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": [], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 15, "type": "Box", "searchKey": "Box,bottomBox,bottomBox", "props": {"x": 0, "width": 720, "var": "bottomBox", "name": "bottomBox", "height": 600, "bottom": 0}, "nodeParent": 2, "label": "bottomBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 15, "child": [{"x": 30, "type": "Box", "searchKey": "Box,leftBox,leftBox", "props": {"y": 74, "width": 300, "var": "leftBox", "name": "leftBox", "left": -300, "height": 540}, "nodeParent": 15, "label": "leftBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 20, "child": [{"x": 45, "type": "Image", "searchKey": "Image", "props": {"y": 33, "skin": "v2_heroInfo/img_panel_bg.png", "sizeGrid": "97,0,76,0", "height": 558}, "nodeParent": 20, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 31, "child": []}, {"x": 45, "type": "Label", "searchKey": "Label", "props": {"y": 115, "x": 7, "text": "等级", "strokeColor": "#000000", "stroke": 1, "fontSize": 22, "color": "#ffffce"}, "nodeParent": 20, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 32, "child": []}, {"x": 45, "type": "FontClip", "searchKey": "FontClip,fcCurrentLv,fcCurrentLv", "props": {"y": 108, "x": 138, "var": "fcCurrentLv", "value": "32", "spaceX": -4, "skin": "v2_heroInfo/img_num_yellow.png", "sheet": "1234567890", "name": "fcCurrentLv", "anchorX": 1}, "nodeParent": 20, "label": "fcCurrentLv", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 33, "child": []}, {"x": 45, "type": "FontClip", "searchKey": "FontClip,fcMaxLv,fcMaxLv", "props": {"y": 114, "x": 137, "var": "fcMaxLv", "value": "/123", "spaceX": 0, "skin": "v2_heroInfo/img_num_white.png", "sheet": "1234567890/", "name": "fcMaxLv"}, "nodeParent": 20, "label": "fcMaxLv", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 34, "child": []}, {"x": 45, "type": "Label", "searchKey": "Label,lbAddLv", "props": {"y": 117, "x": 210, "var": "lbAddLv", "text": "(+75)", "strokeColor": "#000000", "stroke": 1, "fontSize": 20, "color": "#2cc600"}, "nodeParent": 20, "label": "lbAddLv", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 59, "child": []}, {"x": 45, "type": "Box", "searchKey": "Box,upgradeBox,upgradeBox", "props": {"y": 138, "x": 0, "width": 286, "var": "upgradeBox", "name": "upgradeBox", "height": 386}, "nodeParent": 20, "label": "upgradeBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 37, "child": [{"x": 60, "type": "Label", "searchKey": "Label,lbStarStage", "props": {"y": 17, "x": 7, "var": "lbStarStage", "valign": "middle", "text": "进阶", "strokeColor": "#000000", "stroke": 1, "fontSize": 22, "color": "#ffffce", "align": "left"}, "nodeParent": 37, "label": "lbStarStage", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 38, "child": [], "$LOCKED": false, "$HIDDEN": false}, {"x": 60, "type": "Box", "searchKey": "Box,stageBox", "props": {"y": 0, "x": 72, "width": 240, "var": "stageBox", "height": 46}, "nodeParent": 37, "label": "stageBox", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 39, "child": [], "$HIDDEN": false}, {"x": 60, "type": "Box", "searchKey": "Box,starStageBox", "props": {"y": 10, "x": 72, "width": 240, "var": "starStageBox", "height": 46}, "nodeParent": 37, "label": "starStageBox", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 40, "child": [], "$HIDDEN": false}, {"x": 60, "type": "Box", "searchKey": "Box,soulLvBox", "props": {"y": 0, "x": 72, "width": 240, "var": "soulLvBox", "height": 46}, "nodeParent": 37, "label": "soulLvBox", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 41, "child": [], "$HIDDEN": false}, {"x": 60, "type": "UIView", "source": "res/common/CommonCostItem1.scene", "searchKey": "UI<PERSON>iew,silverExpend", "props": {"y": 284, "x": 7, "var": "silverExpend"}, "nodeParent": 37, "label": "silverExpend", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 44, "child": [], "$HIDDEN": false}, {"x": 60, "type": "UIView", "source": "res/common/CommonCostItem1.scene", "searchKey": "UIView,expExpend", "props": {"y": 284, "x": 151, "var": "expExpend"}, "nodeParent": 37, "label": "expExpend", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 45, "child": [], "$HIDDEN": false}, {"x": 60, "type": "Image", "searchKey": "Image,maxLvImg", "props": {"y": 284, "x": 7, "var": "maxLvImg", "skin": "common3/img_limit_level.png"}, "nodeParent": 37, "label": "maxLvImg", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 46, "child": [], "$HIDDEN": false}, {"x": 60, "type": "Label", "searchKey": "Label,maxLvTxt", "props": {"y": 292, "x": 172, "var": "maxLvTxt", "text": "升星可提升上限", "fontSize": 24, "color": "#ffffff"}, "nodeParent": 37, "label": "maxLvTxt", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 47, "child": [], "$HIDDEN": false}, {"x": 60, "type": "<PERSON><PERSON>", "searchKey": "Button,updateBtn,updateBtn", "props": {"y": 325, "x": 37, "var": "updateBtn", "skin": "v2_common/btn_green.png", "name": "updateBtn", "labelStrokeColor": "#7b4826", "labelStroke": 4, "labelSize": 24, "labelColors": "#ffffff,#ffffff,#ffffff", "label": "升级"}, "nodeParent": 37, "label": "updateBtn", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 50, "child": [], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 45, "type": "Box", "searchKey": "Box,notLinkBox,notLinkBox", "props": {"y": 137, "x": 0, "width": 286, "visible": false, "var": "notLinkBox", "name": "notLinkBox", "height": 386}, "nodeParent": 20, "label": "notLinkBox", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 60, "child": [{"type": "Label", "searchKey": "Label,labDesc", "props": {"y": 13, "x": 45, "wordWrap": true, "width": 237, "var": "labDesc", "text": "同调10星以上英雄提升异能英雄等级/星级", "height": 40, "fontSize": 20, "color": "#ffffff"}, "nodeParent": 60, "label": "labDesc", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 62, "child": [], "$HIDDEN": false}, {"type": "<PERSON><PERSON>", "searchKey": "Button,btnGoLink", "props": {"y": 324, "x": 38, "var": "btnGoLink", "skin": "v2_common/btn_green.png", "labelStrokeColor": "#40841f", "labelStroke": 2, "labelSize": 24, "labelColors": "#ffffff,#ffffff,#ffffff", "label": "同调英雄"}, "nodeParent": 60, "label": "btnGoLink", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 63, "child": [], "$HIDDEN": false}, {"type": "<PERSON><PERSON>", "searchKey": "<PERSON><PERSON>,helpbtn,btnHelp", "props": {"y": 13, "x": 3, "width": 36, "var": "btnHelp", "stateNum": "1", "skin": "v2_common/img_help.png", "name": "helpbtn", "label": "227", "height": 36}, "nodeParent": 60, "label": "helpbtn", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 64, "child": [], "$LOCKED": false, "$HIDDEN": false}], "$HIDDEN": true}, {"x": 45, "type": "Box", "searchKey": "Box,linkBox,linkBox", "props": {"y": 137, "x": 0, "width": 286, "visible": false, "var": "linkBox", "name": "linkBox", "height": 386}, "nodeParent": 20, "label": "linkBox", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 65, "child": [{"type": "Box", "searchKey": "Box,linkStateBox", "props": {"y": 1, "x": 58, "width": 243, "var": "linkStateBox", "height": 57}, "nodeParent": 65, "label": "linkStateBox", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 67, "child": [{"type": "Label", "searchKey": "Label", "props": {"y": 13, "x": 55, "text": "同调中", "fontSize": 18, "color": "#ffffff"}, "nodeParent": 67, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 68, "child": [], "$HIDDEN": false}, {"type": "Sprite", "searchKey": "<PERSON><PERSON><PERSON>,spineEff", "props": {"y": 31, "x": 89, "width": 16, "var": "<PERSON><PERSON><PERSON>", "scaleY": 0.8, "scaleX": 0.8, "height": 16}, "nodeParent": 67, "label": "<PERSON><PERSON><PERSON>", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 71, "child": []}, {"type": "Box", "source": "res/hero/HeroHeadItem.scene", "searchKey": "Box,linkHero1", "props": {"y": 7, "x": -55, "var": "linkHero1", "scaleY": 0.5, "scaleX": 0.5}, "nodeParent": 67, "label": "linkHero1", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 69, "child": []}, {"type": "Box", "source": "res/hero/HeroHeadItem.scene", "searchKey": "Box,linkHero2", "props": {"y": 7, "x": 169, "var": "linkHero2", "scaleY": 0.5, "scaleX": 0.5}, "nodeParent": 67, "label": "linkHero2", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 70, "child": []}]}, {"type": "<PERSON><PERSON>", "searchKey": "Button,btnLook", "props": {"y": 324, "x": 38, "var": "btnLook", "skin": "v2_common/btn_green.png", "labelStrokeColor": "#40841f", "labelStroke": 2, "labelColors": "#ffffff,#ffffff,#ffffff", "label": "前往查看"}, "nodeParent": 65, "label": "btnLook", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 72, "child": [], "$HIDDEN": false}], "$HIDDEN": true}, {"x": 45, "type": "Box", "source": "res/heroInfo/HeroEquipItem.scene", "searchKey": "Box,soulHeroBox,soulHeroBox", "props": {"y": 171, "x": 300, "width": 110, "var": "soulHeroBox", "scaleY": 0.5, "scaleX": 0.5, "name": "soulHeroBox", "height": 110, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 20, "label": "soulHeroBox", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 79, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Box", "searchKey": "Box,valueBox", "props": {"y": 245, "x": 0, "width": 190, "var": "valueBox", "height": 145}, "nodeParent": 20, "label": "valueBox", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 52, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Image", "searchKey": "Image,careerImg,careerImg", "props": {"y": 185, "x": 0, "var": "careerImg", "skin": "common/career_1.png", "name": "careerImg"}, "nodeParent": 20, "label": "careerImg", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 57, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Label", "searchKey": "Label,careerLabel", "props": {"y": 199, "x": 54, "var": "<PERSON><PERSON><PERSON><PERSON>", "valign": "middle", "text": "猛将(物理攻击，法术伤害)", "strokeColor": "#000000", "stroke": 1, "fontSize": 20, "color": "#ffffff", "bold": false, "align": "left"}, "nodeParent": 20, "label": "<PERSON><PERSON><PERSON><PERSON>", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 58, "child": [], "$LOCKED": false, "$HIDDEN": false}, {"x": 45, "type": "<PERSON><PERSON>", "searchKey": "Button,btnInfo,btnInfo", "props": {"y": 233, "x": 254, "var": "btnInfo", "stateNum": "1", "skin": "v2_heroInfo/btn_wenhao.png", "scaleY": 0.8, "scaleX": 0.8, "name": "btnInfo"}, "nodeParent": 20, "label": "btnInfo", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 35, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "Box", "searchKey": "Box,rightBox,rightBox", "props": {"y": 74, "width": 244, "var": "rightBox", "right": -244, "name": "rightBox", "height": 540}, "nodeParent": 15, "label": "rightBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 21, "child": [{"x": 45, "type": "<PERSON><PERSON>", "searchKey": "Button,btnDivine,btnDivine", "props": {"y": 17, "x": 62, "var": "btnDivine", "stateNum": "1", "skin": "v2_heroInfo/btn_divine.png", "right": 105, "name": "btnDivine"}, "nodeParent": 21, "label": "btnDivine", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}, {"x": 45, "type": "UIView", "source": "res/heroInfo/HeroEquipItem.scene", "searchKey": "UIView,equipItem1,equipItem1", "props": {"y": 110, "x": 64, "var": "equipItem1", "scaleY": 0.75, "scaleX": 0.75, "name": "equipItem1"}, "nodeParent": 21, "label": "equipItem1", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": [], "$HIDDEN": false}, {"x": 45, "type": "UIView", "source": "res/heroInfo/HeroEquipItem.scene", "searchKey": "UIView,equipItem2,equipItem2", "props": {"y": 110, "x": 153, "var": "equipItem2", "scaleY": 0.75, "scaleX": 0.75, "name": "equipItem2"}, "nodeParent": 21, "label": "equipItem2", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 23, "child": [], "$HIDDEN": false}, {"x": 45, "type": "UIView", "source": "res/heroInfo/HeroEquipItem.scene", "searchKey": "UIView,equipItem3,equipItem3", "props": {"y": 199.75, "x": 64, "var": "equipItem3", "scaleY": 0.75, "scaleX": 0.75, "name": "equipItem3"}, "nodeParent": 21, "label": "equipItem3", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 24, "child": [], "$HIDDEN": false}, {"x": 45, "type": "UIView", "source": "res/heroInfo/HeroEquipItem.scene", "searchKey": "UIView,equipItem4,equipItem4", "props": {"y": 199.75, "x": 153, "var": "equipItem4", "scaleY": 0.75, "scaleX": 0.75, "name": "equipItem4"}, "nodeParent": 21, "label": "equipItem4", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 25, "child": [], "$HIDDEN": false}, {"x": 45, "type": "UIView", "source": "res/heroInfo/HeroEquipItem.scene", "searchKey": "UIView,equipItem5,equipItem5", "props": {"y": 289.75, "x": 64, "var": "equipItem5", "scaleY": 0.75, "scaleX": 0.75, "name": "equipItem5"}, "nodeParent": 21, "label": "equipItem5", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": [], "$HIDDEN": false}, {"x": 45, "type": "UIView", "source": "res/heroInfo/HeroEquipItem.scene", "searchKey": "UIView,equipItem6,equipItem6", "props": {"y": 289.75, "x": 153, "var": "equipItem6", "scaleY": 0.75, "scaleX": 0.75, "name": "equipItem6"}, "nodeParent": 21, "label": "equipItem6", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Box", "source": "res/heroInfo/HeroEquipItem.scene", "searchKey": "Box,equipItem7,equipItem7", "props": {"y": 375, "x": 64, "var": "equipItem7", "scaleY": 0.75, "scaleX": 0.75, "name": "equipItem7"}, "nodeParent": 21, "label": "equipItem7", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 28, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Box", "source": "res/heroInfo/HeroEquipItem.scene", "searchKey": "Box,equipItem8,equipItem8", "props": {"y": 375, "x": 154, "var": "equipItem8", "scaleY": 0.75, "scaleX": 0.75, "name": "equipItem8"}, "nodeParent": 21, "label": "equipItem8", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 30, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Box", "searchKey": "Box,boxEvolveSkill", "props": {"y": -2, "x": 143, "width": 101, "var": "boxEvolveSkill", "height": 103}, "nodeParent": 21, "label": "boxEvolveSkill", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 83, "child": [{"type": "<PERSON><PERSON>", "searchKey": "Button,btnEvolveSkill", "props": {"y": 55, "x": 45, "var": "btnEvolveSkill", "stateNum": "1", "skin": "v2_heroInfo/btn_evolve.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 83, "label": "btnEvolveSkill", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 84, "child": [{"type": "Label", "searchKey": "Label,lbEvolveSkill", "props": {"y": 35, "x": -7, "var": "lbEvolveSkill", "text": "xx级开启", "strokeColor": "#000000", "stroke": 1, "fontSize": 20, "color": "#ffffff"}, "nodeParent": 84, "label": "lbEvolveSkill", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 87, "child": []}], "$HIDDEN": false}, {"type": "UIView", "source": "res/base/SkillItem.scene", "searchKey": "UIView,evolveSkillItem", "props": {"y": -17, "x": -4, "var": "evolveSkillItem", "scaleY": 0.8, "scaleX": 0.8}, "nodeParent": 83, "label": "evolveSkillItem", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 86, "child": [], "$HIDDEN": false}]}, {"x": 45, "type": "<PERSON><PERSON>", "searchKey": "Button,oneKeyUnLoadBtn,oneKeyUnLoadBtn", "props": {"y": 461, "x": 47, "var": "oneKeyUnLoadBtn", "skin": "v2_common/btn_yellow.png", "name": "oneKeyUnLoadBtn", "labelStrokeColor": "#7b4826", "labelStroke": 4, "labelSize": 22, "labelColors": "#ffffff,#ffffff,#ffffff", "label": "一键卸装"}, "nodeParent": 21, "label": "oneKeyUnLoadBtn", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 80, "child": [], "$HIDDEN": false}, {"x": 45, "type": "<PERSON><PERSON>", "searchKey": "<PERSON>ton,onekeyLoadBtn,onekeyLoadBtn", "props": {"y": 461, "x": 47, "var": "onekeyLoadBtn", "skin": "v2_common/btn_green.png", "name": "onekeyLoadBtn", "labelStrokeColor": "#306318", "labelStroke": 4, "labelSize": 24, "labelColors": "#ffffff,#ffffff,#ffffff", "label": "一键装备"}, "nodeParent": 21, "label": "onekeyLoadBtn", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 81, "child": [], "$HIDDEN": false}]}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}