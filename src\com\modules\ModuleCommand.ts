// interface KeyValue {
//     public static readonly   SOCKET_OPENED: string;
//     public static readonly   SOCKET_CLOSED: string;
//     public static readonly   CONFIG_FINISHED: string;
//     public static readonly   DEVICE_WH_CHANGE: string;
//     public static readonly   DEFAULT: string;
// }

export const enum ModuleCommand {
  //主程序启动完成
  START_GAME = "START_GAME",
  SOCKET_CONNECT = "SOCKET_CONNECT",
  SOCKET_OPENED = "SOCKET_OPENED",
  SOCKET_CLOSED = "SOCKET_CLOSED",
  DEVICE_WH_CHANGE = "DEVICE_WH_CHANGE",
  UPDATE_UI_POS = "UPDATE_UI_POS",
  CONFIG_FINISHED = "CONFIG_FINISHED",
  DEFAULT = "DEFAULT",
  REQUEST_LOGIN_SCENE_TOS = "REQUEST_LOGIN_SCENE_TOS",
  /**记录玩家操作状态 */
  REQUEST_LOGIN_CLIENT_CLICK_TOS = "REQUEST_LOGIN_CLIENT_CLICK_TOS",
  EVENT_FRAME = "EVENT_FRAME",
  LOGIN_SUCCESS = "LOGIN_SUCCESS",
  TODAY_TIP_SETTING = "TODAY_TIP_SETTING",
  /**界面关闭回到的界面id */
  CLOSE_BACK_LINK = "close_back_link",
  /**界面关闭返回界面的参数 */
  CLOSE_BACK_PARAM = "close_back_param",
  ON_OPEN_OR_CLOSE_DIALOG = "ON_OPEN_OR_CLOSE_DIALOG",
  ON_HIDE_DIALOG = "ON_HIDE_DIALOG",
  ON_SHOW_HIDE_DIALOG = "ON_SHOW_HIDE_DIALOG",

  SET_DIALOG_RENDER_RECT = "SET_DIALOG_RENDER_RECT",
  RESET_DIALOG_RENDER_RECT = "RESET_DIALOG_RENDER_RECT",

  /**多语言 */
  ON_CHANGE_LANGUAGE = "ON_CHANGE_LANGUAGE",

  //主界面菜单---------------------------------------------
  /**首次进入地图 */
  FIRST_MAP_ENTER = "FIRST_MAP_ENTER",
  UPDATE_SYSTEM_ADD = "UPDATE_SYSTEM_ADD",
  OPEN_CITY_DIALOG = "OPEN_CITY_DIALOG",
  CLOSE_CITY_DIALOG = "CLOSE_CITY_DIALOG",
  OPEN_MENU_SYSTEM_OPEN_DIALOG = "OPEN_MENU_SYSTEM_OPEN_DIALOG",
  CLOSE_MENU_SYSTEM_OPEN_DIALOG = "CLOSE_MENU_SYSTEM_OPEN_DIALOG",
  INIT_MAIN_UI = "INIT_MAIN_UI",
  ON_CHECK_NAV_SHOW = "ON_CHECK_NAV_SHOW",
  REQUEST_CHANGER_MAIN_TAB = "REQUEST_CHANGER_MAIN_TAB",
  FIGHT_MUTE = "FIGHT_MUTE",
  ON_MAIN_TAB_CHANGER = "ON_MAIN_TAB_CHANGER",
  AUTO_MATIC_MAIN_TAB_CHANGER = "AUTO_MATIC_MAIN_TAB_CHANGER",
  ON_CLICK_MAIN_TAB_CHANGER = "ON_CLICK_MAIN_TAB_CHANGER",
  HIDE_TOP_LAYER = "HIDE_TOP_LAYER",
  HIDE_BOTTOM_LAYER = "HIDE_BOTTOM_LAYER",
  SHOW_BOTTOM_BACK = "SHOW_BOTTOM_BACK",
  HILDE_TOP_BOOTTOM = "HILDE_TOP_BOOTTOM",
  UPDATE_TOP_LAYER_ITEM_SHOW = "UPDATE_TOP_LAYER_ITEM_SHOW",
  OPEN_FIGHT_DIALOG = "OPEN_FIGHT_DIALOG",
  MULTI_ENTER_FIGHT = "MULTI_ENTER_FIGHT",
  TO_AUTO_FIGHT = "TO_AUTO_FIGHT",
  NEW_TURN_INDEX = "NEW_TURN_INDEX",
  U_S_V = "U_S_V",
  C_R_S = "C_R_S",
  AUTO_SCENE_ALREADY = "AUTO_SCENE_ALREADY",
  SET_JUMP_BTN_VISIBLE = "SET_JUMP_BTN_VISIBLE",
  OPEN_FIGHT_RESULT_DIALOG = "OPEN_FIGHT_RESULT_DIALOG",
  OPEN_FIGHT_SUCC_DIALOG = "OPEN_FIGHT_SUCC_DIALOG",
  CLOSE_FIGHT_SUCC_DIALOG = "CLOSE_FIGHT_SUCC_DIALOG",
  ON_FIGHT_SUCC_DIALOG_CLOSED = "ON_FIGHT_SUCC_DIALOG_CLOSED",
  OPEN_FIGHT_FAIL_DIALOG = "OPEN_FIGHT_FAIL_DIALOG",
  SET_ICON_LIST = "SET_ICON_LIST",
  LEAVE_FAMILY = "LEAVE_FAMILY",
  HIDE_ALL_UI = "HIDE_ALL_UI",
  CLOSE_LAST_OPEN_MAIN = "CLOSE_LAST_OPEN_MAIN",
  HINT_SHOW_TIP_TOC = "HINT_SHOW_TIP_TOC",
  OPEN_MENU_SYSTEM_TIPS_LOG_DIALOG = "OPEN_MENU_SYSTEM_TIPS_LOG_DIALOG",
  ENTER_NEW_CHAPTER_MAP = "ENTER_NEW_CHAPTER_MAP",
  UPDATE_MENU_ACT_ICON_OPEN_STATE = "UPDATE_MENU_ACT_ICON_OPEN_STATE",
  UPDATE_FIGHT_SIMP_RESULT = "UPDATE_FIGHT_SIMP_RESULT",
  HIDE_MAIN_LAYER = "HIDE_MAIN_LAYER",
  HIDE_MENU_CHAT_VIEW = "HIDE_MENU_CHAT_VIEW",
  ICON_CLIENT_OPEN = "ICON_CLIENT_OPEN",
  SHOW_MAIN_BATTLE = "SHOW_MAIN_BATTLE",
  SHOW_MAIN_BATTLE_2 = "SHOW_MAIN_BATTLE_2",
  BOTTOM_GUAJI_ICON_UPDATE = "BOTTOM_GUAJI_ICON_UPDATE",
  //主界面菜单---------------------------------------------

  //战斗通知
  START_FIGHT = "START_FIGHT",
  FIGHT_RATE = "FIGHT_RATE",
  FIGHT_PAUSE = "FIGHT_PAUSE",
  FIGHT_JUMP = "FIGHT_JUMP",
  FIGHT_REVIEW = "FIGHT_REVIEW",
  FIGHT_FINISH = "FIGHT_FINISH",
  FIGHT_FINISH_DATA = "FIGHT_FINISH_DATA",
  FIGHT_JUMP_ROUND = "FIGHT_JUMP_ROUND",
  FIGHT_CHECK_CLICK_ACTOR = "FIGHT_CHECK_CLICK_ACTOR",
  FIGHT_CLICK_ACTOR = "FIGHT_CLICK_ACTOR",
  SHOW_SKILL_NAME = "SHOW_SKILL_NAME",
  HIDE_SKILL_NAME = "HIDE_SKILL_NAME",
  SHOW_QIMOU_EFF = "SHOW_QIMOU_EFF",
  SHOW_SCENE_EFF = "SHOW_SCENE_EFF",
  UPDATE_BATTER = "UPDATE_BATTER",
  UPDATE_RATE = "UPDATE_RATE",
  FIGHT_FINISH_END = "FIGHT_FINISH_END",
  REPLAY_FIHISHED = "REPLAY_FIHISHED",
  ENTERED_FORMAL_FIGHT = "ENTERED_FORMAL_FIGHT",
  ENTERED_AUTO_FIGHT = "ENTERED_AUTO_FIGHT",
  FIGHT_NEXT_PASS = "FIGHT_NEXT_PASS",

  UPDATE_CONDITION_VIEW = "UPDATE_CONDITION_VIEW",
  // SHOW_FIGHT_MOVIE  = "SHOW_FIGHT_MOVIE",
  CLOSE_FIGHT_DIALOG = "CLOSE_FIGHT_DIALOG",
  OPEN_GUA_JI_REWARD_DIALOG = "OPEN_GUA_JI_REWARD_DIALOG",
  // MODEL_LOAD_COMPLETE  = "MODEL_LOAD_COMPLETE",
  OPEN_FIGHT_STAT_DIALOG = "OPEN_FIGHT_STAT_DIALOG",
  OPEN_FIGHT_STAT2_DIALOG = "OPEN_FIGHT_STAT2_DIALOG",
  FIGHT_NEXT_GUAJI_MAIN_BATTLE = "FIGHT_NEXT_GUAJI_MAIN_BATTLE",
  DROP_BOX_GUAJI_MAIN_BATTLE = "DROP_BOX_GUAJI_MAIN_BATTLE",
  OPEN_GUAJI_COMPOSE_BOX_DIALOG = "OPEN_GUAJI_COMPOSE_BOX_DIALOG",

  UPDATE_FIGHT_ROUND = "UPDATE_FIGHT_ROUND",
  UPDATE_ROUND_HURT = "UPDATE_ROUND_HURT",
  UPDATE_JUMP_ROUND = "UPDATE_JUMP_ROUND",
  /**战斗开始动画 */
  ADD_START_ANI = "ADD_START_ANI",
  HIDE_FIGHT_SCENE_SOME = "HIDE_FIGHT_SCENE_SOME",
  STOP_FIGHT_FOR_GUIDE1 = "STOP_FIGHT_FOR_GUIDE1",
  CHECK_ICON_TIP_VIEW = "CHECK_ICON_TIP_VIEW",
  SHOW_SUB_ICON_VIEW = "SHOW_SUB_ICON_VIEW",
  UPDATE_ICON_VIEW = "UPDATE_ICON_VIEW",
  UPDATE_MiSSION_SHOP_ICON_VIEW = "UPDATE_MiSSION_SHOP_ICON_VIEW",
  //前往------------------------------------------------
  DO_CONFIG_EVENT = "DO_CONFIG_EVENT",
  //前往------------------------------------------------

  START_GAME_MUSIC = "START_GAME_MUSIC",
  START_GAME_SOUND = "START_GAME_SOUND",
  STOP_GAME_MUSIC = "STOP_GAME_MUSIC",
  STOP_GAME_SOUND = "STOP_GAME_SOUND",

  //通用错误---------------------------------------------
  COMMON_ERROR = "COMMON_ERROR",
  //通用错误---------------------------------------------

  //通用错误---------------------------------------------
  OPEN_CCTEST_DIALOG = "OPEN_CCTEST_DIALOG",
  OPEN_CCTEST_DIALOG1 = "OPEN_CCTEST_DIALOG1",
  OPEN_CCTEST_DIALOG2 = "OPEN_CCTEST_DIALOG2",
  OPEN_CCTEST_DIALOG3 = "OPEN_CCTEST_DIALOG3",
  OPEN_CCTEST_DIALOG4 = "OPEN_CCTEST_DIALOG4",
  OPEN_CCTEST_DIALOG5 = "OPEN_CCTEST_DIALOG5",
  OPEN_CCTEST_DIALOG6 = "OPEN_CCTEST_DIALOG6",
  TEST1 = "TEST1",
  CLOSE_CCTEST_DIALOG = "CLOSE_CCTEST_DIALOG",
  //通用错误---------------------------------------------

  //通用次数购买----------------
  UPDATE_BUY_TIMES = "UPDATE_BUY_TIMES",
  //通用次数购买----------------

  //通用信息通知 start ----------------
  UPDATE_COMMON_HERO_REVIVE_INFO = "UPDATE_COMMON_HERO_REVIVE_INFO",
  //通用信息通知 end ----------------

  //登陆---------------------------------------------
  OPEN_LOGIN_DIALOG = "OPEN_LOGIN_DIALOG",
  CLOSE_LOGIN_DIALOG = "CLOSE_LOGIN_DIALOG",
  REQUEST_ROLE_CHOSE_LOGIN_DATA = "REQUEST_ROLE_CHOSE_LOGIN_DATA",
  CLOSE_START_SHOW_DIALOG = "CLOSE_START_SHOW_DIALOG",

  //登陆---------------------------------------------

  //加载---------------------------------------------
  FIRST_LOGIN_SUCCESS = "FIRST_LOGIN_SUCCESS",
  //重连成功
  RECONNECT_SUCCESS = "RECONNECT_SUCCESS",
  LOGIN_RE_CONNECT = "LOGIN_RE_CONNECT",
  OPEN_LOADING_DIALOG = "OPEN_LOADING_DIALOG",
  CLOSE_LOADING_DIALOG = "CLOSE_LOADING_DIALOG",
  OPEN_LOADING_DIALOG2 = "OPEN_LOADING_DIALOG2",
  CLOSE_LOADING_DIALOG2 = "CLOSE_LOADING_DIALOG2",
  LOADING_COMPLETE = "LOADING_COMPLETE",
  PLOT_GAME_INFO = "PLOT_GAME_INFO",
  ON_CONFIG_FILE_PARSE_COMPLETE = "ON_CONFIG_FILE_PARSE_CIMPLETE",
  //加载---------------------------------------------

  //帮助界面---------------------
  OPEN_HELP_DIALOG = "OPEN_HELP_DIALOG",
  CLOSE_HELP_DIALOG = "CLOSE_HELP_DIALOG",
  OPEN_HELP_RULE_DIALOG = "OPEN_HELP_RULE_DIALOG",
  //帮助界面---------------------

  //手册---------------------
  OPEN_BOOK_MISSION_DIALOG = "OPEN_BOOK_MISSION_DIALOG",
  CLOSE_BOOK_MISSION_DIALOG = "CLOSE_BOOK_MISSION_DIALOG",
  UPDATE_BOOK_MISSION = "UPDATE_BOOK_MISSION",
  //手册---------------------

  //寻宝奇兵---------------------
  OPEN_HUNT_ACTIVITY_DIALOG = "OPEN_HUNT_ACTIVITY_DIALOG",
  CLOSE_HUNT_ACTIVITY_DIALOG = "CLOSE_HUNT_ACTIVITY_DIALOG",
  UPDATE_HUNT_ACTIVITY = "UPDATE_HUNT_ACTIVITY",
  OPEN_HUNT_ACTIVITY_BUY_DIALOG = "OPEN_HUNT_ACTIVITY_BUY_DIALOG",
  CLOSE_HUNT_ACTIVITY_BUY_DIALOG = "CLOSE_HUNT_ACTIVITY_BUY_DIALOG",
  //寻宝奇兵---------------------

  //弹窗提示---------------------
  OPEN_TIPS_DIALOG = "OPEN_TIPS_DIALOG",
  CLOSE_TIPS_DIALOG = "CLOSE_TIPS_DIALOG",
  GOODS_TIPS_BTN_CLICK = "GOODS_TIPS_BTN_CLICK",
  AUTO_ROTATE_BACK = "AUTO_ROTATE_BACK",
  CREATE_OTHERS = "CREATE_OTHERS",
  SHOW_WEAPON_LIGHT = "SHOW_WEAPON_LIGHT",
  RESUME_WEAPON = "RESUME_WEAPON",
  UPDATE_WEAPON_BLOOD = "UPDATE_WEAPON_BLOOD",
  NOLOOP_END = "NOLOOP_END",//特效播放结束
  COMPLETE_PLAY = "COMPLETE_PLAY", //完成一次播放
  LOOP_PLAY = "LOOP_PLAY",

  //弹窗提示---------------------

  //选服页---------------------
  OPEN_SELECT_SERVER_DIALOG = "OPEN_SELECT_SERVER_DIALOG",
  CLOSE_SELECT_SERVER_DIALOG = "CLOSE_SELECT_SERVER_DIALOG",
  BACK_SELECTSERVERCHANGED_SELECTSERVER_DATA = "BACK_SELECTSERVERCHANGED_SELECTSERVER_DATA",
  //选服页---------------------

  //跨服组队---------------------
  OPEN_CROSSTEAM_DIALOG = "OPEN_CROSSTEAM_DIALOG",
  OPEN_CROSSTEAM_CREATE_TEAM_DIALOG = "OPEN_CROSSTEAM_CREATE_TEAM_DIALOG",
  OPEN_CHECKOPEN_TEAM_DIALOG = "OPEN_CHECKOPEN_TEAM_DIALOG",
  OPEN_CROSSTEAM_DIFFICULTY_DIALOG = "OPEN_CROSSTEAM_DIFFICULTY_DIALOG",
  OPEN_CROSSTEAM_INVITE_DIALOG = "OPEN_CROSSTEAM_INVITE_DIALOG",
  OPEN_CROSSTEAM_JOIN_DIALOG = "OPEN_CROSSTEAM_JOIN_DIALOG",
  OPEN_CROSSTEAM_UNLOCK_DIALOG = "OPEN_CROSSTEAM_UNLOCK_DIALOG",
  OPEN_LOOK_BOSS_DIALOG = "OPEN_LOOK_BOSS_DIALOG",
  OPEN_CROSS_BOOK_DIALOG = "OPEN_CROSS_BOOK_DIALOG",
  CLOSE_CROSS_BOOK_DIALOG = "CLOSE_CROSS_BOOK_DIALOG",
  OPEN_CROSS_GAINS_DIALOG = "OPEN_CROSS_GAINS_DIALOG",
  OPEN_CROSS_FIRST_PASS_DIALOG = "OPEN_CROSS_FIRST_PASS_DIALOG",
  OPEN_CROSS_PROPERTY_ADD_DIALOG = "OPEN_CROSS_PROPERTY_ADD_DIALOG",
  OPEN_CROSS_ASSIST_TIP_DIALOG = "OPEN_CROSS_ASSIST_TIP_DIALOG",
  TO_MY_CROSS_TEAM = "TO_MY_CROSS_TEAM",
  UPDATE_BOSS_ACTIVITY = "UPDATE_BOSS_ACTIVITY",
  UPDATE_AUTO_FIGHT_TIME = "UPDATE_AUTO_FIGHT_TIME",
  UPEATE_TEAM_BOSS = "UPEATE_TEAM_BOSS",
  OPEN_CROSS_TEAM_LOG_DIALOG = "OPEN_CROSS_TEAM_LOG_DIALOG",
  OPEN_CROSS_TEAM_AWARD_DIALOG = "OPEN_CROSS_TEAM_AWARD_DIALOG",

  CLOSE_CROSSTEAM_DIALOG = "CLOSE_CROSSTEAM_DIALOG",
  CLOSE_CROSSTEAM_CREATE_TEAM_DIALOG = "CLOSE_CROSSTEAM_CREATE_TEAM_DIALOG",
  CLOSE_CROSSTEAM_DIFFICULTY_DIALOG = "CLOSE_CROSSTEAM_DIFFICULTY_DIALOG",
  CLOSE_CROSSTEAM_INVITE_DIALOG = "CLOSE_CROSSTEAM_INVITE_DIALOG",
  CLOSE_CROSSTEAM_JOIN_DIALOG = "CLOSE_CROSSTEAM_JOIN_DIALOG",
  CLOSE_CROSSTEAM_UNLOCK_DIALOG = "CLOSE_CROSSTEAM_UNLOCK_DIALOG",
  TEAM_BOSS_LOGS = "TEAM_BOSS_LOGS",

  //六国争霸图腾激活
  M_CROSS_WAR_TOTEM_ACTIVE_TOC = "M_CROSS_WAR_TOTEM_ACTIVE_TOC",

  //六国争霸出兵翻倍奖励消耗
  OPEN_CROSSREALM_WAR_REWARDDOUBLE_DIALOG = "OPEN_CROSSREALM_WAR_REWARDDOUBLE_DIALOG",
  CLOSE_CROSSREALM_WAR_REWARDDOUBLE_DIALOG = "CLOSE_CROSSREALM_WAR_REWARDDOUBLE_DIALOG",
  CROSSREALM_WAR_REWARDDOUBLE_CHANGE = "CROSSREALM_WAR_REWARDDOUBLE_CHANGE",

  //跨服组队---------------------

  //创角---------------------
  OPEN_CREATE_PLAYER_DIALOG = "OPEN_CREATE_PLAYER_DIALOG",
  CLOSE_CREATE_PLAYER_DIALOG = "CLOSE_CREATE_PLAYER_DIALOG",
  OPEN_FIRST_ENTER_DIALOG = "OPEN_FIRST_ENTER_DIALOG",
  CLOSE_FIRST_ENTER_DIALOG = "CLOSE_FIRST_ENTER_DIALOG",
  //创角---------------------

  //邮件---------------------
  OPEN_LETTER_DIALOG = "OPEN_LETTER_DIALOG",
  CLOSE_LETTER_DIALOG = "CLOSE_LETTER_DIALOG",
  OPEN_LETTER_DETAIL_DIALOG = "OPEN_LETTER_DETAIL_DIALOG",
  CLOSE_LETTER_DETAIL_DIALOG = "CLOSE_LETTER_DETAIL_DIALOG",
  CLOSE_LETTER_ALL_DIALOG = "CLOSE_LETTER_ALL_DIALOG",
  OPEN_LETTERL_SEND_PANEL = "OPEN_LETTERL_SEND_PANEL",
  //邮件---------------------


  //測試界面---------------------
  OPEN_TEST_DIALOG = "OPEN_TEST_DIALOG",
  CLOSE_TEST_DIALOG = "CLOSE_TEST_DIALOG",
  //測試界面---------------------

  //GM面板---------------------
  OPEN_CMD_DIALOG = "OPEN_CMD_DIALOG",
  OPEN_CMD_TEX_DIALOG = "OPEN_CMD_TEX_DIALOG",
  OPEN_GM_SHOP_DIALOG = "OPEN_GM_SHOP_DIALOG",
  REQUEST_GM_SHOP_BUY = "REQUEST_GM_SHOP_BUY",
  OPEN_TEST_INFO_DIALOG = "OPEN_TEST_INFO_DIALOG",
  OPEN_TEST_HERO_MODEL_DIALOG = "OPEN_TEST_HERO_MODEL_DIALOG",
  OPEN_SOUND_TEST_DIALOG = "OPEN_SOUND_TEST_DIALOG",
  OPEN_CCT_LOCAL_IEX_CONFIG_DIR_DIALOG = "OPEN_CCT_LOCAL_IEX_CONFIG_DIR_DIALOG",
  CMD_PREVIEW = "CMD_PREVIEW",
  CLEAN_STAGE_COPY_DATA = "CLEAN_STAGE_COPY_DATA",
  STAGE_COPY_WALK = "STAGE_COPY_WALK",
  SHOW_GM_FIGHT_TOOL = "SHOW_GM_FIGHT_TOOL",

  //骨骼动画相关
  OPEN_SKELETON_EDITOR_DIALOG = "OPEN_SKELETON_EDITOR_DIALOG",
  OPEN_SKELETON_EDITOR_SINGLE_ANI_DIALOG = "OPEN_SKELETON_EDITOR_SINGLE_ANI_DIALOG",
  OPEN_SKELETON_EDITOR_SINGLE_ANI_FRAME_DIALOG = "OPEN_SKELETON_EDITOR_SINGLE_ANI_FRAME_DIALOG",
  OPEN_SKELETON_EDITOR_SINGLE_EFFECT_DIALOG = "OPEN_SKELETON_EDITOR_SINGLE_EFFECT_DIALOG",

  SK_EDITOR_CHANGE_SCALE = "SK_EDITOR_CHANGE_SCALE",
  SK_EDITOR_CHANGE_ACTION = "SK_EDITOR_CHANGE_ACTION",
  SK_EDITOR_CHANGE_ACTION_SELF = "SK_EDITOR_CHANGE_ACTION_SELF",
  SK_EDITOR_CHANGE_SKIN = "SK_EDITOR_CHANGE_SKIN",
  SK_EDITOR_SHOW_ACTIONs = "SK_EDITOR_SHOW_ACTIONs",
  SK_EDITOR_CHANGE_ROTATION = "SK_EDITOR_CHANGE_ROTATION",
  SK_EDITOR_CHANGE_ROTATION_RESET = "SK_EDITOR_CHANGE_ROTATION_RESET",

  //GM面板---------------------

  //聊天面板---------------------
  OPEN_CHAT_DIALOG = "OPEN_CHAT_DIALOG",
  UPDATE_CHAT_MSG = "UPDATE_CHAT_MSG",
  FLUSH_CHAT_MSG = "FLUSH_CHAT_MSG",
  ON_DELETE_PRIVATE_CHAT = "ON_DELETE_PRIVATE_CHAT",
  UPDATE_PRIVATE_CHAT_ROLE_NUM = "UPDATE_PRIVATE_CHAT_ROLE_NUM",
  CLOSE_CHAT_DIALOG = "CLOSE_CHAT_DIALOG",
  EVENT_MENU_CHAT_VIEW_VISIBLE = "EVENT_MENU_CHAT_VIEW_VISIBLE",
  EVENT_SHOW_GOOD_OR_EMOJI = "EVENT_SHOW_GOOD_OR_EMOJI",
  EVENT_PRIVATE_CHAT = "EVENT_PRIVATE_CHAT",
  EVENT_ADD_BROADCAST_TO_CHAT = "EVENT_ADD_BROADCAST_TO_CHAT",
  UPDATE_CHAT_RED_POINT_STATE = "UPDATE_CHAT_RED_POINT_STATE",
  UPDATE_CHAT_ROLE_INFO = "UPDATE_CHAT_ROLE_INFO",
  UPDATE_CHAT_SEND_TIME = "UPDATE_CHAT_SEND_TIME",
  CLEAN_CHAT_MSG = "CLEAN_CHAT_MSG",
  UPDATE_CHAT_MSG_FIGHT_SHARE = "UPDATE_CHAT_MSG_FIGHT_SHARE",
  ROLE_LOOK_SDK_PROFILE = "ROLE_LOOK_SDK_PROFILE",
  CHAT_SEND_SHARE = "CHAT_SEND_SHARE",
  CHAT_SEND_SHARE_TONGJI = "CHAT_SEND_SHARE_TONGJI",
  OPEN_CHAT_FIGHT_SHARE_DIALOG = "OPEN_FIGHT_SHARE_DIALOG",
  OPEN_CHAT_LINEUP_SHARE_DIALOG = "OPEN_CHAT_LINEUP_SHARE_DIALOG",

  OPEN_CHAT_BULLET_SEND_DIALOG = "OPEN_CHAT_BULLET_SEND_DIALOG",
  //聊天面板---------------------

  //广播相关---------------------
  BROADCAST_MSG_MARQUE = "BROADCAST_MSG_MARQUE",
  BROADCAST_SHOW_TOC = "BROADCAST_SHOW_TOC",
  //广播相关---------------------

  //成就面板---------------------
  UPDATE_ACHIEVEMENT_LIST = "UPDATE_ACHIEVEMENT_LIST",
  UPDATE_TIME_ACHIEVEMENT_LIST = "UPDATE_TIME_ACHIEVEMENT_LIST",
  //成就面板---------------------

  //每日任务面板---------------------
  OPEN_DAILY_MISSION_DIALOG = "OPEN_DAILY_MISSION_DIALOG",
  CLOSE_DAILY_MISSION_DIALOG = "CLOSE_DAILY_MISSION_DIALOG",
  UPDATE_DAILY_MISSION_LIST = "UPDATE_DAILY_MISSION_LIST",
  ON_COMPLETE_MISSION_ITEM = "ON_COMPLETE_MISSION_ITEM",
  //每日任务面板---------------------
  //----------功能预告 start ------------
  OPEN_FUNCTION_PRE_DIALOG = "OPEN_FUNCTION_PRE_DIALOG",
  CLOSE_FUNCTION_PRE_DIALOG = "CLOSE_FUNCTION_PRE_DIALOG",
  UPDATE_FUNCTION_PRE_DIALOG = "UPDATE_FUNCTION_PRE_DIALOG",
  //----------功能预告 end ------------
  //背包面板---------------------
  OPEN_BAG_DIALOG = "OPEN_BAG_DIALOG",
  CLOSE_BAG_DIALOG = "CLOSE_BAG_DIALOG",
  OPEN_BAG_COMPOSE_DIALOG = "OPEN_BAG_COMPOSE_DIALOG",
  DELETE_GOODS = "DELETE_GOODS",
  GOODS_UPDATE = "GOODS_UPDATE",
  OPEN_GOOD_TIPS = "OPEN_GOOD_TIPS",
  CLOSE_GOOD_TIPS = "CLOSE_GOOD_TIPS",
  CLOSE_GOODS_TIP_DIALOG = "CLOSE_GOODS_TIP_DIALOG",
  OPEN_GOOD_BOX_TIPS_DIALOG = "OPEN_GOOD_BOX_TIPS_DIALOG",
  CLOSE_GOOD_BOX_TIPS_DIALOG = "CLOSE_GOOD_BOX_TIPS_DIALOG",
  OPEN_BAG_EQUIP_SELL_DIALOG = "OPEN_BAG_EQUIP_SELL_DIALOG",
  OPEN_BAG_BINGFU_SELL_DIALOG = "OPEN_BAG_BINGFU_SELL_DIALOG",
  OPEN_BAG_BINGFA_SELL_DIALOG = "OPEN_BAG_BINGFA_SELL_DIALOG",
  OPEN_GOODS_HERO_TIPS_DIALOG = "OPEN_GOODS_HERO_TIPS_DIALOG",
  OPEN_GOODS_JUE_XING_DIALOG = "OPEN_GOODS_JUE_XING_DIALOG",
  OPEN_GOODS_CONTRAST_DIALOG = "OPEN_GOODS_CONTRAST_DIALOG",
  CLOSE_GOODS_CONTRAST_DIALOG = "CLOSE_GOODS_CONTRAST_DIALOG",
  REQUEST_SELL_GOODS = "REQUEST_SELL_GOODS",
  REQUEST_USE_ITEM = "REQUEST_USE_ITEM",
  REQUEST_LOAD_EQUIP = "REQUEST_LOAD_EQUIP",
  REQUEST_REPLACE_EQUIP = "REQUEST_REPLACE_EQUIP",
  REQUEST_UN_LOAD_EQUIP = "REQUEST_UN_LOAD_EQUIP",
  REQUEST_AUTO_UN_LOAD_EQUIP = "REQUEST_AUTO_UN_LOAD_EQUIP",
  REQUEST_DELETE = "REQUEST_DELETE",
  GOODS_SALE_UPDATE = "GOODS_SALE_UPDATE",
  GOODS_ITEM_COMPOSE = "GOODS_ITEM_COMPOSE",
  OPEN_GOODS_PATH_DIALOG = "OPEN_GOODS_PATH_DIALOG",
  CLOSE_GOODS_PATH_DIALOG = "CLOSE_GOODS_PATH_DIALOG",
  OPEN_GOODS_PATH2_DIALOG = "OPEN_GOODS_PATH2_DIALOG",
  CLOSE_GOODS_PATH2_DIALOG = "CLOSE_GOODS_PATH2_DIALOG",
  REQUEST_OPEN_EUQIPLOAD_EQUIP = "REQUEST_OPEN_EUQIPLOAD_EQUIP",
  OPEN_GO_LINK = "OPEN_GO_LINK",
  GOODS_TIP_SELECT_BOX_ITEM = "GOODS_TIP_SELECT_BOX_ITEM",
  OPEN_GOODS_SELECT_BOX_DIALOG = "OPEN_GOODS_SELECT_BOX_DIALOG",
  ITEM_USE_SUCCEED = "ITEM_USE_SUCCEED",
  UPDATE_ITEM_USE = "UPDATE_ITEM_USE",
  GOODS_BOX_PREVIEW_DIALOG = "GOODS_BOX_PREVIEW_DIALOG",
  REQUEST_SYMBOL = "REQUEST_SYMBOL",
  REQUEST_UNLOAD_SYMBOL_GEM = "REQUEST_UNLOAD_SYMBOL_GEM",
  REQUEST_COMPOSE_GOODS_GEM = "REQUEST_COMPOSE_GOODS_GEM",
  REQUEST_COMPOSE_GOODS_GEM2 = "REQUEST_COMPOSE_GOODS_GEM2",
  REQUEST_REPLACE_GEM = "REQUEST_REPLACE_GEM",
  REQUEST_EQUIP_GOODS_GEM = "REQUEST_EQUIP_GOODS_GEM",
  REQUEST_SYMBOL_APPRAISE = "REQUEST_SYMBOL_APPRAISE",
  REQUEST_SELL_SYMBOL = "REQUEST_SELL_SYMBOL",
  REQUEST_LOAD_ROLE_EQUIP = "REQUEST_LOAD_ROLE_EQUIP",
  REQUEST_UNLOAD_ROLE_EQUIP = "REQUEST_UNLOAD_ROLE_EQUIP",
  REQUEST_LOAD_ROLE_EQUIP_STONE = "REQUEST_LOAD_ROLE_EQUIP_STONE",
  REQUEST_UNLOAD_ROLE_EQUIP_STONE = "REQUEST_UNLOAD_ROLE_EQUIP_STONE",
  SHOW_GOODS_PATH_EFFECT = "SHOW_GOODS_PATH_GUIDE",
  SHOW_GOODS_PATH_TIPS = "SHOW_GOODS_PATH_TIPS",
  SHOW_BAG_GOODS_HAND = "SHOW_BAG_GOODS_HAND",
  OPEN_GOD_EQUIP_SELECT_SELL_DIALOG = "OPEN_GOD_EQUIP_SELECT_SELL_DIALOG",


  //背包面板---------------------

  // 集市 面板---------------------
  OPEN_MYSTERY_DIALOG = "OPEN_MYSTERY_DIALOG",
  UPDATE_SHOP_INFO = "UPDATE_SHOP_INFO",
  SHOP_BUY_SUCCEED = "SHOP_BUY_SUCCEED",
  SHOP_BUY_TOC = "SHOP_BUY_TOC",
  //UPDATE_QUICK_SHOP  = "QUICK_SHOP_INFO_NEW",
  UPDATE_SHOP_RED_POINT = "UPDATE_SHOP_RED_POINT",
  // 集市 面板---------------------

  // 快捷购买 面板 -----------------、
  REQUEST_BUY_SHOP_GOOD = "REQUEST_BUY_SHOP_GOOD",
  CLOSE_QUICK_SHOP_PANEL = "CLOSE_QUICK_SHOP_PANEL",
  QUICK_SHOP_INFO = "QUICK_SHOP_INFO",
  UPDATE_QUICK_SHOP = "UPDATE_QUICK_SHOP",
  OPEN_QUICK_SHOP_BUY_TIP_DIALOG = "OPEN_QUICK_SHOP_BUY_TIP_DIALOG",
  OPEN_SHOP_REWARD_DIALOG = "OPEN_SHOP_REWARD_DIALOG",
  OPEN_RANDOM_BOSS_SHOP_REWARD_DIALOG = "OPEN_RANDOM_BOSS_SHOP_REWARD_DIALOG",
  OPEN_SHOP_GRAIN_DIALOG = "OPEN_SHOP_GRAIN_DIALOG",
  OPEN_SHOP_ITEM_AD_DIALOG = "OPEN_SHOP_ITEM_AD_DIALOG",
  OPEN_SINGLE_SHOP_DIALOG = "OPEN_SINGLE_SHOP_DIALOG",
  CLOSE_SINGLE_SHOP_DIALOG = "CLOSE_SINGLE_SHOP_DIALOG",
  REQUEST_QUICK_SHOP = "REQUEST_QUICK_SHOP",
  OPEN_RANDOM_WAR_METHOD_DIALOG = "OPEN_RANDOM_WAR_METHOD_DIALOG",
  CLOSE_RANDOM_WAR_METHOD_DIALOG = "CLOSE_RANDOM_WAR_METHOD_DIALOG",
  OPEN_RANDOM_WAR_METHOD_RESET_DIALOG = "OPEN_RANDOM_WAR_METHOD_RESET_DIALOG",
  CLOSE_RANDOM_WAR_METHOD_RESET_DIALOG = "CLOSE_RANDOM_WAR_METHOD_RESET_DIALOG",
  UPDATE_WAR_METHOD_DIALOG = "UPDATE_WAR_METHOD_DIALOG",
  OPEN_RANDOM_PVP_METHOD_TIP_SHOW_DIALOG = "OPEN_RANDOM_PVP_METHOD_TIP_SHOW_DIALOG",
  CLOSE_RANDOM_PVP_METHOD_TIP_SHOW_DIALOG = "CLOSE_RANDOM_PVP_METHOD_TIP_SHOW_DIALOG",
  // 快捷购买 面板 -----------------

  // 杂货铺 面板---------------------
  OPEN_OTHER_SHOP_DIALOG = "OPEN_OTHER_SHOP_DIALOG",
  CLOSE_OTHER_SHOP_DIALOG = "CLOSE_OTHER_SHOP_DIALOG",
  OPEN_SHOP_SHOW_DIALOG = "OPEN_SHOP_SHOW_DIALOG",
  CLOSE_SHOP_SHOW_DIALOG = "CLOSE_SHOP_SHOW_DIALOG",
  // 杂货铺 面板---------------------

  // 设置 面板---------------------
  OPEN_ROLE_SETTING_DIALOG = "OPEN_ROLE_SETTING_DIALOG",
  OPEN_GIFT_EXCHANGE_DIALOG = "OPEN_GIFT_EXCHANGE_DIALOG",
  OPEN_COMPLAINTS_DIALOG = "OPEN_COMPLAINTS_DIALOG",
  // 设置 面板---------------------

  // 好友 面板---------------------
  OPEN_FRIEND_DIALOG = "OPEN_FRIEND_DIALOG",
  CLOSE_FRIEND_DIALOG = "CLOSE_FRIEND_DIALOG",
  OPEN_FRIEND_ADD_DIALOG = "OPEN_FRIEND_ADD_DIALOG",
  UPDATE_FRIEND_LIST = "UPDATE_FRIEND_LIST",
  UPDATE_FRIEND_REQUEST_LIST = "UPDATE_FRIEND_REQUEST_LIST",
  UPDATE_FRIEND_BLACK_LIST = "UPDATE_FRIEND_BLACK_LIST",
  ON_FIND_FRIEND_TOC = "ON_FIND_FRIEND_TOC",
  UPDATE_FRIEND_SEND_LIST = "UPDATE_FRIEND_SEND_LIST",
  UPDATE_FRIEND_RECOMMEND_LIST = "UPDATE_FRIEND_RECOMMEND_LIST",
  STOP_FRIEND_SEND_GIFT = "STOP_FRIEND_SEND_GIFT",
  UPDATE_FRIEND_TAB = "UPDATE_FRIEND_TAB",
  SUCCESS_DELETE_FRIEND = "SUCCESS_DELETE_FRIEND",
  // 好友 面板---------------------

  // VIP 面板---------------------
  // OPEN_FREE_VIP_DIALOG  = "OPEN_FREE_VIP_DIALOG",
  // CLOSE_FREE_VIP_DIALOG  = "CLOSE_FREE_VIP_DIALOG",
  // UPDATE_FREE_VIP_MISSION_LIST  = "UPDATE_FREE_VIP_MISSION_LIST",
  // VIP 面板---------------------

  // 排行榜 面板---------------------
  OPEN_RANK_DIALOG = "OPEN_RANK_DIALOG",
  OPEN_RANK_LIST_DIALOG = "OPEN_RANK_LIST_DIALOG",
  OPEN_RANK_DETAIL_DIALOG = "OPEN_RANK_DETAIL_DIALOG",
  OPEN_RANK_MISSION_DIALOG = "OPEN_RANK_MISSION_DIALOG",
  OPEN_RANK_MISSION_TOP5_ROLE_DIALOG = "OPEN_RANK_MISSION_TOP5_ROLE_DIALOG",
  OPEN_SINGLE_RANK_REWARD_SHOW_DIALOG = "OPEN_SINGLE_RANK_REWARD_SHOW_DIALOG",
  OPEN_WORLD_BOSS_LIST_DIALOG = "OPEN_WORLD_BOSS_LIST_DIALOG",
  OPEN_WORLD_BOSS_RANK_REWARD_DIALOG = "OPEN_WORLD_BOSS_RANK_REWARD_DIALOG",
  // OPEN_RANDOM_BOSS_LIST_DIALOG  = "OPEN_RANDOM_BOSS_LIST_DIALOG",
  UPDATE_RANK_LIST_DIALOG = "UPDATE_RANK_LIST_DIALOG",
  UPDATE_RANDOM_BOSS_RANK_LIST_DIALOG = "UPDATE_RANDOM_BOSS_RANK_LIST_DIALOG",
  UPDATE_WORLD_BOSS_REWARD_DIALOG = "UPDATE_WORLD_BOSS_REWARD_DIALOG",
  OPEN_RANK_MORE_TAB_DIALOG = "OPEN_RANK_MORE_TAB_DIALOG",
  COLSE_RANK_MORE_TAB_DIALOG = "COLSE_RANK_MORE_TAB_DIALOG",
  UPDATE_RANK_WORSHIP = "UPDATE_RANK_WORSHIP",
  UPDATE_RANKING_SIMPLE_LIST = "UPDATE_RANK_SIMPLE_LIST",
  UPDATE_CLOSE_RANKING_SIMPLE_LIST = "UPDATE_CLOSE_RANKING_SIMPLE_LIST",
  UPDATE_RANK_HISTORY_LIST = "UPDATE_RANK_HISTORY_LIST",

  UPDATE_RANK_MISSION = "UPDATE_RANK_MISSION",
  FETCH_RANK_REWARD = "FETCH_RANK_REWARD",
  //排行榜膜拜
  UPDATE_RANK_WORSHIP_ITEM = "UPDATE_RANK_WORSHIP_ITEM",
  // 排行榜 面板---------------------

  // 铜币购买 面板---------------------
  OPEN_TAX_DIALOG = "OPEN_TAX_DIALOG",
  OPEN_TAX_FORAGE_DIALOG = "OPEN_TAX_FORAGE_DIALOG",
  OPEN_TAX_HERO_EXP_DIALOG = "OPEN_TAX_HERO_EXP_DIALOG",
  UPDATE_TAX_INFO = "UPDATE_TAX_INFO",
  // 铜币购买 面板---------------------

  // 主线关卡 面板---------------------
  OPEN_MAIN_BATTLE_DIALOG = "OPEN_MAIN_BATTLE_DIALOG",
  // 主线关卡 面板---------------------

  // 挂机系统 面板---------------------
  OPEN_HANGING_DIALOG = "OPEN_HANGING_DIALOG",
  // 挂机系统 面板---------------------

  // 充值 面板---------------------
  OPEN_RECHARGE_DIALOG = "OPEN_RECHARGE_DIALOG",
  CLOSE_RECHARGE_DIALOG = "CLOSE_RECHARGE_DIALOG",
  OPEN_NEW_PAY_DIALOG = "OPEN_NEW_PAY_DIALOG",
  OPEN_NEW_PAY_TIP_DIALOG = "OPEN_NEW_PAY_TIP_DIALOG",
  OPEN_FIRST_RECHARGE_DIALOG = "OPEN_FIRST_RECHARGE_DIALOG",
  CLOSE_FIRST_RECHARGE_DIALOG = "CLOSE_FIRST_RECHARGE_DIALOG",
  OPEN_FIRST_RECHARGE_REWARD_LOOK_DIALOG = "OPEN_FIRST_RECHARGE_REWARD_LOOK_DIALOG",
  OPEN_REPLACE_BUY_DIALOG = "OPEN_REPLACE_BUY_DIALOG",
  OPEN_DAILY_PAY_DIALOG = "OPEN_DAILY_PAY_DIALOG",
  CLOSE_DAILY_PAY_DIALOG = "CLOSE_DAILY_PAY_DIALOG",
  OPEN_FIRE_FOLD_PAY_DIALOG = "OPEN_FIRE_FOLD_PAY_DIALOG",
  CLOSE_FIRE_FOLD_PAY_DIALOG = "CLOSE_FIRE_FOLD_PAY_DIALOG",
  OPEN_FIRE_FOLD_PAY_TIPS = "OPEN_FIRE_FOLD_PAY_TIPS",
  CLOSE_FIRE_FOLD_PAY_TIPS = "CLOSE_FIRE_FOLD_PAY_TIPS",
  UPDATE_FIRE_FOLD_PAY = "UPDATE_FIRE_FOLD_PAY",
  REFRESH_PAY_SHOP_LIST = "REFRESH_PAY_SHOP_LIST",
  PAY_SHOP_LIST = "PAY_SHOP_LIST",
  PAY_SHOP_BUY = "PAY_SHOP_BUY",
  UPDATE_FIRST_PAY_TASK_INFO = "UPDATE_FIRST_PAY_TASK_INFO",
  UPDATE_DAILY_PAY_INFO = "UPDATE_DAILY_PAY_INFO",
  UPDATE_CONTINUE_PAY_INFO = "UPDATE_CONTINUE_PAY_INFO",
  OPEN_CONTINUE_PAY_DIALOG = "OPEN_CONTINUE_PAY_DIALOG",
  // 充值 面板---------------------

  // 称号 面板---------------------
  OPEN_TITLE_VIEW = "OPEN_TITLE_VIEW",
  UPDATE_TITLE_VIEW = "UPDATE_TITLE_VIEW",
  // 称号 面板---------------------

  // 英雄 面板---------------------
  // OPEN_HERO_DIALOG  = "OPEN_HERO_DIALOG",
  // CLOSE_HERO_DIALOG  = "CLOSE_HERO_DIALOG",
  OPEN_ALL_HERO_DIALOG = "OPEN_ALL_HERO_DIALOG",
  UPDATE_HERO_TAB = "UPDATE_HERO_TAB",
  CLOSE_ALL_HERO_DIALOG = "CLOSE_ALL_HERO_DIALOG",
  OPEN_ACT_LIST_DIALOG = "OPEN_ACT_LIST_DIALOG",
  CLOSE_ACT_LIST_DIALOG = "CLOSE_ACT_LIST_DIALOG",
  OPEN_HERO_IFNO_DIALOG = "OPEN_HERO_IFNO_DIALOG",
  CLOSE_HERO_IFNO_DIALOG = "CLOSE_HERO_IFNO_DIALOG",
  OPEN_HERO_SINGLE_IFNO_DIALOG = "OPEN_HERO_SINGLE_IFNO_DIALOG",
  CLOSE_HERO_SINGLE_IFNO_DIALOG = "CLOSE_HERO_SINGLE_IFNO_DIALOG",
  OPEN_HERO_SHOW_DIALOG = "OPEN_HERO_SHOW_DIALOG",
  CLOSE_HERO_SHOW_DIALOG = "CLOSE_HERO_SHOW_DIALOG",
  ON_HERO_SHOW_DIALOG_CLOSE = "ON_HERO_SHOW_DIALOG_CLOSE",
  OPEN_HERO_ATTR_DIALOG = "OPEN_HERO_ATTR_DIALOG",
  CLOSE_HERO_ATTR_DIALOG = "CLOSE_HERO_ATTR_DIALOG",
  OPEN_HERO_ATTR_DESC_TIP_DIALOG = "OPEN_HERO_ATTR_DESC_TIP_DIALOG",
  ADD_NEW_HERO = "ADD_NEW_HERO",
  OPEN_SKILL_INFO_DIALOG = "OPEN_SKILL_INFO_DIALOG",
  OPEN_HERO_STAGE_UPDATE_DIALOG = "OPEN_HERO_STAGE_UPDATE_DIALOG",
  OPEN_HERO_STAR_STAGE_UPDATE_DIALOG = "OPEN_HERO_STAR_STAGE_UPDATE_DIALOG",
  OPEN_HERO_STAR_STAGE_ADDITION_DIALOG = "OPEN_STAR_STAGE_ADDITION_DIALOG",
  OPEN_HERO_RESET_PREVIEW_DISLOG = "OPEN_HERO_RESET_PREVIEW_DISLOG",
  OPEN_HERO_STAGE_UPDATE_COMPLETE_DIALOG = "OPEN_HERO_STAGE_UPDATE_COMPLETE_DIALOG",
  OPEN_HERO_STAR_UPDATE_COMPLETE_DIALOG = "OPEN_HERO_STAR_UPDATE_COMPLETE_DIALOG",
  OPEN_HERO_STAR_UPDATE_GROWTHROAD_DIALOG = "OPEN_HERO_STAR_UPDATE_GROWTHROAD_DIALOG",
  OPEN_HERO_UPGRADE_COMPLETE_DIALOG = "OPEN_HERO_UPGRADE_COMPLETE_DIALOG",
  OPEN_HERO_TUJIAN_INFO_DIALOG = "OPEN_HERO_TUJIAN_INFO_DIALOG",
  OPEN_CAST_SOUL_DIALOG = "OPEN_CAST_SOUL_DIALOG",
  OPEN_CAST_SOUL_COMPARE_DIALOG = "OPEN_CAST_SOUL_COMPARE_DIALOG",
  OPEN_RECAST_SOUL_DIALOG = "OPEN_RECAST_SOUL_DIALOG",
  SELECT_HERO_ACTIVE_CAST_SOUL = "SELECT_HERO_ACTIVE_CAST_SOUL",
  OPEN_CAST_SOUL_SELECT_HERO_DIALOG = "OPEN_CAST_SOUL_SELECT_HERO_DIALOG",
  OPEN_CAST_SOUL_PRE_ATTR_DIALOG = "OPEN_CAST_SOUL_PRE_ATTR_DIALOG",
  UPDATE_CAST_SOUL_INFO = "UPDATE_CAST_SOUL_INFO",
  SHOW_CAST_SOUL_LVUP_EFFECT = "SHOW_CAST_SOUL_LVUP_EFFECT",
  OPEN_HERO_UPGRADE_UNLOCK_TIPS_DIALOG = "OPEN_HERO_UPGRADE_UNLOCK_TIPS_DIALOG",
  SHOW_HERO_UPGRADE_STAR_LEGENDS_TIPS = "SHOW_HERO_UPGRADE_STAR_LEGENDS_TIPS",
  OPEN_HERO_LOCK_DIALOG = "OPEN_HERO_LOCK_DIALOG",
  CLOSE_HERO_LOCK_DIALOG = "CLOSE_HERO_LOCK_DIALOG",
  UPDATE_HERO_ACT_FOURTEEN_LIST = "UPDATE_HERO_ACT_FOURTEEN_LIST",

  OPEN_EVOLVE_SKILL_INFO_DIALOG = "OPEN_EVOLVE_SKILL_INFO_DIALOG",
  OPEN_EVOLVE_SKILL_ACTIVE_DIALOG = "OPEN_EVOLVE_SKILL_ACTIVE_DIALOG",
  UPDATE_EVOLVE_SKILL_INFO = "UPDATE_EVOLVE_SKILL_INFO",

  OPEN_WAR_FLAG_DIALOG = "OPEN_WAR_FLAG_DIALOG",

  UPDATE_HERO_SHOW_WING_STATE = "UPDATE_HERO_SHOW_WING_STATE",//精灵是否解锁
  UPDATE_HERO_WING_LEVEL = "UPDATE_HERO_WING_LEVEL",//精灵升级
  OPEN_WING_STRENGTH_DIALOG = "OPEN_WING_STRENGTH_DIALOG",
  OPEN_WING_TIP_DIALOG = "OPEN_WING_TIP_DIALOG",
  CLOSE_WING_TIP_DIALOG = "CLOSE_WING_TIP_DIALOG",
  UPDATE_WING_RED_POINT = "UPDATE_WING_RED_POINT",

  UPDATE_HERO_SHOW_DEPUTY_STATE = "UPDATE_HERO_SHOW_DEPUTY_STATE",
  OPEN_DEPUTY_STRENGTH_DIALOG = "OPEN_DEPUTY_STRENGTH_DIALOG",
  OPEN_DEPUTY_ACTIVE_DIALOG = "OPEN_DEPUTY_ACTIVE_DIALOG",
  CLOSE_DEPUTY_ACTIVE_DIALOG = "CLOSE_DEPUTY_ACTIVE_DIALOG",
  CLOSE_DEPUTY_STRENGTH_DIALOG = "CLOSE_DEPUTY_STRENGTH_DIALOG",
  OPEN_DEPUTY_LOAD_DIALOG = "OPEN_DEPUTY_LOAD_DIALOG",
  CLOSE_DEPUTY_LOAD_DIALOG = "CLOSE_DEPUTY_LOAD_DIALOG",
  OPEN_DEPUTY_SELECT_HERO_DIALOG = "OPEN_DEPUTY_SELECT_HERO_DIALOG",
  CLOSE_DEPUTY_SELECT_HERO_DIALOG = "CLOSE_DEPUTY_SELECT_HERO_DIALOG",
  OPEN_DEPUTY_SELECT_HERO_TIPS_DIALOG = "OPEN_DEPUTY_SELECT_HERO_TIPS_DIALOG",
  SELECT_DEPUTY_HERO_ID = "SELECT_DEPUTY_HERO_ID",
  OPEN_DEPUTY_TIPS_DIALOG = "OPEN_DEPUTY_TIPS_DIALOG",
  OPEN_DEPUTY_PREVIEW_SKILL_DIALOG = "OPEN_DEPUTY_PREVIEW_SKILL_DIALOG",
  OPEN_DEPUTY_HERO_PREVIEW_SKILL_DIALOG = "OPEN_DEPUTY_HERO_PREVIEW_SKILL_DIALOG",

  //实力预览
  OPEN_HERO_ACTUAL_PREVIEW_DIALOG = "OPEN_HERO_ACTUAL_PREVIEW_DIALOG",
  OPEN_EQUIP_MAX_PREVIEW_DIALOG = "OPEN_EQUIP_MAX_PREVIEW_DIALOG",
  OPEN_GOD_WEAPON_MAX_PREVIEW_DIALOG = "OPEN_GOD_WEAPON_MAX_PREVIEW_DIALOG",
  OPEN_FAMILY_SKILL_MAX_PREVIEW_DIALOG = "OPEN_FAMILY_SKILL_MAX_PREVIEW_DIALOG",
  OPEN_MASTER_CARD_MAX_PREVIEW_DIALOG = "OPEN_MASTER_CARD_MAX_PREVIEW_DIALOG",
  OPEN_BINGFU_PREVIEW_DIALOG = "OPEN_BINGFU_PREVIEW_DIALOG",
  OPEN_WING_MAXLEVEL_DIALOG = "OPEN_WING_MAXLEVEL_DIALOG",
  OPEN_CAST_SOUL_PREVIEW_DIALOG = "OPEN_CAST_SOUL_PREVIEW_DIALOG",

  //---------------------------
  HERO_DEBRIS_FILTER = "HERO_DEBRIS_FILTER",
  REFRESH_HERO_INFO = "REFRESH_HERO_INFO",
  UPDATE_HERO_INFO = "UPDATE_HERO_INFO",
  UPDATE_HERO_COMPOSELOGS_INFO = "UPDATE_HERO_COMPOSELOGS_INFO",
  REFRESH_HERO_LIST_WITH_CAREER = "REFRESH_HERO_LIST_WITH_CAREER",
  REFRESH_HERO_LIST = "REFRESH_HERO_LIST",
  CHANGE_HERO_TAB = "CHANGE_HERO_TAB",
  ON_HERO_UPGRADE = "ON_HERO_UPGRADE",
  OPEN_HERO_KARMA_DIALOG = "OPEN_HERO_KARMA_DIALOG",
  OPEN_HERO_UPDATE_COLOR_DIALOG = "OPEN_HERO_UPDATE_COLOR_DIALOG",
  CLOSE_HERO_UPDATE_COLOR_DIALOG = "CLOSE_HERO_UPDATE_COLOR_DIALOG",
  UPDATE_HERO_KARMA = "UPDATE_HERO_KARMA",
  UPDATE_HERO_BINGFA = "UPDATE_HERO_BINGFA",
  OPEN_HERO_DEBRIS_DIALOG = "OPEN_HERO_DEBRIS_DIALOG",
  CHANGE_HERO_DEBRIS_SELECT = "CHANGE_HERO_DEBRIS_SELECT",
  // OPEN_HERO_INFO_MAXLV_DIALOG = "OPEN_HERO_INFO_MAXLV_DIALOG",
  OPEN_HERO_BINGFA_PREVIEW_DIALOG = "OPEN_HERO_BINGFA_PREVIEW_DIALOG",
  OPEN_HERO_BINGFA_LEVELUP_DIALOG = "OPEN_HERO_BINGFA_LEVELUP_DIALOG",
  CLOSE_HERO_BINGFA_LEVELUP_DIALOG = "CLOSE_HERO_BINGFA_LEVELUP_DIALOG",
  OPEN_HERO_BINGFA_LEVELUP_TIP_DIALOG = "OPEN_HERO_BINGFA_LEVELUP_TIP_DIALOG",
  OPEN_HERO_BINGFA_STUDY_DIALOG = "OPEN_HERO_BINGFA_STUDY_DIALOG",
  OPEN_HERO_BINGFA_CHANGE_DIALOG = "OPEN_HERO_BINGFA_CHANGE_DIALOG",
  CLOSE_HERO_BINGFA_CHANGE_DIALOG = "CLOSE_HERO_BINGFA_CHANGE_DIALOG",
  OPEN_HERO_BINGFA_CHANGE_TIPS_DIALOG = "OPEN_HERO_BINGFA_CHANGE_TIPS_DIALOG",
  OPEN_BINGFA_DETAIL_TIP_DIALOG = "OPEN_BINGFA_DETAIL_TIP_DIALOG",
  OPEN_BINGFA_STUDY_LVUP_DIALOG = "OPEN_BINGFA_STUDY_LVUP_DIALOG",
  OPEN_BINGFA_EXCHANGE_SELECT_HERO_DIALOG = "OPEN_BINGFA_EXCHANGE_SELECT_HERO_DIALOG",
  CLOSE_BINGFA_EXCHANGE_SELECT_HERO_DIALOG = "CLOSE_BINGFA_EXCHANGE_SELECT_HERO_DIALOG",
  OPEN_BINGFA_EXCHANGE_PREVIEW_DIALOG = "OPEN_BINGFA_EXCHANGE_PREVIEW_DIALOG",
  OPEN_HERO_SELL_DIALOG = "OPEN_HERO_SELL_DIALOG",
  OPEN_HERO_RECYCLE_PING_ZHI_DIALOG = "OPEN_HERO_RECYCLE_PING_ZHI_DIALOG",
  OPEN_HERO_DEBRIS_SELECT_DIALOG = "OPEN_HERO_DEBRIS_SELECT_DIALOG",
  OPEN_HERO_DEBRIS_SELECT_FILTER_DIALOG = "OPEN_HERO_DEBRIS_SELECT_FILTER_DIALOG",
  OPEN_HERO_INFO_KARMA_DIALOG = "OPEN_HERO_INFO_KARMA_DIALOG",
  OPEN_HERO_MATERIAL_SELECT_DIALOG = "OPEN_HERO_ITEM_SELECT_DIALOG",
  OPEN_HERO_BINGFU_RECAST_SELECT_SKILL_DIALOG = "OPEN_HERO_BINGFU_RECAST_SELECT_SKILL_DIALOG",
  CLOSE_HERO_BINGFU_RECAST_SELECT_SKILL_DIALOG = "CLOSE_HERO_BINGFU_RECAST_SELECT_SKILL_DIALOG",
  SELECT_BINGFU_RECAST_SKILL = "SELECT_BINGFU_RECAST_SKILL",
  OPEN_HERO_BINGFU_RECAST_SELECT_SKILL_TIPS_DIALOG = "OPEN_HERO_BINGFU_RECAST_SELECT_SKILL_TIPS_DIALOG",
  HERO_INFO_DIALOG_CLOSE = "HERO_INFO_DIALOG_CLOSE",
  OPEN_HERO_INFO_DIALOG = "OPEN_HERO_INFO_DIALOG",
  UPDATE_HERO_WING_INFO = "UPDATE_HERO_WING_INFO",

  OPEN_HERO_INHERIT_DIALOG = "OPEN_HERO_INHERIT_DIALOG",
  OPEN_HERO_INHERIT_SUCC_DIALOG = "OPEN_HERO_INHERIT_SUCC_DIALOG",
  UPDATE_HERO_INHERIT_SUCC = "UPDATE_HERO_INHERIT",

  OPEN_HERO_RECYCLE_DIALOG = "OPEN_HERO_RECYCLE_DIALOG",
  CLOSE_HERO_RECYCLE_DIALOG = "CLOSE_HERO_RECYCLE_DIALOG",
  UPDATE_HERO_RECYCLE_PREVIEW = "UPDATE_HERO_RECYCLE_PREVIEW",
  UPDATE_HERO_LOCK = "UPDATE_HERO_LOCK",
  UPDATE_HERO_BAG_EXPANSION = "UPDATE_HERO_BAG_EXPANSION",
  UPDATE_HERO_TUJIAN_LIST = "UPDATE_HERO_TUJIAN_LIST",
  CHANGE_SUB_TAB = "CHANGE_SUB_TAB",
  UPDATE_HERO_MY_RANK = "UPDATE_HERO_MY_RANK",
  PLAY_ACTIVR_FINAL_STAR_EFF = "PLAY_ACTIVR_FINAL_STAR_EFF",

  //升星面板成长之路
  OPEN_HERO_STAR_UP_GROWTH_ROAD_DIALOG = "OPEN_HERO_STAR_UP_GROWTH_ROAD_DIALOG",
  CLOSE_HERO_STAR_UP_GROWTH_ROAD_DIALOG = "CLOSE_HERO_STAR_UP_GROWTH_ROAD_DIALOG",
  OPEN_HERO_STAR_UP_CONSUME_GO_DIALOG = "OPEN_HERO_STAR_UP_CONSUME_GO_DIALOG",
  UPDATE_STAR_UP_CONSUME_INFO = "UPDATE_STAR_UP_CONSUME_INFO",
  OPEN_HERO_COMPOSE_LOGS_DIALOG = "OPEN_HERO_COMPOSE_LOGS_DIALOG",
  CLOSE_HERO_COMPOSE_LOGS_DIALOG = "CLOSE_HERO_COMPOSE_LOGS_DIALOG",

  // 异能英雄
  OPEN_SOUL_HERO_LINK_SELECT_DIALOG = "OPEN_SOUL_HERO_LINK_SELECT_DIALOG",
  OPEN_SOUL_HERO_LINK_CONFIRM_DIALOG = "OPEN_SOUL_HERO_LINK_CONFIRM_DIALOG",
  OPEN_SOUL_HERO_RESET_DIALOG = "OPEN_SOUL_HERO_RESET_DIALOG",
  UPDATE_SOUL_HERO_INFO = "UPDATE_SOUL_HERO_INFO",
  UPDATE_SOUL_HERO_LINK = "UPDATE_SOUL_HERO_LINK",
  UPDATE_SOUL_HERO_RESET = "UPDATE_SOUL_HERO_RESET",
  UPDATE_SOUL_HERO_UNLOCK = "UPDATE_SOUL_HERO_UNLOCK",

  // 英雄 面板---------------------

  // 英雄助阵-----------------------
  OPEN_RENT_HERO_DIALOG = "OPEN_RENT_HERO_DIALOG",
  UPDATE_RENT_HERO_INFO = "UPDATE_RENT_HERO_INFO",
  ON_GET_BORROW_HERO_INFO = "ON_GET_BORROW_HERO_INFO",

  //技能--------------------------
  UPDATE_HERO_SKILL = "UPDATE_HERO_SKILL",
  OPEN_SKILL_ACTIVE_OR_LV_UP_DIALOG = "OPEN_SKILL_ACTIVE_OR_LV_UP_DIALOG",
  OPEN_SKILL_DETAIL_DIALOG = "OPEN_SKILL_DETAIL_DIALOG",
  OPEN_SKILL_DETAIL_ONLY_SIMPLE_DESC_DIALOG = "OPEN_SKILL_DETAIL_ONLY_SIMPLE_DESC_DIALOG",
  OPEN_SKILL_DETAIL_ONLY_SIMPLE_LEVEL_DESC_DIALOG = "OPEN_SKILL_DETAIL_ONLY_SIMPLE_LEVEL_DESC_DIALOG",
  OPEN_RIDE_SKILL_DETAIL_DIALOG = "OPEN_RIDE_SKILL_DETAIL_DIALOG",
  //技能--------------------------

  // 公会 面板---------------------
  OPEN_FAMILY_DIALOG = "OPEN_FAMILY_DIALOG",
  CLOSE_FAMILY_DIALOG = "CLOSE_FAMILY_DIALOG",
  OPEN_FAMILY_NOT_JOIN_DIALOG = "OPEN_FAMILY_NOT_JOIN_DIALOG",
  CLOSE_FAMILY_NOT_JOIN_DIALOG = "CLOSE_FAMILY_NOT_JOIN_DIALOG",
  OPEN_FAMILY_HALL_DIALOG = "OPEN_FAMILY_HALL_DIALOG",
  CLOSE_FAMILY_HALL_DIALOG = "CLOSE_FAMILY_HALL_DIALOG",
  OPEN_FAMILY_MEMBER_DIALOG = "OPEN_FAMILY_MEMBER_DIALOG",
  CLOSE_FAMILY_MEMBER_DIALOG = "CLOSE_FAMILY_MEMBER_DIALOG",
  OPEN_FAMILY_APPLY_LIST_DIALOG = "OPEN_FAMILY_APPLY_LIST_DIALOG",
  CLOSE_FAMILY_APPLY_LIST_DIALOG = "CLOSE_FAMILY_APPLY_LIST_DIALOG",
  OPEN_FAMILY_QUICK_CHANGE_TIP_DIAlOG = "OPEN_FAMILY_QUICK_CHANGE_TIP_DIAlOG",
  OPEN_FAMILY_QUICK_CHANGE_DIALOG = "OPEN_FAMILY_QUICK_CHANGE_DIALOG",
  OPEN_FAMILY_QUICK_ClENA_DIAlOG = "OPEN_FAMILY_QUICK_ClENA_DIAlOG",
  Family_CLEAN_ALL_UNACTIVE = "Family_CLEAN_ALL_UNACTIVE",
  Family_CLEAN_UNACTIVE = "Family_CLEAN_UNACTIVE",

  CLOSE_FAMILY_QUICK_CHANGE_DIAlOG = "CLOSE_FAMILY_QUICK_CHANGE_DIAlOG",
  OPEN_FAMILY_ALERT_DIALOG = "OPEN_FAMILY_ALERT_DIALOG",
  OPEN_FAMILY_MANAGER_VIEW = "OPEN_FAMILY_MANAGER_VIEW",
  OPEN_FAMILY_BUILD_LV_UP_DIALOG = "OPEN_FAMILY_BUILD_LV_UP_DIALOG",
  CLOSE_FAMILY_BUILD_LV_UP_DIALOG = "CLOSE_FAMILY_BUILD_LV_UP_DIALOG",
  ON_QUIT_FAMILY = "ON_QUIT_FAMILY",
  ON_FAMILY_CHANGE = "ON_FAMILY_CHANGE",
  INFO_FAMILY_LIST = "INFO_FAMILY_LIST",
  OTHER_FAMILY_INFO = "INFO_FAMILY_LIST",
  UPDATE_FAMILY_INFO = "UPDATE_FAMILY_INFO",
  INFO_FAMILY_APPLY_LIST = "INFO_FAMILY_APPLY_LIST",
  INFO_FAMILY_MEMBERS = "INFO_FAMILY_MEMBERS",
  UPDATE_FAMILY_MEMBER = "UPDATE_FAMILY_MEMBER",
  OPEN_FAMILY_CHANGE = "OPEN_FAMILY_CHANGE",
  SUCCESS_FAMIY_QICK_TYPE = 'SUCCESS_FAMIY_QICK_TYPE',
  OPEN_FAMILY_LOG_DIALOG = "OPEN_FAMILY_LOG_DIALOG",
  UPDATE_FAMILY_LOG_DIALOG = "UPDATE_FAMILY_LOG_DIALOG",
  UPDATE_FAMILY_BUILD_LV = "UPDATE_FAMILY_BUILD_LV",
  OPEN_FAMILY_ATTR_TIPS_DIALOG = "OPEN_FAMILY_ATTR_TIPS_DIALOG",
  REQUEST_FAMILY_RANKING_TOS = "REQUEST_FAMILY_RANKING_TOS",
  UPDATE_FAMILY_RANKING_DIALOG = "UPDATE_FAMILY_RANKING_DIALOG",
  UPDATE_WAR_FAMILY_INFO = "UPDATE_WAR_FAMILY_INFO",
  OPEN_FAMILY_RANK_DIALOG = "OPEN_FAMILY_RANK_DIALOG",
  CLOSE_FAMILY_RANK_DIALOG = "CLOSE_FAMILY_RANK_DIALOG",
  UPDATE_JUNLING_NOTICE = "UPDATE_JUNLING_NOTICE",

  OPEN_FAMILY_SCIENCE_DIALOG = "OPEN_FAMILY_SCIENCE_DIALOG",   //打开公会科技界面
  CLOSE_FAMILY_SCIENCE_DIALOG = "CLOSE_FAMILY_SCIENCE_DIALOG",  //关闭公会科技界面
  UPDATE_FAMILY_SCIENCE = "UPDATE_FAMILY_SCIENCE",
  DO_FAMILY_SCIENCE_SUCC = "DO_FAMILY_SCIENCE_SUCC",
  INFO_FAMILY_SIGN = "INFO_FAMILY_SIGN",
  INFO_FAMILY_ACTIVE = "INFO_FAMILY_ACTIVE",
  // 公会 面板---------------------

  //公会战 start ----------------------
  OPEN_FAMILY_WAR_MAINUI_DIALOG = "OPEN_FAMILY_WAR_MAINUI_DIALOG",
  CLOSE_FAMILY_WAR_MAINUI_DIALOG = "CLOSE_FAMILY_WAR_MAINUI_DIALOG",
  OPEN_FAMILY_WAR_ARMY_INFO_DIALOG = "OPEN_FAMILY_WAR_ARMY_INFO_DIALOG",
  OPEN_FAMILY_WAR_ARMY_DEF_LOG_DIALOG = "OPEN_FAMILY_WAR_ARMY_DEF_LOG_DIALOG",
  OPEN_FAMILY_WAR_LIST_DIALOG = "OPEN_FAMILY_WAR_ARMY_LIST_DIALOG",
  OPEN_FAMILY_WAR_ACHIEVEMENT_DIALOG = "OPEN_FAMILY_WAR_ACHIEVEMENT_DIALOG",
  CLOSE_FAMILY_WAR_ACHIEVEMENT_DIALOG = "CLOSE_FAMILY_WAR_ACHIEVEMENT_DIALOG",
  OPEN_FAMILY_WAR_LOG_DIALOG = "OPEN_FAMILY_WAR_LOG_DIALOG",
  OPEN_FAMILY_WAR_SHOP_DIALOG = "OPEN_FAMILY_WAR_SHOP_DIALOG",
  OPEN_FAMILY_WAR_REWARD_DIALOG = "OPEN_FAMILY_WAR_BOX_REWARD_DIALOG",
  UPDATE_FAMILY_WAR_MAINUI_INFO = "UPDATE_FAMILY_WAR_MAINUI_INFO",
  UPDATE_FAMILY_WAR_GROUP_LIST = "UPDATE_FAMILY_WAR_GROUP_LIST",
  UPDATE_FAMILY_WAR_FETCH_LIST = "UPDATE_FAMILY_WAR_FETCH_LIST",
  UPDATE_FAMILY_WAR_BOX_LIST = "UPDATE_FAMILY_WAR_BOX_LIST",
  UPDATE_FAMILY_WAR_SHOP_INFO = "UPDATE_FAMILY_WAR_SHOP_INFO",
  //公会战 end ------------------------

  // 公会BOSS-------------------
  OPEN_FAMILY_BOSS_DIALOG = "OPEN_FAMILY_BOSS_DIALOG",
  CLOSE_FAMILY_BOSS_DIALOG = "CLOSE_FAMILY_BOSS_DIALOG",
  OPEN_FAMILY_BOSS_RANK_REWARD_DIALOG = "OPEN_FAMILY_BOSS_RANK_REWARD_DIALOG",
  CLOSE_FAMILY_BOSS_RANK_REWARD_DIALOG = "CLOSE_FAMILY_BOSS_RANK_REWARD_DIALOG",
  OPEN_FIALILY_BOSS_FIGHT_SUCC_DIALOG = "OPEN_FIALILY_BOSS_FIGHT_SUCC_DIALOG",
  FIGHT_FAMILY_BOSS_FINISH = "FIGHT_FAMILY_BOSS_FINISH",
  UPDATE_FAMILY_BOSS_DIALOG = "UPDATE_FAMILY_BOSS_DIALOG", //更新boss信息和次数
  UPDATE_FAMILY_BOSS = "UPDATE_FAMILY_BOSS", //只更新boss信息
  UPDATE_FAMILY_BOSS_RANK = "UPDATE_FAMILY_BOSS_RANK",
  UPDATE_FAMILY_BOSS_ATTR = "UPDATE_FAMILY_BOSS_ATTR",
  // 公会BOSS-------------------

  // 公会任务 面板---------------------
  OPEN_FAMILY_TASK_DIALOG = "OPEN_FAMILY_TASK_DIALOG",
  CLOSE_FAMILY_TASK_DIALOG = "CLOSE_FAMILY_TASK_DIALOG",
  OPEN_FAMILY_SEND_GIFT_DIALOG = "OPEN_FAMILY_SEND_GIFT_DIALOG",
  CLOSE_FAMILY_SEND_GIFT_DIALOG = "CLOSE_FAMILY_SEND_GIFT_DIALOG",
  UPDATE_FAMILY_TASK_INFO = "UPDATE_FAMILY_TASK_INFO",
  // 公会任务 面板---------------------

  // 公会铸像 面板---------------------
  ON_CLICK_FAMILY_CAST_MODEL = "ON_CLICK_FAMILY_CAST_MODEL",
  OPEN_FAMILY_CAST_DIALOG = "OPEN_FAMILY_COST_DIALOG",
  CLOSE_FAMILY_CAST_DIALOG = "CLOSE_FAMILY_CAST_DIALOG",
  OPEN_FAMILY_CAST_SHOW_DIALOG = "OPEN_FAMILY_CAST_SHOW_DIALOG",
  CLOSE_FAMILY_CAST_SHOW_DIALOG = "CLOSE_FAMILY_CAST_SHOW_DIALOG",
  OPEN_FAMILY_CAST_PLAY_DIALOG = "OPEN_FAMILY_COST_PLAY_DIALOG",
  CLOSE_FAMILY_CAST_PLAY_DIALOG = "CLOSE_FAMILY_COST_PLAY_DIALOG",
  OPEN_FAMILY_CAST_RANK_DIALOG = "OPEN_FAMILY_CAST_RANK_DIALOG",
  OPEN_FAMILY_CAST_PREV_VIEW_REWARD_DIALOG = "OPEN_FAMILY_CAST_PREV_VIEW_REWARD_DIALOG",
  CLOSE_FAMILY_CAST_RANK_DIALOG = "CLOSE_FAMILY_CAST_RANK_DIALOG",
  UPDATE_FAMILY_CAST_INFO = "UPDATE_FAMILY_CAST_INFO",
  UPDATE_FAMILY_CAST_LEVEL = "UPDATE_FAMILY_CAST_LEVEL",
  ON_FAMILY_CAST_ROLE_TOC = "ON_FAMILY_CAST_ROLE_TOC",
  // 公会铸像 面板---------------------

  // 公会试炼 ------------------------
  OPEN_FAMILY_TRIAL_DIALOG = "OPEN_FAMILY_TRIAL_DIALOG",
  CLOSE_FAMILY_TRIAL_DIALOG = "CLOSE_FAMILY_TRIAL_DIALOG",
  OPEN_FAMILY_TRIAL_FIGHT_DIALOG = "OPEN_FAMILY_TRIAL_FIGHT_DIALOG",
  OPEN_FAMILY_TRIAL_FIGHT_SUCC_DIALOG = "OPEN_FAMILY_TRIAL_FIGHT_SUCC_DIALOG",
  OPEN_FAMILY_TRIAL_FIGHT_FAIL_DIALOG = "OPEN_FAMILY_TRIAL_FIGHT_FAIL_DIALOG",
  OPEN_FAMILY_TRIAL_RANK_DIALOG = "OPEN_FAMILY_TRIAL_RANK_DIALOG",
  OPEN_FAMILY_TRIAL_GIFT_DIALOG = "OPEN_FAMILY_TRIAL_GIFT_DIALOG",
  OPEN_FAMILY_TRIAL_HELP_DIALOG = "OPEN_FAMILY_TRIAL_HELP_DIALOG",
  OPEN_FAMILY_TRIAL_HELP_LOGS_DIALOG = "OPEN_FAMILY_TRIAL_HELP_LOGS_DIALOG",
  OPEN_FAMILY_TRIAL_REWARD_DIALOG = "OPEN_FAMILY_TRIAL_REWARD_DIALOG",
  OPEN_FAMILY_TRIAL_FIGHT_LOGS_DIALOG = "OPEN_FAMILY_TRIAL_FIGHT_LOGS_DIALOG",
  UPDATE_FAMILY_TRIAL_MONSTER_INFO = "UPDATE_FAMILY_TRIAL_MONSTER_INFO",
  UPDATE_FAMILY_TRIAL_MONSTER_FIGHT_TIP_INFO = "UPDATE_FAMILY_TRIAL_MONSTER_FIGHT_TIP_INFO",
  UPDATE_FAMILY_TRIAL_GIFT_INFO = "UPDATE_FAMILY_TRIAL_GIFT_INFO",
  UPDATE_FAMILY_TRIAL_BOX_ITEM_INFO = "UPDATE_FAMILY_TRIAL_BOX_ITEM_INFO",
  UPDATE_FAMILY_TRIAL_FIGHT_DIALOG = "UPDATE_FAMILY_TRIAL_FIGHT_DIALOG",
  UPDATE_FAMILY_TRIAL_HELP_DIALOG = "UPDATE_FAMILY_TRIAL_HELP_DIALOG",
  UPDATE_FAMILY_TRIAL_LOGS_DIALOG = "UPDATE_FAMILY_TRIAL_LOGS_DIALOG",
  UPDATE_FAMILY_TRIAL_REWARD_DIALOG = "UPDATE_FAMILY_TRIAL_REWARD_DIALOG",
  UPDATE_FAMILY_TRIAL_MAKE_SURE_DIALOG = "UPDATE_FAMILY_TRIAL_MAKE_SURE_DIALOG",
  UPDATE_FAMILY_TRIAL_RED_POINT = "UPDATE_FAMILY_TRIAL_RED_POINT",
  FAMILY_TRIAL_FINISH = "FAMILY_TRIAL_FINISH",
  // 公会试炼 ------------------------


  // 历练界面 ---------------------
  OPEN_GROW_DIALOG = "OPEN_GROW_DIALOG",
  CLOSE_GROW_DIALOG = "CLOSE_GROW_DIALOG",
  // 历练界面 ---------------------

  // 历练日常副本 ---------------------
  OPEN_GROW_DAILY_MISSION_DIALOG = "OPEN_GROW_DAILY_MISSION_DIALOG",
  CLOSE_GROW_DAILY_MISSION_DIALOG = "CLOSE_GROW_DAILY_MISSION_DIALOG",
  UPDATE_GROW_DAILY_MISSION_INFO = "UPDATE_GROW_DAILY_MISSION_INFO",
  DAILY_COPY_ENTER = "DAILY_COPY_ENTER",
  UPDATE_FIGHT_FINISH_MISSION_INFO = "UPDATE_FIGHT_FINISH_MISSION_INFO",
  GROW_DAILY_FIGHT_FINISH = "GROW_DAILY_FIGHT_FINISH",
  GET_ALL_DAILY_REWARD = "GET_ALL_DAILY_REWARD",
  // 历练日常副本 ---------------------
  // 资源找回-start ---------------------
  OPEN_RESOURCE_RETRIEVE_ALL_DIALOG = "OPEN_RESOURCE_RETRIEVE_ALL_DIALOG",
  UPDATE_RESOURCE_RETRIEVE_CHANGE_NUM = "UPDATE_RESOURCE_RETRIEVE_CHANGE_NUM",
  UPDATE_RESOURCE_RETRIEVE_VIEW = "UPDATE_RESOURCE_RETRIEVE_VIEW",
  OPEN_RESOURCE_RETRIEVE_DIALOG = "OPEN_RESOURCE_RETRIEVE_DIALOG",
  // 资源找回-end ---------------------
  //通用奖励预览、弹窗-------------
  OPEN_PREVIEW_REWARD_DIALOG = "OPEN_PREVIEW_REWARD_DIALOG",
  OPEN_COMMON_REWARD_EFF_TIP_DIALOG = "OPEN_COMMON_REWARD_EFF_TIP_DIALOG",
  OPEN_COMMON_REWARD_TIP_DIALOG = "OPEN_COMMON_REWARD_TIP_DIALOG",
  OPEN_FLY_ATTRIBUTE_CHANGE_DIALOG = "OPEN_FLY_ATTRIBUTE_CHANGE_DIALOG",
  OPEN_FLY_FIGHTING_CHANGE_DIALOG = "OPEN_FLY_FIGHTING_CHANGE_DIALOG",
  CLOSED_COMMON_REWARD_TIP_DIALOG = "CLOSED_COMMON_REWARD_TIP_DIALOG",
  OPEN_COMMON_BG_FRAME = "OPEN_COMMON_BG_FRAME",
  OPEN_TEST_ASTAR_DIALOG = "OPEN_TEST_ASTAR_DIALOG",
  OPEN_COMMON_ATTR_TIPS_DIALOG = "OPEN_COMMON_ATTR_TIPS_DIALOG",
  OPEN_COMMON_DESC_DIALOG = "OPEN_COMMON_DESC_DIALOG",
  OPEN_COMMON_HERO_REVIVE_DIALOG = "OPEN_COMMON_HERO_REVIVE_DIALOG",
  OPEN_BLACK_BG_DIALOG = "OPEN_BLACK_BG_DIALOG",
  CLOSE_BLACK_BG_DIALOG = "CLOSE_BLACK_BG_DIALOG",
  //通用奖励预览、弹窗-------------
  OPEN_ACT_TIME_TIP_DIALOG = "OPEN_ACT_TIME_TIP_DIALOG",

  /** 凌晨跨天了 */
  CHANGE_DAY = "CHANGE_DAY",
  CHANGE_DAY_PRE = "CHANGE_DAY_PRE",

  //角色数据变化-------------------
  /**只要有属性变化就会派发 */
  ROLE_ATTR_CHANGE = "ROLE_ATTR_CHANGE",
  /**等级发生改变 */
  LEVEL_UP = "LEVEL_UP",
  /**玩家货币发生改变 */
  ROLE_CURRENCY_CHANGE = "ROLE_CURRENCY_CHANGE",

  /** 常见的属性变化  begin  */
  ATTR_CHANGE_EXP = "ATTR_CHANGE_EXP",   //玩家经验信息
  ATTR_CHANGE_SILVER = "ATTR_CHANGE_SILVER",   //角色铜币变化
  // ATTR_CHANGE_SILVER_BIND  = "ATTR_CHANGE_SILVER_BIND",   //角色铜币变化，暂时没用
  ATTR_CHANGE_GOLD = "ATTR_CHANGE_GOLD",   //角色钻石变化
  ATTR_CHANGE_GOLD_BIND = "ATTR_CHANGE_GOLD_BIND",   //角色绑定钻石变化
  ATTR_CHANGE_POWER = "ATTR_CHANGE_POWER",   //角色战力变化
  ATTR_CHANGE_STATUS = "ATTR_CHANGE_STATUS",//状态变化
  ATTR_CHANGE_MAX_POWER = "ATTR_CHANGE_MAX_POWER",
  ATTR_CHANGE_PAY_COUNT = "ATTR_CHANGE_PAY_COUNT", //充值次数

  // ATTR_CHANGE_PAY_COUNT  = "ATTR_CHANGE_PAY_COUNT",   //充值次数
  ATTR_CHANGE_PAY_MONEY = "ATTR_CHANGE_PAY_MONEY", //充值总额
  ATTR_CHANGE_REAL_PAY_MONEY = "ATTR_CHANGE_REAL_PAY_MONEY", //真实充值总额
  ATTR_CHANGE_VIP_LEVEL = "ATTR_CHANGE_VIP_LEVEL", //免费VIP等级
  // ATTR_CHANGE_SUPER_VIP_LV  = "ATTR_CHANGE_SUPER_VIP_LV", //贵族VIP等级
  ATTR_CHANGE_FRIEND_POINT = "ATTR_CHANGE_FRIEND_POINT", //好友度
  ATTR_CHANGE_ACTIVE_POINT = "ATTR_CHANGE_ACTIVE_POINT", //活跃度
  ATTR_CHANGE_FAMILY_CONTRIB = "ATTR_CHANGE_FAMILY_CONTRIB", //帮贡
  ATTR_CHANGE_FAMILY_ACTIVE = "ATTR_CHANGE_FAMILY_ACTIVE", //帮会活跃度
  ATTR_CHANGE_PRESTIGE = "ATTR_CHANGE_PRESTIGE", //声望
  ATTR_CHANGE_HONOR = "ATTR_CHANGE_HONOR", //荣耀

  ATTR_CHANGE_HERO_EXP = "ATTR_CHANGE_HERO_EXP", //英雄经验
  ATTR_CHANGE_HERO_SOUL = "ATTR_CHANGE_HERO_SOUL", //将魂
  ATTR_CHANGE_TRAVEL_SCORE = "ATTR_CHANGE_TRAVEL_SCORE", //政务值
  ATTR_CHANGE_HUNT_SCORE = "ATTR_CHANGE_HUNT_SCORE", //寻宝积分
  ATTR_CHANGE_ZHAN_GONG = "ATTR_CHANGE_ZHAN_GONG", //战功
  ATTR_CHANGE_RIDE_SCORE = "ATTR_CHANGE_RIDE_SCORE", //功绩，坐骑积分
  ATTR_CHANGE_VIP_SCORE = "ATTR_CHANGE_VIP_SCORE", //VIP积分
  ATTR_CHANGE_SOUL_GEM2 = "roleattrproperty1018", //天赋
  ATTR_CHANGE_TEMPLE_SOUL = "roleattrproperty1030", //武魂，用于武神庙
  ATTR_CHANGE_FORGE = "ATTR_CHANGE_FORGE", //粮草，来自国战
  ATTR_CHANGE_GONG_XUN = "ATTR_CHANGE_GONG_XUN", //功勋，来自国战
  ATTR_CHANGE_SECRET_SCORE = "ATTR_CHANGE_SECRET_SCORE", //秘宝积分
  ATTR_CHANGE_TUJIAN_SCORE = "ATTR_CHANGE_TUJIAN_SCORE", //图鉴值
  ATTR_CHANGE_NAME = "ATTR_CHANGE_NAME", //改名
  ATTR_CHANGE_EQUIPS_JINGLIAN = "ATTR_CHANGE_EQUIPS_JINGLIAN", //精炼值
  ATTR_CHANGE_HUFU = "ATTR_CHANGE_HUFU", //青铜虎符
  ATTR_CHANGE_FORGING_STONE = "ATTR_CHANGE_FORGING_STONE",//锻造石
  ATTR_CHANGE_PREFIX = "ATTR_CHANGE_PREFIX",//头衔值
  ATTR_CHANGE_PVP_GRADE = "ATTR_CHANGE_PVP_GRADE",//PVP的段位
  ATTR_CHANGE_GOD_STAGE = "ATTR_CHANGE_GOD_STAGE",//成神阶位
  ATTR_CHANGE_HUNT_ACTIVITY = "roleattrproperty1202",//寻宝奖章
  ATTR_CHANGE_ARENA_MEDAL = "roleattrproperty1031",//荣誉勋章
  ATTR_CHANGE_CHAOS_SCORE = "roleattrproperty1032",//混沌宝玉
  ATTR_CHANGE_SOUL_SCORE = "roleattrproperty1033",//战魂宝玉
  ATTR_CHANGE_HERO_STONE = "roleattrproperty1034",//战魂宝玉
  ATTR_CHANGE_SPRING_MISSION_SCORE = "ATTR_CHANGE_SPRING_MISSION_SCORE",//新春庆典活跃值
  /** 常见的属性变化  end  */

  //玩家信息界面---------------------
  OPEN_ROLE_INFO_DIALOG = "OPEN_ROLE_INFO_DIALOG",
  ON_CLOSE_ROLE_INFO_DIALOG = "CLOSE_ROLE_INFO_DIALOG",
  OPEN_ROLE_HEAD_DIALOG = "OPEN_ROLE_HEAD_DIALOG",
  OPEN_RENAME_DIALOG = "OPEN_RENAME_DIALOG",
  UPDATE_ROLE_HEAD_FRAME = "UPDATE_ROLE_HEAD_FRAME",
  UPDATE_ROLE_HEAD = "UPDATE_ROLE_HEAD",
  UPDATE_ROLE_SHOW = "UPDATE_ROLE_SHOW",
  UPDATE_ROLE_CHAT_SKIN = "UPDATE_ROLE_CHAT_SKIN",
  UPDATE_ROLE_TILE_INFO = "UPDATE_ROLE_TILE_INFO",
  CLOSE_ROLE_RENAME_DIALOG = "CLOSE_ROLE_RENAME_DIALOG",
  UPDATE_ROLE_RENAME_INFO = "UPDATE_ROLE_RENAME_INFO",
  CHANGE_ORDER_BY_FAMILY = "CHANGE_ORDER_BY_FAMILY",
  //玩家信息---------------------

  OPEN_ROLE_OTHER_INFO_DIALOG = "OPEN_ROLE_OTHER_INFO_DIALOG",
  CLOSE_ROLE_OTHER_INFO_DIALOG = "CLOSE_ROLE_OTHER_INFO_DIALOG",
  OPEN_ROLE_OTHER_HERO_INFO_DIALOG = "OPEN_ROLE_OTHER_HERO_INFO_DIALOG",
  OPEN_ROLE_OTHER_HERO_ATTR_SOURCE_DIALOG = "OPEN_ROLE_OTHER_HERO_ATTR_SOURCE_DIALOG",
  UPDATE_ROLE_OTHER_HERO_ATTR_SOURCE = "UPDATE_ROLE_OTHER_HERO_ATTR_SOURCE",
  OPEN_LEVEL_UP_REWARD_DIALOG = "OPEN_LEVEL_UP_REWARD_DIALOG",
  CHECK_FIGHT_CHANGE_SCENE = "CHECK_FIGHT_CHANGE_SCENE",
  OPEN_ROLE_FRAME_TIPS_DIALOG = "OPEN_ROLE_FRAME_TIPS_DIALOG",
  OPEN_SHOWSKIN_DIALOG = "OPEN_SHOWSKIN_DIALOG",
  CLOSE_SHOWSKIN_DIALOG = "CLOSE_SHOWSKIN_DIALOG",
  OPEN_ROLE_SCENE_EFFECT_DIALOG = "OPEN_ROLE_SCENE_EFFECT_DIALOG",
  OPEN_ROLE_SCENE_EFFECT_TIP_DIALOG = "OPEN_ROLE_SCENE_EFFECT_TIP_DIALOG",
  //政务系统
  OPEN_TRAVEL_DIALOG = "OPEN_TRAVEL_DIALOG",
  CLOSE_TRAVEL_DIALOG = "CLOSE_TRAVEL_DIALOG",
  UPDATE_SINGLE_TRAVEL = "UPDATE_SINGLE_TRAVEL",
  OPEN_TRAVEL_HERO_DIALOG = "OPEN_TRAVEL_HERO_DIALOG",
  CLOSE_TRAVEL__HERO_DIALOG = "CLOSE_TRAVEL__HERO_DIALOG",
  UPDATE_TRAVEL_INFO = "UPDATE_TRAVEL_INFO",
  ACCEPT_TRAVEL_EFFECT = "ACCEPT_TRAVEL_EFFECT",
  OPEN_TRAVEL_PRIVILEGE_DIALOG = "OPEN_TRAVEL_PRIVILEGE_DIALOG",

  //抽卡系统
  OPEN_LOTTERY_DIALOG = "OPEN_LOTTERY_DIALOG",
  CLOSE_LOTTERY_DIALOG = "CLOSE_LOTTERY_DIALOG",
  OPEN_LOTTERY_GAIN_DIALOG = "OPEN_LOTTERY_GAIN_DIALOG",
  OPEN_LOTTERY_YUNTAI_REWARD_DIALOG = "OPEN_LOTTERY_YUNTAI_REWARD_DIALOG",
  UPDATE_LOTTERY_YUNTAI_INFO = "UPDATE_LOTTERY_YUNTAI_INFO",
  CLOSE_LOTTERY_GAIN_DIALOG = "CLOSE_LOTTERY_GAIN_DIALOG",
  OPEN_LOTTERY_BETTER_DIALOG = "OPEN_LOTTERY_BETTER_DIALOG",
  UPDATE_LOTTERY_INFO = "UPDATE_LOTTERY_INFO",
  LOTTERY_START_TOC = "LOTTERY_START_TOC",
  UPDATE_LOTTERY_REDPOINT = "UPDATE_LOTTERY_REDPOINT",
  UPDATE_LOTTERY_SUPREME_INFO = "UPDATE_LOTTERY_SUPREME_INFO",
  // UPDATE_LOTTERY_WISH_INFO  = "UPDATE_LOTTERY_WISH_INFO",
  ON_REQUEST_LOTTERY = "ON_REQUEST_LOTTERY",
  OPEN_LOTTERY_NORMAL_REWARD_SHOW_DIALOG = "OPEN_LOTTERY_NORMAL_REWARD_SHOW_DIALOG",
  OPEN_LOTTERY_BETTER_REWARD_SHOW_DIALOG = "OPEN_LOTTERY_BETTER_REWARD_SHOW_DIALOG",
  // OPEN_LOTTERY_WISH_DIALOG  = "OPEN_LOTTERY_WISH_DIALOG",
  //OPEN_LOTTERY_NATION_SELECT_DIALOG  = "OPEN_LOTTERY_NATION_SELECT_DIALOG",
  ON_LOTTERY_GAIN_DIALOG_CLOSE = "ON_LOTTERY_GAIN_DIALOG_CLOSE",
  REQUEST_LOTTERY_CHANGE_TAB = "REQUEST_LOTTERY_CHANGE_TAB",
  UPDATE_LOTTERY_NATION_SELECT = "UPDATE_LOTTERY_NATION_SELECT",
  LOTTERY_SKIP_SETTING_CHANGE = "LOTTERY_SKIP_SETTING_CHANGE",
  LOTTERY_LOWER_OUT_SETTING_CHANGE = "LOTTERY_LOWER_OUT_SETTING_CHANGE",
  LOTTERY_LOWER_OUT_CHANGE = "LOTTERY_LOWER_OUT_CHANGE",
  OPEN_LOTTERY_HERO_LOCK_DIALOG = "OPEN_LOTTERY_HERO_LOCK_DIALOG",
  LOTTERY_NATION_ITEM_CLICK = "LOTTERY_NATION_ITEM_CLICK",
  LOTTERY_YUNTAI_NATION_ITEM_CLICK = "LOTTERY_YUNTAI_NATION_ITEM_CLICK",
  OPEN_LOTTERY_YUNTAI_DIALOG = "OPEN_LOTTERY_YUNTAI_DIALOG",
  OPEN_LOTTERY_SCORE_DIALOG = "OPEN_LOTTERY_SCORE_DIALOG",
  UPDATE_HERO_CONVERT_INFO = "UPDATE_HERO_CONVERT_INFO",
  OPEN_LOTTERY_YUNTAI_REWARD_PRE_DIALOG = "OPEN_LOTTERY_YUNTAI_REWARD_PRE_DIALOG",
  OPEN_LOTTERY_YUNTAI_SHOPS_DIALOG = "OPEN_LOTTERY_YUNTAI_SHOPS_DIALOG",
  OPEN_LOTTERY_ZHIZUN_PREVIEW_DIALOG = "OPEN_LOTTERY_ZHIZUN_PREVIEW_DIALOG",
  OPEN_LOTTERY_ZHIZUN_HERO_SELECT_DIALOG = "OPEN_LOTTERY_ZHIZUN_HERO_SELECT_DIALOG",

  //铁匠铺
  OPEN_FORGE_DIALOG = "OPEN_FORGE_DIALOG",
  CLOSE_FORGE_DIALOG = "CLOSE_FORGE_DIALOG",
  OPEN_FORGE_DACHENG_DIALOG = "OPEN_FORGE_DACHENG_DIALOG",
  UPDATE_FORGE_INFO = "UPDATE_FORGE_INFO",
  EQUIP_REINFORCE_TOC = "EQUIP_REINFORCE_TOC",
  EQUIP_REINFORCE_SUCC_TIPS = "EQUIP_REINFORCE_SUCC_TIPS",
  OPEN_FORGE_CARVE_SUIT_DIALOG = "OPEN_FORGE_CARVE_SUIT_DIALOG",
  OPEN_FORGE_CARVE_TIPS_DIALOG = "OPEN_FORGE_CARVE_TIPS_DIALOG",
  OPEN_FORGE_CARVE_JI_HUO_DIALOG = "OPEN_FORGE_CARVE_JI_HUO_DIALOG",
  OPEN_FORGE_SUIT_DIALOG = "OPEN_FORGE_SUIT_DIALOG",
  FORGE_FLY_EFFECT = "FORGE_FLY_EFFECT",
  OPEN_FORGESTAR_SELECTCOLOR_DIALOG = "OPEN_FORGESTAR_SELECTCOLOR_DIALOG",
  CLOSE_FORGESTAR_SELECTCOLOR_DIALOG = "CLOSE_FORGESTAR_SELECTCOLOR_DIALOG",
  UPDATE_EQUIP_FILTER_INFO_TOC = "UPDATE_EQUIP_FILTER_INFO_TOC",

  OPEN_FORGE_STAR_SKILL_DIALOG = "OPEN_FORGE_STAR_SKILL_DIALOG",
  CLOSE_FORGE_STAR_SKILL_DIALOG = "CLOSE_FORGE_STAR_SKILL_DIALOG",

  OPEN_FORGE_STAR_DEBRIS_DIALOG = "OPEN_FORGE_STAR_DEBRIS_DIALOG",
  CLOSE_FORGE_STAR_DEBRIS_DIALOG = "CLOSE_FORGE_STAR_DEBRIS_DIALOG",

  CLEAR_STAR_COST_ITEM = "CLEAR_STAR_COST_ITEM",
  EQUIP_STAR_OP = "EQUIP_STAR_OP",
  OPEN_FORGE_STAR_TIPS_DIALOG = "OPEN_FORGE_STAR_TIPS_DIALOG",
  CLOSE_FORGE_STAR_TIPS_DIALOG = "CLOSE_FORGE_STAR_TIPS_DIALOG",

  //装备
  OPEN_EQUIP_LOAD_DIALOG = "OPEN_EQUIP_LOAD_DIALOG",
  CLOSE_EQUIP_LOAD_DIALOG = "CLOSE_EQUIP_LOAD_DIALOG",
  OPEN_EQUIP_BINGFU_LOAD_DIALOG = "OPEN_EQUIP_BINGFU_LOAD_DIALOG",
  CLOSE_EQUIP_BINGFU_LOAD_DIALOG = "CLOSE_EQUIP_BINGFU_LOAD_DIALOG",
  OPEN_EQUIP_SELECT_DIALOG = "OPEN_EQUIP_SELECT_DIALOG",
  CLOSE_EQUIP_SELECT_DIALOG = "CLOSE_EQUIP_SELECT_DIALOG",
  OPEN_EQUIP_RECYCLE_PING_ZHI_DIALOG = "OPEN_EQUIP_RECYCLE_PING_ZHI_DIALOG",
  OPEN_EQUIP_RETURN_DIALOG = "OPEN_EQUIP_RETURN_DIALOG",
  CLOSE_EQUIP_RETURN_DIALOG = "CLOSE_EQUIP_RETURN_DIALOG",
  OPEN_EQUIP_SELL_DIALOG = "OPEN_EQUIP_SELL_DIALOG",
  OPEN_EQUIP_COMPOSE_TIPS_DIALOG = "OPEN_EQUIP_COMPOSE_TIPS_DIALOG",
  LOAD_EQUIP = "LOAD_EQUIP",
  OPEN_EQUIP_COMPOUND_BATCH_DIALOG = "OPEN_EQUIP_COMPOUND_BATCH_DIALOG",
  OPEN_EQUIP_SUIT_ACTIVATE_DIALOG = "OPEN_EQUIP_SUIT_ACTIVATE_DIALOG",
  OPEN_EQUIP_RIDE_DIALOG = "OPEN_EQUIP_RIDE_DIALOG",
  CLOSE_EQUIP_RIDE_DIALOG = "CLOSE_EQUIP_RIDE_DIALOG",
  OPEN_SMITHY_DIALOG = "OPEN_SMITHY_DIALOG",
  CLOSE_SMITHY_DIALOG = "CLOSE_SMITHY_DIALOG",
  OPEN_EQUIP_COMPOUND_DIALOG = "OPEN_EQUIP_COMPOUND_DIALOG",
  OPEN_EQUIP_RECYC_NEW_DIALOG = "OPEN_EQUIP_RECYC_NEW_DIALOG",
  OPEN_EQUIP_BATCH_RECYC_DIALOG = "OPEN_EQUIP_BATCH_RECYC_DIALOG",
  OPEN_GOD_EQUIP_LOAD_DIALOG = "OPEN_GOD_EQUIP_LOAD_DIALOG",
  UPDATE_GOD_EQUIP_LOAD_DIALOG = "UPDATE_GOD_EQUIP_LOAD_DIALOG",
  CLOSE_GOD_EQUIP_LOAD_DIALOG = "CLOSE_GOD_EQUIP_LOAD_DIALOG",
  OPEN_GOD_EQUIP_TUJIAN_DIALOG = "OPEN_GOD_EQUIP_TUJIAN_DIALOG",
  OPEN_GOD_EQUIP_ADD_POWER_DIALOG = "OPEN_GOD_EQUIP_ADD_POWER_DIALOG",
  OPEN_GOD_EQUIP_TIP_DIALOG = "OPEN_GOD_EQUIP_TIP_DIALOG",
  OPEN_GOD_EQUIP_DESC_TIP_DIALOG = "OPEN_GOD_EQUIP_DESC_TIP_DIALOG",
  OPEN_GOD_EQUIP_SELL_DIALOG = "OPEN_GOD_EQUIP_SELL_DIALOG",
  OPEN_GOD_EQUIP_RECAST_DIALOG = "OPEN_GOD_EQUIP_RECAST_DIALOG",
  OPEN_GOD_EQUIP_CONVERSION_DIALOG = "OPEN_GOD_EQUIP_CONVERSION_DIALOG",
  OPEN_GOD_EQUIP_CONVERSION_SELECT_DIALOG = "OPEN_GOD_EQUIP_CONVERSION_SELECT_DIALOG",
  CLOSE_GOD_EQUIP_CONVERSION_SELECT_DIALOG = "CLOSE_GOD_EQUIP_CONVERSION_SELECT_DIALOG",
  OPEN_RIDE_RECAST_DIALOG = "OPEN_RIDE_RECAST_DIALOG",
  OPEN_BINGFU_SKILL_DETAIL_DIALOG = "OPEN_BINGFU_SKILL_DETAIL_DIALOG",
  UPDATE_GOD_EQUIP_RECAST = "UPDATE_GOD_EQUIP_RECAST",
  UPDATE_GOD_EQUIP_CONVERT = "UPDATE_GOD_EQUIP_CONVERT",
  SELECT_GOD_EQUIP = "SELECT_GOD_EQUIP",
  OPEN_GOD_EQUIP_HEROLIST_DIALOG = "OPEN_GOD_EQUIP_HEROLIST_DIALOG",
  CLOSE_GOD_EQUIP_HEROLIST_DIALOG = "CLOSE_GOD_EQUIP_HEROLIST_DIALOG",
  OPEN_GOD_EQUIP_ENCHANT_SKILL_TIP = "OPEN_GOD_EQUIP_ENCHANT_SKILL_TIP",

  //英雄装备变化
  HERO_EQUIP_CHANGE_UPDATE = "HERO_EQUIP_CHANGE_UPDATE",

  //穿脱装备更新派发
  UPDATE_EQUIP_STATE = "UPDATE_EQUIP_STATE",
  /*装备分解数据*/
  EQUIP_SELECT_RECYCLE_DATA = "EQUIP_SELECT_RECYCLE_DATA",
  /**装备按品质分解 */
  EQUIP_SELECT_COLOR_DATA = "EQUIP_SELECT_COLOR_DATA",
  /**装备分解结果*/
  EQUIP_DECOMPOSE_TOC = "EQUIP_DECOMPOSE_TOC",
  UPDATE_BING_RECAST_TOC = "UPDATE_BING_RECAST_TOC",
  EQUIP_DECOMPOSE_JIAN_UPDATE = "EQUIP_DECOMPOSE_JIAN_UPDATE",
  EQUIP_DALE_GOODS_SELECT = "EQUIP_DALE_GOODS_SELECT",
  EQUIP_HERO_SELECT = "EQUIP_HERO_SELECT",
  HERO_RECYCLE = "HERO_RECYCLE",
  EQUIP_SELECT_FEIJIE = "EQUIP_SELECT_FEIJIE",
  HERO_RECYCLE_NULL = "HERO_RECYCLE_NULL",
  HERO_RECYCLE_GAINS_ITEM = "HERO_RECYCLE_GAINS_ITEM",
  HERO_RECYCLE_ALL_REBIRTH = "HERO_RECYCLE_ALL_REBIRTH",
  /**选中战魂分解 */
  HERO_SOUL_SELECT_RECYCLE_DATA = "HERO_SOUL_SELECT_RECYCLE_DATA",
  UPDATE_HERO_SOUL_SELECT_COLOR_DATA = "UPDATE_HERO_SOUL_SELECT_COLOR_DATA",
  HERO_SOUL_DECOMPOSE_TOC = "HERO_SOUL_DECOMPOSE_TOC",

  //神器分解
  UPDATE_SWEAPON_RECYCLE_VIEW = "UPDATE_SWEAPON_RECYCLE_VIEW",

  FORGE_EQUIP_STAR_SELECT = "FORGE_EQUIP_STAR_SELECT",
  FORGE_EQUIP_STAR_ALL_SELECT = "FORGE_EQUIP_STAR_ALL_SELECT",


  //在线奖励------------
  OPEN_ONLINE_REWARD_DIALOG = "OPEN_ONLINE_REWARD_DIALOG",
  CLOSE_ONLINE_REWARD_DIALOG = "CLOSE_ONLINE_REWARD_DIALOG",
  UPDATE_ONLINE_REWARD_INFO = "UPDATE_ONLINE_REWARD_INFO",
  //在线奖励------------

  //挂机界面UI------------
  OPEN_GUA_JI_DIALOG_M3 = "OPEN_GUA_JI_DIALOG_M3",
  CLOSE_GUA_JI_DIALOG_M3 = "CLOSE_GUA_JI_DIALOG_M3",

  OPEN_GUA_JI_DIALOG_M2 = "OPEN_GUA_JI_DIALOG_M2",
  CLOSE_GUA_JI_DIALOG_M2 = "CLOSE_GUA_JI_DIALOG_M2",

  CLOSE_GUA_JI_NEW_CHAPTER_DIALOG = "CLOSE_GUA_JI_NEW_CHAPTER_DIALOG",
  OPEN_PASS_REWARD_DIALOG = "OPEN_PASS_REWARD_DIALOG",
  CLOSE_PASS_REWARD_DIALOG = "CLOSE_PASS_REWARD_DIALOG",
  OPEN_WORLD_LV_DIALOG = "OPEN_WORLD_LV_DIALOG",
  CLOSE_WORLD_LV_DIALOG = "CLOSE_WORLD_LV_DIALOG",
  OPEN_GUA_JI_QUICK_FIGHT_DIALOG = "OPEN_GUA_JI_QUICK_FIGHT_DIALOG",
  CLOSE_GUA_JI_QUICK_FIGHT_DIALOG = "CLOSE_GUA_JI_QUICK_FIGHT_DIALOG",
  UPDATE_GUA_JI_INFO = "UPDATE_GUA_JI_INFO",
  UPDATE_GUA_JI_HANDING_REWARD_INFO = "UPDATE_GUA_JI_HANDING_REWARD_INFO",
  UPDATE_MAIN_BATTLE_INFO = "UPDATE_MAIN_BATTLE_INFO",
  REQUEST_MAIN_BATTLE_NEXT = "REQUEST_MAIN_BATTLE_NEXT",
  UPDATE_MAIN_BATTLE_MISSION_STATUS = "UPDATE_MAIN_BATTLE_MISSION_STATUS",
  ON_MAIN_BATTLE_FIGHT_SUCC = "ON_MAIN_BATTLE_FIGHT_SUCC",//主线关卡通过成功
  OPEN_GUA_JI_UP_DIALOG = "OPEN_GUA_JI_UP_DIALOG",
  OPEN_GUA_JI_NEW_MAP_DIALOG = "OPEN_GUA_JI_NEW_MAP_DIALOG",
  UPDATE_AUTO_PASS_INFO = "UPDATE_AUTO_PASS_INFO",
  OPEN_AUTO_PASS_DIALOG = "OPEN_AUTO_PASS_DIALOG",
  CLOSE_AUTO_PASS_DIALOG = "CLOSE_AUTO_PASS_DIALOG",
  OPEN_CHAPTER_DIALOG = "OPEN_CHAPTER_DIALOG",
  CLOSE_CHAPTER_DIALOG = "CLOSE_CHAPTER_DIALOG",
  OPEN_MAIN_BATTLE_RECHARGE_DIALOG = "OPEN_MAIN_BATTLE_RECHARGE_DIALOG",
  CLOSE_MAIN_BATTLE_RECHARGE_DIALOG = "CLOSE_MAIN_BATTLE_RECHARGE_DIALOG",
  UPDATE_GUA_JI_ICON = "UPDATE_GUA_JI_ICON",
  UPDATE_FREE_PASS_TIMES = "UPDATE_FREE_PASS_TIMES",
  UPDATE_GUAJI_ICON_TIPS = "UPDATE_GUAJI_ICON_TIPS",
  UPDATE_GUAJI_CHAPTER_INFO = "UPDATE_GUAJI_CHAPTER_INFO",
  CHECK_GUAJI_REWARD_FULL = "CHECK_GUAJI_REWARD_FULL",//检查挂机奖励是否已满
  OPEN_GUAJI_REWARD_FULL_TIP_DIALOG = "OPEN_GUAJI_REWARD_FULL_TIP_DIALOG",//检查挂机奖励是否已满
  OPEN_WORLD_UNLOCK_SMALL_MAP_DIALOG = "OPEN_WORLD_UNLOCK_SMALL_MAP_DIALOG",
  OPEN_GUAJI_DROP_OUT_DIALOG = "OPEN_GUAJI_DROP_OUT_DIALOG",
  OPEN_GUAJI_BOX_DIALOG = "OPEN_GUAJI_BOX_DIALOG",
  CLOSE_GUAJI_BOX_DIALOG = "CLOSE_GUAJI_BOX_DIALOG",
  UPDATE_MAIN_BATTLE_BOX_INFO = "UPDATE_MAIN_BATTLE_BOX_INFO",
  UPDATE_MAIN_BATTLE_BOX_UPGRADE = "UPDATE_MAIN_BATTLE_BOX_UPGRADE",
  FLY_MAIN_BATTLE_BOX = "FLY_MAIN_BATTLE_BOX",
  OPEN_GUAJI_AUTO_END_DIALOG = "OPEN_GUAJI_AUTO_END_DIALOG",
  OPEN_GUAJI_BOX_LEVEL_PREVIEW_DIALOG = "OPEN_GUAJI_BOX_LEVEL_PREVIEW_DIALOG",
  OPEN_GUAJI_BOX_UPGRADE_RESULT_DIALOG = "OPEN_GUAJI_BOX_UPGRADE_RESULT_DIALOG",
  OPEN_GUAJI_BOX_TEQUAN_DIALOG = "OPEN_GUAJI_BOX_TEQUAN_DIALOG",
  CLOSE_GUAJI_BOX_TEQUAN_DIALOG = "CLOSE_GUAJI_BOX_TEQUAN_DIALOG",
  OPEN_GUAJI_NAVIGATION_DIALOG = "OPEN_GUAJI_NAVIGATION_DIALOG",
  CLOSE_GUAJI_NAVIGATION_DIALOG = "CLOSE_GUAJI_NAVIGATION_DIALOG",
  OPEN_GUAJI_SMALL_GAME = "OPEN_GUAJI_SMALL_GAME",
  OPEN_SOLDIER_GO_GAME_DIALOG = "OPEN_SOLDIER_GO_GAME_DIALOG",
  OPEN_SOLDIER_GO_GAME_FIGHT_FAIL_DIALOG = "OPEN_SOLDIER_GO_GAME_FIGHT_FAIL_DIALOG",
  UPDATE_SMALL_GAME_INFO = "UPDATE_SMALL_GAME_INFO",
  UPDATE_MAIN_CITY_BG_DATA = "UPDATE_MAIN_CITY_BG_DATA",

  UPDATE_GUA_JI_SUB_MISSION = "UPDATE_GUA_JI_SUB_MISSION",
  OPEN_RECOMMEND_LINE_UP_DIALOG = "OPEN_RECOMMEND_LINE_UP_DIALOG",
  OPEN_FIGHT_PLAY_BACK_DIALOG = "OPEN_FIGHT_PLAY_BACK_DIALOG",
  BTN_FIGHT_HAND_EFF = "BTN_FIGHT_HAND_EFF",
  QUICK_FIGHT = "QUICK_FIGHT",
  START_AUTO_MAIN_BATTLE = "START_AUTO_MAIN_BATTLE",
  EXIT_GUAJI_MAP = "EXIT_TO_GUAJI_MAP",
  SWITCH_EFFECT = "CLOSE_GUAJI_DIALOG_BY_EFFECT",
  UPDATE_MAIN_BATTLE_AUTO = "UPDATE_MAIN_BATTLE_AUTO",
  CLOSED_GUA_JI = "CLOSED_GUA_JI",
  SHOW_GUAJI_UP = "SHOW_GUAJI_UP",
  HIDE_GUAJI_UP = "HIDE_GUAJI_UP",
  //挂机界面UI------------
  OPEN_GUAJI_BUILD_UP_DIALOG = "OPEN_GUAJI_BUILD_UP_DIALOG",
  GUAJI_BUILD_INFO = "GUAJI_BUILD_INFO",
  GUAJI_BUILD_UPDATE = "GUAJI_BUILD_UPDATE",
  GUAJI_BUILD_WORKER_PATH_END = "GUAJI_BUILD_WORKER_PATH_END",
  GUAJI_BUILD_ON_BACK_MODE = "GUAJI_BUILD_ON_BACK_MODE",
  //等级礼包 start -----------
  OPEN_LEVEL_GIFT_DIALOG = "OPEN_LEVEL_GIFT_DIALOG",
  CLOSE_LEVEL_GIFT_DIALOG = "CLOSE_LEVEL_GIFT_DIALOG",
  UPDATE_LEVEL_GIFT = "UPDATE_LEVEL_GIFT",
  //等级礼包 end -------------

  //BOSS
  UPDATE_SELECT_BOSS = "UPDATE_SELECT_BOSS",
  UPDATE_WELFARE_SELECT_BOSS = "UPDATE_WELFARE_SELECT_BOSS",
  UPDATE_BOSS_INFO = "UPDATE_BOSS_INFO",
  UPDATE_TEAMLIST_INFO = "UPDATE_TEAMLIST_INFO",
  UPDATE_RECENT_TEAMMATE = "UPDATE_RECENT_TEAMMATE",
  UPDATE_TEAM_STATUS = "UPDATE_TEAM_STATUS",
  UPDATE_BOSS_TEAM = "UPDATE_BOSS_TEAM",
  UPDATE_FIRST_PASS_INFO = "UPDATE_FIRST_PASS_INFO",
  CROSS_BOSS_JOIN_TOC = "CROSS_BOSS_JOIN_TOC",
  // UPDATE_TODAY_GAINS_INFO  = "UPDATE_FIRST_PASS_INFO",
  BEFOR_SIGNLE_BOSS_FIGHT = "BEFOR_SIGNLE_BOSS_FIGHT",
  BEFOR_MULTI_BOSS_FIGHT = "BEFOR_MULTI_BOSS_FIGHT",
  TEAM_BOSS_FIGHT_FINISH = "TEAM_BOSS_FIGHT_FINISH",
  //BOSS

  //世界地图-------------
  OPEN_WROLD_MAP_DIALOG = "OPEN_WROLD_MAP_DIALOG",
  OPEN_GUA_JI_WROLD_MAP_DIALOG = "OPEN_GUA_JI_WROLD_MAP_DIALOG",
  CLOSE_GUA_JI_WROLD_MAP_DIALOG = "CLOSE_GUA_JI_WROLD_MAP_DIALOG",
  OPEN_GUA_JI_WROLD_SMALL_MAP_DIALOG = "OPEN_GUA_JI_WROLD_SMALL_MAP_DIALOG",
  CLOSE_GUA_JI_WROLD_SMALL_MAP_DIALOG = "CLOSE_GUA_JI_WROLD_SMALL_MAP_DIALOG",
  CHANGE_GUA_JI_WORLD_MAP_POS = "CHANGE_GUA_JI_WORLD_MAP_POS",
  WORLD_MAP_CITY_UNLOCK_SUCC = "WORLD_MAP_CITY_UNLOCK_SUCC",
  MOVE_TO_WAR_CITY_EVENT = "MOVE_TO_WAR_CITY_EVENT",
  REQUEST_UNLOCK_CITY_BY_FIGHT_SUCC = "REQUEST_UNLOCK_CITY_BY_FIGHT_SUCC",

  OPEN_WAR_WORLD_SCENE_DIALOG = "OPEN_WAR_WORLD_SCENE_DIALOG",
  CLOSE_WAR_WORLD_SCENE_DIALOG = "CLOSE_WAR_WORLD_SCENE_DIALOG",
  WAR_CITY_SPECIALITY_TOC = "WAR_CITY_SPECIALITY_TOC",
  OPEN_KING_YU_SAI_TIPS = "OPEN_KING_YU_SAI_TIPS",

  //世界地图-------------

  //英雄布阵--------------
  OPEN_LINE_UP_DIALOG = "OPEN_LINE_UP_DIALOG",
  CLOSE_LINE_UP_DIALOG = "CLOSE_LINE_UP_DIALOG",

  OPEN_YUAN_JUN_DIALOG = "OPEN_YUAN_JUN_DIALOG",
  CLOSE_YUAN_JUN_DIALOG = "CLOSE_YUAN_JUN_DIALOG",
  OPEN_LINE_UP_HERO_LIST_DIALOG = "OPEN_LINE_UP_HERO_LIST_DIALOG",
  CLOSE_LINE_UP_HERO_LIST_DIALOG = "CLOSE_LINE_UP_HERO_LIST_DIALOG",
  OPEN_LINE_UP_BUFF_DIALOG = "OPEN_LINE_UP_BUFF_DIALOG",
  CLOSE_LINE_UP_BUFF_DIALOG = "CLOSE_LINE_UP_BUFF_DIALOG",
  REQUEST_CHANGE_LINE_UP_POSITION = "CHANGE_LINE_UP_POSITION",
  UPDATE_LINE_UP = "UPDATE_LINE_UP",
  REFRESH_LINE_UP_DIALOG = "REFRESH_LINE_UP_DIALOG",
  //多阵容 TODO TrainingFightDialog
  UPDATE_COMPLETE_LINE_UP = "UPDATE_COMPLETE_LINE_UP",
  OPEN_LINE_UP_TUI_JIAN = "OPEN_LINE_UP_TUI_JIAN",
  ON_SELECT_LINE_UP_ITEM = "ON_SELECT_LINE_UP_ITEM",
  OPEN_LINEUP_HERO_INFO_DIALOG = "OPEN_LINEUP_HERO_INFO_DIALOG",
  OPEN_KARMA_TIP_DIALOG = "OPEN_KARMA_TIP_DIALOG",
  UPDATE_LINE_UP_STYLE = "UPDATE_LINE_UP_STYLE",
  //多阵容 TODO 引导GuideTipIns 相关
  ON_CHANGE_LINE_UP_POS = "ON_CHANGE_LINE_UP_POS",
  ON_CHANGE_LINE_UP_ITEM = "ON_CHANGE_LINE_UP_ITEM",
  ON_CHANGE_LINE_UP_TYPE = "ON_CHANGE_LINE_UP_TYPE",
  HERO_DOWN_LINEUP = "HERO_DOWN_LINEUP",
  SELECT_HERO_DOWN_LINEUP = "SELECT_HERO_DOWN_LINEUP",

  OPEN_LINE_UP_STYLE_SELECT_DIALOG = "OPEN_LINE_UP_STYLE_SELECT_DIALOG",
  OPEN_LINE_UP_GOD_WEAPON_SELECT_DIALOG = "OPEN_LINE_UP_GOD_WEAPON_SELECT_DIALOG",
  OPEN_LINE_UP_LEGGENDS_RESET_DIALOG = "OPEN_LINE_UP_LEGGENDS_RESET_DIALOG",
  OPEN_LINE_UP_RESET_PREVIEW_DIALOG = "OPEN_LINE_UP_RESET_PREVIEW_DIALOG",
  LINE_UP_SELECT_GOD_WEAPON = "LINE_UP_SELECT_GOD_WEAPON",
  UPDATE_GOD_WEAPON_UP_STAR = "UPDATE_GOD_WEAPON_UP_STAR",
  UPDATE_LINE_UP_POWER = "UPDATE_LINE_UP_POWER",
  UPDATE_LINE_UP_INDEX = "UPDATE_LINE_UP_INDEX",
  //英雄布阵--------------

  //无双试炼--------------
  OPEN_TRIAL_ALERT_DIALOG = "OPEN_TRIAL_ALERT_DIALOG",
  CLOSE_TRIAL_ALERT_DIALOG = "CLOSE_TRIAL_ALERT_DIALOG",
  OPEN_TRIAL_SHOW_REWARD_DIALOG = "OPEN_TRIAL_SHOW_REWARD_DIALOG",
  OPEN_TRIAL_DIALOG = "OPEN_TRIAL_DIALOG",
  CLOSE_TRIAL_DIALOG = "CLOSE_TRIAL_DIALOG",
  OPEN_TRIAL_DIFFICULT_DIALOG = "OPEN_TRIAL_DIFFICULT_DIALOG",
  CLOSE_TRIAL_DIFFICULT_DIALOG = "CLOSE_TRIAL_DIFFICULT_DIALOG",
  OPEN_TRIAL_BUFF_DIALOG = "OPEN_TRIAL_BUFF_DIALOG",
  CLOSE_TRIAL_BUFF_DIALOG = "CLOSE_TRIAL_BUFF_DIALOG",
  OPEN_TRIAL_GET_REWARD_DIALOG = "OPEN_TRIAL_GET_REWARD_DIALOG",
  CLOSE_TRIAL_GET_REWARD_DIALOG = "CLOSE_TRIAL_GET_REWARD_DIALOG",
  OPEN_TRIAL_SUCCESS_DIALOG = "OPEN_TRIAL_SUCCESS_DIALOG",
  CLOSE_TRIAL_SUCCESS_DIALOG = "CLOSE_TRIAL_SUCCESS_DIALOG",
  OPEN_TRIAL_FAIL_DIALOG = "OPEN_TRIAL_FAIL_DIALOG",
  CLOSE_TRIAL_FAIL_DIALOG = "CLOSE_TRIAL_FAIL_DIALOG",
  OPEN_TRIAL_SWEEP_DIALOG = "OPEN_TRIAL_SWEEP_DIALOG",
  CLOSE_TRIAL_SWEEP_DIALOG = "CLOSE_TRIAL_SWEEP_DIALOG",
  UPDATE_TRIAL_INFO = "UPDATE_TRIAL_INFO",
  UPDATE_TRIAL_INFO_DIALOG = "UPDATE_TRIAL_INFO_DIALOG",
  OPEN_TRIAL_RESULT_DIALOG = "OPEN_TRIAL_RESULT_DIALOG",
  OPEN_TRIAL_SKILL_DIALOG = "OPEN_TRIAL_SKILL_DIALOG",
  UPDATE_TRIAL_BOSS_SHOW = "UPDATE_TRIAL_BOSS_SHOW",
  REQUEST_START_FIGHT_PASS = "REQUEST_START_FIGHT_PASS",
  //无双试炼--------------


  //氪金商店---------------
  OPEN_HERO_COME_DIALOG = "OPEN_HERO_COME_DIALOG",
  UPDATE_HERO_COME_INFO = "UPDATE_TEQUAN_INFO",
  OPEN_PAYMENT_DIALOG = "OPEN_PAYMENT_DIALOG",
  CLOSE_PAYMENT_DIALOG = "CLOSE_PAYMENT_DIALOG",
  OPEN_PAYMENT_MUST_BUY_DIALOG = "OPEN_PAYMENT_MUST_BUY_DIALOG",
  UPDATE_TEQUAN_INFO = "UPDATE_TEQUAN_INFO",
  UPDATE_PAYMENT_SHOP_INFO = "UPDATE_PAYMENT_SHOP_INFO",
  CHECK_PAYMENT_SHOP_RED_POINT = "CHECK_PAYMENT_SHOP_RED_POINT",
  OPEN_SPECIAL_FUNDS_DIALOG = "OPEN_SPECIAL_FUNDS_DIALOG",
  CLOSE_SPECIAL_FUNDS_DIALOG = "CLOSE_SPECIAL_FUNDS_DIALOG",
  UPDATE_SPECIAL_FUNDS_DIALOG = "UPDATE_SPECIAL_FUNDS_DIALOG",
  OPEN_SPECIAL_FUNDS_REWARD_DIALOG = "OPEN_SPECIAL_FUNDS_REWARD_DIALOG",
  UPDATE_VIP_INFO = "UPDATE_VIP_INFO",
  UPDATE_VIP_ITEM_INFO = "UPDATE_VIP_ITEM_INFO",
  UPDATE_FREE_VIP_GIFTS_INFO = "UPDATE_FREE_VIP_GIFTS_INFO",
  UPDATE_VIP_SELECT = "UPDATE_VIP_SELECT",
  OPEN_NEW_SERVER_DIALOG = "OPEN_NEW_SERVER_DIALOG",
  OPEN_PAYMENT_TIPS_DIALOG = "OPEN_PAYMENT_TIPS_DIALOG",
  CLOSE_PAYMENT_TIPS_DIALOG = "CLOSE_PAYMENT_TIPS_DIALOG",
  UPDATE_DAILY_AD_CODE = "UPDATE_DAILY_AD_CODE",
  OPEN_GIVE_VIP_DIALOG = "OPEN_GIVE_VIP_DIALOG",
  //新每日特惠
  OPEN_NEW_DAILY_DISCOUNT_DIALOG = "OPEN_NEW_DAILY_DISCOUNT_DIALOG",
  OPEN_NEW_DAILY_DISCOUNT_REBATE_DIALOG = "OPEN_NEW_DAILY_DISCOUNT_REBATE_DIALOG",
  UPDATE_NEW_DAILY_DISCOUNT_REBATE_INFO = "UPDATE_NEW_DAILY_DISCOUNT_REBATE_INFO",
  OPEN_NEW_DAILY_REPLENISH_BUY_GIFT_DIALOG = "OPEN_NEW_DAILY_REPLENISH_BUY_GIFT_DIALOG",
  // 将军府
  OPEN_STAR_RETURN_HEIP_DIALOG = "OPEN_STAR_RETURN_HEIP_DIALOG",

  //根据childId打开PaymentDialog指定tab
  OPEN_PAYMENT_DIALOG_TAB_BY_CHILD_ID = "OPEN_PAYMENT_DIALOG_TAB_BY_CHILD_ID",

  //超值基金奖励预览
  OPEN_SPECIALFUNDS_RECHARGE_DIALOG = "OPEN_SPECIALFUNDS_RECHARGE_DIALOG",
  //超值基金月底刷新再买
  OPEN_SPECIALFUNDS_BUY_AGAIN_DIALOG = "OPEN_SPECIALFUNDS_BUY_AGAIN_DIALOG",

  //vip直购选择跳转
  OPEN_PAYMENT_SHOP_LINK_DIALOG = "OPEN_PAYMENT_SHOP_LINK_DIALOG",
  CLOSE_PAYMENT_SHOP_LINK_DIALOG = "CLOSE_PAYMENT_SHOP_LINK_DIALOG",

  CLOSE_LOTTERY_LINK_DIALOG = "CLOSE_LOTTERY_LINK_DIALOG",
  CLOSE_LOTTERY_LINKS_DIALOG = "CLOSE_LOTTERY_LINKS_DIALOG",

  OPEN_VIP_SHOP_LINK_DIALOG = "OPEN_VIP_SHOP_LINK_DIALOG",
  CLOSE_VIP_SHOP_LINK_DIALOG = "CLOSE_VIP_SHOP_LINK_DIALOG",
  //氪金商店---------------


  //充值福利---------------
  OPEN_WELFARE_DIALOG = "OPEN_WELFARE_DIALOG",
  OPEN_WELFARE_REWARD_DIALOG = "OPEN_WELFARE_REWARD_DIALOG",
  UPDATE_WELFARE_FUND_INFO = "UPDATE_WELFARE_FUND_INFO",
  UPDATE_WELFARE_SIGN_INFO = "UPDATE_WELFARE_SIGN_INFO",
  UPDATE_WELFARE_CARD_INFO = "UPDATE_WELFARE_CARD_INFO",
  OPEN_WAR_MAKES_BUY_DIALOG = "OPEN_WAR_MAKES_BUY_DIALOG",
  CLOSE_WAR_MAKES_BUY_DIALOG = "CLOSE_WAR_MAKES_BUY_DIALOG",
  OPEN_WELFARE_GIFT_BUY_TIP_DIALOG = "OPEN_WELFARE_GIFT_BUY_TIP_DIALOG",
  CLOSE_WELFARE_GIFT_BUY_TIP_DIALOG = "CLOSE_WELFARE_GIFT_BUY_TIP_DIALOG",
  UPDATE_WELFARE_WAR_MAKES_INFO = "UPDATE_WELFARE_WAR_MAKES_INFO",
  UPDATE_WELFARE_WAR_MAKES_RED_POINT = "UPDATE_WELFARE_WAR_MAKES_RED_POINT",
  UPDATE_LIMIT_HERO_SIGN_INFO = "UPDATE_LIMIT_HERO_SIGN_INFO",
  OPEN_WELFARE_LIMIT_HERO_DIALOG = "OPEN_WELFARE_LIMIT_HERO_DIALOG",
  OPEN_WELFARE_LIMIT_HERO_REWARD_DIALOG = "OPEN_WELFARE_LIMIT_HERO_REWARD_DIALOG",
  WELFARE_LIMIT_HERO_UNLOCK_TIPS_DIALOG = "WELFARE_LIMIT_HERO_UNLOCK_TIPS_DIALOG",
  UPDATE_WELFARE_GROW_GIFT_INFO = "UPDATE_WELFARE_GROW_GIFT_INFO",
  //充值福利---------------

  //特惠商城--------------
  OPEN_TE_HUI_SHOP_DIALOG = "OPEN_TE_HUI_SHOP_DIALOG",
  CLOSE_TE_HUI_SHOP_DIALOG = "CLOSE_TE_HUI_SHOP_DIALOG",
  //特惠商城--------------

  //红点相关
  RED_CHILD_CHANGE = "RED_CHILD_CHANGE",
  RED_MENU_CHANGE = "RED_MENU_CHANGE",

  //新开放系统----------------------
  NEW_MENU_CHANGE = "NEW_MENU_CHANGE",
  NEW_CHILD_CHANGE = "NEW_CHILD_CHANGE",
  //新开放系统----------------------

  //擂台竞技场相关
  OPEN_ARENA_ROLE_INFO_DIALOG = "OPEN_ARENA_ROLE_INFO_DIALOG",
  OPEN_ARENA_RANK_PREVIEW_DIALOG = "OPEN_ARENA_RANK_PREVIEW_DIALOG",
  OPEN_ARENA_SHOW_DIALOG = "OPEN_ARENA_SHOW_DIALOG",
  CLOSE_ARENA_SHOW_DIALOG = "CLOSE_ARENA_SHOW_DIALOG",
  OPEN_ARENA_TYPE_DIALOG = "OPEN_ARENA_TYPE_DIALOG",
  OPEN_ARENA_LOG_DIALOG = "OPEN_ARENA_LOG_DIALOG",
  OPEN_ARENA_AWARD_DIALOG = "OPEN_ARENA_AWARD_DIALOG",
  OPEN_ARENA_DIALOG = "OPEN_ARENA_DIALOG",
  OPEN_ARENA_SWEEP_TIP_DIALOG = "OPEN_ARENA_SWEEP_TIP_DIALOG",
  OPEN_ARENA_HINT_UP_DIALOG = "OPEN_ARENA_HINT_UP_DIALOG",
  REVIEW_ARENA_FIGHT = "REVIEW_ARENA_FIGHT",
  OPEN_ARENA_RANK_DOWN_DIALOG = "OPEN_ARENA_RANK_DOWN_DIALOG",
  OPEN_ARENA_LINEUP_DIALOG = "OPEN_ARENA_LINEUP_DIALOG",
  OPEN_CITY_ARENA_BUILD = "OPEN_CITY_ARENA_BUILD",
  OPEN_ARENA_ALERT_DIALOG = "OPEN_ARENA_ALERT_DIALOG",
  OPEN_ARENA_INS_DIALOG = "OPEN_ARENA_INS_DIALOG",
  UPADTE_ARENA_INS_DIALOG = "UPADTE_ARENA_INS_DIALOG",
  OPEN_ARENA_REWARD_DIALOG = "OPEN_ARENA_REWARD_DIALOG",
  UPDATE_ARENA_MAX_REWARD_TAB = "UPDATE_ARENA_MAX_REWARD_TAB",
  UPDATE_ARENA_MAX_REWARD_DIALOG = "UPDATE_ARENA_MAX_REWARD_DIALOG",
  EVENT_GET_ITEM_YUANG_WU_LING = "EVENT_GET_ITEM_YUANG_WU_LING",

  ARENA_ROLE_LIST = "ARENA_ROLE_LIST",
  ARENA_INFO = "ARENA_INFO",
  ARENA_LOGS = "ARENA_LOGS",
  FIGHT_CONTEST_DATA = "FIGHT_CONTEST_DATA",
  FIGHT_MAZE_DATA = "FIGHT_MAZE_DATA",
  FIGHT_ST_MAZE_DATA = "FIGHT_ST_MAZE_DATA",
  FIGHT_RADNOM_BOSS_DATA = "FIGHT_RADNOM_BOSS_DATA",
  FIGHT_WORLD_BOSS_DATA = "FIGHT_WORLD_BOSS_DATA",

  /**指引相关  对话指引*/
  OPEN_GUIDE_GIRL_DIALOG = "OPEM_GUIDE_GIRL_DIALOG",
  SHOW_MAINUI_GUIDE = "SHOW_MAINUI_GUIDE",
  GUIDE_CHECK = "GUIDE_CHECK",
  CHANGE_GUIDE_UI_PANEL = "CHANGE_GUIDE_UI_PANEL",
  GUIDE_FLY_MODEL = "fly_model",
  GUIDE_OPEN_DIALOG = "GUIDE_OPEN_DIALOG",
  GUIDE_DIALOGUE = "GUIDE_DIALOGUE",
  GUIDE_DIALOGUE_END = "GUIDE_DIALOGUE_END",
  GUIDE_MAIN_TAB = "GUIDE_MAIN_TAB",
  GUIDE_SKIP = "GUIDE_SKIP",
  GUIDE_SEND_STEP = "GUIDE_SEND_STEP",
  GUIDE_SHOW_HAND = "GUIDE_SHOW_HAND",
  GUIDE_HIDE_HAND = "GUIDE_HIDE_HAND",
  GUIDE_MAIN_PAGE = "GUIDE_MAIN_PAGE",
  GUIDE_HIDE_GIRL = "GUIDE_HIDE_GIRL",
  GUIDE_SHIP_CHANGE = "GUIDE_SHIP_CHANGE",
  CLOSE_GUIDE_GIRL_DIALOG = "CLOSE_GUIDE_GIRL_DIALOG",
  GUIDE_END_ID = "GUIDE_END_ID",
  PLOT_GAME_START_DIALOG = "PLOT_GAME_START_DIALOG",
  GUIDE_CLICK_STAGE = "GUIDE_CLICK_STAGE",
  OPEN_GUIDE_FIGTH_DIALOG = "OPEN_GUIDE_FIGTH_DIALOG",
  ADD_DIALOG_MANAGER = "ADD_DIALOG_MANAGER",
  FIRST_OPEN_GUIDE = "FIRST_OPEN_GUIDE",
  OPEN_GUIDE_HERO_INFO_DIALOG = "OPEN_GUIDE_HERO_INFO_DIALOG",
  UPDATE_NEW_VIP_HAND = "UPDATE_NEW_VIP_HAND",
  HIDE_GUIDE_UI = "HIDE_GUIDE_UI",
  SHOW_GUOZHAN = "SHOW_GUOZHAN",


  //活动界面相关
  /**8天登陆 */
  OPEN_EIGHT_DAYS_DIALOG = "OPEN_EIGHT_DAYS_DIALOG",
  UPDATE_EIGHT_LOGIN_FETCH = "UPDATE_EIGHT_LOGIN_FETCH",
  SELECT_EIGHT_LOGIN_ITEM = "SELECT_EIGHT_LOGIN_ITEM",
  EIGHT_LOGIN_INFO = "EIGHT_LOGIN_INFO",

  OPEN_EIGHT_DAYS_DIALOG2 = "OPEN_EIGHT_DAYS_DIALOG2",
  UPDATE_EIGHT_LOGIN_FETCH2 = "UPDATE_EIGHT_LOGIN_FETCH2",
  SELECT_EIGHT_LOGIN_ITEM2 = "SELECT_EIGHT_LOGIN_ITEM2",
  EIGHT_LOGIN_INFO2 = "EIGHT_LOGIN_INFO2",
  /**8天登陆模块化 */
  OPEN_ACTMODS_DAYS_LOGIN_DIALOG = "OPEN_ACTMODS_EIGHT_DAYS_DIALOG2",
  UPDATE_ACTMODS_DAYS_LOGIN_FETCH = "UPDATE_ACTMODS_DAYS_LOGIN_FETCH",

  //七天目标---------------------
  OPEN_SEVEN_GOAL_DIALOG = "OPEN_SEVEN_GOAL_DIALOG",
  UPDATE_SEVEVN_GOAL_DAY_RED = "UPDATE_SEVEVN_GOAL_DAY_RED",
  CLOSE_SEVEN_GOAL_DIALOG = "CLOSE_SEVEN_GOAL_DIALOG",
  UPDATE_SEVEN_GOAL_INFO = "UPDATE_SEVEN_GOAL_INFO",
  CLICK_SEVEN_GOAL_DAY_ITEM = "CLICK_SEVEN_GOAL_DAY_ITEM",
  UPDATE_SEVEN_GOAL_INFO_STATE = "UPDATE_SEVEN_GOAL_INFO_STATE",
  UPDATE_SEVEN_GOAL_ITEM_STATE = "UPDATE_SEVEN_GOAL_ITEM_STATE",
  //七天目标---------------------
  //七天目标---------------------
  ON_LINK_ACTMODS_DAYS_GOAL = "ON_LINK_ACTMODS_DAYS_GOAL",
  OPEN_ACTMODS_DAYS_GOAL_DIALOG = "OPEN_ACTMODS_DAYS_GOAL_DIALOG",
  UPDATE_ACTMODS_DAYS_GOAL_MISSION_RED = "UPDATE_ACTMODS_DAYS_GOAL_DAY_RED",
  CLOSE_ACTMODS_DAYS_GOAL_DIALOG = "CLOSE_ACTMODS_DAYS_GOAL_DIALOG",
  UPDATE_ACTMODS_DAYS_GOAL_INFO = "UPDATE_ACTMODS_DAYS_GOAL_INFO",
  CLICK_ACTMODS_DAYS_GOAL_DAY_ITEM = "CLICK_ACTMODS_DAYS_GOAL_DAY_ITEM",
  UPDATE_ACTMODS_DAYS_GOAL_INFO_STATE = "UPDATE_ACTMODS_DAYS_GOAL_INFO_STATE",
  UPDATE_ACTMODS_DAYS_GOAL_ITEM_STATE = "UPDATE_ACTMODS_DAYS_GOAL_ITEM_STATE",
  OPEN_ACTMODS_DAYS_GOAL_GIFT_TIPS_DIALOG = "OPEN_ACTMODS_DAYS_GOAL_GIFT_TIPS_DIALOG",
  OPEN_ACTMODS_DAYS_GOAL_RECHARGE_DIALOG = "OPEN_ACTMODS_DAYS_GOAL_RECHARGE_DIALOG",
  OPEN_ACTMODS_DAYS_GOAL_GIFT_PREVIEW_DIALOG = "OPEN_ACTMODS_DAYS_GOAL_GIFT_PREVIEW_DIALOG",
  //七天目标---------------------

  OPEN_HERO_STAR_GIFT_DIALOG = "OPEN_HERO_STAR_GIFT_DIALOG",
  CLOSE_HERO_STAR_GIFT_DIALOG = "CLOSE_HERO_STAR_GIFT_DIALOG",
  OPEN_BAI_JIANG_GIFT_DIALOG = "OPEN_BAI_JIANG_GIFT_DIALOG",
  CLOSE_BAI_JIANG_GIFT_DIALOG = "CLOSE_BAI_JIANG_GIFT_DIALOG",
  UPDATE_HERO_STAR_GIFT_INFO = "UPDATE_HERO_STAR_GIFT_INFO",
  UPDATE_BAI_JIANG_GIFT_INFO = "UPDATE_BAI_JIANG_GIFT_INFO",

  //阵图---------------------
  OPEN_ZHEN_TU_DIALOG = "OPEN_ZHEN_TU_DIALOG",
  UPDATE_ZHEN_TU_UPDATE_SELECT = "UPDATE_ZHEN_TU_UPDATE_SELECT",
  CLOSE_ZHEN_TU_DIALOG = "CLOSE_ZHEN_TU_DIALOG",
  UPDATE_ZHEN_TU_INFO = "UPDATE_ZHEN_TU_INFO",
  OPEN_ZHEN_TU_UPDATE_DIALOG = "OPEN_ZHEN_TU_UPDATE_DIALOG",
  CLOSE_ZHEN_TU_UPDATE_DIALOG = "CLOSE_ZHEN_TU_UPDATE_DIALOG",
  OPEN_ZHEN_TU_ATTR_TIPS_SINGLE_DIALOG = "OPEN_ZHEN_TU_ATTR_TIPS_SINGLE_DIALOG",
  OPEN_ZHEN_TU_SKILL_SHOW_DIALOG = "OPEN_ZHEN_TU_SKILL_SHOW_DIALOG",
  OPEN_ZHEN_TU_MISSION_ATTR_DIALOG = "OPEN_ZHEN_TU_MISSION_ATTR_DIALOG",
  OPEN_ZHEN_TU_SKILL_UPDATE_DIALOG = "OPEN_ZHEN_TU_SKILL_UPDATE_DIALOG",
  CLOSE_ZHEN_TU_SKILL_UPDATE_DIALOG = "CLOSE_ZHEN_TU_SKILL_UPDATE_DIALOG",
  OPEN_ZHEN_TU_PATH_DIALOG = "OPEN_ZHEN_TU_PATH_DIALOG",
  OPEN_ZHEN_TU_COMPOSE_DIALOG = "OPEN_ZHEN_TU_COMPOSE_DIALOG",
  OPEN_ZHEN_TU_SKILL_UPDATE_SUCCESS_DIALOG = "OPEN_ZHEN_TU_SKILL_UPDATE_SUCCESS_DIALOG",
  CLOSE_ZHEN_TU_SKILL_UPDATE_SUCCESS_DIALOG = "CLOSE_ZHEN_TU_SKILL_UPDATE_SUCCESS_DIALOG",
  //阵图---------------------


  //寻宝------------------
  OPEN_TREASURE_DIALOG = "OPEN_TREASURE_DIALOG",
  TREASURE_INFO = "TREASURE_INFO",
  TREASURE_REFRESH = "TREASURE_REFRESH",
  TREASURE_HUNT_RUN = "TREASURE_HUNT_RUN",
  TREASURE_LOG = "TREASURE_LOG",
  TREASURE_UPDATE_GIFT_SCORES = "TREASURE_UPDATE_GIFT_SCORES",
  TREASURE_DESC_DIALOG = "TREASURE_DESC_DIALOG",
  TREASURE_EFFECT_STOP = "TREASURE_EFFECT_STOP",
  TREASURE_EFFECT_CONTINUE = "TREASURE_EFFECT_CONTINUE",
  REQ_HUNT_RUN_TOS = "REQ_HANG_RUN_TOS",
  //寻宝---------------------end

  //活动相关--------------
  OPEN_TIME_ACT_DIALOG = "OPEN_TIME_ACT_DIALOG",
  CLOSE_TIME_ACT_DIALOG = "CLOSE_TIME_ACT_DIALOG",
  OPEN_THEME_TIME_ACT_DIALOG = "OPEN_THEME_TIME_ACT_DIALOG",
  CLOSE_THEME_TIME_ACT_DIALOG = "CLOSE_THEME_TIME_ACT_DIALOG",
  UPDATE_TIME_ACT_INFO = "UPDATE_TIME_ACT_INFO",
  OPEN_TIME_ACT_RANK_REWARD_DIALOG = "OPEN_TIME_ACT_RANK_REWARD_DIALOG",
  ACTIVITY_INFO_TOC = "ACTIVITY_INFO_TOC",
  ACTIVITY_LIST_TOC = "ACTIVITY_LIST_TOC",

  // UPDATE_ACT_INFO  = "UPDATE_ACT_INFO",
  //活动相关---------------------
  //节日活动--------------------------
  OPEN_FESTIVAL_ACT_DIALOG = "OPEN_FESTIVAL_ACT_DIALOG",
  CLOSE_FESTIVAL_ACT_DIALOG = "CLOSE_FESTIVAL_ACT_DIALOG",

  //元旦
  OPEN_FESTIVAL_ACT_YUANDAN_DIALOG = "OPEN_FESTIVAL_ACT_YUANDAN_DIALOG",
  CLOSE_FESTIVAL_ACT_YUANDAN_DIALOG = "CLOSE_FESTIVAL_ACT_YUANDAN_DIALOG",
  //圣诞
  OPEN_FESTIVAL_ACT_SHENGDAN_DIALOG = "OPEN_FESTIVAL_ACT_SHENGDAN_DIALOG",
  CLOSE_FESTIVAL_ACT_SHENGDAN_DIALOG = "CLOSE_FESTIVAL_ACT_SHENGDAN_DIALOG",


  UPDATE_FESTIVAL_MISSION = "UPDATE_FESTIVAL_MISSION",

  //舌战群儒
  OPEN_EXAM_ACT_DIALOG = "OPEN_ANSER_ACT_DIALOG",
  OPEN_EXAM_ACT_REWARDS_DIALOG = "OPEN_EXAM_ACT_REWARDS_DIALOG",
  OPEN_EXAM_ACT_ANSWER_DIALOG = "OPEN_EXAM_ACT_ANSWER_DIALOG",
  ACTIVITY_EXAM_INFO_TOC = "ACTIVITY_EXAM_INFO_TOC",
  ACTIVITY_EXAM_ANSWER_TOC = "ACTIVITY_EXAM_ANSWER_TOC",
  ACTIVITY_EXAM_FINISH = "ACTIVITY_EXAM_FINISH",

  //节日活动--------------------------
  //活动
  OPEN_ACT_MODS_WEEK_WELFARE_DIALOG = "OPEN_ACT_MODS_WEEK_WELFARE_DIALOG",
  UPDATE_ACT_MODS_WEEK_WELFARE_DIALOG = "UPDATE_ACT_MODS_WEEK_WELFARE_DIALOG",
  //主题玩法 模块化 start -------------------
  ON_LINK_ACTMODS_MAZE = "ON_LINK_ACTMODS_MAZE",
  OPEN_MAZE_DIALOG = "OPEN_MAZE_DIALOG",
  CLOSE_MAZE_DIALOG = "CLOSE_MAZE_DIALOG",
  OPEN_MAZE_CURE_DIALOG = "OPEN_MAZE_CURE_DIALOG",
  OPEN_MAZE_PUB_DIALOG = "OPEN_MAZE_PUB_DIALOG",
  OPEN_MAZE_BOSS_INFO_DIALOG = "OPEN_MAZE_BOSS_INFO_DIALOG",
  OPEN_MAZE_MERCHANT_DIALOG = "OPEN_MAZE_MERCHANT_DIALOG",
  OPEN_MAZE_HERO_DIALOG = "OPEN_MAZE_HERO_DIALOG",
  CLOSE_MAZE_HERO_DIALOG = "CLOSE_MAZE_HERO_DIALOG",
  OPEN_MAZE_SPOILS_DIALOG = "OPEN_SPOILS_DIALOG",
  OPEN_MAZE_CHOOSE_SPOILS_DIALOG = "OPEN_CHOOSE_SPOILS_DIALOG",
  OPEN_MAZE_PASS_DIALOG = "OPEN_MAZE_PASS_DIALOG",
  OPEN_MAZE_CHOOSE_DIFFUCULT_DIALOG = "OPEN_MAZE_CHOOSE_DIFFUCULT_DIALOG",
  OPEN_MAZE_CRUSH_FIGHT_DIALOG = "OPEN_MAZE_LLANDSLIDE_VICTORY_DIALOG",
  OPEN_MAZE_SWEEP_DIALOG = "OPEN_MAZE_SWEEP_DIALOG",
  CLOSE_MAZE_SWEEP_DIALOG = "CLOSE_MAZE_SWEEP_DIALOG",
  OPEN_MAZE_REWARD_DIALOG = "OPEN_MAZE_REWARD_DIALOG",
  OPEN_MAZE_REVIVE_DIALOG = "OPEN_MAZE_REVIVE_DIALOG",
  OPEN_MAZE_REPLAY_DIALOG = "OPEN_MAZE_REPLAY_DIALOG",
  OPEN_MAZE_AUTO_END_DIALOG = "OPEN_MAZE_AUTO_END_DIALOG",

  OPEN_ACT_MODS_LOTTERY_TARGET_DIALOG = "OPEN_ACT_MODS_LOTTERY_TARGET_DIALOG",
  UPDATE_MAZE_USE_POTION_INFO = "UPDATE_MAZE_USE_POTION_INFO",
  UPDATE_LOTTERY_TARGET_INFO = "UPDATE_LOTTERY_TARGET_INFO",
  UPDATE_MAZE_HERO_INFO = "UPDATE_MAZE_HERO_INFO",
  UPDATE_MAZE_INFO = "UPDATE_MAZE_INFO",
  UPDATE_MAZE_FIGHT_INFO = "UPDATE_MAZE_FIGHT_INFO",
  UPDATE_MAZE_REPORT_INFO = "UPDATE_MAZE_REPORT_INFO",
  UPDATE_MAZE_AUTO_START_INFO = "UPDATE_MAZE_AUTO_START_INFO",
  UPDATE_MAZE_AUTO_END_INFO = "UPDATE_MAZE_AUTO_END_INFO",
  MAZE_START_TOC = "MAZE_START_TOC",
  MAZE_PUB_TOC = "MAZE_PUB_TOC",
  MAZE_SAVE_FIGHT = "MAZE_SAVE_FIGHT",
  SELECT_MAZE_PUB_HERO = "SELECT_MAZE_PUB_HERO",
  SELECT_MAZE_SPOILS_ITEM = "SELECT_MAZE_SPOILS_ITEM",
  SELECT_MAZE_SPOILS_TOC = "SELECT_MAZE_SPOILS",
  MAZE_ITEM_SHOW_FIGHT_EFF = "MAZE_ITEM_SHOW_FIGHT_EFF",
  OPEN_ACT_MODS_LOTTERY_RARE_DIALOG = "OPEN_ACT_MODS_LOTTERY_RARE_DIALOG",
  UPDATE_ACT_MODS_LOTTERY_RARE_DIALOG = "UPDATE_ACT_MODS_LOTTERY_RARE_DIALOG",
  //主题玩法 模块化 end -------------------

  //审核剧情-平定中原 模块化 start -------------------
  ON_ST_MAZE_LINK_ACTMODS_MAZE = "ON_ST_MAZE_LINK_ACTMODS_MAZE",
  OPEN_ST_MAZE_DIALOG = "OPEN_ST_MAZE_DIALOG",
  OPEN_ST_MAZE_CURE_DIALOG = "OPEN_ST_MAZE_CURE_DIALOG",
  OPEN_ST_MAZE_PUB_DIALOG = "OPEN_ST_MAZE_PUB_DIALOG",
  OPEN_ST_MAZE_BOSS_INFO_DIALOG = "OPEN_ST_MAZE_BOSS_INFO_DIALOG",
  OPEN_ST_MAZE_MERCHANT_DIALOG = "OPEN_ST_MAZE_MERCHANT_DIALOG",
  OPEN_ST_MAZE_HERO_DIALOG = "OPEN_ST_MAZE_HERO_DIALOG",
  CLOSE_ST_MAZE_HERO_DIALOG = "CLOSE_ST_MAZE_HERO_DIALOG",
  OPEN_ST_MAZE_SPOILS_DIALOG = "OPEN_ST_MAZE_SPOILS_DIALOG",
  OPEN_ST_MAZE_CHOOSE_SPOILS_DIALOG = "OPEN_ST_MAZE_CHOOSE_SPOILS_DIALOG",
  OPEN_ST_MAZE_PASS_DIALOG = "OPEN_ST_MAZE_PASS_DIALOG",
  OPEN_ST_MAZE_CHOOSE_DIFFUCULT_DIALOG = "OPEN_ST_MAZE_CHOOSE_DIFFUCULT_DIALOG",
  OPEN_ST_MAZE_CRUSH_FIGHT_DIALOG = "OPEN_ST_MAZE_LLANDSLIDE_VICTORY_DIALOG",
  OPEN_ST_MAZE_REWARD_DIALOG = "OPEN_ST_MAZE_REWARD_DIALOG",
  OPEN_ST_MAZE_REPLAY_DIALOG = "OPEN_ST_MAZE_REPLAY_DIALOG",
  OPEN_ST_MAZE_AUTO_END_DIALOG = "OPEN_ST_MAZE_AUTO_END_DIALOG",

  OPEN_ST_MAZE_WORLD_MAP_DIALOG = "OPEN_ST_MAZE_WORLD_MAP_DIALOG",
  CLOSE_ST_MAZE_WORLD_MAP_DIALOG = "CLOSE_ST_MAZE_WORLD_MAP_DIALOG",

  UPDATE_ST_MAZE_USE_POTION_INFO = "UPDATE_ST_MAZE_USE_POTION_INFO",
  UPDATE_ST_MAZE_HERO_INFO = "UPDATE_ST_MAZE_HERO_INFO",
  UPDATE_ST_MAZE_INFO = "UPDATE_ST_MAZE_INFO",
  UPDATE_ST_MAZE_FIGHT_INFO = "UPDATE_ST_MAZE_FIGHT_INFO",
  UPDATE_ST_MAZE_REPORT_INFO = "UPDATE_ST_MAZE_REPORT_INFO",
  UPDATE_ST_MAZE_AUTO_START_INFO = "UPDATE_ST_MAZE_AUTO_START_INFO",
  UPDATE_ST_MAZE_AUTO_END_INFO = "UPDATE_ST_MAZE_AUTO_END_INFO",
  ST_MAZE_START_TOC = "ST_MAZE_START_TOC",
  ST_MAZE_PUB_TOC = "ST_MAZE_PUB_TOC",
  SELECT_ST_MAZE_PUB_HERO = "SELECT_ST_MAZE_PUB_HERO",
  SELECT_ST_MAZE_SPOILS_ITEM = "SELECT_ST_MAZE_SPOILS_ITEM",
  SELECT_ST_MAZE_SPOILS_TOC = "SELECT_ST_MAZE_SPOILS",
  ST_MAZE_ITEM_SHOW_FIGHT_EFF = "ST_MAZE_ITEM_SHOW_FIGHT_EFF",

  //审核剧情-平定中原 模块化 end -------------------

  //审核剧情 模块 start-----------------------
  OPEN_STORY_GAME_PLOT_DIALOG = "OPEN_STORY_GAME_PLOT_DIALOG",
  ON_STORY_GAME_PLOT_FINISH = "ON_STORY_GAME_PLOT_FINISH",

  //审核剧情 模块 end-----------------------


  //公告---------------------------------------------
  OPEN_NOTICE_DIALOG = "OPEN_NOTICE_DIALOG",
  CLOSE_NOTICE_DIALOG = "CLOSE_NOTICE_DIALOG",
  OPEN_GAME_USE_DIALOG = "OPEN_GAME_USE_DIALOG",
  GAME_USE_STATE = "GAME_USE_STATE",
  OPEN_CUSTOM_MSG_NOTICE_DIALOG = "OPEN_CUSTOM_MSG_NOTICE_DIALOG",
  //公告---------------------------------------------

  //失败商店---------------------------------------------
  OPEN_FAILED_SHOP_DIALOG = "OPEN_FAILED_SHOP_DIALOG",
  CLOSE_FAILED_SHOP_DIALOG = "CLOSE_FAILED_SHOP_DIALOG",
  OPEN_FAILED_SHOP_TIPS = "OPEN_FAILED_SHOP_TIPS",
  CLOSE_FAILED_SHOP_TIPS = "CLOSE_FAILED_SHOP_TIPS",
  UPDATE_FAILED_SHOP_INFO = "UPDATE_FAILED_SHOP_INFO",
  FAILED_SHOP_ADD = "FAILED_SHOP_ADD",
  UPDATE_FAILED_SHOP_BUY = "UPDATE_FAILED_SHOP_BUY",
  UPDATE_FAILED_SHOP_OUT = "UPDATE_FAILED_SHOP_OUT",
  //公告---------------------------------------------
  //城池战斗-----------------------------------------------
  OPEN_WAR_FIGHT_DIALOG = "OPEN_WAR_FIGHT_DIALOG",
  CLOSE_WAR_FIGHT_DIALOG = "CLOSE_WAR_FIGHT_DIALOG",
  OPEN_WAR_END_DIALOG = "OPEN_WAR_END_DIALOG",
  OPEN_WAR_CITY_FIGHT_INFO_DIALOG = "OPEN_WAR_CITY_FIGHT_INFO_DIALOG",
  OPEN_WAR_TROOPS_DIALOG = "OPEN_WAR_TROOPS_DIALOG",
  OPEN_WAR_MANOR_DIALOG = "OPEN_WAR_MANOR_DIALOG",
  CLOSE_WAR_MANOR_DIALOG = "CLOSE_WAR_MANOR_DIALOG",
  OPEN_WAR_LOCK_DIALOG = "OPEN_WAR_LOCK_DIALOG",
  OPEN_WAR_DECLARE_DIALOG = "OPEN_WAR_DECLARE_DIALOG",
  //OPEN_LINE_UP_WAR_DIALOG  = "OPEN_LINE_UP_WAR_DIALOG",
  //CLOSE_LINE_UP_WAR_DIALOG  = "CLOSE_LINE_UP_WAR_DIALOG",
  OPEN_WAR_KILL_PREVIEW_DIALOG = "OPEN_WAR_KILL_PREVIEW_DIALOG",
  OPEN_WAR_KILL_DNEMY_DIALOG = "OPEN_WAR_KILL_DNEMY_DIALOG",
  CLOSE_WAR_KILL_DNEMY_DIALOG = "CLOSE_WAR_KILL_DNEMY_DIALOG",
  OPEN_WAR_KILL_RANK_DIALOG = "OPEN_WAR_KILL_RANK_DIALOG",
  OPEN_WAR_UNIFY_DIALOG = "OPEN_WAR_UNIFY_DIALOG",
  SHOW_WAR_CITY_MENU = "SHOW_WAR_CITY_MENU",
  OPEN_WAR_UNIFY_AWARD_DIALOG = "OPEN_WAR_UNIFY_AWARD_DIALOG",
  WAR_KILL_FETCH_TOC = "WAR_KILL_FETCH_TOC",
  OPEN_WAR_FAISED_DIALOG = "OPEN_WAR_FAISED_DIALOG",
  OPEN_WAR_LOCK_AWARD_DIALOG = "OPEN_WAR_LOCK_AWARD_DIALOG",
  OPEN_WAR_POSITION_DIALOG = "OPEN_WAR_POSITION_DIALOG",
  M_WAR_OPERATE_TOC = "M_WAR_OPERATE_TOC",
  M_WAR_CITY_INFO = "M_WAR_CITY_INFO",
  M_WAR_CITY_INFO_SM = "M_WAR_CITY_INFO_SM",
  OPEN_WAR_REN_MIAN_DIALOG = "OPEN_WAR_REN_MIAN_DIALOG",
  M_FAMILY_BOX_LOTTERY = "M_FAMILY_BOX_LOTTERY",
  OPEN_WAR_MANOR_INFO_DIALOG = "OPEN_WAR_MANOR_INFO_DIALOG",
  COLSE_WAR_MANOR_INFO_DIALOG = "COLSE_WAR_MANOR_INFO_DIALOG",
  M_WAR_FAMILY_CITY_FETCH = "M_WAR_FAMILY_CITY_FETCH",
  OPEN_WAR_MANOR_WAIVE_DIALOG = "OPEN_WAR_MANOR_WAIVE_DIALOG",
  UPDATE_FAMILY_CITY_LIST = "UPDATE_FAMILY_CITY_LIST",
  OPEN_WAR_TROOPS_RETIRE_DIALOG = "OPEN_WAR_TROOPS_RETIRE_DIALOG",
  CLOSE_WAR_TROOPS_DIALOG = "CLOSE_WAR_TROOPS_DIALOG",
  OPEN_WAR_POSITION_INFO_DIALOG = "OPEN_WAR_POSITION_INFO_DIALOG",
  M_WAR_FAMILY_TITLE_LIST = "M_WAR_FAMILY_TITLE_LIST",
  OPEN_WAR_CITY_TIP_DIALOG = "OPEN_WAR_CITY_TIP_DIALOG",
  CLOSE_WAR_CITY_TIP_DIALOG = "CLOSE_WAR_CITY_TIP_DIALOG",
  SHOW_WAR_KILL_RANK_BOX = "SHOW_WAR_KILL_RANK_BOX",
  M_WAR_FAMILY_SET_TITLE = "M_WAR_FAMILY_SET_TITLE",
  OPEN_WAR_MANOR_TESHE_DIALOG = "OPEN_WAR_MANOR_TESHE_DIALOG",
  OPEN_WAR_HERO_FU_HUO_DIALOG = "OPEN_WAR_HERO_FU_HUO_DIALOG",
  M_WAR_REVIVE_HERO = "M_WAR_REVIVE_HERO",
  OPEN_FAMILY_CAMP_DIALOG = "OPEN_FAMILY_CAMP_DIALOG",
  OPEN_FAMILY_SIGN_DIALOG = "OPEN_FAMILY_SIGN_DIALOG",
  OPEN_FAMILY_ACTIVE_DIALOG = "OPEN_FAMILY_ACTIVE_DIALOG",
  OPEN_FAMILY_ACTIVE_PREVIEW_DIALOG = "OPEN_FAMILY_ACTIVE_PREVIEW_DIALOG",
  M_WAR_FAMILY_CAMP = "M_WAR_FAMILY_CAMP",
  OPEN_WAR_ROLE_INFO_DIALOG = "OPEN_WAR_ROLE_INFO_DIALOG",
  OPEN_WAR_ROLE_VS_DIALOG = "OPEN_WAR_ROLE_VS_DIALOG",
  OPEN_FAMILY_CAMP_LOCK_INFO_DIALOG = "OPEN_FAMILY_CAMP_LOCK_INFO_DIALOG",
  M_WAR_ROLE_TEAM_SINGLE = "M_WAR_ROLE_TEAM_SINGLE",
  OPEN_FAMILY_XUAN_ZHAN_DIALOG = "OPEN_FAMILY_XUAN_ZHAN_DIALOG",
  M_WAR_DECLARE_LIST = "M_WAR_DECLARE_LIST",
  CLOSE_FAMILY_XUAN_ZHAN_DIALOG = "CLOSE_FAMILY_XUAN_ZHAN_DIALOG",
  OPEN_WAR_DIAO_BING_DIALOG = "OPEN_WAR_DIAO_BING_DIALOG",
  M_WAR_DEPLOY_VIEW = "M_WAR_DEPLOY_VIEW",
  WAR_GUIDE_CHECK = "WAR_GUIDE_CHECK",
  WAR_RETURN_FIGHT = "WAR_RETURN_FIGHT",
  M_WAR_CAMP_UPDATE = "M_WAR_CAMP_UPDATE",
  OPEN_WAR_HELP_DIALOG = "OPEN_WAR_HELP_DIALOG",
  OPEN_WAR_DIALOG = "OPEN_WAR_DIALOG",
  WAR_ROLE_TEAM_INFO_TOC = "WAR_ROLE_TEAM_INFO_TOC",
  WAR_ROLE_TEAM_UPDATE = "WAR_ROLE_TEAM_UPDATE",
  CLOSE_WAR_DIALOG = "ClOSE_WAR_DIALOG",
  WAR_FIGHT_HELP_TOC = "WAR_FIGHT_HELP_TOC",
  OPEN_FAMILY_ZHEN_BING_DIALOG = "OPEN_FAMILY_ZHEN_BING_DIALOG",
  M_WAR_CONSCRIPTION_OP_TOC = "M_WAR_CONSCRIPTION_OP_TOC",
  M_WAR_CONSCRIPTION_SEND_TOC = "M_WAR_CONSCRIPTION_SEND_TOC",
  M_WAR_CITY_AREA_TOC = "M_WAR_CITY_AREA_TOC",
  HIDE_WAR_PATH = "HIDE_WAR_PATH",
  M_WAR_TEAM_DEATH_TOC = "M_WAR_TEAM_DEATH_TOC",
  OPEN_WAR_TROOPS_LIST_DIALOG = "OPEN_WAR_TROOPS_LIST_DIALOG",

  //城池战斗------------------------------------------------

  //国战托管 start-----------------------------------------------
  OPEN_WAR_AUTO_DIALOG = "OPEN_WAR_AUTO_DIALOG",
  CLOSE_WAR_AUTO_DIALOG = "CLOSE_WAR_AUTO_DIALOG",
  OPEN_WAR_AUTO_SIMP_SETTING_DIALOG = "OPEN_WAR_AUTO_SIMP_SETTING_DIALOG",
  CLOSE_WAR_AUTO_SIMP_SETTING_DIALOG = "CLOSE_WAR_AUTO_SIMP_SETTING_DIALOG",
  OPEN_WAR_AUTO_FAMILY_LIST_DIALOG = "OPEN_WAR_AUTO_FAMILY_LIST_DIALOG",
  CLOSE_WAR_AUTO_FAMILY_LIST_DIALOG = "CLOSE_WAR_AUTO_FAMILY_LIST_DIALOG",
  OPEN_WAR_AUTO_LINE_UP_DIALOG = "OPEN_WAR_AUTO_LINE_UP_DIALOG",
  CLOSE_WAR_AUTO_LINE_UP_DIALOG = "CLOSE_WAR_AUTO_LINE_UP_DIALOG",
  UPDATE_WAR_AUTO_INFO = "UPDATE_WAR_AUTO_INFO",
  UPDATE_WAR_AUTO_TEAM_LIST = "UPDATE_WAR_AUTO_TEAM_LIST",
  // UPDATE_WAR_AUTO_CLIENT_STATE  = "UPDATE_WAR_AUTO_CLIENT_STATE",
  UPDATE_WAR_AUTO_SERVER_STATE = "UPDATE_WAR_AUTO_SERVER_STATE",
  CHANGE_WAR_AUTO_CITY_SELECT = "CHANGE_WAR_AUTO_CITY_SELECT",
  CHANGE_WAR_AUTO_FAMILY_SELECT = "CHANGE_WAR_AUTO_FAMILY_SELECT",
  UPDATE_WAR_AUTO_CITY_LIST = "UPDATE_WAR_AUTO_CITY_LIST",
  ON_CHANGE_WAR_AUTO_TEAM_LIST = "ON_CHANGE_WAR_AUTO_TEAM_LIST",
  //国战托管 end ------------------------------------------------

  //任务------------------
  SIMP_MISSION_UPDATE = "SIMP_MISSION_UPDATE",
  SIMP_MISSION_LIST = "SIMP_MISSION_LIST",
  SIMP_MISSION_UPDATE_ACTIVITY = "SIMP_MISSION_UPDATE_ACTIVITY",
  SIMP_MISSION_LIST_ACTIVITY = "SIMP_MISSION_LIST_ACTIVITY",
  //任务------------------

  //冠军赛----------------------------------------------
  OPEN_RANDOM_PVP_DIALOG = "OPEN_RANDOM_PVP_DIALOG",
  CLOSE_RANDOM_PVP_DIALOG = "CLOSE_RANDOM_PVP_DIALOG",
  OPEN_RANDOM_PVP_GRADE_SHOW_DIALOG = "OPEN_RANDOM_PVP_LEVEL_SHOW_DIALOG",
  CLOSE_RANDOM_PVP_GRADE_SHOW_DIALOG = "CLOSE_RANDOM_PVP_LEVEL_SHOW_DIALOG",
  OPEN_RANDOM_PVP_REWARD_DIALOG = "OPEN_RANDOM_PVP_REWARD_DIALOG",
  CLOSE_RANDOM_PVP_REWARD_DIALOG = "CLOSE_RANDOM_PVP_REWARD_DIALOG",
  OPEN_RANDOM_PVP_PALACE_DIALOG = "OPEN_RANDOM_PVP_PALACE_DIALOG",
  CLOSE_RANDOM_PVP_PALACE_DIALOG = "CLOSE_RANDOM_PVP_PALACE_DIALOG",
  OPEN_RANDOM_PVP_SCORE_CHANGE_DIALOG = "OPEN_RANDOM_PVP_SCORE_CHANGE_DIALOG",
  OPEN_RANDOM_PVP_SCORE_FIGHT_CHANGE_DIALOG = "OPEN_RANDOM_PVP_SCORE_FIGHT_CHANGE_DIALOG",
  OPEN_RANDOM_PVP_SEASON_SELECT_DIALOG = "OPEN_RANDOM_PVP_SEASON_SELECT_DIALOG",
  OPEN_RANDOM_PVP_LOGS_DIALOG = "OPEN_RANDOM_PVP_LOGS_DIALOG",
  OPEN_RANDOM_PVP_RANK_DIALOG = "OPEN_RANDOM_PVP_RANK_DIALOG",
  OPEN_RANDOM_PVP_NEW_SEASON_DIALOG = "OPEN_RANDOM_PVP_NEW_SEASON_DIALOG",
  OPEN_RANDOM_PVP_RANDOM_DIALOG = "OPEN_RANDOM_PVP_RANDOM_DIALOG",
  CLOSE_RANDOM_PVP_RANDOM_DIALOG = "CLOSE_RANDOM_PVP_RANDOM_DIALOG",
  UPDATE_RANDOM_PVP_NEW_SEASON = "UPDATE_RANDOM_PVP_NEW_SEASON",
  ON_CHANGE_RANDOM_PVP_SEASON_SELECT = "ON_CHANGE_RANDOM_PVP_SEASON_SELECT",
  UPDATE_RANDOM_PVP_MISSION_LIST = "UPDATE_RANDOM_PVP_MISSION_LIST",
  UPDATE_RANDOM_PVP_GRADE_REWARD = "UPDATE_RANDOM_PVP_TITLE_REWARD",
  UPDATE_RANDOM_PVP_INFO = "UPDATE_RANDOM_PVP_INFO",
  FIGHT_RANDOM_PVP_FINISH = "FIGHT_RANDOM_PVP_FINISH",
  UPDATE_RANDOM_PVP_PALACE_INFO = "UPDATE_RANDOM_PVP_PALACE_INFO",
  OPEN_RANDOM_PVP_PASSPORT_DIALOG = "OPEN_RANDOM_PVP_PASSPORT_DIALOG",
  CLOSE_RANDOM_PVP_PASSPORT_DIALOG = "CLOSE_RANDOM_PVP_PASSPORT_DIALOG",
  UPDATE_RANDOM_PVP_PASSPORT_INFO = "UPDATE_PASSPORT_INFO",
  OPEN_RANDOM_PVP_PASSPORT_UNLOCK_TIPS_DIALOG = "OPEN_RANDOM_PVP_PASSPORT_UNLOCK_TIPS_DIALOG",
  OPEN_WAR_ZONE_SHOP_BUG_TIP_DIALOG = "OPEN_WAR_ZONE_SHOP_BUG_TIP_DIALOG",
  OPEN_SERVICE_DETAIL_LIST_TIP = "OPEN_SERVICE_DETAIL_LIST_TIP",
  OPEN_RANDOM_PVP_SERVER_LIST_DIALOG = "OPEN_RANDOM_PVP_SERVER_LIST_DIALOG",
  CLOSE_SERVICE_DETAIL_LIST_TIP = "CLOSE_SERVICE_DETAIL_LIST_TIP",
  SHOW_SERVICE_DETAIL_LIST_TIP = "SHOW_SERVICE_DETAIL_LIST_TIP",
  //冠军赛----------------------------------------------
  /**模块化通行证 */
  UPDATE_ACT_MODS_BASE_PASSPORT_INFO = "UPDATE_ACT_MODS_BASE_PASSPORT_INFO",
  /**段位塞通行证 汉中争夺战通行证 无任务通行证 模块化 */
  OPEN_ACT_MODS_PVP_PASSPORT_DIALOG = "OPEN_ACT_MODS_PVP_PASSPORT_DIALOG",
  CLOSE_ACT_MODS_PVP_PASSPORT_DIALOG = "CLOSE_ACT_MODS_PVP_PASSPORT_DIALOG",
  OPEN_ACT_MODS_PVP_PASSPORT_UNLOCK_TIPS_DIALOG = "OPEN_ACT_MODS_PVP_PASSPORT_UNLOCK_TIPS_DIALOG",
  OPEN_CITY_RANDOM_PVP_BUILD = "OPEN_CITY_RANDOM_PVP_BUILD",
  CLOSE_ACT_MODS_STORY_CHAPTER_DIALOG = "CLOSE_ACT_MODS_STORY_CHAPTER_DIALOG",
  UPDATE_DRAMA_DIALOG = "UPDATE_DRAMA_DIALOG",
  CHANGE_RANDOM_PVP_TAB = "CHANGE_RANDOM_PVP_TAB",
  //神器系统---------------------------------------------
  ClOSE_WEAPON_DIALOG = "ClOSE_WEAPON_DIALOG",
  OPEN_WEAPON_SHOW_DIALOG = "OPEN_WEAPON_SHOW_DIALOG",
  ClOSE_WEAPON_SHOW_DIALOG = "ClOSE_WEAPON_SHOW_DIALOG",
  OPEN_WEAPON_TIPS_INFO_DIALOG = "OPEN_WEAPON_TIPS_INFO_DIALOG",
  OPEN_WEAPON_SOUL_DIALOG = "OPEN_WEAPON_SOUL_DIALOG",
  OPEN_WEAPON_SKILL_INFO_DIALOG = "OPEN_WEAPON_SKILL_INFO_DIALOG",
  OPEN_WEAPON_ACTIVATE_TIPS_DIALOG = "OPEN_WEAPON_ACTIVATE_TIPS_DIALOG",
  OPEN_WEAPON_CHANGE_DIALOG = "OPEN_WEAPON_CHANGE_DIALOG",
  UPDATE_WEAPON_UPGRADE_INFO = "UPDATE_WEAPON_UPGRADE_INFO",
  UPDATE_WEAPON_MISSION_INFO = "UPDATE_WEAPON_MISSION_INFO",
  UPDATE_WEAPON_INFO = "UPDATE_WEAPON_INFO",
  UPDATE_WEAPON_SELECTID_INFO = "UPDATE_WEAPON_SELECTID_INFO",
  UPDATE_WEAPON_SWITCH_INFO = "UPDATE_WEAPON_SWITCH_INFO",

  UPDATE_WEAPON_STRENG = "UPDATE_WEAPON_STRENG",
  UPDATE_WEAPON_SKILL = "UPDATE_WEAPON_SKILL",
  UPDATE_WEAPON_REFINE = "UPDATE_WEAPON_REFINE",
  UPDATE_WEAPON_SOUL = "UPDATE_WEAPON_SOUL",

  //w7
  OPEN_GOD_WEAPON_DIALOG = "OPEN_GOD_WEAPON_DIALOG",
  CLOSE_GOD_WEAPON_DIALOG = "CLOSE_GOD_WEAPON_DIALOG",
  OPEN_GOD_WEAPON_SOUL_DIALOG = "OPEN_GOD_WEAPON_SOUL_DIALOG",
  OPEN_GOD_WEAPON_ACTIVE_DIALOG = "OPEN_GOD_WEAPON_ACTOVE_DIALOG",
  OPEN_GOD_WEAPON_MAX_TIP_DIALOG = "OPEN_GOD_WEAPON_MAX_TIP_DIALOG",
  OPEN_GOD_WEAPON_ALL_SHOW_DIALOG = "OPEN_GOD_WEAPON_ALL_SHOW_DIALOG",
  OPEN_GOD_WEAPON_UPGRADE_COMPLETE_DIALOG = "OPEN_GOD_WEAPON_UPGRADE_COMPLETE_DIALOG",
  OPEN_GOD_WEAPON_UPSTAR_COMPLETE_DIALOG = "OPEN_GOD_WEAPON_UPSTAR_COMPLETE_DIALOG",
  UPDATE_GOD_WEAPON_INFO = "UPDATE_GOD_WEAPON_INFO",
  UPDATE_GOD_WEAPON_UPGRADE = "UPDATE_GOD_WEAPON_UPGRADE",
  GOD_WEAPON_SELECT_WEAPON = "GOD_WEAPON_SELECT_WEAPON",
  GOD_WEAPON_CHANGE_WEAPON = "GOD_WEAPON_CHANGE_WEAPON",
  GOD_WEAPON_SELECT_TAB = "GOD_WEAPON_SELECT_TAB",
  //神器系统---------------------------------------------

  //武神庙(占星台)-------------------------
  OPEN_HERO_TEMPLE2_DIALOG = "OPEN_HERO_TEMPLE2_DIALOG",
  CLOSE_HERO_TEMPLE2_DIALOG = "CLOSE_HERO_TEMPLE2_DIALOG",
  OPEN_HERO_TEMPLE2_SELECT_HERO_DIALOG = "OPEN_HERO_TEMPLE2_SELECT_HERO_DIALOG",
  CLOSE_HERO_TEMPLE2_SELECT_HERO_DIALOG = "CLOSE_HERO_TEMPLE2_SELECT_HERO_DIALOG",
  OPEN_HERO_TEMPLE2_SELECT_MAIN_HERO_DIALOG = "OPEN_HERO_TEMPLE2_SELECT_MAIN_HERO_DIALOG",
  CLOSE_HERO_TEMPLE2_SELECT_MAIN_HERO_DIALOG = "CLOSE_HERO_TEMPLE2_SELECT_MAIN_HERO_DIALOG",
  OPEN_HERO_TEMPLE2_UN_LOAD_HERO_DIALOG = "OPEN_HERO_TEMPLE2_UN_LOAD_HERO_DIALOG",
  OPEN_HERO_TEMPLE2_LOAD_HERO_SUCCESS_DIALOG = "OPEN_HERO_TEMPLE2_LOAD_HERO_SUCCESS_DIALOG",
  UPDATE_HERO_TEMPLE2_SHOW_LIST = "UPDATE_HERO_TEMPLE2_SHOW_LIST",
  UPDATE_HERO_TEMPLE2_GRID_LIST = "UPDATE_HERO_TEMPLE2_GRID_LIST",
  //武神庙-------------------------


  // 南蛮入侵 面板---------------------
  // OPEN_RANDOM_BOSS_DIALOG  = "OPEN_RANDOM_BOSS_DIALOG",
  // CLOSE_RANDOM_BOSS_DIALOG  = "CLOSE_RANDOM_BOSS_DIALOG",
  // OPEN_RANDOM_BOSS_FIGHT_DIALOG  = "OPEN_RANDOM_BOSS_FIGHT_DIALOG",
  // UPDATE_RANDOM_BOSS_DIALOG  = "UPDATE_RANDOM_BOSS_DIALOG",
  // CLOSE_RANDOM_BOSS_FIGHT_DIALOG  = "CLOSE_RANDOM_BOSS_FIGHT_DIALOG",
  OPEN_WORLDBOSS_GIFT_TIPS_DIALOG = "OPEN_WORLDBOSS_GIFT_TIPS_DIALOG",
  OPEN_WORLD_BOSS_DIALOG = "OPEN_WORLD_BOSS_DIALOG",
  CLOSE_WORLD_BOSS_DIALOG = "CLOSE_WORLD_BOSS_DIALOG",
  // OPEN_RANDOM_BOSS_SCORE_DIALOG  = "OPEN_RANDOM_BOSS_SCORE_DIALOG",
  OPEN_WORLD_BOSS_BUY_DIALOG = "OPEN_WORLD_BOSS_BUY_DIALOG",
  CLOSE_WORLD_BOSS_BUY_DIALOG = "CLOSE_WORLD_BOSS_BUY_DIALOG",
  OPEN_WORLD_BOSS_REWARD_DIALOG = "OPEN_WORLD_BOSS_REWARD_DIALOG",
  CLOSE_WORLD_BOSS_REWARD_DIALOG = "CLOSE_WORLD_BOSS_REWARD_DIALOG",
  UPDATE_WORLD_BOSS_DIALOG = "UPDATE_WORLD_BOSS_DIALOG",
  UPDATE_WORLD_BOSS_HP = "UPDATE_WORLD_BOSS_HP",
  UPDATE_WORLD_BOSS_FIGHT_RESULT = "UPDATE_WORLD_BOSS_FIGHT_RESULT",
  UPDATE_RANDOM_BOSS_ATTACK = "UPDATE_RANDOM_BOSS_ATTACK",
  UPDATE_RANDOM_BOSS = "UPDATE_RANDOM_BOSS",
  OPEN_WORLD_BOSS_ACCRE_RANK_DIALOG = "OPEN_WORLD_BOSS_ACCRE_RANK_DIALOG",
  OPEN_WORLD_BOSS_ANECDOTAL_DIALOG = "OPEN_WORLD_BOSS_ANECDOTAL_DIALOG",
  //坐骑
  OPEN_RIDE_DIALOG = "OPEN_RIDE_DIALOG",
  OPEN_RIDE_GOOD_SELECT_DIALOG = "OPEN_RIDE_GOOD_SELECT_DIALOG",
  LOAD_RIDE_ITEM = "LOAD_RIDE_ITEM",
  DELETE_RIDE_ITEM = "DELETE_RIDE_ITEM",
  UPDATE_RIDE_COST_LIST = "UPDATE_RIDE_COST_LIST",
  UPDATE_STAR_UP_COST = "UPDATE_STAR_UP_COST",
  OPEN_RIDE_SHOP_BUY_DIALOG = "OPEN_RIDE_SHOP_BUY_DIALOG",
  RIDE_UPDATE_SUCCESS = "RIDE_UPDATE_SUCCESS",
  RIDE_UPDATE_FAILED = "RIDE_UPDATE_FAILED",
  OPEN_RIDE_TAB = "OPEN_RIDE_TAB",
  OPEN_RIDE_SKILL_REPLACE_DIALOG = "OPEN_RIDE_SKILL_REPLACE_DIALOG",
  UPDATE_RIDE_SKILL_LOCK_STATE = "UPDATE_RIDE_SKILL_LOCK_STATE",
  UPDATE_RIDE_REFRESH_SKILL = "UPDATE_RIDE_REFRESH_SKILL",
  UPDATE_RIDE_REFRESH_SKILL_DELAY = "UPDATE_RIDE_REFRESH_SKILL_DELAY",
  RIDE_REPLACE_SKILL_SELECT = "RIDE_REPLACE_SKILL_SELECT",
  OPEN_RIDE_SKILL_SUCCESS_DIALOG = "OPEN_RIDE_SKILL_SUCCESS_DIALOG",
  OPEN_RIDE_COLOR_DIALOG = "OPEN_RIDE_COLOR_DIALOG",
  OPEN_RIDE_SKILL_SHOW_DIALOG = "OPEN_RIDE_SKILL_SHOW_DIALOG",
  OPEN_RIDE_SKILL_TIPS_DIALOG = "OPEN_RIDE_SKILL_TIPS_DIALOG",
  OPEN_RIDE_TUJIAN_DIALOG = "OPEN_RIDE_TUJIAN_DIALOG",
  CLOSE_RIDE_TUJIAN_DIALOG = "CLOSE_RIDE_TUJIAN_DIALOG",
  OPEN_RIDE_TUJIAN_LEVEL_ATTR_DIALOG = "OPEN_RIDE_TUJIAN_LEVEL_ATTR_DIALOG",
  OPEN_RIDE_TUJIAN_LEVEL_DIALOG = "OPEN_RIDE_TUJIAN_LEVEL_DIALOG",
  CLOSE_RIDE_TUJIAN_LEVEL_DIALOG = "CLOSE_RIDE_TUJIAN_LEVEL_DIALOG",
  OPEN_RIDE_TUJIAN_LEVEL_ACTIVE_DIALOG = "OPEN_RIDE_TUJIAN_LEVEL_ACTIVE_DIALOG",
  CLOSE_RIDE_TUJIAN_LEVEL_ACTIVE_DIALOG = "CLOSE_RIDE_TUJIAN_LEVEL_ACTIVE_DIALOG",
  OPEN_RIDE_TUJIAN_STAR_DIALOG = "OPEN_RIDE_TUJIAN_STAR_DIALOG",
  OPEN_RIDE_TUJIAN_STAR_ATTR_DIALOG = "OPEN_RIDE_TUJIAN_STAR_ATTR_DIALOG",
  OPEN_RIDE_TUJIAN_ACTIVE_SUCCESS_DIALOG = "OPEN_RIDE_TUJIAN_ACTIVE_SUCCESS_DIALOG",
  RIDE_EQUIP_SELECT_COLOR_DATA = "RIDE_EQUIP_SELECT_COLOR_DATA",
  RIDE_CHANGE_TAB_DATA = "RIDE_CHANGE_TAB_DATA",
  RIDE_UPDATE_TUJIAN_INFO = "RIDE_UPDATE_TUJIAN_INFO",
  DO_REFINE_ONE_KEY = "DO_REFINE_ONE_KEY",
  OPEN_RIDE_TUJIAN_SKILL_SHOW_DIALOG = "OPEN_RIDE_TUJIAN_SKILL_SHOW_DIALOG",
  OPEN_RIDE_SKILL_SLOT_DETAIL_DIALOG = "OPEN_RIDE_SKILL_SLOT_DETAIL_DIALOG",
  //WAR_GUIDE
  WAR_START_GUIDE = "WAR_START_GUIDE",
  WAR_NEXT_GUIDE = "WAR_NEXT_GUIDE",
  WAR_GUIDE_STOP = "WAR_GUIDE_STOP",
  WAR_ARMY_NUMBER_GUIDE = "WAR_ARMY_NUMBER_GUIDE",
  WAR_ARMY_ITEM_POS = "WAR_ARMY_ITEM_POS",
  WAR_JOIN_FIGHT_STATUS = "WAR_JOIN_FIGHT_STATUS",

  WAR_ROLE_MAO_PAO_ADD = "WAR_ROLE_MAO_PAO_ADD",
  WAR_MASTER_CARD_MAO_PAO = "WAR_MASTER_CARD_MAO_PAO",
  WAR_ROLE_MAO_PAO = "WAR_ROLE_MAO_PAO",
  WAR_ROLE_MAO_PAO_DEL = "WAR_ROLE_MAO_PAO_DEL",

  //登陆广告
  OPEN_AD_DIALOG = "OPEN_AD_DIALOG",
  CLOSE_AD_DIALOG = "CLOSE_AD_DIALOG",

  //英雄评论
  OPEN_HERO_REMARK_DIALOG = "OPEN_HERO_REMARK_DIALOG",
  CLOSE_HERO_REMARK_DIALOG = "CLOSE_HERO_REMARK_DIALOG",
  UPDATE_HERO_REMARK = "UPDATE_HERO_REMARK",

  M_WAR_FAMILY_CITY_TOC = "M_WAR_FAMILY_CITY_TOC",

  //-----英雄收集 start ------
  OPEN_HERO_COLLECT_DIALOG = "OPEN_HERO_COLLECT_DIALOG",
  CLOSE_HERO_COLLECT_DIALOG = "CLOSE_HERO_COLLECT_DIALOG",
  OPEN_HERO_COLLECT_PRE_DIALOG = "OPEN_HERO_COLLECT_PRE_DIALOG",
  CLOSE_HERO_COLLECT_PRE_DIALOG = "CLOSE_HERO_COLLECT_PRE_DIALOG",
  UPDATE_HERO_COLLECT_MISSION = "UPDATE_HERO_COLLECT_MISSION",
  UPDATE_HERO_COLLECT_PRE_INFO = "UPDATE_HERO_COLLECT_PRE_INFO",
  UPDATE_HERO_COLLECT_MAIN_UI_SHOW = "UPDATE_HERO_COLLECT_MAIN_UI_SHOW",
  //-----英雄收集 end --------


  //------公会红包 start -----
  OPEN_FAMILY_REDPACK_DIALOG = "OPEN_FAMILY_REDPACK_DIALOG",
  CLOSE_FAMILY_REDPACK_DIALOG = "CLOSE_FAMILY_REDPACK_DIALOG",
  OPEN_FAMILY_REDPACK_OPEN_LIST_DIALOG = "OPEN_FAMILY_REDPACK_OPEN_LIST_DIALOG",
  // CLOSE_FAMILY_REDPACK_OPEN_LIST_DIALOG  = "CLOSE_FAMILY_REDPACK_OPEN_LIST_DIALOG",


  FAMILY_REDPACK_UPDATE = "FAMILY_REDPACK_UPDATE",
  FAMILY_REDPACK_INFO = "FAMILY_REDPACK_INFO",
  FAMILY_REDPACK_BLESS_CHOOSE = "FAMILY_REDPACK_BLESS_CHOOSE",
  FAMILY_REDPACK_ITEM_CHOOSE = "FAMILY_REDPACK_ITEM_CHOOSE",
  FAMILY_REDPACK_FETCH_UPDATE = "FAMILY_REDPACK_FETCH_UPDATE",
  FAMILY_REDPACK_FETCH_TIMEOUT = "FAMILY_REDPACK_FETCH_TIMEOUT",
  FAMILY_REDPACK_MISSION_FETCH_UPDATE = "FAMILY_REDPACK_MISSION_FETCH_UPDATE",
  //------公会红包 end -----

  //------我要变强 start -----
  OPEN_STRONG_DIALOG = "OPEN_STRONG_DIALOG",
  OPEN_STRONG_MY_COLLET_DIALOG = "OPEN_STRONG_MY_COLLET_DIALOG",
  CLOSE_STRONG_DIALOG = "CLOSE_STRONG_DIALOG",
  UNDATE_HERO_LIENUP_DIALOG = "UNDATE_HERO_LIENUP_DIALOG",
  UNDATE_OME_HERO_STRENGTHEN_DIALOG = "UNDATE_OME_HERO_STRENGTHEN_DIALOG",
  //------我要变强 end -----
  //------活动攻略 start -----
  OPEN_ACTIVIRY_GUIDE_DIALOG = "OPEN_ACTIVIRY_GUIDE_DIALOG",
  UPDATE_ACTIVIRY_GUIDE_DIALOG = "UPDATE_ACTIVIRY_GUIDE_DIALOG",
  //--------活动攻略 end --------
  //--------战魂 start --------
  OPEN_HERO_SOUL_DIALOG = "OPEN_FIGHT_SOUL_DIALOG",
  CLOSE_HERO_SOUL_DIALOG = "CLOSE_FIGHT_SOUL_DIALOG",
  OPEN_HERO_SOUL_INTRODUCE_DIALOG = "OPEN_HERO_SOUL_INTRODUCE_DIALOG",
  CLOSE_HERO_SOUL_INTRODUCE_DIALOG = "CLOSE_HERO_SOUL_INTRODUCE_DIALOG",
  OPEN_HERO_SOUL_STRENGTH_DIALOG = "OPEN_HERO_SOUL_STRENGTH_DIALOG",
  CLOSE_HERO_SOUL_STRENGTH_DIALOG = "CLOSE_HERO_SOUL_STRENGTH_DIALOG",
  OPEN_HERO_SOUL_COMPOSE_SELECT_DIALOG = "OPEN_HERO_SOUL_COMPOSE_SELECT_DIALOG",
  OPEN_HERO_SOUL_WS_ATTR_RANDOM_LIB_DIALOG = "OPEN_HERO_SOUL_WS_ATTR_RANDOM_LIB_DIALOG",
  OPEN_HERO_SOUL_RANDOM_ATTR_LIB_DIALOG = "OPEN_HERO_SOUL_RANDOM_ATTR_LIB_DIALOG",
  OPEN_HERO_SOUL_HERO_ALL_ATTR_DIALOG = "OPEN_HERO_SOUL_HERO_ALL_ATTR_DIALOG",
  OPEN_HERO_SOUL_Skill_DIALOG = "OPEN_HERO_SOUL_Skill_DIALOG",
  OPEN_HERO_SOUL_COMPARE_DIALOG = "OPEN_HERO_SOUL_COMPARE_DIALOG",
  CLOSE_HERO_SOUL_COMPARE_DIALOG = "CLOSE_HERO_SOUL_COMPARE_DIALOG",
  REQUEST_HERO_SOUL_JUE_XING = "REQUEST_HERO_SOUL_JUE_XING",
  REQUEST_HERO_SOUL_XI_LIAN = "REQUEST_HERO_SOUL_XI_LIAN",
  REQUEST_HERO_SOUL_LOAD = "REQUEST_HERO_SOUL_LOAD",
  UPDATE_HERO_SOUL_STRENGTH_COST_LIST = "UPDATE_HERO_SOUL_STRENGTH_COST_LIST",
  REQUEST_ADD_HERO_SOUL_STRENGTH_ITEM = "REQUEST_ADD_HERO_SOUL_STRENGTH_ITEM",
  UPDATE_HERO_SOUL_XI_LIAN_LOCK_ATTR_LIST = "UPDATE_HERO_SOUL_XI_LIAN_LOCK_ATTR_LIST",
  UPDATE_HERO_SOUL_COMPOSE_COST_LIST = "UPDATE_HERO_SOUL_COMPOSE_COST_LIST",
  UPDATE_HERO_SOUL_STRENGTH_VIEW = "UPDATE_HERO_SOUL_STRENGTH_VIEW",
  ON_HERO_SOUL_STRENGTH_FILTER = "ON_HERO_SOUL_STRENGTH_FILTER",
  UPDATE_HERO_SOUL_JUE_XING_VIEW = "UPDATE_HERO_SOUL_JUE_XING_VIEW",
  UPDATE_HERO_SOUL_XI_LIAN_VIEW = "UPDATE_HERO_SOUL_XI_LIAN_VIEW",
  ON_CHANGE_HERO_SOUL_HERO_ID = "ON_CHANGE_HERO_SOUL_HERO_ID",
  REQUEST_OPEN_HERO_SOUL_COMPOSE_SELECT_DIALOG = "REQUEST_OPEN_HERO_SOUL_COMPOSE_SELECT_DIALOG",
  HERO_SOUL_COMPOSE_TOC = "UPDATE_HERO_SOUL_COMPOSE_TOC",
  //--------战魂 end ----------

  //-------助阵  start------------
  OPEN_CHEER_DIALOG = "OPEN_CHEER_DIALOG",
  CLOSE_CHEER_DIALOG = "CLOSE_CHEER_DIALOG",
  //------助阵 end -----

  //-------助阵.头衔  start------------
  OPEN_CHEER_PREFIX_DIALOG = "OPEN_CHEER_PREFIX_DIALOG",
  CLOSE_CHEER_PREFIX_DIALOG = "CLOSE_CHEER_PREFIX_DIALOG",
  OPEN_CHEER_PREFIX_SKILL_UP_DIALOG = "OPEN_CHEER_PREFIX_SKILL_UP_DIALOG",
  CLOSE_CHEER_PREFIX_SKILL_UP_DIALOG = "CLOSE_CHEER_PREFIX_SKILL_UP_DIALOG",
  OPEN_CHEER_PREFIX_SKILL_FULL_DIALOG = "OPEN_CHEER_PREFIX_SKILL_FULL_DIALOG",
  CLOSE_CHEER_PREFIX_SKILL_FULL_DIALOG = "CLOSE_CHEER_PREFIX_SKILL_FULL_DIALOG",
  OPEN_CHEER_PREFIX_SKILL_TIPS_DIALOG = "OPEN_CHEER_PREFIX_SKILL_TIPS_DIALOG",
  CLOSE_CHEER_PREFIX_SKILL_TIPS_DIALOG = "CLOSE_CHEER_PREFIX_SKILL_TIPS_DIALOG",
  OPEN_CHEER_PREFIX_CHANGE_DIALOG = "OPEN_CHEER_PREFIX_CHANGE_DIALOG",
  CLOSE_CHEER_PREFIX_CHANGE_DIALOG = "CLOSE_CHEER_PREFIX_CHANGE_DIALOG",

  CHEER_PREFIX_UPDATE = "CHEER_PREFIX_UPDATE",
  CHEER_PREFIX_SKILL_INFO = "CHEER_PREFIX_SKILL_INFO",
  CHEER_PREFIX_SKILL_UPDATE = "CHEER_PREFIX_SKILL_UPDATE",
  CHEER_PREFIX_SKILL_UPGRADE = "CHEER_PREFIX_SKILL_UPGRADE",
  CHEER_PREFIX_SKILL_UPGRADE_CLOSE = "CHEER_PREFIX_SKILL_UPGRADE_CLOSE",
  //------助阵.头衔 end -----

  //-------全民升星 start------------
  FASHION_ACTIVITY_INFO = "FASHION_ACTIVITY_INFO",
  FASHION_ACTIVITY_UPDATE = "FASHION_ACTIVITY_UPDATE",

  OPEN_FASHION_ACTIVITY_DIALOG = "OPEN_FASHION_ACTIVITY_DIALOG",
  CLOSE_FASHION_ACTIVITY_DIALOG = "CLOSE_FASHION_ACTIVITY_DIALOG",

  OPEN_FASHION_ACTIVITY_ATTR_DIALOG = "OPEN_FASHION_ACTIVITY_ATTR_DIALOG",
  CLOSE_FASHION_ACTIVITY_ATTR_DIALOG = "CLOSE_FASHION_ACTIVITY_ATTR_DIALOG",

  OPEN_FASHION_ACTIVITY_TIPS_DIALOG = "OPEN_FASHION_ACTIVITY_TIPS_DIALOG",
  CLOSE_FASHION_ACTIVITY_TIPS_DIALOG = "CLOSE_FASHION_ACTIVITY_TIPS_DIALOG",

  OPEN_FASHION_DIALOG = "OPEN_FASHION_DIALOG",
  CLOSE_FASHION_DIALOG = "CLOSE_FASHION_DIALOG",
  //------全民升星 end -----

  //-----CCT ------
  OPEN_CCT_DIALOG = "OPEN_CCT_DIALOG",

  GUA_JI_TOWER_DIALOG = "GUA_JI_TOWER_DIALOG",
  //挂机重置
  GUA_JI_TOWER_RESET = "GUA_JI_TOWER_RESET",
  GUA_JI_TOWER_BLUR = "GUA_JI_TOWER_BLUR",

  //----------兵器----------------
  OPEN_FAMILY_SYMBOL_DIALOG = "OPEN_FAMILY_SYMBOL_DIALOG",
  CLOSE_FAMILY_SYMBOL_DIALOG = "CLOSE_FAMILY_SYMBOL_DIALOG",
  OPEN_FAMILY_SYMBOL_MAKE_DIALOG = "OPEN_FAMILY_SYMBOL_MAKE_DIALOG",
  CLOSE_FAMILY_SYMBOL_MAKE_DIALOG = "CLOSE_FAMILY_SYMBOL_MAKE_DIALOG",
  OPEN_FAMILY_SYMBOL_SHIFT_DIALOG = "OPEN_FAMILY_SYMBOL_SHIFT_DIALOG",
  CLOSE_FAMILY_SYMBOL_SHIFT_DIALOG = "CLOSE_FAMILY_SYMBOL_SHIFT_DIALOG",
  OPEN_FAMILY_SYMBOL_RESOLVE_DIALOG = "OPEN_FAMILY_SYMBOL_RESOLVE_DIALOG",
  OPEN_FAMILY_SYMBOL_GEM_DIALOG = "OPEN_FAMILY_SYMBOL_GEM_DIALOG",
  OPEN_FAMILY_SYMBOL_GEM_BAG_DIALOG = "OPEN_FAMILY_SYMBOL_GEM_BAG_DIALOG",
  CLOSE_FAMILY_SYMBOL_GEM_DIALOG = "CLOSE_FAMILY_SYMBOL_GEM_DIALOG",
  CLOSE_FAMILY_SYMBOL_GEM__BAG_DIALOG = "CLOSE_FAMILY_SYMBOL_GEM__BAG_DIALOG",
  OPEN_FAMILY_SYMBOL_ONE_COMPOSE_DIALOG = "OPEN_FAMILY_SYMBOL_ONE_COMPOSE_DIALOG",
  OPEN_FAMILY_SYMBOL_CHANCE_DIALOG = "OPEN_FAMILY_SYMBOL_CHANCE_DIALOG",
  OPEN_FAMILY_SYMBOL_TALENT_DIALOG = "OPEN_FAMILY_SYMBOL_TALENT_DIALOG",
  OPEN_FAMILY_SYMBOL_STAR_UP_DIALOG = "OPEN_FAMILY_SYMBOL_STAR_UP_DIALOG",
  OPEN_FAMILY_SYMBOL_MORE_APPRAISE_DIALOG = "OPEN_FAMILY_SYMBOL_MORE_APPRAISE_DIALOG",
  CLOSE_FSMILY_QUICK_CHSNGE_LIST = "CLOSE_FSMILY_QUICK_CHSNGE_LIST",
  OPEN_FAMILY_SYMBOL_MORE_SALE_DIALOG = "OPEN_FAMILY_SYMBOL_MORE_SALE_DIALOG",
  OPEN_SYMBOL_COMPOSE_DIALOG = "OPEN_SYMBOL_COMPOSE_DIALOG",
  CLOSE_SYMBOL_COMPOSE_DIALOG = "CLOSE_SYMBOL_COMPOSE_DIALOG",
  UPDATE_SYMBOL_ATTR_INFO = "UPDATE_SYMBOL_ATTR_INFO",
  UPDATE_SYMBOL_BAG_INFO = "UPDATE_SYMBOL_BAG_INFO",
  UPDATE_SYMBOL_STAGE_INFO = "UPDATE_SYMBOL_STAGE_INFO",
  UPDATE_SYMBOL_SHIFT_INFO = "UPDATE_SYMBOL_SHIFT_INFO",
  GET_SYMBOL_SHIFT = "GET_SYMBOL_SHIFT",
  UPDATE_SYMBOL_BREED_INFO = "UPDATE_SYMBOL_BREED_INFO",
  UPDATE_SYMBOL_GEM_NATION_INFO = "UPDATE_SYMBOL_GEM_NATION_INFO",
  UPDATE_SYMBOL_TAB_INFO = "UPDATE_SYMBOL_TAB_INFO",
  UPDATE_EQUIP_SYMBOL_INFO = "UPDATE_EQUIP_SYMBOL_INFO",
  UPDATE_SYMBOL_MAKE_INFO = "UPDATE_SYMBOL_MAKE_INFO",
  UPDATE_SYMBOL_STAR_INFO = "UPDATE_SYMBOL_STAR_INFO",
  UPDATE_SYMBOL_EQUIP_GEM_INFO = "UPDATE_SYMBOL_EQUIP_GEM_INFO",
  UPDATE_SYMBOL_SHIFT_ITEM_SELECT = "UPDATE_SYMBOL_SHIFT_ITEM_SELECT",
  REQUEST_SYMBOL_GEM_CHANGE = "REQUEST_SYMBOL_GEM_CHANGE",
  SYMBOL_FLY_EFFECT = "SYMBOL_FLY_EFFECT",

  OPEN_FAMILY_SYMBOL_ATTR_DIALOG = "OPEN_FAMILY_SYMBOL_ATTR_DIALOG",
  CLOSE_FAMILY_SYMBOL_ATTR_DIALOG = "CLOSE_FAMILY_SYMBOL_ATTR_DIALOG",

  //----------兵器----------------

  //------- 混乱之地  start------------
  OPEN_CHAOS_DIALOG = "OPEN_CHAOS_DIALOG",
  CLOSE_CHAOS_DIALOG = "CLOSE_CHAOS_DIALOG",
  OPEN_CHAOS_CHAPTER_DIALOG = "OPEN_CHAOS_CHAPTER_DIALOG",
  CLOSE_CHAOS_CHAPTER_DIALOG = "CLOSE_CHAOS_CHAPTER_DIALOG",
  OPEN_CHAOS_FIGHT_DIALOG = "OPEN_CHAOS_FIGHT_DIALOG",
  CLOSE_CHAOS_FIGHT_DIALOG = "CLOSE_CHAOS_FIGHT_DIALOG",
  OPEN_CHAOS_REWARD_DIALOG = "OPEN_CHAOS_REWARD_DIALOG",
  CLOSE_CHAOS_REWARD_DIALOG = "CLOSE_CHAOS_REWARD_DIALOG",
  OPEN_CHAOS_REWARD_TIPS_DIALOG = "OPEN_CHAOS_REWARD_TIPS_DIALOG",
  CLOSE_CHAOS_REWARD_TIPS_DIALOG = "CLOSE_CHAOS_REWARD_TIPS_DIALOG",

  CHAOS_SHOW_EFFECT = "CHAOS_SHOW_EFFECT",
  CHAOS_COPY_INFO = "CHAOS_COPY_INFO",
  CHAOS_COPY_MONSTER = "CHAOS_COPY_MONSTER",
  CHAOS_COPY_FIGHT_FINISH = "CHAOS_COPY_FIGHT_FINISH",
  CHAOS_COPY_FIGHT_FINISH_REBACK = "CHAOS_COPY_FIGHT_FINISH_REBACK", //战斗完成事件的回调
  //------ 混乱之地 end -----


  /**挂机奖励 */
  GUA_JI_TOWER_AWARD = "GUA_JI_TOWER_AWARD",
  /**实力对比 */
  OPEN_POWER_COMPARE_DIALOG = "OPEN_POWER_COMPARE_DIALOG",
  CLOSE_POWER_COMPARE_DIALOG = "CLOSE_POWER_COMPARE_DIALOG",

  OPEN_POWER_COMPARE_SINGLE_DIALOG = "OPEN_POWER_COMPARE_SINGLE_DIALOG",
  OPEN_POWER_COMPARE_SINGLE_2_DIALOG = "OPEN_POWER_COMPARE_SINGLE_2_DIALOG",
  REQUEST_POWER_COMPARE_SINGLE = "REQUEST_POWER_COMPARE_SINGLE",

  /**条件直购礼包 */
  OPEN_MISSION_SHOP_DIALOG = "OPEN_MISSION_SHOP_DIALOG",
  CLOSE_MISSION_SHOP_DIALOG = "CLOSE_MISSION_SHOP_DIALOG",
  OPEN_MISSION_SHOP_TIP_DIALOG = "OPEN_MISSION_SHOP_TIP_DIALOG",
  OPEN_MISSION_SHOP_OME_TIP_DIALOG = "OPEN_MISSION_SHOP_OME_TIP_DIALOG",
  CLOSE_MISSION_SHOP_TIP_DIALOG = "CLOSE_MISSION_SHOP_TIP_DIALOG",
  UPDATE_MISSION_SHOP_INFO = "UPDATE_MISSION_SHOP_INFO",

  //主动调用打开missionShopTip
  OPEN_MISSION_SHOP_TIP_REQUEST_SYSTEM = "OPEN_MISSION_SHOP_TIP_DIALOG_CALL",

  /**游戏助手 */
  OPEN_GAME_HELPER_DIALOG = "OPEN_GAME_HELPER_DIALOG",
  CLOSE_GAME_HELPER_DIALOG = "CLOSE_GAME_HELPER_DIALOG",
  OPEN_GAME_HELPER_WIKI_DIALOG = "OPEN_GAME_HELPER_WIKI_DIALOG",
  CLOSE_GAME_HELPER_WIKI_DIALOG = "CLOSE_GAME_HELPER_WIKI_DIALOG",
  OPEN_GAME_HELPER_HELPER_DIALOG = "OPEN_GAME_HELPER_HELPER_DIALOG",
  CLOSE_GAME_HELPER_HELPER_DIALOG = "CLOSE_GAME_HELPER_HELPER_DIALOG",
  /**擂台竞技 */

  OPEN_MATCH_DIALOG = "OPEN_MATCH_DIALOG",

  OPEN_MATCH_LINEUP_DIALOG = "OPEN_MATCH_LINEUP_DIALOG",
  OPEN_GAMBLE_DIALOG = "OPEN_GAMBLE_DIALOG",
  OPEN_INFO_DIALOG = "OPEN_INFO_DIALOG",
  OPEN_MATCH_RANK_DIALOG = "OPEN_MATCH_RANK_DIALOG",
  OPEN_MATCH_LAST_FINALS_DIALOG = "OPEN_MATCH_LAST_FINALS_DIALOG",

  ARENA_MATCH_INFO = "ARENA_MATCH_INFO",
  SQUAD_LINEUP_GET = "SQUAD_LINEUP_GET",
  ARENA_MATCH_FINALS = "ARENA_MATCH_FINALS",
  ARENA_MATCH_GUESS_INFO = "ARENA_MATCH_GUESS_INFO",
  ARENA_MATCH_FINALS_INFO = "ARENA_MATCH_FINALS_INFO",
  ARENA_MATCH_LAST_FINALS = "ARENA_MATCH_LAST_FINALS",
  UPDATE_SQUAD_LINEUP = "UPDATE_SQUAD_LINEUP",
  ARENA_MATCH_TOP_INFO = "ARENA_MATCH_TOP_INFO",
  MATCH_TEAM_ITEM_DRAG_START = "MATCH_TEAM_ITEM_DRAG_START",
  MATCH_TEAM_ITEM_DRAG_END = "MATCH_TEAM_ITEM_DRAG_END",
  ARENA_MATCH_FETCH = "ARENA_MATCH_FETCH",
  MATCH_SIMPLE_INFO = "MATCH_SIMPLE_INFO",

  //-------天下试炼  start------------
  OPEN_TRAINING_DIALOG = "OPEN_TRAINING_DIALOG",
  CLOSE_TRAINING_DIALOG = "CLOSE_TRAINING_DIALOG",
  OPEN_TRAINING_NATION_DIALOG = "OPEN_TRAINING_NATION_DIALOG",
  CLOSE_TRAINING_NATION_DIALOG = "CLOSE_TRAINING_NATION_DIALOG",
  OPEN_TRAINING_FIGHT_DIALOG = "OPEN_TRAINING_FIGHT_DIALOG",
  CLOSE_TRAINING_FIGHT_DIALOG = "CLOSE_TRAINING_FIGHT_DIALOG",
  OPEN_TRAINING_TEAM_DIALOG = "OPEN_TRAINING_TEAM_DIALOG",
  CLOSE_TRAINING_TEAM_DIALOG = "CLOSE_TRAINING_TEAM_DIALOG",
  OPEN_TRAINING_REWARD_DIALOG = "OPEN_TRAINING_REWARD_DIALOG",
  CLOSE_TRAINING_REWARD_DIALOG = "CLOSE_TRAINING_REWARD_DIALOG",
  OPEN_TRAINING_REWARD_TIPS_DIALOG = "OPEN_TRAINING_REWARD_TIPS_DIALOG",
  CLOSE_TRAINING_REWARD_TIPS_DIALOG = "CLOSE_TRAINING_REWARD_TIPS_DIALOG",

  OPEN_EMPOWERMENT_DIALOG = "OPEN_EMPOWERMENT_DIALOG",
  CLOSE_EMPOWERMENT_DIALOG = "CLOSE_EMPOWERMENT_DIALOG",


  TRAINING_INFO = "TRAINING_INFO",
  TRAINING_OPERATE_UPDATE = "TRAINING_OPERATE_UPDATE",

  TRAINING_TEAM_ITEM_DRAG_START = "TRAINING_TEAM_ITEM_DRAG_START",
  TRAINING_TEAM_ITEM_DRAG_END = "TRAINING_TEAM_ITEM_DRAG_END",
  TRAINING_COPY_FIGHT_FINISH = "TRAINING_COPY_FIGHT_FINISH",

  TRAINING_FIGHT_FINISH_INFO = "TRAINING_FIGHT_FINISH_INFO",
  //------ 天下试炼 end -----

  SQUAD_LINEUP_SET_TOC = "SQUAD_LINEUP_SET_TOC",
  /**托管失败 */
  WAR_AUTO_FAILED = "WAR_AUTO_FAILED",

  //-----------特权直购-------------------
  OPEN_FOREVER_PRIVILEGE_DIALOG = "OPEN_FOREVER_PRIVILEGE_DIALOG",
  CLOSE_FOREVER_PRIVILEGE_DIALOG = "CLOSE_FOREVER_PRIVILEGE_DIALOG",
  OPEN_FOREVER_PRIVILEGE_INFO_DIALOG = "OPEN_FOREVER_PRIVILEGE_INFO_DIALOG",
  CLOSE_FOREVER_PRIVILEGE_INFO_DIALOG = "CLOSE_FOREVER_PRIVILEGE_INFO_DIALOG",
  OPEN_FOREVER_PRIVILEGE_REWARD_DIALOG = "OPEN_FOREVER_PRIVILEGE_REWARD_DIALOG",
  CLOSE_FOREVER_PRIVILEGE_REWARD_DIALOG = "CLOSE_FOREVER_PRIVILEGE_REWARD_DIALOG",
  OPEN_FOREVER_PRIVILEGE_HERO_DIALOG = "OPEN_FOREVER_PRIVILEGE_HERO_DIALOG",
  CLOSE_FOREVER_PRIVILEGE_HERO_DIALOG = "CLOSE_FOREVER_PRIVILEGE_HERO_DIALOG",
  UPDATE_FOREVER_PRIVILEGE_INFO = "UPDATE_FOREVER_PRIVILEGE_INFO",
  UPDATE_FOREVER_PRIVILEGE_HERO_INFO = "UPDATE_FOREVER_PRIVILEGE_HERO_INFO",
  UPDATE_FOREVER_PRIVILEGE_ONE = "UPDATE_FOREVER_PRIVILEGE_ONE",

  CLOSE_FOREVER_PRIVILEGE_INFO = "CLOSE_FOREVER_PRIVILEGE_INFO",
  //-----------特权直购-------------------


  OPEN_MENUFLY_ICON = "OPEN_MENUFLY_ICON",
  /**检测菜单飞行位置 */
  CHECK_MENU_FLY_POS = "CHECK_MENU_FLY_POS",
  CHECK_TAB_FLY_POS = "CHECK_TAB_FLY_POS",
  /**接收飞行位置 */
  RECEIPT_FLY_TAR_POS = "RECEIPT_FLY_TAR_POS",

  MENU_CHAT_VIEW_ZORDER = "MENU_CHAT_VIEW_ZORDER",
  ARENA_SHOW_3D = "ARENA_SHOW_3D",


  //-------------招财猫------------------
  CLOSE_FORTUNE_CAT_DIALOG = "CLOSE_FORTUNE_CAT_DIALOG",
  OPEN_FORTUNE_CAT_DIALOG = "OPEN_FORTUNE_CAT_DIALOG",
  CAT_ACTIVITY_INFO_TOC = "CAT_ACTIVITY_INFO_TOC",
  OPEN_FORTUNE_CAT_LOOK_DIALOG = "OPEN_FORTUNE_CAT_LOOK_DIALOG",
  CLOSE_FORTUNE_CAT_LOOK_DIALOG = "CLOSE_FORTUNE_CAT_LOOK_DIALOG",
  OPEN_TE_HUI_RETURN = "OPEN_TE_HUI_RETURN",

  CAT_ACTIVITY_LOGS_UPDATE = "CAT_ACTIVITY_LOGS_UPDATE",
  //-------------招财猫------------------

  //-------------大师光环-----------------
  OPEN_MASTER_HALO_DIALOG = "OPEN_MASTER_HALO_DIALOG",
  //-------------大师光环-----------------


  //-------------新春庆典------------------
  OPEN_SPRING_SHOP_DIALOG = "OPEN_SPRING_SHOP_DIALOG",
  CLOSE_SPRING_SHOP_DIALOG = "CLOSE_SPRING_SHOP_DIALOG",
  // OPEN_SPRING_SHOP_BUY_DIALOG  = "OPEN_SPRING_SHOP_BUY_DIALOG",
  // CLOSE_SPRING_SHOP_BUY_DIALOG  = "CLOSE_SPRING_SHOP_BUY_DIALOG",

  OPEN_SPRING_SHOP_VIEW_WITH_INDEX = "OPEN_SPRING_SHOP_VIEW_WITH_INDEX",
  //SPRING_SHOP_SIGN_INFO_UPDATE  = "SPRING_SHOP_SIGN_INFO_UPDATE",
  SPRING_SHOP_MISSION_INFO_UPDATE = "SPRING_SHOP_MISSION_INFO_UPDATE",
  SPRING_SHOP_SHOP_INFO_UPDATE = "SPRING_SHOP_SHOP_INFO_UPDATE",
  SPRING_SHOP_MARKET_INFO_UPDATE = "SPRING_SHOP_MARKET_INFO_UPDATE",
  SPRING_EXAM_INFO = "SPRING_EXAM_INFO",
  SPRING_EXAM_FETCH_INFO = "SPRING_EXAM_FETCH_INFO",


  //-------------新春庆典------------------

  //-------------vip客服-------------------
  OPEN_VIP_KEFU_DIALOG = "OPEN_VIP_KEFU_DIALOG",
  CLOSE_VIP_KEFU_DIALOG = "CLOSE_VIP_KEFU_DIALOG",
  VIP_KEFU_UPDATE_INFO = "VIP_KEFU_UPDATE_INFO",
  //-------------vip客服-------------------

  //------------排名活动-------------------
  OPEN_TOPS_ACTIVITY_DIALOG = "OPEN_TOPS_ACTIVITY_DIALOG",
  CLOSE_TOPS_ACTIVITY_DIALOG = "CLOSE_TOPS_ACTIVITY_DIALOG",
  TOPS_ACTIVITY_INFO_TOC = "TOPS_ACTIVITY_INFO_TOC",
  TOPS_ACTIVITY_UPDATE_TOC = "TOPS_ACTIVITY_UPDATE_TOC",

  //------------排名活动-------------------

  //-----------三军备战---------------
  OPEN_PREPARE_DIALOG = "OPEN_PREPARE_DIALOG",
  OPEN_PREPARE_LOTTERY_SELECT_DIALOG = "OPEN_PREPARE_LOTTERY_SELECT_DIALOG",
  CLOSE_PREPARE_LOTTERY_SELECT_DIALOG = "CLOSE_PREPARE_LOTTERY_SELECT_DIALOG",
  OPEN_PREPARE_SIGN_TIP_DIALOG = "OPEN_PREPARE_SIGN_TIP_DIALOG",
  OPEN_PREPARE_FETE_GLOBAL_DIALOG = "OPEN_PREPARE_FETE_GLOBAL_DIALOG",
  OPEN_PREPARE_FETE_USE_DIALOG = "OPEN_PREPARE_FETE_USE_DIALOG",
  OPEN_PREPARE_SELF_REWARD_DIALOG = "OPEN_PREPARE_SELF_REWARD_DIALOG",
  UPDATE_PREPARE_SGIN_INFO = "UPDATE_PREPARE_SGIN_INFO",
  UPDATA_PREPARE_MISSION_INFO = "UPDATA_PREPARE_MISSION_INFO",
  UPDATE_PREPARE_SACRIFICE_INFO = "UPDATE_PREPARE_SACRIFICE_INFO",
  UPDATE_PREPARE_SELF_REWARD_INFO = "UPDATE_PREPARE_SELF_REWARD_INFO",
  UPDATE_PREPARE_GLOBAL_REWARD_INFO = "UPDATE_PREPARE_GLOBAL_REWARD_INFO",
  //-----------三军备战---------------

  //-----------限时神裔英雄---------------
  OPEN_HERO_ACT_DIALOG = "OPEN_HERO_ACT_DIALOG",
  CLOSE_HERO_ACT_DIALOG = "CLOSE_HERO_ACT_DIALOG",
  OPEN_HERO_ACT_PASS_LIST_DIALOG = "OPEN_HERO_ACT_PASS_LIST_DIALOG",
  CLOSE_HERO_ACT_PASS_LIST_DIALOG = "CLOSE_HERO_ACT_PASS_LIST_DIALOG",
  OPEN_HERO_ACT_PASS_DIALOG = "OPEN_HERO_ACT_PASS_DIALOG",
  CLOSE_HERO_ACT_PASS_DIALOG = "CLOSE_HERO_ACT_PASS_DIALOG",
  OPEN_HERO_ACT_TIPS_AD_DIALOG = "OPEN_HERO_ACT_TIPS_AD_DIALOG",
  CLOSE_HERO_ACT_TIPS_AD_DIALOG = "CLOSE_HERO_ACT_TIPS_AD_DIALOG",
  OPEN_HERO_ACT_LINE_UP_DIALOG = "OPEN_HERO_ACT_LINE_UP_DIALOG",
  CLOSE_HERO_ACT_LINE_UP_DIALOG = "CLOSE_HERO_ACT_LINE_UP_DIALOG",
  OPEN_HERO_ACT_LOTTERY_DIALOG = "OPEN_HERO_ACT_LOTTERY_DIALOG",
  OPEN_HERO_ACT_LOTTERY_SELECT_GOODS_DIALOG = "OPEN_HERO_ACT_LOTTERY_SELECT_GOODS_DIALOG",
  OPEN_HERO_ACT_HERO_SOUL_SETTING_DIALOG = "OPEN_HERO_ACT_HERO_SOUL_SETTING_DIALOG",
  OPEN_HERO_ACT_HERO_SOUL_SELECT_DIALOG = "OPEN_HERO_ACT_HERO_SOUL_SELECT_DIALOG",
  OPEN_HERO_ACT_FIGHT_SUCC_DIALOG = "OPEN_HERO_ACT_FIGHT_SUCC_DIALOG",
  OPEN_HERO_ACT_PASS_REWARD_DIALOG = "OPEN_HERO_ACT_PASS_REWARD_DIALOG",
  CLOSE_HERO_ACT_PASS_REWARD_DIALOG = "CLOSE_HERO_ACT_PASS_REWARD_DIALOG",
  OPEN_HERO_ACT_BOX_DIALOG = "OPEN_HERO_ACT_BOX_DIALOG",
  OPEN_HERO_ACT_SHOP_DIALOG = "OPEN_HERO_ACT_SHOP_DIALOG",
  OPEN_HERO_ACT_LOTTERY_RANK_REWARD_DIALOG = "OPEN_HERO_ACT_LOTTERY_RANK_REWARD_DIALOG",
  CHECK_HERO_ACT_TIPS_AD = "CHECK_HERO_ACT_TIPS_AD",
  ON_SELECT_HERO_ACT_LINE_UP_ITEM = "ON_SELECT_HERO_ACT_LINE_UP_ITEM",
  ON_CHANGE_HERO_ACT_LINE_UP_POS = "ON_CHANGE_HERO_ACT_LINE_UP_POS",
  UPDATE_HERO_ACT_PASS_INFO = "UPDATE_HERO_ACT_PASS_INFO",
  UPDATE_HERO_ACT_BOX_INFO = "UPDATE_HERO_ACT_BOX_INFO",
  FIGHT_HERO_ACT_PASS_FINISH = "FIGHT_HERO_ACT_PASS_FINISH",
  //限时神裔英雄英雄战魂技能更换、坐骑技能更换
  UPDATE_HERO_ACT_LINE_UP_VO = "UPDATE_HERO_ACT_LINE_UP_VO",
  //-----------限时神裔英雄---------------

  //--------英雄助阵----------
  OPEN_HERO_CHEER_DIALOG = "OPEN_HERO_CHEER_DIALOG",
  UPDATE_HERO_CHEER_DIALOG = "UPDATE_HERO_CHEER_DIALOG",
  OPEN_HERO_CHEER_UPGRADE_DIALOG = "OPEN_HERO_CHEER_UPGRADE_DIALOG",
  CLOSE_HERO_CHEER_UPGRADE_DIALOG = "CLOSE_HERO_CHEER_UPGRADE_DIALOG",
  OPEN_HERO_CHEER_UNLOCK_DIALOG = "OPEN_HERO_CHEER_UNLOCK_DIALOG",
  CLOSE_HERO_CHEER_UNLOCK_DIALOG = "CLOSE_HERO_CHEER_UNLOCK_DIALOG",
  OPEN_HERO_CHEER_PREVIEW_DIALOG = "OPEN_HERO_CHEER_PREVIEW_DIALOG",
  OPEN_HERO_CHEER_LINEUP_DIALOG = "OPEN_HERO_CHEER_LINEUP_DIALOG",
  UPDATE_HERO_CHEER_LINEUP_DIALOG = "UPDATE_HERO_CHEER_LINEUP_DIALOG",
  UPDATE_HERO_CHEER_LINE_UP_ITEM = "UPDATE_HERO_CHEER_LINE_UP_ITEM",
  OPEN_HERO_CHEER_UPGRADE_TIP_DIALOG = "OPEN_HERO_CHEER_UPGRADE_TIP_DIALOG",
  UPDATE_HERO_CHEER_UNLOCK_ITEM = "UPDATE_HERO_CHEER_UNLOCK_ITEM",
  OPEN_HERO_CHEER_LINE_UP_INFO_DIALOG = "OPEN_HERO_CHEER_LINE_UP_INFO_DIALOG",
  //--------英雄助阵----------

  //-----------星图------------------
  OPEN_XING_TU_TEST_EDITOR_DIALOG = "OPEN_XING_TU_TEST_EDITOR_DIALOG",
  OPEN_XING_TU_DIALOG = "OPEN_XING_TU_DIALOG",
  CLOSE_XING_TU_DIALOG = "CLOSE_XING_TU_DIALOG",
  OPEN_XING_TU_DESC_DIALOG = "OPEN_XING_TU_DESC_DIALOG",
  OPEN_XING_TU_POINT_DESC_DIALOG = "OPEN_XING_TU_POINT_DESC_DIALOG",
  CLOSE_XING_TU_POINT_DESC_DIALOG = "CLOSE_XING_TU_POINT_DESC_DIALOG",
  UPDATE_XING_TU_VIEW = "UPDATE_XING_TU_VIEW",
  ON_XING_TU_UPGRADE = "ON_XING_TU_UPGRADE",

  //-----------星图------------------

  GUA_JI_STOP = "GUA_JI_STOP",
  TOPS_ACTIVITY_ITEM_CLICK = "TOPS_ACTIVITY_ITEM_CLICK",
  OPEN_QQ_DIALOG = "OPEN_QQ_DIALOG",
  ClOSE_QQ_DIALOG = "ClOSE_QQ_DIALOG",

  //----------------------巡城好礼--------------------------
  OPEN_CITY_ACT_DIALOG = "OPEN_CITY_ACT_DIALOG",
  CLOSE_CITY_ACT_DIALOG = "CLOSE_CITY_ACT_DIALOG",
  OPEN_CITY_ACT_ANSWER_DIALOG = "OPEN_CITY_ACT_ANSWER_DIALOG",
  CLOSE_CITY_ACT_ANSWER_DIALOG = "CLOSE_CITY_ACT_ANSWER_DIALOG",
  OPEN_CITY_ACT_REWARD_DIALOG = "OPEN_CITY_ACT_REWARD_DIALOG",
  CLOSE_CITY_ACT_REWARD_DIALOG = "CLOSE_CITY_ACT_REWARD_DIALOG",

  // OPEN_CITY_ACT_BUY_DIALOG  = "OPEN_CITY_ACT_BUY_DIALOG",
  // CLOSE_CITY_ACT_BUY_DIALOG  = "CLOSE_CITY_ACT_BUY_DIALOG",

  CITY_ACT_INFO_UPDATE = "CITY_ACT_INFO_UPDATE",
  CITY_ACT_GAME_INFO = "CITY_ACT_GAME_INFO",
  CITY_ACT_GAME_UPDATE = "CITY_ACT_GAME_UPDATE",
  CITY_ACT_GAME_SCORE_UPDATE = "CITY_ACT_GAME_SCORE_UPDATE",

  CITY_ACT_DIALOG_CHOOSE = "CITY_ACT_DIALOG_CHOOSE",

  CITY_ACT_GAME_CHOOSE_CLOSE = "CITY_ACT_GAME_CHOOSE_CLOSE",

  // CITY_ACT_SHOP_INFO  = "CITY_ACT_SHOP_INFO",
  CITY_ACT_SHOP_UPDATE = "CITY_ACT_SHOP_UPDATE",

  CITY_ACT_GAME_CHOOSE = "CITY_ACT_GAME_CHOOSE",
  //----------------------巡城好礼--------------------------

  //------------------聚宝盆--------------------------
  OPEN_CORNUCOPIA_DIALOG = "OPEN_CORNUCOPIA_DIALOG",
  OPEN_CORNUCOPIA_CHOOSE_DIALOG = "OPEN_CORNUCOPIA_CHOOSE_DIALOG",
  UPDATE_CORNUCAPIA_INFO = "UPDATE_CORNUCAPIA_INFO",
  UPDATE_CORNUCAPIA_CHOOSE_INFO = "UPDATE_CORNUCAPIA_CHOOSE_INFO",
  UPDATE_CORNUCAPIA_CHOOSE_SELECT = "UPDATE_CORNUCAPIA_CHOOSE_SELECT",
  //------------------聚宝盆--------------------------

  //--------------------vip任务-----------------------
  OPEN_VIP_MISSION_DIALOG = "OPEN_VIP_MISSION_DIALOG",
  CLOSE_VIP_MISSION_DIALOG = "CLOSE_VIP_MISSION_DIALOG",
  UPDATE_VIP_MISSION_LIST = "UPDATE_VIP_MISSION_LIST",
  ON_COMPLETE_VIP_MISSION_ITEM = "ON_COMPLETE_VIP_MISSION_ITEM",

  //--------------------vip任务-----------------------

  /**更新充值相关内容显示 */
  UPDATE_SHOW_RECHARGE_STATE = "UPDATE_SHOW_RECHARGE_STATE",

  //------------------QQ游戏--------------------------
  OPEN_QQ_BLUE_DIAMON_DIALOG = "OPEN_QQ_BLUE_DIAMON_DIALOG",
  CLOSE_QQ_BLUE_DIAMON_DIALOG = "CLOSE_QQ_BLUE_DIAMON_DIALOG",

  OPEN_QQ_GAME_DIALOG = "OPEN_QQ_GAME_DIALOG",
  CLOSE_QQ_GAME_DIALOG = "CLOSE_QQ_GAME_DIALOG",

  OPEN_QQ_DA_WAN_KA_DIALOG = "OPEN_QQ_DA_WAN_KA_DIALOG",
  UPDATE_QQ_DA_WAN_KA_INFO = "UPDATE_QQ_DA_WAN_KA_INFO",
  OPEN_QQ_DAILY_DIALOG = "OPEN_QQ_DAILY_DIALOG",
  CLOSE_QQ_DAILY_DIALOG = "CLOSE_QQ_DAILY_DIALOG",

  QQ_VIP_UPDATE = "QQ_VIP_UPDATE",
  QQ_BLUE_CHOOSE = "QQ_BLUE_CHOOSE",
  //------------------QQ游戏--------------------------


  //------------------列传 start--------------------------
  OPEN_LIE_ZHUAN_DIALOG = "OPEN_LIE_ZHUAN_DIALOG",
  CLOSE_LIE_ZHUAN_DIALOG = "CLOSE_LIE_ZHUAN_DIALOG",

  OPEN_LIE_ZHUAN_ATTR_TIPS_DIALOG = "OPEN_LIE_ZHUAN_ATTR_TIPS_DIALOG",
  CLOSE_LIE_ZHUAN_ATTR_TIPS_DIALOG = "CLOSE_LIE_ZHUAN_ATTR_TIPS_DIALOG",

  OPEN_LIE_ZHUAN_UPDATE_DIALOG = "OPEN_LIE_ZHUAN_UPDATE_DIALOG",
  CLOSE_LIE_ZHUAN_UPDATE_DIALOG = "CLOSE_LIE_ZHUAN_UPDATE_DIALOG",

  OPEN_LIE_ZHUAN_SKILL_SHOW_DIALOG = "OPEN_LIE_ZHUAN_SKILL_SHOW_DIALOG",
  CLOSE_LIE_ZHUAN_SKILL_SHOW_DIALOG = "CLOSE_LIE_ZHUAN_SKILL_SHOW_DIALOG",

  OPEN_LIE_ZHUAN_SKILL_UPDATE_DIALOG = "OPEN_LIE_ZHUAN_SKILL_UPDATE_DIALOG",
  CLOSE_LIE_ZHUAN_SKILL_UPDATE_DIALOG = "CLOSE_LIE_ZHUAN_SKILL_UPDATE_DIALOG",

  OPEN_LIE_ZHUAN_SKILL_SUCCESS_DIALOG = "OPEN_LIE_ZHUAN_SKILL_SUCCESS_DIALOG",
  CLOSE_LIE_ZHUAN_SKILL_SUCCESS_DIALOG = "CLOSE_LIE_ZHUAN_SKILL_SUCCESS_DIALOG",

  OPEN_LIE_ZHUAN_ROLE_SUCCESS_DIALOG = "OPEN_LIE_ZHUAN_ROLE_SUCCESS_DIALOG",
  CLOSE_LIE_ZHUAN_ROLE_SUCCESS_DIALOG = "CLOSE_LIE_ZHUAN_ROLE_SUCCESS_DIALOG",

  OPEN_LIE_ZHUAN_ROLE_UPDATE_DIALOG = "OPEN_LIE_ZHUAN_ROLE_UPDATE_DIALOG",
  CLOSE_LIE_ZHUAN_ROLE_UPDATE_DIALOG = "CLOSE_LIE_ZHUAN_ROLE_UPDATE_DIALOG",

  LIE_ZHUAN_INFO = "LIE_ZHUAN_INFO",
  LIE_ZHUAN_UPDATE = "LIE_ZHUAN_UPDATE",


  //------------------列传 end--------------------------

  OPEN_PAYMENT_VIP_LEVEL_DIALOG = "OPEN_PAYMENT_VIP_LEVEL_DIALOG",
  CLOSE_PAYMENT_VIP_LEVEL_DIALOG = "CLOSE_PAYMENT_VIP_LEVEL_DIALOG",
  UPDATE_SVIP_PAY_INFO_TOC = "UPDATE_SVIP_PAY_INFO_TOC",


  //------------------- 主角 -----------------------------------//

  OPEN_MAIN_ROLE_DIALOG = "OPEN_MAIN_ROLE_DIALOG",
  COLSE_MAIN_ROLE_DIALOG = "COLSE_MAIN_ROLE_DIALOG",
  OPEN_ROLE_EUIQP_BAG_DIALOG = "OPEN_ROLE_EUIQP_BAG_DIALOG",
  COLSE_ROLE_EUIQP_BAG_DIALOG = "COLSE_ROLE_EUIQP_BAG_DIALOG",
  OPEN_MASTER_EQUIP_DIALOG = "OPEN_MASTER_EQUIP_DIALOG",
  COLSE_MASTER_EQUIP_DIALOG = "COLSE_MASTER_EQUIP_DIALOG",
  OPEN_MASTER_EQUIP_REFINE_SUIT_DIALOG = "OPEN_MASTER_EQUIP_REFINE_SUIT_DIALOG",
  OPEN_MASTER_EQUIP_REFINE_SUIT_SUCC_DIALOG = "OPEN_MASTER_EQUIP_REFINE_SUIT_SUCC_DIALOG",
  UPDATE_ROLE_EQUIP_INFO = "UPDATE_ROLE_EQUIP_INFO",
  UPDATE_ROLE_EQUIP_TIP_INFO = "UPDATE_ROLE_EQUIP_TIP_INFO",
  UPDATE_ROLE_EQUIP_COMPOSE_INFO = "UPDATE_ROLE_EQUIP_COMPOSE_INFO",
  OPEN_MASTER_SKILL_ORDER_DIALOG = "OPEN_MASTER_SKILL_ORDER_DIALOG",
  OPEN_MASTER_SKILL_SETING_DIALOG = "OPEN_MASTER_SKILL_SETING_DIALOG",
  OPEN_ROLE_EQUIP_COMPOSE_DIALOG = "OPEN_ROLE_EQUIP_COMPOSE_DIALOG",
  CLOSE_ROLE_EQUIP_COMPOSE_DIALOG = "CLOSE_ROLE_EQUIP_COMPOSE_DIALOG",
  OPEN_ROLE_EQUIP_AUTO_COMPOSE_DIALOG = "OPEN_ROLE_EQUIP_AUTO_COMPOSE_DIALOG",
  OPEN_ROLE_SPECIAL_EQUIP_DIALOG = "OPEN_ROLE_SPECIAL_EQUIP_DIALOG",
  OPEN_ROLE_EQUIP_CULTIVATE_DIALOG = "OPEN_ROLE_EQUIP_CULTIVATE_DIALOG",
  OPEN_ROLE_SPECIAL_TIP_DIALOG = "OPEN_ROLE_SPECIAL_TIP_DIALOG",
  CLOSE_ROLE_SPECIAL_TIP_DIALOG = "CLOSE_ROLE_SPECIAL_TIP_DIALOG",
  OPEN_MASTER_WEAPON_ACTIVITE_DIALOG = "OPEN_MASTER_WEAPON_ACTIVITE_DIALOG",
  OPEN_MASTER_WEAPON_FORGE_DIALOG = "OPEN_MASTER_WEAPON_FORGE_DIALOG",
  OPEN_MASTER_WEAPON_SKILL_SHOW_DIALOG = "OPEN_MASTER_WEAPON_SKILL_SHOW_DIALOG",
  OPEN_MASTER_WEAPON_TAL_DIALOG = "OPEN_MASTER_WEAPON_TAL_DIALOG",
  OPEN_MASTER_WEAPON_LIANXIE_DIALOG = "OPEN_MASTER_WEAPON_LIANXIE_DIALOG",
  OPEN_MASTER_WEAPON_STAGE_ACTIVITE_DIALOG = "OPEN_MASTER_WEAPON_STAGE_ACTIVITE_DIALOG",
  OPEN_MASTER_WEAPON_LIANXIE_ACTIVITE_DIALOG = "OPEN_MASTER_WEAPON_LIANXIE_ACTIVITE_DIALOG",
  OPEN_MASTER_WEAPON_TIPS_INFO_DIALOG = "OPEN_MASTER_WEAPON_TIPS_INFO_DIALOG",
  WEAPON_SKILL_ITEM_DRAG_START = "WEAPON_SKILL_ITEM_DRAG_START",
  WEAPON_SKILL_ITEM_DRAG_END = "WEAPON_SKILL_ITEM_DRAG_END",
  //主角装备强化成功
  UPDATE_ROLE_STRENGTH_SUCC = "UPDATE_ROLE_STRENGTH_SUCC",
  //主角装备精炼成功
  UPDATE_ROLE_REFINE_SUCC = "UPDATE_ROLE_REFINE_SUCC",

  SELECT_WEAPON_FORGE_ITEM = "SELECT_WEAPON_FORGE_ITEM",

  SWEAPON_INFO_TOC = "SWEAPON_INFO_TOC",

  ROLE_EQUIP_UP_SKILL = "ROLE_EQUIP_UP_SKILL",


  //--------------------美人
  OPEN_MASTER_BEAUTY_DIALOG = "OPEN_MASTER_BEAUTY_DIALOG",
  OPEN_MASTER_BEAUTY_TONGXIN_DIALOG = "OPEN_MASTER_BEAUTY_TONGXIN_DIALOG",
  OPEN_MASTER_BEAUTY_SETTING_DIALOG = "OPEN_MASTER_BEAUTY_SETTING_DIALOG",
  OPEN_MASTER_BEAUTY_FORGE_TALENT_DIALOG = "OPEN_MASTER_BEAUTY_FORGE_TALENT_DIALOG",
  //------------------- 主角 -----------------------------------//


  // 公会天星 面板---------------------
  OPEN_FAMILY_ACTIVITY_DIALOG = "OPEN_FAMILY_ACTIVITY_DIALOG",
  CLOSE_FAMILY_ACTIVITY_DIALOG = "CLOSE_FAMILY_ACTIVITY_DIALOG",
  OPEN_FAMILY_ACTIVITY_RANK_DIALOG = "OPEN_FAMILY_ACTIVITY_RANK_DIALOG",
  CLOSE_FAMILY_ACTIVITY_RANK_DIALOG = "CLOSE_FAMILY_ACTIVITY_RANK_DIALOG",
  OPEN_FAMILY_ACTIVITY_PREVIEW_DIALOG = "OPEN_FAMILY_ACTIVITY_PREVIEW_DIALOG",
  CLOSE_FAMILY_ACTIVITY_PREVIEW_DIALOG = "CLOSE_FAMILY_ACTIVITY_PREVIEW_DIALOG",
  UPDATE_FAMILY_ACTIVITY_INFO = "UPDATE_FAMILY_ACTIVITY_INFO",

  SWEAPON_OPERATE_TOC = "SWEAPON_OPERATE_TOC",
  SWEAPON_SET_LINEUP = "SWEAPON_SET_LINEUP",

  SWEAPON_INFO_UPDATE = "SWEAPON_INFO_UPDATE",

  // 公会天星 面板---------------------

  //皇城预告------------------------

  OPEN_KING_WAR_ABDIALOG = "OPEN_KING_WAR_ABDIALOG",
  CLOSE_KING_WAR_ABDIALOG = "CLOSE_KING_WAR_ABDIALOG",

  OPEN_KING_WAR_TIPS_DIALOG = "OPEN_KING_WAR_TIPS_DIALOG",
  OPEN_KING_WAR_JIFEN_RANKDIALOG = "OPEN_KING_WAR_JIFEN_RANKDIALOG",
  OPEN_KING_WAR_SCORE_RANK_DIALOG = "OPEN_KING_WAR_SCORE_RANK_DIALOG",
  OPEN_KING_WAR_CITY_DIALOG = "OPEN_KING_WAR_CITY_DIALOG",
  MOVE_KING_WAR_CITY_EVENT = "MOVE_KING_WAR_CITY_EVENT",
  CLOSE_KING_WAR_CITY_DIALOG = "CLOSE_KING_WAR_CITY_DIALOG",
  OPEN_KING_WAR_SCORE_AWARD_DIALOG = "OPEN_KING_WAR_SCORE_AWARD_DIALOG",
  CLOSE_KING_WAR_SCORE_AWARD_DIALOG = "CLOSE_KING_WAR_SCORE_AWARD_DIALOG",
  OPEN_KING_WAR_RANK_DIALOG = "OPEN_KING_WAR_RANK_DIALOG",

  M_KING_MATCH_INFO_TOC = "M_KING_MATCH_INFO_TOC",
  KING_MATCH_STATUS = "KING_MATCH_STATUS",

  KING_WAR_DOOR_INFO_VIEW_CLICK = "KING_WAR_DOOR_INFO_VIEW_CLICK",

  OPEN_KING_WAR_END_DIALOG = "OPEN_KING_WAR_END_DIALOG",
  OPEN_KING_WAR_DOOR_DIALOG = "OPEN_KING_WAR_DOOR_DIALOG",
  OPEN_KING_WAR_DOOR_HAVE_DIALOG = "OPEN_KING_WAR_DOOR_HAVE_DIALOG",
  OPEN_KING_WAR_SCORE_NEXT_AWARD_DIALOG = "OPEN_KING_WAR_SCORE_NEXT_AWARD_DIALOG",
  SHOW_GOODS_LIST_TIPS = "SHOW_GOODS_LIST_TIPS",
  OPEN_KIND_WAR_FINAL_DIALOG = "OPEN_KIND_WAR_FINAL_DIALOG",
  OPEN_KING_WAR_PREPARE_DIALOG = "OPEN_KING_WAR_PREPARE_DIALOG",


  //皇城预告------------------------

  //-----皇城----------------------------
  OPEN_FAMILY_PALACE_DIALOG = "OPEN_FAMILY_PALACE_DIALOG",
  CLOSE_FAMILY_PALACE_DIALOG = "CLOSE_FAMILY_PALACE_DIALOG",

  OPEN_FAMILY_PALACE_WORSHIP_DIALOG = "OPEN_FAMILY_PALACE_WORSHIP_DIALOG",
  CLOSE_FAMILY_PALACE_WORSHIP_DIALOG = "CLOSE_FAMILY_PALACE_WORSHIP_DIALOG",

  OPEN_FAMILY_PALACE_MONEY_DIALOG = "OPEN_FAMILY_PALACE_MONEY_DIALOG",
  CLOSE_FAMILY_PALACE_MONEY_DIALOG = "CLOSE_FAMILY_PALACE_MONEY_DIALOG",

  OPEN_FAMILY_PALACE_MONEY_INFO_DIALOG = "OPEN_FAMILY_PALACE_MONEY_INFO_DIALOG",
  CLOSE_FAMILY_PALACE_MONEY_INFO_DIALOG = "CLOSE_FAMILY_PALACE_MONEY_INFO_DIALOG",

  OPEN_FAMILY_PALACE_MONEY_REN_DIALOG = "OPEN_FAMILY_PALACE_MONEY_REN_DIALOG",
  CLOSE_FAMILY_PALACE_MONEY_REN_DIALOG = "CLOSE_FAMILY_PALACE_MONEY_REN_DIALOG",

  FAMILY_PALACE_UPDATE = "FAMILY_PALACE_UPDATE",

  FAMILY_PALACE_CHOOSE = "FAMILY_PALACE_CHOOSE",
  //-----皇城end------------------------

  //-----------英雄战力冲榜活动---------------
  OPEN_HERO_UP_ACT_DIALOG = "OPEN_HERO_UP_ACT_DIALOG",
  CLOSE_HERO_UP_ACT_DIALOG = "CLOSE_HERO_UP_ACT_DIALOG",
  OPEN_HERO_UP_ACT_SELECT_HERO_DIALOG = "OPEN_HERO_UP_ACT_SELECT_HERO_DIALOG",
  CLOSE_HERO_UP_ACT_SELECT_HERO_DIALOG = "CLOSE_HERO_UP_ACT_SELECT_HERO_DIALOG",
  ON_HERO_UP_ACT_SELECT_HERO = "ON_HERO_UP_ACT_SELECT_HERO",
  UPDATE_HERO_UP_ACT_VIEW = "UPDATE_HERO_UP_ACT_VIEW",
  UPDATE_HERO_UP_ACT_INFO = "UPDATE_HERO_UP_ACT_INFO",
  //-----------英雄战力冲榜活动---------------

  //-----------国战战力冲榜活动---------------
  OPEN_WAR_UP_ACT_DIALOG = "OPEN_WAR_UP_ACT_DIALOG",
  CLOSE_WAR_UP_ACT_DIALOG = "CLOSE_WAR_UP_ACT_DIALOG",
  OPEN_WAR_UP_ACT_SELECT_HERO_DIALOG = "OPEN_WAR_UP_ACT_SELECT_HERO_DIALOG",
  CLOSE_WAR_UP_ACT_SELECT_HERO_DIALOG = "CLOSE_WAR_UP_ACT_SELECT_HERO_DIALOG",
  ON_WAR_UP_ACT_SELECT_HERO = "ON_WAR_UP_ACT_SELECT_HERO",
  UPDATE_WAR_UP_ACT_VIEW = "UPDATE_WAR_UP_ACT_VIEW",
  UPDATE_WAR_UP_ACT_INFO = "UPDATE_WAR_UP_ACT_INFO",
  //-----------英雄战力冲榜活动---------------

  //-----英雄时装----------------------------
  OPEN_HERO_SKIN_DIALOG = "OPEN_HERO_SKIN_DIALOG",
  CLOSE_HERO_SKIN_DIALOG = "CLOSE_HERO_SKIN_DIALOG",

  OPEN_HERO_SKIN_TIP_DIALOG = "OPEN_HERO_SKIN_TIP_DIALOG",
  CLOSE_HERO_SKIN_TIP_DIALOG = "CLOSE_HERO_SKIN_TIP_DIALOG",
  OPEN_HERO_SKIN_DEBRIS_SELECT_DIALOG = "OPEN_HERO_SKIN_DEBRIS_SELECT_DIALOG",
  CLOSE_HERO_SKIN_DEBRIS_SELECT_DIALOG = "CLOSE_HERO_SKIN_DEBRIS_SELECT_DIALOG",
  OPEN_HERO_SKIN_UPDATE_DIALOG = "OPEN_HERO_SKIN_UPDATE_DIALOG",
  OPEN_HERO_SKIN_DIRECT_PURCHASE = "OPEN_HERO_SKIN_DIRECT_PURCHASE",
  CLOSE_HERO_SKIN_DIRECT_PURCHASE = "CLOSE_HERO_SKIN_DIRECT_PURCHASE",

  SELECT_HERO_SKIN_INDEX = "SELECT_HERO_SKIN_INDEX",

  OPEN_HERO_SKIN_ACTIVITE_DIALOG = "OPEN_HERO_SKIN_ACTIVITE_DIALOG",
  CLOSE_HERO_SKIN_ACTIVITE_DIALOG = "CLOSE_HERO_SKIN_ACTIVITE_DIALOG",

  HERO_SKIN_UPDATE = "HERO_SKIN_UPDATE",
  HERO_SKIN_UPGRADE = "HERO_SKIN_UPGRADE",
  HERO_SKIN_LOAD = "HERO_SKIN_LOAD",
  //-----英雄时装 end---------------------------

  //---------------祭祀 start---------------
  OPEN_FAMILY_PALACE_WIELD_DIALOG = "OPEN_FAMILY_PALACE_WIELD_DIALOG",
  CLOSE_FAMILY_PALACE_WIELD_DIALOG = "CLOSE_FAMILY_PALACE_WIELD_DIALOG",
  OPEN_FAMILY_PALACE_SPECIALITY_PRIVIEW_DIALOG = "OPEN_FAMILY_PALACE_SPECIALITY_PRIVIEW_DIALOG",
  CLOSE_FAMILY_PALACE_SPECIALITY_PRIVIEW_DIALOG = "CLOSE_FAMILY_PALACE_SPECIALITY_PRIVIEW_DIALOG",

  UPDATE_FAMILY_PALACE_WIELD = "UPDATE_FAMILY_PALACE_WIELD",
  //---------------祭祀 end---------------

  //----------------skeleton相关------------------------------------
  SKELETON_ADD_TO_CACHE = "SKELETON_ADD_TO_CACHE",
  SKELETON_UPDATE_URL = "SKELETON_UPDATE_URL",

  SKELETON_TEMPLET_COMPLETE = "SKELETON_TEMPLET_COMPLET",
  SKELETON_TEMPLET_ERROR = "SKELETON_TEMPLET_ERROR",

  SKELETON_BEFORE_ROLE_RES_LOAD = "SKELETON_BEFORE_ROLE_RES_LOAD",
  // SKELETON_TEMPLET_LATER_COMPLETE = "SKELETON_TEMPLET_LATER_COMPLETE",

  ON_W73D_ROTATION_CHANGE = "ON_W73D_ROTATION_CHANGE",

  ON_SK_TEMPLET_JY_ANI_FRAMES_COMPLETE = "ON_SK_TEMPLET_JY_ANI_FRAMES_COMPLETE",
  //----------------skeleton相关------------------------------------

  /***2d战斗相关 */

  OPEN_BATTLE_DIALOG = "OPEN_BATTLE_DIALOG",

  OPEN_BATTLE_EDITOR_DIALOG = "OPEN_BATTLE_EDITOR_DIALOG",
  BATTLE_EDITOR_ATTRBUTE_VALUE_CHANGE = "BATTLE_EDITOR_ATTRBUTE_VALUE_CHANGE",
  BATTLE_EDITOR_SKILL_EFFECT_ID_CHANGE = "BATTLE_EDITOR_SKILL_EFFECT_ID_CHANGE",
  BATTLE_EDITOR_COMMIT = "BATTLE_EDITOR_COMMIT",

  //角色受伤
  ROLE_HURT = "ROLE_HURT",
  ROLE_DEAD = "ROLE_DEAD",
  ROLE_DEAD_HIDE = "ROLE_DEAD_HIDE",
  ROLE_BASE_CHANGE = "ROLE_BASE_CHANGE",
  ROLE_STATE_CHANGE = "ROLE_STATE_CHANGE",
  /**初始战斗数据 */
  BEGAN_RUN_MAP = "BEGAN_RUN_MAP",
  /**初始化剧情数据 */
  BEGAN_RUN_STORY = "BEGAM_RUN_STORY",
  /**初始化挂机战斗数据 */
  BEGAN_RUN_GUAJI = "BEGAN_RUN_GUAJI",
  /**主线战斗完成 */
  MAIN_BATTLE_FIGHT_RESULT = "MAIN_BATTLE_FIGHT_RESULT",
  /**退出挂机 */
  EXIT_GUAJI = "EXIT_TO_GUAJI",
  /**初始化主线战斗数据 */
  BEGAN_RUN_MAIN_BATTLE = "BEGAN_RUN_MAIN_BATTLE",
  /**战斗结束 */
  FIGHT_FINISHED = "FIGHT_FINISHED",
  /**夺屏后战斗继续 */
  FIGHT_DUO_PING_CONTINUE = "FIGHT_DUO_PING_CONTINUE",
  /**使用技能 */
  FIGHT_USE_SKILL = "FIGHT_USE_SKILL",
  /**触发伤害 */
  FIGHT_TRIG_HURT = "FIGHT_TRIG_HURT",
  /**战斗步骤结束 */
  FIGHT_STEP_END = "FIGHT_STEP_END",
  /**战斗回合结束 */
  FIGHT_ROUND_END = "FIGHT_ROUND_END",
  /**触发场外BUFF */
  FIGHT_TRIG_OFFCOURT_BUFF = "FIGHT_TRIG_OFFCOURT_BUFF",

  /**清理地图 */
  DESTROY_MAP = "DESTROY_MAP",
  /**战斗次数 */
  FIGHT_TIMES = "FIGHT_TIMES",

  ON_GUAJI_SHOW_TYPE = "ON_GUAJI_SHOW_TYPE",

  //--------------新图鉴 start---------------------
  BUDDY_INFO = "BUDDY_INFO",

  BUDDY_UPDATE = "BUDDY_UPDATE",

  BUDDY_UPDATE_LEVEL = "BUDDY_UPDATE_LEVEL",

  //--------------新图鉴 end---------------------


  /**** */

  OPEN_FAMILY_RANK_ACTIVITY_DIALOG = "OPEN_FAMILY_RANK_ACTIVITY_DIALOG",
  CLOSE_FAMILY_RANK_ACTIVITY_DIALOG = "CLOSE_FAMILY_RANK_ACTIVITY_DIALOG",
  UPDATE_FAMILY_RANK_ACTIVITY = "UPDATE_FAMILY_RANK_ACTIVITY",
  OPEN_FAMILY_RANK_ACTIVITY_SCROE_DIALOG = "OPEN_FAMILY_RANK_ACTIVITY_SCROE_DIALOG",

  UPDATE_SELECT_STAR_DEBRIS = "UPDATE_SELECT_STAR_DEBRIS",
  UPDATE_SELECT_MATERIAL_ITEM = "UPDATE_SELECT_MATERIAL_ITEM",

  OPEN_FIGHT_BUFF_DIALOG = "OPEN_FIGHT_BUFF_DIALOG",
  CLOSE_FIGHT_BUFF_DIALOG = "CLOSE_FIGHT_BUFF_DIALOG",
  OPEN_FIGHT_BUFF_INFO_DIALOG = "OPEN_FIGHT_BUFF_INFO_DIALOG",
  CLOSE_FIGHT_BUFF_INFO_DIALOG = "CLOSE_FIGHT_BUFF_INFO_DIALOG",
  OPEN_FIGHT_BUFF_ICON_DIALOG = "OPEN_FIGHT_LOSING_STREAK_BUFF_DIALOG",

  UPDATE_FIGHT_BUFF = "UPDATW_FIGHT_BUFF",
  SHOW_FIGHT_BUFF_EFFECT = "SHOW_FIGHT_BUFF_EFFECT",

  OPEN_BUDDY_DIALOG = "OPEN_BUDDY_DIALOG",
  CLOSE_BUDDY_DIALOG = "CLOSE_BUDDY_DIALOG",
  OPEN_ONE_BUDDY_DIALOG = "OPEN_ONE_BUDDY_DIALOG",
  CLOSE_ONE_BUDDY_DIALOG = "CLOSE_ONE_BUDDY_DIALOG",
  OPEN_ACTIVE_BUDDY_DIALOG = "OPEN_ACTIVE_BUDDY_DIALOG",
  CLOSE_ACTIVE_BUDDY_DIALOG = "CLOSE_ACTIVE_BUDDY_DIALOG",

  FIGHT_SKILL_END = "FIGHT_SKILL_END",
  FIGHT_START_LOADING = "FIGHT_START_LOADING",

  TEST_RES = "TEST_RES",
  SHOW_UI_SHOUYINTUPO = "SHOW_UI_SHOUYINTUPO",
  AUTO_UPDATE_LINE_UP = "AUTO_UPDATE_LINE_UP",
  HTML_LINK_HANDLER = "HTML_LINK_HANDLER",
  OPEN_EQUIP_NOT_UP_LOAD_DIALOG = "OPEN_EQUIP_NOT_UP_LOAD_DIALOG",
  SKIP_TAB_HERO_INFO_DIALOG = "SKIP_TAB_HERO_INFO_DIALOG",
  FIGHT_END = "FIGHT_END",

  //------------跨服国战 start -------------
  //六国争霸场景地图
  OPEN_CROSSREALM_WAR_SCENE_DIALOG = "OPEN_CROSSREALM_WAR_SCENE_DIALOG",
  CLOSE_CROSSREALM_WAR_SCENE_DIALOG = "CLOSE_CROSSREALM_WAR_SCENE_DIALOG",
  MOVE_TO_CROSSREALM_WAR_CITY_EVENT = "MOVE_TO_CROSSREALM_WAR_CITY_EVENT",
  CROSSREALM_WAR_RETURN_FIGHT = "CROSSREALM_WAR_RETURN_FIGHT",
  //六国争霸 大厅
  OPEN_CROSSREALM_WAR_LOBBY_DIALOG = "OPEN_CROSSREALM_WAR_LOBBY_DIALOG",
  CLOSE_CROSSREALM_WAR_LOBBY_DIALOG = "CLOSE_CROSSREALM_WAR_LOBBY_DIALOG",
  //六国争霸助手
  OPEN_CROSSREALM_WAR_HELP_DIALOG = "OPEN_CROSSREALM_WAR_HELP_DIALOG",
  CLOSE_CROSSREALM_WAR_HELP_DIALOG = "CLOSE_CROSSREALM_WAR_HELP_DIALOG",
  //六国争霸 公会大厅
  OPEN_CROSSREALM_WAR_TEAM_DIALOG = "OPEN_CROSSREALM_WAR_TEAM_DIALOG",
  CLOSE_CROSSREALM_WAR_TEAM_DIALOG = "CLOSE_CROSSREALM_WAR_TEAM_DIALOG",
  //六国争霸排行榜
  OPEN_CROSSREALM_WAR_RANK_DIALOG = "OPEN_CROSSREALM_WAR_RANK_DIALOG",
  CLOSE_CROSSREALM_WAR_RANK_DIALOG = "CLOSE_CROSSREALM_WAR_RANK_DIALOG",
  //六国争霸排行榜预览
  OPEN_CROSSREALM_WAR_RANK_REWARD_DIALOG = "OPEN_CROSSREALM_WAR_RANK_REWARD_DIALOG",
  CLOSE_CROSSREALM_WAR_RANK_REWARD_DIALOG = "CLOSE_CROSSREALM_WAR_RANK_REWARD_DIALOG",
  //六国争霸积分奖励
  OPEN_CROSSREALM_WAR_SCORE_DIALOG = "OPEN_CROSSREALM_WAR_SCORE_DIALOG",
  CLOSE_CROSSREALM_WAR_SCORE_DIALOG = "CLOSE_CROSSREALM_WAR_SCORE_DIALOG",
  //六国争霸礼包界面
  OPEN_CROSSREALM_WAR_SHOP_GIFT = "OPEN_CROSSREALM_WAR_SHOP_GIFT",
  CLOSE_CROSSREALM_WAR_SHOP_GIFT = "CLOSE_CROSSREALM_WAR_SHOP_GIFT",
  //六国争霸结束提示界面
  OPEN_CROSSREALM_WAR_END_HINT = "OPEN_CROSSREALM_WAR_END_HINT",
  CLOSE_CROSSREALM_WAR_END_HINT = "CLOSE_CROSSREALM_WAR_END_HINT",
  //六国争霸战斗界面
  OPEN_CROSSREALM_WAR_FIGHT_DIALOG = "OPEN_CROSSREALM_WAR_FIGHT_DIALOG",
  CLOSE_CROSSREALM_WAR_FIGHT_DIALOG = "CLOSE_CROSSREALM_WAR_FIGHT_DIALOG",
  //六国争霸查看城池界面
  OPEN_CROSSREALM_WAR_LOOK_DIALOG = "OPEN_CROSSREALM_WAR_LOOK_DIALOG",
  CLOSE_CROSSREALM_WAR_LOOK_DIALOG = "CLOSE_CROSSREALM_WAR_LOOK_DIALOG",
  //六国争霸调兵界面
  OPEN_CROSSREALM_WAR_DIAOBING_DIALOG = "OPEN_CROSSREALM_WAR_DIAOBING_DIALOG",
  CLOSE_CROSSREALM_WAR_DIAOBING_DIALOG = "CLOSE_CROSSREALM_WAR_DIAOBING_DIALOG",
  //六国争霸城战信息界面
  OPEN_CROSSREALM_WAR_CITY_FIGHT_INFO_DIALOG = "OPEN_CROSSREALM_WAR_CITY_FIGHT_INFO_DIALOG",
  CLOSE_CROSSREALM_WAR_CITY_FIGHT_INFO_DIALOG = "CLOSE_CROSSREALM_WAR_CITY_FIGHT_INFO_DIALOG",
  //六国争霸角色对战界面
  OPEN_CROSSREALM_WAR_ROLE_VS_DIALOG = "OPEN_CROSSREALM_WAR_ROLE_VS_DIALOG",
  CLOSE_CROSSREALM_WAR_ROLE_VS_DIALOG = "CLOSE_CROSSREALM_WAR_ROLE_VS_DIALOG",
  //六国争霸角色信息界面
  OPEN_CROSSREALM_WAR_ROLE_INFO_DIALOG = "OPEN_CROSSREALM_WAR_ROLE_INFO_DIALOG",
  CLOSE_CROSSREALM_WAR_ROLE_INFO_DIALOG = "CLOSE_CROSSREALM_WAR_ROLE_INFO_DIALOG",
  //六国争霸领地信息界面
  OPEN_CROSSREALM_WAR_MANOR_INFO_DIALOG = "OPEN_CROSSREALM_WAR_MANOR_INFO_DIALOG",
  CLOSE_CROSSREALM_WAR_MANOR_INFO_DIALOG = "CLOSE_CROSSREALM_WAR_MANOR_INFO_DIALOG",
  //六国争霸放弃城池界面
  OPEN_CROSSREALM_WAR_MANOR_WAIVE_DIALOG = "OPEN_CROSSREALM_WAR_MANOR_WAIVE_DIALOG",
  CLOSE_CROSSREALM_WAR_MANOR_WAIVE_DIALOG = "CLOSE_CROSSREALM_WAR_MANOR_WAIVE_DIALOG",
  //六国争霸小地图
  OPEN_CROSSREALM_WAR_SMALL_MAP_DIALOG = "OPEN_CROSSREALM_WAR_SMALL_MAP_DIALOG",
  CLOSE_CROSSREALM_WAR_SMALL_MAP_DIALOG = "CLOSE_CROSSREALM_WAR_SMALL_MAP_DIALOG",
  //六国争霸城战结束界面
  OPEN_CROSSREALM_WAR_END_DIALOG = "OPEN_CROSSREALM_WAR_END_DIALOG",
  CLOSE_CROSSREALM_WAR_END_DIALOG = "CLOSE_CROSSREALM_WAR_END_DIALOG",
  //六国争霸buff界面
  OPEN_CROSSREALM_WAR_BUFF_DIALOG = "OPEN_CROSSREALM_WAR_BUFF_DIALOG",
  CLOSE_CROSSREALM_WAR_BUFF_DIALOG = "CLOSE_CROSSREALM_WAR_BUFF_DIALOG",
  CLOSE_CROSSREALM_FAMILY_CAMP_LOCK_INFO_DIALOG = "CLOSE_CROSSREALM_FAMILY_CAMP_LOCK_INFO_DIALOG",
  OPEN_CROSSREALM_FAMILY_CAMP_LOCK_INFO_DIALOG = "OPEN_CROSSREALM_FAMILY_CAMP_LOCK_INFO_DIALOG",
  //大营撤回界面
  OPEN_CROSS_WAR_TROOPS_RETIRE_DIALOG = "OPEN_CROSS_WAR_TROOPS_RETIRE_DIALOG",
  CLOSE_CROSS_WAR_TROOPS_RETIRE_DIALOG = "CLOSE_CROSS_WAR_TROOPS_RETIRE_DIALOG",
  //六国争霸託管界面
  OPEN_CROSSREALM_WAR_AUTO = "OPEN_CROSSREALM_WAR_AUTO",
  CLOSE_CROSSREALM_WAR_AUTO = "CLOSE_CROSSREALM_WAR_AUTO",
  //六国争霸领地界面
  OPEN_CROSSREALM_WAR_MANOR_DIALOG = "OPEN_CROSSREALM_WAR_MANOR_DIALOG",
  CLOSE_CROSSREALM_WAR_MANOR_DIALOG = "CLOSE_CROSSREALM_WAR_MANOR_DIALOG",
  OPEN_CROSSREALM_WAR_ABDIALOG = "OPEN_CROSSREALM_WAR_ABDIALOG",
  CLOSE_CROSSREALM_WAR_ABDIALOG = "CLOSE_CROSSREALM_WAR_ABDIALOG",

  M_CROSS_WAR_OPERATE_TOC = "M_CROSS_WAR_OPERATE_TOC",

  M_CROSS_WAR_CITY_INFO = "M_CROSS_WAR_CITY_INFO",
  M_CROSS_WAR_CITY_SM_INFO = "M_CROSS_WAR_CITY_SM_INFO",
  M_CROSS_WAR_FAMILY_CITY_TOC = "M_CROSS_WAR_FAMILY_CITY_TOC",
  M_CROSS_WAR_ROLE_TEAM_SINGLE_TOC = "M_CROSS_WAR_ROLE_TEAM_SINGLE_TOC",
  M_CROSS_WAR_CONSCRIPTION_SEND_TOC = "M_CROSS_WAR_CONSCRIPTION_SEND_TOC",
  M_CROSS_WAR_TEAM_DEATH_TOC = "M_CROSS_WAR_TEAM_DEATH_TOC",




  //积分，每日，奖励领取返回
  CROSS_WAR_KILL_FETCH_TOC = "CROSS_WAR_KILL_FETCH_TOC",
  //六国争霸自动征兵设置返回
  CROSS_WAR_CONSCRIPTION_OP_TOC = "CROSS_WAR_CONSCRIPTION_OP_TOC",
  //六国争霸大营部队
  CROSS_WAR_FAMILY_CAMP = "CROSS_WAR_FAMILY_CAMP",
  //图腾
  M_TOTEM_INFO_TOC = "M_TOTEM_INFO_TOC",
  M_TOTEM_UPGRADE_TOC = "M_TOTEM_UPGRADE_TOC",
  OPEN_HERO_TUTENG_DIALOG = "OPEN_HERO_TUTENG_DIALOG",
  CLOSE_HERO_TUTENG_DIALOG = "CLOSE_HERO_TUTENG_DIALOG",
  OPEN_HERO_TUTENG_LEVELUP_SUCCESS_DIALOG = "OPEN_HERO_TUTENG_LEVELUP_SUCCESS_DIALOG",
  CLOSE_HERO_TUTENG_LEVELUP_SUCCESS_DIALOG = "CLOSE_HERO_TUTENG_LEVELUP_SUCCESS_DIALOG",
  OPEN_HERO_TUTENG_ATTR_DIALOG = "OPEN_HERO_TUTENG_ATTR_DIALOG",
  CLOSE_HERO_TUTENG_ATTR_DIALOG = "CLOSE_HERO_TUTENG_ATTR_DIALOG",

  UPDATE_FORGE_FAMILY_CITY_LIST = "UPDATE_FORGE_FAMILY_CITY_LIST",
  //M_CROSS_WAR_ROLE_TEAM_INFO_TOC  = "M_CROSS_WAR_ROLE_TEAM_INFO_TOC",
  M_CROSS_WAR_BUFF_INFO_TOC = "M_CROSS_WAR_BUFF_INFO_TOC",
  M_CROSS_WAR_BUFF_UPGRADE_TOC = "M_CROSS_WAR_BUFF_UPGRADE_TOC",
  M_CROSS_WAR_ROLE_TEAM_UPDATE = "M_CROSS_WAR_ROLE_TEAM_UPDATE",
  //六国争霸地图位置变化
  CHANGE_CROSSREALM_WAR_MAP_POS = "CHANGE_CROSSREALM_WAR_MAP_POS",
  OPEN_CROSS_WAR_AUTO_FAMILY_LIST_DIALOG = "OPEN_CROSS_WAR_AUTO_FAMILY_LIST_DIALOG",
  CLOSE_CROSS_WAR_AUTO_FAMILY_LIST_DIALOG = "CLOSE_CROSS_WAR_AUTO_FAMILY_LIST_DIALOG",
  UPDATE_CROSS_WAR_AUTO_INFO = "UPDATE_CROSS_WAR_AUTO_INFO",
  ON_CHANGE_CROSS_WAR_AUTO_TEAM_LIST = "ON_CHANGE_CROSS_WAR_AUTO_TEAM_LIST",
  UPDATE_CROSS_WAR_AUTO_SERVER_STATE = "UPDATE_CROSS_WAR_AUTO_SERVER_STATE",
  UPDATE_CROSS_WAR_AUTO_CITY_LIST = "UPDATE_CROSS_WAR_AUTO_CITY_LIST",
  UPDATE_CROSS_WAR_AUTO_TEAM_LIST = "UPDATE_CROSS_WAR_AUTO_TEAM_LIST",
  CHANGE_CROSS_WAR_AUTO_CITY_SELECT = "CHANGE_CROSS_WAR_AUTO_CITY_SELECT",
  CHANGE_CROSS_WAR_AUTO_FAMILY_SELECT = "CHANGE_CROSS_WAR_AUTO_FAMILY_SELECT",
  CLOSE_CROSS_WAR_AUTO_LINE_UP_DIALOG = "CLOSE_CROSS_WAR_AUTO_LINE_UP_DIALOG",
  OPEN_CROSS_WAR_AUTO_LINE_UP_DIALOG = "OPEN_CROSS_WAR_AUTO_LINE_UP_DIALOG",
  //六国争霸城池菜单
  SHOW_CROSSREALM_WAR_CITY_MENU = "SHOW_CROSSREALM_WAR_CITY_MENU",
  //创建BUFF图标
  CREATE_BUFF_IMAGE = "CREATE_BUFF_IMAGE",
  DELETE_BUFF_IMAGE = "DELETE_BUFF_IMAGE",
  //跨服国战英雄复活
  M_CROSS_WAR_REVIVE_HERO = "M_CROSS_WAR_REVIVE_HERO",
  //六国争霸复活界面
  OPEN_CROSSREALM_WAR_HERO_FUHUO_DIALOG = "OPEN_CROSSREALM_WAR_HERO_FUHUO_DIALOG",
  CLOSE_CROSSREALM_WAR_HERO_FUHUO_DIALOG = "CLOSE_CROSSREALM_WAR_HERO_FUHUO_DIALOG",

  CROSSREALM_WAR_MAP_COMPLETE = "CROSSREALM_WAR_MAP_COMPLETE",
  /**派遣部队 */
  OPEN_CROSSREALM_WAR_TROOPS_LIST_DIALOG = "OPEN_CROSSREALM_WAR_TROOPS_LIST_DIALOG",

  //------------跨服国战 end ---------------


  //------------官渡之战 start -------------
  //官渡之战场景地图
  OPEN_CSGD_WAR_SCENE_DIALOG = "OPEN_CSGD_WAR_SCENE_DIALOG",
  CLOSE_CSGD_WAR_SCENE_DIALOG = "CLOSE_CSGD_WAR_SCENE_DIALOG",
  MOVE_TO_CSGD_WAR_CITY_EVENT = "MOVE_TO_CSGD_WAR_CITY_EVENT",
  CSGD_WAR_RETURN_FIGHT = "CSGD_WAR_RETURN_FIGHT",
  //官渡之战 大厅
  OPEN_CSGD_WAR_LOBBY_DIALOG = "OPEN_CSGD_WAR_LOBBY_DIALOG",
  CLOSE_CSGD_WAR_LOBBY_DIALOG = "CLOSE_CSGD_WAR_LOBBY_DIALOG",
  //官渡之战助手
  OPEN_CSGD_WAR_HELP_DIALOG = "OPEN_CSGD_WAR_HELP_DIALOG",
  CLOSE_CSGD_WAR_HELP_DIALOG = "CLOSE_CSGD_WAR_HELP_DIALOG",
  //官渡之战 公会大厅
  OPEN_CSGD_WAR_TEAM_DIALOG = "OPEN_CSGD_WAR_TEAM_DIALOG",
  CLOSE_CSGD_WAR_TEAM_DIALOG = "CLOSE_CSGD_WAR_TEAM_DIALOG",
  //官渡之战排行榜
  OPEN_CSGD_WAR_RANK_DIALOG = "OPEN_CSGD_WAR_RANK_DIALOG",
  CLOSE_CSGD_WAR_RANK_DIALOG = "CLOSE_CSGD_WAR_RANK_DIALOG",
  //官渡之战排行榜预览
  OPEN_CSGD_WAR_RANK_REWARD_DIALOG = "OPEN_CSGD_WAR_RANK_REWARD_DIALOG",
  CLOSE_CSGD_WAR_RANK_REWARD_DIALOG = "CLOSE_CSGD_WAR_RANK_REWARD_DIALOG",
  //官渡之战积分奖励
  OPEN_CSGD_WAR_SCORE_DIALOG = "OPEN_CSGD_WAR_SCORE_DIALOG",
  CLOSE_CSGD_WAR_SCORE_DIALOG = "CLOSE_CSGD_WAR_SCORE_DIALOG",
  //官渡之战礼包界面
  OPEN_CSGD_WAR_SHOP_GIFT = "OPEN_CSGD_WAR_SHOP_GIFT",
  CLOSE_CSGD_WAR_SHOP_GIFT = "CLOSE_CSGD_WAR_SHOP_GIFT",
  //官渡之战结束提示界面
  OPEN_CSGD_WAR_END_HINT = "OPEN_CSGD_WAR_END_HINT",
  CLOSE_CSGD_WAR_END_HINT = "CLOSE_CSGD_WAR_END_HINT",
  //官渡之战战斗界面
  OPEN_CSGD_WAR_FIGHT_DIALOG = "OPEN_CSGD_WAR_FIGHT_DIALOG",
  CLOSE_CSGD_WAR_FIGHT_DIALOG = "CLOSE_CSGD_WAR_FIGHT_DIALOG",
  //官渡之战查看城池界面
  OPEN_CSGD_WAR_LOOK_DIALOG = "OPEN_CSGD_WAR_LOOK_DIALOG",
  CLOSE_CSGD_WAR_LOOK_DIALOG = "CLOSE_CSGD_WAR_LOOK_DIALOG",
  //官渡之战调兵界面
  OPEN_CSGD_WAR_DIAOBING_DIALOG = "OPEN_CSGD_WAR_DIAOBING_DIALOG",
  CLOSE_CSGD_WAR_DIAOBING_DIALOG = "CLOSE_CSGD_WAR_DIAOBING_DIALOG",
  //官渡之战城战信息界面
  OPEN_CSGD_WAR_CITY_FIGHT_INFO_DIALOG = "OPEN_CSGD_WAR_CITY_FIGHT_INFO_DIALOG",
  CLOSE_CSGD_WAR_CITY_FIGHT_INFO_DIALOG = "CLOSE_CSGD_WAR_CITY_FIGHT_INFO_DIALOG",
  //官渡之战角色对战界面
  OPEN_CSGD_WAR_ROLE_VS_DIALOG = "OPEN_CSGD_WAR_ROLE_VS_DIALOG",
  CLOSE_CSGD_WAR_ROLE_VS_DIALOG = "CLOSE_CSGD_WAR_ROLE_VS_DIALOG",
  //官渡之战角色信息界面
  OPEN_CSGD_WAR_ROLE_INFO_DIALOG = "OPEN_CSGD_WAR_ROLE_INFO_DIALOG",
  CLOSE_CSGD_WAR_ROLE_INFO_DIALOG = "CLOSE_CSGD_WAR_ROLE_INFO_DIALOG",
  //官渡之战领地信息界面
  OPEN_CSGD_WAR_MANOR_INFO_DIALOG = "OPEN_CSGD_WAR_MANOR_INFO_DIALOG",
  CLOSE_CSGD_WAR_MANOR_INFO_DIALOG = "CLOSE_CSGD_WAR_MANOR_INFO_DIALOG",
  //官渡之战放弃城池界面
  OPEN_CSGD_WAR_MANOR_WAIVE_DIALOG = "OPEN_CSGD_WAR_MANOR_WAIVE_DIALOG",
  CLOSE_CSGD_WAR_MANOR_WAIVE_DIALOG = "CLOSE_CSGD_WAR_MANOR_WAIVE_DIALOG",
  //官渡之战小地图
  OPEN_CSGD_WAR_SMALL_MAP_DIALOG = "OPEN_CSGD_WAR_SMALL_MAP_DIALOG",
  CLOSE_CSGD_WAR_SMALL_MAP_DIALOG = "CLOSE_CSGD_WAR_SMALL_MAP_DIALOG",
  //官渡之战城战结束界面
  OPEN_CSGD_WAR_END_DIALOG = "OPEN_CSGD_WAR_END_DIALOG",
  CLOSE_CSGD_WAR_END_DIALOG = "CLOSE_CSGD_WAR_END_DIALOG",
  //官渡之战buff界面
  OPEN_CSGD_WAR_BUFF_DIALOG = "OPEN_CSGD_WAR_BUFF_DIALOG",
  CLOSE_CSGD_WAR_BUFF_DIALOG = "CLOSE_CSGD_WAR_BUFF_DIALOG",
  CLOSE_CSGD_FAMILY_CAMP_LOCK_INFO_DIALOG = "CLOSE_CSGD_FAMILY_CAMP_LOCK_INFO_DIALOG",
  OPEN_CSGD_FAMILY_CAMP_LOCK_INFO_DIALOG = "OPEN_CSGD_FAMILY_CAMP_LOCK_INFO_DIALOG",
  //大营撤回界面
  OPEN_CSGD_WAR_TROOPS_RETIRE_DIALOG = "OPEN_CSGD_WAR_TROOPS_RETIRE_DIALOG",
  CLOSE_CSGD_WAR_TROOPS_RETIRE_DIALOG = "CLOSE_CSGD_WAR_TROOPS_RETIRE_DIALOG",
  //官渡之战託管界面
  OPEN_CSGD_WAR_AUTO = "OPEN_CSGD_WAR_AUTO",
  CLOSE_CSGD_WAR_AUTO = "CLOSE_CSGD_WAR_AUTO",
  //官渡之战领地界面
  OPEN_CSGD_WAR_MANOR_DIALOG = "OPEN_CSGD_WAR_MANOR_DIALOG",
  CLOSE_CSGD_WAR_MANOR_DIALOG = "CLOSE_CSGD_WAR_MANOR_DIALOG",
  OPEN_CSGD_WAR_ABDIALOG = "OPEN_CSGD_WAR_ABDIALOG",
  CLOSE_CSGD_WAR_ABDIALOG = "CLOSE_CSGD_WAR_ABDIALOG",

  M_CSGD_WAR_OPERATE_TOC = "M_CSGD_WAR_OPERATE_TOC",
  M_CSGD_WAR_CITY_INFO = "M_CSGD_WAR_CITY_INFO",
  M_CSGD_WAR_CITY_SM_INFO = "M_CSGD_WAR_CITY_SM_INFO",
  M_CSGD_WAR_ROLE_TEAM_SINGLE_TOC = "M_CSGD_WAR_ROLE_TEAM_SINGLE_TOC",
  M_CSGD_WAR_DEPLOY_VIEW = "M_CSGD_WAR_DEPLOY_VIEW",
  M_CSGD_WAR_CONSCRIPTION_SEND_TOC = "M_CSGD_WAR_CONSCRIPTION_SEND_TOC",
  M_CSGD_WAR_FAMILY_CITY_TOC = "M_CSGD_WAR_FAMILY_CITY_TOC",
  M_CSGD_WAR_CITY_SM_UPDATE = "M_CSGD_WAR_CITY_SM_UPDATE",

  //积分，每日，奖励领取返回
  CSGD_WAR_KILL_FETCH_TOC = "CSGD_WAR_KILL_FETCH_TOC",
  //官渡之战自动征兵设置返回
  CSGD_WAR_CONSCRIPTION_OP_TOC = "CSGD_WAR_CONSCRIPTION_OP_TOC",
  //官渡之战大营部队
  CSGD_WAR_FAMILY_CAMP = "CSGD_WAR_FAMILY_CAMP",

  M_CSGD_WAR_BUFF_INFO_TOC = "M_CSGD_WAR_BUFF_INFO_TOC",
  M_CSGD_WAR_BUFF_UPGRADE_TOC = "M_CSGD_WAR_BUFF_UPGRADE_TOC",
  //官渡之战地图位置变化
  CHANGE_CSGD_WAR_MAP_POS = "CHANGE_CSGD_WAR_MAP_POS",
  OPEN_CSGD_WAR_AUTO_FAMILY_LIST_DIALOG = "OPEN_CSGD_WAR_AUTO_FAMILY_LIST_DIALOG",
  CLOSE_CSGD_WAR_AUTO_FAMILY_LIST_DIALOG = "CLOSE_CSGD_WAR_AUTO_FAMILY_LIST_DIALOG",
  UPDATE_CSGD_WAR_AUTO_INFO = "UPDATE_CSGD_WAR_AUTO_INFO",
  ON_CHANGE_CSGD_WAR_AUTO_TEAM_LIST = "ON_CHANGE_CSGD_WAR_AUTO_TEAM_LIST",
  UPDATE_CSGD_WAR_AUTO_SERVER_STATE = "UPDATE_CSGD_WAR_AUTO_SERVER_STATE",
  UPDATE_CSGD_WAR_AUTO_CITY_LIST = "UPDATE_CSGD_WAR_AUTO_CITY_LIST",
  UPDATE_CSGD_WAR_AUTO_TEAM_LIST = "UPDATE_CSGD_WAR_AUTO_TEAM_LIST",
  CHANGE_CSGD_WAR_AUTO_CITY_SELECT = "CHANGE_CSGD_WAR_AUTO_CITY_SELECT",
  CHANGE_CSGD_WAR_AUTO_FAMILY_SELECT = "CHANGE_CSGD_WAR_AUTO_FAMILY_SELECT",
  CLOSE_CSGD_WAR_AUTO_LINE_UP_DIALOG = "CLOSE_CSGD_WAR_AUTO_LINE_UP_DIALOG",
  OPEN_CSGD_WAR_AUTO_LINE_UP_DIALOG = "OPEN_CSGD_WAR_AUTO_LINE_UP_DIALOG",
  //官渡之战城池菜单
  SHOW_CSGD_WAR_CITY_MENU = "SHOW_CSGD_WAR_CITY_MENU",
  //创建BUFF图标
  CREATE_CSGD_BUFF_IMAGE = "CREATE_CSGD_BUFF_IMAGE",
  DELETE_CSGD_BUFF_IMAGE = "DELETE_CSGD_BUFF_IMAGE",
  //跨服国战英雄复活
  M_CSGD_WAR_REVIVE_HERO = "M_CSGD_WAR_REVIVE_HERO",
  //官渡之战复活界面
  OPEN_CSGD_WAR_HERO_FUHUO_DIALOG = "OPEN_CSGD_WAR_HERO_FUHUO_DIALOG",
  CLOSE_CSGD_WAR_HERO_FUHUO_DIALOG = "CLOSE_CSGD_WAR_HERO_FUHUO_DIALOG",

  CSGD_WAR_MAP_COMPLETE = "CSGD_WAR_MAP_COMPLETE",
  /**派遣部队 */
  OPEN_CSGD_WAR_TROOPS_LIST_DIALOG = "OPEN_CSGD_WAR_TROOPS_LIST_DIALOG",


  M_CSGD_WAR_TOTEM_ACTIVE_TOC = "M_CROSS_WAR_TOTEM_ACTIVE_TOC",

  //官渡之战出兵翻倍奖励消耗
  OPEN_CSGD_WAR_REWARDDOUBLE_DIALOG = "OPEN_CSGD_WAR_REWARDDOUBLE_DIALOG",
  CLOSE_CSGD_WAR_REWARDDOUBLE_DIALOG = "CLOSE_CSGD_WAR_REWARDDOUBLE_DIALOG",
  CSGD_WAR_REWARDDOUBLE_CHANGE = "CSGD_WAR_REWARDDOUBLE_CHANGE",
  CSGD_WAR_MOVE_TO_CITY = "CSGD_WAR_MOVE_TO_CITY",
  CSGD_WAR_MOVE_TO_TEAM = "CSGD_WAR_MOVE_TO_TEAM",
  CSGD_WAR_EXCHANGE_DIALOG = "CSGD_WAR_EXCHANGE_DIALOG",
  CSGD_WAR_AUTO_LINEUP = "CSGD_WAR_AUTO_LINEUP",
  OPEN_CSGD_WAR_CITY_SELECT_DIALOG = "OPEN_CSGD_WAR_CITY_SELECT_DIALOG",
  SELECT_CSGD_AUTO_CITY_ITEM = "SELECT_CSGD_AUTO_CITY_ITEM",


  //统帅装备界面
  OPEN_CROSSREALM_WAR_EQUIP_DIALOG = "CROSSREALM_WAR_EQUIP_DIALOG",
  CLOSE_CROSSREALM_WAR_EQUIP_DIALOG = "CLOSE_CROSSREALM_WAR_EQUIP_DIALOG",

  //统帅装备界面
  OPEN_CROSSREALM_WAR_EQUIP_SHOP_DIALOG = "OPEN_CROSSREALM_WAR_EQUIP_SHOP_DIALOG",
  CLOSE_CROSSREALM_WAR_EQUIP_SHOP_DIALOG = "CLOSE_CROSSREALM_WAR_EQUIP_SHOP_DIALOG",

  //统帅装备升级界面
  OPEN_CROSSREALM_WAR_EQUIP_UP_DIALOG = "OPEN_CROSSREALM_WAR_EQUIP_UP_DIALOG",
  CLOSE_CROSSREALM_WAR_EQUIP_UP_DIALOG = "CLOSE_CROSSREALM_WAR_EQUIP_UP_DIALOG",

  //统帅装备背包界面
  OPEN_CROSSREALM_WAR_EQUIP_BAG_DIALOG = "OPEN_CROSSREALM_WAR_EQUIP_BAG_DIALOG",
  CLOSE_CROSSREALM_WAR_EQUIP_BAG_DIALOG = "CLOSE_CROSSREALM_WAR_EQUIP_BAG_DIALOG",

  //统帅装备总属性界面
  OPEN_CROSSREALM_WAR_EQUIP_ALL_ATTR_DIALOG = "OPEN_CROSSREALM_WAR_EQUIP_ALL_ATTR_DIALOG",
  CLOSE_CROSSREALM_WAR_EQUIP_ALL_ATTR_DIALOG = "CLOSE_CROSSREALM_WAR_EQUIP_ALL_ATTR_DIALOG",

  CROSSREALM_WAR_UPDATE_EQUIP_INFO = "CROSSREALM_WAR_UPDATE_EQUIP_INFO",
  CROSSREALM_WAR_UPDATE_EQUIP_UP = "CROSSREALM_WAR_UPDATE_EQUIP_UP",
  CROSSREALM_WAR_UPDATE_EQUIP_LOAD = "CROSSREALM_WAR_UPDATE_EQUIP_LOAD",
  CROSSREALM_WAR_EQUIP_DECOMPOSE = "CROSSREALM_WAR_EQUIP_DECOMPOSE",

  //------------官渡之战 end ---------------

  //------------公会试炼 start -------------
  //公会试炼场景地图
  OPEN_FAMILY_TRIAL_WAR_SCENE_DIALOG = "OPEN_FAMILY_TRIAL_WAR_SCENE_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_SCENE_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_SCENE_DIALOG",
  MOVE_TO_FAMILY_TRIAL_WAR_CITY_EVENT = "MOVE_TO_FAMILY_TRIAL_WAR_CITY_EVENT",
  FAMILY_TRIAL_WAR_RETURN_FIGHT = "FAMILY_TRIAL_WAR_RETURN_FIGHT",
  //公会试炼 大厅
  OPEN_FAMILY_TRIAL_WAR_LOBBY_DIALOG = "OPEN_FAMILY_TRIAL_WAR_LOBBY_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_LOBBY_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_LOBBY_DIALOG",
  //公会试炼助手
  OPEN_FAMILY_TRIAL_WAR_HELP_DIALOG = "OPEN_FAMILY_TRIAL_WAR_HELP_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_HELP_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_HELP_DIALOG",
  //公会试炼 公会大厅
  OPEN_FAMILY_TRIAL_WAR_TEAM_DIALOG = "OPEN_FAMILY_TRIAL_WAR_TEAM_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_TEAM_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_TEAM_DIALOG",
  //公会试炼排行榜
  OPEN_FAMILY_TRIAL_WAR_RANK_DIALOG = "OPEN_FAMILY_TRIAL_WAR_RANK_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_RANK_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_RANK_DIALOG",
  //公会试炼 战报
  OPEN_FAMILY_TRIAL_WAR_LOGS_DIALOG = "OPEN_FAMILY_TRIAL_WAR_LOGS_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_LOGS_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_LOGS_DIALOG",
  OPEN_FAMILY_TRIAL_WAR_PK_LOGS_DIALOG = "OPEN_FAMILY_TRIAL_WAR_PK_LOGS_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_PK_LOGS_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_PK_LOGS_DIALOG",

  //公会试炼排行榜预览
  OPEN_FAMILY_TRIAL_WAR_RANK_REWARD_DIALOG = "OPEN_FAMILY_TRIAL_WAR_RANK_REWARD_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_RANK_REWARD_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_RANK_REWARD_DIALOG",
  //公会试炼积分奖励
  OPEN_FAMILY_TRIAL_WAR_SCORE_DIALOG = "OPEN_FAMILY_TRIAL_WAR_SCORE_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_SCORE_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_SCORE_DIALOG",
  //公会试炼礼包界面
  OPEN_FAMILY_TRIAL_WAR_SHOP_GIFT = "OPEN_FAMILY_TRIAL_WAR_SHOP_GIFT",
  CLOSE_FAMILY_TRIAL_WAR_SHOP_GIFT = "CLOSE_FAMILY_TRIAL_WAR_SHOP_GIFT",
  //公会试炼结束提示界面
  OPEN_FAMILY_TRIAL_WAR_END_HINT = "OPEN_FAMILY_TRIAL_WAR_END_HINT",
  CLOSE_FAMILY_TRIAL_WAR_END_HINT = "CLOSE_FAMILY_TRIAL_WAR_END_HINT",
  //公会试炼战斗界面
  OPEN_FAMILY_TRIAL_WAR_FIGHT_DIALOG = "OPEN_FAMILY_TRIAL_WAR_FIGHT_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_FIGHT_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_FIGHT_DIALOG",
  //公会试炼查看城池界面
  OPEN_FAMILY_TRIAL_WAR_LOOK_DIALOG = "OPEN_FAMILY_TRIAL_WAR_LOOK_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_LOOK_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_LOOK_DIALOG",
  //公会试炼调兵界面
  OPEN_FAMILY_TRIAL_WAR_DIAOBING_DIALOG = "OPEN_FAMILY_TRIAL_WAR_DIAOBING_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_DIAOBING_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_DIAOBING_DIALOG",
  //公会试炼城战信息界面
  OPEN_FAMILY_TRIAL_WAR_CITY_FIGHT_INFO_DIALOG = "OPEN_FAMILY_TRIAL_WAR_CITY_FIGHT_INFO_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_CITY_FIGHT_INFO_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_CITY_FIGHT_INFO_DIALOG",
  //公会试炼角色对战界面
  OPEN_FAMILY_TRIAL_WAR_ROLE_VS_DIALOG = "OPEN_FAMILY_TRIAL_WAR_ROLE_VS_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_ROLE_VS_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_ROLE_VS_DIALOG",
  //公会试炼角色信息界面
  OPEN_FAMILY_TRIAL_WAR_ROLE_INFO_DIALOG = "OPEN_FAMILY_TRIAL_WAR_ROLE_INFO_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_ROLE_INFO_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_ROLE_INFO_DIALOG",
  //公会试炼领地信息界面
  OPEN_FAMILY_TRIAL_WAR_MANOR_INFO_DIALOG = "OPEN_FAMILY_TRIAL_WAR_MANOR_INFO_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_MANOR_INFO_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_MANOR_INFO_DIALOG",
  //公会试炼放弃城池界面
  OPEN_FAMILY_TRIAL_WAR_MANOR_WAIVE_DIALOG = "OPEN_FAMILY_TRIAL_WAR_MANOR_WAIVE_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_MANOR_WAIVE_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_MANOR_WAIVE_DIALOG",
  //公会试炼小地图
  OPEN_FAMILY_TRIAL_WAR_SMALL_MAP_DIALOG = "OPEN_FAMILY_TRIAL_WAR_SMALL_MAP_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_SMALL_MAP_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_SMALL_MAP_DIALOG",
  //公会试炼城战结束界面
  OPEN_FAMILY_TRIAL_WAR_END_DIALOG = "OPEN_FAMILY_TRIAL_WAR_END_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_END_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_END_DIALOG",
  //公会试炼buff界面
  OPEN_FAMILY_TRIAL_WAR_BUFF_DIALOG = "OPEN_FAMILY_TRIAL_WAR_BUFF_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_BUFF_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_BUFF_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_FAMILY_CAMP_LOCK_INFO_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_FAMILY_CAMP_LOCK_INFO_DIALOG",
  OPEN_FAMILY_TRIAL_WAR_FAMILY_CAMP_LOCK_INFO_DIALOG = "OPEN_FAMILY_TRIAL_WAR_FAMILY_CAMP_LOCK_INFO_DIALOG",
  //大营撤回界面
  OPEN_FAMILY_TRIAL_WAR_TROOPS_RETIRE_DIALOG = "OPEN_FAMILY_TRIAL_WAR_TROOPS_RETIRE_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_TROOPS_RETIRE_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_TROOPS_RETIRE_DIALOG",
  //公会试炼託管界面
  OPEN_FAMILY_TRIAL_WAR_AUTO = "OPEN_FAMILY_TRIAL_WAR_AUTO",
  CLOSE_FAMILY_TRIAL_WAR_AUTO = "CLOSE_FAMILY_TRIAL_WAR_AUTO",
  //公会试炼领地界面
  OPEN_FAMILY_TRIAL_WAR_MANOR_DIALOG = "OPEN_FAMILY_TRIAL_WAR_MANOR_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_MANOR_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_MANOR_DIALOG",
  OPEN_FAMILY_TRIAL_WAR_ABDIALOG = "OPEN_FAMILY_TRIAL_WAR_ABDIALOG",
  CLOSE_FAMILY_TRIAL_WAR_ABDIALOG = "CLOSE_FAMILY_TRIAL_WAR_ABDIALOG",

  M_FAMILY_TRIAL_WAR_OPERATE_TOC = "M_FAMILY_TRIAL_WAR_OPERATE_TOC",
  M_FAMILY_TRIAL_WAR_TEAM_DEATH_TOC = "M_FAMILY_TRIAL_WAR_TEAM_DEATH_TOC",
  M_FAMILY_TRIAL_WAR_CITY_INFO = "M_FAMILY_TRIAL_WAR_CITY_INFO",
  M_FAMILY_TRIAL_WAR_CITY_SM_INFO = "M_FAMILY_TRIAL_WAR_CITY_SM_INFO",
  M_FAMILY_TRIAL_WAR_ROLE_TEAM_SINGLE_TOC = "M_FAMILY_TRIAL_WAR_ROLE_TEAM_SINGLE_TOC",
  M_FAMILY_TRIAL_WAR_DEPLOY_VIEW = "M_FAMILY_TRIAL_WAR_DEPLOY_VIEW",
  M_FAMILY_TRIAL_WAR_CONSCRIPTION_SEND_TOC = "M_FAMILY_TRIAL_WAR_CONSCRIPTION_SEND_TOC",
  M_FAMILY_TRIAL_WAR_FAMILY_CITY_TOC = "M_FAMILY_TRIAL_WAR_FAMILY_CITY_TOC",
  M_FAMILY_TRIAL_WAR_CITY_SM_UPDATE = "M_FAMILY_TRIAL_WAR_CITY_SM_UPDATE",

  M_FAMILY_TRIAL_WAR_MARCH_LIST_TOC = "M_FAMILY_TRIAL_WAR_MARCH_LIST_TOC",
  M_FAMILY_TRIAL_WAR_CAMP_UPDATE_TOC = "M_FAMILY_TRIAL_WAR_CAMP_UPDATE_TOC",
  M_FAMILY_TRIAL_WAR_CAMP_INFO_TOC = "M_FAMILY_TRIAL_WAR_CAMP_INFO_TOC",
  M_FAMILY_TRIAL_WAR_MARCH_FIGHT_TOC = "M_FAMILY_TRIAL_WAR_MARCH_FIGHT_TOC",
  M_FAMILY_TRIAL_WAR_MARCH_POS_TOC = "M_FAMILY_TRIAL_WAR_MARCH_POS_TOC",
  M_FAMILY_TRIAL_WAR_MARCH_DOWN_TOC = "M_FAMILY_TRIAL_WAR_MARCH_DOWN_TOC",

  //积分，每日，奖励领取返回
  FAMILY_TRIAL_WAR_KILL_FETCH_TOC = "FAMILY_TRIAL_WAR_KILL_FETCH_TOC",
  //公会试炼自动征兵设置返回
  FAMILY_TRIAL_WAR_CONSCRIPTION_OP_TOC = "FAMILY_TRIAL_WAR_CONSCRIPTION_OP_TOC",
  //公会试炼大营部队
  FAMILY_TRIAL_WAR_FAMILY_CAMP = "FAMILY_TRIAL_WAR_FAMILY_CAMP",

  //M_FAMILY_TRIAL_WAR_ROLE_TEAM_INFO_TOC  = "M_FAMILY_TRIAL_WAR_ROLE_TEAM_INFO_TOC",
  M_FAMILY_TRIAL_WAR_BUFF_INFO_TOC = "M_FAMILY_TRIAL_WAR_BUFF_INFO_TOC",
  M_FAMILY_TRIAL_WAR_BUFF_UPGRADE_TOC = "M_FAMILY_TRIAL_WAR_BUFF_UPGRADE_TOC",
  //M_FAMILY_TRIAL_WAR_ROLE_TEAM_UPDATE  = "M_FAMILY_TRIAL_WAR_ROLE_TEAM_UPDATE",
  //公会试炼地图位置变化
  OPEN_FAMILY_TRIAL_WAR_AUTO_FAMILY_LIST_DIALOG = "OPEN_FAMILY_TRIAL_WAR_AUTO_FAMILY_LIST_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_AUTO_FAMILY_LIST_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_AUTO_FAMILY_LIST_DIALOG",
  UPDATE_FAMILY_TRIAL_WAR_AUTO_INFO = "UPDATE_FAMILY_TRIAL_WAR_AUTO_INFO",
  ON_CHANGE_FAMILY_TRIAL_WAR_AUTO_TEAM_LIST = "ON_CHANGE_FAMILY_TRIAL_WAR_AUTO_TEAM_LIST",
  UPDATE_FAMILY_TRIAL_WAR_AUTO_SERVER_STATE = "UPDATE_FAMILY_TRIAL_WAR_AUTO_SERVER_STATE",
  UPDATE_FAMILY_TRIAL_WAR_AUTO_CITY_LIST = "UPDATE_FAMILY_TRIAL_WAR_AUTO_CITY_LIST",
  UPDATE_FAMILY_TRIAL_WAR_AUTO_TEAM_LIST = "UPDATE_FAMILY_TRIAL_WAR_AUTO_TEAM_LIST",
  CHANGE_FAMILY_TRIAL_WAR_AUTO_CITY_SELECT = "CHANGE_FAMILY_TRIAL_WAR_AUTO_CITY_SELECT",
  CHANGE_FAMILY_TRIAL_WAR_AUTO_FAMILY_SELECT = "CHANGE_FAMILY_TRIAL_WAR_AUTO_FAMILY_SELECT",
  CLOSE_FAMILY_TRIAL_WAR_AUTO_LINE_UP_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_AUTO_LINE_UP_DIALOG",
  OPEN_FAMILY_TRIAL_WAR_AUTO_LINE_UP_DIALOG = "OPEN_FAMILY_TRIAL_WAR_AUTO_LINE_UP_DIALOG",
  //公会试炼城池菜单
  SHOW_FAMILY_TRIAL_WAR_CITY_MENU = "SHOW_FAMILY_TRIAL_WAR_CITY_MENU",
  //创建BUFF图标
  CREATE_FAMILY_TRIAL_WAR_BUFF_IMAGE = "CREATE_FAMILY_TRIAL_WAR_BUFF_IMAGE",
  DELETE_FAMILY_TRIAL_WAR_BUFF_IMAGE = "DELETE_FAMILY_TRIAL_WAR_BUFF_IMAGE",
  //跨服国战英雄复活
  M_FAMILY_TRIAL_WAR_REVIVE_HERO = "M_FAMILY_TRIAL_WAR_REVIVE_HERO",
  //公会试炼复活界面
  OPEN_FAMILY_TRIAL_WAR_HERO_FUHUO_DIALOG = "OPEN_FAMILY_TRIAL_WAR_HERO_FUHUO_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_HERO_FUHUO_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_HERO_FUHUO_DIALOG",

  FAMILY_TRIAL_WAR_MAP_COMPLETE = "FAMILY_TRIAL_WAR_MAP_COMPLETE",
  /**派遣部队 */
  OPEN_FAMILY_TRIAL_WAR_TROOPS_LIST_DIALOG = "OPEN_FAMILY_TRIAL_WAR_TROOPS_LIST_DIALOG",

  /** */
  OPEN_FAMILY_TRIAL_WAR_TEAM_SHOW_DIALOG = "OPEN_FAMILY_TRIAL_WAR_TEAM_SHOW_DIALOG",

  M_FAMILY_TRIAL_WAR_TOTEM_ACTIVE_TOC = "M_CROSS_WAR_TOTEM_ACTIVE_TOC",

  //公会试炼出兵翻倍奖励消耗
  OPEN_FAMILY_TRIAL_WAR_REWARDDOUBLE_DIALOG = "OPEN_FAMILY_TRIAL_WAR_REWARDDOUBLE_DIALOG",
  CLOSE_FAMILY_TRIAL_WAR_REWARDDOUBLE_DIALOG = "CLOSE_FAMILY_TRIAL_WAR_REWARDDOUBLE_DIALOG",
  FAMILY_TRIAL_WAR_REWARDDOUBLE_CHANGE = "FAMILY_TRIAL_WAR_REWARDDOUBLE_CHANGE",
  FAMILY_TRIAL_WAR_MOVE_TO_CITY = "FAMILY_TRIAL_WAR_MOVE_TO_CITY",
  FAMILY_TRIAL_WAR_MOVE_TO_TEAM = "FAMILY_TRIAL_WAR_MOVE_TO_TEAM",
  FAMILY_TRIAL_WAR_EXCHANGE_DIALOG = "FAMILY_TRIAL_WAR_EXCHANGE_DIALOG",
  FAMILY_TRIAL_WAR_AUTO_LINEUP = "FAMILY_TRIAL_WAR_AUTO_LINEUP",
  OPEN_FAMILY_TRIAL_WAR_CITY_SELECT_DIALOG = "OPEN_FAMILY_TRIAL_WAR_CITY_SELECT_DIALOG",
  SELECT_FAMILY_TRIAL_WAR_AUTO_CITY_ITEM = "SELECT_FAMILY_TRIAL_WAR_AUTO_CITY_ITEM",

  FAMILY_TRIAL_WAR_FB_INFO = "FAMILY_TRIAL_WAR_FB_INFO",
  FAMILY_TRIAL_WAR_END = "FAMILY_TRIAL_WAR_END",

  FAMILY_TRIAL_WAR_SELECT_TEAM = "FAMILY_TRIAL_WAR_SELECT_TEAM",
  //------------公会试炼 end ---------------

  WARS_WALK_LIST = "WARS_WALK_LIST",


  //---------------英雄点名系统 start----------------
  //banner直购 天官赐福
  OPEN_BANNER_SHOP_DIALOG = "OPEN_BANNER_SHOP_DIALOG",
  CLOSE_BANNER_SHOP_DIALOG = "CLOSE_BANNER_SHOP_DIALOG",
  UPDATE_BANNER_SHOP_INFO = "UPDATE_BANNER_SHOP_INFO",
  CHECK_BANNER_SHOP_TIPS_AD = "CHECK_BANNER_SHOP_TIPS_AD",
  OPEN_HERO_ROLLCALL_TASK_DIALOG = "OPEN_HERO_ROLLCALL_TASK_DIALOG",
  CLOSE_HERO_ROLLCALL_TASK_DIALOG = "CLOSE_HERO_ROLLCALL_TASK_DIALOG",
  OPEN_HERO_ROLLCALL_GIFT_DIALOG = "OPEN_HERO_ROLLCALL_GIFT_DIALOG",
  CLOSE_HERO_ROLLCALL_GIFT_DIALOG = "CLOSE_HERO_ROLLCALL_GIFT_DIALOG",

  UPDATE_HERO_ROLLCALL_TASK_INFO = "UPDATE_HERO_ROLLCALL_TASK_INFO",
  UPDATE_HERO_ROLLCALL_GIFT_INFO = "UPDATE_HERO_ROLLCALL_GIFT_INFO",

  //---------------英雄点名系统 end----------------

  UPDATE_SKIN_RED_POINT = "UPDATE_SKIN_RED_POINT",


  //集字活动
  OPEN_BRAND_DIALOG = "OPEN_BRAND_DIALOG",
  COLSE_BRAND_DIALOG = "COLSE_BRAND_DIALOG",
  OPEN_BRAND_MISSION_DIALOG = "OPEN_BRAND_MISSION_DIALOG",
  COLSE_BRAND_MISSION_DIALOG = "COLSE_BRAND_MISSION_DIALOG",
  UPDATE_BRAND_ACTIVITY_MISSION = "UPDATE_BRAND_ACTIVITY_MISSION",
  UPDATE_BRAND_EXCHANGE_LIST = "UPDATE_BRAND_EXCHANGE_LIST",
  OPEN_BRAND_REQ_DEAL_DIALOG = "OPEN_BRAND_REQ_DEAL_DIALOG",
  OPEN_BRAND_SWAP_DIALOG = "OPEN_BRAND_SWAP_DIALOG",
  UPDATE_GATHER_SWAP = "UPDATE_GATHER_SWAP",
  UPDATE_GATHER_ACTIVITY_SHOP = "UPDATE_GATHER_ACTIVITY_SHOP",

  //------集字活动 end ------

  //畅游必购
  OPEN_ALL_BUY_DIALOG = "OPEN_ALL_BUY_DIALOG",
  CLOSE_ALL_BUY_DIALOG = "CLOSE_ALL_BUY_DIALOG",
  UPDATE_ALL_BUY_INFO = "UPDATE_ALL_BUY_INFO",

  //新手必购
  OPEN_NEW_BUY_DIALOG = "OPEN_NEW_BUY_DIALOG",
  UPDATE_NEW_BUY_INFO = "UPDATE_NEW_BUY_INFO",

  //跨服争霸
  OPEN_CROSS_ENTER_DIALOG = "OPEN_CROSS_ENTER_DIALOG",
  CLOSE_CROSS_ENTER_DIALOG = "CLOSE_CROSS_ENTER_DIALOG",

  //---------活动商店---------------------

  // UPDATE_ACTIVITY_SHOP_ITEM  = "UPDATE_ACTIVITY_SHOP_ITEM",
  UPDATE_ACTIVITY_SHOP_INFO = "UPDATE_ACTIVITY_SHOP_INFO",
  OPEN_ACTIVITY_SHOP_BUY_DIALOG = "OPEN_ACTIVITY_SHOP_BUY_DIALOG",
  OPEN_ACTIVITY_SHOP_BUY_TIP_DIALOG = "OPEN_ACTIVITY_SHOP_BUY_TIP_DIALOG",

  //---------活动商店 end-------------------

  //--------- w7官渡玩法 start ----------------
  OPEN_GUAN_DU_DIALOG = "OPEN_GUAN_DU_DIALOG",
  CLOSE_GUAN_DU_DIALOG = "CLOSE_GUAN_DU_DIALOG",
  OPEN_GUAN_DU_LINE_UP_DIALOG = "OPEN_GUAN_DU_LINE_UP_DIALOG",
  CLOSE_GUAN_DU_LINE_UP_DIALOG = "CLOSE_GUAN_DU_LINE_UP_DIALOG",
  OPEN_GUAN_DU_READY_DIALOG = "OPEN_GUAN_DU_READY_DIALOG",
  CLOSE_GUAN_DU_READY_DIALOG = "CLOSE_GUAN_DU_READY_DIALOG",
  OPEN_GUAN_DU_MISSION_DIALOG = "OPEN_GUAN_DU_MISSION_DIALOG",
  CLOSE_GUAN_DU_MISSION_DIALOG = "CLOSE_GUAN_DU_MISSION_DIALOG",
  OPEN_GUAN_DU_EVENT_SHOP_DIALOG = "OPEN_GUAN_DU_EVENT_SHOP_DIALOG",
  OPEN_GUAN_DU_SIMP_SHOP_DIALOG = "OPEN_GUAN_DU_SIMP_SHOP_DIALOG",
  OPEN_GUAN_DU_EVENT_ANSWER_DIALOG = "OPEN_GUAN_DU_EVENT_ANSWER_DIALOG",
  OPEN_GUAN_DU_EVENT_CHOSE_DIALOG = "OPEN_GUAN_DU_EVENT_CHOSE_DIALOG",
  OPEN_GUAN_DU_EVENT_BOX_DIALOG = "OPEN_GUAN_DU_EVENT_BOX_DIALOG",
  OPEN_GUAN_DU_SHOP_DIALOG = "OPEN_GUAN_DU_SHOP_DIALOG",
  OPEN_GUAN_DU_GAIN_REWARD_DIALOG = "OPEN_GUAN_DU_GAIN_REWARD_DIALOG",
  OPEN_GUAN_DU_BUFF_ADD_DIALOG = "OPEN_GUAN_DU_BUFF_ADD_DIALOG",
  OPEN_GUAN_DU_POISON_USE_DIALOG = "OPEN_GUAN_DU_POISON_USE_DIALOG",
  OPEN_GUAN_DU_HP_GOODS_USE_DIALOG = "OPEN_GUAN_DU_HP_GOODS_USE_DIALOG",
  OPEN_GUAN_DU_BUY_GOODS_TIP_DIALOG = "OPEN_GUAN_DU_BUY_GOODS_TIP_DIALOG",
  UPDATE_GUAN_DU_DIALOG = "UPDATE_GUAN_DU_DIALOG",
  UPDATE_GUAN_DU_MISSION = "UPDATE_GUAN_DU_MISSION",
  UPDATE_GUAN_DU_SHOP_ITEM_LIST = "UPDATE_GUAN_DU_SHOP_ITEM_LIST",
  CHANGE_GUAN_DU_HERO_SELECT = "CHANGE_GUAN_DU_HERO_SELECT",
  UPDATE_GUAN_DU_HERO_LIST = "UPDATE_GUAN_DU_HERO_LIST",
  UPDATE_GUAN_DU_EVENT_DROP_LIST = "UPDATE_GUAN_DU_EVENT_LIST",
  CLEAR_ALL_GUAN_DU_DROP_ITEM = "CLEAR_ALL_GUAN_DU_EVENT_DROP_ITEM",
  UPDATE_GUAN_DU_GOODS_NUM_LIST = "UPDATE_GUAN_DU_GOODS_NUM_LIST",
  UPDATE_GUAN_DU_MONSTER_LIST = "UPDATE_GUAN_DU_MONSTER_LIST",
  GUAN_DU_FIGHT_FINISH = "GUAN_DU_FIGHT_FINISH",
  ON_GUAN_DU_EVENT_FINISH_TOC = "ON_GUAN_DU_EVENT_FINISH_TOC",
  FLY_GUAN_DU_EVENT_ANI = "FLY_GUAN_DU_EVENT_ANI",
  //--------- w7官渡玩法 end ------------------

  //海外社区(sdk)
  OPEN_DISCORDJOIN_DIALOG = "OPEN_DISCORDJOIN_DIALOG",
  OPEN_STAR_REVIEW_DIALOG = "OPEN_STAR_REVIEW_DIALOG",
  UPDATE_DISCORD_INFO = "UPDATE_DISCORD_INFO",
  //---------SDK功能---------------------
  OPEN_SDK_SVIP_DIALOG = "OPEN_SDK_SVIP_DIALOG",
  OPEN_SDK_KEFU_DIALOG = "OPEN_SDK_KEFU_DIALOG",
  OPEN_SDK_FOCUS_DIALOG = "OPEN_SDK_FOCUS_DIALOG",
  // OPEN_SDK_SHARE_DIALOG  = "OPEN_SDK_SHARE_DIALOG",

  //sdk分享(邀请)
  OPEN_WX_SHARE_DIALOG = "OPEN_WX_SHARE_DIALOG",
  CLOSE_WX_SHARE_DIALOG = "CLOSE_WX_SHARE_DIALOG",

  UPDATE_SHARE_SINGLE_INFO = "UPDATE_SHARE_SINGLE_INFO",

  OPEN_SDK_FOCUS_REWARD_DIALOG = "OPEN_SDK_FOCUS_REWARD_DIALOG",
  CLOSE_SDK_FOCUS_REWARD_DIALOG = "CLOSE_SDK_FOCUS_REWARD_DIALOG",
  UPDATE_SDK_MESSAGE_CHECK = "UPDATE_SDK_MESSAGE_CHECK",
  OPEN_WX_TURN_APP_DIALOG = "OPEN_WX_TURN_APP_DIALOG",
  OPEN_WX_SVIP_TURN_APP_DIALOG = "OPEN_WX_SVIP_TURN_APP_DIALOG",
  OPEN_SDK_TURN_APP_FUNC = "OPEN_SDK_TURN_APP_FUNC",
  BIND_PHONE_NUMBER_DATA = "BIND_PHONE_NUMBER_DATA",
  OPEN_SIX_TURN_APP_DIALOGN = "OPEN_SIX_TURN_APP_DIALOGN",


  UPDATE_WX_GAME_CLUB_FETCH_INFO = "UPDATE_WX_GAME_CLUB_FETCH_INFO",
  OPEN_WX_GAME_CLUB_DIALOG = "OPEN_WX_GAME_CLUB_DIALOG",
  UPDATE_SDK_DATA = "UPDATE_SDK_DATA",
  UPDATE_WX_REWARD_AD_STATE = "UPDATE_WX_REWARD_AD_STATE",
  //---------SDK功能 end---------------------

  //----------战神殿 start -----------------
  OPEN_ARES_PALACE_DIALOG = "OPEN_ARES_PALACE_DIALOG",
  CLOSE_ARES_PALACE_DIALOG = "CLOSE_ARES_PALACE_DIALOG",
  OPEN_ARES_PALACE_INFO_DIALOG = "OPEN_ARES_PALACE_INFO_DIALOG",
  CLOSE_ARES_PALACE_INFO_DIALOG = "CLOSE_ARES_PALACE_INFO_DIALOG",
  OPEN_ARES_PALACE_INFO_LOG_DIALOG = "OPEN_ARES_PALACE_INFO_LOG_DIALOG",
  UPDATE_ARES_PALACE_DIALOG = "UPDATE_ARES_PALACE_DIALOG",
  FIGHT_ARES_PALACE_FINISH = "FIGHT_ARES_PALACE_FINISH",
  OPEN_ARES_PALACE_SUCC_DIALOG = "FIGHT_ARES_PALACE_SUCC",

  //----------战神殿 end -------------------

  //------------ SceneManager start --------------
  ONE_SECOND_TIME_LOOP = "ONE_SECOND_TIME_LOOP",
  TWO_SECOND_TIME_LOOP = "TWO_SECOND_TIME_LOOP",
  ONE_MINUTE_TIME_LOOP = "ONE_MINUTE_TIME_LOOP",
  //------------ SceneManager end --------------

  //----------战场主宰 start ---------
  OPEN_LARGE_PEAK_ENTER_DIALOG = "OPEN_LARGE_PEAK_ENTER_DIALOG",
  OPEN_LARGE_PEAK_DIALOG = "OPEN_LARGE_PEAK_DIALOG",
  CLOSE_LARGE_PEAK_DIALOG = "CLOSE_LARGE_PEAK_DIALOG",
  OPEN_LARGE_PEAK_GUESS_DIALOG = "OPEN_LARGE_PEAK_GUESS_DIALOG",
  OPEN_LARGE_PEAK_HISTORY_DIALOG = "OPEN_LARGE_PEAK_HISTORY_DIALOG",
  OPEN_LARGE_PEAK_MSG_SEND_DIALOG = "OPEN_LARGE_PEAK_MSG_SEND_DIALOG",
  OPEN_LARGE_PEAK_NOTICE_DIALOG = "OPEN_LARGE_PEAK_NOTICE_DIALOG",
  OPEN_LARGE_PEAK_SIGN_NOTICE_DIALOG = "OPEN_LARGE_PEAK_SIGN_NOTICE_DIALOG",
  OPEN_LARGE_PEAK_HISTORY_LEGEND_DIALOG = "OPEN_LARGE_PEAK_HISTORY_LEGEND_DIALOG",
  OPEN_LARGE_PEAK_RANK_DIALOG = "OPEN_LARGE_PEAK_RANK_DIALOG",
  OPEN_LARGE_PEAK_RANK_REWARD_DIALOG = "OPEN_LARGE_PEAK_RANK_REWARD_DIALOG",
  OPEN_LARGE_PEAK_SCHEDULE_DIALOG = "OPEN_LARGE_PEAK_SCHEDULE_DIALOG",
  OPEN_LARGE_PEAK_NOT_OPEN_DIALOG = "OPEN_LARGE_PEAK_NOT_OPEN_DIALOG",
  OPEN_LARGE_ADVANCE_LINE_UP_DIALOG = "OPEN_ADVANCE_LINE_UP_DIALOG",
  UPDATE_LARGE_PEAK_INFO = "UPDATE_LARGE_PEAK_INFO",
  UPDATE_LARGE_PEAK_INFO_OPP = "UPDATE_LARGE_PEAK_INFO_OPP",
  UPDATE_LARGE_PEAK_INFO_MEMBER = "UPDATE_LARGE_PEAK_INFO_MEMBER",
  UPDATE_LARGE_PEAK_INFO_LOOK_MEMBER = "UPDATE_LARGE_PEAK_INFO_LOOK_MEMBER",
  UPDATE_LARGE_PEAK_INFO_BATTLE = "UPDATE_LARGE_PEAK_INFO_BATTLE",
  UPDATE_LARGE_PEAK_INFO_BET = "UPDATE_LARGE_PEAK_INFO_BET",
  UPDATE_LARGE_PEAK_INFO_HISTORY = "UPDATE_LARGE_PEAK_INFO_HISTORY",
  UPDATE_LARGE_PEAK_INFO_GET_MSG = "UPDATE_LARGE_PEAK_INFO_GET_MSG",
  UPDATE_LARGE_PEAK_INFO_PERSONAL = "UPDATE_LARGE_PEAK_INFO_PERSONAL",
  REPLAY_LARGE_PEAK_BATTLE = "REPLAY_LARGE_PEAK_BATTLE",
  UPDATE_LARGE_PEAK_BATTLE_RESULT = "UPDATE_LARGE_PEAK_BATTLE_RESULT",
  CHANGE_LARGE_PEAK_SUB_TAB = "CHANGE_LARGE_PEAK_SUB_TAB",
  CHECK_LARGE_PEAK_LINE_UP_STATUS = "CHECK_LARGE_PEAK_LINE_UP_STATUS",
  CLOSE_LARGE_PEAK_ZONE_BOX = "CLOSE_LARGE_PEAK_ZONE_BOX",
  OPEN_LARGE_PEAK_SIKP_DIALOG = "OPEN_LARGE_PEAK_SIKP_DIALOG",
  CLOSE_LARGE_PEAK_SIKP_DIALOG = "CLOSE_LARGE_PEAK_SIKP_DIALOG",
  UPDATA_LARGE_PEAK_GROUP_ID = "UPDATA_LARGE_PEAK_GROUP_ID",
  //----------战场主宰 end ---------

  //----------诸神战场 start ------------
  UPDATE_DOMINATE_PVP_INFO = "UPDATE_DOMINATE_PVP_INFO",
  UPDATE_DOMINATE_PVP_NEW_SEASON = "UPDATE_DOMINATE_PVP_NEW_SEASON",
  UPDATE_DOMINATE_PVP_PASSPORT_INFO = "UPDATE_DOMINATE_PVP_PASSPORT_INFO",
  UPDATE_DOMINATE_PVP_GRADE_REWARD = "UPDATE_DOMINATE_PVP_GRADE_REWARD",
  UPDATE_DOMINATE_PVP_MISSION_LIST = "UPDATE_DOMINATE_PVP_MISSION_LIST",
  OPEN_DOMINATE_PVP_GRADE_SHOW_DIALOG = "OPEN_DOMINATE_PVP_GRADE_SHOW_DIALOG",
  OPEN_DOMINATE_PVP_REWARD_DIALOG = "OPEN_DOMINATE_PVP_REWARD_DIALOG",
  CLOSE_DOMINATE_PVP_REWARD_DIALOG = "CLOSE_DOMINATE_PVP_REWARD_DIALOG",
  OPEN_DOMINATE_PVP_SCORE_CHANGE_DIALOG = "OPEN_DOMINATE_PVP_SCORE_CHANGE_DIALOG",
  OPEN_DOMINATE_PVP_LOGS_DIALOG = "OPEN_DOMINATE_PVP_LOGS_DIALOG",
  OPEN_DOMINATE_PVP_RANK_DIALOG = "OPEN_DOMINATE_PVP_RANK_DIALOG",
  OPEN_DOMINATE_PVP_NEW_SEASON_DIALOG = "OPEN_DOMINATE_PVP_NEW_SEASON_DIALOG",
  OPEN_DOMINATE_PVP_RANDOM_DIALOG = "OPEN_DOMINATE_PVP_RANDOM_DIALOG",
  CLOSE_DOMINATE_PVP_RANDOM_DIALOG = "CLOSE_DOMINATE_PVP_RANDOM_DIALOG",
  OPEN_DOMINATE_SHOP_BUG_TIP_DIALOG = "OPEN_DOMINATE_SHOP_BUG_TIP_DIALOG",
  OPEN_DOMINATE_PVP_PASSPORT_DIALOG = "OPEN_DOMINATE_PVP_PASSPORT_DIALOG",
  OPEN_DOMINATE_PVP_PASSPORT_UNLOCK_TIPS_DIALOG = "OPEN_DOMINATE_PVP_PASSPORT_UNLOCK_TIPS_DIALOG",
  OPEN_DOMINATE_PVP_LARGE_PEAK_NOTICE_DIALOG = "OPEN_DOMINATE_PVP_LARGE_PEAK_NOTICE_DIALOG",
  OPEN_DOMINATE_PVP_ZONE_PREVIEW_DIALOG = "OPEN_DOMINATE_PVP_ZONE_PREVIEW_DIALOG",
  OPEN_DOMINATE_PVP_STAGE_CHANGE_DIALOG = "OPEN_DOMINATE_PVP_STAGE_CHANGE_DIALOG",
  OPEN_DOMINATE_PVP_LIMIT_TIP_DIALOG = "OPEN_DOMINATE_PVP_LIMIT_TIP_DIALOG",
  FIGHT_DOMINATE_PVP_FINISH = "FIGHT_DOMINATE_PVP_FINISH",
  //----------诸神战场 end ------------

  //--------- 国战通用相关  ---------------------

  M_WARS_CITY_TROOP = "M_WARS_CITY_TROOP",
  M_WARS_CAMP_INFO = "M_WARS_CAMP_INFO",
  M_WARS_CAMP_UPDATE = "M_WARS_CAMP_UPDATE",
  M_WARS_MARCH_LIST = "M_WARS_MARCH_LIST",
  M_WARS_MARCH_POS = "M_WARS_MARCH_POS",
  M_WARS_MARCH_FIGHT = "M_WARS_MARCH_FIGHT",
  M_WARS_MARCH_DOWN = "M_WARS_MARCH_DOWN",
  M_WARS_CITY_INFO = "M_WARS_CITY_INFO",
  M_WARS_UPDATE_CITY = "M_WARS_UPDATE_CITY",
  M_WARS_CITY_DEATH = "M_WARS_CITY_DEATH",
  M_WARS_TEAM_SINGLE = "M_WARS_TEAM_SINGLE",
  M_WARS_TEAM_INFO = "M_WARS_TEAM_INFO",
  M_WARS_TEAM_UPDATE = "M_WARS_TEAM_UPDATE",
  M_WARS_FAMILY_CAMP = "M_WARS_FAMILY_CAMP",
  M_WARS_FAMILY_CITY = "M_WARS_FAMILY_CITY",
  M_WARS_FAMILY_LIST = "M_WARS_FAMILY_LIST",
  M_WARS_FAMILY_CITY_FETCH = "M_WARS_FAMILY_CITY_FETCH",
  M_WARS_CONSCRIPTION_SEND = "M_WARS_CONSCRIPTION_SEND",
  M_WARS_CONSCRIPTION_INFO = "M_WARS_CONSCRIPTION_INFO",


  //---------国战通用相关 end-------------------

  //每日百抽
  M_DAILY_FULI_INFO_TOC = "M_DAILY_FULI_INFO_TOC",



  //新的春节活动
  OPEN_SPRING_SHOP_DENG_MI_DIALOG = "OPEN_SPRING_SHOP_DENG_MI_DIALOG",
  COLSE_SPRING_SHOP_DENG_MI_DIALOG = "COLSE_SPRING_SHOP_DENG_MI_DIALOG",


  UPDATE_SPRING_ACT_INFO = "UPDATE_SPRING_ACT_INFO",
  UPDATE_SPRING_ACT_GIFT = "UPDATE_SPRING_ACT_GIFT",


  //融合天坛
  OPEN_GENERAL_MANSION_DIALOG = "OPEN_GENERAL_MANSION_DIALOG",
  CLOSE_GENERAL_MANSION_DIALOG = "CLOSE_GENERAL_MANSION_DIALOG",
  OPEN_HERO_RESET_DIALOG = "OPEN_HERO_RESET_DIALOG",
  CLOSE_HERO_RESET_DIALOG = "CLOSE_HERO_RESET_DIALOG",
  UPDATE_HERO_RESET_DIALOG = "UPDATE_HERO_RESET_DIALOG",
  UPDATE_HERO_RESET_HIGH_PREVIEW = "UPDATE_HERO_RESET_HIGH_PREVIEW",
  UPDATE_HERO_LEGENDS_DIALOG = "UPDATE_HERO_LEGENDS_DIALOG",
  UPDATE_HERO_RECYCLE_TIMES = "UPDATE_HERO_RECYCLE_TIMES",
  SELECT_HERO_LEGENDS = "SELECT_HERO_LEGENDS",
  OPEN_HERO_LEGENDS_SELECT_DIALOG = "OPEN_HERO_LEGENDS_SELECT_DIALOG",
  OPEN_HERO_SHENGGE_CHANGE_TIPS_DIALOG = "OPEN_HERO_SHENGGE_CHANGE_TIPS_DIALOG",
  OPEN_HERO_CHANGE_SELECT_DIALOG = "OPEN_HERO_CHANGE_SELECT_DIALOG",
  CLOSE_HERO_CHANGE_SELECT_DIALOG = "CLOSE_HERO_CHANGE_SELECT_DIALOG",
  OPEN_HERO_CHANGE_SUCCESS_DIALOG = "OPEN_HERO_CHANGE_SUCCESS_DIALOG",
  CLOSE_HERO_CHANGE_SUCCESS_DIALOG = "CLOSE_HERO_CHANGE_SUCCESS_DIALOG",
  OPEN_HERO_RESET_HGIH_SELECT_DIALOG = "OPEN_HERO_RESET_HGIH_SELECT_DIALOG",
  CLOSE_HERO_RESET_HGIH_SELECT_DIALOG = "CLOSE_HERO_RESET_HGIH_SELECT_DIALOG",
  OPEN_HERO_RESET_HGIH_CHECK_DIALOG = "OPEN_HERO_RESET_HGIH_CHECK_DIALOG",
  CLOSE_HERO_RESET_HGIH_CHECK_DIALOG = "CLOSE_HERO_RESET_HGIH_CHECK_DIALOG",
  OPEN_HERO_RESET_HGIH_SUCCESS_DIALOG = "OPEN_HERO_RESET_HGIH_SUCCESS_DIALOG",
  OPEN_HERO_ONE_KEY_COMPOUND_DIALOG = "OPEN_HERO_ONE_KEY_COMPOUND_DIALOG",
  OPEN_HERO_ONE_KEY_COMPLETE_DIALOG = "OPEN_HERO_ONE_KEY_COMPLETE_DIALOG",
  //工坊
  OPEN_EQUIP_WORK_SHOW_DIALOG = "OPEN_EQUIP_WORK_SHOW_DIALOG",
  OPEN_EQUIP_COMPOSE_LOGS_DIALOG = "OPEN_EQUIP_COMPOSE_LOGS_DIALOG",
  WORKSHOW_EQUIP_RED = "WORKSHOW_EQUIP_LOGS",
  UPDATE_BINGFU_COMPOSE_INFO = "UPDATE_BINGFU_COMPOSE_INFO",
  UPDATE_BINGFU_REFINE_INFO = "UPDATE_BINGFU_REFINE_INFO",
  OPEN_BINGFU_SELECT_DIALOG = "OPEN_BINGFU_SELECT_DIALOG",
  OPEN_BINGFU_COMPOSE_TIPS_DIALOG = "OPEN_BINGFU_COMPOSE_TIPS_DIALOG",
  OPEN_BINGFU_COMPOSE_REWARD_DIALOG = "OPEN_BINGFU_COMPOSE_REWARD_DIALOG",
  OPEN_BINGFA_REFINE_DIALOG = "OPEN_BINGFA_REFINE_DIALOG",
  OPEN_BINGFA_REFINE_TIPS_DIALOG = "OPEN_BINGFA_REFINE_TIPS_DIALOG",
  OPEN_EQUIP_AUTO_COMPOSE_DIALOG = "OPEN_EQUIP_AUTO_COMPOSE_DIALOG",
  UPDATE_GODEQUIP_COMPOSE_INFO = "UPDATE_GODEQUIP_COMPOSE_INFO",
  UPDATE_GODEQUIP_COMPOSE_SELECT_INFO = "UPDATE_GODEQUIP_COMPOSE_SELECT_INFO",
  OPEN_GODEQUIP_SELECT_DIALOG = "OPEN_GODEQUIP_SELECT_DIALOG",
  OPEN_GODEQUIP_ENCHANT_DIALOG = "OPEN_GODEQUIP_ENCHANT_DIALOG",
  OPEN_GODEQUIP_ENCHANT_TIPS_DIALOG = "OPEN_GODEQUIP_ENCHANT_TIPS_DIALOG",
  UPDATE_GODEQUIP_ENCHANT_INFO = "UPDATE_GODEQUIP_ENCHANT_INFO",


  //显示标题
  SHOW_DEBUG_TITLE = "SHOW_DEBUG_TITLE",

  //神裔英雄试炼
  OPEN_GOD_TRIAL_DIALOG = "OPEN_GOD_TRIAL_DIALOG",
  OPEN_GOD_TRIAL_BUFF_SELECT_DIALOG = "OPEN_GOD_TRIAL_BUFF_SELECT_DIALOG",
  OPEN_GOD_TRIAL_REWARDS_PREVIEW_DIALOG = "OPEN_GOD_TRIAL_REWARDS_PREVIEW_DIALOG",
  OPEN_GOD_TRIAL_RANK_DIALOG = "OPEN_GOD_TRIAL_RANK_DIALOG",
  GOD_TRIAL_FIGHT_FINISH = "GOD_TRIAL_FIGHT_FINISH",
  UPDATE_GOD_TRIAL_INFO = "UPDATE_GOD_TRIAL_INFO",
  UPDATE_GOD_TRIAL_FETCH = "UPDATE_GOD_TRIAL_FETCH",
  UPDATE_GOD_TRIAL_BUFF = "UPDATE_GOD_TRIAL_BUFF",

  //勇闯异境
  OPEN_PASS_BEHEAD_DIALOG = "OPEN_PASS_BEHEAD_DIALOG",
  OPEN_PASS_BEHEAD_SELECT_MODEL_DIALOG = "OPEN_PASS_BEHEAD_SELECT_MODEL_DIALOG",
  OPEN_PASS_BEHEAD_BATTLE_INFO_DIALOG = "OPEN_PASS_BEHEAD_BATTLE_INFO_DIALOG",
  OPEN_PASS_BEHEAD_CRUSH_FIGHT_DIALOG = "OPEN_PASS_BEHEAD_CRUSH_FIGHT_DIALOG",
  OPEN_PASS_BEHEAD_REVIVE_DIALOG = "OPEN_PASS_BEHEAD_REVIVE_DIALOG",
  OPEN_PASS_BEHEAD_REWARDS_DIALOG = "OPEN_PASS_BEHEAD_REWARDS_DIALOG",
  UPDATE_PASS_BEHEAD_INFO = "UPDATE_PASS_BEHEAD_INFO",
  UPDATE_PASS_BEHEAD_BATTLE = "UPDATE_PASS_BEHEAD_BATTLE",


  //-------赤壁之战  start------------
  OPEN_RED_CLIFF_BOSS_DIALOG = "OPEN_RED_CLIFF_BOSS_DIALOG",
  CLOSE_RED_CLIFF_BOSS_DIALOG = "CLOSE_RED_CLIFF_BOSS_DIALOG",
  OPEN_RED_CLIFF_NATION_DIALOG = "OPEN_RED_CLIFF_NATION_DIALOG",
  CLOSE_RED_CLIFF_NATION_DIALOG = "CLOSE_RED_CLIFF_NATION_DIALOG",
  UPDATE_RED_CLIFF_INFO = "UPDATE_RED_CLIFF_INFO",
  FIGHT_RED_CLIFF_FINISH = "FIGHT_RED_CLIFF_FINISH",
  RED_CLIFF_BUY_TIMES = "RED_CLIFF_BUY_TIMES",
  FIGHT_RED_CLIFF_NEXT_FLOOR = "FIGHT_RED_CLIFF_NEXT_FLOOR",
  RED_CLIFF_NATION_CLICK = "RED_CLIFF_NATION_CLICK",
  OPEN_RED_CLIFF_REWARD_PRE_DIALOG = "OPEN_RED_CLIFF_REWARD_PRE_DIALOG",
  OPEN_RED_CLIFF_REWARD_PRE_TIP_DIALOG = "OPEN_RED_CLIFF_REWARD_PRE_TIP_DIALOG",
  //------ 赤壁之战 end -----IP

  //-------试炼塔  start------------
  OPEN_EPIC_BATTLE_DIALOG = "OPEN_EPIC_BATTLE_DIALOG",
  //战旗
  SELECT_HERO_ACTIVE_WAR_FLAG = "SELECT_HERO_ACTIVE_WAR_FLAG",
  OPEN_SELECT_ACTIVE_WAR_FLAG_DIALOG = "OPEN_SELECT_ACTIVE_WAR_FLAG_DIALOG",
  UPDATE_WAR_FLAG_INFO = "UPDATE_WAR_FLAG_INFO",
  SELECT_WAR_FLAG_NATION = "SELECT_WAR_FLAG_NATION",
  OPEN_ACTIVE_WAR_FLAG_DIALOG = "OPEN_ACTIVE_WAR_FLAG_DIALOG",
  UPDATE_WAR_FLAG_LEVEL_STAGE_INFO = "UPDATE_WAR_FLAG_LEVEL_STAGE_INFO",
  UPDATE_WAR_FLAG_LEVEL_UPGRADE = "UPDATE_WAR_FLAG_LEVEL_UPGRADE",
  WAR_FLAG_SELECT_TAB = "WAR_FLAG_SELECT_TAB",
  OPEN_WAR_FLAG_STAGE_PRE_DIALOG = "OPEN_WAR_FLAG_STAGE_PRE_DIALOG",
  OPEN_LVUP_STAGE_WAR_FLAG_DIALOG = "OPEN_LVUP_STAGE_WAR_FLAG_DIALOG",
  CLOSE_LVUP_STAGE_WAR_FLAG_DIALOG = "CLOSE_LVUP_STAGE_WAR_FLAG_DIALOG",
  OPEN_WAR_FLAG_RESET_DIALOG = "OPEN_WAR_FLAG_RESET_DIALOG",
  OPEN_WAR_FLAG_LINK_DIALOG = "OPEN_WAR_FLAG_LINK_DIALOG",
  CLOSE_WAR_FLAG_LINK_DIALOG = "CLOSE_WAR_FLAG_LINK_DIALOG",
  OPEN_WAR_FLAG_LINK_ATTR_DIALOG = "OPEN_WAR_FLAG_LINK_ATTR_DIALOG",
  CLOSE_WAR_FLAG_LINK_ATTR_DIALOG = "CLOSE_WAR_FLAG_LINK_ATTR_DIALOG",
  OPEN_WAR_FLAG_LINK_SELECT_DIALOG = "OPEN_WAR_FLAG_LINK_SELECT_DIALOG",
  CLOSE_WAR_FLAG_LINK_SELECT_DIALOG = "CLOSE_WAR_FLAG_LINK_SELECT_DIALOG",
  SELECT_WAR_FLAG_LINK_NATION = "SELECT_WAR_FLAG_LINK_NATION",
  UPDATE_WAR_FLAG_LINK_NATION = "UPDATE_WAR_FLAG_LINK_NATION",
  UPDATE_WAR_FLAG_LINK_POWER = "UPDATE_WAR_FLAG_LINK_POWER",
  OPEN_WAR_FLAG_LINK_SUCCESS_DIALOG = "OPEN_WAR_FLAG_LINK_SUCCESS_DIALOG",
  OPEN_WAR_FLAG_RESET_LINK_DIALOG = "OPEN_WAR_FLAG_RESET_LINK_DIALOG",
  OPEN_WAR_FLAG_UNLOCK_LINK_TIP_DIALOG = "OPEN_WAR_FLAG_UNLOCK_LINK_TIP_DIALOG",
  CLOSE_WAR_FLAG_UNLOCK_LINK_TIP_DIALOG = "CLOSE_WAR_FLAG_UNLOCK_LINK_TIP_DIALOG",
  OPEN_WAR_FLAG_EXCHANGE_DIALOG = "OPEN_WAR_FLAG_EXCHANGE_DIALOG",
  CLOSE_WAR_FLAG_EXCHANGE_DIALOG = "CLOSE_WAR_FLAG_EXCHANGE_DIALOG",
  UPDATE_WAR_FLAG_EXCHANGE_NATION = "UPDATE_WAR_FLAG_EXCHANGE_NATION",

  UPDATE_TEST_TOWER_INFO = "UPDATE_TEST_TOWER_INFO",
  UPDATE_TEST_TOWER_INFO_FLOOR = "UPDATE_TEST_TOWER_INFO_FLOOR",
  OPEN_TEST_TOWER_BOSS_DIALOG = "OPEN_TEST_TOWER_BOSS_DIALOG",
  CLOSE_TEST_TOWER_BOSS_DIALOG = "CLOSE_TEST_TOWER_BOSS_DIALOG",
  OPEN_TEST_TOWER_DIALOG = "OPEN_TEST_TOWER_DIALOG",
  CLOSE_TEST_TOWER_DIALOG = "CLOSE_TEST_TOWER_DIALOG",
  OPEN_SP_TEST_TOWER_DIALOG = "OPEN_SP_TEST_TOWER_DIALOG",
  FIGHT_TEST_TOWER_FINISH = "FIGHT_TEST_TOWER_FINISH",
  FIGHT_TEST_TOWER_NEXT_FLOOR = "FIGHT_TEST_TOWER_NEXT_FLOOR",
  OPEN_TEST_TOWER_REPLAY_DIALOG = "OPEN_TEST_TOWER_REPLAY_DIALOG",
  OPEN_TEST_TOWER_RERWARD_DIALOG = "OPEN_TEST_TOWER_RERWARD_DIALOG",
  CLOSE_TEST_TOWER_RERWARD_DIALOG = "CLOSE_TEST_TOWER_RERWARD_DIALOG",
  UPDATE_TEST_TOWER_FETCH = "UPDATE_TEST_TOWER_FETCH",
  TEST_TOWER_BUY_TIMES = "TEST_TOWER_BUY_TIMES",
  OPEN_TEST_TOWER_SKIP_DIALOG = "OPEN_TEST_TOWER_SKIP_DIALOG",
  OPEN_TEST_TOWER_SHOP_RED = "OPEN_TEST_TOWER_SHOP_RED",
  OPEN_TEST_TOWER_DOUBLE_BOSS_DIALOG = "OPEN_TEST_TOWER_DOUBLE_BOSS_DIALOG",
  CLOSE_TEST_TOWER_DOUBLE_BOSS_DIALOG = "CLOSE_TEST_TOWER_DOUBLE_BOSS_DIALOG",
  OPEN_TEST_TOWER_THREE_BOSS_DIALOG = "OPEN_TEST_TOWER_THREE_BOSS_DIALOG",
  CLOSE_TEST_TOWER_THREE_BOSS_DIALOG = "CLOSE_TEST_TOWER_THREE_BOSS_DIALOG",
  OPEN_TEST_TOWER_SWEEP_TIP_DIALOG = "OPEN_TEST_TOWER_SWEEP_TIP_DIALOG",
  CLOSE_TEST_TOWER_SWEEP_TIP_DIALOG = "CLOSE_TEST_TOWER_SWEEP_TIP_DIALOG",

  UPDATE_MONSTER_GROUP_POWER = "UPDATE_MONSTER_GROUP_POWER",
  //------ 试炼塔 end -----
  //------ 种族塔 star -----
  FIGHT_CROSS_TEST_TOWER_NEXT_FLOOR = "FIGHT_CROSS_TEST_TOWER_NEXT_FLOOR",
  OPEN_CROSS_TEST_TOWER_NATION_DIALOG = "OPEN_CROSS_TEST_TOWER_NATION_DIALOG",
  CLOSE_CROSS_TEST_TOWER_NATION_DIALOG = "CLOSE_CROSS_TEST_TOWER_NATION_DIALOG",
  CROSS_TEST_TOWER_NATION_CLICK = "CROSS_TEST_TOWER_NATION_CLICK",

  OPEN_CROSS_TEST_TOWER_DIALOG = "OPEN_CROSS_TEST_TOWER_DIALOG",
  CLOSE_CROSS_TEST_TOWER_DIALOG = "CLOSE_CROSS_TEST_TOWER_DIALOG",
  OPEN_CROSS_TEST_TOWER_BOSS_DIALOG = "OPEN_CROSS_TEST_TOWER_BOSS_DIALOG",
  CLOSE_CROSS_TEST_TOWER_BOSS_DIALOG = "CLOSE_CROSS_TEST_TOWER_BOSS_DIALOG",
  OPEN_CROSS_TEST_TOWER_RERWARD_DIALOG = "OPEN_CROSS_TEST_TOWER_RERWARD_DIALOG",
  CLOSE_CROSS_TEST_TOWER_RERWARD_DIALOG = "CLOSE_CROSS_TEST_TOWER_RERWARD_DIALOG",
  OPEN_CROSS_TEST_TOWER_REPLAY_DIALOG = "OPEN_CROSS_TEST_TOWER_REPLAY_DIALOG",
  UPDATE_CROSS_TEST_TOWER_INFO = "UPDATE_CROSS_TEST_TOWER_INFO",


  OPEN_CROSS_TEST_TOWER_RANK_DIALOG = "OPEN_CROSS_TEST_TOWER_RANK_DIALOG",
  CLOSE_CROSS_TEST_TOWER_RANK_DIALOG = "CLOSE_CROSS_TEST_TOWER_RANK_DIALOG",


  //------ 种族塔 end -----
  UPDATE_HERO_RESET_SELECT = "UPDATE_HERO_RESET_SELECT",
  UPDATE_HERO_CHANGE_SELECT = "UPDATE_HERO_CHANGE_SELECT",
  UPDATE_HERO_RESET_HIGH_SELECT = "UPDATE_HERO_RESET_HIGH_SELECT",

  OPEN_TEST_TOWER_AUTO_SUCC_DIALOG = "OPEN_TEST_TOWER_AUTO_SUCC_DIALOG",
  UNDATE_TEST_TOWER_DOUBLE_BOSS_DIALOG = "UNDATE_TEST_TOWER_DOUBLE_BOSS_DIALOG",
  UNDATE_TEST_TOWER_THREE_BOSS_DIALOG = "UNDATE_TEST_TOWER_THREE_BOSS_DIALOG",

  OPEN_HERO_RECYCLE_NEW_DIALOG = "OPEN_HERO_RECYCLE_NEW_DIALOG",
  OPEN_HERO_RECYCLE_NEW_CHECK_DIALOG = "OPEN_HERO_RECYCLE_NEW_CHECK_DIALOG",
  OPEN_HERO_RECYCLE_DEBRIS_NEW_CHECK_DIALOG = "OPEN_HERO_RECYCLE_DEBRIS_NEW_CHECK_DIALOG",
  OPEN_HERO_RECYCLE_NEW_SIMPLE_SHOW_DIALOG = "OPEN_HERO_RECYCLE_NEW_SIMPLE_SHOW_DIALOG",
  OPEN_HERO_RECYCLE_DEBRIS_NEW_TIPS_DIALOG = "OPEN_HERO_RECYCLE_DEBRIS_NEW_TIPS_DIALOG",
  OPEN_HERO_RECYCLE_NEW_SIMPLE_SHOW_DIALOG2 = "OPEN_HERO_RECYCLE_NEW_SIMPLE_SHOW_DIALOG2",
  OPEN_HERO_RECYCLE_SKIN_CHECK_DIALOG = "OPEN_HERO_RECYCLE_SKIN_CHECK_DIALOG",
  //----------每日礼包 start ---------
  UPDATE_SYS_DAILY_REWARD = "UPDATE_SYS_DAILY_REWARD",
  UPDATE_SYS_DAILY_REWARD_RED_POINT = "UPDATE_SYS_DAILY_REWARD_RED_POINT",
  //----------每日礼包 end -----------

  OPEN_PROGRESS_GIFT_DIALOG = "OPEN_PROGRESS_GIFT_DIALOG",
  OPEN_PROGRESS_GIFT_TIPS_DIALOG = "OPEN_PROGRESS_GIFT_TIPS_DIALOG",
  UPDATE_PROGRESS_GIFT_INFO = "UPDATE_PROGRESS_GIFT_INFO",

  //----------七日任务 start ---------
  OPEN_SEVENGOAL_GIFT_TIPS_DIALOG = "OPEN_SEVENGOAL_GIFT_TIPS_DIALOG",
  OPEN_SEVENGOAL_RECHARGE_DIALOG = "OPEN_SEVENGOAL_RECHARGE_DIALOG",
  OPEN_SEVENGOAL_GIFT_PREVIEW_DIALOG = "OPEN_SEVENGOAL_GIFT_PREVIEW_DIALOG",
  //----------七日任务 end ---------

  //----------冠军赛 start ---------
  OPEN_QXZL_DIALOG = "OPEN_QXZL_DIALOG",
  CLOSE_QXZL_DIALOG = "CLOSE_QXZL_DIALOG",
  OPEN_QXZL_GUESS_DIALOG = "OPEN_QXZL_GUESS_DIALOG",
  OPEN_QXZL_HISTORY_DIALOG = "OPEN_QXZL_HISTORY_DIALOG",
  OPEN_QXZL_LAST_RANK_DIALOG = "OPEN_QXZL_LAST_RANK_DIALOG",
  OPEN_QXZL_LAST_INFO_DIALOG = "OPEN_QXZL_LAST_INFO_DIALOG",
  OPEN_QXZL_MSG_SEND_DIALOG = "OPEN_QXZL_MSG_SEND_DIALOG",
  OPEN_QXZL_ADVANCE_NOTICE_DIALOG = "OPEN_QXZL_ADVANCE_NOTICE_DIALOG",
  CLOSE_QXZL_ADVANCE_NOTICE_DIALOG = "CLOSE_QXZL_ADVANCE_NOTICE_DIALOG",
  UPDATE_QXZL_INFO = "UPDATE_QXZL_INFO",
  UPDATE_QXZL_INFO_OPP = "UPDATE_QXZL_INFO_OPP",
  UPDATE_QXZL_INFO_MEMBER = "UPDATE_QXZL_INFO_MEMBER",
  UPDATE_QXZL_INFO_LOOK_MEMBER = "UPDATE_QXZL_INFO_LOOK_MEMBER",
  UPDATE_QXZL_INFO_BATTLE = "UPDATE_QXZL_INFO_BATTLE",
  UPDATE_QXZL_INFO_BET = "UPDATE_QXZL_INFO_BET",
  UPDATE_QXZL_INFO_HISTORY = "UPDATE_QXZL_INFO_HISTORY",
  UPDATE_QXZL_INFO_GET_MSG = "UPDATE_QXZL_INFO_GET_MSG",
  REPLAY_QXZL_BATTLE = "REPLAY_QXZL_BATTLE",
  UPDATE_QXZL_BATTLE_RESULT = "UPDATE_QXZL_BATTLE_RESULT",
  //----------冠军赛 end ---------

  //------------神装纷争 start -------------
  OPEN_CHALLENGE_CHAPTER_DIALOG = "OPEN_CHALLENGE_CHAPTER_DIALOG",
  CLOSE_CHALLENGE_CHAPTER_DIALOG = "CLOSE_CHALLENGE_CHAPTER_DIALOG",
  OPEN_CHALLENGE_CHAPTER_LIST_DIALOG = "OPEN_CHALLENGE_CHAPTER_LIST_DIALOG",
  CLOSE_CHALLENGE_CHAPTER_LIST_DIALOG = "CLOSE_CHALLENGE_CHAPTER_LIST_DIALOG",
  OPEN_CHALLENGE_CHAPTER_FIGHT_SUCC_DIALOG = "OPEN_CHALLENGE_CHAPTER_FIGHT_SUCC_DIALOG",
  OPEN_CHALLENGE_CHAPTER_DAILY_GIFT_DIALOG = "OPEN_CHALLENGE_CHAPTER_DAILY_GIFT_DIALOG",
  OPEN_CHALLENGE_CHAPTER_MONSTER_LOOK_DIALOG = "OPEN_CHALLENGE_CHAPTER_MONSTER_LOOK_DIALOG",
  OPEN_CHALLENGE_CHAPTER_ACC_STAR_REWARD_DIALOG = "OPEN_CHALLENGE_CHAPTER_ACC_STAR_REWARD_DIALOG",
  CLOSE_CHALLENGE_CHAPTER_ACC_STAR_REWARD_DIALOG = "CLOSE_CHALLENGE_CHAPTER_ACC_STAR_REWARD_DIALOG",
  OPEN_CHALLENGE_CHAPTER_ACC_STAR_REWARD_TIP_DIALOG = "OPEN_CHALLENGE_CHAPTER_ACC_STAR_REWARD_TIP_DIALOG",
  CHALLENGE_CHAPTER_FIGHT_FINISH = "CHALLENGE_CHAPTER_FIGHT_FINISH",
  UPDATE_CHALLENGE_CHAPTER_INFO = "UPDATE_CHALLENGE_CHAPTER_INFO",
  UPDATE_CHALLENGE_CHAPTER_SIMP_INFO_LIST = "UPDATE_CHALLENGE_CHAPTER_SIMP_INFO_LIST",
  REQUEST_CHALLENGE_CHAPTER_SELECT_CHAPTER = "REQUEST_CHALLENGE_CHAPTER_SELECT_CHAPTER",
  CHANGE_CHALLENGE_CHAPTER_SELECT_PASS = "CHANGE_CHALLENGE_CHAPTER_SELECT_PASS",
  //------------神装纷争 end ---------------

  //----------运河夺宝 star ------------
  OPNE_RIVER_TREASURE_DIALOG = "OPNE_RIVER_TREASURE_DIALOG",
  CLOSE_RIVER_TREASURE_DIALOG = "CLOSE_RIVER_TREASURE_DIALOG",
  OPNE_RIVER_TREASURE_COLLECT_GOODS_DIALOG = "OPNE_RIVER_TREASURE_COLLECT_GOODS_DIALOG",
  CLOSE_RIVER_TREASURE_COLLECT_GOODS_DIALOG = "CLOSE_RIVER_TREASURE_COLLECT_GOODS_DIALOG",
  UPDATE_RIVER_TREASURE_ROLE_INFO = "UPDATE_RIVER_TREASURE_ROLE_INFO",
  UPDATE_RIVER_TREASURE_CAL_TIME_INFO = "UPDATE_RIVER_TREASURE_CAL_TIME_INFO",
  UPDATE_RIVER_TREASURE_DISPATCH = "UPDATE_RIVER_TREASURE_DISPATCH",
  UPDATE_RIVER_TREASURE_OTHER_ROLE_INFO = "UPDATE_RIVER_TREASURE_OTHER_ROLE_INFO",
  OPNE_RIVER_TREASURE_OTHER_ROLE_LIST_DIALOG = "OPNE_RIVER_TREASURE_OTHER_ROLE_LIST_DIALOG",
  CLOSE_RIVER_TREASURE_OTHER_ROLE_LIST_DIALOG = "CLOSE_RIVER_TREASURE_OTHER_ROLE_LIST_DIALOG",
  OPNE_RIVER_TREASURE_REWARD_DIALOG = "OPNE_RIVER_TREASURE_REWARD_DIALOG",
  OPNE_RIVER_TREASURE_LOG_DIALOG = "OPNE_RIVER_TREASURE_LOG_DIALOG",
  OPNE_RIVER_TREASURE_WORKER_DIALOG = "OPNE_RIVER_TREASURE_WORKER_DIALOG",
  OPNE_RIVER_TREASURE_ENERGY_DETAIL_DIALOG = "OPNE_RIVER_TREASURE_ENERGY_DETAIL_DIALOG",
  OPNE_RIVER_TREASURE_PACK_DIALOG = "OPNE_RIVER_TREASURE_PACK_DIALOG",
  OPNE_RIVER_PRE_WORKER_DIALOG = "OPNE_RIVER_PRE_WORKER_DIALOG",
  UPDATE_RIVER_TREASURE_LOG_INFO = "UPDATE_RIVER_TREASURE_LOG_INFO",
  UPDATE_RIVER_TREASURE_INFO = "UPDATE_RIVER_TREASURE_INFO",
  UPDATE_RIVER_TREASURE_ROLE_EXIST = "UPDATE_RIVER_TREASURE_ROLE_EXIST",
  UPDATE_RIVER_TREASURE_PULL_LIST = "UPDATE_RIVER_TREASURE_PULL_LIST",
  OPNE_OTHER_ROLE_DISPATCH_TYPE = "OPNE_OTHER_ROLE_DISPATCH_TYPE",
  CLEAN_RIVER_TREASURE_BOX_TWEEN = "CLEAN_RIVER_TREASURE_BOX_TWEEN",
  UPDATE_ROLE_WORKER_INFO = "UPDATE_ROLE_WORKER_INFO",
  OPNE_RIVER_ATTRI_SHOW_DIALOG = "OPNE_RIVER_ATTRI_SHOW_DIALOG",
  OPNE_RIVER_TE_QUAN_DIALOG = "OPNE_RIVER_TE_QUAN_DIALOG",
  //----------运河夺宝 end ------------


  //------------千抽活动 start -------------
  OPEN_OPTION_LOTTERY_DIALOG = "OPEN_OPTION_LOTTERY_DIALOG",
  CLOSE_OPTION_LOTTERY_DIALOG = "CLOSE_OPTION_LOTTERY_DIALOG",
  OPEN_OPTION_LOTTERY_PREVIEW_REWARD_DIALOG = "OPEN_OPTION_LOTTERY_PREVIEW_REWARD_DIALOG",
  UPDATE_OPTION_LOTTERY = "UPDATE_OPTION_LOTTERY",

  //------------千抽活动 end ---------------

  //----------号码绑定start ---------
  OPEN_PHONE_NUMBER_BIND_DIALOG = "OPEN_PHONE_NUMBER_BIND_DIALOG",
  CLOSE_PHONE_NUMBER_BIND_DIALOG = "CLOSE_PHONE_NUMBER_BIND_DIALOG",
  //---------- 号码绑定end ---------

  //----------三系密令/征战日志 start ---------
  OPEN_WAR_LOG_DIALOG = "OPEN_WAR_LOG_DIALOG",
  CLOSE_WAR_LOG_DIALOG = "CLOSE_WAR_LOG_DIALOG",
  OPEN_SECRET_ORDER_DIALOG = "OPEN_SECRET_ORDER_DIALOG",
  CLOSE_SECRET_ORDER_DIALOG = "CLOSE_SECRET_ORDER_DIALOG",
  UNDATE_WAR_LOG_DIALOG = "UNDATE_WAR_LOG_DIALOG",
  UNDATE_SECRET_ORDER_DIALOG = "UNDATE_SECRET_ORDER_DIALOG",
  UNDATE_WAR_LOG_ITEM = "UNDATE_WAR_LOG_ITEM",
  UNDATE_SECRET_ORDER_ITEM = "UNDATE_SECRET_ORDER_ITEM",
  OPEN_SECRET_ORDER_RECHARGE_DIALOG = "OPEN_SECRET_ORDER_RECHARGE_DIALOG",
  OPEN_SCORE_SECRET_ORDER_RECHARGE_DIALOG = "OPEN_SCORE_SECRET_ORDER_RECHARGE_DIALOG",
  //----------三系密令/征战日志 end ---------

  //---------- 游戏剧情 start ----------
  OPEN_STORY_DIALOG = "OPEN_STORY_DIALOG",
  SHOE_STORY_BUBBLE = "SHOE_STORY_BUBBLE",
  SKIP_STORY = "SKIP_STORY",
  SHOW_STORY_SKIP_BTN = "SHOW_STORY_SKIP_BTN",
  //---------- 游戏剧情 end ----------


  //---------- 三系通行证 start ----------
  OPEN_PASSPORT_DIALOG = "OPEN_PASSPORT_DIALOG",
  CLOSE_PASSPORT_DIALOG = "CLOSE_PASSPORT_DIALOG",
  UPDATE_PASSPORT_INFO = "UPDATE_PASSPORT_INFO",
  UPDATE_PASSPORT_MISSION_D_INFO = "UPDATE_PASSPORT_MISSION_D_INFO",
  UPDATE_PASSPORT_MISSION_W_INFO = "UPDATE_PASSPORT_MISSION_W_INFO",
  UPDATE_PASSPORT_MISSION_E_INFO = "UPDATE_PASSPORT_MISSION_E_INFO",
  OPEN_PASSPORT_UNLOCK_TIPS_DIALOG = "OPEN_PASSPORT_UNLOCK_TIPS_DIALOG",
  //---------- 三系通行证 end ----------
  //---------- 三系通行证模块化 start ----------
  OPEN_ACT_MODS_PASSPORT_DIALOG = "OPEN_ACT_MODS_PASSPORT_DIALOG",
  CLOSE_ACT_MODS_PASSPORT_DIALOG = "CLOSE_ACT_MODS_PASSPORT_DIALOG",
  UPDATE_ACT_MODS_PASSPORT_INFO = "UPDATE_ACT_MODS_PASSPORT_INFO",
  UPDATE_ACT_MODS_PASSPORT_MISSION_D_INFO = "UPDATE_ACT_MODS_PASSPORT_MISSION_D_INFO",
  UPDATE_ACT_MODS_PASSPORT_MISSION_W_INFO = "UPDATE_ACT_MODS_PASSPORT_MISSION_W_INFO",
  UPDATE_ACT_MODS_PASSPORT_MISSION_E_INFO = "UPDATE_ACT_MODS_PASSPORT_MISSION_E_INFO",
  OPEN_ACT_MODS_PASSPORT_UNLOCK_TIPS_DIALOG = "OPEN_ACT_MODS_PASSPORT_UNLOCK_TIPS_DIALOG",
  OPEN_ACT_MODS_PASSPORT_UNLOCK_TIPS_2_DIALOG = "OPEN_ACT_MODS_PASSPORT_UNLOCK_TIPS_2_DIALOG",
  //---------- 三系通行证 end ----------


  //----------- 主题活动-名将招募 start ---------
  UPDATE_THEME_ACT_HERO_WISH_SELECT_HERO = "UPDATE_THEME_ACT_HERO_WISH_SELECT_HERO",
  //----------- 主题活动-名将招募 end -----------
  //---------- 主题活动 start ----------
  UPDATE_UP_STAR_REWARD_INFO = "UPDATE_UP_STAR_REWARD_INFO",
  UPDATE_STAR_PLAN_REWARD_INFO = "UPDATE_STAR_PLAN_REWARD_INFO",
  UPDATE_HERO_PASS_REWARD_INFO = "UPDATE_HERO_PASS_REWARD_INFO",
  UPDATE_HERO_PASS_MISSION_REWARD_INFO = "UPDATE_HERO_PASS_MISSION_REWARD_INFO",
  UPDATE_HERO_PASS_MISSION_REWARD_INFO_WITHPARAM = "UPDATE_HERO_PASS_MISSION_REWARD_INFO_WITHPARAM",
  OPEN_UPSTARPLAN_SELECT_DIALOG = "OPEN_UPSTARPLAN_SELECT_DIALOG",
  CLOSE_UPSTARPLAN_SELECT_DIALOG = "CLOSE_UPSTARPLAN_SELECT_DIALOG",
  OPEN_UPSTARPLAN_TIP_DIALOG = "OPEN_UPSTARPLAN_TIP_DIALOG",
  CLOSE_UPSTARPLAN_TIP_DIALOG = "CLOSE_UPSTARPLAN_TIP_DIALOG",
  OPEN_HERO_PASS_UNLOCK_TIPS_DIALOG = "OPEN_HERO_PASS_UNLOCK_TIPS_DIALOG",
  OPEN_HERO_PASS_MISSION_DIALOG = "OPEN_HERO_PASS_MISSION_DIALOG",
  CLOSE_HERO_PASS_MISSION_DIALOG = "CLOSE_HERO_PASS_MISSION_DIALOG",
  ACTIVITY_GIFT_UPDATE = "ACTIVITY_GIFT_UPDATE",
  //---------- 主题活动 end ----------

  /**名将通行证 模块化 7天任务 */
  UPDATE_ACT_MODS_HERO_PASS_REWARD_INFO = "UPDATE_ACT_MODS_HERO_PASS_REWARD_INFO",
  UPDATE_ACT_MODS_HERO_PASS_MISSION_REWARD_INFO = "UPDATE_ACT_MODS_HERO_PASS_MISSION_REWARD_INFO",
  UPDATE_ACT_MODS_HERO_PASS_MISSION_REWARD_INFO_WITHPARAM = "UPDATE_ACT_MODS_HERO_PASS_MISSION_REWARD_INFO_WITHPARAM",
  OPEN_ACT_MODS_HERO_PASS_UNLOCK_TIPS_DIALOG = "OPEN_ACT_MODS_HERO_PASS_UNLOCK_TIPS_DIALOG",
  OPEN_ACT_MODS_HERO_PASS_MISSION_DIALOG = "OPEN_ACT_MODS_HERO_PASS_MISSION_DIALOG",
  CLOSE_ACT_MODS_HERO_PASS_MISSION_DIALOG = "CLOSE_ACT_MODS_HERO_PASS_MISSION_DIALOG",
  CLICK_ACT_MODS_HERO_PASS_MISSION_PROCESS = "CLICK_ACT_MODS_HERO_PASS_MISSION_PROCESS",
  OPEN_ACT_MODS_HERO_PASS_REWARD_DIALOG = "OPEN_ACT_MODS_HERO_PASS_REWARD_DIALOG",
  CLOSE_ACT_MODS_HERO_PASS_REWARD_DIALOG = "CLOSE_ACT_MODS_HERO_PASS_REWARD_DIALOG",

  /**合服活动-合服狂欢 */
  UPDATE_REWARD_DOUBLED_INFO = "UPDATE_REWARD_DOUBLED_INFO",
  /**合服活动-合服活动预告 */
  OPEN_MOD_REWARD_DOUBLED_PREVIEW_DIALOG = "OPEN_MOD_REWARD_DOUBLED_PREVIEW_DIALOG",

  //---------- 汉中争夺战 start ----------
  OPEN_HZZD_DIALOG = "OPEN_HZZD_DIALOG",
  OPEN_HZZD_CAMP_DIALOG = "OPEN_HZZD_CAMP_DIALOG",
  OPEN_HZZD_GUESS_DIALOG = "OPEN_HZZD_GUESS_DIALOG",
  OPEN_HZZD_GUESS_INFO_DIALOG = "OPEN_HZZD_GUESS_INFO_DIALOG",
  OPEN_HZZD_CAMP_SITUATION_DIALOG = "OPEN_HZZD_CAMP_SITUATION_DIALOG",
  OPEN_HZZD_BATTLE_DETAIL_INFO__DIALOG = "OPEN_HZZD_BATTLE_DETAIL_INFO__DIALOG",
  OPEN_HZZD_BATTLE_STATISTICS_DIALOG = "OPEN_HZZD_BATTLE_STATISTICS_DIALOG",
  OPEN_HZZD_HISTORY_DIALOG = "OPEN_HZZD_HISTORY_DIALOG",
  OPEN_HZZD_NOTICE_DIALOG = "OPEN_HZZD_NOTICE_DIALOG",
  OPEN_HZZD_SCHEDULE_DIALOG = "OPEN_HZZD_SCHEDULE_DIALOG",
  OPEN_HZZD_STRATEGY_DIALOG = "OPEN_HZZD_STRATEGY_DIALOG",
  OPEN_HZZD_ACHIEVEMENT_DIALOG = "OPEN_HZZD_ACHIEVEMENT_DIALOG",
  OPEN_HZZD_TRAVEL_EVENT_DIALOG = "OPEN_HZZD_TRAVEL_EVENT_DIALOG",

  UPDATE_HZZD_INFO = "UPDATE_HZZD_INFO",
  UPDATE_HZZD_TEAM_INFO = "UPDATE_HZZD_TEAM_INFO",
  UPDATE_HZZD_INFO_HISTORY = "UPDATE_HZZD_INFO_HISTORY",
  UPDATE_HZZD_INFO_OPP = "UPDATE_HZZD_INFO_OPP",
  UPDATE_HZZD_INFO_BET = "UPDATE_HZZD_INFO_BET",
  UPDATE_HZZD_ROUTE_INFO = "UPDATE_HZZD_ROUTE_INFO",
  UPDATE_HZZD_ROUTE_SELECT_INFO = "UPDATE_HZZD_ROUTE_SELECT_INFO",
  UPDATE_HZZD_BATTLE_INFO = "UPDATE_HZZD_BATTLE_INFO",
  UPDATE_HZZD_LOOK_KILL_INFO = "UPDATE_HZZD_LOOK_KILL_INFO",
  UPDATE_HZZD_FETCH_INFO = "UPDATE_HZZD_FETCH_INFO",
  UPDATE_HZZD_MISSION_INFO = "UPDATE_HZZD_MISSION_INFO",
  //---------- 汉中争夺战 end ----------

  //---------- 神装秘宝 start ------------
  OPEN_GOD_EQUIP_TREASURE_DIALOG = "OPEN_GOD_EQUIP_TREASURE_DIALOG",
  CLOSE_GOD_EQUIP_TREASURE_DIALOG = "CLOSE_GOD_EQUIP_TREASURE_DIALOG",
  OPEN_GOD_EQUIP_TREASURE_LOG_DIALOG = "OPEN_GOD_EQUIP_TREASURE_LOG_DIALOG",
  CHECK_GOD_EQUIP_TREASURE_RED_POINT = "CHECK_GOD_EQUIP_TREASURE_RED_POINT",
  //---------- 神装秘宝 end --------------

  //模块化活动
  M_MODULAR_ACTIVITY_INFO_TOC = "M_MODULAR_ACTIVITY_INFO_TOC",    //通用活动info返回
  M_MODULAR_ACTIVITY_LIST_TOC = "M_MODULAR_ACTIVITY_LIST_TOC",    //活动list返回
  OPEN_ACT_MODS_DIALOG = "OPEN_ACT_MODS_DIALOG",  //打开活动主界面
  OPEN_ACT_MODS_PREVIEW_DIALOG = "OPEN_ACT_MODS_PREVIEW_DIALOG",  //打开活动预览界面
  UPDATE_ACTIVITY_MODULES_DIALOG_TAB_LIST = "UPDATE_ACTIVITY_MODULES_DIALOG_TAB_LIST",
  M_MODULAR_ACTIVITY_FETCH_TOC = "M_MODULAR_ACTIVITY_FETCH_TOC",
  M_MODULAR_ACTIVITY_SHOP_TOC = "M_MODULAR_ACTIVITY_SHOP_TOC",
  M_MODULAR_ACTIVITY_MISSION_TOC = "M_MODULAR_ACTIVITY_MISSION_ITEM_TOC",
  UPDATE_ACT_MODS_MISSION_RED_POINT = "UPDATE_ACT_MODS_MISSION_RED_POINT", //更新通用任务红点
  UPDATE_ACT_MODS_PAYMENT_SHOP_RED_POINT = "UPDATE_ACT_MODS_PAYMENT_SHOP_RED_POINT",       //更新通用商店红点
  OPEN_ACT_MODS_PREVIEW_REWARD_DIALOG = "OPEN_ACT_MODS_PREVIEW_REWARD_DIALOG",       //打开预览奖励
  UPDATE_MODULAR_ACTIVITY_SIX_TURN_INFO = "UPDATE_MODULAR_ACTIVITY_SIX_TURN_INFO",
  //抽将模块化
  OPEN_ACT_MODS_HERO_LOTTERY_DIALOG = "OPEN_ACT_MODS_HERO_LOTTERY_DIALOG",
  CLOSE_ACT_MODS_HERO_LOTTERY_DIALOG = "CLOSE_ACT_MODS_HERO_LOTTERY_DIALOG",
  UPDATE_ACT_MODS_HERO_LOTTERY_INFO = "UPDATE_ACT_MODS_HERO_LOTTERY_INFO",
  //自选抽将模块化活动
  OPEN_ACT_MODS_LOTTERY_WISH_SELECT_DIALOG = "OPEN_ACT_MODS_LOTTERY_WISH_SELECT_DIALOG",  //打开自选抽将选择界面
  CLOSE_ACT_MODS_LOTTERY_WISH_SELECT_DIALOG = "CLOSE_ACT_MODS_LOTTERY_WISH_SELECT_DIALOG",//关闭自选抽将选择界面
  OPEN_ACT_MODS_LOTTERY_WISH_SHOP_DIALOG = "OPEN_ACT_MODS_LOTTERY_WISH_SHOP_DIALOG",  //打开自选抽将商店界面
  CLOSE_ACT_MODS_LOTTERY_WISH_SHOP_DIALOG = "CLOSE_ACT_MODS_LOTTERY_WISH_SHOP_DIALOG",//关闭自选抽将商店界面
  UPDATE_ACT_MODS_LOTTERY_WISH_INFO = "UPDATE_ACT_MODS_LOTTERY_WISH_INFO",
  //活动模块化-真命天子
  OPEN_ACT_MODS_LOTTERY_SKIN_DIALOG = "OPEN_ACT_MODS_LOTTERY_SKIN_DIALOG",
  UPDATE_ACT_MODS_LOTTERY_SKIN_INFO = "UPDATE_ACT_MODS_LOTTERY_SKIN_INFO",
  ACT_MODS_LOTTERY_SKIN_TOC = "ACT_MODS_LOTTERY_SKIN_TOC",
  START_ACT_MODS_LOTTERY_SKIN = "START_ACT_MODS_LOTTERY_SKIN",
  //模块化 转盘抽将
  UPDATE_ACT_MODS_HUNT_INFO = "UPDATE_ACT_MODS_HUNT_INFO",
  UPDATE_ACT_MODS_HUNT_TOC = "UPDATE_ACT_MODS_HUNT_TOC",
  UPDATE_ACT_MODS_HUNT_REFRESH_TOC = "UPDATE_ACT_MODS_HUNT_REFRESH_TOC",
  UPDATE_ACT_MODS_HUNT_LOGS_TOC = "UPDATE_ACT_MODS_HUNT_LOGS_TOC",
  OPEN_ACT_MODS_HUNT_DESC_DIALOG = "OPEN_ACT_MODS_HUNT_DESC_DIALOG",
  OPEN_ACT_MODS_HUNT_LOTTERY_SHOP_DIALOG = "OPEN_ACT_MODS_HUNT_LOTTERY_SHOP_DIALOG",
  REQ_ACT_MODS_HUNT_RUN_TOS = "REQ_ACT_MODS_HUNT_RUN_TOS",
  ACT_MODS_HUNT_EFFECT_CONTINUE = "ACT_MODS_HUNT_EFFECT_CONTINUE",
  OPEN_ACT_REWARD_PRE_DIALOG = "OPEN_ACT_REWARD_PRE_DIALOG",

  OPEN_DUDUFU_DIALOG = "OPEN_DUDUFU_DIALOG",
  OPEN_FIVE_HERO_SELECT_HERO_DIALOG = "OPEN_FIVE_HERO_SELECT_HERO_DIALOG",
  CLOSE_FIVE_HERO_SELECT_HERO_DIALOG = "CLOSE_FIVE_HERO_SELECT_HERO_DIALOG",
  OPEN_FIVE_HERO_SELECT_HERO_TIPS_DIALOG = "OPEN_FIVE_HERO_SELECT_HERO_TIPS_DIALOG",
  OPEN_FIVE_HERO_ACTIVE_LEVEL_DIALOG = "OPEN_FIVE_HERO_ACTIVE_LEVEL_DIALOG",
  OPEN_FIVE_HERO_RESET_HERO_DIALOG = "OPEN_FIVE_HERO_RESET_HERO_DIALOG",
  OPEN_DHYANA_SELECT_HERO_DIALOG = "OPEN_DHYANA_SELECT_HERO_DIALOG",
  OPEN_FIVE_HERO_EQUIP_DIALOG = "OPEN_FIVE_HERO_EQUIP_DIALOG",
  UPDATE_DUDU_HERO_INFO = "UPDATE_DUDU_HERO_INFO",
  UPDATE_DUDUFU_INFO = "UPDATE_DUDUFU_INFO",
  UPDATE_DHYANA_INFO = "UPDATE_DHYANA_INFO",
  SELECT_DHYANA_HERO = "SELECT_DHYANA_HERO",
  UPDATE_HERO_RESONATE_EQUIP = "UPDATE_HERO_RESONATE_EQUIP",

  //活动模块化 - 升星计划
  OPEN_ACT_MODS_UPSTARPLAN_SELECT_DIALOG = "OPEN_ACT_MODS_UPSTARPLAN_SELECT_DIALOG",
  OPEN_ACT_MODS_UPSTARPLAN_GOODS_SELECT_DIALOG = "OPEN_ACT_MODS_UPSTARPLAN_GOODS_SELECT_DIALOG",
  OPEN_ACT_MODS_UPSTARPLAN_TIP_DIALOG = "OPEN_ACT_MODS_UPSTARPLAN_TIP_DIALOG",
  UPDATE_ACT_MODS_STAR_PLAN_REWARD_INFO = "UPDATE_ACT_MODS_STAR_PLAN_REWARD_INFO",

  //活动模块化 征战四方、衣带密诏
  OPEN_ACT_MODS_WAR_LOGS_DIALOG = "OPEN_ACT_MODS_WAR_LOGS_DIALOG",
  CLOSE_ACT_MODS_WAR_LOGS_DIALOG = "CLOSE_ACT_MODS_WAR_LOGS_DIALOG",
  OPEN_ACT_MODS_WAR_LOGS_MISSION_DIALOG = "OPEN_ACT_MODS_WAR_LOGS_MISSION_DIALOG",
  OPEN_ACT_MODS_WAR_LOGS_REWARD_MISSION_DIALOG = "OPEN_ACT_MODS_WAR_LOGS_REWARD_MISSION_DIALOG",

  //秘境探险（富甲一方）投骰子活动
  OPEN_ACT_MODS_DICE_TIP_DIALOG = "OPEN_ACT_MODS_DICE_TIP_DIALOG",
  ON_CLOSE_ACT_MODS_DICE_TIP_DIALOG = "ON_CLOSE_ACT_MODS_DICE_TIP_DIALOG",
  OPEN_ACT_MODS_DRAGON_DICE_DIALOG = "OPEN_ACT_MODS_DRAGON_DICE_DIALOG",
  OPEN_ACT_MODS_DICE_NPC_DIALOG = "OPEN_ACT_MODS_DICE_NPC_DIALOG",
  OPEN_ACT_MODS_DICE_NPC_LIST_DIALOG = "OPEN_ACT_MODS_DICE_NPC_LIST_DIALOG",
  OPEN_ACT_MODS_DICE_DAY_TIP_DIALOG = "OPEN_ACT_MODS_DICE_DAY_TIP_DIALOG",
  ON_ACT_MODS_DICE_TOC = "ON_ACT_MODS_DICE_TOC",
  UPDATE_ACT_MODS_MONSTER_LIST = "UPDATE_ACT_MODS_MONSTER_LIST",
  UPDATE_ACT_MODS_MONSTER_UN_LOCK = "UPDATE_ACT_MODS_MONSTER_UN_LOCK",

  //模块化假日签到活动
  UPDATE_ACT_MODS_HOLIDAY_WELFWARE_INFO = "UPDATE_ACT_MODS_HOLIDAY_WELFWARE_INFO",

  UPDATE_ACT_MODS_SIGN_WELFWARE_INFO = "UPDATE_ACT_MODS_SIGN_WELFWARE_INFO",

  //模块化活动道具
  UPDATE_ACT_MODS_DROP_INFO = "UPDATE_ACT_MODS_DROP_INFO",
  //模块化主题英雄挑战
  UPDATE_ACT_MODS_HERO_CHALLENG_INFO = "UPDATE_ACT_MODS_HERO_CHALLENG_INFO",
  UPDATE_ACT_MODS_HERO_CHALLENG_REPORT_INFO_INFO = "UPDATE_ACT_MODS_HERO_CHALLENG_REPORT_INFO_INFO",
  OPEN_ACT_MODS_HERO_CHALLENG_REPORT_INFO_INFO = "OPEN_ACT_MODS_HERO_CHALLENG_REPORT_INFO_INFO",
  //模块化天赐皮肤/神裔英雄
  OPEN_ACT_MODS_HEAVEN_GIVE_DIALOG = "OPEN_ACT_MODS_HEAVEN_GIVE_DIALOG",
  UPDATE_ACT_MODS_HEAVEN_GIVE_INFO = "UPDATE_ACT_MODS_HEAVEN_GIVE_INFO",
  // 皮肤模块
  UPDATE_SKIN_SHOW_LIST = "UPDATE_SKIN_SHOW_LIST",
  //转端活动
  OPEN_ACT_MODS_MINI_DUAN_DIALOG = "OPEN_ACT_MODS_MINI_DUAN_DIALOG",

  //剧情活动
  OPEN_ACT_MODS_STORY_DIALOG = "OPEN_ACT_MODS_STORY_DIALOG",
  OPEN_ACT_MODS_STORY_CHAPTER_DIALOG = "OPEN_ACT_MODS_STORY_CHAPTER_DIALOG",
  OPEN_ACT_MODS_STORY_CHAPTER_LIST_DIALOG = "OPEN_ACT_MODS_STORY_CHAPTER_LIST_DIALOG",
  OPEN_ACT_MODS_STORY_CHAPTER_MAP_DIALOG = "OPEN_ACT_MODS_STORY_CHAPTER_MAP_DIALOG",
  OPEN_ACT_MODS_STORY_AGAINST_DIALOG = "OPEN_ACT_MODS_STORY_AGAINST_DIALOG",
  UPDATE_ACT_MODS_STORY_INFO = "UPDATE_ACT_MODS_STORY_INFO",
  OPEN_DRAMA_DIALOG = "OPEN_DRAMA_DIALOG",//剧情整合

  //任务墙
  OPEN_ACT_MODS_TASK_WALL_DIALOG = "OPEN_ACT_MODS_TASK_WALL_DIALOG",
  CLOSE_ACT_MODS_TASK_WALL_DIALOG = "CLOSE_ACT_MODS_TASK_WALL_DIALOG",
  UPDATE_TASK_WALL_INFO = "UPDATE_TASK_WALL_INFO",
  OPEN_ACT_MODS_TASK_WALL_BUY_DIALOG = "OPEN_ACT_MODS_TASK_WALL_BUY_DIALOG",
  OPEN_ACT_MODS_TASK_BUY_SELECT_DIALOG = "OPEN_ACT_MODS_TASK_BUY_SELECT_DIALOG",

  OPEN_SIX_TURN_DIALOGN = "OPEN_SIX_TURN_DIALOGN",
  CLOSE_SIX_TURN_DIALOGN = "CLOSE_SIX_TURN_DIALOGN",

  //----------巅峰赛 start ---------
  OPEN_PEAK_DIALOG = "OPEN_PEAK_DIALOG",
  CLOSE_PEAK_DIALOG = "CLOSE_PEAK_DIALOG",
  OPEN_PEAK_GUESS_DIALOG = "OPEN_PEAK_GUESS_DIALOG",
  OPEN_PEAK_HISTORY_DIALOG = "OPEN_PEAK_HISTORY_DIALOG",
  OPEN_PEAK_LAST_RANK_DIALOG = "OPEN_PEAK_LAST_RANK_DIALOG",
  OPEN_PEAK_LAST_INFO_DIALOG = "OPEN_PEAK_LAST_INFO_DIALOG",
  OPEN_PEAK_MSG_SEND_DIALOG = "OPEN_PEAK_MSG_SEND_DIALOG",
  OPEN_PEAK_NOTICE_DIALOG = "OPEN_PEAK_ADVANCE_NOTICE_DIALOG",
  OPEN_PEAK_RANK_DIALOG = "OPEN_PEAK_RANK_DIALOG",
  OPEN_PEAK_RANK_REWARD_DIALOG = "OPEN_PEAK_RANK_REWARD_DIALOG",
  OPEN_PEAK_LEGEND_DIALOG = "OPEN_PEAK_LEGEND_DIALOG",
  OPEN_PEAK_SCHEDULE_DIALOG = "OPEN_PEAK_SCHEDULE_DIALOG",
  OPEN_PEAK_NOT_OPEN_DIALOG = "OPEN_PEAK_NOT_OPEN_DIALOG",
  OPEN_ADVANCE_LINE_UP_DIALOG = "OPEN_ADVANCE_LINE_UP_DIALOG",
  UPDATE_PEAK_INFO = "UPDATE_PEAK_INFO",
  UPDATE_PEAK_INFO_OPP = "UPDATE_PEAK_INFO_OPP",
  UPDATE_PEAK_INFO_MEMBER = "UPDATE_PEAK_INFO_MEMBER",
  UPDATE_PEAK_INFO_LOOK_MEMBER = "UPDATE_PEAK_INFO_LOOK_MEMBER",
  UPDATE_PEAK_INFO_BATTLE = "UPDATE_PEAK_INFO_BATTLE",
  UPDATE_PEAK_INFO_BET = "UPDATE_PEAK_INFO_BET",
  UPDATE_PEAK_INFO_HISTORY = "UPDATE_PEAK_INFO_HISTORY",
  UPDATE_PEAK_INFO_GET_MSG = "UPDATE_PEAK_INFO_GET_MSG",
  UPDATE_PEAK_INFO_PERSONAL = "UPDATE_PEAK_INFO_PERSONAL",
  REPLAY_PEAK_BATTLE = "REPLAY_PEAK_BATTLE",
  UPDATE_PEAK_BATTLE_RESULT = "UPDATE_PEAK_BATTLE_RESULT",
  CHANGE_PEAK_SUB_TAB = "CHANGE_PEAK_SUB_TAB",
  CHECK_PEAK_LINE_UP_STATUS = "CHECK_PEAK_LINE_UP_STATUS",
  //----------巅峰赛 end ---------

  //----------跨服天梯 start ---------
  OPEN_CROSS_LADDER_DIALOG = "OPEN_CROSS_LADDER_DIALOG",
  OPEN_CROSS_LADDER_AGAINST_INFO_DIALOG = "OPEN_CROSS_LADDER_AGAINST_INFO_DIALOG",
  OPEN_CROSS_LADDER_FIGHT_RESULT_DIALOG = "OPEN_CROSS_LADDER_FIGHT_RESULT_DIALOG",
  OPEN_CROSS_LADDER_LEGEND_DIALOG = "OPEN_CROSS_LADDER_LEGEND_DIALOG",
  OPEN_CROSS_LADDER_LOGS_DIALOG = "OPEN_CROSS_LADDER_LOGS_DIALOG",
  OPEN_CROSS_LADDER_RANK_DIALOG = "OPEN_CROSS_LADDER_RANK_DIALOG",
  OPEN_CROSS_LADDER_RANK_REWARD_DIALOG = "OPEN_CROSS_LADDER_RANK_REWARD_DIALOG",
  UPDATE_CROSS_LADDER_INFO = "UPDATE_CROSS_LADDER_INFO",
  UPDATE_CROSS_LADDER_OPP_INFO = "UPDATE_CROSS_LADDER_OPP_INFO",
  UPDATE_CROSS_LADDER_LIST = "UPDATE_CROSS_LADDER_LIST",
  UPDATE_CROSS_LADDER_LOGS = "UPDATE_CROSS_LADDER_LOGS",
  CROSS_LADDER_REQ_REFRESH = "CROSS_LADDER_REQ_REFRESH",
  //----------跨服天梯 end ---------

  //----------仙山外海 --------------
  OPEN_XSWH_DIALOG = "OPEN_XSWH_DIALOG",
  CLOSE_XSWH_DIALOG = "CLOSE_XSWH_DIALOG",
  OPEN_XSWH_BOSS_DIALOG = "OPEN_XSWH_BOSS_DIALOG",
  CLOSE_XSWH_BOSS_DIALOG = "CLOSE_XSWH_BOSS_DIALOG",
  OPEN_XSWH_RANK_DIALOG = "OPEN_XSWH_RANK_DIALOG",
  CLOSE_XSWH_RANK_DIALOG = "CLOSE_XSWH_RANK_DIALOG",
  OPEN_XSWH_BEST_RANK_DIALOG = "OPEN_XSWH_BEST_RANK_DIALOG",
  OPEN_XSWH_RANK_REWARD_DIALOG = "OPEN_XSWH_RANK_REWARD_DIALOG",
  OPEN_XSWH_HURT_REWARD_DIALOG = "OPEN_XSWH_HURT_REWARD_DIALOG",
  OPEN_XSWH_GIFT_DIALOG = "OPEN_XSWH_GIFT_DIALOG",
  UPDATE_XSWH_INFO = "UPDATE_XSWH_INFO",
  UPDATE_XSWH_RANK = "UPDATE_XSWH_RANK",
  UPDATE_XSWH_BEST_RANK = "UPDATE_XSWH_BEST_RANK",
  FIGHT_XSWH_FINISH = "FIGHT_XSWH_FINISH",

  //--------------龙魂----------------
  UPDATE_QIMOU_INFO = "UPDATE_QIMOU_INFO",
  UPDATE_QIMOU_UP_STAR_SUCCESS = "UPDATE_QIMOU_UP_STAR_SUCCESS",
  OPEN_QIMOU_VIEW = "OPEN_QIMOU_VIEW",
  OPEN_QIMOU_FEN_JIE_DIALOG = "OPEN_QIMOU_FEN_JIE_DIALOG",
  OPEN_QIMOU_ONE_KEY_FEN_JIE_DIALOG = "OPEN_QIMOU_ONE_KEY_FEN_JIE_DIALOG",
  OPEN_QIMOU_DECODE_SELECT_DIALOG = "OPEN_QIMOU_DECODE_SELECT_DIALOG",
  OPEN_QIMOU_SHOW_DIALOG = "OPEN_QIMOU_SHOW_DIALOG",
  OPEN_QIMOU_UP_STAR_DIALOG = "OPEN_QIMOU_UP_STAR_DIALOG",
  OPEN_QIMOU_UP_STAR_TIPS_DIALOG = "OPEN_QIMOU_UP_STAR_TIPS_DIALOG",
  CLOSE_QIMOU_UP_STAR_DIALOG = "CLOSE_QIMOU_UP_STAR_DIALOG",
  OPEN_QIMOU_UP_STAR_COST_SELECT_DIALOG = "OPEN_QIMOU_UP_STAR_COST_SELECT_DIALOG",
  OPEN_QIMOU_LINE_UP_DIALOG = "OPEN_QIMOU_LINE_UP_DIALOG",
  OPEN_QIMOU_GAINS_DIALOG = "OPEN_QIMOU_GAINS_DIALOG",
  OPEN_QIMOU_DETAIL_TIPS_DIALOG = "OPEN_QIMOU_DETAIL_TIPS_DIALOG",
  OPEN_QIMOU_DECODE_SPEED_UP_DIALOG = "OPEN_QIMOU_DECODE_SPEED_UP_DIALOG",
  CLOSE_QIMOU_DECODE_SPEED_UP_DIALOG = "CLOSE_QIMOU_DECODE_SPEED_UP_DIALOG",
  OPEN_QIMOU_UNLOCK_SLOT_COST_DIALOG = "OPEN_QIMOU_UNLOCK_SLOT_COST_DIALOG",
  UPDATE_QI_MOU_UP_STAR_COST_LIST = "UPDATE_QI_MOU_UP_STAR_COST_LIST",
  UPDATE_QI_MOU_LOAD_LIST = "UPDATE_QI_MOU_LOAD_LIST",
  UPDATE_QI_MOU_SELECT_STATE = "UPDATE_QI_MOU_SELECT_STATE",
  ON_QI_MOU_GAINS_TOC = "ON_QI_MOU_GAINS_TOC",
  OPEN_QIMOU_UP_STAGE_TIPS_DIALOG = "OPEN_QIMOU_UP_STAGE_TIPS_DIALOG",
  OPEN_QIMOU_UP_ONE_KEY_UP_STAR_DIALOG = "OPEN_QIMOU_UP_ONE_KEY_UP_STAR_DIALOG",
  OPEN_QIMOU_CONVERT_DIALOG = "OPEN_QIMOU_CONVERT_DIALOG",
  CLOSE_QIMOU_CONVERT_DIALOG = "CLOSE_QIMOU_CONVERT_DIALOG",
  OPEN_QIMOU_CONVERT_SELECT_DIALOG = "OPEN_QIMOU_CONVERT_SELECT_DIALOG",
  SELECT_QIMOU_CONVERT = "SELECT_QIMOU_CONVERT",
  OPEN_QIMOU_CONVERT_TIPS_DIALOG = "OPEN_QIMOU_CONVERT_TIPS_DIALOG",
  UPDATE_QIMOU_UP_UNLOCK_DIALOG = "UPDATE_QIMOU_UP_UNLOCK_DIALOG",
  CLOSE_QIMOU_UP_UNLOCK_DIALOG = "CLOSE_QIMOU_UP_UNLOCK_DIALOG",
  UPDATE_QI_MOU_UP_LEVEL = "UPDATE_QI_MOU_UP_LEVEL",
  OPEN_NEW_QIMOU_DIALOG = "OPEN_NEW_QIMOU_DIALOG",
  //龙魂GM页面
  OPEN_QIMOU_DIALOG = "OPEN_QIMOU_DIALOG",
  //--------------勋章----------------
  OPEN_XUNZHANG_DIALOG = "OPEN_XUNZHANG_DIALOG",
  CLOSE_XUNZHANG_DIALOG = "CLOSE_XUNZHANG_DIALOG",
  OPEN_XUNZHANG_TIPS_DIALOG = "OPEN_XUNZHANG_TIPS_DIALOG",
  OPEN_XUNZHANG_SELSET_DIALOG = "OPEN_XUNZHANG_SELSET_DIALOG",
  OPEN_XUNZHANG_PROGRESS_DIALOG = "OPEN_XUNZHANG_PROGRESS_DIALOG",
  OPEN_XUNZHANG_MAGICIAN_DIALOG = "OPEN_XUNZHANG_MAGICIAN_DIALOG",
  UPDATE_XUNZHANG_DIALOG = "UPDATE_XUNZHANG_DIALOG",
  UPDATE_XUNZHANG_SELECT = "UPDATE_XUNZHANG_SELECT",
  UPDATE_XUNZHANG_DIALOG_SWAP = "UPDATE_XUNZHANG_DIALOG_SWAP",
  OPEN_XUNZHANG_DESCRIBE_DIALOG = "OPEN_XUNZHANG_DESCRIBE_DIALOG",
  //-------------360微端 ---------------------
  UPDATE_360_SIGN_INFO = "UPDATE_360_SIGN_INFO",
  OPEN_GAME_TIP_DIALOG = "OPEN_GAME_TIP_DIALOG",
  OPEN_GAME_SIGN_DIALOG = "OPEN_GAME_SIGN_DIALOG",
  //-------------360微端 ---------------------

  //-------------授权豪礼 start--------------------
  OPEN_AUTHORIZED_GIFT_DIALOG = "OPEN_AUTHORIZED_GIFT_DIALOG",
  //-------------授权豪礼 end--------------------

  //-------------小游戏 start ------------------
  MINI_GAME_PASS = "MINI_GAME_PASS",
  MINI_GAME_START = "MINI_GAME_START",
  MINI_GAME_INIT_DATA = "MINI_GAME_INIT_DATA",
  OPEN_MINI_GAME_LOADING_DIALOG = "OPEN_MINI_GAME_LOADING_DIALOG",
  CLOSE_MINI_GAME_LOADING_DIALOG = "CLOSE_MINI_GAME_LOADING_DIALOG",

  //-------------三消 start ------------------
  OPEN_SAN_XIAO_DIALOG = "OPEN_SAN_XIAO_DIALOG",
  OPEN_SAN_XIAO_SHOP_DIALOG = "OPEN_SAN_XIAO_SHOP_DIALOG",
  OPEN_SAN_XIAO_HELP_DIALOG = "OPEN_SAN_XIAO_HELP_DIALOG",
  SAN_XIAO_ATTR_CHANGE = "SAN_XIAO_ATTR_CHANGE",
  SAN_XIAO_BUY_ITEM = "SAN_XIAO_BUY_ITEM",
  UPDATE_SAN_XIAO_INIT_INFO = "UPDATE_SAN_XIAO_INIT_INFO",
  UPDATE_SAN_XIAO_INFO = "UPDATE_SAN_XIAO_INFO",
  //-------------三消 end ------------------

  UPDATE_SYS_USE_TIMES_INFO = "UPDATE_SYS_USE_TIMES_INFO",

  //-------------小兵快跑 start ------------------
  //-------------小兵快跑 end ------------------

  //-------------小游戏 end ------------------
  //马甲包提审
  ON_REVIEW_GAME_INIT = "ON_REVIEW_GAME_INIT",
  //--------------号码绑定----------------
  UPDATA_SDK_REWARD_STATE = "UPDATA_SDK_REWARD_STATE",

  //-------------跨服组队 start ------------------
  OPEN_CROSS_TEAM_LINE_UP_DIALOG = "OPEN_CROSS_TEAM_LINE_UP_DIALOG",
  CLOSE_CROSS_TEAM_LINE_UP_DIALOG = "CLOSE_CROSS_TEAM_LINE_UP_DIALOG",
  OPEN_CROSS_TEAM_WEAPON_LINE_UP_DIALOG = "OPEN_CROSS_TEAM_WEAPON_LINE_UP_DIALOG",
  OPEN_CROSS_TEAM_QIMOU_LINE_UP_DIALOG = "OPEN_CROSS_TEAM_QIMOU_LINE_UP_DIALOG",
  OPEN_CROSS_TEAM_SHARE_HERO_DIALOG = "OPEN_CROSS_TEAM_SHARE_HERO_DIALOG",
  OPEN_CROSS_TEAM_SHARE_HERO_LIST_DIALOG = "OPEN_CROSS_TEAM_SHARE_HERO_LIST_DIALOG",
  UPDATE_TEAM_INFO_MY_TEAM = "UPDATE_TEAM_INFO_MY_TEAM",
  UPDATE_TEAM_INFO_TEAMS = "UPDATE_TEAM_INFO_TEAMS",
  UPDATE_TEAM_INFO_APPLY = "UPDATE_TEAM_INFO_APPLY",
  UPDATE_TEAM_INFO_UPDATE = "UPDATE_TEAM_INFO_UPDATE",
  UPDATE_TEAM_OPERATE = "UPDATE_TEAM_OPERATE",
  UPDATE_TEAM_LINE_UP_DATA = "UPDATE_TEAM_LINE_UP_DATA",
  UPDATE_TEAM_LINE_UP_LIST = "UPDATE_TEAM_LINE_UP_LIST",
  UPDATE_TEAM_LINE_UP_INFO = "UPDATE_TEAM_LINE_UP_INFO",
  UPDATE_TEAM_MERGE_REWARD = "UPDATE_TEAM_MERGE_REWARD",
  UPDATE_TEAM_SHARE_HERO = "UPDATE_TEAM_SHARE_HERO",
  ENTER_TEAM_BATTLE = "ENTER_TEAM_BATTLE",
  EXIT_TEAM_BATTLE = "EXIT_TEAM_BATTLE",
  //-------------跨服组队 end ------------------

  //-------------组队征战 start ------------------
  OPEN_BATTLE_TRIAL_DIALOG = "OPEN_BATTLE_TRIAL_DIALOG",
  OPEN_BATTLE_TRIAL_TEAM_HALL_DIALOG = "OPEN_BATTLE_TRIAL_TEAM_HALL_DIALOG",
  OPEN_BATTLE_TRIAL_TEAM_SETTING_DIALOG = "OPEN_BATTLE_TRIAL_TEAM_SETTING_DIALOG",
  OPEN_BATTLE_TRIAL_PASS_REWARD_DIALOG = "OPEN_BATTLE_TRIAL_PASS_REWARD_DIALOG",
  CLOSE_BATTLE_TRIAL_PASS_REWARD_DIALOG = "CLOSE_BATTLE_TRIAL_PASS_REWARD_DIALOG",
  OPEN_BATTLE_TRIAL_NOTICE_DIALOG = "OPEN_BATTLE_TRIAL_NOTICE_DIALOG",
  OPEN_BATTLE_TRIAL_AGINST_INFO_DIALOG = "OPEN_BATTLE_TRIAL_AGINST_INFO_DIALOG",
  OPEN_BATTLE_TRIAL_APPLY_NOTICE_DIALOG = "OPEN_BATTLE_TRIAL_APPLY_NOTICE_DIALOG",
  OPEN_BATTLE_TRIAL_RANK_DIALOG = "OPEN_BATTLE_TRIAL_RANK_DIALOG",
  OPEN_BATTLE_TRIAL_DAILY_END_INFO_DIALOG = "OPEN_BATTLE_TRIAL_DAILY_END_INFO_DIALOG",
  OPEN_BATTLE_TRIAL_MERGE_REWARD_DIALOG = "OPEN_BATTLE_TRIAL_MERGE_REWARD_DIALOG",
  OPEN_BATTLE_TRIAL_OFFLINE_REWARD_DIALOG = "OPEN_BATTLE_TRIAL_OFFLINE_REWARD_DIALOG",
  OPEN_BATTLE_TRIAL_TEAM_SURE_DIALOG = "OPEN_BATTLE_TRIAL_TEAM_SURE_DIALOG",
  OPEN_BATTLE_TRIAL_GUAJI_ONLINE_REWARD_DIALOG = "OPEN_BATTLE_TRIAL_GUAJI_ONLINE_REWARD_DIALOG",
  OPEN_BATTLE_TRIAL_GUAJI_OFFLINE_REWARD_DIALOG = "OPEN_BATTLE_TRIAL_GUAJI_OFFLINE_REWARD_DIALOG",
  OPEN_BATTLE_TRIAL_UPDATE_COMPEN_DIALOG = "OPEN_BATTLE_TRIAL_UPDATE_COMPEN_DIALOG",
  OPEN_BATTLE_TRIAL_GUAJI_EARNINGS_DIALOG = "OPEN_BATTLE_TRIAL_GUAJI_EARNINGS_DIALOG",
  UPDATE_BATTLE_TRIAL_INFO = "UPDATE_BATTLE_TRIAL_INFO",
  UPDATE_BATTLE_TRIAL_FETCH_INFO = "UPDATE_BATTLE_TRIAL_FETCH_INFO",
  UPDATE_BATTLE_TRIAL_GUA_JI_DROP = "UPDATE_BATTLE_TRIAL_GUA_JI_DROP",
  UPDATE_BATTLE_TRIAL_GUA_JI_TALK = "UPDATE_BATTLE_TRIAL_GUA_JI_TALK",
  //-------------组队征战 end ------------------

  UPDATE_MONTHCARD_ACTIVE_INFO = "UPDATE_MONTHCARD_ACTIVE_INFO",
  OPEN_MONTHCARD_ACTIVE_DIALOG = "OPEN_MONTHCARD_ACTIVE_DIALOG",
  CLOSE_MONTHCARD_ACTIVE_DIALOG = "CLOSE_MONTHCARD_ACTIVE_DIALOG",

  //----------组队领主战 --------------
  OPEN_TEAM_XSWH_DIALOG = "OPEN_TEAM_XSWH_DIALOG",
  CLOSE_TEAM_XSWH_DIALOG = "CLOSE_TEAM_XSWH_DIALOG",
  OPEN_TEAM_XSWH_BOSS_DIALOG = "OPEN_TEAM_XSWH_BOSS_DIALOG",
  CLOSE_TEAM_XSWH_BOSS_DIALOG = "CLOSE_TEAM_XSWH_BOSS_DIALOG",
  OPEN_TEAM_XSWH_RANK_DIALOG = "OPEN_TEAM_XSWH_RANK_DIALOG",
  OPEN_TEAM_XSWH_RANK_DAYL_DIALOG = "OPEN_TEAM_XSWH_RANK_DAYL_DIALOG",
  CLOSE_TEAM_XSWH_RANK_DIALOG = "CLOSE_TEAM_XSWH_RANK_DIALOG",
  OPEN_TEAM_XSWH_BEST_RANK_DIALOG = "OPEN_TEAM_XSWH_BEST_RANK_DIALOG",
  OPEN_TEAM_XSWH_RANK_REWARD_DIALOG = "OPEN_TEAM_XSWH_RANK_REWARD_DIALOG",
  OPEN_TEAM_XSWH_HURT_REWARD_DIALOG = "OPEN_TEAM_XSWH_HURT_REWARD_DIALOG",
  OPEN_TEAM_XSWH_GIFT_DIALOG = "OPEN_TEAM_XSWH_GIFT_DIALOG",
  UPDATE_TEAM_XSWH_INFO = "UPDATE_TEAM_XSWH_INFO",
  UPDATE_TEAM_XSWH_RANK = "UPDATE_TEAM_XSWH_RANK",
  UPDATE_TEAM_XSWH_BEST_RANK = "UPDATE_TEAM_XSWH_BEST_RANK",
  FIGHT_TEAM_XSWH_FINISH = "FIGHT_TEAM_XSWH_FINISH",
  UPDATE_TEAM_XSWH_REDPOINT = "UPDATE_TEAM_XSWH_REDPOINT",

  //-------------模块化定制礼包 start -------------
  OPEN_ACT_MODS_CISTOMIZEDGIFT_SELECT_DIALOG = "OPEN_ACT_MODS_CISTOMIZEDGIFT_SELECT_DIALOG",
  UNDATA_ACT_MODS_CISTOMIZEDGIFT_SELECT_DIALOG = "UNDATA_ACT_MODS_CISTOMIZEDGIFT_SELECT_DIALOG",

  //-------------模块化定制礼包 end ---------------

  //-------------模块化敲砖块活动 start -------------
  OPEN_ACT_MODS_BRICK_SELECT_DIALOG = "OPEN_ACT_MODS_BRICK_SELECT_DIALOG",
  UNDATA_ACT_MODS_BRICK_VIEW = "UNDATA_ACT_MODS_BRICK_VIEW",
  UNDATA_ACT_MODS_BRICK_YING_DAO = "UNDATA_ACT_MODS_BRICK_YING_DAO",
  CLOSE_BRICK_AUTO_GO = "CLOSE_BRICK_AUTO_GO",
  GO_BRICK_AUTO_GO = "GO_BRICK_AUTO_GO",
  UNDATE_ACT_MODS_GOODS_TIP_SELECT_BOX = "UNDATE_ACT_MODS_GOODS_TIP_SELECT_BOX",
  OPEN_ACT_MODS_BRICK_DIALOG = "OPEN_ACT_MODS_BRICK_DIALOG",
  OPEN_ACT_MODS_BRICK_REWARD_SHOW_DIALOG = "OPEN_ACT_MODS_BRICK_REWARD_SHOW_DIALOG",
  //-------------模块化敲砖块活动 end ---------------
  //-------------模块化祝福活动 start ---------------
  OPEN_ACT_MODS_BLESS_MISSION_DIALOG = "OPEN_ACT_MODS_BLESS_MISSION_DIALOG",
  UNDATA_ACT_MODS_BLESS_VIEW = "UNDATA_ACT_MODS_BLESS_VIEW",
  //-------------模块化祝福活动 end ---------------
  //-------------模块化任务选择活动 start ------------
  UPDATE_ACT_MODS_TARGET_TASK = "UPDATE_ACT_MODS_TARGET_TASK",       //更新通用商店红点
  OPEN_ACT_MODS_TARGET_TASK_TIP_DIALOG = "OPEN_ACT_MODS_TARGET_TASK_TIP_DIALOG",
  //-------------模块化任务选择活动 end ------------

  /* 打开名将卡界面 */
  OPEN_MASTER_CART_DIALOG = "OPEN_MASTER_CART_DIALOG",
  CLOSE_MASTER_CART_DIALOG = "CLOSE_MASTER_CART_DIALOG",
  SHOW_MASTER_CARD_GUIDE = "SHOW_MASTER_CARD_GUIDE",
  OPEN_MASTER_CART_COMPARE_DIALOG = "OPEN_MASTER_CART_COMPARE_DIALOG",
  CLOSE_MASTER_CART_COMPARE_DIALOG = "CLOSE_MASTER_CART_COMPARE_DIALOG",
  UPDATE_MASTER_CART_DIALOG = "UPDATE_MASTER_CART_DIALOG",
  UPDATE_MASTER_CART_INFO = "UPDATE_MASTER_CART_INFO",
  UPDATE_MASTER_CART_STAGE = "UPDATE_MASTER_CART_STAGE",
  OPEN_MASTER_CART_LEVEL_DIALOG = "OPEN_MASTER_CART_LEVEL_DIALOG",
  CLOSE_MASTER_CART_LEVEL_DIALOG = "CLOSE_MASTER_CART_LEVEL_DIALOG",
  OPEN_MASTER_CART_AUTO_DIALOG = "OPEN_MASTER_CART_AUTO_DIALOG",
  CLOSE_MASTER_CART_AUTO_DIALOG = "CLOSE_MASTER_CART_AUTO_DIALOG",
  OPEN_MASTER_CART_INFO_DIALOG = "OPEN_MASTER_CART_INFO_DIALOG",
  CLOSE_MASTER_CART_INFO_DIALOG = "CLOSE_MASTER_CART_INFO_DIALOG",
  UPDATE_MASTER_CART_LEVEL = "UPDATE_MASTER_CART_LEVEL",
  OPEN_MASTER_CARD_AUTO_OPERATE = "OPEN_MASTER_CARD_AUTO_OPERATE",
  CLOSE_MASTER_CARD_AUTO_OPERATE = "CLOSE_MASTER_CARD_AUTO_OPERATE",
  FISH_LOTTERY_TOC = "MASTER_LOTTERY_TOC",
  SUCCEED_MASTER_CARD_OUT = "SUCCEED_MASTER_CARD_OUT",
  OPEN_MASTER_CART_ATTRI_SHOW_DIALOG = "OPEN_MASTER_CART_ATTRI_SHOW_DIALOG",
  CLOSE_MASTER_CART_ATTRI_SHOW_DIALOG = "CLOSE_MASTER_CART_ATTRI_SHOW_DIALOG",
  UPDATE_MASTER_CARD_QUENE = "UPDATE_MASTER_CARD_QUENE",
  UPDATE_LEVEL_ONT_ON_LINE = "UPDATE_LEVEL_ONT_ON_LINE",
  OPEN_MASTER_CART_SHOP_DIALOG = "OPEN_MASTER_CART_SHOP_DIALOG",
  CLOSE_MASTER_CART_SHOP_DIALOG = "CLOSE_MASTER_CART_SHOP_DIALOG",
  OPEN_MASTER_CART_FASTER_TOOL_DIALOG = "OPEN_MASTER_CART_FASTER_TOOL_DIALOG",
  CLOSE_MASTER_CART_FASTER_TOOL_DIALOG = "CLOSE_MASTER_CART_FASTER_TOOL_DIALOG",
  UPDATE_MASTER_CART_FASTER_TOOL_DIALOG = "UPDATE_MASTER_CART_FASTER_TOOL_DIALOG",
  OPEN_MASTER_CART_UP_GUANG_DIALOG = "OPEN_MASTER_CART_UP_GUANG_DIALOG",
  CLOSE_MASTER_CART_UP_GUANG_DIALOG = "CLOSE_MASTER_CART_UP_GUANG_DIALOG",
  OPEN_MASTER_CART_GUANG_CHANGE_DIALOG = "OPEN_MASTER_CART_GUANG_CHANGE_DIALOG",
  CLOSE_MASTER_CART_GUANG_CHANGE_DIALOG = "CLOSE_MASTER_CART_GUANG_CHANGE_DIALOG",
  OPEN_MASTER_CARD_GUANG_UP_TIP_DIALOG = "OPEN_MASTER_CARD_GUANG_UP_TIP_DIALOG",
  SHOW_MASTER_CARD_GUANGUP_EFFCT = "SHOW_MASTER_CARD_GUANGUP_EFFCT",
  CLOSE_MASTER_CARD_GUANG_UP_TIP_DIALOG = "CLOSE_MASTER_CARD_GUANG_UP_TIP_DIALOG",
  OPEN_MASTER_CARD_UP_GUANG_RESULT_DIALOG = "OPEN_MASTER_CARD_UP_GUANG_RESULT_DIALOG",
  CLOSE_MASTER_CARD_UP_GUANG_RESULT_DIALOG = "CLOSE_MASTER_CARD_UP_GUANG_RESULT_DIALOG",
  OPEN_MASTER_CARD_PREVIEW_ATTR = "OPEN_MASTER_CARD_PREVIEW_ATTR",
  UPDATE_MASTER_CARD_HEADER_ITEM = "UPDATE_MASTER_CARD_HEADER_ITEM",
  OPEN_MASTER_CARD_RES_HAPE_DIALOG = "OPEN_MASTER_CARD_RES_HAPE_DIALOG",
  CLOSE_MASTER_CARD_RES_HAPE_DIALOG = "CLOSE_MASTER_CARD_RES_HAPE_DIALOG",
  UPADATE_MASTER_CARD_RES_TYPE = "UPADATE_MASTER_CARD_RES_TYPE",
  SHOW_MASTER_CARD_POWER = "SHOW_MASTER_CARD_POWER",
  UPDATE_BOTTOM_MASTER_CARD_HAS_TIP = "UPDATE_BOTTOM_MASTER_CARD_HAS_TIP",
  OPEN_MASTER_CARD_LOTTRY_SHOW_DIALOG = "OPEN_MASTER_CARD_LOTTRY_SHOW_DIALOG",
  CLOSE_MASTER_CARD_LOTTRY_SHOW_DIALOG = "CLOSE_MASTER_CARD_LOTTRY_SHOW_DIALOG",
  AUTO_BOTTOM_MASTER_CART = "AUTO_BOTTOM_MASTER_CART",
  OPEN_MASTER_CARD_INFO_COMPARE_DIALOG = "OPEN_MASTER_CARD_INFO_COMPARE_DIALOG",
  CLOSE_MASTER_CARD_INFO_COMPARE_DIALOG = "CLOSE_MASTER_CARD_INFO_COMPARE_DIALOG",
  OPEN_MASTER_CARD_DECORATION_DIALOG = "OPEN_MASTER_CARD_DECORATION_DIALOG",
  CLOSE_MASTER_CARD_DECORATION_DIALOG = "CLOSE_MASTER_CARD_DECORATION_DIALOG",
  CHANGE_MASTER_CARD_DECORATOION = "CHANGE_MASTER_CARD_DECORATOION",
  UPDATE_MASTER_CARD_DECORATION_INFO = "UPDATE_MASTER_CARD_DECORATION_INFO",
  CHANGE_MASTER_CARD_ROLE_POSITION = "CHANGE_MASTER_CARD_ROLE_POSITION",
  OPEN_MASTER_CARD_ATTRI_PREVIEW_DIALOG = "OPEN_MASTER_CARD_ATTRI_PREVIEW_DIALOG",
  CLOSE_MASTER_CARD_ATTRI_PREVIEW_DIALOG = "CLOSE_MASTER_CARD_ATTRI_PREVIEW_DIALOG",
  CHECK_MASTER_CARD_ATTRI_PREVIEW = "CHECK_MASTER_CARD_ATTRI_PREVIEW",
  CHANGE_ROLE_PREVIEW_ENTRY_SHOW = "CHANGE_ROLE_PREVIEW_ENTRY_SHOW",
  RESET_MASTER_CARD_DECORATION = "RESET_MASTER_CARD_DECORATION",
  OPEN_MASTER_CARD_DECORATION_GOODS_TIP = "OPEN_MASTER_CARD_DECORATION_GOODS_TIP",
  CLOSE_MASTER_CARD_DECORATION_GOODS_TIP = "CLOSE_MASTER_CARD_DECORATION_GOODS_TIP",
  UPDATE_MASTER_CARD_EXTREME_SATISFY_DATA = "UPDATE_MASTER_CARD_EXTREME_SATISFY_DATA",
  RESET_TOP_LAYERS = "RESET_TOP_LAYERS",
  UPDATE_MASTER_CARD_AUTO_SETTING = "UPDATE_MASTER_CARD_AUTO_SETTING",
  UPDATE_MASTER_CARD_BOX_EDIT = "UPDATE_MASTER_CARD_BOX_EDIT",
  UPDATE_MASTER_CARD_OUT_INFO = "UPDATE_MASTER_CARD_OUT_INFO",
  UPDATE_MASTER_CARD_LOTTERY_INFO = "UPDATE_MASTER_CARD_LOTTERY_INFO",
  UPDATE_MASTER_CARD_SRORE_BAG = "UPDATE_MASTER_CARD_SRORE_BAG",
  OPEN_MILITARY_RANK_DIALOG = "OPEN_MILITARY_RANK_DIALOG",
  CLOSE_MILITARY_RANK_DIALOG = "CLOSE_MILITARY_RANK_DIALOG",
  /**名将卡-助力商店 */
  UPDATE_MASTER_CART_HELP_SHOP_DIALOG = "UPDATE_MASTER_CART_HELP_SHOP_DIALOG",
  /**名将卡-钓鱼周助力 */
  UPDATE_WEEK_TARGET_INFO_DIALOG = "UPDATE_WEEK_TARGET_INFO_DIALOG",
  /**名将卡-钓鱼周助力-升级预览 */
  OPEN_WEEK_TARGET_LEVEL_PREVIEW_DIALOG = "OPEN_WEEK_TARGET_LEVEL_PREVIEW_DIALOG",
  CLOSE_WEEK_TARGET_LEVEL_PREVIEW_DIALOG = "CLOSE_WEEK_TARGET_LEVEL_PREVIEW_DIALOG",
  /**更新广告视频相关  */
  UPDATE_MASTER_CART_ADVERT = "UPDATE_MASTER_CART_ADVERT",
  OPEN_MASTER_CARD_ADVER_TIP_DIALOG = "OPEN_MASTER_CARD_ADVER_TIP_DIALOG",
  CLOSE_MASTER_CARD_ADVER_TIP_DIALOG = "CLOSE_MASTER_CARD_ADVER_TIP_DIALOG",

  /**名将卡-首充福利 */
  OPEN_MASTER_CARD_FIRST_GIFT_DIALOG = "OPEN_MASTER_CARD_FIRST_GIFT_DIALOG",
  CLOSE_MASTER_CARD_FIRST_GIFT_DIALOG = "CLOSE_MASTER_CARD_FIRST_GIFT_DIALOG",
  UPDATE_MASTER_CARD_FIRST_PAY_TASK_INFO = "UPDATE_MASTER_CARD_FIRST_PAY_TASK_INFO",
  OPEN_MASTER_CARD_FIRST_RECHARGE_REWARD_LOOK_DIALOG = "OPEN_MASTER_CARD_FIRST_RECHARGE_REWARD_LOOK_DIALOG",
  OPEN_MASTER_CARD_HNADLER_DIALOG = "OPEN_MASTER_CARD_HNADLER_DIALOG",
  CLOSE_MASTER_CARD_HNADLER_DIALOG = "CLOSE_MASTER_CARD_HNADLER_DIALOG",
  SHOW_LOTTERY_SPE_EFFECT = "SHOW_LOTTERY_SPE_EFFECT",
  RESH_MASTER_CARD_DECORATION_STAGE = "RESH_MASTER_CARD_DECORATION_STAGE",
  CHECK_GO_BATTLE_ANIMATION = "CHECK_GO_BATTLE_ANIMATION",
  GO_BATTLE_ANIMATION_START = "GO_BATTLE_ANIMATION_START",
  GO_BATTLE_ANIMATION_END = "GO_BATTLE_ANIMATION_END",

  /**名将卡-成长基金 */
  OPEN_MASTER_CARD_FULI_TOKEN_DIALOG = "OPEN_MASTER_CARD_FULI_TOKEN_DIALOG",


  // --- 劳动节 --- 
  UPDATE_ACT_MODS_LABOR_EXCHANGE = "UPDATE_ACT_MODS_LABOR_EXCHANGE",
  UPDATE_LABOR_EXCHANGE_LOGIN_RED = "UPDATE_LABOR_EXCHANGE_LOGIN_RED",

  //------------ 境界系统 start ------------------
  OPEN_JINGJIE_DIALOG = "OPEN_JINGJIE_DIALOG",
  CLOSE_JINGJIE_DIALOG = "CLOSE_JINGJIE_DIALOG",
  UPDATE_JINGJIE_DIALOG = "UPDATE_JINGJIE_DIALOG",
  OPEN_JINGJIE_UPGRAE_DIALOG = "OPEN_JINGJIE_UPGRAE_DIALOG",
  OPEN_JINGJIE_EAT_DIALOG = "OPEN_JINGJIE_EAT_DIALOG",
  OPEN_JINGJIE_PRE_REWARD_DIALOG = "OPEN_JINGJIE_PRE_REWARD_DIALOG",
  OPEN_JINGJIE_PROP_PREVIEW_DIALOG = "OPEN_JINGJIE_PROP_PREVIEW_DIALOG",
  UPDATE_STAGE_BREED_INFO = "UPDATE_STAGE_BREED_INFO",
  UPDATE_SIMP_MISSION = "UPDATE_SIMP_MISSION",
  EAT_STAGE_BREED_FINISH = "EAT_STAGE_BREED_FINISH",
  UPDATE_STAGE_TREE_INFO = "UPDATE_STAGE_TREE_INFO",
  UPDATE_SHORTCUT_SHOP = "UPDATE_SHORTCUT_SHOP",
  OPEN_JINGJIE_TREE_TIME_DIALOG = "OPEN_JINGJIE_TREE_TIME_DIALOG",
  OPEN_STAGE_BUY_ITEM_DIALOG = "OPEN_STAGE_BUY_ITEM_DIALOG",

  OPEN_JINGJIE_PREVIEW_DIALOG = "OPEN_JINGJIE_PREVIEW_DIALOG",
  CLOSE_JINGJIE_PREVIEW_DIALOG = "CLOSE_JINGJIE_PREVIEW_DIALOG",
  OPEN_JINGJIE_FRUIT_PREVIEW_DIALOG = "OPEN_JINGJIE_FRUIT_PREVIEW_DIALOG",
  OPEN_JINGJIE_TOOL_DIALOG = "OPEN_JINGJIE_TOOL_DIALOG",
  UPDATE_JINGJIE_SHOW = "UPDATE_JINGJIE_SHOW",
  OPEN_JINGJIE_TITLE_DIALOG = "OPEN_JINGJIE_TITLE_DIALOG",
  //------- 境界副本  start------------
  OPEN_STAGECOPY_DIALOG = "OPEN_STAGECOPY_DIALOG",
  CLOSE_STAGECOPY_DIALOG = "CLOSE_STAGECOPY_DIALOG",
  OPEN_STAGECOPY_CHAPTER_DIALOG = "OPEN_STAGECOPY_CHAPTER_DIALOG",
  CLOSE_STAGECOPY_CHAPTER_DIALOG = "CLOSE_STAGECOPY_CHAPTER_DIALOG",
  OPEN_STAGECOPY_FIGHT_DIALOG = "OPEN_STAGECOPY_FIGHT_DIALOG",
  CLOSE_STAGECOPY_FIGHT_DIALOG = "CLOSE_STAGECOPY_FIGHT_DIALOG",
  OPEN_STAGECOPY_REWARD_DIALOG = "OPEN_STAGECOPY_REWARD_DIALOG",
  CLOSE_STAGECOPY_REWARD_DIALOG = "CLOSE_STAGECOPY_REWARD_DIALOG",
  OPEN_STAGECOPY_REWARD_TIPS_DIALOG = "OPEN_STAGECOPY_REWARD_TIPS_DIALOG",
  CLOSE_STAGECOPY_REWARD_TIPS_DIALOG = "CLOSE_STAGECOPY_REWARD_TIPS_DIALOG",
  OPEN_SHOW_BUY_SWEEP_DIALOG = "OPEN_SHOW_BUY_SWEEP_DIALOG",
  OPEN_STAGECOPY_STORY_PREVIEW_DIALOG = "OPEN_STAGECOPY_STORY_PREVIEW_DIALOG",
  OPEN_STAGECOPY_STORY_DIALOGUE_DIALOG = "OPEN_STAGECOPY_STORY_DIALOGUE_DIALOG",
  OPEN_STAGECOPY_ADVENTURE_DIALOG = "OPEN_STAGECOPY_ADVENTURE_DIALOG",
  OPEN_STAGECOPY_ADVENTURE_END = "OPEN_STAGECOPY_ADVENTURE_END",
  OPEN_STAGECOPY_TARGET_DIALOG = "OPEN_STAGECOPY_TARGET_DIALOG",
  OPEN_STAGECOPY_SAODANG_DIALOG = "OPEN_STAGECOPY_SAODANG_DIALOG",
  CLOSE_STAGECOPY_TARGET_DIALOG = "CLOSE_STAGECOPY_TARGET_DIALOG",
  OPEN_STAGECOPY_CHOOSE_DIALOG = "OPEN_STAGECOPY_CHOOSE_DIALOG",
  CLOSE_STAGECOPY_CHOOSE_DIALOG = "CLOSE_STAGECOPY_CHOOSE_DIALOG",
  OPEN_STAGECOPY_GOODS_DIALOG = "OPEN_STAGECOPY_GOODS_DIALOG",
  OPEN_STAGECOPY_TALK_DIALOG = "OPEN_STAGECOPY_TALK_DIALOG",
  CLOSE_STAGECOPY_TALK_DIALOG = "CLOSE_STAGECOPY_TALK_DIALOG",
  OPEN_STAGECOPY_MISSION_DIALOG = "OPEN_STAGECOPY_MISSION_DIALOG",
  OPEN_STAGECOPY_PREVIEW_REWARD_TIPS_DIALOG = "OPEN_STAGECOPY_PREVIEW_REWARD_TIPS_DIALOG",
  OPEN_STAGECOPY_PREVIEW_REWARD_DIALOG = "OPEN_STAGECOPY_PREVIEW_REWARD_DIALOG",
  OPEN_STAGECOPY_MISSION_PREVIEW_DIALOG = "OPEN_STAGECOPY_MISSION_PREVIEW_DIALOG",
  OPEN_STAGECOPY_BAG_DIALOG = "OPEN_STAGECOPY_BAG_DIALOG",
  STAGECOPY_SHOW_EFFECT = "STAGECOPY_SHOW_EFFECT",
  STAGECOPY_INFO = "STAGECOPY_INFO",
  OPEN_STAGECOPY_EXPLORE_DIALOG = "OPEN_STAGECOPY_EXPLORE_DIALOG",
  OPEN_STAGECOPY_SHOP_DIALOG = "OPEN_STAGECOPY_SHOP_DIALOG",
  STAGECOPY_MONSTER = "STAGECOPY_MONSTER",
  STAGE_COPY_DROP_GROUP = "STAGE_COPY_DROP_GROUP",
  STAGECOPY_FIGHT_FINISH = "STAGECOPY_FIGHT_FINISH",
  STAGECOPY_FIGHT_FINISH_REBACK = "STAGECOPY_FIGHT_FINISH_REBACK", //战斗完成事件的回调
  UPDATE_STAGECOPY_MISSION = "UPDATE_STAGECOPY_SUB_MISSION",
  STAGECOPY_OPEN_PREVIEW_DIALOG = "STAGECOPY_OPEN_PREVIEW_DIALOG",
  STAGECOPY_MISSION_UPDATE = "STAGECOPY_MISSION_UPDATE",

  STAGECOPY_SHOW_STORY = "STAGECOPY_SHOW_STORY",
  OPEN_STAGECOPY_TEQUAN_DIALOG = "OPEN_STAGECOPY_TEQUAN_DIALOG",
  //------ 境界副本 end -----

  //------ 证神之技 start -----
  OPEN_STAGE_SKILL_DIALOG = "OPEN_STAGE_SKILL_DIALOG",
  OPEN_STAGE_SKILL_GIFT_DIALOG = "OPEN_STAGE_SKILL_GIFT_DIALOG",
  UPDATE_STAGE_SKILL_DIALOG = "UPDATE_STAGE_SKILL_DIALOG",
  //------ 证神之技 end -----

  ACC_GIFT_INFO = "ACC_GIFT_INFO",

  OPEN_GOODS_SHOW_DIALOG = "OPEN_GOODS_SHOW_DIALOG",

  //-------------武将觉醒 start ------------------
  OPEN_HERO_ZHOUYIN_GOODS_SELECT_DIALOG = "OPEN_HERO_ZHOUYIN_ITEM_SELECT_DIALOG",
  OPEN_HERO_ZHOUYIN_UPGRADE_COMPLETE_DIALOG = "OPEN_HERO_ZHOUYIN_UPGRADE_COMPLETE_DIALOG",  // 武将觉醒升级成功界面
  OPEN_HERO_ZHOUYIN_PREVIEW_DIALOG = "OPEN_HERO_ZHOUYIN_PREVIEW_DIALOG",
  OPEN_HERO_ZHOUYIN_UPGRADE_DIALOG = "OPEN_HERO_ZHOUYIN_UPGRADE_DIALOG",
  UPDATE_HERO_ZHOUYIN_INFO = "UPDATE_HERO_ZHOUYIN_INFO",
  UPGRADE_HERO_ZHOUYIN = "UPGRADE_HERO_ZHOUYIN",
  REFRESH_HERO_ZHOUYIN = "REFRESH_HERO_ZHOUYIN",
  //-------------武将觉醒 end ------------------

  //联盟 龙舟start ----------------------
  OPEN_FAMILY_BOAT_RACE_DIALOG = "OPEN_FAMILY_DRAGON_BOAT_DIALOG",   //打开龙舟
  OPEN_FAMILY_BOAT_RACE_CHEER_DIALOG = "OPEN_FAMILY_BOAT_RACE_CHEER_DIALOG",   //打开助威
  OPEN_FAMILY_BOAT_RACE_ON_BOAT_DIALOG = "OPEN_FAMILY_BOAT_RACE_ON_BOAT_DIALOG",
  OPEN_FAMILY_BOAT_RACE_REWARD_DIALOG = "OPEN_FAMILY_BOAT_RACE_REWARD_DIALOG",
  OPEN_FAMILY_BOAT_RACE_LOG_DIALOG = "OPEN_FAMILY_BOAT_RACE_LOG_DIALOG",
  OPEN_FAMILY_BOAT_RACE_AUCTION_DIALOG = "OPEN_FAMILY_BOAT_RACE_AUCTION_DIALOG",
  OPEN_FAMILY_BOAT_RACE_FINISH_RANK_DIALOG = "OPEN_FAMILY_BOAT_RACE_FINISH_RANK_DIALOG",
  OPEN_FAMILY_BOAT_RACE_FINISH_RESULT_DIALOG = "OPEN_FAMILY_BOAT_RACE_FINISH_RESULT_DIALOG",
  OPEN_FAMILY_BOAT_RACE_REACH_DIALOG = "OPEN_FAMILY_BOAT_RACE_REACH_DIALOG",
  OPEN_FAMILY_BOAT_RACE_FAIL_DIALOG = "OPEN_FAMILY_BOAT_RACE_FAIL_DIALOG",
  OPEN_FAMILY_BOAT_RACE_MEMBER_CHANGE_DIALOG = "OPEN_FAMILY_BOAT_RACE_MEMBER_CHANGE_DIALOG",
  OPEN_FAMILY_BOAT_RACE_BONUS_DIALOG = "OPEN_FAMILY_BOAT_RACE_BONUS_DIALOG",
  OPEN_FAMILY_BOAT_RACE_BONUS_LOG_DIALOG = "OPEN_FAMILY_BOAT_RACE_BONUS_LOG_DIALOG",
  OPEN_FAMILY_BOAT_RACE_REWARD_LOOK_DIALOG = "OPEN_FAMILY_BOAT_RACE_REWARD_LOOK_DIALOG",
  OPEN_FAMILY_BOAT_RACE_TE_QUAN_DIALOG = "OPEN_FAMILY_BOAT_RACE_TE_QUAN_DIALOG",
  CLOSE_FAMILY_BOAT_RACE_TE_QUAN_DIALOG = "CLOSE_FAMILY_BOAT_RACE_TE_QUAN_DIALOG",
  OPEN_FAMILY_BOAT_RACE_TE_QUAN_LOG_DIALOG = "OPEN_FAMILY_BOAT_RACE_TE_QUAN_LOG_DIALOG",
  FAMILY_BOAT_OPEN = "FAMILY_BOAT_OPEN",
  OPEN_FAMILY_BOAT_RACE_ALLOC_DIALOG = "OPEN_FAMILY_BOAT_RACE_ALLOC_DIALOG",
  OPEN_FAMILY_BOAT_RACE_ROLE_ITEM_DIALOG = "OPEN_FAMILY_BOAT_RACE_ROLE_ITEM_DIALOG",

  ON_FAMILY_BOAT_RACE_LOG_UPDATE = "ON_FAMILY_BOAT_RACE_LOG_UPDATE", //龙舟日志更新

  FAMILY_BOAT_RACE_OP = "FAMILY_BOAT_RACE_OP",
  FAMILY_BOAT_RACE_LOG = "FAMILY_BOAT_RACE_LOG",

  FAMILY_BOAT_INFO = "FAMILY_BOAT_INFO",
  FAMILY_BOAT_BROADCAST = "FAMILY_BOAT_BROADCAST",
  FAMILY_BOAT_RANK = "FAMILY_BOAT_RANK",
  FAMILY_BOAT_RACE_UPDATE = "FAMILY_BOAT_RACE_UPDATE",
  FAMILY_BOAT_RACE_BONUS = "FAMILY_BOAT_RACE_BONUS",
  FAMILY_BOAT_PALY_LIST = "FAMILY_BOAT_PALY_LIST",
  FAMILY_BOAT_MY_BOAT_UPDATE = "FAMILY_BOAT_MY_BOAT_UPDATE",

  FAMILY_BOAT_EVENT = "FAMILY_BOAT_EVENT",

  FAMILY_BOAT_PLAY_RACE_ITEM = "FAMILY_BOAT_PLAY_RACE_ITEM", //施放道具

  OPEN_FAMILY_HALL_ACTIVITY_DIALOG = "OPEN_FAMILY_HALL_ACTIVITY_DIALOG",   //打开联盟活动
  FAMILY_BOAT_RESULT_INFO_UPDATE = "FAMILY_BOAT_RESULT_INFO_UPDATE",//龙舟赛结果信息

  BOAT_RACE_ROLE_ITEMS_UPDATE = "BOAT_RACE_ROLE_ITEMS_UPDATE",
  BOAT_RACE_ROLE_ALLOC_ITEMS_UPDATE = "BOAT_RACE_ROLE_ALLOC_ITEMS_UPDATE",

  //联盟 龙舟end ------------------------

  //-------------天赋树---------------------------
  OPEN_TALENT_SCIENCE_DIALOG = "OPEN_TALENT_SCIENCE_DIALOG",
  CLOSE_TALENT_SCIENCE_DIALOG = "CLOSE_TALENT_SCIENCE_DIALOG",
  TALENT_SCIENCE_INFO = "TALENT_SCIENCE_INFO",
  TALENT_SCIENCE_UPDATE = "TALENT_SCIENCE_UPDATE",
  OPEN_TALENT_SCIENCE_FASTER_TOOL_DIALOG = "OPEN_TALENT_SCIENCE_FASTER_TOOL_DIALOG",
  //-------------天赋树End------------------------


  UPDATE_MAP_POSITION = "UPDATE_MAP_POSITION",

  MAP_CLICK = "MAP_CLICK",
  MAP_ITEM_CLICK = "MAP_ITEM_CLICK",

  //塔楼之战(马甲包)
  OPEN_STORY_TOWER_BATTLE_DIALOG = "OPEN_STORY_TOWER_BATTLE_DIALOG",
  CLOSE_STORY_TOWER_BATTLE_DIALOG = "CLOSE_STORY_TOWER_BATTLE_DIALOG",
  OPEN_STORY_TOWER_BATTLE_GAME_DIALOG = "OPEN_STORY_TOWER_BATTLE_GAME_DIALOG",
  CLOSE_STORY_TOWER_BATTLE_GAME_DIALOG = "CLOSE_STORY_TOWER_BATTLE_GAME_DIALOG",
  OPEN_STORY_TOWER_BATTLE_END_DIALOG = "OPEN_STORY_TOWER_BATTLE_END_DIALOG",
  CLOSE_STORY_TOWER_BATTLE_END_DIALOG = "CLOSE_STORY_TOWER_BATTLE_END_DIALOG",
  STORY_TOWER_BATTLE_DATA_INFO = "STORY_TOWER_BATTLE_DATA_INFO",
  STORY_TOWER_BATTLE_SHOW_3D_MOUSE_DOWN = "STORY_TOWER_BATTLE_SHOW_3D_MOUSE_DOWN",
  STORY_TOWER_BATTLE_BATTLE_END_HOUSE = "STORY_TOWER_BATTLE_BATTLE_END_HOUSE",
  END_GUIDE_GIRL_DIALOG = "END_GUIDE_GIRL_DIALOG",
  STORY_TOWER_BATTLE_HOUSE_CHANGE = "STORY_TOWER_BATTLE_HOUSE_CHANGE",
  STORY_TOWER_BATTLE_BATTLE_END_FLOOR = "STORY_TOWER_BATTLE_BATTLE_END_FLOOR",

  //---------攻城掠地 start ---------------------
  OPEN_STORY_SIEGELORD_SCENE_DIALOG = "OPEN_STORY_SIEGELORD_SCENE_DIALOG",
  OPEN_STORY_SIEGELORD_REWARD_DIALOG = "OPEN_STORY_SIEGELORD_REWARD_DIALOG",
  CLSOE_STORY_SIEGELORD_SCENE_DIALOG = "CLSOE_STORY_SIEGELORD_SCENE_DIALOG",
  OPEN_STORY_SIEGELORD_RESULT_DIALOG = "OPEN_STORY_SIEGELORD_RESULT_DIALOG",
  UPDATE_STORY_SIEGELORD_CITY_INFO = "UPDATE_STORY_SIEGELORD_CITY_INFO",
  RESET_STORY_SIEGELORD_CITY_DATA = "RESET_STORY_SIEGELORD_CITY_DATA",
  OPEN_STORY_SIEGELORD_GAME_DIALOG = "OPEN_STORY_SIEGELORD_GAME_DIALOG",
  CLOSE_STORY_SIEGELORD_GAME_DIALOG = "CLOSE_STORY_SIEGELORD_GAME_DIALOG",
  UPDATE_STORY_SIEGELORD_INFO = "UPDATE_STORY_SIEGELORD_INFO",
  //---------攻城掠地 end-------------------

  CLOSE_ALERT_PROP_DIALOG = "CLOSE_ALERT_PROP_DIALOG",

  //---------领主 start ---------------------
  OPEN_LORD_CHOOSE_DIALOG = "OPEN_LORD_CHOOSE_DIALOG",
  OPEN_LORD_ACTUAL_PREVIEW_DIALOG = "OPEN_LORD_ACTUAL_PREVIEW_DIALOG",
  UPDATE_LORD_INFO = "UPDATE_LORD_INFO",
  OPEN_LORD_COMPOSE_DIALOG = "OPEN_LORD_COMPOSE_DIALOG",
  OPEN_LORD_CHANGE_DIALOG = "OPEN_LORD_CHANGE_DIALOG",
  CLOSE_LORD_CHANGE_DIALOG = "CLOSE_LORD_CHANGE_DIALOG",
  OPEN_LORD_ACT_PRE_DETAIL_DIALOG = "OPEN_LORD_ACT_PRE_DETAIL_DIALOG",
  LORD_LOAD_EQUIP = "LORD_LOAD_EQUIP",
  OPEN_LORD_EQUIP_LOAD_DIALOG = "OPEN_LORD_EQUIP_LOAD_DIALOG",
  CLOSE_LORD_EQUIP_LOAD_DIALOG = "CLOSE_LORD_EQUIP_LOAD_DIALOG",
  REMOVE_LORD_SLOTS = "REMOVE_LORD_SLOTS",
  REQUEST_OPEN_LORD_EUQIPLOAD_EQUIP = "REQUEST_OPEN_LORD_EUQIPLOAD_EQUIP",
  OPEN_LORD_SKILL_LOAD_EQUIP = "OPEN_LORD_SKILL_LOAD_EQUIP",
  CLOSE_LORD_SKILL_LOAD_EQUIP = "CLOSE_LORD_SKILL_LOAD_EQUIP",
  OPEN_LORD_SKILL_DETAIL_DIALOG = "OPEN_LORD_SKILL_DETAIL_DIALOG",
  OPEN_LORD_SKILL_UPDATE_DIALOG = "OPEN_LORD_SKILL_UPDATE_DIALOG",
  OPEN_LORD_GRADE_UP_TIP_DIALOG = "OPEN_LORD_GRADE_UP_TIP_DIALOG",
  OPEN_LORD_SKILL_REMAKE_DIALOG = "OPEN_LORD_SKILL_REMAKE_DIALOG",
  CLOSE_LORD_SKILL_REMAKE_DIALOG = "CLOSE_LORD_SKILL_REMAKE_DIALOG",
  OPEN_LORD_LINE_UP_DIALOG = "OPEN_LORD_LINE_UP_DIALOG",
  ON_CLOSE_LORD_LINE_UP_DIALOG = "ON_CLOSE_LORD_LINE_UP_DIALOG",
  UPDATE_LORD_RECYCLE_PREVIEW_INFO = "UPDATE_LORD_RECYCLE_PREVIEW_INFO",
  UPDATE_LORD_RECYCLE = "UPDATE_LORD_RECYCLE",
  UPDATE_LORD_POS = "UPDATE_LORD_POS",
  OPEN_LORD_SKILL_EXCHANGE_DIALOG = "OPEN_LORD_SKILL_EXCHANGE_DIALOG",
  CLOSE_LORD_SKILL_EXCHANGE_DIALOG = "CLOSE_LORD_SKILL_EXCHANGE_DIALOG",
  OPEN_LORD_INFO_TIP_DIALOG = "OPEN_LORD_INFO_TIP_DIALOG",
  UPPDATE_LORD_SKILL_DIALOG = "UPPDATE_LORD_SKILL_DIALOG",
  OPEN_LORD_ACTIVE_DIALOG = "OPEN_LORD_ACTIVE_DIALOG",
  CLOSE_LORD_ACTIVE_DIALOG = "CLOSE_LORD_ACTIVE_DIALOG",
  UPDATE_LORD_GROUP_TYPE = "UPDATE_LORD_GROUP_TYPE",
  OPEN_LORD_SKILL_SELECT_DIALOG = "OPEN_LORD_SKILL_SELECT_DIALOG",
  OPEN_LORD_LORD_SKILL_SELECT_DIALOG = "OPEN_LORD_LORD_SKILL_SELECT_DIALOG",
  UPDATE_LORD_SKILL_SELECT = "UPDATE_LORD_SKILL_SELECT",
  UPDATE_LORD_LORD_SKILL_SELECT = "UPDATE_LORD_LORD_SKILL_SELECT",
  UPDATE_LORD_SKILL_ATTACK_INC_CALC = "UPDATE_LORD_SKILL_ATTACK_INC_CALC",
  OPEN_LORD_LORD_UPGRADE_STAR_DIALOG = "OPEN_LORD_LORD_UPGRADE_STAR_DIALOG",
  OPEN_LORD_UPGRADE_STAR_COMPLETE_DIALOG = "OPEN_LORD_UPGRADE_STAR_COMPLETE_DIALOG",
  OPEN_LORD_EXCHANGE_DIALOG = "OPEN_LORD_EXCHANGE_DIALOG",
  UPDATE_LORD_PREVIEW_COMPLETE = "UPDATE_LORD_PREVIEW_COMPLETE",
  UPDATE_LORD_VIEW_CHILD_PAGE = "UPDATE_LORD_VIEW_CHILD_PAGE",
  UPDATE_LORD_SELECT_VIEW_CHILD_PAGE = "UPDATE_LORD_SELECT_VIEW_CHILD_PAGE",
  OPEN_LORD_EQUIP_TARGET_SELECT_DIALOG = "OPEN_LORD_EQUIP_SELECT_DIALOG",
  UPDATE_LORD_EQUIP_COMPOSE_INFO = "UPDATE_LORD_EQUIP_COMPOSE_INFO",
  LORD_EQUIP_COMPOSE_ITEM_SELECT_DIALOG = "LORD_EQUIP_COMPOSE_ITEM_SELECT_DIALOG",

  //---------领主 end-------------------

  //---------领主宝物start ---------------------
  OPEN_LORD_TREASURE_DIALOG = "OPEN_LORD_TREASURE_DIALOG",
  UPDATE_SELECT_LORD_TREASURE_DIALOG_INDEX = "UPDATE_SELECT_LORD_TREASURE_DIALOG_INDEX",
  UPDATE_LORD_TREASURE_OP = "UPDATE_LORD_TREASURE_OP",
  OPEN_LORD_TREASURE_TIP_DIALOG = "OPEN_LORD_TREASURE_TIP_DIALOG",
  OPEN_LORD_BAG_INFO = "OPEN_LORD_BAG_INFO",

  //---------领主宝物end ---------------------


  //----------新家族 star ------------
  OPEN_CSCLAN_DIALOG = "OPNE_CSCLAN_DIALOG",
  CLOSE_CSCLAN_DIALOG = "CLOSE_CSCLAN_DIALOG",
  OPEN_CSCLAN_LOG_DIALOG = "OPNE_CSCLAN_LOG_DIALOG",
  OPEN_CSCLAN_MEMBER_DIALOG = "OPNE_CSCLAN_MEMBER_DIALOG",
  OPNE_CSCLAN_WELCOME_DIALOG = "OPNE_CSCLAN_WELCOME_DIALOG",
  OPEN_CSCLAN_NOT_JOIN_DIALOG = "OPEN_CSCLAN_NOT_JOIN_DIALOG",
  CLOSE_CSCLAN_NOT_JOIN_DIALOG = "CLOSE_CSCLAN_NOT_JOIN_DIALOG",

  OPEN_CSCLAN_HALL_DIALOG = "OPEN_CSCLAN_HALL_DIALOG",
  CLOSE_CSCLAN_HALL_DIALOG = "CLOSE_CSCLAN_HALL_DIALOG",
  CLOSE_CSCLAN_MEMBER_DIALOG = "CLOSE_CSCLAN_MEMBER_DIALOG",
  OPEN_CSCLAN_APPLY_LIST_DIALOG = "OPEN_CSCLAN_APPLY_LIST_DIALOG",
  CLOSE_CSCLAN_APPLY_LIST_DIALOG = "CLOSE_CSCLAN_APPLY_LIST_DIALOG",
  INFO_CSCLAN_LIST = "INFO_CSCLAN_LIST",
  UPDATE_CSCLAN_INFO = "UPDATE_CSCLAN_INFO",
  OPEN_CREATE_CSCLAN_DIALOG = "OPEN_CREATE_CSCLAN_DIALOG",
  CLOSE_CREATE_CSCLAN_DIALOG = "CLOSE_CREATE_CSCLAN_DIALOG",
  OPEN_CSCLAN_ALERT_DIALOG = "OPEN_CSCLAN_ALERT_DIALOG",

  CSCLAN_SCENE_TOC = "CSCLAN_SCENE_TOC",
  LEAVE_CSCLAN = "LEAVE_CSCLAN",
  INFO_CSCLAN_MEMBERS = "INFO_CSCLAN_MEMBERS",
  INFO_CSCLAN_APPLY_LIST = "INFO_CSCLAN_APPLY_LIST",
  ON_QUIT_CSCLAN = "ON_QUIT_CSCLAN",
  OPEN_CSCLAN_MANAGER_VIEW = "OPEN_CSCLAN_MANAGER_VIEW",
  UPDATE_CSCLAN_MEMBER = "UPDATE_CSCLAN_MEMBER",
  UPDATE_CSCLAN_LOG_DIALOG = "UPDATE_CSCLAN_LOG_DIALOG",
  OPEN_CSCLAN_HEAD_VIEW = "OPEN_CSCLAN_HEAD_VIEW",
  CLOSE_CSCLAN_HEAD_VIEW = "CLOSE_CSCLAN_HEAD_VIEW",
  CLOSE_CSCLAN_MANAGER_VIEW = "CLOSE_CSCLAN_MANAGER_VIEW",
  //----------新家族 end ------------

  //------家族红包 start -----
  OPEN_CSCLAN_REDPACK_DIALOG = "OPEN_CSCLAN_REDPACK_DIALOG",
  CLOSE_CSCLAN_REDPACK_DIALOG = "CLOSE_CSCLAN_REDPACK_DIALOG",
  OPEN_CSCLAN_REDPACK_OPEN_LIST_DIALOG = "OPEN_CSCLAN_REDPACK_OPEN_LIST_DIALOG",

  CSCLAN_REDPACK_UPDATE = "CSCLAN_REDPACK_UPDATE",
  CSCLAN_REDPACK_INFO = "CSCLAN_REDPACK_INFO",
  CSCLAN_REDPACK_BLESS_CHOOSE = "CSCLAN_REDPACK_BLESS_CHOOSE",
  CSCLAN_REDPACK_ITEM_CHOOSE = "CSCLAN_REDPACK_ITEM_CHOOSE",
  CSCLAN_REDPACK_FETCH_UPDATE = "CSCLAN_REDPACK_FETCH_UPDATE",
  CSCLAN_REDPACK_FETCH_TIMEOUT = "CSCLAN_REDPACK_FETCH_TIMEOUT",
  REQUEST_USE_REDPACK = "REQUEST_USE_REDPACK",
  //------家族红包 end -----

  OPEN_ACT_MODS_ACTIVITY_DAY_SHOP_DIALOG = "OPEN_ACT_MODS_ACTIVITY_DAY_SHOP_DIALOG",
  UPDATE_ACT_MODS_ACTIVITY_DAY_SHOP = "UPDATE_ACT_MODS_ACTIVITY_DAY_SHOP",

  OPEN_ACT_MODS_LORD_GALA_DIALOG = "OPEN_ACT_MODS_LORD_GALA_DIALOG",

  //----------龙舟争霸赛 star ------------
  OPEN_BOAT_PEAK_DIALOG = "OPEN_BOAT_PEAK_DIALOG",
  CLOSE_BOAT_PEAK_DIALOG = "CLOSE_BOAT_PEAK_DIALOG",
  OPEN_BOAT_PEAK_SCORE_RANK_DIALOG = "OPEN_BOAT_PEAK_SCORE_RANK_DIALOG",
  UPDATE_BOAT_PEAK_INFO = "UPDATE_BOAT_PEAK_INFO",
  UPDATE_BOAT_PEAK_INFO_OPP = " UPDATE_BOAT_PEAK_INFO_OPP",
  UPDATE_BOAT_PEAK_INFO_MEMBER = " UPDATE_BOAT_PEAK_INFO_MEMBER",
  UPDATE_BOAT_PEAK_INFO_LOOK_MEMBER = " UPDATE_BOAT_PEAK_INFO_LOOK_MEMBER",
  UPDATE_BOAT_PEAK_INFO_BATTLE = " UPDATE_BOAT_PEAK_INFO_BATTLE",
  UPDATE_BOAT_PEAK_INFO_BET = " UPDATE_BOAT_PEAK_INFO_BET",
  UPDATE_BOAT_PEAK_INFO_HISTORY = " UPDATE_BOAT_PEAK_INFO_HISTORY",
  UPDATE_BOAT_PEAK_INFO_GET_MSG = " UPDATE_BOAT_PEAK_INFO_GET_MSG",
  UPDATE_BOAT_PEAK_INFO_PERSONAL = " UPDATE_BOAT_PEAK_INFO_PERSONAL",
  // UPDATE_BOAT_PEAK_BATTLE_RESULT = " UPDATE_BOAT_PEAK_BATTLE_RESULT",
  OPEN_BOAT_PEAK_RACE_REWARD_DIALOG = "OPEN_BOAT_PEAK_RACE_REWARD_DIALOG",
  OPEN_BOAT_PEAK_RACE_SCHEDULE_DIALOG = "OPEN_BOAT_PEAK_RACE_SCHEDULE_DIALOG",
  OPEN_BOAT_PEAK_REPORT_DIALOG = "OPEN_BOAT_PEAK_REPORT_DIALOG",
  OPEN_BOAT_PEAK_GUESS_DIALOG = "OPEN_BOAT_PEAK_GUESS_DIALOG",
  OPEN_BOAT_PEAK_HISTORY_DIALOG = "OPEN_BOAT_PEAK_HISTORY_DIALOG",
  OPEN_BOAT_PEAK_BET_GIFT_DIALOG = "OPEN_BOAT_PEAK_BET_GIFT_DIALOG",
  UPDATE_BOAT_PEAK_SHOP_HISTORY = " UPDATE_BOAT_PEAK_SHOP_HISTORY",
  OPEN_BOAT_PEAK_RANK_DETAIL = " OPEN_BOAT_PEAK_RANK_DETAIL",
  UPDATE_BOAT_PEAK_PRE_SCORE_HISTORY = " UPDATE_BOAT_PEAK_PRE_SCORE_HISTORY",
  OPEN_BOAT_PEAK_PRE_DIALOG = "OPEN_BOAT_PEAK_PRE_DIALOG",
  OPEN_BOAT_PEAK_SCHEDULE_DIALOG = "OPEN_BOAT_PEAK_SCHEDULE_DIALOG",
  OPEN_BOAT_PEAK_LIST_DIALOG = "OPEN_BOAT_PEAK_LIST_DIALOG",
  UPDATE_BOAT_PEAK_JOIN_MENBER_LIST = "UPDATE_BOAT_PEAK_JOIN_MENBER_LIST",
  OPEN_BOAT_PEAK_NOTICE_DIALOG = "OPEN_BOAT_PEAK_NOTICE_DIALOG",
  //----------龙舟争霸赛 end ------------

  //许愿
  UPDATE_ACT_MODS_WISH_INFO = "UPDATE_ACT_MODS_WISH_INFO",
  OPEN_ACT_MODS_WISH_SELECT_DIALOG = "OPEN_ACT_MODS_WISH_SELECT_DIALOG",  //打开自选抽将选择界面
  OPEN_ACT_MODS_WISH_SHOP_DIALOG = "OPEN_ACT_MODS_WISH_SHOP_DIALOG",  //打开自选抽将选择界面
  UPDATE_ACT_MODS_WISH_SELECT_HERO = "UPDATE_ACT_MODS_WISH_SELECT_HERO",
  START_ACT_MODS_WISH_EFFECT = "START_ACT_MODS_WISH_EFFECT",

  //------  英魂卡---------------
  UPDATE_FISH_EXTREME_SATISFY_DATA = "UPDATE_FISH_EXTREME_SATISFY_DATA",
  OPEN_FISH_DIALOG = "OPEN_FISH_DIALOG",
  CLOSE_FISH_DIALOG = "CLOSE_FISH_DIALOG",
  SHOW_FISH_GUIDE = "SHOW_FISH_GUIDE",
  OPEN_FISH_COMPARE_DIALOG = "OPEN_FISH_COMPARE_DIALOG",
  CLOSE_FISH_COMPARE_DIALOG = "CLOSE_FISH_COMPARE_DIALOG",
  UPDATE_FISH_DIALOG_BG = "UPDATE_FISH_DIALOG_BG",
  UPDATE_FISH_DIALOG = "UPDATE_FISH_DIALOG",
  UPDATE_FISH_UP_Level = "UPDATE_FISH_UP_OFFICIAL",
  OPEN_FISH_LEVEL_DIALOG = "OPEN_FISH_LEVEL_DIALOG",
  CLOSE_FISH_LEVEL_DIALOG = "CLOSE_FISH_LEVEL_DIALOG",
  OPEN_FISH_AUTO_DIALOG = "OPEN_FISH_AUTO_DIALOG",
  CLOSE_FISH_AUTO_DIALOG = "CLOSE_FISH_AUTO_DIALOG",
  OPEN_FISH_INFO_DIALOG = "OPEN_FISH_INFO_DIALOG",
  CLOSE_FISH_INFO_DIALOG = "CLOSE_FISH_INFO_DIALOG",
  UPDATE_FISH_LEVEL = "UPDATE_FISH_LEVEL",
  OPEN_FISH_AUTO_OPERATE = "OPEN_FISH_AUTO_OPERATE",
  CLOSE_FISH_AUTO_OPERATE = "CLOSE_FISH_AUTO_OPERATE",
  MASTER_LOTTERY_TOC = "MASTER_LOTTERY_TOC",
  SUCCEED_FISH_OUT = "SUCCEED_FISH_OUT",
  OPEN_FISH_ATTRI_SHOW_DIALOG = "OPEN_FISH_ATTRI_SHOW_DIALOG",
  CLOSE_FISH_ATTRI_SHOW_DIALOG = "CLOSE_FISH_ATTRI_SHOW_DIALOG",
  FISH_CHECK_GO_BATTLE_ANIMATION = "FISH_CHECK_GO_BATTLE_ANIMATION",
  FISH_GO_BATTLE_ANIMATION_START = "FISH_GO_BATTLE_ANIMATION_START",
  FISH_GO_BATTLE_ANIMATION_END = "FISH_GO_BATTLE_ANIMATION_END",
  UPDATE_FISH_QUENE = "UPDATE_FISH_QUENE",
  FISH_UPDATE_LEVEL_ONT_ON_LINE = "FISH_UPDATE_LEVEL_ONT_ON_LINE",
  OPEN_FISH_SHOP_DIALOG = "OPEN_FISH_SHOP_DIALOG",
  CLOSE_FISH_SHOP_DIALOG = "CLOSE_FISH_SHOP_DIALOG",
  OPEN_FISH_FASTER_TOOL_DIALOG = "OPEN_FISH_FASTER_TOOL_DIALOG",
  CLOSE_FISH_FASTER_TOOL_DIALOG = "CLOSE_FISH_FASTER_TOOL_DIALOG",
  UPDATE_FISH_FASTER_TOOL_DIALOG = "UPDATE_FISH_FASTER_TOOL_DIALOG",
  OPEN_FISH_UP_GUANG_DIALOG = "OPEN_FISH_UP_GUANG_DIALOG",
  CLOSE_FISH_UP_GUANG_DIALOG = "CLOSE_FISH_UP_GUANG_DIALOG",
  OPEN_FISH_GUANG_CHANGE_DIALOG = "OPEN_FISH_GUANG_CHANGE_DIALOG",
  CLOSE_FISH_GUANG_CHANGE_DIALOG = "CLOSE_FISH_GUANG_CHANGE_DIALOG",
  OPEN_FISH_GUANG_UP_TIP_DIALOG = "OPEN_FISH_GUANG_UP_TIP_DIALOG",
  SHOW_FISH_GUANGUP_EFFCT = "SHOW_FISH_GUANGUP_EFFCT",
  CLOSE_FISH_GUANG_UP_TIP_DIALOG = "CLOSE_FISH_GUANG_UP_TIP_DIALOG",
  OPEN_FISH_UP_GUANG_RESULT_DIALOG = "OPEN_FISH_UP_GUANG_RESULT_DIALOG",
  CLOSE_FISH_UP_GUANG_RESULT_DIALOG = "CLOSE_FISH_UP_GUANG_RESULT_DIALOG",
  OPEN_FISH_PREVIEW_ATTR = "OPEN_FISH_PREVIEW_ATTR",
  UPDATE_FISH_HEADER_ITEM = "UPDATE_FISH_HEADER_ITEM",
  OPEN_FISH_RES_HAPE_DIALOG = "OPEN_FISH_RES_HAPE_DIALOG",
  CLOSE_FISH_RES_HAPE_DIALOG = "CLOSE_FISH_RES_HAPE_DIALOG",
  UPADATE_FISH_RES_TYPE = "UPADATE_FISH_RES_TYPE",
  SHOW_FISH_POWER = "SHOW_FISH_POWER",
  UPDATE_BOTTOM_FISH_HAS_TIP = "UPDATE_BOTTOM_FISH_HAS_TIP",
  OPEN_FISH_LOTTRY_SHOW_DIALOG = "OPEN_FISH_LOTTRY_SHOW_DIALOG",
  CLOSE_FISH_LOTTRY_SHOW_DIALOG = "CLOSE_FISH_LOTTRY_SHOW_DIALOG",
  AUTO_BOTTOM_FISH = "AUTO_BOTTOM_FISH",
  OPEN_FISH_INFO_COMPARE_DIALOG = "OPEN_FISH_INFO_COMPARE_DIALOG",
  CLOSE_FISH_INFO_COMPARE_DIALOG = "CLOSE_FISH_INFO_COMPARE_DIALOG",
  OPEN_FISH_LOG_DIALOG = "OPEN_FISH_LOG_DIALOG",
  CLOSE_FISH_LOG_DIALOG = "CLOSE_FISH_LOG_DIALOG",
  OPEN_FISH_MAX_PREVIEW_DIALOG = "OPEN_FISH_MAX_PREVIEW_DIALOG",
  OPEN_FISH_SHOW_DIALOG = "OPEN_FISH_SHOW_DIALOG",
  OPEN_FISH_TANK_DIALOG = "OPEN_FISH_TANK_DIALOG",
  OPEN_FISH_SCENE_DIALOG = "OPEN_FISH_SCENE_DIALOG",
  OPEN_FISH_ALERT_DIALOG = "OPEN_FISH_ALERT_DIALOG",
  OPEN_FISH_SELECT_DIALOG = "OPEN_FISH_SELECT_DIALOG",
  OPEN_FISH_TANK_ACTIVE_DIALOG = "OPEN_FISH_TANK_ACTIVE_DIALOG",
  OPEN_FISH_SCENE_UNLOCK_DIALOG = "OPEN_FISH_SCENE_UNLOCK_DIALOG",
  UPDATE_FISH_TANK = "UPDATE_FISH_TANK",
  UPDATE_FISH_FISHBOWL = "UPDATE_FISH_FISHBOWL",
  UPDATE_FISH_SELECT = "UPDATE_FISH_SELECT",
  /**名将卡-助力商店 */
  UPDATE_FISH_HELP_SHOP_DIALOG = "UPDATE_FISH_HELP_SHOP_DIALOG",
  /**更新广告视频相关  */
  UPDATE_FISH_ADVERT = "UPDATE_FISH_ADVERT",
  /**名将卡-首充福利 */
  OPEN_FISH_FIRST_GIFT_DIALOG = "OPEN_FISH_FIRST_GIFT_DIALOG",
  CLOSE_FISH_FIRST_GIFT_DIALOG = "CLOSE_FISH_FIRST_GIFT_DIALOG",
  UPDATE_FISH_FIRST_PAY_TASK_INFO = "UPDATE_FISH_FIRST_PAY_TASK_INFO",
  OPEN_FISH_FIRST_RECHARGE_REWARD_LOOK_DIALOG = "OPEN_FISH_FIRST_RECHARGE_REWARD_LOOK_DIALOG",
  OPEN_FISH_HNADLER_DIALOG = "OPEN_FISH_HNADLER_DIALOG",
  CLOSE_FISH_HNADLER_DIALOG = "CLOSE_FISH_HNADLER_DIALOG",
  FISH_SHOW_LOTTERY_SPE_EFFECT = "FISH_SHOW_LOTTERY_SPE_EFFECT",
  FISH_LOG = "FISH_LOG",
  FISH_TANK_ATTR = "FISH_TANK_ATTR",
  FISH_GAINS_TOC = "FISH_GAINS_TOC",
  /**名将卡-成长基金 */
  OPEN_FISH_FULI_TOKEN_DIALOG = "OPEN_FISH_FULI_TOKEN_DIALOG",
  FISH_LINEUP_LIST = "FISH_LINEUP_LIST",

  OPEN_FISH_ANI_EDITOR_DIALOG = "OPEN_FISH_ANI_EDITOR_DIALOG",
  FISH_ANI_EDITOR_UPDATE = "FISH_ANI_EDITOR_UPDATE",

  FISH_HANDBOOK_INFO = "FISH_HANDBOOK_INFO",
  OPEN_FISH_HANDBOOK_DIALOG = "OPEN_FISH_HANDBOOK_DIALOG",
  OPEN_FISH_HANDBOOK_REWARD_DIALOG = "OPEN_FISH_HANDBOOK_REWARD_DIALOG",
  OPEN_FISH_HANDBOOK_REWARD_SHOW_DIALOG = "OPEN_FISH_HANDBOOK_REWARD_SHOW_DIALOG",
  OPEN_FISH_TANK_ATTR_SHOW_DIALOG = "OPEN_FISH_TANK_ATTR_SHOW_DIALOG",
  OPEN_FISH_LEVEL_PREVIEW_DIALOG = "OPEN_FISH_LEVEL_PREVIEW_DIALOG",


  //------  英魂卡---------------

  //------  塔防 start---------------

  OPEN_TD_TRIAL_EDITOR_DIALOG = "OPEN_TD_TRIAL_EDITOR_DIALOG",
  CLOSE_TD_TRIAL_EDITOR_DIALOG = "CLOSE_TD_TRIAL_EDITOR_DIALOG",
  UPDATE_TD_TRIAL_EDITOR_PATH_POINT = "UPDATE_TD_TRIAL_EDITOR_PATH_POINT",

  OPEN_TOWER_DEFENSE_UPDATE = "OPEN_TOWER_DEFENSE_UPDATE",
  TD_POS_CHANGE = "TD_POS_CHANGE",
  TD_OUT_LINEUP = "TD_OUT_LINEUP",

  ON_TD_FIGHT_GET_INFO = "OPEN_TD_MAIN_FIGHT_GET_INFO",
  ON_TD_REQUEST_FIGHT_FAIL = "ON_TD_FIGHT_FAIL",

  TD_HERO_MENU_BOX_SHOW = "TD_HERO_MENU_BOX_SHOW",

  CLOSE_TD_PROP_EDITOR_VIEW = "CLOSE_TD_PROP_EDITOR_VIEW",
  //------  塔防 end---------------

  TD_FIGHT_TOC = "TD_FIGHT_TOC_",
  TD_LINEUP_TOC = "TD_LINEUP_TOC_",

  //------  塔防试炼 start---------------

  OPEN_TD_TRIAL_DIALOG = "OPEN_TD_TRIAL_DIALOG",
  OPEN_TD_TRIAL_RANK_DIALOG = "OPEN_TD_TRIAL_RANK_DIALOG",
  OPEN_TD_TRIAL_REWARD_PREVIEW_DIALOG = "OPEN_TD_TRIAL_REWARD_PREVIEW_DIALOG",
  OPEN_TD_TRIAL_GAME_DIALOG = "OPEN_TD_TRIAL_GAME_DIALOG",
  CLOSE_TD_TRIAL_GAME_DIALOG = "CLOSE_TD_TRIAL_GAME_DIALOG",

  TD_TRIAL_INFO_UPDATE = "OPEN_TD_TRIAL_INFO_UPDATE",
  OPEN_TD_TRIAL_SKILL_UPDATE = "OPEN_TD_TRIAL_SKILL_UPDATE",
  OPEN_TD_TRIAL_START_FIGHT = "OPEN_TD_TRIAL_START_FIGHT",
  TD_TRIAL_NEXT_FIGHT = "TD_TRIAL_NEXT_FIGHT",
  TD_TRIAL_LINE_UP_UPDATE = "TD_TRIAL_LINE_UP_UPDATE",
  //------  塔防试炼 end---------------

  //-------- 主线任务 -----------
  MAIN_MISSION_UPDATE = "MAIN_MISSION_UPDATE",
  //-----------------

  //--------塔防挂机-----------
  OPEN_TD_MAIN_DIALOG = "OPEN_TD_MAIN_DIALOG",
  CLOSE_TD_MAIN_DIALOG = "CLOSE_TD_MAIN_DIALOG",
  TD_MAIN_NULL_CLICK = "TD_MAIN_NULL_CLICK",
  OPEN_TD_HERO_SELECT = "OPEN_TD_MAIN_HERO_SELECT",

  TD_MAIN_LINE_UP_UPDATE = "TD_MAIN_LINE_UP_UPDATE",
  TD_MAIN_MISSION_UPDATE = "TD_MAIN_MISSION_UPDATE",

  OPEN_TD_MAIN_INFO_UPDATE = "OPEN_TD_MAIN_INFO_UPDATE",
  TD_MISSION_INFO_UPDATE = "TD_MISSION_INFO_UPDATE",
  OPEN_TD_MAIN_START_FIGHT = "OPEN_TD_MAIN_START_FIGHT",
  TD_MAIN_NEXT_FIGHT = "TD_MAIN_NEXT_FIGHT",

  OPEN_TD_GUIDE_NEW_HERO_DIALOG = "OPEN_TD_GUIDE_NEW_HERO_DIALOG",
  ON_CLOSE_TD_GUIDE_NEW_HERO_DIALOG = "ON_CLOSE_TD_GUIDE_NEW_HERO_DIALOG",

  //------  塔防挂机 end---------------
  //--------日常副本塔防-----------
  OPEN_TD_DAILY_COPY_DIALOG = "OPEN_TD_DAILY_COPY_DIALOG",
  CLOSE_TD_DAILY_COPY_DIALOG = "CLOSE_TD_DAILY_COPY_DIALOG",
  TD_DAILY_COPY_LINE_UP_UPDATE = "TD_DAILY_COPY_LINE_UP_UPDATE",
  TD_DAILY_COPY_INFO = "TD_DAILY_COPY_INFO",
  OPEN_TD_DAILY_COPY_START_FIGHT = "OPEN_TD_DAILY_COPY_START_FIGHT",



  TD_MISSION_UPDATE = "TD_MISSION_UPDATE",

  //------ 模拟对战 start -------
  OPEN_MOCK_PVP_TUJIAN_DIALOG = "OPEN_MOCK_PVP_TUJIAN_DIALOG",
  UPDATE_MOCK_PVP_INFO = "UPDATE_MOCK_PVP_INFO",
  OPEN_MOCK_PVP_DIALOG = "OPEN_MOCK_PVP_DIALOG",
  CLOSE_MOCK_PVP_DIALOG = "CLOSE_MOCK_PVP_DIALOG",
  OPEN_MOCK_PVP_LINE_UP_DIALOG = "OPEN_MOCK_PVP_LINE_UP_DIALOG",
  CLOSE_MOCK_PVP_LINE_UP_DIALOG = "CLOSE_MOCK_PVP_LINE_UP_DIALOG",
  OPEN_MOCK_PVP_LOGS_DIALOG = "OPEN_MOCK_PVP_LOGS_DIALOG",
  UPDATE_MOCK_PVP_CONFIG = "UPDATE_MOCK_PVP_CONFIG",
  UPDATE_MOCK_PVP_READY = "UPDATE_MOCK_PVP_READY",
  UPDATE_MOCK_PVP_TOPS = "UPDATE_MOCK_PVP_TOPS",
  // UPDATE_MOCK_PVP_RESULT = "UPDATE_MOCK_PVP_RESULT",
  OPEN_MOCK_PVP_WAITING_DIALOG = "OPEN_MOCK_PVP_WAITING_DIALOG",
  CLOSE_MOCK_PVP_WAITING_DIALOG = "CLOSE_MOCK_PVP_WAITING_DIALOG",
  // UPDATE_MOCK_PVP_MATCH = "UPDATE_MOCK_PVP_MATCH",
  OPEN_MOCK_PVP_READY_DIALOG = "OPEN_MOCK_PVP_READY_DIALOG",
  CLOSE_MOCK_PVP_READY_DIALOG = "CLOSE_MOCK_PVP_READY_DIALOG",

  OPEN_MOCK_PVP_MISSION_DIALOG = "OPEN_MOCK_PVP_MISSION_DIALOG",
  CLOSE_MOCK_PVP_MISSION_DIALOG = "CLOSE_MOCK_PVP_MISSION_DIALOG",
  UPDATE_MOCK_PVP_MISSION_DIALOG = "UPDATE_MOCK_PVP_MISSION_DIALOG",
  // UPDATE_MOCK_PVP_LINE_UP = "UPDATE_MOCK_PVP_LINE_UP",
  OPEN_MOCK_PVP_CONFIGS_DIALOG = "OPEN_MOCK_PVP_CONFIGS_DIALOG",
  OPEN_MOCK_PVP_CONFIGS_CHANGE_BINGFA_DIALOG = "OPEN_MOCK_PVP_CONFIGS_CHANGE_BINGFA_DIALOG",
  OPEN_MOCK_PVP_CONFIGS_CHANGE_GOD_EQUIP_DIALOG = "OPEN_MOCK_PVP_CONFIGS_CHANGE_GOD_EQUIP_DIALOG",
  UPDATE_MOCK_PVP_LOAD_GOD_WEAPON = "UPDATE_MOCK_PVP_LOAD_GOD_WEAPON",
  OPEN_MOCK_PVP_SPARRING_DIALOG = "OPEN_MOCK_PVP_SPARRING_DIALOG",
  OPEN_MOCK_PVP_SPARRING_WAITING_DIALOG = "OPEN_MOCK_PVP_SPARRING_WAITING_DIALOG",
  CLOSE_MOCK_PVP_SPARRING_WAITING_DIALOG = "CLOSE_MOCK_PVP_SPARRING_WAITING_DIALOG",
  UPDATE_MOCK_PVP_SPARRING_WAITING_DIALOG = "UPDATE_MOCK_PVP_SPARRING_WAITING_DIALOG",
  FIGHT_MOCK_PVP_FINISH = "FIGHT_MOCK_PVP_FINISH",
  OPEN_MOCK_PVP_LINEUP_CONFIG_DIALOG = "OPEN_MOCK_PVP_LINEUP_CONFIG_DIALOG",
  OPEN_MOCK_PVP_RANK_DIALOG = "OPEN_MOCK_PVP_RANK_DIALOG",
  UPDATE_MOCK_PVP_CHAT_SHARE = "UPDATE_MOCK_PVP_CHAT_SHARE",
  OPEN_MOCK_PVP_HEAT_DIALOG = "OPEN_MOCK_PVP_HEAT_DIALOG",
  OPEN_MOCK_PVP_HEAT_HERO_DIALOG = "OPEN_MOCK_PVP_HEAT_HERO_DIALOG",
  OPEN_MOCK_PVP_HEAT_LOAD_DIALOG = "OPEN_MOCK_PVP_HEAT_LOAD_DIALOG",
  OPEN_MOCK_PVP_LINE_UP_PREVIEW_DIALOG = "OPEN_MOCK_PVP_LINE_UP_PREVIEW_DIALOG",
  //------ 模拟对战 end -------

  //------ 闯关千抽------6
  OPEN_MISSION_LOTTRY_DIALOG = "OPEN_MISSION_LOTTRY_DIALOG",
  OPEN_MISSION_LOTTRY_PREVIEW_DIALOG = "OPEN_MISSION_LOTTRY_PREVIEW_DIALOG",
  ON_CLOSE_MISSION_LOTTRY_PREVIEW_DIALOG = "ON_CLOSE_MISSION_LOTTRY_PREVIEW_DIALOG",


  //------  神临装备 start---------------
  OPEN_DIVINE_EQUIP_ACTIVE_DIALOG = "OPEN_DIVINE_EQUIP_ACTIVE_DIALOG",
  OPEN_DIVINE_HERO_ACTIVE_DIALOG = "OPEN_DIVINE_HERO_ACTIVE_DIALOG",
  OPEN_DIVINE_EQUIP_PREVIEW_DIALOG = "OPEN_DIVINE_EQUIP_PREVIEW_DIALOG",
  UPDATE_DIVINE_INFO = "UPDATE_DIVINE_INFO",
  UPDATE_DIVINE_EQUIP_ACTIVATE = "UPDATE_DIVINE_EQUIP_ACTIVATE",
  UPDATE_EQUIP_LOCK = "UPDATE_EQUIP_LOCK",
  OPEN_DIVINE_EQUIP_DIALOG = "OPEN_DIVINE_EQUIP_DIALOG",
  OPEN_DIVINE_EQUIP_FILTRATE_DIALOG = "OPEN_DIVINE_EQUIP_FILTRATE_DIALOG",
  UPDATE_DIVINE_EQUIP_FILTRATE_LIST = "UPDATE_DIVINE_EQUIP_FILTRATE_LIST",
  UPDATE_DIVINE_EQUIP_DECOMPOSE_LIST = "UPDATE_DIVINE_EQUIP_DECOMPOSE_LIST",
  OPEN_DIVINE_RESET_CONFIRM_DIALOG = "OPEN_DIVINE_RESET_CONFIRM_DIALOG",
  OPEN_DIVINE_LOAD_DIALOG = "OPEN_DIVINE_LOAD_DIALOG",
  OPEN_DIVINE_STRENGTH_DIALOG = "OPEN_DIVINE_STRENGTH_DIALOG",
  UPDATE_DIVINE_EQUIP_COMPOSE_FILTRATE_LIST = "UPDATE_DIVINE_EQUIP_COMPOSE_FILTRATE_LIST",
  OPEN_DIVINE_COMPOSE_SUIT_CHOOSE_DIALOG = "OPEN_DIVINE_COMPOSE_SUIT_CHOOSE_DIALOG",
  SET_CHOOSE_TARGET_SUIT_KIND = "SET_CHOOSE_TARGET_SUIT_KIND",
  UPDATE_DIVINE_EQUIP_COMPOSE = "UPDATE_DIVINE_EQUIP_COMPOSE",
  UPDATE_DIVINE_EQUIP_COMPOSE_LIST = "UPDATE_DIVINE_EQUIP_COMPOSE_LIST",
  UPDATE_DIVINE_EQUIP_UPGRADE = "UPDATE_DIVINE_EQUIP_UPGRADE",
  OPEN_DIVINE_COMPOSE_PRO_DIALOG = "OPEN_DIVINE_COMPOSE_PRO_DIALOG",
  UPDATE_DIVINE_COMPOSE_PRO_INFO = "UPDATE_DIVINE_COMPOSE_PRO_INFO",
  UPDATE_DIVINE_EQUIP_RESET = "UPDATE_DIVINE_EQUIP_RESET",
  OPEN_DIVINE_EQUIP_HERO_REWARD_DIALOG = "OPEN_DIVINE_EQUIP_HERO_REWARD_DIALOG",
  OPEN_DIVINE_PRO_ADD_TIME_DIALOG = "OPEN_DIVINE_PRO_ADD_TIME_DIALOG",
  UPDATE_DIVINE_COMPOSE_EFF_INFO = "UPDATE_DIVINE_COMPOSE_EFF_INFO",
  OPEN_DIVINE_COMPOSE_DIALOG = "OPEN_DIVINE_COMPOSE_DIALOG",
  OPEN_DIVINE_RANDOM_ATTR_PRVIEW = "OPEN_DIVINE_RANDOM_ATTR_PRVIEW",

  //------  神临装备 end---------------

  //------  神临装备副本 start-----------
  OPEN_DIVINE_COPY_DIALOG = "OPEN_DIVINE_COPY_DIALOG",
  UPDATE_DIVINE_COPY_INFO = "UPDATE_DIVINE_COPY_INFO",
  OPEN_DIVINE_COPY_SHOP_DIALOG = "OPEN_DIVINE_COPY_SHOP_DIALOG",
  OPEN_DIVINE_COPY_WEEK_SHOP_DIALOG = "OPEN_DIVINE_COPY_WEEK_SHOP_DIALOG",
  OPEN_DIVINE_COPY_PREVIEW_DIALOG = "OPEN_DIVINE_COPY_PREVIEW_DIALOG",
  OPEN_DIVINE_COPY_READY_DIALOG = "OPEN_DIVINE_COPY_READY_DIALOG",
  //------  神临装备副本 end-------------

  //家族战 start ----------------------
  OPEN_CSCLAN_WAR_MAINUI_DIALOG = "OPEN_CSCLAN_WAR_MAINUI_DIALOG",
  CLOSE_CSCLAN_WAR_MAINUI_DIALOG = "CLOSE_CSCLAN_WAR_MAINUI_DIALOG",
  OPEN_CSCLAN_WAR_ARMY_INFO_DIALOG = "OPEN_CSCLAN_WAR_ARMY_INFO_DIALOG",
  OPEN_CSCLAN_WAR_ARMY_DEF_LOG_DIALOG = "OPEN_CSCLAN_WAR_ARMY_DEF_LOG_DIALOG",
  OPEN_CSCLAN_WAR_LIST_DIALOG = "OPEN_CSCLAN_WAR_ARMY_LIST_DIALOG",
  OPEN_CSCLAN_WAR_ACHIEVEMENT_DIALOG = "OPEN_CSCLAN_WAR_ACHIEVEMENT_DIALOG",
  CLOSE_CSCLAN_WAR_ACHIEVEMENT_DIALOG = "CLOSE_CSCLAN_WAR_ACHIEVEMENT_DIALOG",
  OPEN_CSCLAN_WAR_LOG_DIALOG = "OPEN_CSCLAN_WAR_LOG_DIALOG",
  OPEN_CSCLAN_WAR_SHOP_DIALOG = "OPEN_CSCLAN_WAR_SHOP_DIALOG",
  OPEN_CSCLAN_WAR_REWARD_DIALOG = "OPEN_CSCLAN_WAR_BOX_REWARD_DIALOG",
  UPDATE_CSCLAN_WAR_MAINUI_INFO = "UPDATE_CSCLAN_WAR_MAINUI_INFO",
  UPDATE_CSCLAN_WAR_GROUP_LIST = "UPDATE_CSCLAN_WAR_GROUP_LIST",
  UPDATE_CSCLAN_WAR_FETCH_LIST = "UPDATE_CSCLAN_WAR_FETCH_LIST",
  UPDATE_CSCLAN_WAR_BOX_LIST = "UPDATE_CSCLAN_WAR_BOX_LIST",
  UPDATE_CSCLAN_WAR_SHOP_INFO = "UPDATE_CSCLAN_WAR_SHOP_INFO",
  //家族战 end ------------------------

  //------  游商 start-----------
  OPEN_ITINERANT_SHOP_DIALOG = "OPEN_ITINERANT_SHOP_DIALOG",
  INFO_ITINERANT_SHOP_INFO = "INFO_ITINERANT_SHOP_INFO",
  //------  游商 end-------------

  //------  国战 start-----------
  COUNTRY_WAR_SELECT_CITY = "COUNTRY_WAR_SELECT_CITY",
  OPEN_COUNTRY_WAR_ENTER_DIALOG = "OPEN_COUNTRY_WAR_ENTER_DIALOG",
  OPEN_COUNTRY_WAR_DIALOG = "OPEN_COUNTRY_WAR_DIALOG",
  CLOSE_COUNTRY_WAR_DIALOG = "CLOSE_COUNTRY_WAR_DIALOG",
  OPEN_COUNTRY_WAR_CITY_DETAIL_INFO_DIALOG = "OPEN_COUNTRY_WAR_CITY_DETAIL_INFO_DIALOG",
  OPEN_COUNTRY_WAR_DETAIL_MAP_DIALOG = "OPEN_COUNTRY_WAR_DETAIL_MAP_DIALOG",
  OPEN_COUNTRY_WAR_REPORT_DIALOG = "OPEN_COUNTRY_WAR_REPORT_DIALOG",
  OPEN_COUNTRY_WAR_REPORT_DETAIL_DIALOG = "OPEN_COUNTRY_WAR_REPORT_DETAIL_DIALOG",
  OPEN_COUNTRY_WAR_TEAM_SELECT_DIALOG = "OPEN_COUNTRY_WAR_TEAM_SELECT_DIALOG",
  OPEN_COUNTRY_WAR_REWARD_RANK_DIALOG = "OPEN_COUNTRY_WAR_REWARD_RANK_DIALOG",
  OPEN_COUNTRY_WAR_TEAM_REVIVE_DIALOG = "OPEN_COUNTRY_WAR_TEAM_REVIVE_DIALOG",
  OPEN_COUNTRY_WAR_ENEMY_MARCH_DIALOG = "OPEN_COUNTRY_WAR_ENEMY_MARCH_DIALOG",
  CLOSE_COUNTRY_WAR_ENEMY_MARCH_DIALOG = "CLOSE_COUNTRY_WAR_ENEMY_MARCH_DIALOG",
  OPEN_COUNTRY_WAR_RANK_DIALOG = "OPEN_COUNTRY_WAR_RANK_DIALOG",
  OPEN_COUNTRY_WAR_DAILY_RANK_DIALOG = "OPEN_COUNTRY_WAR_DAILY_RANK_DIALOG",
  OPEN_COUNTRY_WAR_GENERAL_SELECT_DIALOG = "OPEN_COUNTRY_WAR_GENERAL_SELECT_DIALOG",
  OPEN_COUNTRY_WAR_CAMP_DETAIL_DIALOG = "OPEN_COUNTRY_WAR_CAMP_DETAIL_DIALOG",
  OPEN_COUNTRY_WAR_SIGN_DIALOG = "OPEN_COUNTRY_WAR_SIGN_DIALOG",
  CLOSE_COUNTRY_WAR_SIGN_DIALOG = "CLOSE_COUNTRY_WAR_SIGN_DIALOG",
  OPEN_COUNTRY_WAR_SETTING_DIALOG = "OPEN_COUNTRY_WAR_SETTING_DIALOG",
  OPEN_COUNTRY_WAR_SIGN_NOTICE_DIALOG = "OPEN_COUNTRY_WAR_SIGN_NOTICE_DIALOG",
  OPEN_COUNTRY_WAR_RANDOM_SIGN_SUCC_DIALOG = "OPEN_COUNTRY_WAR_RANDOM_SIGN_SUCC_DIALOG",
  OPEN_COUNTRY_WAR_SCHEDULE_DIALOG = "OPEN_COUNTRY_WAR_SCHEDULE_DIALOG",
  OPEN_COUNTRY_WAR_ALERT_DIALOG = "OPEN_COUNTRY_WAR_ALERT_DIALOG",
  UPDATE_COUNTRY_WAR_CITY_TEAM_LIST = "UPDATE_COUNTRY_WAR_CITY_TEAM_LIST",
  OPEN_COUNTRY_WAR_ACHIEVEME_DIALOG = "OPEN_COUNTRY_WAR_ACHIEVEME_DIALOG",
  OPEN_COUNTRY_WAR_CANCEL_RELATIONSHIP_DIALOG = "OPEN_COUNTRY_WAR_CANCEL_RELATIONSHIP_DIALOG",
  OPEN_COUNTRY_WAR_GENERAL_DIALOG = "OPEN_COUNTRY_WAR_GENERAL_DIALOG",
  OPEN_COUNTRY_WAR_CITY_BUFF_DIALOG = "OPEN_COUNTRY_WAR_CITY_BUFF_DIALOG",
  OPEN_COUNTRY_WAR_SHOP_DIALOG = "OPEN_COUNTRY_WAR_SHOP_DIALOG",
  OPEN_COUNTRY_WAR_HONOR_WALL_DIALOG = "OPEN_COUNTRY_WAR_HONOR_WALL_DIALOG",
  //------  国战副本 end-------------

  //------地图相关通用 start ------//
  UPDATE_WARS_INIT = "UPDATE_WARS_INIT",
  UPDATE_WARS_INIT_PERSONAL = "UPDATE_WARS_INIT_PERSONAL",
  UPDATE_WARS_WALK_LIST = "UPDATE_WARS_WALK_LIST",
  UPDATE_WARS_UPDATE_CITY = "UPDATE_WARS_UPDATE_CITY",
  UPDATE_WARS_UPDATE_CITY_CAMP = "UPDATE_WARS_UPDATE_CITY_CAMP",
  UPDATE_WARS_CITY_INFO = "UPDATE_WARS_CITY_INFO",
  UPDATE_WARS_OPERATE = "UPDATE_WARS_OPERATE",
  UPDATE_WARS_SIGN = "UPDATE_WARS_SIGN",
  UPDATE_WARS_TEAM_OP = "UPDATE_WARS_TEAM_OP",
  UPDATE_WARS_TEAM_INFO = "UPDATE_WARS_TEAM_INFO",
  UPDATE_WARS_TEAM_HERO_INFO = "UPDATE_WARS_TEAM_HERO_INFO",
  UPDATE_WARS_ROLE_PRESONAL_INFO = "UPDATE_WARS_ROLE_PRESONAL_INFO",
  UPDATE_WARS_ACT_INFO = "UPDATE_WARS_ACT_INFO",
  UPDATA_WARS_MAP_LOCATION_POSITION = "WARS_LOCATION_POSITION",
  UPDATE_WARS_SCORE_RANK = "UPDATE_WARS_SCORE_RANK",
  UPDATE_WARS_FIGHT_LOG = "UPDATE_WARS_FIGHT_LOG",
  UPDATE_WARS_CITY_LOG = "UPDATE_WARS_CITY_LOG",
  UPDATE_WARS_CAMP_RANK = "UPDATE_WARS_CAMP_RANK",
  UPDATE_WARS_GENERAL_SELECT = "UPDATE_WARS_GENERAL_SELECT",
  UPDATE_WARS_RECOMMENT_SELECT = "UPDATE_WARS_RECOMMENT_SELECT",
  UPDATE_WARS_VOTE_SELECT = "UPDATE_WARS_VOTE_SELECT",
  UPDATE_WARS_KILL_INFO = "UPDATE_WARS_KILL_INFO",
  UPDATE_WARS_MAP_POSITION = "UPDATE_WARS_MAP_POSITION",
  UPDATE_WARS_CITY_TEAM_LIST = "UPDATE_WARS_CITY_TEAM_LIST",
  UPDATE_WARS_INFO_NTY_RANK_DATA = "UPDATE_WARS_INFO_NTY_RANK_DATA",
  UPDAET_WARS_SELECT_TEAM = "UPDAET_WARS_SELECT_TEAM",
  UPDATE_WARS_SIGN_STATUS = "UPDATE_WARS_SIGN_STATUS",
  UPDATE_WARS_EVENT = "UPDATE_WARS_EVENT",
  UPDATE_WARS_TEAM_SCORE = "UPDATE_WARS_TEAM_SCORE",
  UPDATE_WARS_DISALLIANCE = "UPDATE_WARS_DISALLIANCE",
  UPDATE_WARS_AUTO_DISALLIANCE = "UPDATE_WARS_AUTO_DISALLIANCE",
  UPDATE_WARS_INIT_CAMP = "UPDATE_WARS_INIT_CAMP",
  GM_UPDATE_WARS_CITY_ITEM = "GM_UPDATE_WARS_CITY_ITEM",
  UPDATE_WARS_ROLE = "UPDATE_WARS_ROLE",
  UPDATE_WARS_HONOR_WALL = "UPDATE_WARS_HONOR_WALL",
  //------地图相关通用 end ------//
}

// export let ModuleCommand:typeof ModuleCommand1 = ModuleCommand1;
