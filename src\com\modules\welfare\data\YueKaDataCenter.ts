import { ConfigManager } from "../../../managers/ConfigManager";
import { p_fuli_yueka } from "../../../proto/common/p_fuli_yueka";
import { DataCenter } from "../../DataCenter";
import { PanelEventConstants } from "../../PanelEventConstants";
import { PaymentDataCenter } from "../../payment/data/PaymentDataCenter";
import { PaymentItemVO } from "../../payment/vo/PaymentItemVO";
import { PaymentVO } from "../../payment/vo/PaymentVO";
import { RedPointMgr } from "../../redPoint/RedPointMgr";
import { WelfareDataCenter } from "./WelfareDataCenter";
import {EYueKaType} from "../../../auto/ConstAuto";

// export enum EYueKaType {
//     TYPE_1 = 1,	//畅游月卡
//     TYPE_2 = 2,	//豪华月卡
//     TYPE_3 = 3,	//尊贵月卡
//     TYPE_4 = 4,	//王者月卡
//     TYPE_5 = 101, //永久招募卡
// }
/**
 * 月卡dataCenter
 */
export class YueKaDataCenter {
    private static _instance:YueKaDataCenter;

    static get instance(){
        if(!YueKaDataCenter._instance){
            YueKaDataCenter._instance = new YueKaDataCenter();
        }
        return YueKaDataCenter._instance;
    }

    /**月卡详情 */
    public yueka_info: p_fuli_yueka[] = [];
    /**月卡领取状态 */
    public fetch_list: number[] = [];

    /**获取月卡信息 */
    public getFuliYuekaInfo(type: EYueKaType): p_fuli_yueka {
        if (this.yueka_info.length <= 0)
            return null;
        for (let i = 0; i < this.yueka_info.length; i++) {
            if (this.yueka_info[i].type == type)
                return this.yueka_info[i];
        }
    }

    public getMaxYuekaType(): number {
        if (this.yueka_info.length <= 0)
            return 0;

        this.yueka_info.sort((t1, t2) => {
            return t2.type - t1.type;
        });
        for (let info of this.yueka_info) {
            if (info.end_time - DataCenter.serverTimeSeconds > 0) {
                return info.type;
            }
        }
        return 0;
    }

    /**0不可以领取 1可领取 2已领取 */
    public getFuliYuekaState(type: EYueKaType): number {
        if (this.yueka_info.length <= 0)
            return 0;

        let state = 0;
        for (let info of this.yueka_info) {
            if (info.type == type){
                if(info.end_time - DataCenter.serverTimeSeconds > 0){
                    state = 1;
                }
                break;
            }
        }

        if (this.fetch_list.length > 0){
            for (let i = 0; i < this.fetch_list.length; i++) {
                if (this.fetch_list[i] == type){
                    state = 2;
                    break;
                }
            }
        }
        return state;
    }

    /**0不可以领取 1可领取 2已领取 */
    public getPremanentCardState(type: EYueKaType){
        let yuekaInfo = this.yueka_info.filter(info => info.type == type)[0];
        if(!yuekaInfo){
            return 0;
        }
        let paymentItemVo = PaymentDataCenter.instance.getPaymentVoByID(yuekaInfo.shop_id).list.filter(
            payMentItem => payMentItem.item_id == yuekaInfo.goods_id)[0];
        if(yuekaInfo.price < paymentItemVo.price){
            return 0;
        } else {
            if (YueKaDataCenter.instance.fetch_list.length > 0){
                for (let i = 0; i < YueKaDataCenter.instance.fetch_list.length; i++) {
                    if (YueKaDataCenter.instance.fetch_list[i] == yuekaInfo.type){
                        return 2;
                    }
                }
            }
        }
        return 1;
    }
    
    //获取月卡的名称
    public getYuekaName(yueKaType: number) {
        let cfgYueka = ConfigManager.cfg_fuli_yuekaCache.get(yueKaType);
        if (cfgYueka) {
            return cfgYueka.yueka_name;

        } else {
            return "";
        }
    }

    /**
     * 是否开通月卡
     * @param type 
     * @returns true = 已开通    false = 未开通
     */
    public isOpenFuliYueKa(type: EYueKaType): boolean {
        let state = this.getFuliYuekaState(type);
        return state != 0;
    }

    /**
     * 是否开通某月卡及其以上
     * @param type 
     * @returns true = 已开通    false = 未开通
     */
    public isOpenFuliYueKaAndAbove(type: EYueKaType): boolean {
        let state = 0;
        for (let info of this.yueka_info) {
            if (info.type >= type) {
                state += this.getFuliYuekaState(info.type);
            }
        }

        return state != 0;
    }

    /**月卡红点 */
    public checkCardRedPoint(): void {
        let count: number = 0;
        if (this.yueka_info.length <= 0) {
            RedPointMgr.ins.SetRedData(PanelEventConstants.PAYMENT, PaymentVO.KEY_WELFARE_CARD, count);
            return;
        }
        for (let info of this.yueka_info) {
            if (info.type == EYueKaType.TYPE_101_FOREVER) {
                if(this.getPremanentCardState(info.type) == 1) {
                    count++;
                }
            } else {
                if (YueKaDataCenter.instance.getFuliYuekaState(info.type) == 1){
                    count++;
                }
            } 
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.PAYMENT, PaymentVO.KEY_WELFARE_CARD, count);
    }

    /**获取增加快速挂机次数的月卡列表 */
    public getQuickFightYueKaList():number[] {
        if(this["_quickFightYueKaList"]){
            return this["_quickFightYueKaList"];
        }
        let temp:number[] = [];
        ConfigManager.cfg_fuli_yuekaCache.forEach((cfg) => {
            if(cfg.quick_hanging_times > 0 || cfg.quick_free_times > 0){
                temp.push(cfg.yueka_type);
            }
        });
        this["_quickFightYueKaList"] = temp;
        return temp;
    }

    /**是否所有的快速挂机月卡都开启了 */
    public isOpenAllQuickFightYueKa():boolean {
        let typeList = this.getQuickFightYueKaList();
        for(let yueka_type of typeList){
            if(!this.isOpenFuliYueKa(yueka_type)){
                return false;
            }
        }
        return true;
    }

    /**获取当前已激活的快速挂机次数 */
    public getGuajiQuickFightTime():number {
        let typeList = this.getQuickFightYueKaList();
        let times = 0;
        for(let yueka_type of typeList){
            if(this.isOpenFuliYueKa(yueka_type)){
                let yuekaCfg = ConfigManager.cfg_fuli_yuekaCache.get(yueka_type);
                if(yuekaCfg != undefined){
                    times += yuekaCfg.quick_hanging_times;
                    times += yuekaCfg.quick_free_times;
                }
            }
        }
        return times;
    }
}