import { com } from "../../../../ui/layaMaxUI";
import { ModuleCommand } from "../../../ModuleCommand";
import { p_hero } from "../../../../proto/common/p_hero";
import { TipsUtil } from "../../../../util/TipsUtil";
import { cfg_hero_base } from "../../../../cfg/vo/cfg_hero_base";
import { DudufuDataCenter } from "../../../dudufu/data/DudufuDataCenter";
import { GoodsVO } from "../../../goods/GoodsVO";
import { ItemConst } from "../../../goods/ItemConst";
import HeroEquipVo from "../../../equip/data/HeroEquipVo";
import HeroEquipManager from "../../../equip/data/HeroEquipManager";
import { Tween } from "laya/utils/Tween";
import { Ease } from "laya/utils/Ease";
import HeroEquipItem from "./HeroEquipItem";
import { UITabData } from "../../../baseModules/UITabData";
import WingHeroEquipItem from "../../../wing/view/WingHeroEquipItem";
import DeputyHeroEquipItem from "../../../deputy/view/DeputyHeroEquipItem";
import { CfgCacheMapMgr } from "../../../../cfg/CfgCacheMapMgr";
import { HeroConsts, HeroMaterialData, HeroNation, SoulHeroLinkType } from "../../data/HeroConsts";
import { HeroDataCenter } from "../../data/HeroDataCenter";
import { ConfigManager } from "../../../../managers/ConfigManager";
import { ItemMacro } from "../../../../auto/ItemMacro";
import { ConsoleUtils } from "../../../../util/ConsoleUtils";
import { cfg_divine } from "../../../../cfg/vo/cfg_divine";
import { DeputyDataCenter } from "../../../deputy/data/DeputyDataCenter";
import { WingDataCenter } from "../../../wing/data/WingDataCenter";
import { DataCenter } from "../../../DataCenter";
import SkillItem from "../../../skill/view/SkillItem";
import { SkillDataCenter } from "../../../skill/data/SkillDataCenter";
import { SkillItemVo } from "../../../skill/vo/SkillItemVo";
import { LangConst } from "../../../../auto/LangConst";
import { fightAttr } from "../../../../cfg/vo/fightAttr";
import { FightAttrMgr } from "../../../role/data/FightAttrMgr";
import { HeroUtil } from "../../util/HeroUtil";
import { FightAttrVO } from "../../../role/vo/FightAttrVO";
import { UrlConfig } from "../../../../../game/UrlConfig";
import { GameConst } from "../../../GameConst";
import { UIList } from "../../../baseModules/UIList";
import HeroStageItem from "../stageUpdate/HeroStageItem";
import { HeroStarStageItem } from "../starUpdate/HeroStarStageItem";
import CommonCostItem1 from "../../../common/CommonCostItem1";
import { MiscConst } from "../../../misc_config/MiscConst";
import VipTeQuanUtil from "../../../payment/VipTeQuanUtil";
import { cfg_hero_level } from "../../../../cfg/vo/cfg_hero_level";
import { cfg_hero_star_stage } from "../../../../cfg/vo/cfg_hero_star_stage";
import { Event } from "laya/events/Event";
import { GoodsManager } from "../../../test_bag/GoodsManager";
import { ESkeletonType } from "../../../baseModules/skeleton/SkeletonData";
import { GSkeleton } from "../../../baseModules/skeleton/GSkeleton";
import HeroHeadItem from "../HeroHeadItem";
import { HeroSkinDataCenter } from "../../../heroSkin/data/HeroSkinDataCenter";
import { MiscConstAuto } from "../../../../auto/MiscConstAuto";
import { DivineDataCenter } from "../../../Divine/data/DivineDataCenter";
import { SkeletonManager } from "../../../baseModules/skeleton/SkeletonManager";
import { HeroEvolveDataCenter } from "../../data/HeroEvolveDataCenter";
import { Sprite } from "laya/display/Sprite";
import { cfg_hero_skin } from "../../../../cfg/vo/cfg_hero_skin";
import HeroInfoSumItem2 from "./HeroInfoSumItem2";
import { TweenUtil } from "../../../../util/TweenUtil";
import { UIUtil } from "../../../../util/UIUtil";

export default class HeroInfoSumView2 extends com.ui.res.heroInfo.HeroInfoSumView2UI {
    private _hero_info: p_hero;
    //英雄基础配置
    protected _cfg: cfg_hero_base;
    private _equipList: HeroEquipItem[];
    private wingItem: WingHeroEquipItem;
    private deputyItem: DeputyHeroEquipItem;
    protected _skillItemList: SkillItem[];
    private _btnList:Sprite[];
    protected _attrList: UIList;
    private _stageList: UIList;
    private _starStageList: UIList;
    private _soulLvList: UIList;
    private _expExpend: CommonCostItem1;
    private _silverExpend: CommonCostItem1;
    public isTujian: boolean = false;
    private isFiveHeroResonate: boolean = false;
    private isDudu: boolean = false;

    private _isHoldOn: boolean = false;
    private _isUpdated: boolean = false;
    private _updateTick: number = 0;

    private linkEff:GSkeleton;
    private _linkHeroItem1: HeroHeadItem;
    private _linkHeroItem2: HeroHeadItem;
    public isShowWS9ZhouyinBox:boolean = false;
    private soulHeroItem: HeroHeadItem;

    private _divineBtnEff: GSkeleton;
    private _evolveSkillItem: SkillItem;

    public initUI(): void {
        this.wingItem = new WingHeroEquipItem();
        this.equipItem7.addChild(this.wingItem);

        this.deputyItem = new DeputyHeroEquipItem();
        this.equipItem8.addChild(this.deputyItem);

        this._equipList = [
            this.equipItem1,
            this.equipItem2,
            this.equipItem3,
            this.equipItem4,
            this.equipItem5,
            this.equipItem6,
        ];

        this._skillItemList = [
            this.skillItem1,
            this.skillItem2,
            this.skillItem3,
            this.skillItem4,
        ];
        this._btnList = [
            this.btnSkin,
            this.btnGet,
            this.btnUnlock,
            this.btnPowerPre,
            this.btnCastSoul,
            this.btnReset,
            this.btnSameheart,
            this.btnSoulReset,
            this.fiveHeroResonateCheck
        ]
        this._attrList = UIList.SetUIList(this, this.valueBox, HeroInfoSumItem2);
        this._attrList.SetRepeat(1, 4);
        this._attrList.SetSpace(1, 10);
        
        this._stageList = UIList.SetUIList(this, this.stageBox, HeroStageItem);
        this._stageList.SetRepeat(10, 1);
        this._stageList.SetSpace(5, 0);
        this._stageList.scrollBarHide = true;
        
        this._starStageList = UIList.SetUIList(this, this.starStageBox, HeroStarStageItem);
        this._starStageList.SetRepeat(10, 1);
        this._starStageList.SetSpace(5, 0);
        this._starStageList.scrollBarHide = true;

        this._soulLvList = UIList.SetUIList(this, this.soulLvBox, HeroStageItem);
        this._soulLvList.SetRepeat(10, 1);
        this._soulLvList.SetSpace(5, 0);
        this._soulLvList.scrollBarHide = true;
        this._soulLvList.AddOtherParamete("iconSkin", "heroInfo/img_soul_lv.png");
        this._soulLvList.AddOtherParamete("iconBgSkin", "");

        this._silverExpend = this.silverExpend;
        this._silverExpend.setTxtStyle();
        this._expExpend = this.expExpend;
        this._expExpend.setTxtStyle();

        this._linkHeroItem1 = new HeroHeadItem();
        this.linkHero1.addChild(this._linkHeroItem1);
        this._linkHeroItem2 = new HeroHeadItem();
        this.linkHero2.addChild(this._linkHeroItem2);

        this.btnHelp.text.visible = false;
        this.timerLoop(33, this, this.loop);
    }
    layoutButtons(): void {
        // // 清除之前布局
        // this.btnBox.removeChildren();

        // const spacing = 76; // 按钮之间的间距
        // const rowHeight = 80; // 行高（包括按钮高度和上下留白）
        // let maxRow = 4;
        // let visibleButtons = this._btnList.filter(btn => btn.visible); // 只处理可见按钮
        // if (visibleButtons.length > 8) {
        //     maxRow = 5;
        // }
        // let y = 0;
        // let rowIndex = 0;
        // while (rowIndex * maxRow < visibleButtons.length) {
        //     let rowButtons = visibleButtons.slice(rowIndex * maxRow, (rowIndex + 1) * maxRow);
        //     let x = this.btnBox.x; // 从右向左排布
        //     for (let i = 0; i < rowButtons.length; i++) {
        //         let btn = rowButtons[i];
        //         btn.x = x;
        //         x -= spacing;
        //         btn.y = y;
        //         this.btnBox.addChild(btn); // 直接添加到当前容器
        //     }
        //     y += rowHeight;
        //     rowIndex++;
        // }
        let visibleButtons = this._btnList.filter(btn => btn.visible); // 只处理可见按钮
        const maxRowPerLine = visibleButtons.length > 8 ? 5 : 4;
        UIUtil.layoutItems(this.btnBox,this._btnList,{rtl:true,maxRowPerLine:maxRowPerLine});
    }
    public addClick(): void {
        this.addOnClick(this,this.btnPowerPre,this.onClickPowerPreview);
        this.addOnClick(this, this.btnGet, this.onClickGet);
        this.addOnClick(this, this.btnSkin, this.onSkinShow);
        this.addOnClick(this, this.btnCastSoul, this.onCastSoulBtnClick);
        this.addOnClick(this, this.btnSoulReset, this.onSoulBtnReset);
        this.addOnClick(this, this.btnReset, this.onBtnReset);
        this.addOnClick(this, this.btnLook, this.onClickBtnLook);
        this.addOnClick(this, this.btnGoLink, this.onClickBtnGoLink);
        this.addOnClick(this, this.onekeyLoadBtn, this.OnClcikOneKeyLoad);
        this.addOnClick(this, this.oneKeyUnLoadBtn, this.OnClcikOneKeyUnLoad);
        this.addOnClick(this, this.btnUnlock, this.onBtnLock);
        this.addOnClick(this, this.btnInfo, this.onClickAttrTip);
        this.addOnClick(this, this.fiveHeroResonateCheck, this.onClickFiveHeroResonateSetting);
        this.addOnClick(this, this.btnSameheart, this.onClickSameHeart);
        this.addOnClick(this, this.btnDivine, this.onClickBtnDivine);
        this.addOnClick(this, this.boxEvolveSkill, this.onClickBtnEvolveSkill);
        this.addOnClick(this, this.linkStateBox, this.onClickBoxLinkState);
        this.addOnClick(this, this.soulHeroBox, this.onClickSoulHeroBox);
        this.addOnClick(this, this.limithero,this.onClickLimitHero);
        this.updateBtn.on(Event.MOUSE_DOWN, this, this.onMouseDownBtnUpdate);
        this.updateBtn.on(Event.MOUSE_UP, this, this.onMouseUpBtnUpdate);
        this.updateBtn.on(Event.MOUSE_OUT, this, this.onMouseUpBtnUpdate);
    }
    private loop(): void {
        if (this._isHoldOn && this._updateTick > 0) {
            this._updateTick -= 33;
            if (this._updateTick <= 0) {
                this._upgradeHero();
            }
        }
    }
    private onMouseDownBtnUpdate(e: Event): void {
        if (DataCenter.myLevel >= 10) {
            this._isHoldOn = true;
            this._updateTick = 0;
        }
        this._upgradeHero();
    }

    private onMouseUpBtnUpdate(e: Event): void {
        this._isHoldOn = false;
    }
    public addEvent(): void {
        this.addEventListener(ModuleCommand.UPDATE_HERO_SHOW_WING_STATE, this, this.onUpdateWingEquipShow);
        this.addEventListener(ModuleCommand.UPDATE_HERO_SHOW_DEPUTY_STATE, this, this.onRefreshEquipInfo);
        this.addEventListener(ModuleCommand.UPDATE_EQUIP_STATE, this, this.onRefreshEquipInfo);
        this.addEventListener(ModuleCommand.UPDATE_HERO_SKILL, this, this.updateSkillInfo);
        this.addEventListener(ModuleCommand.LEVEL_UP, this, this.updateLevelInfo);
        this.addEventListener(ModuleCommand.ON_HERO_UPGRADE, this, this.onHeroUpgrade);
        this.addEventListener(ModuleCommand.UPDATE_EVOLVE_SKILL_INFO, this, this.UpdateBtnState);
        this.addEventListener(ModuleCommand.UPDATE_HERO_LOCK, this, this.refreshHeroLockState);
        /**红点相关 */
        this.addEventListener(ModuleCommand.UPDATE_PAYMENT_SHOP_INFO, this, this.refreshSkinbtnRedPoint);
        this.addEventListener(ModuleCommand.UPDATE_SKIN_RED_POINT, this, this.refreshHeroRedPoint);
        this.addEventListener(ModuleCommand.UPDATE_WING_RED_POINT, this, this.refreshHeroRedPoint);
    }
    private onHeroUpgrade(upType: number): void {
        if (upType == 1) {
            this._isUpdated = false;
        }
    }
    onBtnLock(){
        if (!this._hero_info) {
            return;
        }
        this.dispatchEvent(ModuleCommand.OPEN_HERO_LOCK_DIALOG, {
            heroInfo: this._hero_info,
        });
    }
    onClickLimitHero(){
        this.dispatchEvent(ModuleCommand.OPEN_HELP_DIALOG, "1008");
    }
    private onClickBoxLinkState(): void {
        if (!this._hero_info) {
            return;
        }
        let beLinkHero = HeroDataCenter.instance.getBeLinkHero(this._hero_info.hero_id);
        if (beLinkHero) {
            this.dispatchEvent(ModuleCommand.OPEN_SOUL_HERO_LINK_CONFIRM_DIALOG, {
                linkType: SoulHeroLinkType.UNLINK,
                linkHero: beLinkHero,
                soulHero: this._hero_info,
            });
        }
    }
    private onClickSoulHeroBox(): void {
        let linkSoulHero = HeroDataCenter.instance.getLinkSoulHero(this._hero_info.hero_id);
        if (linkSoulHero) {
            this.dispatchEvent(ModuleCommand.OPEN_SOUL_HERO_LINK_CONFIRM_DIALOG, {
                linkType: SoulHeroLinkType.UNLINK,
                linkHero: this._hero_info,
                soulHero: linkSoulHero,
            });
        }
    }
    onClickBtnEvolveSkill(){
        if (!this._hero_info) {
            return
        }
        this.dispatchEvent(ModuleCommand.OPEN_EVOLVE_SKILL_INFO_DIALOG, {heroInfo: this._hero_info});
    }
    onClickAttrTip(){
        if (this._hero_info) {
            this.dispatchEvent(ModuleCommand.OPEN_HERO_ATTR_DIALOG, { heroInfo: this._hero_info, isMine: true });
        }
    }
    private onClickBtnDivine(): void {
        if (DivineDataCenter.instance.IsActivateHero(this._hero_info.type_id)) {
            TipsUtil.showDescDialog("该英雄已激活【神临】装备权限，获得后可直接穿戴。", {
                fontSize: 24,
                color: "#ed6e3c",
                width: 500,
                height: 200,
                bgSkin: "common2/panel_bg2.png",
            });
        } else {
            this.dispatchEvent(ModuleCommand.OPEN_DIVINE_HERO_ACTIVE_DIALOG, this._hero_info);
        }
    }
    /* 点击同心触发事件 */
    private onClickSameHeart() {
        if (!this._hero_info) {
            TipsUtil.showDebugTips(`无效的英雄数据 HeroInfoDialog.onClickSameHeart`, true, "Errors");
            return;
        }
        let heroCfg = ConfigManager.cfg_hero_baseCache.get(this._hero_info.type_id);
        let isSoulHero = heroCfg ? heroCfg.nation == HeroNation.NATION_HUN : false;
        if (isSoulHero && !HeroUtil.checkPopUnlockTongXinDialog(this._hero_info)) {
            let soulHeroName = heroCfg ? heroCfg.name : "";//同调异能英雄名称
            TipsUtil.showTips(window.iLang.L2_ch23_P0_ch24_YI_JIE_SUO_TONG_XIN.il([soulHeroName]));
        }
    }
    // 更新同心图标的状态
    private updateSameHeartState() {
        if (!this._hero_info) {
            TipsUtil.showDebugTips(`无效的英雄数据 HeroInfoDialog.updateSameHeartState`, true, "Errors");
            return;
        }

        let cfg = ConfigManager.cfg_hero_baseCache.get(this._hero_info.type_id);
        let isSoulHero = cfg ? cfg.nation == HeroNation.NATION_HUN : false;
        this.btnSameheart.visible = isSoulHero;
        this.btnSameheart.gray = isSoulHero && !HeroDataCenter.instance.checkIsUnLockTongXinLimit(this._hero_info.type_id);
    }
    private onClickFiveHeroResonateSetting(): void {
        if (!this._hero_info) {
            return;
        }
        this.btnfiveHeroResonate.skin = this.fiveHeroResonateCheck.selected ? "v2_heroInfo/hztx_open.png" : "v2_heroInfo/hztx_close.png";
        if (DudufuDataCenter.instance.checkHeroIsFiveHeroResonate(this._hero_info.hero_id) || DudufuDataCenter.instance.checkHeroIsDudu(this._hero_info.hero_id)) {
            let op_type = this.fiveHeroResonateCheck.selected ? 1 : 2;
            DudufuDataCenter.instance.m_hero_resonate_equip_op_tos(op_type, this._hero_info.hero_id);
        }
    }
    /**一键穿戴 */
    OnClcikOneKeyLoad(): void {
        //可以一键穿戴
        if (HeroDataCenter.instance.CheckIsCanOneKey(this._hero_info)) {
            HeroDataCenter.instance.m_equip_auto_load_tos(this._hero_info.hero_id);
        } else {
            TipsUtil.showTips(window.iLang.L2_ZAN_WU_KE_CHUAN_DAI_DE_ZHUANG_BEI.il());
        }
    }

    /**一件卸下 */
    OnClcikOneKeyUnLoad(): void {
        if (DudufuDataCenter.instance.checkHeroIsUseFiveHeroResonateEquip(this._hero_info.hero_id)) {
            TipsUtil.showTips(window.iLang.L2_GONG_MING_HONG_ZHUANG_WU_FA_JIN_XING_GAI_CAO_ZUO_ch26.il());
            return;
        }

        if (DudufuDataCenter.instance.is_unlock_equip && DudufuDataCenter.instance.checkHeroIsFiveHero(this._hero_info.hero_id)) {
            let content = window.iLang.L2_GAI_CAO_ZUO_HUI_DAO_ZHI_ch23_HONG_ZHUANG_GONG_MING_ch24_SHI_XIAO_ch31_GONG_MING_HERO.il();
            TipsUtil.showDialog(this, content, window.iLang.L2_TI_ch11_SHI.il(), () => {
                HeroDataCenter.instance.m_equip_auto_unload_tos(this._hero_info.hero_id);
            }, {todayTipKey: "HERO_RESONATE_EQUIP"});
        } else {
            HeroDataCenter.instance.m_equip_auto_unload_tos(this._hero_info.hero_id);
        }
    }
    private onClickBtnGoLink(): void {
        let material_data = HeroMaterialData.create(0, 0, {
            num: 1,
            hero_id: this._hero_info.hero_id,
            min_star: 10,
            excludeNations: [HeroNation.NATION_HUN],
        });
        this.dispatchEvent(ModuleCommand.OPEN_SOUL_HERO_LINK_SELECT_DIALOG, { material_data: material_data });
    }
    private onClickBtnLook(): void {
        let belinkHero = HeroDataCenter.instance.getBeLinkHero(this._hero_info.hero_id);
        if (belinkHero) {
            this.dispatchEvent(ModuleCommand.UPDATE_HERO_INFO, belinkHero);
        }
    }
    private onClickGet(){
        if (this._cfg) {
            TipsUtil.ShowPathTypeIdToTips(this._cfg.hero_complete_id);
        }
    }
    private onSkinShow(){
        this.dispatchEvent(ModuleCommand.OPEN_HERO_SKIN_DIALOG, {heroInfo: this._hero_info});
    }
    private onClickPowerPreview(){
        this.dispatchEvent(ModuleCommand.OPEN_HERO_ACTUAL_PREVIEW_DIALOG, {"heroinfo": this._hero_info});
    }
    private onCastSoulBtnClick(): void {
        let equipVo: HeroEquipVo = HeroEquipManager.instance.getHeroEquipVo(this._hero_info.hero_id);
        let kind: number;
        let equipKindVo: GoodsVO;
        //已穿戴的装备数
        for (let i: number = 0; i < ItemConst.equinKinds.length; i++) {
            kind = ItemConst.equinKinds[i];
            if (equipVo) {
                equipKindVo = equipVo.getEquipByKind(kind);
                if (equipKindVo != undefined) {
                    break;
                }
            } else {
                break;
            }
        }

        if (DudufuDataCenter.instance.is_unlock_equip && DudufuDataCenter.instance.checkHeroIsUseFiveHeroResonateEquip(this._hero_info.hero_id)) {
            let duduEquipType = DudufuDataCenter.instance.equip_type_list;
            for (let i = 0; i < duduEquipType.length; i++) {
                let nowequipVo: GoodsVO = GoodsVO.GetVoByTypeId(duduEquipType[i]);
                if (nowequipVo) {
                    equipKindVo = nowequipVo;
                    break;
                }
            }

        }

        if (equipKindVo) {
            this.dispatchEvent(ModuleCommand.OPEN_CAST_SOUL_DIALOG, {
                hero_info: this._hero_info,
                kind: equipKindVo.kind
            });
        } else {
            TipsUtil.showTips(window.iLang.L2_QING_CHUAN_DAI_ZHI_SHAO_1_JIAN_ZHUANG_BEI_YI_KAI_QI_ZHU_HUN.il());
        }
    }
    private onBtnReset(): void {
        if (!this._hero_info) {
            return ;
        }
        if(this.isFiveHeroResonate || this.isDudu){
            TipsUtil.showTips(window.iLang.L2_GONG_MING_HERO_BU_KE_ZHONG_ZHI.il());
            return;
        }
        if (HeroDataCenter.instance.checkIsBeLinkHero(this._hero_info.hero_id)) {
            TipsUtil.showTips(window.iLang.L2_GAI_HERO_YI_TONG_DIAO_ch31_WU_FA_ZHONG_ZHI.il());
            return;
        }
        if (HeroDataCenter.instance.isCanReset(true)) {
            this.dispatchEvent(ModuleCommand.OPEN_HERO_RESET_PREVIEW_DISLOG, this._hero_info);
        }
    }
    private onSoulBtnReset() {
        if (this._cfg && this._cfg.nation == HeroNation.NATION_HUN && this._hero_info && this._hero_info.soul_level > 1) {
            this.dispatchEvent(ModuleCommand.OPEN_SOUL_HERO_RESET_DIALOG, {
                soulHero: this._hero_info,
            });
        }
    }
    public onOpen(param:UITabData): void {
        if (!param) {
            return;
        }
        this._hero_info = param.data.hero;
        this.adaptionView(this.parent,this);
        this.moveTween();
        this.onRefreshEquipInfo();
        this.updateSkillInfo();
        this.refRoleHeroInfoChange();
    }
    private _upgradeHero(): void {
        if (this._isUpdated) {
            return ;
        }
        
        if(this.isFiveHeroResonate || this.isDudu){
            TipsUtil.showTips(window.iLang.L2_GONG_MING_HERO_BU_KE_SHENG_JI.il());
            return;
        }
        
        let curLevel = this._hero_info["ori_level"] || this._hero_info.level;

        //当前星级允许的最大阶级
        let maxStage: number = ConfigManager.getHeroMaxUpgradeStage(this._hero_info.star);
        let starMaxLevel: number = ConfigManager.GetHeroMaxUpgradeLevel(this._hero_info.star, maxStage, this._hero_info.star_stage);
        //当前星级、星阶和阶级允许的最大等级
        let stageMaxLevel: number = ConfigManager.GetHeroMaxUpgradeLevel(this._hero_info.star, this._hero_info.stage, this._hero_info.star_stage);

        //可升级的次数
        let upLevelStepLimit = MiscConst.hero_up_level_step_limit;//X级前可以快速升级
        let upLevelStep = MiscConst.hero_up_level_step;//升级次数
        let curUpLvMax: number = 0;
        if(curLevel < upLevelStepLimit) {
            curUpLvMax = Math.min(stageMaxLevel - curLevel, upLevelStepLimit);
        } else {
            curUpLvMax = Math.min(stageMaxLevel - curLevel, upLevelStep);
        }
        let canUpLv = 0;
        
        //消耗的金币和经验数
        let cost_id_1: number = 0, cost_num_1: number = 0;
        let cost_id_2: number = 0, cost_num_2: number = 0;
        for (let i: number = 0; i < curUpLvMax; i++) {
            let level_cfg: cfg_hero_level = ConfigManager.cfg_hero_levelCache.get(curLevel + i);
            //计算消耗是否满足
            cost_id_1 = level_cfg.cost_1;
            cost_id_2 = level_cfg.cost_2;

            if(cost_num_1 + level_cfg.cost_num_1 <= GoodsManager.instance.GetGoodsNumByTypeId(cost_id_1) 
            && cost_num_2 + level_cfg.cost_num_2 <= GoodsManager.instance.GetGoodsNumByTypeId(cost_id_2)) {
                canUpLv++;
                cost_num_1 += level_cfg.cost_num_1;
                cost_num_2 += level_cfg.cost_num_2;
            }
            else {
                break;
            }
        }
        if (curLevel < starMaxLevel && curLevel >= stageMaxLevel) {
            this.timerOnce(100,this,()=>{
                this.dispatchEvent(ModuleCommand.OPEN_HERO_STAGE_UPDATE_DIALOG);
            });

        }
        else {
            if (this._hero_info.hero_id) {
                if (canUpLv > 0) {
                    this._updateTick = MiscConst.hero_upgrade_lv_interval;
                    this._isUpdated = true;
                }
                HeroDataCenter.instance.m_hero_upgrade_tos(this._hero_info.hero_id, HeroDataCenter.LEVEL_UPDATE, Math.max(1, canUpLv));
            }
        }

    }
    public UpdateInfo(isUpdate: boolean = false): void {
        if (HeroDataCenter.instance.select_type_id != 0) {
            this.btnInfo.visible = false;
            this._cfg = ConfigManager.cfg_hero_baseCache.get(HeroDataCenter.instance.select_type_id);
        }
        else {
            this.btnInfo.visible = true;
            let selectHeroId: number = HeroDataCenter.instance.select_hero_id;
            this._hero_info = HeroDataCenter.instance.getHero(selectHeroId);
            if (this._hero_info == null) {//出错，获取英雄信息失败了。
                TipsUtil.showTips(window.iLang.L2_HUO_QU_HERO_SHU_JU_CHU_CUO.il());
                this.dispatchEvent(ModuleCommand.CLOSE_HERO_IFNO_DIALOG);
                this.dispatchEvent(ModuleCommand.CLOSE_HERO_SINGLE_IFNO_DIALOG);
                return;
            }
            this._cfg = ConfigManager.cfg_hero_baseCache.get(this._hero_info.type_id);
        }
        this.isFiveHeroResonate = DudufuDataCenter.instance.checkHeroIsFiveHeroResonate(this._hero_info.hero_id);
        this.isDudu = DudufuDataCenter.instance.checkHeroIsDudu(this._hero_info.hero_id);
        this.setData(this._hero_info, this._cfg);
    }
    UpdateBtnState(): void {
        if (!this._hero_info) return;
        let heroInfo = this._hero_info;
        let heroCfg = ConfigManager.cfg_hero_baseCache.get(heroInfo.type_id);
        let isSoulHero = heroCfg && heroCfg.nation == HeroNation.NATION_HUN;
        this.btnSkin.visible = HeroSkinDataCenter.instance.hasSkin(heroInfo.type_id);

        //铸魂
        let showCastSoul = HeroDataCenter.instance.getMaxStar(heroInfo.type_id) >= MiscConstAuto.hero_star_open_casting_soul;//最大星级
        let showActual = HeroDataCenter.instance.getMaxStar(heroInfo.type_id) >= MiscConstAuto.shili_preview_open_star;
        this.btnPowerPre.visible = showActual;
        this.btnCastSoul.visible = !isSoulHero && showCastSoul;
        this.btnSoulReset.visible = isSoulHero && heroInfo.soul_level > 1;
        this.btnReset.visible = !isSoulHero && heroInfo.hero_id > 0 && !HeroDataCenter.instance.checkHeroInitialState(heroInfo.hero_id);
        this.fiveHeroResonateCheck.visible = DudufuDataCenter.instance.is_unlock_equip
            && (DudufuDataCenter.instance.checkHeroIsFiveHeroResonate(heroInfo.hero_id)
                || DudufuDataCenter.instance.checkHeroIsDudu(heroInfo.hero_id));
        this.fiveHeroResonateCheck.selected = DudufuDataCenter.instance.checkHeroIsUseFiveHeroResonateEquip(heroInfo.hero_id);
        this.btnfiveHeroResonate.skin = this.fiveHeroResonateCheck.selected ? "v2_heroInfo/btn_hztx_open.png" : "v2_heroInfo/btn_hztx_close.png";
        this.imgDudu.skin = "";
        if (DudufuDataCenter.instance.checkHeroIsDudu(heroInfo.hero_id)) {
            let dudu_cfg = ConfigManager.cfg_hero_resonate_dudu_levelCache.get(heroInfo.star);
            if (dudu_cfg) {
                this.imgDudu.skin = "dudufu/dudu_" + dudu_cfg.title_res_id + ".png";
            } else {
                this.imgDudu.skin = "";
            }
        }
        if (DudufuDataCenter.instance.checkHeroIsUseFiveHeroResonateEquip(heroInfo.hero_id)) {
            this.SetRedPoint(this.onekeyLoadBtn, false, 160, -10);
            this.onekeyLoadBtn.visible = false;
            this.oneKeyUnLoadBtn.visible = true;
            this.oneKeyUnLoadBtn.gray = true;
        }
        this.limithero.visible = heroInfo.limit_type > 0;
        this.updateSameHeartState();
        this.updateEvolveSkill();
        this.refreshSkinbtnRedPoint();
        this.layoutButtons();
    }
    moveTween(){
        //设置初始位置
        this.leftBox.x = -this.leftBox.width;
        this.rightBox.x = 720 + this.rightBox.width;
        TweenUtil.slideToSide(this.leftBox,0,300,0,Ease.quadIn);
        TweenUtil.slideToSide(this.rightBox,720 - this.rightBox.width,300,0,Ease.quadIn);
    }
    destroy(destroyChild?: boolean): void {
        super.destroy(destroyChild);
    }
    refRoleHeroInfoChange(){
        this.UpdateBtnState();
        this.refreshSoulHeroItem();
        this.updateDivineShow();
        this.refreshHeroLockState();
        this.onRefreshEquipInfo();
    }
    onRefreshEquipInfo(): void {
        //显示装备
        let heroId = this._hero_info.hero_id;
        let showHeroId = heroId;
        //异能英雄装备取被同调英雄的装备
        let belinkHero = HeroDataCenter.instance.getBeLinkHero(heroId);
        if (belinkHero) {
            showHeroId = belinkHero.hero_id;
        }
        let showTypeId = this._hero_info.type_id;
        let heroCfg = ConfigManager.cfg_hero_baseCache.get(showTypeId);
        let isSoulHero = heroCfg.nation == HeroNation.NATION_HUN;
        let isLinkSoulHero = isSoulHero && HeroDataCenter.instance.checkIsLinkSoulHero(heroId);

        this.onUpdateWingEquipShow();
        this.onUpdateDeputyEquipShow();

        let equipCount: number = HeroDataCenter.equipKindList.length;
        let soulHeroLockEquipKind = [
            ItemMacro.ITEM_KIND_WEAPON,
            ItemMacro.ITEM_KIND_ARMET,
            ItemMacro.ITEM_KIND_SHOES,
            ItemMacro.ITEM_KIND_ARMOR,
        ];

        let oriEquipVo: HeroEquipVo = HeroEquipManager.instance.getHeroEquipVo(heroId);
        let equipVo: HeroEquipVo = HeroEquipManager.instance.getHeroEquipVo(showHeroId);
        ConsoleUtils.log("oriEquipVo", oriEquipVo, "equipVo", equipVo);
        let kind: number;
        let isResonateEquip = false;
        //已穿戴的装备数
        let equipLen: number = 0;
        for (let i: number = 0; i < equipCount; i++) {
            kind = HeroDataCenter.equipKindList[i];
            let vo: any = {
                index: i,
                hero_id: heroId,
                isCanClick: true,
                isShowRed: true,
                isOtherHero: false,
                clickTips: null,
                isResonateEquip: isResonateEquip,
            };
            if (equipVo || oriEquipVo) {
                if (kind == ItemMacro.ITEM_KIND_BING_FU) {
                    kind = i == 4 ? ItemConst.ITEM_KIND_L_RIDE : ItemConst.ITEM_KIND_R_RIDE;
                    if (oriEquipVo) {
                        vo.equip = oriEquipVo.getEquipByKind(kind);
                    }
                } else {
                    if (equipVo) {
                        vo.equip = equipVo.getEquipByKind(kind);
                    }
                }

                if (vo.equip && kind != ItemConst.ITEM_KIND_L_RIDE && kind != ItemConst.ITEM_KIND_R_RIDE) {
                    equipLen = equipLen + 1;
                }
            }

            if (DudufuDataCenter.instance.is_unlock_equip && DudufuDataCenter.instance.checkHeroIsUseFiveHeroResonateEquip(showHeroId)) {
                let equipTypeId = DudufuDataCenter.instance.getFiveHeroResonateEquip(kind);
                if (equipTypeId != -1) {
                    let showGoodsId = equipTypeId;
                    let cfg_divine: cfg_divine = CfgCacheMapMgr.cfg_divineCache.get(equipTypeId);
                    if (cfg_divine && cfg_divine.resonate_replace_id > 0) {
                        showGoodsId = cfg_divine.resonate_replace_id;
                    }
                    vo.equip = GoodsVO.GetVoByTypeId(showGoodsId);
                    vo.equip.hero_id = heroId;
                    vo.isResonateEquip = true;
                    isResonateEquip = true
                }
            }

            let isSoulLockEquip = soulHeroLockEquipKind.indexOf(kind) > -1;
            if (isSoulHero && isSoulLockEquip) {
                vo.isSoulHero = true;
                if (!vo.equip || !vo.equip.typeId) {
                    vo.clickTips = window.iLang.L2_YI_NENG_HERO_XU_YAO_TONG_DIAO_LAI_TONG_BU_ZHUANG_BEI.il();
                    vo.isLock = isSoulLockEquip;
                    vo.isShowRed = !isSoulLockEquip;
                    vo.isCanClick = !isSoulLockEquip;
                } else {
                    let ori = vo.equip as GoodsVO;
                    vo.equip = GoodsVO.GetVoByTypeId(vo.equip.typeId);
                    if (vo.equip?.IsDivine) {
                        vo.equip.goods = ori.goods;
                    }
                    vo.equip.hero_id = heroId;
                    vo.isShowRed = true;
                    vo.isCanClick = true;
                }
            }

            if (kind == ItemMacro.ITEM_KIND_WING) {
                this.wingItem.setEquipData(this._hero_info, oriEquipVo ? oriEquipVo.getEquipByKind(kind) : null, false, isSoulHero && !isLinkSoulHero);
                this.dispatchEvent(ModuleCommand.UPDATE_HERO_WING_INFO);
            } else if (kind == ItemMacro.ITEM_KIND_DEPUTY) {
                this.deputyItem.setEquipData(this._hero_info, oriEquipVo ? oriEquipVo.getEquipByKind(kind) : null, false);
            } else {
                let equipItem = this._equipList[i];
                equipItem.setEquipData(vo);
            }
        }
        this.oneKeyUnLoadBtn.gray = false;
        if (isSoulHero) {
            this.onekeyLoadBtn.visible = false;
            this.oneKeyUnLoadBtn.visible = false;
        } else {
            let bol: boolean = HeroDataCenter.instance.CheckIsCanOneKey(this._hero_info);
            this.SetRedPoint(this.onekeyLoadBtn, bol, 160, -10);

            if (equipLen == 0 && bol == false) {
                this.onekeyLoadBtn.visible = true;
                this.oneKeyUnLoadBtn.visible = false;
            } else {
                this.onekeyLoadBtn.visible = bol;
                this.oneKeyUnLoadBtn.visible = !bol;
            }
        }
    }
    private refreshHeroLockState(): void {
        if (!this._hero_info) {
            return;
        }
        let is_lock = HeroDataCenter.instance.isHeroLock(this._hero_info.hero_id);
        this.btnUnlock.skin = `v2_heroInfo/btn_${is_lock ? "lock" : "unlock"}.png`;
    }
    private refreshSoulHeroItem(): void {
        let linkSoulHero = HeroDataCenter.instance.getLinkSoulHero(this._hero_info.hero_id);
        if (linkSoulHero) {
            this.soulHeroBox.visible = true;
            if (!this.soulHeroItem) {
                this.soulHeroItem = new HeroHeadItem();

                this.soulHeroItem.x = this.soulHeroBox.width / 2 - this.soulHeroItem.width / 2;
                this.soulHeroBox.y = 171;
                this.soulHeroBox.addChild(this.soulHeroItem);
            }
            this.soulHeroItem.SetInfo(linkSoulHero);
            this.soulHeroItem.setLinkEff(true);
        } else {
            this.soulHeroBox.visible = false;
        }
    }
    /**精灵装备槽位是否显示 */
    onUpdateWingEquipShow(): void {
        let showWing = HeroDataCenter.instance.getMaxStar(this._hero_info.type_id) >= WingDataCenter.instance.wing_unlock_star_limit;
        this.equipItem7.visible = showWing;
    }

    /**守护灵装备槽位是否显示 */
    onUpdateDeputyEquipShow(): void {
        let showDeputy = DeputyDataCenter.instance.isShowDeputy(this._hero_info.type_id);
        this.equipItem8.visible = showDeputy;
        this.imgDeputy.visible = DeputyDataCenter.instance.isDeputyHero(this._hero_info.hero_id);
    }
    public updateSkillInfo() {
        //w10 sentry容错
        if(!this._hero_info){
            return;
        }
        //w3改版.显示active_skills
        let skillIds = SkillDataCenter.ins.getHeroSkillIdsByHeroInfo(this._hero_info);
        let skill_id_level_map = SkillDataCenter.ins.GetHeroActiveSkills_id_Level_map(this._hero_info);
        for (let i = 0; i < this._skillItemList.length; ++i) {
            let item = this._skillItemList[i];
            let skillId = skillIds[i];
            if (item && skillId) {
                item.visible = true;
                let level = skill_id_level_map[skillId];
                let vo = new SkillItemVo();
                vo.skill_id = skillId;
                vo.skill_level = level;
                vo.heroInfo = this._hero_info;
                vo.id_level_map = skill_id_level_map;
                vo.isShowName = true;
                vo.isShowLevel = true;
                vo.isGrayWhenNotActive = true;
                item.setDataBySkillItemVO(vo,true);
                item.setIsShowCastSoulBg(vo.skill_level == 4);
            }
            else {
                item.visible = false;
            }
        }
    }
    private updateEvolveSkill() {
        //进化
        let heroInfo = this._hero_info;
        let cfg = CfgCacheMapMgr.cfg_hero_evolve_skillCache.get(heroInfo.type_id);
        if (!cfg || this._hero_info.limit_type > 0) {
            this.boxEvolveSkill.visible = false;
            return;
        }

        this.boxEvolveSkill.visible = true;
        this.btnEvolveSkill.visible = false;
        this.evolveSkillItem.visible = false;
        this._evolveSkillItem = this.evolveSkillItem;

        //改为默认显示
        let isEvolve = HeroEvolveDataCenter.ins.checkHasEvolveSkill(heroInfo.hero_id);
        if (isEvolve) {
            let vo = HeroEvolveDataCenter.ins.getEvolveSkillItemVo(heroInfo.hero_id);
            this._evolveSkillItem.setDataBySkillItemVO(vo);
            this._evolveSkillItem.visible = true;
        } else {
            //改为默认显示
            let canEvolve = true;
            this.lbEvolveSkill.text = cfg.star_limit + window.iLang.L2_XING_KAI_QI.il();
            this.lbEvolveSkill.visible = false;
            this.btnEvolveSkill.visible = true;
            if (canEvolve) {
                canEvolve = HeroEvolveDataCenter.ins.checkCanEvolveSkill(heroInfo.hero_id);
                this.SetRedPoint(this.btnEvolveSkill, canEvolve,);
            }
        }
    }
    public setData(hero: p_hero, cfg: cfg_hero_base = null): void {
        this._hero_info = hero;

        if (cfg) {
            this._cfg = cfg;
        }

        //设置英雄属性
        let arr: any[] = [];
        if (this._hero_info != null) {//代表是已经激活的英雄
            let cfgArr: fightAttr[] = ConfigManager.cfg_fightAttrByTypeCache.get(HeroDataCenter.ATTR_SHOW_TYPE_NORAML);
            cfgArr.sort(function (vo1: fightAttr, vo2: fightAttr) {
                return vo1.sort - vo2.sort;
            });

            for (let i: number = 0; i < cfgArr.length; i++) {
                let vo: fightAttr = cfgArr[i];
                let obj: any = new Object();
                obj.attrValue = FightAttrMgr.getIncValue(this._hero_info.fight[vo.attrKey], this._hero_info.fight[vo.attrKey + "_inc"]);
                obj.attrName = vo.attrDesc;
                obj.icon = vo.icon;
                arr.push(obj);
            }

            this._attrList.array = arr;
        }
        else if (this._cfg != null) {
            //英雄基础属性
            let fight_attr_vos = HeroUtil.getHeroFinalAttr(cfg.type_id);
            for (let i: number = 0; i < fight_attr_vos.length; i++) {
                let obj: any = new Object();
                let vo: FightAttrVO = fight_attr_vos[i];
                obj.attrValue = vo.valString;
                obj.attrName = vo.name;
                obj.icon = vo.icon;
                arr.push(obj);
            }

            this._attrList.array = arr;
        }

        this.btnReset.visible = this._hero_info.hero_id > 0 && !HeroDataCenter.instance.checkHeroInitialState(this._hero_info.hero_id);
        this.careerImg.skin = UrlConfig.COMMON_PATH + "career_" + cfg.career + ".png";
        this.careerLabel.text = `${GameConst.getCareerName(cfg.career)}（${cfg.desc}）`;

        this.updateLevelInfo();
        this.updateSkillInfo();
        this.refreshLinkBox();
        this.refreshWS9ZhouyinBox();
        this.updateDivineShow();
        this.onRefreshEquipInfo();
        this.refreshHeroRedPoint();
        this.updateEvolveSkill();
        this.refresUpdateBtnRedPoint();
    }
    private refreshLinkBox() {
        if (this.isTujian) {
            return ;
        }

        let isSoulHero = this._cfg.nation == HeroNation.NATION_HUN;
        let belinkHero = HeroDataCenter.instance.getBeLinkHero(this._hero_info.hero_id);
        this.upgradeBox.visible = !isSoulHero;
        this.notLinkBox.visible = isSoulHero && !belinkHero;
        this.linkBox.visible = isSoulHero && !!belinkHero;

        if (belinkHero) {
            this._linkHeroItem1.SetInfo(belinkHero);
            this._linkHeroItem1.setLinkEff(false);
            this._linkHeroItem2.SetInfo(this._hero_info);
            this.linkEff = this.showGSkeleton(this.spineEff,"link_eff",this.linkEff,{type:ESkeletonType.EFFECT});
        }
    }
    private refreshWS9ZhouyinBox() {
        if (!this.isTujian || !this.isShowWS9ZhouyinBox) {
            return;
        }
        this.upgradeBox.visible = true;
    }
    private updateLevelInfo() {
        let curLevel = this._hero_info["ori_level"] || this._hero_info.level;
        let curStage = HeroDataCenter.getHeroBigStage(this._hero_info);
        let curStar = this._hero_info.star;
        let curStarStage = this._hero_info.star_stage;
        let curSoulLv = this._hero_info.soul_level;
        let maxStar = this._cfg.max_star;
        let nextStar = Math.min(maxStar, curStar + 1);
        let isMaxFinalStar = maxStar == HeroConsts.FINAL_STAR;
        if (!this.isTujian) {
            let isActiveUpStar14 = HeroDataCenter.instance.checkIsActiveUpStar14(this._hero_info.hero_id);
            if (isMaxFinalStar && !isActiveUpStar14) {
                nextStar = Math.min(maxStar - 1, curStar + 1);
            }
        }
        else if (isMaxFinalStar && !HeroDataCenter.instance.isHadActiveUpStar14()) {
            nextStar = Math.min(maxStar - 1, curStar + 1);
        }

        let dudu_add_level = 0;
        let dhyana_add_level = 0;
        let strLevel = LangConst.L_P0_LEVEL.il(DudufuDataCenter.instance.dudu_add_level);
        (DudufuDataCenter.instance.is_unlock_dudu && DudufuDataCenter.instance.dudu_add_level != 0 && !this.isTujian) ? (strLevel) : "";
        if(DudufuDataCenter.instance.is_unlock_dudu && DudufuDataCenter.instance.dudu_add_level != 0){
            dudu_add_level = DudufuDataCenter.instance.dudu_add_level;
        }
        if(DudufuDataCenter.instance.is_unlock_dhyana && DudufuDataCenter.instance.dhyana_add_level != 0){
            dhyana_add_level = DudufuDataCenter.instance.dhyana_add_level;
        }

        let add_desc = "";
        if(!this.isTujian){
            let add_level = dudu_add_level + dhyana_add_level;
            add_desc = add_level > 0 ? "(+" + window.iLang.L2_P0_JI_ch37.il([add_level]) : "";
        }

        //异能英雄等级显示
        let heroCfg = ConfigManager.cfg_hero_baseCache.get(this._hero_info.type_id);
        let isSoulHero = heroCfg.nation == HeroNation.NATION_HUN;
        if (!this.isTujian && isSoulHero) {
            let soulHeroLvCfg = ConfigManager.cfg_soul_hero_link_levelCache.get(this._hero_info.soul_level);
            let maxLevel = soulHeroLvCfg.level_limit;
            this.setHeroLvText((this._hero_info ? curLevel : 1), "/" + maxLevel,add_desc);
            return;
        }

        //当前星级允许的最大阶级
        let maxStage: number = ConfigManager.getHeroMaxUpgradeStage(this._hero_info.star);
        let starMaxLevel: number = ConfigManager.GetHeroMaxUpgradeLevel(this._hero_info.star, maxStage, this._hero_info.star_stage);
        //当前星级、星阶和阶级允许的最大等级
        let stageMaxLevel: number = ConfigManager.GetHeroMaxUpgradeLevel(this._hero_info.star, this._hero_info.stage, this._hero_info.star_stage);
        this.updateUpLevelInfo(curLevel, stageMaxLevel, starMaxLevel);
        this.setHeroLvText((this._hero_info ? curLevel : 1), "/" + stageMaxLevel,add_desc);
        this.lbStarStage.text = curStar >= HeroConsts.openStarStageLV ? window.iLang.L2_XING_JIE_ch05.il() : window.iLang.L2_JIN_JIE_ch05.il();
        this.starStageBox.visible = curStar >= HeroConsts.openStarStageLV;
        this.stageBox.visible = curStar < HeroConsts.openStarStageLV;
        this.soulLvBox.visible = this.isTujian && isSoulHero && curSoulLv > 1;
        if (this.soulLvBox.visible) {
            //设置能级
            this.lbStarStage.text = window.iLang.L2_NENG_JI_ch05.il();
            this.setSoulLv(curSoulLv, curSoulLv);
        }
        else if (curStar >= HeroConsts.openStarStageLV) {
            //设置星阶
            let cfgList = ConfigManager.getCfgHeroStarStageList(this._hero_info.type_id, nextStar);
            this.setStarStageList(curStarStage, cfgList, curStar);
        }
        else {
            //设置英雄阶级
            this.setStageLv(curStage, maxStage);
        }

        this.maxLvImg.visible = curLevel >= starMaxLevel || curLevel >= stageMaxLevel;
        this.maxLvTxt.visible = curLevel >= starMaxLevel && this._hero_info.hero_id != 0 && this._cfg && this._hero_info.star < this._cfg.max_star;
        if((this.isFiveHeroResonate || this.isDudu) && !this.isTujian){
            this.updateBtn.gray = true;
            this.updateBtn.label = window.iLang.L2_SHENG_JI.il();
            this.setHeroLvText(DudufuDataCenter.instance.five_level,"",add_desc);
        }
    }
    private setHeroLvText(currentLv:number,maxLv:string,addText:string){
        this.fcCurrentLv.value = currentLv + "";
        this.fcMaxLv.value = maxLv + "";
        this.fcMaxLv.visible = !!maxLv;
        this.lbAddLv.text =  addText;
        this.lbAddLv.x = maxLv ? 210 : 145;
    }
    public updateUpLevelInfo(curLevel: number, stageMaxLevel: number, starMaxLevel: number): void {
        //剩下可升级的次数
        let upLevelStepLimit = MiscConst.hero_up_level_step_limit;//X级前可以快速升级
        let upLevelStep = MiscConst.hero_up_level_step;//升级次数
        let curUpLvMax: number = 0;
        if(curLevel < upLevelStepLimit) {
            curUpLvMax = Math.min(stageMaxLevel - curLevel, upLevelStepLimit);
        } else {
            curUpLvMax = Math.min(stageMaxLevel - curLevel, upLevelStep);
        }
        let canUpLv = 0;

        //消耗的金币和经验数
        let cost_id_1: number = 0;
        let cost_id_2: number = 0;
        let cost_num_1: number = 0;
        let cost_num_2: number = 0;

        for (let i: number = 0; i < curUpLvMax; i++) {
            let level_cfg: cfg_hero_level = ConfigManager.cfg_hero_levelCache.get(curLevel + i);
            cost_id_1 = level_cfg.cost_1;
            cost_id_2 = level_cfg.cost_2;
            //计算消耗是否满足
            if(HeroDataCenter.instance.isCheckMaterials(cost_id_1, cost_num_1 + level_cfg.cost_num_1) 
            && HeroDataCenter.instance.isCheckMaterials(cost_id_2, cost_num_2 + level_cfg.cost_num_2)) {
                canUpLv++;
                cost_num_1 += level_cfg.cost_num_1;
                cost_num_2 += level_cfg.cost_num_2;
            }
            else {
                if (i == 0){
                    cost_num_1 = level_cfg.cost_num_1;
                    cost_num_2 = level_cfg.cost_num_2;
                }
                break;
            }
        }

        //消耗
        this._silverExpend.setData(cost_id_1, cost_num_1);
        this._expExpend.setData(cost_id_2, cost_num_2);

        this.updateBtn.gray = curLevel >= starMaxLevel;
        this.updateBtn.disabled = curLevel >= starMaxLevel;
        let strUpLevel = (canUpLv > 0) ? LangConst.L_UP_P0_LEVEL.il([canUpLv]) : LangConst.L2_SHENG_JI.il();
        const isCanUpStage:boolean = curLevel <= starMaxLevel && curLevel >= stageMaxLevel;
        this.updateBtn.label = isCanUpStage ?  window.iLang.L2_JIN_JIE.il():strUpLevel;
        this._expExpend.visible = curLevel < stageMaxLevel;
        this._silverExpend.visible = curLevel < stageMaxLevel;
        if((this.isFiveHeroResonate || this.isDudu) && !this.isTujian){
            this.updateBtn.gray = true;
            this.updateBtn.label = window.iLang.L2_SHENG_JI.il();
        }
    }
    /**设置能级 */
    public setSoulLv(level: number = 0, maxLevel: number = 0): void {
        let arr: any[] = [];
        for (let i: number = 0; i < maxLevel; i++) {
            arr.push(i + 1);
        }
        this._soulLvList.visible = true;
        this._soulLvList.AddOtherParamete("curStage", level);
        this._soulLvList.array = arr;
    }
      /**设置英雄阶级 */
      public setStageLv(level: number = 0, maxStage: number = 0): void {
        let arr: any[] = [];
        for (let i: number = 0; i < maxStage; i++) {
            arr.push(i + 1);
        }
        this._stageList.visible = true;
        this._stageList.AddOtherParamete("curStage", level);
        this._stageList.array = arr;
    }

    /**设置星阶 */
    public setStarStageList(starStageList: number[], starStageCfg: cfg_hero_star_stage[], star: number = 0): void {
        this._starStageList.parameter = { 
            starStageList: starStageList,
            star: star,
        };
        this._starStageList.array = starStageCfg;
    }
    public onAttrChange(): void {
        let curLevel = this._hero_info["ori_level"] || this._hero_info.level;
        //当前星级允许的最大阶级
        let maxStage: number = ConfigManager.getHeroMaxUpgradeStage(this._hero_info.star);
        let starMaxLevel: number = ConfigManager.GetHeroMaxUpgradeLevel(this._hero_info.star, maxStage, this._hero_info.star_stage);
        //当前星级、星阶和阶级允许的最大等级
        let stageMaxLevel: number = ConfigManager.GetHeroMaxUpgradeLevel(this._hero_info.star, this._hero_info.stage, this._hero_info.star_stage);
        this.updateUpLevelInfo(curLevel, stageMaxLevel, starMaxLevel);
    }
    updateDivineShow() {
        if (!this._cfg) {
            return;
        }
        if (this._cfg.nation != HeroNation.NATION_HUN && this._hero_info.star >= MiscConstAuto.divine_equip_open) {
            this.btnDivine.visible = true;
        } else {
            this.btnDivine.visible = false;
        }

        this.onStopDivineBtnEff();
        let isDivineActivated = DivineDataCenter.instance.IsActivateHero(this._hero_info.type_id);
        if (isDivineActivated) {
            this.btnDivine.skin = "divine/btnActivedOpen.png";
            this.playDivineBtnEff("divine_icon_2");
        } else {
            this.btnDivine.skin = "divine/btnOpen.png";
            this.playDivineBtnEff("divine_icon_1");
        }
        this.SetRedPoint(this.btnDivine, DivineDataCenter.instance.getHeroActiveRedPoint(this._hero_info.type_id), 48);
    }
    private playDivineBtnEff(name: string): void {
        if (!this._divineBtnEff) {
            this._divineBtnEff = SkeletonManager.ins.createSkeleton(name, ESkeletonType.UI_EFFECT);
            this._divineBtnEff.pos(this.btnDivine.width / 2, this.btnDivine.height / 2);
            this.btnDivine.addChild(this._divineBtnEff);
        }
    }

    private onStopDivineBtnEff(): void {
        if (this._divineBtnEff) {
            this._divineBtnEff.removeSelf();
            this._divineBtnEff.destroy();
            this._divineBtnEff = null;
        }
    }
    //刷新红点状态
    public refreshHeroRedPoint(): void {
        this.SetRedPoint(this.btnSkin, false);
    }
    public refresUpdateBtnRedPoint(){
        if (!this._hero_info) {
            return;
        }
        if(this.isFiveHeroResonate || this.isDudu){
            this.SetRedPoint(this.updateBtn, false, this.updateBtn.width - 20, -10);
            return;
        }
        let is_can_level_up = HeroDataCenter.instance.IsCanLevelUpdate(this._hero_info);
        let is_can_stage_up = HeroDataCenter.instance.IsCanStageUpdate(this._hero_info);
        this.SetRedPoint(this.updateBtn, is_can_level_up , this.updateBtn.width - 20, -10);
        this.SetRedPoint(this.updateBtn,is_can_level_up || is_can_stage_up, this.updateBtn.width - 20, -10);
    }
    private refreshSkinbtnRedPoint() {
        let skinList: cfg_hero_skin[];
        if (this._hero_info && this._hero_info?.type_id) {
            skinList = ConfigManager.cfg_hero_skin_listCache.get(this._hero_info.type_id);
        } 
        if (skinList instanceof Array) {
            let isSkinActive = false;
            for (let i = 0; i < skinList.length; i++) {
                let skinCfg = ConfigManager.cfg_hero_skinCache.get(skinList[i]?.skin_id);
                let itemCount = GoodsManager.instance.GetGoodsNumByTypeId(skinCfg?.item_id);
                let isCanUnlock = itemCount > 0;
                if (!HeroSkinDataCenter.instance.isSkinActive(skinList[i]?.skin_id) && isCanUnlock && HeroSkinDataCenter.instance.isSatisfyStar(skinList[i]?.skin_id)) {
                    isSkinActive = true;
                }
            }
            let isSkinUpdate = false;
            for (let i = 0; i < skinList.length; i++) {
                if (HeroSkinDataCenter.instance.isSkinCanUpdate(skinList[i]?.skin_id)) {
                    isSkinUpdate = true;
                }
            }
            if (isSkinActive || isSkinUpdate) {
                this.SetRedPoint(this.btnSkin, true);

            } else {
                this.SetRedPoint(this.btnSkin, false);
            }
        }
    }
}