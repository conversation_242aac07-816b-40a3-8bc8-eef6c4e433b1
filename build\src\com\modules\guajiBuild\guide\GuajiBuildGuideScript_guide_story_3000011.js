import { Handler } from "laya/utils/Handler";
import { GuideMgr } from "../../guide/GuideMgr";
import { GuajiBuildGuideScript_StepBase } from "./GuajiBuildGuideScript_StepBase";
import { Point } from "laya/maths/Point";
import { ESkeletonAction, ESkeletonType } from "../../baseModules/skeleton/SkeletonData";
import { GuajiBuildWorker } from "../game/GuajiBuildWorker";
import { SkeletonManager } from "../../baseModules/skeleton/SkeletonManager";
import { GuaJiBuildDataCenter } from "../data/GuaJiBuildDataCenter";
import { GuajiBuildGuideScript } from "./GuajiBuildGuideScript";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { MiscConstAuto } from "../../../auto/MiscConstAuto";
import { LordDataCenter } from "../../Lord/data/LordDataCenter";
import { LayerManager } from "../../../managers/LayerManager";
/**
 * 工人走向建筑，并显示喊话
 * 镜头放大平移至左上角，3个模型从左侧跑出来（使用英雄3d模型）
 * 右侧巡逻兵喊话
 * 3秒后进行下一句
 * 3秒后左边两个模型统一喊话
 * 3秒后左侧两个模型跑出屏幕
 * 然后引导点击模型
 * 点击模型后显示特效
 * 特效结束，地图中的模型变为获得英雄的模型，弹出获得英雄窗口（相当于初始英雄）
 */
export class GuajiBuildGuideScript_guide_story_3000011 extends GuajiBuildGuideScript_StepBase {
    constructor() {
        super(...arguments);
        this._isTalkFinish = false;
        this._isFinish = false;
        this.recordStepMap = new Map();
        this.heroViewMap = new Map();
        this.heroViewList = [];
    }
    // private fightRole:GuajiBuildWorker;
    get isFinish() {
        return this._isFinish;
    }
    set isFinish(value) {
        this._isFinish = value;
    }
    init(isNewLogin, dialog) {
        var _a;
        super.init(isNewLogin, dialog);
        let isFinish = GuideMgr.ins.isFinishMission(GuajiBuildGuideScript.guide_story_3000011);
        if (isFinish) {
            GuideMgr.ins.skipGuide(GuajiBuildGuideScript.guide_story_3000011);
            return;
        }
        this.dialog.isScriptHideGuide = true;
        // let heroSkinNameList = ["H226_shushi", "H106_huonv", "H206_banrenma"];
        let heroSkinNameList = [];
        MiscConstAuto.build_plot_heros.forEach(heroId => {
            let cfg = CfgCacheMapMgr.cfg_client_w3_skinCache.get(heroId);
            heroSkinNameList.push(cfg.skin_res);
        });
        //探索剧情的武将显示（从左到右,前3个是混沌阵营,后3个是秩序阵营）
        let camp = LordDataCenter.instance.camp;
        if (camp == LordDataCenter.TYPE_CHAOS) {
            heroSkinNameList = heroSkinNameList.slice(0, 3);
        }
        else {
            heroSkinNameList = heroSkinNameList.slice(3, 6);
        }
        for (let i = 0; i < 3; i++) {
            let skinName = heroSkinNameList[i];
            let heroView = new GuajiBuildWorker();
            heroView.init();
            heroView.set3d(skinName);
            // heroView.size(100, 100);
            heroView.show3d.sp3Scale(0.3);
            heroView.name = "guideStoryHero" + i;
            heroView.isShowShadow = true;
            heroView.zOrder = LayerManager.STAGE_ZORDE_TOPEST;
            this.dialog.addOnClick(this, heroView, this._on_click_hero);
            this.boxMap.addChild(heroView);
            this.heroViewMap.set(skinName, heroView);
            this.heroViewList.push(heroView);
            if (i == 0) {
                //预加载
                (_a = heroView.show3d.sp3view) === null || _a === void 0 ? void 0 : _a.sp3LoadAnim(ESkeletonAction.ATTACK_1);
            }
        }
        this._step_0_move();
    }
    _step_0_move() {
        this.timer.once(1500, this, () => {
            this._step_1_talk();
        });
        //镜头
    }
    _step_1_talk() {
        let heroX = 300;
        let heroY = 1250;
        let mapPos = Point.create().setTo(heroX, heroY);
        // this.boxMap._tweenTo({scaleX:1.5, scaleY:1.5}, 2000);
        this.buildView.moveToMapPos(mapPos, 0.5, 1.3, Handler.create(this, () => {
            // this.buildView.moveToBuilding(EBuildingType.BUILD_TYPE_ALTAR, 1, Handler.create(this, () => {
            let delay = 1000;
            //3个模型从左侧跑出来（使用英雄3d模型）
            for (let i = 0; i < this.heroViewList.length; i++) {
                let heroView = this.heroViewList[i];
                let fromPos = Point.create().setTo((i * -100) + 100, heroY);
                let toPos = Point.create().setTo(360 - i * 100, heroY);
                heroView.zOrder = heroY;
                heroView.pos(fromPos.x, fromPos.y);
                heroView.playAni(ESkeletonAction.RUN, true);
                heroView._tweenTo({ x: toPos.x, y: toPos.y }, 1500, null, Handler.create(this, () => {
                    heroView.playAni(ESkeletonAction.IDLE, true);
                }));
                // heroView.talkView.bottom = 180;
            }
            //右侧巡逻兵喊话
            delay += 1000;
            this.timer.once(delay, this, () => {
                let firstWorker = this.heroViewList[0];
                firstWorker.talkView.size(200, 150);
                firstWorker.talkView.pos(0, -220);
                firstWorker.talkView.setTalkText(this.getPlotDialogue(4, this.camp), 2500, false);
            });
            //3秒后进行下一句
            delay += 3000;
            this.timer.once(delay, this, () => {
                let firstWorker = this.heroViewList[0];
                // let scaleX = Math.abs(firstWorker.scaleX);
                // firstWorker.scaleX = -scaleX;
                //转向
                // firstWorker.show3d.sp3FlipX(true);
                firstWorker.show3d.sp3Rotate(0, 0, 0);
                firstWorker.talkView.size(200, 150);
                firstWorker.talkView.setTalkText(this.getPlotDialogue(5, this.camp), 2500, false);
            });
            //3秒后左边两个模型统一喊话
            delay += 3000;
            this.timer.once(delay, this, () => {
                for (let i = 1; i < this.heroViewList.length; i++) {
                    let heroView = this.heroViewList[i];
                    heroView.talkView.setTalkText(this.getPlotDialogue(6, this.camp), 2500, true);
                    heroView.talkView.pos(0, -170);
                }
            });
            //3秒后左侧两个模型跑出屏幕
            delay += 2000;
            this.timer.once(delay, this, () => {
                for (let i = 1; i < this.heroViewList.length; i++) {
                    let toPos = Point.create().setTo(i * -100, heroY);
                    let heroView = this.heroViewList[i];
                    // let scaleX = Math.abs(heroView.scaleX);
                    // heroView.scaleX = -scaleX;
                    heroView.show3d.sp3FlipX(true);
                    // heroView.show3d.sp3Rotate(0, 360, 0);
                    heroView.playAni(ESkeletonAction.RUN, true);
                    heroView._tweenTo({ x: toPos.x, y: toPos.y }, 1500, null, Handler.create(this, () => {
                        heroView.playAni(ESkeletonAction.IDLE, true);
                    }));
                }
            });
            delay += 2000;
            this.timer.once(delay, this, () => {
                // let firstWorker = this.heroViewList[0];
                // firstWorker.talkView.setDesc("释放黑暗魔法", 999999, true);
                this._step_2_click_hero();
            });
        }));
    }
    _step_2_click_hero() {
        this._isTalkFinish = true;
        this.dialog.isScriptHideGuide = false;
        // GuideMgr.ins.clearGranchGuide(true);
        // GuideMgr.ins.setGranchGuideId(GuajiBuildGuideScript.guide_story1_3000011);
    }
    //点击模型后显示特效
    _on_click_hero(e) {
        if (!this._isTalkFinish) {
            return;
        }
        this.dialog.isScriptHideGuide = true;
        let firstWorker = this.heroViewList[0];
        firstWorker.talkView.setTalkText(this.getPlotDialogue(7, this.camp), 2500, true);
        let heroView = this.heroViewList[0];
        heroView.playAni(ESkeletonAction.ATTACK_1, false);
        let sk = SkeletonManager.ins.createSkeleton("build_fuhuo", ESkeletonType.UI_EFFECT);
        sk.pos(heroView.x - 20, heroView.y - 30);
        sk.zOrder = heroView.zOrder;
        let delay = 500;
        this.timer.once(delay, this, () => {
            sk.playAction(ESkeletonAction.IDLE, false, true);
            this.boxMap.addChild(sk);
        });
        delay += 1500;
        //特效结束，地图中的模型变为获得英雄的模型，弹出获得英雄窗口（相当于初始英雄）
        this.timer.once(delay, this, () => {
            let get_hero_id = +this.getNewGuideFirstHero();
            let cfg = CfgCacheMapMgr.cfg_client_w3_skinCache.get(get_hero_id);
            heroView.set3d(cfg.skin_res);
            heroView.show3d.sp3Scale(0.3);
        });
        delay += 500;
        this.timer.once(delay, this, () => {
            sk.destroy();
            GuaJiBuildDataCenter.instance.m_guide_get_hero_tos(1);
        });
        delay += 1000;
        this.timer.once(delay, this, () => {
            this.dialog.isScriptHideGuide = false;
        });
    }
    getNewGuideFirstHero() {
        let heroes = MiscConstAuto.new_guide_first_heroes;
        let camp = LordDataCenter.instance.camp;
        if (camp > 0 && heroes.length >= camp) {
            return heroes[camp - 1];
        }
        else {
            return heroes[0];
        }
    }
    initUI() {
    }
    addClick() {
    }
    addEvent() {
        // this.addEventListener(ModuleCommand.ON_CLOSE_TD_GUIDE_NEW_HERO_DIALOG, this, this.onCloseNewHeroDialog);
    }
    onNewLogin() {
    }
    isOpenGuide() {
        return true;
    }
    checkGuide(currCfg) {
        let step = GuideMgr.ins.curStep;
        if (!this.recordStepMap.get(step)) {
            this.recordStepMap.set(step, true);
            if (step == 0) {
            }
            else if (step == 1) {
            }
            else if (step == 2) {
                //点击挑战按钮
            }
            else if (step == 3) {
            }
        }
    }
    // private createFightHero(){
    //     //创建一个假人,用于引导
    //     let elementMgr = MainUIManager.instance.mainBattleLayer.elementMgr;
    //     let guaJiLayer: GuaJiSceneLayer = elementMgr.guaJiLayer;
    //     let enemy: RoleBase = elementMgr.findRoleByCamp(RoleCamp.ENEMY)[0];
    //     if (enemy) {
    //         let heroView = new GuajiBuildWorker();
    //         heroView.init();
    //         heroView.set3d(enemy._cfgHero.skin3d);
    //         // heroView.size(100, 100);
    //         heroView.show3d.sp3Scale(0.3);
    //         heroView.name = "guideStoryFightHero";
    //         heroView.zOrder = enemy.zOrder;
    //         heroView.pos(enemy.x, enemy.y);
    //         heroView.playAni(ESkeletonAction.DEAD, true);
    //         guaJiLayer.addChild(heroView);
    //         this.dialog.addOnClick(this, heroView, this._on_click_fight_hero);
    //     }
    // }
    onGuideEnd(guideId) {
    }
    onFightResult(toc) {
        //播放武将变身动画
        // heroView.playAni(ESkeletonAction.ATTACK_1, false);
        // let sk = SkeletonManager.ins.createSkeleton("build_fuhuo", ESkeletonType.UI_EFFECT);
        // sk.pos(heroView.x - 20, heroView.y - 30);
        // sk.zOrder = heroView.zOrder;
        // let sk = SkeletonManager.ins.createSkeleton("build_fuhuo", ESkeletonType.UI_EFFECT);
        // sk.pos(heroView.x - 20, heroView.y - 30);
        // sk.zOrder = heroView.zOrder;
        // let delay = 500;
        // this.timer.once(delay, this, () => {
        //     sk.playAction(ESkeletonAction.IDLE, false, true);
        //     this.boxMap.addChild(sk);
        // })
        GuaJiBuildDataCenter.instance.m_guide_get_hero_tos(2);
        // this.dialog.isScriptHideGuide = false;
        //     //创建一个假人,用于引导
        // let elementMgr = MainUIManager.instance.mainBattleLayer.elementMgr;
        // let guaJiLayer: GuaJiSceneLayer = elementMgr.guaJiLayer;
        // let enemy: RoleBase =null;
        // for(let child of guaJiLayer._children){
        //     if(child instanceof RoleBase && child.camp == RoleCamp.ENEMY){
        //         enemy = child;
        //     }
        // }
        // if (enemy) {
        //     let imgClick = new Image();
        //     // imgClick.skin = CommonButton.BtnYellow;
        //     imgClick.name = "imgClick";
        //     imgClick.size(100, 100);
        //     imgClick.pos(-50, -100);
        //     imgClick = imgClick;
        //     enemy.addChild(imgClick);
        //     enemy.hitArea = Rectangle.create().setTo(-50, -100, 100, 100);
        //     this.dialog.addOnClick(this, enemy, this._on_click_fight_hero);
        // }
    }
    _on_click_fight_hero() {
        console.log("-----_on_click_fight_hero");
    }
    onDestroy() {
        this.heroViewList.forEach((heroView) => {
            heroView.destroy();
        });
        this.heroViewList = [];
        this.heroViewMap.clear();
    }
}
