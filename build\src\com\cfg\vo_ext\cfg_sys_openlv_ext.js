import { cfg_sys_openlv } from "../vo/cfg_sys_openlv";
import { DataCenter } from "../../modules/DataCenter";
/**
 * 配置扩展
 */
export class cfg_sys_openlv_ext extends cfg_sys_openlv {
    constructor(cfgBase) {
        super();
        this.cfgBase = cfgBase;
        this.initAttrs();
        //获取字段的属性索引，M3模式=1
        let propIndex = DataCenter.isM3Building ? 1 : 0;
        this.parseExt(propIndex);
    }
    initAttrs() {
        //赋值
        let vo = this.cfgBase;
        for (const key in vo) {
            if (key != cfg_sys_openlv_ext.ext) {
                this[key] = vo[key];
            }
        }
    }
    parseExtField(strValue, propIndex = 0) {
        if (strValue) {
            let arrNumValues = strValue.split("|");
            if (arrNumValues.length > propIndex) {
                if (propIndex > 0) {
                    return Number(arrNumValues[propIndex]);
                }
                else {
                    return Number(arrNumValues[0]);
                }
            }
            else {
                return Number(arrNumValues[0]);
            }
        }
        return 0;
    }
    parseExt(proIndex = 0) {
        this.openLevel = this.parseExtField(this.level_limits, proIndex);
        this.guaji_pass = this.parseExtField(this.pass_limits, proIndex);
        this.openCardStage = this.parseExtField(this.card_stage_limits, proIndex);
    }
}
cfg_sys_openlv_ext.ext = "ext";
