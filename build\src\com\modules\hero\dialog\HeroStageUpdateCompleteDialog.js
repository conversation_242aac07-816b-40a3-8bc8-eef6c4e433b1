import { ConfigManager } from "../../../managers/ConfigManager";
import { com } from "../../../ui/layaMaxUI";
import { UIList } from "../../baseModules/UIList";
import { FightAttrVO } from "../../role/vo/FightAttrVO";
import { HeroDataCenter } from "../data/HeroDataCenter";
import { HeroUtil } from "../util/HeroUtil";
import HeroStageItem from "../view/stageUpdate/HeroStageItem";
//界面类都是每次打开每次新建的
export default class HeroStageUpdateCompleteDialog extends com.ui.res.heroInfo.HeroStageUpdateCompleteDialogUI {
    constructor() {
        super();
        // this.resName = UrlConfig.HERO_UPDATE_RES;
        //有大底图的都要单独加载，不要打包到图集
        this.navShow = 0 /* NONE */;
    }
    initUI() {
        this._topIns = this.topIns;
        this._topIns.setTitleIcon("img_tips_title4.png");
        this._heroItem = this.heroItem;
        this._itemList = [
            this.attrItem1,
            this.attrItem2,
            this.attrItem3,
            this.attrItem4,
        ];
        this._stageList = UIList.SetUIList(this, this.stageBox, HeroStageItem, null);
        this._stageList.SetRepeat(10, 1);
        this._stageList.SetSpace(5, 0);
        this._stageList.scrollBarHide = true;
        this._stageList.isBoxCenter = true;
    }
    onOpen(param) {
        let msg = param[0];
        let oldStage = param[1];
        let heroId = msg.hero_id;
        let newStage = msg.new_level;
        this._heroInfo = HeroDataCenter.instance.getHero(heroId);
        let typeId = this._heroInfo.type_id;
        let curLevel = this._heroInfo.level;
        let curStar = this._heroInfo.star;
        let heroBaseCfg = ConfigManager.cfg_hero_baseCache.get(typeId);
        this._heroItem.setSimpleInfoByCfg(heroBaseCfg, curStar, curLevel, heroBaseCfg.name);
        //战力
        this.curLabel.text = msg.old_power.toString();
        this.nextLabel.text = msg.new_power.toString();
        //属性
        let maxStage = Math.floor(ConfigManager.getHeroMaxUpgradeStage(curStar));
        let curMaxLevel = ConfigManager.GetHeroMaxUpgradeLevel(curStar, oldStage);
        let nextMaxLevel = ConfigManager.GetHeroMaxUpgradeLevel(curStar, newStage);
        this.setStarLv(this._stageList, newStage, maxStage, true);
        this.levelItem.curLabel.text = curMaxLevel.toString();
        this.levelItem.nextLabel.text = nextMaxLevel.toString();
        this.levelItem.bgImg.visible = true;
        let cur_attrs = HeroUtil.getHeroFinalAttr(typeId, curLevel, oldStage, curStar);
        let next_attrs = HeroUtil.getHeroFinalAttr(typeId, curLevel, newStage, curStar);
        for (let i = 0; i < cur_attrs.length; i++) {
            let curAttrVo = cur_attrs[i];
            let nextAttrVo = next_attrs[i];
            let item = this._itemList[i];
            if (item) {
                // item.iconImg.skin = curAttrVo.iconUrl;
                item.bgImg.visible = true;
                item.descLabel.text = curAttrVo.name + "：";
                item.curLabel.text = FightAttrVO.formatValLimitLen(curAttrVo.val, curAttrVo.val_type, { fractionDigits: 0 });
                item.nextLabel.text = FightAttrVO.formatValLimitLen(nextAttrVo.val, nextAttrVo.val_type, { fractionDigits: 0 });
            }
        }
    }
    addClick() {
        // this.addOnClick(this, this, this.onClick);
    }
    onClick() {
        this.close();
    }
    close(type = null) {
        super.close(type);
    }
    /**设置英雄星级 */
    setStarLv(list, level = 0, maxStage = 0, showEffect) {
        let arr = [];
        for (let i = 0; i < maxStage; i++) {
            arr.push(i + 1);
        }
        list.visible = true;
        list.AddOtherParamete("curStage", level);
        list.array = arr;
    }
}
