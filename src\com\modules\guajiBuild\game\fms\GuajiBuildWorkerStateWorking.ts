import { ILaya } from "ILaya";
import { ESkeletonAction } from "../../../baseModules/skeleton/SkeletonData";
import { RoleStateBase } from "../../../../scene2d/role/fsm/state/RoleStateBase";
import { GuajiBuildWorker } from "../GuajiBuildWorker";
import { EGuajiBuildPathPointType } from "../../vo/GuajiBuildPathPointVo";

export class GuajiBuildWorkerStateWorking extends RoleStateBase {

	public duration = 0;
	public workType: EGuajiBuildPathPointType = EGuajiBuildPathPointType.GO_AND_BACK;

	public OnEnter(): void {

		this.PlayAni(ESkeletonAction.ATTACK, true);

		let worker = this.roleBase as GuajiBuildWorker;
		let pointList = worker.pathPointList;
		let firstPoint = worker.firstPathPoint;
		let lastPoint = worker.lastPathPoint;

		this.duration = worker.workDuration;
		this.workType = worker.editorVo.work_type;

		// let sk = worker.skAni;
		// if (sk) {
		// 	sk.playAction(ESkeletonAction.RUN, true);
		// }

		//flipX

		if (lastPoint && worker.sk) {
			// let flipX = lastPoint.worker_flip_x ? -1 : 1;
			// let scale = Math.abs(worker.sk.scaleX);
			// // worker.show3d.scaleX = scale * isFlipX;
			// worker.setSkScale(scale * flipX, worker.sk.scaleY);
			worker.isFlipX = lastPoint.worker_flip_x;
		}
	}

	public OnUpdate(): void {
		if (this.workType == EGuajiBuildPathPointType.GO_AND_BACK){
			this.duration -= ILaya.timer.delta;
			if (this.duration <= 0) {
				let worker = this.roleBase as GuajiBuildWorker;
				this.duration = 0;
				worker.startReversePathPoint();
			}
		}
	}

	public OnLeave(): void {
		// let worker = this.roleBase as GuajiBuildWorker;
		// if (worker.sk){
		// 	let scale = Math.abs(worker.sk.scaleX);
		// 	worker.setSkScale(scale, scale);
		// }

		// this.ToStand();
	}
}