import { ILaya } from "ILaya";
import { Node } from "laya/display/Node";
import { Sprite } from "laya/display/Sprite";
import { Point } from "laya/maths/Point";
import { Dialog } from "laya/ui/Dialog";
import { Handler } from "laya/utils/Handler";
import { GlobalConfig } from "../../../game/GlobalConfig";
import { ConfigManager } from "../../managers/ConfigManager";
import { DispatchManager } from "../../managers/DispatchManager";
import { LayerManager } from "../../managers/LayerManager";
import { LocalStorageUtil } from "../../util/LocalStorageUtil";
import { TipsUtil } from "../../util/TipsUtil";
import { CmdDataCenter } from "../cmd/data/CmdDataCenter";
import { DataCenter } from "../DataCenter";
import HeroEquipManager from "../equip/data/HeroEquipManager";
import { ItemConst } from "../goods/ItemConst";
import { LoadingDataCenter } from "../loading/data/LoadingDataCenter";
import MainUIManager, { EMainUIBottomIndex } from "../menu/MainUIManager";
import { ModuleCommand } from "../ModuleCommand";
import { GuideConst } from "./GuideConst";
import GuideTipIns from "./view/GuideTipIns";
import { Event } from "../../../../libs/laya/events/Event";
import { CmdConsts } from "../cmd/data/CmdConsts";
import { MiscConst } from "../misc_config/MiscConst";
import { CfgCacheMapMgr } from "../../cfg/CfgCacheMapMgr";
import { cfg_guide_helper } from "../../cfg/vo/cfg_guide_helper";
import { UIUtil } from "../../util/UIUtil";
import { cfg_guide_mission } from "../../cfg/vo/cfg_guide_mission";
import { StoryGameMgr } from "../storyGame/StoryGameMgr";
import { TdMainDataCenter } from "../tdMain/data/TdMainDataCenter";
import { ItemMacro } from "../../auto/ItemMacro";

export class GuideMgr {

    private static _ins: GuideTipIns;

    /**对话 */
    public static TYPE_DIALOGUE: number = 1;
    /**对话手势 */
    public static TYPE_HAND: number = 2;
    /**气泡手势 */
    public static TYPE_BUBBLE_HAND: number = 3;
    /**黑幕 */
    public static TYPE_BLAK: number = 4;
    /**剧情2 */
    public static TYPE_DIALOGUE_5: number = 5;
    /**英雄展示 */
    public static TYPE_HERO_INFO: number = 6;
    /**指引模拟战斗 */
    public static TYPE_FIGHT: number = 7;
    /**战斗剧情 */
    public static TYPE_STORY: number = 8;
    /**战斗剧情对话 */
    public static TYPE_STORY_DIALOGUE: number = 9;
    /** 对话气泡 */
    public static TYPE_DIALOGUE_POP: number = 10;
    /**审核剧情 */
    public static TYPE_SHEN_HE_STORY: number = 11;

    /**指引描述显示位置**/
    public static left_up: number = 1;
    //public static left_center: number = 2;
    public static left_down: number = 3;

    public static right_up: number = 4;
    //public static right_center: number = 5;
    public static right_down: number = 6;

    public nextHandPos: Point;

    private _serverGuideId: number = -1;
    /**缓存的指引 */
    //private _cacheGuideId: number[] = [];

    /**当前前端运行中的指引ID */
    private _curGuideId: number = 0;

    public granchTempData: any = null;

    /**指引步骤 */
    private _curStep: number = 0;

    public guideTipList: Map<Sprite, GuideTipIns> = new Map();

    private static _instance: GuideMgr;

    /***强制隐藏指引 */
    private _isHideGuide: boolean = false;

    /**隐藏指引对话界面*/
    private _isHideGirl: boolean = false;

    public isOpenGuideGirl: boolean = false;

    /**是否启动指引 true 表示指引已经启动 */
    private _isStartGuide: boolean = false;

    /**最新指引 */
    private _curCfg: cfg_guide_helper;

    /**正在指引中 */
    public isGuideing: boolean = false;
    /**禁止点击菜单 */
    private _isClickMenu: boolean = false;

    public _hideGuideUIDic: Map<string, boolean> = new Map();

    private _curGuideHelper: cfg_guide_helper[] = [];
    /**已经完成的指引 */
    public finishMissions: number[] = [];
    /**指令关闭指引 */
    public cmdCloseGuide: boolean = false;

    /**根据性别获取立绘 */
    public getPicByGender(pic:string){
        let realPic = pic;
        if(pic == "zhujue_nan.png" || pic == "zhujue_nv.png"){
            realPic = DataCenter.sex == 1 ? "zhujue_nan.png" : "zhujue_nv.png";    
        }
        if (realPic && realPic.endsWith(".png") == false){
            realPic += ".png";
        }
        return realPic;
    }
    public static get ins(): GuideMgr {
        if (GuideMgr._instance == null) {
            GuideMgr._instance = new GuideMgr();
        }
        return GuideMgr._instance;
    }

    public getTipIns(): GuideTipIns {
        return new GuideTipIns();
    }
    public get curCfg(): cfg_guide_helper {
        return this._curCfg || this._curGuideHelper[this.curStep];
    }

    /**禁止点击菜单  2000毫秒 */
    public set isClickMenu(val: boolean) {
        this._isClickMenu = val;
        if (this._isClickMenu == true) {
            ILaya.timer.once(2000, this, function (): void {
                this._isClickMenu = false;
            });
        }
    }

    public get isClickMenu(): boolean {
        return this._isClickMenu;
    }

    public set isHideGuide(val: boolean) {
        this._isHideGuide = val;
        if (val == true) {
            this.hideAllHand();
        } else {
            this.startGuide();
        }
    }

    public checkIsBottomOrTopLayer(cfg: cfg_guide_helper): boolean {
        let ret = false;
        if (cfg) {
            if(cfg.ui_name == "MainUIBottomLayer" || cfg.ui_name == "MainUITopLayer"){
                ret = true;
            }
        }
        return ret;
    }

    public getIsHideGuideUI(uiName: string): boolean {
        return !!this._hideGuideUIDic.get(uiName);
    }
    public setHideGuideUI(val: boolean, uiName: string): void {
        if (val == true && uiName != undefined) {
            this._hideGuideUIDic.set(uiName, val);
        } else {
            this._hideGuideUIDic.delete(uiName);
        }

        if (this._hideGuideUIDic.size > 0) {
            //this.isHideGirl = true;
            this.hideAllHand();
            this.dispatchEvent(ModuleCommand.CLOSE_GUIDE_GIRL_DIALOG);
        } else {
            this.isHideGirl = false;
            this.startGuide();
        }
    }

    /**隐藏指引对话界面*/
    public set isHideGirl(val: boolean) {
        this._isHideGirl = val;
        if (val == true) {
            this.dispatchEvent(ModuleCommand.CLOSE_GUIDE_GIRL_DIALOG)
        }

    }
    /**隐藏指引对话界面*/
    public get isHideGirl(): boolean {

        if (this.isHideGuide == true) {
            return true;
        }

        if (this._hideGuideUIDic.size > 0) {
            return true;
        }
        return this._isHideGirl;
    }

    /**启动指引 (登陆界面后的 loading界面关闭时启动)*/
    public set isStartGuide(val: boolean) {
        this._isStartGuide = val;
        if (val == true) {
            this.startGuide();
        }
    }

    public get isStartGuide(): boolean {
        return this._isStartGuide;
    }

    public get isHideGuide(): boolean {
        /**指引还没有启动 */
        if (this._isStartGuide == false) {
            return true;
        }
        let size: number = this._hideGuideUIDic.size;
        for (const key of this._hideGuideUIDic.keys()) {
            if (key == "FightDialog" && LayerManager.topUI == true) {
                size--;
                break;
            }
        }
        return size > 0;
    }

    public isFinishMission(guide_id: number): boolean {
        // if (GuideMgr.ins.isOpenGuide == false) {
        //     return true;
        // }
        return this.finishMissions.indexOf(guide_id) > -1;
    }

    /**前端正在运行的指引ID */
    public set curGuideId(val: number) {
        GuideConst.log(window.iLang.L2_SHE_ZHI_ZHI_YIN_ch10.il(), val);
        if (GuideConst.zhaoYunMount == val) {
            for (const item of HeroEquipManager.instance.equipHeroMap.values()) {
                if (item.equip_map.has(ItemMacro.ITEM_KIND_BING_FU)) {
                    this.dispatchEvent(ModuleCommand.GUIDE_SKIP, [GuideConst.zhaoYunMount, 2]);
                    val = 0;
                }
            }
        }
        // this.delCacheGuideId(val);
        GuideConst._curGuideId = val;
        this._curGuideId = val;
        this._curStep = 0;
        this._curGuideHelper = [];

        if(val == 0){
            this.granchTempData = null;
        }
    }

    public get curGuideId(): number {
        if (this.cmdCloseGuide == true) {
            return 0;
        }
        return this._curGuideId;
    }

    /**设置支线引导ID (弱引导)*/
    public setGranchGuideId(val: number, isForce: boolean = false, tempData:any = null): boolean {

        if (GuideConst.checkWarGuide(val) == false) {
            if (DataCenter.myLevel > GuideConst.level && DataCenter.myLevel > 5) {
                //return false;
            }
        }

        if (val == 0) {
            return;
        }

        if (this.curGuideId > 0) {
            return false;
        }

        let cfg: cfg_guide_helper = ConfigManager.getGuideHelper(this._serverGuideId);
        if (cfg != null && cfg.is_branch == 0 && GuideConst.isWarFight == false) {
            return;
        }
        
        let mission_cfg: cfg_guide_mission = ConfigManager.getGuideVerCfg("cfg_guide_mission").get(val);
        if (mission_cfg != null && (DataCenter.myLevel < mission_cfg.min_level)) {
            return ;
        }

        if (this._serverGuideId == val && isForce == false && this._curStep != 0) {
            return false;
        }

        switch (val) {
            case GuideConst._failureID1:
            case GuideConst._failureID2:
            case GuideConst._failureID3:
                if (LocalStorageUtil.CheckUserLocalStorage(val.toString(), 1) == true) {
                    return;
                } else {
                    LocalStorageUtil.setUserLocalStorage(val.toString(), "true", 1);
                }
                break;
            default:
                break;
        }

        GuideConst.log(window.iLang.L2_QI_DONG_ZHI_XIAN_ZHI_YIN_ch05.il(), val);

        if (this.curGuideId == 0 || this._curStep == 0) {
            this.curGuideId = val;
            this.granchTempData = tempData;
            if (this.guideTipList.size > 0) {
                return;
            }
            if (this.isStartGuide == true) {
                this.startGuide();
            }
        }

        return true;
    }

    /**清理支线引导 */
    public clearGranchGuide(force: boolean = false): void {
        let cfg: cfg_guide_helper = ConfigManager.getGuideHelper(this.curGuideId);
        if (cfg != null && (cfg.is_branch == 1 || force)) {
            this.clear();
            this.dispatchEvent(ModuleCommand.CLOSE_GUIDE_GIRL_DIALOG);
            let tmpe: GuideTipIns[] = Array.from(this.guideTipList.values()).concat();
            tmpe.forEach(item => {
                if (item != null && item.isBranch == true) {
                    this.guideTipList.delete(item.guideTarget);
                    item.visible = false;
                    item.destroy();
                }
            });
            return;
        }
    }

    /**后端实际开启的指引ID */
    public set serverGuideId(val: number) {

        //先屏蔽
        if(TdMainDataCenter.ins.isOpenServerGuide(val) == false){
            return;
        }

        if(StoryGameMgr.ins.isServerGuide() == false){
            //屏蔽后端引导
            return;
        }
        if (this.curGuideId != 0 && (this._serverGuideId == val || val == this.curGuideId)) {
            return;
        }

        let cfg: cfg_guide_helper = ConfigManager.getGuideHelper(val);
        if (val > 0) {
            if (cfg != null) {
                /***登陆才启动该指引 */
                if (cfg.startup == 1 && this.isStartGuide == true) {
                    return;
                }
                /**重新登陆跳过该指引 */
                if (cfg.startup == 2 && this.isStartGuide == false) {
                    GuideConst.log(window.iLang.L2_CHONG_XIN_DENG_LU_TIAO_GUO_GAI_ZHI_YIN_ch10.il(), cfg.guide_id);
                    this.dispatchEvent(ModuleCommand.GUIDE_SKIP, [cfg.guide_id, 1]);
                    return;
                }
            }
        }
        if (val > 0 && GuideConst.isWarFight == false) {
            this.clearGranchGuide();
        }
        this._serverGuideId = val;
        // this._cacheGuideId.push(val);

        if (GuideConst.isWarFight == true) {
            return;
        }


        if (this.curGuideId == 0 && this._curStep == 0) {
            this.curGuideId = val;
            this.granchTempData = null;
            this._serverGuideId = 0;
            let tmepIndex: number = 0;
            //部分指引在线启动时跳过startup = 3 类型
            if (this._curGuideId > 0 && LoadingDataCenter.instance.isFirstEnterMap == false) {
                for (let index = 0; index < this.curGuideHelper.length; index++) {
                    const element = this.curGuideHelper[index];
                    if (element.startup != 3) {
                        tmepIndex = element.step;
                        break;
                    }
                }
            }
            this._curStep = tmepIndex;

            if (this.guideTipList.size > 0) {
                return;
            }
            if (this.isStartGuide == true) {
                this.startGuide();
            }
        }
    }

    /**移除缓存的指引 */
    // public delCacheGuideId(val: number): void {
    //     for (let index = 0; index < this._cacheGuideId.length; index++) {
    //         if (this._cacheGuideId[index] == val) {
    //             this._cacheGuideId.splice(index, 1);
    //             break;
    //         }
    //     }
    // }

    public get guideHandCount(): number {
        return this.guideTipList.size;
    }

    /**启动新指引 */
    public startGuide(): void {
        GuideConst.log(window.iLang.L2_DANG_QIAN_ZHI_YIN_ch10.il(), this.curGuideId);
        if (this.curGuideId <= 0) {
            return;
        }

        let cfg: cfg_guide_helper = this.getCurCfg();
        if (cfg != null) {
            this.dispatchEvent(ModuleCommand.GUIDE_CHECK);
        } 
        this.dispatchEvent(ModuleCommand.GUIDE_MAIN_TAB);
    }

    public dispatchEvent(type: string, data: any = null): void {
        ILaya.timer.callLater(this, function (): void {
            DispatchManager.dispatchEvent(type, data);
        });
    }

    public get serverGuideId(): number {
        // if (this._cacheGuideId.length > 0) {
        //     return this._cacheGuideId[0];
        // }
        return this._serverGuideId;
    }

    public getCurCfg(): cfg_guide_helper {
        let arr: cfg_guide_helper[] = this.curGuideHelper;
        if (this._curGuideId > 0 && arr) {
            if (arr.length > this._curStep) {
                return arr[this._curStep];
            }
        }
        return null;
    }

    public get maxStep(): number {
        return this.curGuideHelper.length;
    }

    private count: number = 0;
    public get isOpenGuide(): boolean {
        if (!GlobalConfig.enableGuide) return false;
        if (LocalStorageUtil.CheckLocalStorage(CmdConsts.CMD_GM_GUIDE_CLOSE) == true) {
            if (this.count < 3) {
                TipsUtil.showTips(window.iLang.L2_ZHI_YIN_YI_JING_GUAN_BI.il());
                GuideMgr.ins.cmdCloseGuide = true;
            }
            this.count++;
            return false;
        }
        let reviewCfg = ConfigManager.cfg_guide_reviewCache.get(GlobalConfig.PlatName);
        if(GlobalConfig.isReviewStaus && reviewCfg != undefined){
            return true;
        }
        if (StoryGameMgr.ins.isServerGuide() == false && (GlobalConfig.is_ios_shenhe == true || !GlobalConfig.showRecharge)) {
            return false;
        }

        if (this.isHideGuide == true) {
            return false;
        }

        if (this.curGuideId > 0 && this.curGuideHelper) {
            //   return true;
        }
        return true;
    }

    public get curStep(): number {
        return this._curStep;
    }
    public set curStep(val: number) {
        GuideConst.log(window.iLang.L2_BU_ZHOU_BIAN_HUA_ch05.il(), this.curGuideId, val);
        if (val >= this.curGuideHelper.length) {
            this._curStep = 0;
        } else {
            this._curStep = val;
        }

        this.dispatchEvent(ModuleCommand.GUIDE_SEND_STEP, {guide_id: this.curGuideId, step: this._curStep});
        DispatchManager.dispatchEvent(ModuleCommand.GUIDE_SHIP_CHANGE);
    }

    /**当前新手指引 */
    public get curGuideHelper(): cfg_guide_helper[] {

        if (this._curGuideHelper.length > 0) {
            return this._curGuideHelper;
        }

        let arr: cfg_guide_helper[] = ConfigManager.getGuideVerCfg("cfg_guide_helper").get(this.curGuideId);
        if (arr != null) {
            arr.sort(function (a: cfg_guide_helper, b: cfg_guide_helper): number {
                return a.step - b.step;
            });
        } else {
            arr = [];
        }
        this._curGuideHelper = arr;
        return this._curGuideHelper;
    }




    /**菜单上的指引
         * @param sysId 菜单id(系统ID)
         * @param guideTarget 指引目标
         * @param posX x轴位置
         * @param posY y轴位置
         * @param text 指引描述
         * @param dic 指引描述显示的方向
         * @param isMask true 表有遮罩
         * @param isShowTxt true 描述资源描述
         * @param isAddGlobal true 添加到全局对象 flase 添加到自己身上
         * **/
    public ShowMainUi(cfg: cfg_guide_helper, sysId: number, desc: String = window.iLang.L2_DIAN_JI_DA_KAI_JIE_MIAN.il()): void {
        DispatchManager.dispatchEvent(ModuleCommand.SHOW_MAINUI_GUIDE, { cfg: cfg, id: sysId, text: desc });
    }

    /**指引手势
     * @param guideTarget 指引目标
     * @param posX x轴位置
     * @param posY y轴位置
     * @param text 指引描述
     * @param dic 指引描述显示的方向
     * @param isMask true 表有遮罩
     * @param isShowTxt true 描述资源描述
     * @param isAddGlobal true 添加到全局对象 flase 添加到自己身上
     * ***/
    public showHand(cfg: cfg_guide_helper, guideTarget: Sprite, text: string = window.iLang.L2_XIN_SHOU_ZHI_YIN.il(), offsetX: number = 0, offsetY: number = 0, clickCallBack: Handler = null): GuideTipIns {

        if (this.isOpenGuide == false) {
            return;
        }
        //保持唯一指引
        let tmpe: GuideTipIns[] = Array.from(this.guideTipList.values()).concat();
        tmpe.forEach(item => {
            if (guideTarget && item.name != guideTarget.name && item.isClear == false) {
                this.removeHand(item.guideTarget);
            }
        });
        tmpe = [];  
        if (guideTarget == null || guideTarget.destroyed) {
            return null;
        }
        var guideTip: GuideTipIns = this.guideTipList.get(guideTarget);
        if (guideTip) {
            guideTip.visible = true;
        }

        for (const iterator of this.guideTipList.values()) {
            if (iterator.guideTarget == guideTarget && iterator.isClear == false) {
                return;
            }
        }

        this._curCfg = cfg;

        GuideConst.log(window.iLang.L2_BU_ZHOU_ch05_CH81_CH81_CH81_CH81_CH81_CH81_CH81_CH81_CH81.il(), this._curGuideId, this._curCfg.guide_name, this._curCfg.guide_id, this._curCfg.step, this.curGuideHelper.length - 1, this._curCfg.btn_name);

        if (cfg != null && cfg.step == 0 && cfg.guide_id == GuideConst.timingId && MainUIManager.instance.selectTabIndex == EMainUIBottomIndex.index_4_guaji) {
            GuideMgr.ins.nextGuide();
            this.dispatchEvent(ModuleCommand.GUIDE_CHECK);
            return;
        }

        if (guideTip == null) {
            guideTip = this.getTipIns();
            guideTip.name = guideTarget.name;
            //this.dispatchEvent(ModuleCommand.ADD_DIALOG_MANAGER, guideTip);
            Dialog.manager.addChild(guideTip);
            this.guideTipList.set(guideTarget, guideTip);
        }

        if (guideTip.destroyed) {
            this.removeHand(guideTarget);
            return null;
        }
        
        if(guideTarget.width == 0 && guideTarget.height == 0) {
            guideTarget.once(Event.LOADED, guideTip, () => {
                guideTip.setData(cfg, guideTarget, offsetX, offsetY, text, clickCallBack);
            });
        }
        else {
            guideTip.setData(cfg, guideTarget, offsetX, offsetY, text, clickCallBack);
        }
        guideTip.visible = true;
        this.isGuideing = true;
        return guideTip;
    }

    /**移除手势**/
    public removeHand(guideTarget: Sprite): void {
        var guideTip: GuideTipIns = this.guideTipList.get(guideTarget);
        if (guideTip != null) {
            this.guideTipList.delete(guideTarget);
            guideTip.visible = false;
            guideTip.destroy();
        }
    }

    public removeAllHand(): void {
        //保持唯一指引
        let tmpe: GuideTipIns[] = Array.from(this.guideTipList.values()).concat();
        tmpe.forEach(item => {
            if (item.isClear == false) {
                this.removeHand(item.guideTarget);
            }
        });
    }

    public hideAllHand(): void {
        this.guideTipList.forEach(guideTip => {
            if (guideTip != null) {
                guideTip.visible = false;
            }
        });
    }

    public hideHand(guideTar: Sprite): void {
        var guideTip: GuideTipIns = this.guideTipList.get(guideTar);
        if (guideTip != null) {
            guideTip.visible = false;
        }
    }

    public isCheckUIName(uiName: string): boolean {
        let cfg: cfg_guide_helper = this.getCurCfg();
        if (cfg != null && cfg.ui_name == uiName) {
            return true;
        }
        return false;
    }

    public checkHand(guideTarget: Sprite): Boolean {
        return this.guideTipList.get(guideTarget) != null;
    }
    public getGuideTip(guideTarget: Sprite): GuideTipIns {
        return this.guideTipList.get(guideTarget);
    }

    public getUINameGuide(btn_name: string, parent: Node): Node {
        let arr: string[] = btn_name.split('_');
        if (arr.length > 0) {
            return this.getNode(arr, parent);
        }
        return null;
    }

    private getNode(arr: string[], parent: Node): Node {

        if (parent == null) {
            return null;
        }
        if (arr.length > 0) {
            let nodeName: string = arr[0];
            arr.splice(0, 1);
            let tmep: Node = parent.getChildByName(nodeName);
            if (arr.length > 0) {
                return this.getNode(arr, tmep);
            }
            return tmep;
        }
        return null;
    }

    public nextGuide(): void {
        if (this._curCfg) {
            let arr: cfg_guide_helper[] = this.curGuideHelper;
            if (this._curStep < arr.length - 1) {
                this.curStep++;
                this._curCfg = arr[this._curStep];
                //this._curStep = this._curCfg.step;
                GuideConst.log(window.iLang.L2_XIA_BU_ch05_CH81_CH81_CH81_CH81_CH81_CH81_CH81_CH81_CH81.il(), this._curGuideId, this._curCfg.guide_name, this._curCfg.guide_id, this._curCfg.step, arr.length - 1, this._curCfg.ui_name, this._curCfg.btn_name);
            } else {
                this.endGuide();
            }
        }
    }
    public endGuide(){
        this.dispatchEvent(ModuleCommand.GUIDE_END_ID, this.curGuideId);
        //this.delCacheGuideId(this.curGuideId);
        GuideConst.log(window.iLang.L2_ZHI_YIN_JIE_SHU_ch05.il(), this.curGuideId);
        if(GuideMgr.ins.curGuideId == GuideConst.one_guide_id){
            this.dispatchEvent(ModuleCommand.REQUEST_LOGIN_CLIENT_CLICK_TOS, 13);
        }

        this.granchTempData = null;

        if (this._serverGuideId > 0 && this._serverGuideId != this.curGuideId) {
            this.curGuideId = this._serverGuideId;
            this._serverGuideId = 0;
            this._curCfg = null;
            this._curGuideHelper = [];
            this.startGuide();
        } else {
            //通知后端，完成当前指引
            let guideId = this._curCfg?.guide_id || this.curGuideId;
            DispatchManager.dispatchEvent(ModuleCommand.GUIDE_SKIP, [guideId, 1]);
            //this.delRunGuideId(this.curGuideId);
            this.curGuideId = 0;
            GuideMgr.ins.clear();
            GuideConst.log(window.iLang.L2_MEI_YOU_XIN_ZHI_YIN_ch10.il(), this.curGuideId);
        }
    }
    public skipGuide(guideId: number, msg: string = window.iLang.L2_ZHI_YIN_JIE_SHU_ch05.il()): void {
        //结束剧情战斗
        this.dispatchEvent(ModuleCommand.GUIDE_SKIP, [guideId, 1]);
        GuideConst.log(msg, guideId);
        this.dispatchEvent(ModuleCommand.GUIDE_END_ID, guideId);
        GuideMgr.ins.finishMissions.push(guideId);
    }

    public clear(): void {
        if (this.serverGuideId == this.curGuideId) {
            this.serverGuideId = 0;
        }
        this.curGuideId = 0;
        this._curCfg = null;
        this._curGuideHelper = [];
    }

    public checkTargetVisible(guideTarget: Sprite, cfg: cfg_guide_helper, isTips: boolean) {
        let ret = !!guideTarget;

        //这个不检查
        if (this.checkIsBottomOrTopLayer(cfg)){

        }
        else if (guideTarget && guideTarget.destroyed == false && UIUtil.checkSpriteVisible(guideTarget, 1) == false) {

            ret = false;
            if (isTips) {
                let tips1 = window.iLang.L2_JING_GAO_ch05_ZHI_YIN_DUI_XIANG_BU_KE_JIAN_1_CH81_CH81_CH81_ch11_ZHI_YIN_ID.il([cfg.guide_id,cfg.step,cfg.ui_name,cfg.btn_name,cfg.is_player]);
                GuideConst.warn(tips1);

                if (GlobalConfig.IsDebug) {
                    TipsUtil.showTips(tips1);
                } else {
                    // SentryUtils.sendWarningMsg(tips1);
                    //新版本用协议通知后端
                }
            }

        }
        return ret;
    }
    private _curGuideUiPanel:any;
    /**当前进行引导的界面 */
    set curGuideUiPanel(uiPanel){
        this._curGuideUiPanel = uiPanel;
        this.dispatchEvent(ModuleCommand.CHANGE_GUIDE_UI_PANEL,uiPanel);
    }
    get curGuideUiPanel(){
        return this._curGuideUiPanel;
    }

    public isFrontStepGuide(targetName:string, step:number): boolean {
        let arr: cfg_guide_helper[] = this.curGuideHelper;
        let frontStep = step - 1;
        if(frontStep < 0) {
            return false;
        }
        let frontStepCfg = arr[frontStep];
        //w6d容错
        if(!frontStepCfg){
            return false;
        }
        return frontStepCfg.btn_name.indexOf(targetName) != -1;
    }

    public isBeast(): boolean {
        return CfgCacheMapMgr.cfg_beast_platformCache.get(GlobalConfig.PlatName) != undefined;
    }
}
