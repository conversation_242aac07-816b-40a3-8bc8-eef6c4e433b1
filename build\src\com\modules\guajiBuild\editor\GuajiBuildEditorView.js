import { Laya } from "Laya";
import { Event } from "laya/events/Event";
import { Image } from "laya/ui/Image";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { MathUtil2 } from "../../../util/MathUtil2";
import { TipsUtil } from "../../../util/TipsUtil";
import { DragAndDropScript } from "../../baseModules/scripts/DragAndDropScript";
import { Point } from "laya/maths/Point";
import { com } from "../../../ui/layaMaxUI";
import { GuajiBuildPathPoint } from "./GuajiBuildPathPoint";
import { GuajiBuildPathPointVo, EGuajiBuildPathPointType } from "../vo/GuajiBuildPathPointVo";
import { GuajiBuildEditorVo } from "../vo/GuajiBuildEditorVo";
import { GuajiBuildWorker } from "../game/GuajiBuildWorker";
import { LayerManager } from "../../../managers/LayerManager";
import { GuajiBuildWorkerPoint } from "./GuajiBuildWorkerPoint";
import { ESkeletonType } from "../../baseModules/skeleton/SkeletonData";
import { GuaJiBuildDataCenter } from "../data/GuaJiBuildDataCenter";
export class GuajiBuildEditorView extends com.ui.res.guaJiBuild.GuajiBuildEditorViewUI {
    constructor() {
        super();
        /**移动类型,默认 0巡逻 1跟随路点 2出生点*/
        this.moveType = 0;
        this.isShowWorker = true;
        this.pathItemGroupMap = new Map();
        this.workerItemGroupMap = new Map();
        this.workerListMap = new Map();
        this.currBuildingType = 0;
        this.onSelPoint = null;
        this.worker_type_list = [EGuajiBuildPathPointType.GO_AND_BACK + "_" + "路点寻路",
            EGuajiBuildPathPointType.STAND_POSITION + "_" + "固定工位"];
    }
    get visible() {
        return super.visible;
    }
    set visible(value) {
        super.visible = value;
        this._setViewVisible();
    }
    _setViewVisible() {
        if (!this.map) {
            return;
        }
        if (!this.currBuildingType) {
            return;
        }
        let cfgVo = GuaJiBuildDataCenter.instance.getBuildingCfgVo(this.currBuildingType);
        let visible = this.visible;
        let pathItemVisible = visible; //&& cfgVo.worker_type == EGuajiBuildWorkerType.GO_AND_BACK;
        let workerItemVisible = visible; //&& cfgVo.worker_type == EGuajiBuildWorkerType.FIXED_POINT;
        if (this.spPathPointConnect) {
            this.spPathPointConnect.visible = visible;
        }
        this.pathItemGroupMap.forEach(list => {
            list.forEach(item => {
                item.visible = pathItemVisible;
            });
        });
        this.workerItemGroupMap.forEach(list => {
            list.forEach(item => {
                item.visible = workerItemVisible;
            });
        });
        this.workerListMap.forEach((list, point) => {
            let v = visible && this.isShowWorker; //visible && cfgVo.worker_type == point.vo.type;
            list.forEach(worker => {
                worker.visible = v;
            });
        });
        if (this.dialog) {
            this.dialog.buildItemMap.forEach((item) => {
                item.showDebugRect = visible && this.boxProps.visible;
                item.onEditorDebug(visible);
            });
            if (!visible && this.map) {
                let miscVo = this.getBuildingMiscVo();
                this.map.scale(miscVo.map_scale, miscVo.map_scale);
            }
        }
    }
    updateFrame(interval) {
        this.workerListMap.forEach(list => {
            list.forEach(worker => {
                worker.updateFrame(interval);
            });
        });
    }
    init(dialog, map) {
        this.dialog = dialog;
        this.map = map;
        this.initScene();
        this._addClick();
    }
    initUI() {
        super.initUI();
        this.mouseThrough = true;
        this.boxFuncPanel.mouseThrough = true;
        this.zOrder = LayerManager.STAGE_ZORDE_TOPEST + 1;
        this.addComponent(DragAndDropScript);
        this.boxProps.addComponent(DragAndDropScript);
    }
    _addClick() {
        this.addOnClick(this, this.btnDebug, () => {
            this.boxProps.visible = !this.boxProps.visible;
            if (this.dialog) {
                this.dialog.buildItemMap.forEach(item => {
                    item.showDebugRect = this.boxProps.visible;
                });
            }
        });
        this.addOnClick(this, this.btnAddPoint, this.onAddPathPoint);
        this.addOnClick(this, this.btnDelPoint, this.onDelPathPoint);
        this.addOnClick(this, this.btnStartPoint, this.onStartPathPoint);
        this.addOnClick(this, this.btnAddPointGroup, this.onAddPointGroup);
        this.addOnClick(this, this.btnDelPointGroup, this.onDelPointGroup);
        this.addOnClick(this, this.btnSave, () => {
            this.onSavePathPoint(true);
        });
        this.addOnClick(this, this.btnShowRange, this.onShowRange);
        this.addOnClick(this, this.btnShowWorker, () => {
            this.isShowWorker = !this.isShowWorker;
            this._setViewVisible();
        });
        let id_name_list = [];
        CfgCacheMapMgr.cfg_buildingCache.get_all().forEach(cfg => {
            id_name_list.push(cfg.type + "_" + cfg.name);
        });
        this.cbBuilding.visibleNum = 100;
        this.cbBuilding.labels = id_name_list.join(",");
        this.cbBuilding.on(Event.CHANGE, this, this.onBuildingChange);
        this.cbBuilding.selectedIndex = 0;
        let worker_type_list = this.worker_type_list;
        this.cbPropWorkType.visibleNum = 100;
        this.cbPropWorkType.labels = worker_type_list.join(",");
        this.cbPropWorkType.on(Event.CHANGE, this, this.onWorkTypeChange);
        this.cbPropWorkType.selectedIndex = 0;
        this.cbWorkerFlip.on(Event.CHANGE, this, this.onWorkerFlipChange);
        this.inputScale.on(Event.INPUT, this, this.onMapProps);
        this.inputMapWidth.on(Event.INPUT, this, this.onMapProps);
        this.inputMapHeight.on(Event.INPUT, this, this.onMapProps);
        this.inputPropWorkerScale.on(Event.INPUT, this, this.onInput);
        this.inputPropWorkerSkName.on(Event.INPUT, this, this.onInput);
        this.inputPointRange.on(Event.INPUT, this, this.onInput);
        this.inputPropBuildingScale.on(Event.INPUT, this, this.onInput);
        this.inputPropBuildingX.on(Event.INPUT, this, this.onInput);
        this.inputPropBuildingY.on(Event.INPUT, this, this.onInput);
        this.inputPropBuildingItemX.on(Event.INPUT, this, this.onInput);
        this.inputPropBuildingItemY.on(Event.INPUT, this, this.onInput);
        this.inputPropBuildingScale.on(Event.INPUT, this, this.onInput);
        this.inputPropBuildingSkName.on(Event.INPUT, this, this.onInput);
        /**----------------------------路点和武将出生点 end------------------------------------ */
    }
    onMapProps() {
        let miscVo = this.getBuildingMiscVo();
        miscVo.map_scale = this.getTextInputNumber(this.inputScale, miscVo.map_scale);
        miscVo.map_width = this.getTextInputNumber(this.inputMapWidth, miscVo.map_width);
        miscVo.map_height = this.getTextInputNumber(this.inputMapHeight, miscVo.map_height);
        this.map.scale(miscVo.map_scale, miscVo.map_scale);
    }
    onInput() {
        let cfgVo = this.getCurrBuildingCfgVo();
        let miscVo = this.getBuildingMiscVo();
        miscVo.map_scale = this.getTextInputNumber(this.inputScale, miscVo.map_scale);
        miscVo.map_width = this.getTextInputNumber(this.inputMapWidth, miscVo.map_width);
        miscVo.map_height = this.getTextInputNumber(this.inputMapHeight, miscVo.map_height);
        cfgVo.building_scale = this.getTextInputNumber(this.inputPropBuildingScale, cfgVo.building_scale);
        cfgVo.building_x = this.getTextInputNumber(this.inputPropBuildingX, cfgVo.building_x);
        cfgVo.building_y = this.getTextInputNumber(this.inputPropBuildingY, cfgVo.building_y);
        cfgVo.building_item_x = this.getTextInputNumber(this.inputPropBuildingItemX, cfgVo.building_item_x);
        cfgVo.building_item_y = this.getTextInputNumber(this.inputPropBuildingItemY, cfgVo.building_item_y);
        cfgVo.work_type = this.getComboBoxSelId(this.cbPropWorkType);
        cfgVo.worker_sk = this.inputPropWorkerSkName.text;
        cfgVo.worker_scale = this.getTextInputNumber(this.inputPropWorkerScale, cfgVo.worker_scale);
        cfgVo.building_sk = this.inputPropBuildingSkName.text;
        // let currWokerPoint = this.getCurrSelectPoint(EGuajiBuildPathPointType.STAND_POSITION);
        // if (currWokerPoint) {
        //     currWokerPoint.vo.worker_flip_x = this.cbWorkerFlip.selected;
        // }
        let currPathPoint = this.getCurrSelectPoint(EGuajiBuildPathPointType.GO_AND_BACK);
        if (currPathPoint) {
            currPathPoint.vo.worker_flip_x = this.cbWorkerFlip.selected;
            currPathPoint.vo.range = this.getTextInputNumber(this.inputPointRange, currPathPoint.vo.range);
        }
        this.resetView();
    }
    initView() {
        let cfgVo = this.getCurrBuildingCfgVo();
        let miscVo = this.getBuildingMiscVo();
        this.inputScale.text = miscVo.map_scale.toString();
        this.inputMapWidth.text = miscVo.map_width.toString();
        this.inputMapHeight.text = miscVo.map_height.toString();
        this.lbPropBuildingName.text = "当前建筑: " + this.cbBuilding.selectedLabel;
        this.cbPropWorkType.selectedLabel = this.worker_type_list[cfgVo.work_type - 1];
        this.inputPropBuildingSkName.text = cfgVo.building_sk;
        this.inputPropBuildingScale.text = cfgVo.building_scale.toString();
        this.inputPropBuildingX.text = cfgVo.building_x.toString();
        this.inputPropBuildingY.text = cfgVo.building_y.toString();
        this.inputPropBuildingItemX.text = cfgVo.building_item_x.toString();
        this.inputPropBuildingItemY.text = cfgVo.building_item_y.toString();
        this.inputPropWorkerSkName.text = cfgVo.worker_sk;
        this.inputPropWorkerScale.text = cfgVo.worker_scale.toString();
        // this.map.scale(miscVo.map_scale, miscVo.map_scale);
        this.resetView();
    }
    resetView() {
        let cfgVo = this.getCurrBuildingCfgVo();
        let miscVo = this.getBuildingMiscVo();
        this.dialog.buildItemMap.forEach((item) => {
            item.initShow(item.vo, this.map);
            item.onEditorDebug(this.visible);
        });
        let currPathPoint = this.getCurrSelectPoint(EGuajiBuildPathPointType.GO_AND_BACK);
        if (currPathPoint) {
            this.inputPointRange.text = currPathPoint.vo.range + "";
            currPathPoint.setPointRange(currPathPoint.vo.range);
            // currPathPoint.vo.range = this.getTextInputNumber(this.inputPointRange, currPathPoint.vo.range);
        }
        let scale = cfgVo.worker_scale || 1;
        this.workerListMap.forEach(list => {
            list.forEach(worker => {
                let scaleX = worker.sk.scaleX < 0 ? -1 : 1;
                worker.setSk(cfgVo.worker_sk, ESkeletonType.MODEL_ACTION);
                // worker.setSkScale(scale * scaleX, scale);
            });
        });
    }
    getTextInputNumber(input, defNum) {
        // let text = input.text;
        // if (text == "") {
        //     return defNum;
        // } else {
        //     if (isNaN(+text)) {
        //         return defNum;
        //     }
        //     return +text;
        // }
        if (isNaN(+input.text)) {
            return defNum;
        }
        return +input.text;
    }
    getComboBoxSelId(cb) {
        let label = cb.selectedLabel;
        if (!label) {
            return 0;
        }
        let labelList = label.split("_");
        return +labelList[0] || 0;
    }
    getCurrBuildingCfgVo() {
        let buildingType = this.getComboBoxSelId(this.cbBuilding) || 1;
        return GuaJiBuildDataCenter.instance.getBuildingCfgVo(buildingType);
    }
    getBuildingMiscVo() {
        return GuaJiBuildDataCenter.instance.saveMiscVoMap;
    }
    onWorkerFlipChange() {
        let currWokerPoint = this.getCurrSelectPoint(EGuajiBuildPathPointType.STAND_POSITION);
        if (currWokerPoint) {
            currWokerPoint.vo.worker_flip_x = this.cbWorkerFlip.selected;
        }
    }
    onWorkTypeChange() {
        let cfgVo = this.getCurrBuildingCfgVo();
        let miscVo = this.getBuildingMiscVo();
        cfgVo.work_type = this.getComboBoxSelId(this.cbPropWorkType);
        // this.initView();
    }
    onBuildingChange() {
        this.currBuildingType = this.getComboBoxSelId(this.cbBuilding);
        this.initScene();
    }
    initScene() {
        let type = this.currBuildingType;
        //删除所有路点,武将出生点
        this.removeAllPathPoint();
        //初始化路点
        let cfg = CfgCacheMapMgr.cfg_buildingCache.get(type);
        if (cfg) {
            this.initCfgVo(type, EGuajiBuildPathPointType.GO_AND_BACK);
            this.initCfgVo(type, EGuajiBuildPathPointType.STAND_POSITION);
            let heroPointList = this.workerItemGroupMap.get(0);
            // this.onInputMapBornOffset();
            // this.onBornPlanChange();
        }
        this.initView();
    }
    checkBornPlan() {
    }
    initCfgVo(buildingType, type) {
        var pointList = [];
        var pointMap = GuajiBuildPathPointVo.parseCfgPathPoint(buildingType, type);
        for (let voList of pointMap.values()) {
            pointList.push(...voList);
        }
        pointList.forEach(item => {
            this.addPathPoint(item.index, item.targetX, item.targetY, type, item.groupIndex, item.range);
        });
    }
    onRoleCB() {
    }
    removeAllPathPoint() {
        let allPointList = [];
        this.pathItemGroupMap.forEach(list => {
            allPointList.push(...list);
        });
        this.workerItemGroupMap.forEach(list => {
            allPointList.push(...list);
        });
        for (let i = allPointList.length - 1; i >= 0; i--) {
            this.doDelPathPoint(allPointList[i]);
        }
        this.pathItemGroupMap.clear();
        this.workerItemGroupMap.clear();
    }
    getGroupMapByType(type) {
        switch (type) {
            case EGuajiBuildPathPointType.GO_AND_BACK:
                return this.pathItemGroupMap;
            case EGuajiBuildPathPointType.STAND_POSITION:
                return this.workerItemGroupMap;
        }
    }
    getMaxIndex(type) {
        let maxIndex = 0;
        let currList = this.getCurrSelectPointList(type);
        currList.forEach(item => {
            if (item.pointIndex > maxIndex) {
                maxIndex = item.pointIndex;
            }
        });
        return maxIndex;
    }
    getMapCenterPos() {
        // let x = this.dialog.width / 2 + MathUtil2.rangeInt(-100, 100);
        // let y = this.dialog.height / 2 + MathUtil2.rangeInt(-100, 100);
        // return Point.create().setTo(x, y);
        return Point.create().setTo(800 + MathUtil2.rangeInt(-100, 100), 1000 + MathUtil2.rangeInt(-100, 100));
    }
    addWoker(point) {
        if (point.vo.type == EGuajiBuildPathPointType.GO_AND_BACK && point.vo.index != 0) {
            return;
        }
        let configVo = this.getCurrBuildingCfgVo();
        // if(point.vo.type == EGuajiBuildPathPointType.STAND_POSITION){
        //     var skName = "056banshouren";
        // }else{
        //     var skName = "063huangjiaqishi";
        // }
        // var skName = "063huangjiaqishi";
        // var skName = "fei_li_tu";
        var skName = configVo.worker_sk;
        let flipX = point.vo.worker_flip_x;
        let worker = new GuajiBuildWorker();
        worker.init();
        worker.setSk(skName, ESkeletonType.MODEL_ACTION);
        worker.pointVo = point.vo;
        worker.editorVo = configVo;
        worker.zOrder = point.zOrder - 1;
        worker.visible = this.isShowWorker;
        // worker.work_type = configVo.work_type;
        worker.pos(point.vo.targetX, point.vo.targetY);
        worker.setSkScale(configVo.worker_scale);
        let buildItem = this.dialog.buildItemMap.get(configVo.buidingType);
        this.map.addChild(worker);
        let list = this.workerListMap.get(point) || [];
        list.push(worker);
        this.workerListMap.set(point, list);
        this._setViewVisible();
    }
    removeWorker(point) {
        let list = this.workerListMap.get(point) || [];
        for (let i = list.length - 1; i >= 0; i--) {
            let worker = list[i];
            worker.destroy();
        }
        this.workerListMap.delete(point);
        this._setViewVisible();
    }
    onAddPathPoint(e) {
        let target = e.target;
        // let addType = target == this.dialog.btnAddPoint ? EBuildingPathPointType.PATH : EBuildingPathPointType.HERO;
        if (target == this.btnAddPoint) {
            var addType = EGuajiBuildPathPointType.GO_AND_BACK;
        }
        // else if (target == this.btnAddWorker) {
        //     var addType = EGuajiBuildPathPointType.STAND_POSITION;
        // }
        let maxIndex = this.getMaxIndex(addType);
        maxIndex += 1;
        let point = this.getMapCenterPos();
        let pathPoint = this.addPathPoint(maxIndex, point.x, point.y, addType);
    }
    onAddPointGroup(e) {
        let target = e.target;
        if (target == this.btnAddPointGroup) {
            var addType = EGuajiBuildPathPointType.GO_AND_BACK;
        }
        let groupMap = this.getGroupMapByType(addType);
        let newGroupIndex = groupMap.size;
        for (let i = 0; i < groupMap.size; i++) {
            if (!groupMap.get(i)) {
                newGroupIndex = i;
                break;
            }
        }
        let pos = this.getMapCenterPos();
        this.addPathPoint(0, pos.x, pos.y, addType, newGroupIndex);
    }
    onDelPointGroup(e) {
        let target = e.target;
        if (target == this.btnDelPointGroup) {
            var addType = EGuajiBuildPathPointType.GO_AND_BACK;
        }
        let currList = this.getCurrSelectPointList(addType);
        for (let i = currList.length - 1; i >= 0; i--) {
            this.doDelPathPoint(currList[i]);
        }
    }
    addPathPoint(index, x, y, addType, groupIndex = -1, range = 0) {
        if (addType == EGuajiBuildPathPointType.GO_AND_BACK) {
            var point = new GuajiBuildPathPoint();
        }
        else if (addType == EGuajiBuildPathPointType.STAND_POSITION) {
            var point = new GuajiBuildWorkerPoint();
        }
        let configVo = this.getCurrBuildingCfgVo();
        let groupMap = this.getGroupMapByType(addType);
        let currGroup = groupIndex >= 0 ? groupIndex : this.getCurrSelectPointGroupIndex(addType);
        let currList = groupMap.get(currGroup) || [];
        currList.push(point);
        groupMap.set(currGroup, currList);
        // point.mapBornOffsetX = offset.x;
        // point.mapBornOffsetY = offset.y;
        point.vo.groupIndex = currGroup;
        point.vo.index = index;
        point.vo.type = addType;
        point.vo.targetX = x;
        point.vo.targetY = y;
        point.vo.range = range;
        // point.pos(x - point._offsetPos.x, y - point._offsetPos.y);
        point.zOrder = LayerManager.STAGE_ZORDE_TOPEST;
        point.name = currGroup + "_" + index;
        this.setPointPos(point, x, y);
        this.addOnClick(this, point, this.onSelPathPoint, Laya.Event.CLICK, [point]);
        let buildItem = this.dialog.buildItemMap.get(configVo.buidingType);
        this.map.addChild(point);
        point.on(Event.DRAG_MOVE, this, this.onPointDrag);
        this.onUpdatePathPoint(addType);
        this.onSelPathPoint(point);
        this.addWoker(point);
        return point;
    }
    onPointDrag(point) {
        // let currList = this.getCurrSelectPointList(EBuildingPathPointType.BORN);
        // if(currList[currList.length - 1] == point){
        // }else{
        //     this.drawPathPointConnect();
        // }
        this.drawPathPointConnect();
        let list = this.workerListMap.get(point) || [];
        list.forEach(worker => {
            worker.pos(point.vo.targetX, point.vo.targetY);
        });
    }
    setPointPos(point, x, y) {
        // point.vo.targetX = x;
        // point.vo.targetY = y;
        x = x - point._offsetPos.x;
        y = y - point._offsetPos.y;
        point.pos(x, y);
    }
    onDelPathPoint(e) {
        let target = e.target;
        if (target == this.btnDelPoint) {
            var type = EGuajiBuildPathPointType.GO_AND_BACK;
        }
        // else if (target == this.btnDelWorker) {
        //     var type = EGuajiBuildPathPointType.STAND_POSITION;
        // }
        let currPoint = this.getCurrSelectPoint(type);
        this.doDelPathPoint(currPoint);
    }
    doDelPathPoint(currPoint) {
        if (!currPoint) {
            return;
        }
        let addType = currPoint.vo.type;
        let groupMap = this.getGroupMapByType(addType);
        let currGroup = currPoint.vo.groupIndex;
        let currList = groupMap.get(currGroup) || [];
        this.map.removeChild(currPoint);
        let index = currList.indexOf(currPoint);
        currList.splice(index, 1);
        this.onUpdatePathPoint(addType);
        let prePoint = currList[index - 1];
        this.onSelPathPoint(prePoint);
        if (currList.length == 0) {
            groupMap.delete(currGroup);
        }
        this.removeWorker(currPoint);
    }
    onUpdatePathPoint(addType) {
        let index = 0;
        let groupMap = this.getGroupMapByType(addType);
        groupMap.forEach(pointList => {
            index = 0;
            for (let i = 0; i < pointList.length; i++) {
                let item = pointList[i];
                item.setPointIndex(index, false);
                index++;
            }
        });
        this.drawPathPointConnect();
    }
    drawPathPointConnect() {
        if (!this.spPathPointConnect) {
            this.spPathPointConnect = new Image();
            this.spPathPointConnect.zOrder = LayerManager.STAGE_ZORDE_TOPEST;
            this.map.addChild(this.spPathPointConnect);
        }
        //连接所有路点
        this.spPathPointConnect.graphics.clear();
        this.pathItemGroupMap.forEach(pointList => {
            for (let i = 0; i < pointList.length; i++) {
                let point1 = pointList[i];
                let point2 = pointList[i + 1];
                if (point1 && point2) {
                    this.spPathPointConnect.graphics.drawLine(point1.vo.targetX, point1.vo.targetY, point2.vo.targetX, point2.vo.targetY, point1.vo.color, 2);
                }
                // let pos1 = point1.parentPointToGlobal(Point.create().setTo(point1.vo.targetX, point1.vo.targetY), true, this.box);
                // let pos2 = point2.parentPointToGlobal(Point.create().setTo(point2.vo.targetX, point2.vo.targetY), true, this.box);
                // if (point2) {
                //     this.spPathPointConnect.graphics.drawLine(pos1.x, pos1.y, pos2.x, pos2.y, point1.vo.color, 2);
                // }
            }
        });
    }
    onStartPathPoint(monster) {
        this.moveType = 1;
        if (this.pathItemGroupMap.size == 0) {
            TipsUtil.showTips(window.iLang.L2_QING_XIAN_TIAN_JIA_LU_DIAN_ch03.il());
            return;
        }
        this.workerListMap.forEach((list, point) => {
            let workerList = list; //list.filter(worker => worker.work_type == EGuajiBuildPathPointType.GO_AND_BACK);
            for (let i = 0; i < workerList.length; i++) {
                let worker = workerList[i];
                let groupList = Array.from(this.pathItemGroupMap.keys());
                // let pointList = this.pathItemGroupMap.get(groupList[worker.pointVo.groupIndex]);
                let pointList = this.pathItemGroupMap.get(worker.pointVo.groupIndex);
                worker.pathPointList = pointList.map(item => item.vo);
                let firstPoint = worker.firstPathPoint;
                worker.pos(firstPoint.targetX, firstPoint.targetY);
                var delay = i * 500;
                this.timer.once(delay, this, () => {
                    worker.startPathPoint(pointList.map(item => item.vo));
                });
            }
        });
    }
    onSavePathPoint(isSaveIex) {
        let cfgVo = this.getCurrBuildingCfgVo();
        this._saveAllType();
        // cfgVo.saveMapCfgVo();
        if (isSaveIex) {
            this.saveAllMapCfgVo();
        }
    }
    saveAllMapCfgVo() {
        GuajiBuildEditorVo.saveAllBuildingCfgVo();
        this.getBuildingMiscVo().saveBuildingCfgVo();
    }
    _saveAllType(typeList = [EGuajiBuildPathPointType.GO_AND_BACK, EGuajiBuildPathPointType.STAND_POSITION]) {
        let buildingType = this.currBuildingType;
        typeList.forEach(pointType => {
            let groupMap = this.getGroupMapByType(pointType);
            let allPointList = [];
            groupMap.forEach((pointList, groupIndex) => {
                if (pointList.length > 0) {
                    allPointList.push(...pointList);
                }
            });
            allPointList.sort((a, b) => {
                if (a.vo.groupIndex != b.vo.groupIndex) {
                    return a.vo.groupIndex - b.vo.groupIndex;
                }
                if (a.pointIndex != b.pointIndex) {
                    return a.pointIndex - b.pointIndex;
                }
                let tagA = a.vo.groupIndex + "_" + a.pointIndex;
                let tagB = b.vo.groupIndex + "_" + b.pointIndex;
                return tagA.localeCompare(tagB);
            });
            var oldBuildingData;
            if (pointType == EGuajiBuildPathPointType.GO_AND_BACK) {
                oldBuildingData = GuajiBuildPathPointVo.savePathPointVo;
            }
            else if (pointType == EGuajiBuildPathPointType.STAND_POSITION) {
                oldBuildingData = GuajiBuildPathPointVo.saveHeroPointVo;
            }
            let saveGroupMap = new Map();
            allPointList.forEach(item => {
                let vo = item.vo;
                let groupIndex = vo.groupIndex;
                let list = saveGroupMap.get(groupIndex);
                if (!list) {
                    list = [];
                    saveGroupMap.set(groupIndex, list);
                }
                list.push(vo);
            });
            oldBuildingData.set(buildingType, saveGroupMap);
        });
        let cfgVo = GuaJiBuildDataCenter.instance.getBuildingCfgVo(buildingType);
        cfgVo.worker_points = GuajiBuildPathPointVo.stringifyAllVoMap(EGuajiBuildPathPointType.STAND_POSITION, buildingType);
        cfgVo.path_points = GuajiBuildPathPointVo.stringifyAllVoMap(EGuajiBuildPathPointType.GO_AND_BACK, buildingType);
    }
    onShowRange() {
        GuajiBuildPathPoint.isForceShowRange = !GuajiBuildPathPoint.isForceShowRange;
        this.pathItemGroupMap.forEach(list => {
            list.forEach(item => {
                item.showRange();
            });
        });
    }
    onSelPathPoint(point) {
        if (!point) {
            return;
        }
        let type = point.vo.type;
        let groupMap = this.getGroupMapByType(type);
        let selectItem = null;
        groupMap.forEach(pointList => {
            pointList.forEach(item => {
                let isSelected = item == point;
                item.setPointIndex(item.pointIndex, isSelected);
                if (isSelected) {
                    selectItem = item;
                }
            });
        });
        if (selectItem) {
            this._onSelPathPoint(selectItem);
        }
    }
    _onSelPathPoint(point) {
        let type = point.vo.type;
        let groupMap = this.getGroupMapByType(type);
        let cfgVo = this.getCurrBuildingCfgVo();
        this.onSelPoint = point;
        this.cbWorkerFlip.selected = point.vo.worker_flip_x;
        this.resetView();
    }
    // private getCurrSelectPoint(type: EGuajiBuildPathPointType): GuajiBuildPathPoint {
    //     let point: GuajiBuildPathPoint = null;
    //     let groupMap = this.getGroupMapByType(type);
    //     groupMap.forEach(_list => {
    //         _list.forEach(item => {
    //             if (item.isSelected) {
    //                 point = item;
    //             }
    //         })
    //     })
    //     return point;
    // }
    //现在只有一种路点
    getCurrSelectPoint(type) {
        type = EGuajiBuildPathPointType.GO_AND_BACK;
        let point = null;
        let groupMap = this.getGroupMapByType(type);
        groupMap.forEach(_list => {
            _list.forEach(item => {
                if (item.isSelected) {
                    point = item;
                }
            });
        });
        return point;
    }
    getCurrSelectPointList(type) {
        let groupIndex = this.getCurrSelectPointGroupIndex(type);
        let groupMap = this.getGroupMapByType(type);
        let currList = groupMap.get(groupIndex) || [];
        return currList;
    }
    getCurrSelectPointGroupIndex(type) {
        let currPoint = this.getCurrSelectPoint(type);
        if (currPoint) {
            return currPoint.vo.groupIndex;
        }
        return 0;
    }
    destroy(destroyChild) {
        this.onSavePathPoint(false);
        super.destroy(destroyChild);
    }
}
