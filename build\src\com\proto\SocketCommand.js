import { ClassUtils } from "laya/utils/ClassUtils";
import { m_treasure_info_toc } from "./line/m_treasure_info_toc";
import { m_family_boss_info_toc } from "./line/m_family_boss_info_toc";
import { m_world_boss_fight_result_toc } from "./line/m_world_boss_fight_result_toc";
import { m_travel_speed_toc } from "./line/m_travel_speed_toc";
import { m_peak_info_battle_toc } from "./line/m_peak_info_battle_toc";
import { m_family_boss_fight_result_toc } from "./line/m_family_boss_fight_result_toc";
import { m_csclan_solo_shop_lv_toc } from "./line/m_csclan_solo_shop_lv_toc";
import { m_team_info_my_team_toc } from "./line/m_team_info_my_team_toc";
import { m_treasure_refresh_toc } from "./line/m_treasure_refresh_toc";
import { m_peak_info_get_msg_toc } from "./line/m_peak_info_get_msg_toc";
import { m_peak_personal_info_toc } from "./line/m_peak_personal_info_toc";
import { m_treasure_gift_active_toc } from "./line/m_treasure_gift_active_toc";
import { m_hzzd_route_info_toc } from "./line/m_hzzd_route_info_toc";
import { m_goods_show_goods_toc } from "./line/m_goods_show_goods_toc";
import { m_hongbao_fetch_toc } from "./line/m_hongbao_fetch_toc";
import { m_treasure_log_toc } from "./line/m_treasure_log_toc";
import { m_dawanka_fetch_toc } from "./line/m_dawanka_fetch_toc";
import { m_travel_fetch_toc } from "./line/m_travel_fetch_toc";
import { m_csclan_solo_box_toc } from "./line/m_csclan_solo_box_toc";
import { m_test_tower_op_toc } from "./line/m_test_tower_op_toc";
import { m_random_pvp_info_toc } from "./line/m_random_pvp_info_toc";
import { m_treasure_cal_time_toc } from "./line/m_treasure_cal_time_toc";
import { m_dominate_pvp_new_season_toc } from "./line/m_dominate_pvp_new_season_toc";
import { m_level_gift_info_toc } from "./line/m_level_gift_info_toc";
import { m_mock_pvp_ready_toc } from "./line/m_mock_pvp_ready_toc";
import { m_cross_ladder_info_toc } from "./line/m_cross_ladder_info_toc";
import { m_mini_game_get_str_toc } from "./line/m_mini_game_get_str_toc";
import { m_modular_activity_war_log_info_toc } from "./line/m_modular_activity_war_log_info_toc";
import { m_team_lineup_set_toc } from "./line/m_team_lineup_set_toc";
import { m_mock_pvp_contest_chat_toc } from "./line/m_mock_pvp_contest_chat_toc";
import { m_welfare_activate_code_toc } from "./line/m_welfare_activate_code_toc";
import { m_main_battle_auto_toc } from "./line/m_main_battle_auto_toc";
import { m_test_tower_continuous_fight_result_toc } from "./line/m_test_tower_continuous_fight_result_toc";
import { m_family_boss_sweep_toc } from "./line/m_family_boss_sweep_toc";
import { m_family_task_gift_toc } from "./line/m_family_task_gift_toc";
import { m_mock_pvp_heat_toc } from "./line/m_mock_pvp_heat_toc";
import { m_fish_lineup_list_toc } from "./line/m_fish_lineup_list_toc";
import { m_goods_list_toc } from "./line/m_goods_list_toc";
import { m_level_gift_fetch_toc } from "./line/m_level_gift_fetch_toc";
import { m_treasure_worker_active_toc } from "./line/m_treasure_worker_active_toc";
import { m_rent_hero_list_toc } from "./line/m_rent_hero_list_toc";
import { m_fish_info_toc } from "./line/m_fish_info_toc";
import { m_treasure_fetch_toc } from "./line/m_treasure_fetch_toc";
import { m_treasure_worker_info_toc } from "./line/m_treasure_worker_info_toc";
import { m_fuli_yueka_fetch_toc } from "./line/m_fuli_yueka_fetch_toc";
import { m_goods_update_toc } from "./line/m_goods_update_toc";
import { m_hzzd_fetch_toc } from "./line/m_hzzd_fetch_toc";
import { m_cross_ladder_list_toc } from "./line/m_cross_ladder_list_toc";
import { m_ares_palace_info_toc } from "./line/m_ares_palace_info_toc";
import { m_team_xswh_fight_result_toc } from "./line/m_team_xswh_fight_result_toc";
import { m_boat_race_info_toc } from "./line/m_boat_race_info_toc";
import { m_team_xswh_fetch_toc } from "./line/m_team_xswh_fetch_toc";
import { m_vip_info_toc } from "./line/m_vip_info_toc";
import { m_pass_check_info_toc } from "./line/m_pass_check_info_toc";
import { m_vip_buy_gift_toc } from "./line/m_vip_buy_gift_toc";
import { m_role_base_reload_toc } from "./line/m_role_base_reload_toc";
import { m_family_task_reward_toc } from "./line/m_family_task_reward_toc";
import { m_wars_update_city_toc } from "./line/m_wars_update_city_toc";
import { m_wars_city_info_toc } from "./line/m_wars_city_info_toc";
import { m_rent_hero_look_toc } from "./line/m_rent_hero_look_toc";
import { m_role_setting_list_toc } from "./line/m_role_setting_list_toc";
import { m_wars_team_op_toc } from "./line/m_wars_team_op_toc";
import { m_lord_op_toc } from "./line/m_lord_op_toc";
import { m_lord_equip_compose_toc } from "./line/m_lord_equip_compose_toc";
import { m_lord_treasure_op_toc } from "./line/m_lord_treasure_op_toc";
import { m_wars_act_info_toc } from "./line/m_wars_act_info_toc";
import { m_role_setting_toc } from "./line/m_role_setting_toc";
import { m_csclan_list_toc } from "./line/m_csclan_list_toc";
import { m_role_look_hero_toc } from "./line/m_role_look_hero_toc";
import { m_role_look_hero_attr_source_toc } from "./line/m_role_look_hero_attr_source_toc";
import { m_role_setting2_toc } from "./line/m_role_setting2_toc";
import { m_role_look_sdk_profile_toc } from "./line/m_role_look_sdk_profile_toc";
import { m_role_look_lord_toc } from "./line/m_role_look_lord_toc";
import { m_wars_kill_info_toc } from "./line/m_wars_kill_info_toc";
import { m_wars_select_army_toc } from "./line/m_wars_select_army_toc";
import { m_wars_info_get_msg_toc } from "./line/m_wars_info_get_msg_toc";
import { m_wars_info_nty_rank_data_toc } from "./line/m_wars_info_nty_rank_data_toc";
import { m_wars_event_toc } from "./line/m_wars_event_toc";
import { m_wars_make_path_toc } from "./line/m_wars_make_path_toc";
import { m_wars_init_camp_toc } from "./line/m_wars_init_camp_toc";
import { m_wars_update_role_toc } from "./line/m_wars_update_role_toc";
import { m_team_info_teams_toc } from "./line/m_team_info_teams_toc";
import { m_wars_daily_report_toc } from "./line/m_wars_daily_report_toc";
import { m_welfare_sdk_share_info_toc } from "./line/m_welfare_sdk_share_info_toc";
import { m_daily_mission_gift_toc } from "./line/m_daily_mission_gift_toc";
import { m_main_battle_info_toc } from "./line/m_main_battle_info_toc";
import { m_red_cliff_sweep_toc } from "./line/m_red_cliff_sweep_toc";
import { m_boat_race_update_toc } from "./line/m_boat_race_update_toc";
import { m_csclan_create_toc } from "./line/m_csclan_create_toc";
import { m_role_level_up_toc } from "./line/m_role_level_up_toc";
import { m_main_battle_fight_result_toc } from "./line/m_main_battle_fight_result_toc";
import { m_travel_info_toc } from "./line/m_travel_info_toc";
import { m_progress_gift_buy_toc } from "./line/m_progress_gift_buy_toc";
import { m_family_boss_update_toc } from "./line/m_family_boss_update_toc";
import { m_wars_operate_toc } from "./line/m_wars_operate_toc";
import { m_itinerant_shop_info_toc } from "./line/m_itinerant_shop_info_toc";
import { m_hongbao_fetch_update_toc } from "./line/m_hongbao_fetch_update_toc";
import { m_medal_update_toc } from "./line/m_medal_update_toc";
import { m_csc_fmsolo_info_toc } from "./line/m_csc_fmsolo_info_toc";
import { m_travel_refresh_toc } from "./line/m_travel_refresh_toc";
import { m_hero_pass_info_toc } from "./line/m_hero_pass_info_toc";
import { m_eight_login_info_toc } from "./line/m_eight_login_info_toc";
import { m_pass_check_fetch_toc } from "./line/m_pass_check_fetch_toc";
import { m_maze_info_toc } from "./line/m_maze_info_toc";
import { m_equip_auto_unload_toc } from "./line/m_equip_auto_unload_toc";
import { m_mission_shop_buy_toc } from "./line/m_mission_shop_buy_toc";
import { m_team_xswh_info_toc } from "./line/m_team_xswh_info_toc";
import { m_simp_mission_update_toc } from "./line/m_simp_mission_update_toc";
import { m_csclan_self_toc } from "./line/m_csclan_self_toc";
import { m_csclan_audit_limit_toc } from "./line/m_csclan_audit_limit_toc";
import { m_cross_ladder_logs_toc } from "./line/m_cross_ladder_logs_toc";
import { m_csclan_set_owner_toc } from "./line/m_csclan_set_owner_toc";
import { m_csclan_update_notice_toc } from "./line/m_csclan_update_notice_toc";
import { m_csclan_operate_toc } from "./line/m_csclan_operate_toc";
import { m_csclan_members_toc } from "./line/m_csclan_members_toc";
import { m_csclan_apply_list_toc } from "./line/m_csclan_apply_list_toc";
import { m_csclan_logs_toc } from "./line/m_csclan_logs_toc";
import { m_csclan_rename_toc } from "./line/m_csclan_rename_toc";
import { m_csclan_base_toc } from "./line/m_csclan_base_toc";
import { m_csclan_scene_toc } from "./line/m_csclan_scene_toc";
import { m_quick_shop_info_toc } from "./line/m_quick_shop_info_toc";
import { m_daily_gift_tehui_info_toc } from "./line/m_daily_gift_tehui_info_toc";
import { m_mock_pvp_logs_toc } from "./line/m_mock_pvp_logs_toc";
import { m_family_apply_list_toc } from "./line/m_family_apply_list_toc";
import { m_multi_lineup_get_power_toc } from "./line/m_multi_lineup_get_power_toc";
import { m_daily_gift_new_discount_info_toc } from "./line/m_daily_gift_new_discount_info_toc";
import { m_pass_check_update_toc } from "./line/m_pass_check_update_toc";
import { m_family_join_toc } from "./line/m_family_join_toc";
import { m_maze_start_toc } from "./line/m_maze_start_toc";
import { m_fight_set_speed_toc } from "./line/m_fight_set_speed_toc";
import { m_small_game_info_toc } from "./line/m_small_game_info_toc";
import { m_master_card_notice_toc } from "./line/m_master_card_notice_toc";
import { m_family_reply_join_toc } from "./line/m_family_reply_join_toc";
import { m_lord_skill_set_toc } from "./line/m_lord_skill_set_toc";
import { m_master_card_power_toc } from "./line/m_master_card_power_toc";
import { m_master_card_spe_effect_toc } from "./line/m_master_card_spe_effect_toc";
import { m_boat_race_rank_toc } from "./line/m_boat_race_rank_toc";
import { m_lcqs_fight_result_toc } from "./line/m_lcqs_fight_result_toc";
import { m_bag_goods_list_toc } from "./line/m_bag_goods_list_toc";
import { m_family_set_owner_toc } from "./line/m_family_set_owner_toc";
import { m_mission_shop_update_toc } from "./line/m_mission_shop_update_toc";
import { m_world_level_info_toc } from "./line/m_world_level_info_toc";
import { m_master_card_decoration_op_toc } from "./line/m_master_card_decoration_op_toc";
import { m_battle_trial_info_toc } from "./line/m_battle_trial_info_toc";
import { m_role_attr_change_toc } from "./line/m_role_attr_change_toc";
import { m_fish_lottery_toc } from "./line/m_fish_lottery_toc";
import { m_boat_race_bonus_toc } from "./line/m_boat_race_bonus_toc";
import { m_main_battle_mission_status_toc } from "./line/m_main_battle_mission_status_toc";
import { m_boat_race_logs_toc } from "./line/m_boat_race_logs_toc";
import { m_maze_fetch_toc } from "./line/m_maze_fetch_toc";
import { m_god_trial_info_toc } from "./line/m_god_trial_info_toc";
import { m_battle_trial_fetch_info_toc } from "./line/m_battle_trial_fetch_info_toc";
import { m_hero_zhouyin_info_toc } from "./line/m_hero_zhouyin_info_toc";
import { m_main_battle_box_info_toc } from "./line/m_main_battle_box_info_toc";
import { m_hero_pass_fetch_toc } from "./line/m_hero_pass_fetch_toc";
import { m_boat_race_result_toc } from "./line/m_boat_race_result_toc";
import { m_hero_comment_info_toc } from "./line/m_hero_comment_info_toc";
import { m_lcqs_chapter_fetch_toc } from "./line/m_lcqs_chapter_fetch_toc";
import { m_guandu_update_event_toc } from "./line/m_guandu_update_event_toc";
import { m_hongbao_info_update_toc } from "./line/m_hongbao_info_update_toc";
import { m_guandu_next_toc } from "./line/m_guandu_next_toc";
import { m_master_card_replace_toc } from "./line/m_master_card_replace_toc";
import { m_master_card_up_official_toc } from "./line/m_master_card_up_official_toc";
import { m_family_cancel_join_toc } from "./line/m_family_cancel_join_toc";
import { m_master_card_preview_attr_toc } from "./line/m_master_card_preview_attr_toc";
import { m_change_day_toc } from "./line/m_change_day_toc";
import { m_casting_soul_op_toc } from "./line/m_casting_soul_op_toc";
import { m_master_card_decoration_info_toc } from "./line/m_master_card_decoration_info_toc";
import { m_family_update_notice_toc } from "./line/m_family_update_notice_toc";
import { m_family_operate_toc } from "./line/m_family_operate_toc";
import { m_family_info_change_toc } from "./line/m_family_info_change_toc";
import { m_family_logs_toc } from "./line/m_family_logs_toc";
import { m_family_update_member_toc } from "./line/m_family_update_member_toc";
import { m_family_rename_toc } from "./line/m_family_rename_toc";
import { m_family_info_toc } from "./line/m_family_info_toc";
import { m_family_attr_tip_toc } from "./line/m_family_attr_tip_toc";
import { m_family_flagname_toc } from "./line/m_family_flagname_toc";
import { m_family_transfer_count_toc } from "./line/m_family_transfer_count_toc";
import { m_family_transfer_join_toc } from "./line/m_family_transfer_join_toc";
import { m_family_hot_toc } from "./line/m_family_hot_toc";
import { m_family_hot_op_toc } from "./line/m_family_hot_op_toc";
import { m_time_activity_info_toc } from "./line/m_time_activity_info_toc";
import { m_hint_show_tip_toc } from "./line/m_hint_show_tip_toc";
import { m_god_trial_buff_toc } from "./line/m_god_trial_buff_toc";
import { m_battle_trial_daily_end_info_toc } from "./line/m_battle_trial_daily_end_info_toc";
import { m_monster_group_power_toc } from "./line/m_monster_group_power_toc";
import { m_vip_free_gift_info_toc } from "./line/m_vip_free_gift_info_toc";
import { m_lord_info_toc } from "./line/m_lord_info_toc";
import { m_boat_race_alloc_items_toc } from "./line/m_boat_race_alloc_items_toc";
import { m_hzzd_info_history_toc } from "./line/m_hzzd_info_history_toc";
import { m_maze_hero_toc } from "./line/m_maze_hero_toc";
import { m_god_equip_compose_toc } from "./line/m_god_equip_compose_toc";
import { m_battle_trial_offline_info_toc } from "./line/m_battle_trial_offline_info_toc";
import { m_monster_group_power_list_toc } from "./line/m_monster_group_power_list_toc";
import { m_world_boss_info_toc } from "./line/m_world_boss_info_toc";
import { m_peak_info_member_toc } from "./line/m_peak_info_member_toc";
import { m_lord_set_lineup_toc } from "./line/m_lord_set_lineup_toc";
import { m_gmcmd_battle_edit_toc } from "./line/m_gmcmd_battle_edit_toc";
import { m_chat_get_goods_toc } from "./line/m_chat_get_goods_toc";
import { m_ranking_history_list_toc } from "./line/m_ranking_history_list_toc";
import { m_story_maze_fetch_toc } from "./line/m_story_maze_fetch_toc";
import { m_god_equip_select_toc } from "./line/m_god_equip_select_toc";
import { m_family_boss_look_toc } from "./line/m_family_boss_look_toc";
import { m_world_boss_buy_times_toc } from "./line/m_world_boss_buy_times_toc";
import { m_random_pvp_op_toc } from "./line/m_random_pvp_op_toc";
import { m_wars_init_toc } from "./line/m_wars_init_toc";
import { m_god_equip_recast_toc } from "./line/m_god_equip_recast_toc";
import { m_pass_behead_battle_toc } from "./line/m_pass_behead_battle_toc";
import { m_treasure_pull_list_toc } from "./line/m_treasure_pull_list_toc";
import { m_large_peak_info_look_member_toc } from "./line/m_large_peak_info_look_member_toc";
import { m_battle_trial_battle_up_toc } from "./line/m_battle_trial_battle_up_toc";
import { m_broadcast_normal_toc } from "./line/m_broadcast_normal_toc";
import { m_stage_breed_info_toc } from "./line/m_stage_breed_info_toc";
import { m_boat_peak_member_toc } from "./line/m_boat_peak_member_toc";
import { m_shop_update_toc } from "./line/m_shop_update_toc";
import { m_family_hongbao_fetch_update_toc } from "./line/m_family_hongbao_fetch_update_toc";
import { m_family_hongbao_mission_info_toc } from "./line/m_family_hongbao_mission_info_toc";
import { m_peak_info_toc } from "./line/m_peak_info_toc";
import { m_activity_update_fetch_toc } from "./line/m_activity_update_fetch_toc";
import { m_theme_activity_up_star_reward_info_toc } from "./line/m_theme_activity_up_star_reward_info_toc";
import { m_boat_peak_look_pre_score_toc } from "./line/m_boat_peak_look_pre_score_toc";
import { m_boat_peak_join_members_toc } from "./line/m_boat_peak_join_members_toc";
import { m_boat_peak_fetch_toc } from "./line/m_boat_peak_fetch_toc";
import { m_profile_update_toc } from "./line/m_profile_update_toc";
import { m_team_info_my_team_update_toc } from "./line/m_team_info_my_team_update_toc";
import { m_hero_resonate_dhyana_op_toc } from "./line/m_hero_resonate_dhyana_op_toc";
import { m_hero_resonate_equip_op_toc } from "./line/m_hero_resonate_equip_op_toc";
import { m_god_equip_enchant_toc } from "./line/m_god_equip_enchant_toc";
import { m_battle_trial_battle_reset_toc } from "./line/m_battle_trial_battle_reset_toc";
import { m_equip_reinforce_toc } from "./line/m_equip_reinforce_toc";
import { m_random_pvp_match_toc } from "./line/m_random_pvp_match_toc";
import { m_peak_info_opp_toc } from "./line/m_peak_info_opp_toc";
import { m_player_strategy_info_toc } from "./line/m_player_strategy_info_toc";
import { m_equip_recycle_toc } from "./line/m_equip_recycle_toc";
import { m_main_battle_box_upgrade_toc } from "./line/m_main_battle_box_upgrade_toc";
import { m_war_flag_op_toc } from "./line/m_war_flag_op_toc";
import { m_equip_decompose_toc } from "./line/m_equip_decompose_toc";
import { m_equip_compose_toc } from "./line/m_equip_compose_toc";
import { m_family_boss_attr_toc } from "./line/m_family_boss_attr_toc";
import { m_arena_list_toc } from "./line/m_arena_list_toc";
import { m_activity_limit_sign_info_toc } from "./line/m_activity_limit_sign_info_toc";
import { m_time_activity_update_toc } from "./line/m_time_activity_update_toc";
import { m_squad_lineup_set_toc } from "./line/m_squad_lineup_set_toc";
import { m_bingfu_info_toc } from "./line/m_bingfu_info_toc";
import { m_activity_yueka_info_toc } from "./line/m_activity_yueka_info_toc";
import { m_page_list_toc } from "./line/m_page_list_toc";
import { m_equip_info_update_toc } from "./line/m_equip_info_update_toc";
import { m_family_boss_gather_toc } from "./line/m_family_boss_gather_toc";
import { m_fight_simp_result_toc } from "./line/m_fight_simp_result_toc";
import { m_lord_star_toc } from "./line/m_lord_star_toc";
import { m_activity_list_toc } from "./line/m_activity_list_toc";
import { m_team_operate_toc } from "./line/m_team_operate_toc";
import { m_bingfu_breed_toc } from "./line/m_bingfu_breed_toc";
import { m_daily_ad_code_info_toc } from "./line/m_daily_ad_code_info_toc";
import { m_random_pvp_palace_toc } from "./line/m_random_pvp_palace_toc";
import { m_arena_fight_result_toc } from "./line/m_arena_fight_result_toc";
import { m_item_show_gains_toc } from "./line/m_item_show_gains_toc";
import { m_boat_race_boats_toc } from "./line/m_boat_race_boats_toc";
import { m_complaints_info_toc } from "./line/m_complaints_info_toc";
import { m_time_achievement_info_toc } from "./line/m_time_achievement_info_toc";
import { m_fuli_token_buy_toc } from "./line/m_fuli_token_buy_toc";
import { m_hunt_gift_toc } from "./line/m_hunt_gift_toc";
import { m_star_plan_update_toc } from "./line/m_star_plan_update_toc";
import { m_hero_lock_toc } from "./line/m_hero_lock_toc";
import { m_system_setting_toc } from "./line/m_system_setting_toc";
import { m_hero_recycle_toc } from "./line/m_hero_recycle_toc";
import { m_system_time_toc } from "./line/m_system_time_toc";
import { m_hero_down_lineup_toc } from "./line/m_hero_down_lineup_toc";
import { m_fish_notice_toc } from "./line/m_fish_notice_toc";
import { m_fish_preview_attr_toc } from "./line/m_fish_preview_attr_toc";
import { m_fish_power_toc } from "./line/m_fish_power_toc";
import { m_fish_spe_effect_toc } from "./line/m_fish_spe_effect_toc";
import { m_fish_logs_toc } from "./line/m_fish_logs_toc";
import { m_fish_handbook_info_toc } from "./line/m_fish_handbook_info_toc";
import { m_hero_recycle_change_toc } from "./line/m_hero_recycle_change_toc";
import { m_hero_my_rank_toc } from "./line/m_hero_my_rank_toc";
import { m_wing_info_toc } from "./line/m_wing_info_toc";
import { m_hero_inherit_update_toc } from "./line/m_hero_inherit_update_toc";
import { m_hero_recycle_down_toc } from "./line/m_hero_recycle_down_toc";
import { m_hero_recycle_times_toc } from "./line/m_hero_recycle_times_toc";
import { m_hero_act_fourteen_toc } from "./line/m_hero_act_fourteen_toc";
import { m_hero_evolve_op_toc } from "./line/m_hero_evolve_op_toc";
import { m_hero_evolve_info_toc } from "./line/m_hero_evolve_info_toc";
import { m_item_sale_toc } from "./line/m_item_sale_toc";
import { m_random_pvp_update_toc } from "./line/m_random_pvp_update_toc";
import { m_bingfu_shift_toc } from "./line/m_bingfu_shift_toc";
import { m_equip_info_toc } from "./line/m_equip_info_toc";
import { m_team_info_my_team_apply_toc } from "./line/m_team_info_my_team_apply_toc";
import { m_rank_mission_info_toc } from "./line/m_rank_mission_info_toc";
import { m_daily_mission_info_toc } from "./line/m_daily_mission_info_toc";
import { m_treasure_dispatch_toc } from "./line/m_treasure_dispatch_toc";
import { m_mock_pvp_op_toc } from "./line/m_mock_pvp_op_toc";
import { m_common_tips_toc } from "./line/m_common_tips_toc";
import { m_arena_max_reward_info_toc } from "./line/m_arena_max_reward_info_toc";
import { m_random_pvp_fight_result_toc } from "./line/m_random_pvp_fight_result_toc";
import { m_peak_info_bet_toc } from "./line/m_peak_info_bet_toc";
import { m_equip_load_toc } from "./line/m_equip_load_toc";
import { m_profile_info_toc } from "./line/m_profile_info_toc";
import { m_treasure_other_toc } from "./line/m_treasure_other_toc";
import { m_mock_pvp_info_toc } from "./line/m_mock_pvp_info_toc";
import { m_family_boss_rank_toc } from "./line/m_family_boss_rank_toc";
import { m_fight_share_chat_toc } from "./line/m_fight_share_chat_toc";
import { m_vip_buy_recommend_toc } from "./line/m_vip_buy_recommend_toc";
import { m_boat_race_event_toc } from "./line/m_boat_race_event_toc";
import { m_random_pvp_new_season_toc } from "./line/m_random_pvp_new_season_toc";
import { m_peak_info_history_toc } from "./line/m_peak_info_history_toc";
import { m_equip_unload_toc } from "./line/m_equip_unload_toc";
import { m_profile_change_toc } from "./line/m_profile_change_toc";
import { m_shortcut_shop_info_toc } from "./line/m_shortcut_shop_info_toc";
import { m_csc_fmsolo_group_toc } from "./line/m_csc_fmsolo_group_toc";
import { m_mock_pvp_look_hero_toc } from "./line/m_mock_pvp_look_hero_toc";
import { m_csclan_solo_info_toc } from "./line/m_csclan_solo_info_toc";
import { m_story_maze_hero_toc } from "./line/m_story_maze_hero_toc";
import { m_equip_auto_load_toc } from "./line/m_equip_auto_load_toc";
import { m_team_lineup_get_toc } from "./line/m_team_lineup_get_toc";
import { m_guide_mission_toc } from "./line/m_guide_mission_toc";
import { m_red_cliff_info_toc } from "./line/m_red_cliff_info_toc";
import { m_soul_hero_info_toc } from "./line/m_soul_hero_info_toc";
import { m_shortcut_shop_buy_toc } from "./line/m_shortcut_shop_buy_toc";
import { m_activity_info_toc } from "./line/m_activity_info_toc";
import { m_hero_pass_gift_toc } from "./line/m_hero_pass_gift_toc";
import { m_hero_pass_update_toc } from "./line/m_hero_pass_update_toc";
import { m_activity_shop_update_toc } from "./line/m_activity_shop_update_toc";
import { m_equip_auto_reinforce_toc } from "./line/m_equip_auto_reinforce_toc";
import { m_rent_hero_op_toc } from "./line/m_rent_hero_op_toc";
import { m_activity_exam_answer_toc } from "./line/m_activity_exam_answer_toc";
import { m_activity_gift_toc } from "./line/m_activity_gift_toc";
import { m_equip_auto_compose_info_toc } from "./line/m_equip_auto_compose_info_toc";
import { m_equip_compose_logs_toc } from "./line/m_equip_compose_logs_toc";
import { m_equip_replace_toc } from "./line/m_equip_replace_toc";
import { m_pass_check_gift_toc } from "./line/m_pass_check_gift_toc";
import { m_equip_filter_info_toc } from "./line/m_equip_filter_info_toc";
import { m_equip_star_op_toc } from "./line/m_equip_star_op_toc";
import { m_equip_bingfu_recast_toc } from "./line/m_equip_bingfu_recast_toc";
import { m_equip_bingfu_decompose_toc } from "./line/m_equip_bingfu_decompose_toc";
import { m_maze_spoils_toc } from "./line/m_maze_spoils_toc";
import { m_equip_bingfa_op_toc } from "./line/m_equip_bingfa_op_toc";
import { m_equip_bingfa_exchange_toc } from "./line/m_equip_bingfa_exchange_toc";
import { m_equip_lock_toc } from "./line/m_equip_lock_toc";
import { m_wing_op_toc } from "./line/m_wing_op_toc";
import { m_team_lineup_list_toc } from "./line/m_team_lineup_list_toc";
import { m_lord_lineup_list_toc } from "./line/m_lord_lineup_list_toc";
import { m_deputy_info_toc } from "./line/m_deputy_info_toc";
import { m_activity_shop_info_toc } from "./line/m_activity_shop_info_toc";
import { m_guide_hint_toc } from "./line/m_guide_hint_toc";
import { m_mock_pvp_schemes_toc } from "./line/m_mock_pvp_schemes_toc";
import { m_battle_trial_hanging_info_toc } from "./line/m_battle_trial_hanging_info_toc";
import { m_csclan_solo_fetch_toc } from "./line/m_csclan_solo_fetch_toc";
import { m_csclan_solo_group_toc } from "./line/m_csclan_solo_group_toc";
import { m_medal_list_toc } from "./line/m_medal_list_toc";
import { m_fuli_fund_fetch_toc } from "./line/m_fuli_fund_fetch_toc";
import { m_welfare_sdk_share_toc } from "./line/m_welfare_sdk_share_toc";
import { m_wars_sign_toc } from "./line/m_wars_sign_toc";
import { m_activity_exam_list_toc } from "./line/m_activity_exam_list_toc";
import { m_role_family_change_toc } from "./line/m_role_family_change_toc";
import { m_quick_shop_tips_toc } from "./line/m_quick_shop_tips_toc";
import { m_master_card_lottery_toc } from "./line/m_master_card_lottery_toc";
import { m_wars_team_info_toc } from "./line/m_wars_team_info_toc";
import { m_letter_get_toc } from "./line/m_letter_get_toc";
import { m_mock_pvp_config_toc } from "./line/m_mock_pvp_config_toc";
import { m_wars_team_hero_info_toc } from "./line/m_wars_team_hero_info_toc";
import { m_hero_skin_info_toc } from "./line/m_hero_skin_info_toc";
import { m_microterminal_info_toc } from "./line/m_microterminal_info_toc";
import { m_role_update_ext_toc } from "./line/m_role_update_ext_toc";
import { m_role_best_hero_toc } from "./line/m_role_best_hero_toc";
import { m_team_merge_reward_toc } from "./line/m_team_merge_reward_toc";
import { m_role_look_lineup_toc } from "./line/m_role_look_lineup_toc";
import { m_guide_step_toc } from "./line/m_guide_step_toc";
import { m_master_talent_science_update_toc } from "./line/m_master_talent_science_update_toc";
import { m_system_heartbeat_toc } from "./line/m_system_heartbeat_toc";
import { m_wars_look_op_army_toc } from "./line/m_wars_look_op_army_toc";
import { m_daily_fuli_info_toc } from "./line/m_daily_fuli_info_toc";
import { m_cross_ladder_fight_result_toc } from "./line/m_cross_ladder_fight_result_toc";
import { m_microterminal_sign_fetch_toc } from "./line/m_microterminal_sign_fetch_toc";
import { m_wars_look_op_score_rank_toc } from "./line/m_wars_look_op_score_rank_toc";
import { m_stage_copy_info_toc } from "./line/m_stage_copy_info_toc";
import { m_wars_look_op_fight_log_toc } from "./line/m_wars_look_op_fight_log_toc";
import { m_wars_look_op_city_log_toc } from "./line/m_wars_look_op_city_log_toc";
import { m_wars_look_op_sign_toc } from "./line/m_wars_look_op_sign_toc";
import { m_team_share_list_toc } from "./line/m_team_share_list_toc";
import { m_wars_look_op_camp_rank_toc } from "./line/m_wars_look_op_camp_rank_toc";
import { m_guide_event_toc } from "./line/m_guide_event_toc";
import { m_system_error_toc } from "./line/m_system_error_toc";
import { m_role_cross_group_toc } from "./line/m_role_cross_group_toc";
import { m_gmcmd_do_toc } from "./line/m_gmcmd_do_toc";
import { m_story_maze_pub_toc } from "./line/m_story_maze_pub_toc";
import { m_war_flag_info_toc } from "./line/m_war_flag_info_toc";
import { m_ranking_list_toc } from "./line/m_ranking_list_toc";
import { m_rank_mission_update_toc } from "./line/m_rank_mission_update_toc";
import { m_arena_update_toc } from "./line/m_arena_update_toc";
import { m_stage_copy_drop_group_toc } from "./line/m_stage_copy_drop_group_toc";
import { m_arena_logs_toc } from "./line/m_arena_logs_toc";
import { m_chat_auth_toc } from "./line/m_chat_auth_toc";
import { m_td_simp_info_toc } from "./line/m_td_simp_info_toc";
import { m_td_mission_info_toc } from "./line/m_td_mission_info_toc";
import { m_lord_recycle_preview_toc } from "./line/m_lord_recycle_preview_toc";
import { m_chat_channel_toc } from "./line/m_chat_channel_toc";
import { m_letter_operate_toc } from "./line/m_letter_operate_toc";
import { m_system_message_toc } from "./line/m_system_message_toc";
import { m_hero_skin_upgrade_toc } from "./line/m_hero_skin_upgrade_toc";
import { m_chat_private_history_toc } from "./line/m_chat_private_history_toc";
import { m_hero_list_toc } from "./line/m_hero_list_toc";
import { m_chat_misc_toc } from "./line/m_chat_misc_toc";
import { m_war_flag_active_toc } from "./line/m_war_flag_active_toc";
import { m_chat_cost_toc } from "./line/m_chat_cost_toc";
import { m_stage_copy_fetch_toc } from "./line/m_stage_copy_fetch_toc";
import { m_login_scene_toc } from "./line/m_login_scene_toc";
import { m_acc_gift_info_toc } from "./line/m_acc_gift_info_toc";
import { m_qxzl_info_get_msg_toc } from "./line/m_qxzl_info_get_msg_toc";
import { m_sys_daily_info_toc } from "./line/m_sys_daily_info_toc";
import { m_boat_race_op_toc } from "./line/m_boat_race_op_toc";
import { m_modular_activity_wish_lottery_info_toc } from "./line/m_modular_activity_wish_lottery_info_toc";
import { m_wars_honor_wall_toc } from "./line/m_wars_honor_wall_toc";
import { m_treasure_exist_toc } from "./line/m_treasure_exist_toc";
import { m_system_config_change_toc } from "./line/m_system_config_change_toc";
import { m_hero_skin_reset_toc } from "./line/m_hero_skin_reset_toc";
import { m_medal_detail_toc } from "./line/m_medal_detail_toc";
import { m_tax_silver_info_toc } from "./line/m_tax_silver_info_toc";
import { m_wars_role_presonal_info_toc } from "./line/m_wars_role_presonal_info_toc";
import { m_sys_daily_fetch_toc } from "./line/m_sys_daily_fetch_toc";
import { m_cycle_activity_info_toc } from "./line/m_cycle_activity_info_toc";
import { m_td_lineup_toc } from "./line/m_td_lineup_toc";
import { m_divine_copy_sweep_toc } from "./line/m_divine_copy_sweep_toc";
import { m_master_card_shop_toc } from "./line/m_master_card_shop_toc";
import { m_family_ranking_list_toc } from "./line/m_family_ranking_list_toc";
import { m_system_config_toc } from "./line/m_system_config_toc";
import { m_hero_update_fight_toc } from "./line/m_hero_update_fight_toc";
import { m_war_flag_link_toc } from "./line/m_war_flag_link_toc";
import { m_hunt_info_toc } from "./line/m_hunt_info_toc";
import { m_cycle_activity_fetch_toc } from "./line/m_cycle_activity_fetch_toc";
import { m_hero_update_list_toc } from "./line/m_hero_update_list_toc";
import { m_td_skill_toc } from "./line/m_td_skill_toc";
import { m_divine_copy_info_toc } from "./line/m_divine_copy_info_toc";
import { m_first_pay_info_toc } from "./line/m_first_pay_info_toc";
import { m_csc_fmsolo_box_toc } from "./line/m_csc_fmsolo_box_toc";
import { m_battle_trial_pass_info_toc } from "./line/m_battle_trial_pass_info_toc";
import { m_hero_upgrade_toc } from "./line/m_hero_upgrade_toc";
import { m_tax_forage_info_toc } from "./line/m_tax_forage_info_toc";
import { m_hunt_run_toc } from "./line/m_hunt_run_toc";
import { m_daily_pay_info_toc } from "./line/m_daily_pay_info_toc";
import { m_td_fight_toc } from "./line/m_td_fight_toc";
import { m_letter_open_toc } from "./line/m_letter_open_toc";
import { m_hero_resonate_level_info_toc } from "./line/m_hero_resonate_level_info_toc";
import { m_story_maze_start_toc } from "./line/m_story_maze_start_toc";
import { m_sgame_info_toc } from "./line/m_sgame_info_toc";
import { m_dawanka_info_toc } from "./line/m_dawanka_info_toc";
import { m_player_strategy_update_toc } from "./line/m_player_strategy_update_toc";
import { m_random_pvp_peak_toc } from "./line/m_random_pvp_peak_toc";
import { m_main_battle_mission_info_toc } from "./line/m_main_battle_mission_info_toc";
import { m_main_battle_mission_fetch_toc } from "./line/m_main_battle_mission_fetch_toc";
import { m_mock_pvp_show_tops_toc } from "./line/m_mock_pvp_show_tops_toc";
import { m_shop_buy_toc } from "./line/m_shop_buy_toc";
import { m_mock_pvp_fight_result_toc } from "./line/m_mock_pvp_fight_result_toc";
import { m_main_battle_box_open_toc } from "./line/m_main_battle_box_open_toc";
import { m_mock_pvp_clean_chat_toc } from "./line/m_mock_pvp_clean_chat_toc";
import { m_main_battle_auto_end_toc } from "./line/m_main_battle_auto_end_toc";
import { m_mock_pvp_lineup_preview_toc } from "./line/m_mock_pvp_lineup_preview_toc";
import { m_daily_copy_sweep_toc } from "./line/m_daily_copy_sweep_toc";
import { m_main_battle_box_rate_toc } from "./line/m_main_battle_box_rate_toc";
import { m_main_battle_missions_toc } from "./line/m_main_battle_missions_toc";
import { m_modular_activity_update_mission_toc } from "./line/m_modular_activity_update_mission_toc";
import { m_daily_pay_fetch_toc } from "./line/m_daily_pay_fetch_toc";
import { m_boat_peak_battle_toc } from "./line/m_boat_peak_battle_toc";
import { m_td_trial_info_toc } from "./line/m_td_trial_info_toc";
import { m_hero_resonate_info_toc } from "./line/m_hero_resonate_info_toc";
import { m_theme_activity_zhouka_info_toc } from "./line/m_theme_activity_zhouka_info_toc";
import { m_theme_activity_wish_lottery_info_toc } from "./line/m_theme_activity_wish_lottery_info_toc";
import { m_friend_fetch_toc } from "./line/m_friend_fetch_toc";
import { m_family_hongbao_mission_update_toc } from "./line/m_family_hongbao_mission_update_toc";
import { m_modular_activity_rare_lottery_info_toc } from "./line/m_modular_activity_rare_lottery_info_toc";
import { m_friend_list_toc } from "./line/m_friend_list_toc";
import { m_friend_best_list_toc } from "./line/m_friend_best_list_toc";
import { m_login_activity_info_toc } from "./line/m_login_activity_info_toc";
import { m_hero_resonate_operate_toc } from "./line/m_hero_resonate_operate_toc";
import { m_soul_hero_link_toc } from "./line/m_soul_hero_link_toc";
import { m_theme_activity_up_star_reward_update_toc } from "./line/m_theme_activity_up_star_reward_update_toc";
import { m_modular_activity_skin_info_toc } from "./line/m_modular_activity_skin_info_toc";
import { m_csclan_solo_state_toc } from "./line/m_csclan_solo_state_toc";
import { m_modular_activity_skin_lottery_toc } from "./line/m_modular_activity_skin_lottery_toc";
import { m_hunt_logs_toc } from "./line/m_hunt_logs_toc";
import { m_fuli_token_fetch_toc } from "./line/m_fuli_token_fetch_toc";
import { m_vip_kefu_op_toc } from "./line/m_vip_kefu_op_toc";
import { m_soul_hero_reset_toc } from "./line/m_soul_hero_reset_toc";
import { m_family_task_do_toc } from "./line/m_family_task_do_toc";
import { m_sgame_wx_club_toc } from "./line/m_sgame_wx_club_toc";
import { m_friend_request_toc } from "./line/m_friend_request_toc";
import { m_squad_lineup_get_toc } from "./line/m_squad_lineup_get_toc";
import { m_broadcast_show_toc } from "./line/m_broadcast_show_toc";
import { m_soul_hero_unlock_toc } from "./line/m_soul_hero_unlock_toc";
import { m_modular_activity_war_log_update_toc } from "./line/m_modular_activity_war_log_update_toc";
import { m_family_science_info_toc } from "./line/m_family_science_info_toc";
import { m_stage_breed_cost_toc } from "./line/m_stage_breed_cost_toc";
import { m_story_tower_battle_info_toc } from "./line/m_story_tower_battle_info_toc";
import { m_progress_gift_info_toc } from "./line/m_progress_gift_info_toc";
import { m_retrieval_info_toc } from "./line/m_retrieval_info_toc";
import { m_fuli_sign_acc_toc } from "./line/m_fuli_sign_acc_toc";
import { m_fuli_yueka_info_toc } from "./line/m_fuli_yueka_info_toc";
import { m_fight_finish_times_toc } from "./line/m_fight_finish_times_toc";
import { m_fuli_fund_info_toc } from "./line/m_fuli_fund_info_toc";
import { m_fight_start_pass_toc } from "./line/m_fight_start_pass_toc";
import { m_fight_times_toc } from "./line/m_fight_times_toc";
import { m_maze_pub_toc } from "./line/m_maze_pub_toc";
import { m_maze_monster_toc } from "./line/m_maze_monster_toc";
import { m_maze_fight_result_toc } from "./line/m_maze_fight_result_toc";
import { m_maze_use_item_toc } from "./line/m_maze_use_item_toc";
import { m_friend_agree_toc } from "./line/m_friend_agree_toc";
import { m_travel_accept_toc } from "./line/m_travel_accept_toc";
import { m_theme_activity_famous_lottery_update_toc } from "./line/m_theme_activity_famous_lottery_update_toc";
import { m_shop_info_toc } from "./line/m_shop_info_toc";
import { m_hero_convert_info_toc } from "./line/m_hero_convert_info_toc";
import { m_family_science_op_toc } from "./line/m_family_science_op_toc";
import { m_stage_breed_upgrade_toc } from "./line/m_stage_breed_upgrade_toc";
import { m_story_tower_battle_fetch_toc } from "./line/m_story_tower_battle_fetch_toc";
import { m_day_acc_pay_gift_info_toc } from "./line/m_day_acc_pay_gift_info_toc";
import { m_friend_operate_toc } from "./line/m_friend_operate_toc";
import { m_daily_copy_process_toc } from "./line/m_daily_copy_process_toc";
import { m_lord_skill_attack_inc_calc_toc } from "./line/m_lord_skill_attack_inc_calc_toc";
import { m_time_achievement_update_toc } from "./line/m_time_achievement_update_toc";
import { m_hero_come_info_toc } from "./line/m_hero_come_info_toc";
import { m_divine_equip_activate_hero_toc } from "./line/m_divine_equip_activate_hero_toc";
import { m_hint_list_toc } from "./line/m_hint_list_toc";
import { m_friend_find_toc } from "./line/m_friend_find_toc";
import { m_daily_copy_enter_toc } from "./line/m_daily_copy_enter_toc";
import { m_time_achievement_share_toc } from "./line/m_time_achievement_share_toc";
import { m_divine_equip_info_toc } from "./line/m_divine_equip_info_toc";
import { m_modular_activity_list_toc } from "./line/m_modular_activity_list_toc";
import { m_qxzl_info_last_battle_toc } from "./line/m_qxzl_info_last_battle_toc";
import { m_hero_skin_active_toc } from "./line/m_hero_skin_active_toc";
import { m_online_reward_fetch_toc } from "./line/m_online_reward_fetch_toc";
import { m_month_fund_info_toc } from "./line/m_month_fund_info_toc";
import { m_general_info_toc } from "./line/m_general_info_toc";
import { m_stage_skill_info_toc } from "./line/m_stage_skill_info_toc";
import { m_family_active_up_toc } from "./line/m_family_active_up_toc";
import { m_login_relogin_toc } from "./line/m_login_relogin_toc";
import { m_login_gen_name_toc } from "./line/m_login_gen_name_toc";
import { m_login_auth_toc } from "./line/m_login_auth_toc";
import { m_lottery_supreme_info_toc } from "./line/m_lottery_supreme_info_toc";
import { m_boat_race_broadcast_toc } from "./line/m_boat_race_broadcast_toc";
import { m_login_activity_fetch_toc } from "./line/m_login_activity_fetch_toc";
import { m_test_tower_auto_sweep_toc } from "./line/m_test_tower_auto_sweep_toc";
import { m_hzzd_info_bet_toc } from "./line/m_hzzd_info_bet_toc";
import { m_hzzd_route_select_info_toc } from "./line/m_hzzd_route_select_info_toc";
import { m_login_re_connect_toc } from "./line/m_login_re_connect_toc";
import { m_hzzd_look_kill_toc } from "./line/m_hzzd_look_kill_toc";
import { m_ranking_worship_toc } from "./line/m_ranking_worship_toc";
import { m_divine_equip_op_toc } from "./line/m_divine_equip_op_toc";
import { m_god_equip_convert_toc } from "./line/m_god_equip_convert_toc";
import { m_family_hongbao_info_toc } from "./line/m_family_hongbao_info_toc";
import { m_first_pay_fetch_toc } from "./line/m_first_pay_fetch_toc";
import { m_stage_copy_set_toc } from "./line/m_stage_copy_set_toc";
import { m_ingenious_plan_info_toc } from "./line/m_ingenious_plan_info_toc";
import { m_td_main_info_toc } from "./line/m_td_main_info_toc";
import { m_modular_activity_dice_info_toc } from "./line/m_modular_activity_dice_info_toc";
import { m_medal_retire_update_toc } from "./line/m_medal_retire_update_toc";
import { m_modular_activity_dice_toc } from "./line/m_modular_activity_dice_toc";
import { m_modular_activity_dice_unlock_toc } from "./line/m_modular_activity_dice_unlock_toc";
import { m_common_error_toc } from "./line/m_common_error_toc";
import { m_qxzl_info_history_toc } from "./line/m_qxzl_info_history_toc";
import { m_online_reward_info_toc } from "./line/m_online_reward_info_toc";
import { m_qqvip_info_toc } from "./line/m_qqvip_info_toc";
import { m_family_set_title_toc } from "./line/m_family_set_title_toc";
import { m_sdk_reward_info_toc } from "./line/m_sdk_reward_info_toc";
import { m_treasure_use_item_toc } from "./line/m_treasure_use_item_toc";
import { m_ranking_reward_toc } from "./line/m_ranking_reward_toc";
import { m_month_fund_update_toc } from "./line/m_month_fund_update_toc";
import { m_family_hongbao_fetch_toc } from "./line/m_family_hongbao_fetch_toc";
import { m_qqvip_fetch_toc } from "./line/m_qqvip_fetch_toc";
import { m_share_single_info_toc } from "./line/m_share_single_info_toc";
import { m_xswh_rank_toc } from "./line/m_xswh_rank_toc";
import { m_playing_preview_info_toc } from "./line/m_playing_preview_info_toc";
import { m_random_pvp_logs_toc } from "./line/m_random_pvp_logs_toc";
import { m_hero_come_update_toc } from "./line/m_hero_come_update_toc";
import { m_common_show_attr_toc } from "./line/m_common_show_attr_toc";
import { m_modular_activity_story_info_toc } from "./line/m_modular_activity_story_info_toc";
import { m_family_hongbao_info_update_toc } from "./line/m_family_hongbao_info_update_toc";
import { m_ingenious_plan_star_toc } from "./line/m_ingenious_plan_star_toc";
import { m_family_sign_info_toc } from "./line/m_family_sign_info_toc";
import { m_ares_palace_log_toc } from "./line/m_ares_palace_log_toc";
import { m_playing_preview_award_toc } from "./line/m_playing_preview_award_toc";
import { m_story_siegelord_info_toc } from "./line/m_story_siegelord_info_toc";
import { m_payment_shop_toc } from "./line/m_payment_shop_toc";
import { m_payment_request_toc } from "./line/m_payment_request_toc";
import { m_worship_do_toc } from "./line/m_worship_do_toc";
import { m_hero_strengthen_info_toc } from "./line/m_hero_strengthen_info_toc";
import { m_payment_buy_toc } from "./line/m_payment_buy_toc";
import { m_payment_shop_info_toc } from "./line/m_payment_shop_info_toc";
import { m_payment_auto_buy_toc } from "./line/m_payment_auto_buy_toc";
import { m_deputy_op_toc } from "./line/m_deputy_op_toc";
import { m_family_members_toc } from "./line/m_family_members_toc";
import { m_tequan_info_toc } from "./line/m_tequan_info_toc";
import { m_modular_activity_customized_gift_select_list_toc } from "./line/m_modular_activity_customized_gift_select_list_toc";
import { m_family_sign_fetch_toc } from "./line/m_family_sign_fetch_toc";
import { m_hero_handbook_list_toc } from "./line/m_hero_handbook_list_toc";
import { m_red_cliff_fight_result_toc } from "./line/m_red_cliff_fight_result_toc";
import { m_god_trial_fight_result_toc } from "./line/m_god_trial_fight_result_toc";
import { m_god_trial_fetch_toc } from "./line/m_god_trial_fetch_toc";
import { m_chat_clean_toc } from "./line/m_chat_clean_toc";
import { m_family_self_toc } from "./line/m_family_self_toc";
import { m_family_sign_do_toc } from "./line/m_family_sign_do_toc";
import { m_csclan_set_title_toc } from "./line/m_csclan_set_title_toc";
import { m_qxzl_info_toc } from "./line/m_qxzl_info_toc";
import { m_fish_fishbowl_attr_toc } from "./line/m_fish_fishbowl_attr_toc";
import { m_large_peak_info_toc } from "./line/m_large_peak_info_toc";
import { m_page_switch_toc } from "./line/m_page_switch_toc";
import { m_hero_handbook_all_fetch_toc } from "./line/m_hero_handbook_all_fetch_toc";
import { m_family_hongbao_mission_send_toc } from "./line/m_family_hongbao_mission_send_toc";
import { m_fuli_token_info_toc } from "./line/m_fuli_token_info_toc";
import { m_payment_pay_result_toc } from "./line/m_payment_pay_result_toc";
import { m_hanging_info_toc } from "./line/m_hanging_info_toc";
import { m_qxzl_info_opp_toc } from "./line/m_qxzl_info_opp_toc";
import { m_modular_activity_six_bless_info_toc } from "./line/m_modular_activity_six_bless_info_toc";
import { m_theme_activity_skin_info_toc } from "./line/m_theme_activity_skin_info_toc";
import { m_large_peak_info_opp_toc } from "./line/m_large_peak_info_opp_toc";
import { m_zero_buy_op_toc } from "./line/m_zero_buy_op_toc";
import { m_family_uplevel_toc } from "./line/m_family_uplevel_toc";
import { m_hanging_reward_toc } from "./line/m_hanging_reward_toc";
import { m_qxzl_info_member_toc } from "./line/m_qxzl_info_member_toc";
import { m_theme_activity_skin_lottery_toc } from "./line/m_theme_activity_skin_lottery_toc";
import { m_large_peak_info_member_toc } from "./line/m_large_peak_info_member_toc";
import { m_hero_zhouyin_upgrade_toc } from "./line/m_hero_zhouyin_upgrade_toc";
import { m_hero_cost_info_toc } from "./line/m_hero_cost_info_toc";
import { m_friend_refresh_toc } from "./line/m_friend_refresh_toc";
import { m_modular_activity_info_toc } from "./line/m_modular_activity_info_toc";
import { m_qxzl_info_look_member_toc } from "./line/m_qxzl_info_look_member_toc";
import { m_qxzl_info_battle_toc } from "./line/m_qxzl_info_battle_toc";
import { m_dominate_pvp_fight_result_toc } from "./line/m_dominate_pvp_fight_result_toc";
import { m_friend_give_toc } from "./line/m_friend_give_toc";
import { m_acc_pay_fetch_toc } from "./line/m_acc_pay_fetch_toc";
import { m_friend_online_toc } from "./line/m_friend_online_toc";
import { m_story_maze_use_item_toc } from "./line/m_story_maze_use_item_toc";
import { m_modular_activity_lottery_tips_toc } from "./line/m_modular_activity_lottery_tips_toc";
import { m_story_maze_report_toc } from "./line/m_story_maze_report_toc";
import { m_test_tower_into_floor_toc } from "./line/m_test_tower_into_floor_toc";
import { m_modular_activity_star_plan_info_toc } from "./line/m_modular_activity_star_plan_info_toc";
import { m_modular_activity_star_plan_update_toc } from "./line/m_modular_activity_star_plan_update_toc";
import { m_modular_activity_general_pass_info_toc } from "./line/m_modular_activity_general_pass_info_toc";
import { m_modular_activity_general_pass_fetch_toc } from "./line/m_modular_activity_general_pass_fetch_toc";
import { m_modular_activity_star_repay_info_toc } from "./line/m_modular_activity_star_repay_info_toc";
import { m_test_tower_fight_result_toc } from "./line/m_test_tower_fight_result_toc";
import { m_modular_activity_fetch_toc } from "./line/m_modular_activity_fetch_toc";
import { m_modular_activity_maze_info_toc } from "./line/m_modular_activity_maze_info_toc";
import { m_modular_activity_maze_start_toc } from "./line/m_modular_activity_maze_start_toc";
import { m_modular_activity_maze_spoils_toc } from "./line/m_modular_activity_maze_spoils_toc";
import { m_modular_activity_maze_fetch_toc } from "./line/m_modular_activity_maze_fetch_toc";
import { m_modular_activity_maze_hero_toc } from "./line/m_modular_activity_maze_hero_toc";
import { m_modular_activity_maze_pub_toc } from "./line/m_modular_activity_maze_pub_toc";
import { m_modular_activity_maze_monster_toc } from "./line/m_modular_activity_maze_monster_toc";
import { m_modular_activity_maze_fight_result_toc } from "./line/m_modular_activity_maze_fight_result_toc";
import { m_modular_activity_maze_use_item_toc } from "./line/m_modular_activity_maze_use_item_toc";
import { m_modular_activity_report_toc } from "./line/m_modular_activity_report_toc";
import { m_modular_activity_maze_auto_rolling_start_toc } from "./line/m_modular_activity_maze_auto_rolling_start_toc";
import { m_modular_activity_maze_auto_rolling_end_toc } from "./line/m_modular_activity_maze_auto_rolling_end_toc";
import { m_modular_activity_seven_goal_info_toc } from "./line/m_modular_activity_seven_goal_info_toc";
import { m_modular_activity_seven_goal_update_toc } from "./line/m_modular_activity_seven_goal_update_toc";
import { m_modular_activity_seven_goal_fetch_toc } from "./line/m_modular_activity_seven_goal_fetch_toc";
import { m_modular_activity_seven_goal_gift_toc } from "./line/m_modular_activity_seven_goal_gift_toc";
import { m_modular_activity_hunt_info_toc } from "./line/m_modular_activity_hunt_info_toc";
import { m_modular_activity_hunt_toc } from "./line/m_modular_activity_hunt_toc";
import { m_modular_activity_hunt_logs_toc } from "./line/m_modular_activity_hunt_logs_toc";
import { m_modular_activity_hunt_refresh_toc } from "./line/m_modular_activity_hunt_refresh_toc";
import { m_modular_activity_carnival_toc } from "./line/m_modular_activity_carnival_toc";
import { m_modular_activity_login_info_toc } from "./line/m_modular_activity_login_info_toc";
import { m_theme_activity_famous_lottery_info_toc } from "./line/m_theme_activity_famous_lottery_info_toc";
import { m_hzzd_info_opp_toc } from "./line/m_hzzd_info_opp_toc";
import { m_daily_gift_info_toc } from "./line/m_daily_gift_info_toc";
import { m_modular_activity_dice_boss_toc } from "./line/m_modular_activity_dice_boss_toc";
import { m_modular_activity_holiday_welfare_info_toc } from "./line/m_modular_activity_holiday_welfare_info_toc";
import { m_modular_activity_holiday_welfare_update_toc } from "./line/m_modular_activity_holiday_welfare_update_toc";
import { m_modular_activity_sign_info_toc } from "./line/m_modular_activity_sign_info_toc";
import { m_modular_activity_sign_update_toc } from "./line/m_modular_activity_sign_update_toc";
import { m_modular_activity_hero_challenge_info_toc } from "./line/m_modular_activity_hero_challenge_info_toc";
import { m_modular_activity_hero_challenge_report_toc } from "./line/m_modular_activity_hero_challenge_report_toc";
import { m_modular_activity_preview_toc } from "./line/m_modular_activity_preview_toc";
import { m_modular_activity_preview_info_toc } from "./line/m_modular_activity_preview_info_toc";
import { m_boat_race_role_items_toc } from "./line/m_boat_race_role_items_toc";
import { m_modular_activity_story_group_toc } from "./line/m_modular_activity_story_group_toc";
import { m_modular_activity_heaven_give_info_toc } from "./line/m_modular_activity_heaven_give_info_toc";
import { m_modular_activity_customized_gift_select_toc } from "./line/m_modular_activity_customized_gift_select_toc";
import { m_hzzd_battle_info_toc } from "./line/m_hzzd_battle_info_toc";
import { m_modular_activity_brick_info_toc } from "./line/m_modular_activity_brick_info_toc";
import { m_modular_activity_brick_toc } from "./line/m_modular_activity_brick_toc";
import { m_modular_activity_bless_info_toc } from "./line/m_modular_activity_bless_info_toc";
import { m_modular_activity_target_info_toc } from "./line/m_modular_activity_target_info_toc";
import { m_modular_activity_wall_info_toc } from "./line/m_modular_activity_wall_info_toc";
import { m_modular_activity_exchange_info_toc } from "./line/m_modular_activity_exchange_info_toc";
import { m_modular_activity_strategy_info_toc } from "./line/m_modular_activity_strategy_info_toc";
import { m_modular_activity_lottery_target_info_toc } from "./line/m_modular_activity_lottery_target_info_toc";
import { m_login_chose_toc } from "./line/m_login_chose_toc";
import { m_modular_activity_day_shop_toc } from "./line/m_modular_activity_day_shop_toc";
import { m_simp_mission_list_toc } from "./line/m_simp_mission_list_toc";
import { m_modular_activity_festival_wish_info_toc } from "./line/m_modular_activity_festival_wish_info_toc";
import { m_modular_activity_festival_wish_toc } from "./line/m_modular_activity_festival_wish_toc";
import { m_sys_concern_info_toc } from "./line/m_sys_concern_info_toc";
import { m_week_target_info_toc } from "./line/m_week_target_info_toc";
import { m_family_audit_limit_toc } from "./line/m_family_audit_limit_toc";
import { m_treasure_result_toc } from "./line/m_treasure_result_toc";
import { m_qxzl_info_bet_toc } from "./line/m_qxzl_info_bet_toc";
import { m_fish_replace_toc } from "./line/m_fish_replace_toc";
import { m_modular_activity_update_fetch_toc } from "./line/m_modular_activity_update_fetch_toc";
import { m_ranking_simple_toc } from "./line/m_ranking_simple_toc";
import { m_fuli_sign_fetch_toc } from "./line/m_fuli_sign_fetch_toc";
import { m_pass_behead_info_toc } from "./line/m_pass_behead_info_toc";
import { m_wars_init_personal_toc } from "./line/m_wars_init_personal_toc";
import { m_story_maze_info_toc } from "./line/m_story_maze_info_toc";
import { m_tax_silver_fetch_toc } from "./line/m_tax_silver_fetch_toc";
import { m_vip_kefu_qq_toc } from "./line/m_vip_kefu_qq_toc";
import { m_general_pass_gift_toc } from "./line/m_general_pass_gift_toc";
import { m_letter_send_toc } from "./line/m_letter_send_toc";
import { m_tax_forage_fetch_toc } from "./line/m_tax_forage_fetch_toc";
import { m_large_peak_info_battle_toc } from "./line/m_large_peak_info_battle_toc";
import { m_large_peak_info_bet_toc } from "./line/m_large_peak_info_bet_toc";
import { m_large_peak_info_history_toc } from "./line/m_large_peak_info_history_toc";
import { m_large_peak_personal_info_toc } from "./line/m_large_peak_personal_info_toc";
import { m_large_peak_info_get_msg_toc } from "./line/m_large_peak_info_get_msg_toc";
import { m_modular_activity_shop_toc } from "./line/m_modular_activity_shop_toc";
import { m_pay_gift_fetch_toc } from "./line/m_pay_gift_fetch_toc";
import { m_acc_pay_info_toc } from "./line/m_acc_pay_info_toc";
import { m_guandu_info_toc } from "./line/m_guandu_info_toc";
import { m_boat_peak_info_toc } from "./line/m_boat_peak_info_toc";
import { m_test_tower_skip_toc } from "./line/m_test_tower_skip_toc";
import { m_qxzl_info_last_rank_toc } from "./line/m_qxzl_info_last_rank_toc";
import { m_hzzd_team_info_toc } from "./line/m_hzzd_team_info_toc";
import { m_modular_activity_lottery_start_toc } from "./line/m_modular_activity_lottery_start_toc";
import { m_lottery_start_toc } from "./line/m_lottery_start_toc";
import { m_guandu_op_toc } from "./line/m_guandu_op_toc";
import { m_worship_online_toc } from "./line/m_worship_online_toc";
import { m_boat_peak_shop_toc } from "./line/m_boat_peak_shop_toc";
import { m_boat_peak_opp_toc } from "./line/m_boat_peak_opp_toc";
import { m_dominate_pvp_op_toc } from "./line/m_dominate_pvp_op_toc";
import { m_item_use_toc } from "./line/m_item_use_toc";
import { m_login_give_name_toc } from "./line/m_login_give_name_toc";
import { m_modular_activity_famous_lottery_info_toc } from "./line/m_modular_activity_famous_lottery_info_toc";
import { m_lottery_info_toc } from "./line/m_lottery_info_toc";
import { m_recommend_lineup_info_toc } from "./line/m_recommend_lineup_info_toc";
import { m_title_list_toc } from "./line/m_title_list_toc";
import { m_worship_info_toc } from "./line/m_worship_info_toc";
import { m_lcqs_chapter_info_toc } from "./line/m_lcqs_chapter_info_toc";
import { m_dominate_pvp_info_toc } from "./line/m_dominate_pvp_info_toc";
import { m_building_info_toc } from "./line/m_building_info_toc";
import { m_multi_lineup_set_toc } from "./line/m_multi_lineup_set_toc";
import { m_lottery_score_fetch_toc } from "./line/m_lottery_score_fetch_toc";
import { m_title_load_toc } from "./line/m_title_load_toc";
import { m_story_maze_spoils_toc } from "./line/m_story_maze_spoils_toc";
import { m_seven_goal_info_toc } from "./line/m_seven_goal_info_toc";
import { m_boat_peak_look_member_toc } from "./line/m_boat_peak_look_member_toc";
import { m_dominate_pvp_match_toc } from "./line/m_dominate_pvp_match_toc";
import { m_multi_lineup_list_toc } from "./line/m_multi_lineup_list_toc";
import { m_building_op_toc } from "./line/m_building_op_toc";
import { m_dominate_pvp_update_toc } from "./line/m_dominate_pvp_update_toc";
import { m_sdk_sns_reward_info_toc } from "./line/m_sdk_sns_reward_info_toc";
import { m_star_plan_info_toc } from "./line/m_star_plan_info_toc";
import { m_guide_finish_toc } from "./line/m_guide_finish_toc";
import { m_cycle_activity_update_toc } from "./line/m_cycle_activity_update_toc";
import { m_war_flag_exchange_toc } from "./line/m_war_flag_exchange_toc";
import { m_guide_get_hero_toc } from "./line/m_guide_get_hero_toc";
import { m_main_battle_fetch_pass_toc } from "./line/m_main_battle_fetch_pass_toc";
import { m_gmcmd_panel_toc } from "./line/m_gmcmd_panel_toc";
import { m_lottery_nation_start_toc } from "./line/m_lottery_nation_start_toc";
import { m_title_update_toc } from "./line/m_title_update_toc";
import { m_wars_walk_list_toc } from "./line/m_wars_walk_list_toc";
import { m_seven_goal_update_toc } from "./line/m_seven_goal_update_toc";
import { m_ingenious_plan_update_toc } from "./line/m_ingenious_plan_update_toc";
import { m_lcqs_chapter_list_toc } from "./line/m_lcqs_chapter_list_toc";
import { m_dominate_pvp_logs_toc } from "./line/m_dominate_pvp_logs_toc";
import { m_xswh_info_toc } from "./line/m_xswh_info_toc";
import { m_hunt_refresh_toc } from "./line/m_hunt_refresh_toc";
import { m_fish_update_toc } from "./line/m_fish_update_toc";
import { m_login_add_toc } from "./line/m_login_add_toc";
import { m_login_fetch_vip_toc } from "./line/m_login_fetch_vip_toc";
import { m_hero_recycle_preview_toc } from "./line/m_hero_recycle_preview_toc";
import { m_fish_shop_toc } from "./line/m_fish_shop_toc";
import { m_seven_goal_fetch_toc } from "./line/m_seven_goal_fetch_toc";
import { m_hero_set_lineup_toc } from "./line/m_hero_set_lineup_toc";
import { m_rank_activity_info_toc } from "./line/m_rank_activity_info_toc";
import { m_achievement_info_toc } from "./line/m_achievement_info_toc";
import { m_xswh_fight_result_toc } from "./line/m_xswh_fight_result_toc";
import { m_fish_up_official_toc } from "./line/m_fish_up_official_toc";
import { m_sys_use_times_info_toc } from "./line/m_sys_use_times_info_toc";
import { m_hero_lineup_info_toc } from "./line/m_hero_lineup_info_toc";
import { m_login_filter_fun_toc } from "./line/m_login_filter_fun_toc";
import { m_role_score_update_toc } from "./line/m_role_score_update_toc";
import { m_team_xswh_rank_toc } from "./line/m_team_xswh_rank_toc";
import { m_seven_goal_gift_toc } from "./line/m_seven_goal_gift_toc";
import { m_hero_bag_expansion_toc } from "./line/m_hero_bag_expansion_toc";
import { m_boat_peak_bet_toc } from "./line/m_boat_peak_bet_toc";
import { m_item_compose_toc } from "./line/m_item_compose_toc";
import { m_hero_recycle_all_toc } from "./line/m_hero_recycle_all_toc";
import { m_sys_use_times_update_toc } from "./line/m_sys_use_times_update_toc";
import { m_master_card_info_toc } from "./line/m_master_card_info_toc";
import { m_stage_copy_sweep_toc } from "./line/m_stage_copy_sweep_toc";
import { m_hero_recycle_gains_item_toc } from "./line/m_hero_recycle_gains_item_toc";
import { m_family_list_toc } from "./line/m_family_list_toc";
import { m_lottery_supreme_start_toc } from "./line/m_lottery_supreme_start_toc";
import { m_story_maze_fight_result_toc } from "./line/m_story_maze_fight_result_toc";
import { m_boat_peak_his_toc } from "./line/m_boat_peak_his_toc";
import { m_share_fetch_toc } from "./line/m_share_fetch_toc";
import { m_icon_list_toc } from "./line/m_icon_list_toc";
import { m_xswh_best_rank_toc } from "./line/m_xswh_best_rank_toc";
import { m_cross_ladder_opp_info_toc } from "./line/m_cross_ladder_opp_info_toc";
import { m_test_tower_process_toc } from "./line/m_test_tower_process_toc";
import { m_cycle_activity_gift_toc } from "./line/m_cycle_activity_gift_toc";
import { m_mission_shop_info_toc } from "./line/m_mission_shop_info_toc";
import { m_buy_times_update_toc } from "./line/m_buy_times_update_toc";
import { m_god_weapon_info_toc } from "./line/m_god_weapon_info_toc";
import { m_god_weapon_upgrade_toc } from "./line/m_god_weapon_upgrade_toc";
import { m_bingfu_operate_toc } from "./line/m_bingfu_operate_toc";
import { m_csc_fmsolo_state_toc } from "./line/m_csc_fmsolo_state_toc";
import { m_bingfu_compose_toc } from "./line/m_bingfu_compose_toc";
import { m_csc_fmsolo_shop_lv_toc } from "./line/m_csc_fmsolo_shop_lv_toc";
import { m_bingfu_refine_toc } from "./line/m_bingfu_refine_toc";
import { m_icon_switch_toc } from "./line/m_icon_switch_toc";
import { m_pay_gift_info_toc } from "./line/m_pay_gift_info_toc";
import { m_test_tower_fetch_toc } from "./line/m_test_tower_fetch_toc";
import { m_buy_times_info_toc } from "./line/m_buy_times_info_toc";
import { m_master_talent_science_info_toc } from "./line/m_master_talent_science_info_toc";
import { m_fight_share_operate_toc } from "./line/m_fight_share_operate_toc";
import { m_peak_info_look_member_toc } from "./line/m_peak_info_look_member_toc";
import { m_csc_fmsolo_fetch_toc } from "./line/m_csc_fmsolo_fetch_toc";
import { m_treasure_role_info_toc } from "./line/m_treasure_role_info_toc";
import { m_share_info_toc } from "./line/m_share_info_toc";
import { m_role_rename_toc } from "./line/m_role_rename_toc";
import { m_role_score_list_toc } from "./line/m_role_score_list_toc";
import { m_icon_switch_list_toc } from "./line/m_icon_switch_list_toc";
import { m_hanging_quick_toc } from "./line/m_hanging_quick_toc";
import { m_fight_start_toc } from "./line/m_fight_start_toc";
import { m_master_card_update_toc } from "./line/m_master_card_update_toc";
import { m_family_active_info_toc } from "./line/m_family_active_info_toc";
import { m_fight_share_stat_toc } from "./line/m_fight_share_stat_toc";
import { m_hero_cheer_info_toc } from "./line/m_hero_cheer_info_toc";
import { m_divine_equip_compose_toc } from "./line/m_divine_equip_compose_toc";
import { m_role_rename_info_toc } from "./line/m_role_rename_info_toc";
import { m_arena_info_toc } from "./line/m_arena_info_toc";
import { m_fight_finish_toc } from "./line/m_fight_finish_toc";
import { m_family_task_info_toc } from "./line/m_family_task_info_toc";
import { m_option_lottery_start_toc } from "./line/m_option_lottery_start_toc";
import { m_hero_cheer_op_toc } from "./line/m_hero_cheer_op_toc";
import { m_hongbao_info_toc } from "./line/m_hongbao_info_toc";
import { m_team_xswh_best_rank_toc } from "./line/m_team_xswh_best_rank_toc";
import { m_role_acclogin_toc } from "./line/m_role_acclogin_toc";
import { m_test_tower_sweep_toc } from "./line/m_test_tower_sweep_toc";
import { m_family_task_update_toc } from "./line/m_family_task_update_toc";
import { m_fuli_sign_info_toc } from "./line/m_fuli_sign_info_toc";
import { m_option_lottery_info_toc } from "./line/m_option_lottery_info_toc";
import { m_hzzd_info_toc } from "./line/m_hzzd_info_toc";
import { m_family_create_toc } from "./line/m_family_create_toc";
import { m_xswh_fetch_toc } from "./line/m_xswh_fetch_toc";
import { m_eight_login_fetch_toc } from "./line/m_eight_login_fetch_toc";
import { m_role_look_profile_toc } from "./line/m_role_look_profile_toc";
/**
  *开发者：成畅
  *修改时间:
  *自动生成的代码，请勿修改
**/
export class SocketCommand {
    static getProtoNameByCommand(command) {
        return SocketCommand.protoMap[command];
    }
    static init() {
        if (SocketCommand.protoMap) {
            return;
        }
        SocketCommand.protoMap = {};
        SocketCommand.protoMap[10506240] = "proto.line.m_treasure_info_toc";
        ClassUtils.regClass("proto.line.m_treasure_info_toc", m_treasure_info_toc);
        SocketCommand.protoMap[6303744] = "proto.line.m_family_boss_info_toc";
        ClassUtils.regClass("proto.line.m_family_boss_info_toc", m_family_boss_info_toc);
        SocketCommand.protoMap[7354370] = "proto.line.m_world_boss_fight_result_toc";
        ClassUtils.regClass("proto.line.m_world_boss_fight_result_toc", m_world_boss_fight_result_toc);
        SocketCommand.protoMap[3151875] = "proto.line.m_travel_speed_toc";
        ClassUtils.regClass("proto.line.m_travel_speed_toc", m_travel_speed_toc);
        SocketCommand.protoMap[8404996] = "proto.line.m_peak_info_battle_toc";
        ClassUtils.regClass("proto.line.m_peak_info_battle_toc", m_peak_info_battle_toc);
        SocketCommand.protoMap[6303749] = "proto.line.m_family_boss_fight_result_toc";
        ClassUtils.regClass("proto.line.m_family_boss_fight_result_toc", m_family_boss_fight_result_toc);
        SocketCommand.protoMap[11556870] = "proto.line.m_csclan_solo_shop_lv_toc";
        ClassUtils.regClass("proto.line.m_csclan_solo_shop_lv_toc", m_csclan_solo_shop_lv_toc);
        SocketCommand.protoMap[9455617] = "proto.line.m_team_info_my_team_toc";
        ClassUtils.regClass("proto.line.m_team_info_my_team_toc", m_team_info_my_team_toc);
        SocketCommand.protoMap[10506248] = "proto.line.m_treasure_refresh_toc";
        ClassUtils.regClass("proto.line.m_treasure_refresh_toc", m_treasure_refresh_toc);
        SocketCommand.protoMap[8405001] = "proto.line.m_peak_info_get_msg_toc";
        ClassUtils.regClass("proto.line.m_peak_info_get_msg_toc", m_peak_info_get_msg_toc);
        SocketCommand.protoMap[8405002] = "proto.line.m_peak_personal_info_toc";
        ClassUtils.regClass("proto.line.m_peak_personal_info_toc", m_peak_personal_info_toc);
        SocketCommand.protoMap[10506251] = "proto.line.m_treasure_gift_active_toc";
        ClassUtils.regClass("proto.line.m_treasure_gift_active_toc", m_treasure_gift_active_toc);
        SocketCommand.protoMap[8011009] = "proto.line.m_hzzd_route_info_toc";
        ClassUtils.regClass("proto.line.m_hzzd_route_info_toc", m_hzzd_route_info_toc);
        SocketCommand.protoMap[1050626] = "proto.line.m_goods_show_goods_toc";
        ClassUtils.regClass("proto.line.m_goods_show_goods_toc", m_goods_show_goods_toc);
        SocketCommand.protoMap[11753858] = "proto.line.m_hongbao_fetch_toc";
        ClassUtils.regClass("proto.line.m_hongbao_fetch_toc", m_hongbao_fetch_toc);
        SocketCommand.protoMap[10506243] = "proto.line.m_treasure_log_toc";
        ClassUtils.regClass("proto.line.m_treasure_log_toc", m_treasure_log_toc);
        SocketCommand.protoMap[8667649] = "proto.line.m_dawanka_fetch_toc";
        ClassUtils.regClass("proto.line.m_dawanka_fetch_toc", m_dawanka_fetch_toc);
        SocketCommand.protoMap[3151876] = "proto.line.m_travel_fetch_toc";
        ClassUtils.regClass("proto.line.m_travel_fetch_toc", m_travel_fetch_toc);
        SocketCommand.protoMap[11556869] = "proto.line.m_csclan_solo_box_toc";
        ClassUtils.regClass("proto.line.m_csclan_solo_box_toc", m_csclan_solo_box_toc);
        SocketCommand.protoMap[5909766] = "proto.line.m_test_tower_op_toc";
        ClassUtils.regClass("proto.line.m_test_tower_op_toc", m_test_tower_op_toc);
        SocketCommand.protoMap[7879681] = "proto.line.m_random_pvp_info_toc";
        ClassUtils.regClass("proto.line.m_random_pvp_info_toc", m_random_pvp_info_toc);
        SocketCommand.protoMap[10506246] = "proto.line.m_treasure_cal_time_toc";
        ClassUtils.regClass("proto.line.m_treasure_cal_time_toc", m_treasure_cal_time_toc);
        SocketCommand.protoMap[11294214] = "proto.line.m_dominate_pvp_new_season_toc";
        ClassUtils.regClass("proto.line.m_dominate_pvp_new_season_toc", m_dominate_pvp_new_season_toc);
        SocketCommand.protoMap[4793472] = "proto.line.m_level_gift_info_toc";
        ClassUtils.regClass("proto.line.m_level_gift_info_toc", m_level_gift_info_toc);
        SocketCommand.protoMap[11031559] = "proto.line.m_mock_pvp_ready_toc";
        ClassUtils.regClass("proto.line.m_mock_pvp_ready_toc", m_mock_pvp_ready_toc);
        SocketCommand.protoMap[8536321] = "proto.line.m_cross_ladder_info_toc";
        ClassUtils.regClass("proto.line.m_cross_ladder_info_toc", m_cross_ladder_info_toc);
        SocketCommand.protoMap[9061632] = "proto.line.m_mini_game_get_str_toc";
        ClassUtils.regClass("proto.line.m_mini_game_get_str_toc", m_mini_game_get_str_toc);
        SocketCommand.protoMap[8142355] = "proto.line.m_modular_activity_war_log_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_war_log_info_toc", m_modular_activity_war_log_info_toc);
        SocketCommand.protoMap[9455624] = "proto.line.m_team_lineup_set_toc";
        ClassUtils.regClass("proto.line.m_team_lineup_set_toc", m_team_lineup_set_toc);
        SocketCommand.protoMap[11031561] = "proto.line.m_mock_pvp_contest_chat_toc";
        ClassUtils.regClass("proto.line.m_mock_pvp_contest_chat_toc", m_mock_pvp_contest_chat_toc);
        SocketCommand.protoMap[1575936] = "proto.line.m_welfare_activate_code_toc";
        ClassUtils.regClass("proto.line.m_welfare_activate_code_toc", m_welfare_activate_code_toc);
        SocketCommand.protoMap[2626570] = "proto.line.m_main_battle_auto_toc";
        ClassUtils.regClass("proto.line.m_main_battle_auto_toc", m_main_battle_auto_toc);
        SocketCommand.protoMap[5909767] = "proto.line.m_test_tower_continuous_fight_result_toc";
        ClassUtils.regClass("proto.line.m_test_tower_continuous_fight_result_toc", m_test_tower_continuous_fight_result_toc);
        SocketCommand.protoMap[6303745] = "proto.line.m_family_boss_sweep_toc";
        ClassUtils.regClass("proto.line.m_family_boss_sweep_toc", m_family_boss_sweep_toc);
        SocketCommand.protoMap[3217540] = "proto.line.m_family_task_gift_toc";
        ClassUtils.regClass("proto.line.m_family_task_gift_toc", m_family_task_gift_toc);
        SocketCommand.protoMap[11031563] = "proto.line.m_mock_pvp_heat_toc";
        ClassUtils.regClass("proto.line.m_mock_pvp_heat_toc", m_mock_pvp_heat_toc);
        SocketCommand.protoMap[10834565] = "proto.line.m_fish_lineup_list_toc";
        ClassUtils.regClass("proto.line.m_fish_lineup_list_toc", m_fish_lineup_list_toc);
        SocketCommand.protoMap[1050624] = "proto.line.m_goods_list_toc";
        ClassUtils.regClass("proto.line.m_goods_list_toc", m_goods_list_toc);
        SocketCommand.protoMap[4793473] = "proto.line.m_level_gift_fetch_toc";
        ClassUtils.regClass("proto.line.m_level_gift_fetch_toc", m_level_gift_fetch_toc);
        SocketCommand.protoMap[10506252] = "proto.line.m_treasure_worker_active_toc";
        ClassUtils.regClass("proto.line.m_treasure_worker_active_toc", m_treasure_worker_active_toc);
        SocketCommand.protoMap[5318784] = "proto.line.m_rent_hero_list_toc";
        ClassUtils.regClass("proto.line.m_rent_hero_list_toc", m_rent_hero_list_toc);
        SocketCommand.protoMap[10834560] = "proto.line.m_fish_info_toc";
        ClassUtils.regClass("proto.line.m_fish_info_toc", m_fish_info_toc);
        SocketCommand.protoMap[10506253] = "proto.line.m_treasure_fetch_toc";
        ClassUtils.regClass("proto.line.m_treasure_fetch_toc", m_treasure_fetch_toc);
        SocketCommand.protoMap[10506254] = "proto.line.m_treasure_worker_info_toc";
        ClassUtils.regClass("proto.line.m_treasure_worker_info_toc", m_treasure_worker_info_toc);
        SocketCommand.protoMap[3742852] = "proto.line.m_fuli_yueka_fetch_toc";
        ClassUtils.regClass("proto.line.m_fuli_yueka_fetch_toc", m_fuli_yueka_fetch_toc);
        SocketCommand.protoMap[1050625] = "proto.line.m_goods_update_toc";
        ClassUtils.regClass("proto.line.m_goods_update_toc", m_goods_update_toc);
        SocketCommand.protoMap[8011012] = "proto.line.m_hzzd_fetch_toc";
        ClassUtils.regClass("proto.line.m_hzzd_fetch_toc", m_hzzd_fetch_toc);
        SocketCommand.protoMap[8536323] = "proto.line.m_cross_ladder_list_toc";
        ClassUtils.regClass("proto.line.m_cross_ladder_list_toc", m_cross_ladder_list_toc);
        SocketCommand.protoMap[5844096] = "proto.line.m_ares_palace_info_toc";
        ClassUtils.regClass("proto.line.m_ares_palace_info_toc", m_ares_palace_info_toc);
        SocketCommand.protoMap[9586945] = "proto.line.m_team_xswh_fight_result_toc";
        ClassUtils.regClass("proto.line.m_team_xswh_fight_result_toc", m_team_xswh_fight_result_toc);
        SocketCommand.protoMap[10112256] = "proto.line.m_boat_race_info_toc";
        ClassUtils.regClass("proto.line.m_boat_race_info_toc", m_boat_race_info_toc);
        SocketCommand.protoMap[9586948] = "proto.line.m_team_xswh_fetch_toc";
        ClassUtils.regClass("proto.line.m_team_xswh_fetch_toc", m_team_xswh_fetch_toc);
        SocketCommand.protoMap[4268160] = "proto.line.m_vip_info_toc";
        ClassUtils.regClass("proto.line.m_vip_info_toc", m_vip_info_toc);
        SocketCommand.protoMap[7420032] = "proto.line.m_pass_check_info_toc";
        ClassUtils.regClass("proto.line.m_pass_check_info_toc", m_pass_check_info_toc);
        SocketCommand.protoMap[4268161] = "proto.line.m_vip_buy_gift_toc";
        ClassUtils.regClass("proto.line.m_vip_buy_gift_toc", m_vip_buy_gift_toc);
        SocketCommand.protoMap[1116290] = "proto.line.m_role_base_reload_toc";
        ClassUtils.regClass("proto.line.m_role_base_reload_toc", m_role_base_reload_toc);
        SocketCommand.protoMap[3217539] = "proto.line.m_family_task_reward_toc";
        ClassUtils.regClass("proto.line.m_family_task_reward_toc", m_family_task_reward_toc);
        SocketCommand.protoMap[11622532] = "proto.line.m_wars_update_city_toc";
        ClassUtils.regClass("proto.line.m_wars_update_city_toc", m_wars_update_city_toc);
        SocketCommand.protoMap[11622533] = "proto.line.m_wars_city_info_toc";
        ClassUtils.regClass("proto.line.m_wars_city_info_toc", m_wars_city_info_toc);
        SocketCommand.protoMap[5318786] = "proto.line.m_rent_hero_look_toc";
        ClassUtils.regClass("proto.line.m_rent_hero_look_toc", m_rent_hero_look_toc);
        SocketCommand.protoMap[1116295] = "proto.line.m_role_setting_list_toc";
        ClassUtils.regClass("proto.line.m_role_setting_list_toc", m_role_setting_list_toc);
        SocketCommand.protoMap[11622536] = "proto.line.m_wars_team_op_toc";
        ClassUtils.regClass("proto.line.m_wars_team_op_toc", m_wars_team_op_toc);
        SocketCommand.protoMap[10571913] = "proto.line.m_lord_op_toc";
        ClassUtils.regClass("proto.line.m_lord_op_toc", m_lord_op_toc);
        SocketCommand.protoMap[10571914] = "proto.line.m_lord_equip_compose_toc";
        ClassUtils.regClass("proto.line.m_lord_equip_compose_toc", m_lord_equip_compose_toc);
        SocketCommand.protoMap[10571915] = "proto.line.m_lord_treasure_op_toc";
        ClassUtils.regClass("proto.line.m_lord_treasure_op_toc", m_lord_treasure_op_toc);
        SocketCommand.protoMap[11622540] = "proto.line.m_wars_act_info_toc";
        ClassUtils.regClass("proto.line.m_wars_act_info_toc", m_wars_act_info_toc);
        SocketCommand.protoMap[1116301] = "proto.line.m_role_setting_toc";
        ClassUtils.regClass("proto.line.m_role_setting_toc", m_role_setting_toc);
        SocketCommand.protoMap[10637568] = "proto.line.m_csclan_list_toc";
        ClassUtils.regClass("proto.line.m_csclan_list_toc", m_csclan_list_toc);
        SocketCommand.protoMap[1116303] = "proto.line.m_role_look_hero_toc";
        ClassUtils.regClass("proto.line.m_role_look_hero_toc", m_role_look_hero_toc);
        SocketCommand.protoMap[1116304] = "proto.line.m_role_look_hero_attr_source_toc";
        ClassUtils.regClass("proto.line.m_role_look_hero_attr_source_toc", m_role_look_hero_attr_source_toc);
        SocketCommand.protoMap[1116305] = "proto.line.m_role_setting2_toc";
        ClassUtils.regClass("proto.line.m_role_setting2_toc", m_role_setting2_toc);
        SocketCommand.protoMap[1116306] = "proto.line.m_role_look_sdk_profile_toc";
        ClassUtils.regClass("proto.line.m_role_look_sdk_profile_toc", m_role_look_sdk_profile_toc);
        SocketCommand.protoMap[1116307] = "proto.line.m_role_look_lord_toc";
        ClassUtils.regClass("proto.line.m_role_look_lord_toc", m_role_look_lord_toc);
        SocketCommand.protoMap[11622548] = "proto.line.m_wars_kill_info_toc";
        ClassUtils.regClass("proto.line.m_wars_kill_info_toc", m_wars_kill_info_toc);
        SocketCommand.protoMap[11622549] = "proto.line.m_wars_select_army_toc";
        ClassUtils.regClass("proto.line.m_wars_select_army_toc", m_wars_select_army_toc);
        SocketCommand.protoMap[11622551] = "proto.line.m_wars_info_get_msg_toc";
        ClassUtils.regClass("proto.line.m_wars_info_get_msg_toc", m_wars_info_get_msg_toc);
        SocketCommand.protoMap[11622552] = "proto.line.m_wars_info_nty_rank_data_toc";
        ClassUtils.regClass("proto.line.m_wars_info_nty_rank_data_toc", m_wars_info_nty_rank_data_toc);
        SocketCommand.protoMap[11622553] = "proto.line.m_wars_event_toc";
        ClassUtils.regClass("proto.line.m_wars_event_toc", m_wars_event_toc);
        SocketCommand.protoMap[11622554] = "proto.line.m_wars_make_path_toc";
        ClassUtils.regClass("proto.line.m_wars_make_path_toc", m_wars_make_path_toc);
        SocketCommand.protoMap[11622555] = "proto.line.m_wars_init_camp_toc";
        ClassUtils.regClass("proto.line.m_wars_init_camp_toc", m_wars_init_camp_toc);
        SocketCommand.protoMap[11622556] = "proto.line.m_wars_update_role_toc";
        ClassUtils.regClass("proto.line.m_wars_update_role_toc", m_wars_update_role_toc);
        SocketCommand.protoMap[9455618] = "proto.line.m_team_info_teams_toc";
        ClassUtils.regClass("proto.line.m_team_info_teams_toc", m_team_info_teams_toc);
        SocketCommand.protoMap[11622558] = "proto.line.m_wars_daily_report_toc";
        ClassUtils.regClass("proto.line.m_wars_daily_report_toc", m_wars_daily_report_toc);
        SocketCommand.protoMap[1575938] = "proto.line.m_welfare_sdk_share_info_toc";
        ClassUtils.regClass("proto.line.m_welfare_sdk_share_info_toc", m_welfare_sdk_share_info_toc);
        SocketCommand.protoMap[2101249] = "proto.line.m_daily_mission_gift_toc";
        ClassUtils.regClass("proto.line.m_daily_mission_gift_toc", m_daily_mission_gift_toc);
        SocketCommand.protoMap[2626560] = "proto.line.m_main_battle_info_toc";
        ClassUtils.regClass("proto.line.m_main_battle_info_toc", m_main_battle_info_toc);
        SocketCommand.protoMap[6369409] = "proto.line.m_red_cliff_sweep_toc";
        ClassUtils.regClass("proto.line.m_red_cliff_sweep_toc", m_red_cliff_sweep_toc);
        SocketCommand.protoMap[10112258] = "proto.line.m_boat_race_update_toc";
        ClassUtils.regClass("proto.line.m_boat_race_update_toc", m_boat_race_update_toc);
        SocketCommand.protoMap[10637569] = "proto.line.m_csclan_create_toc";
        ClassUtils.regClass("proto.line.m_csclan_create_toc", m_csclan_create_toc);
        SocketCommand.protoMap[1116288] = "proto.line.m_role_level_up_toc";
        ClassUtils.regClass("proto.line.m_role_level_up_toc", m_role_level_up_toc);
        SocketCommand.protoMap[2626561] = "proto.line.m_main_battle_fight_result_toc";
        ClassUtils.regClass("proto.line.m_main_battle_fight_result_toc", m_main_battle_fight_result_toc);
        SocketCommand.protoMap[3151872] = "proto.line.m_travel_info_toc";
        ClassUtils.regClass("proto.line.m_travel_info_toc", m_travel_info_toc);
        SocketCommand.protoMap[6894721] = "proto.line.m_progress_gift_buy_toc";
        ClassUtils.regClass("proto.line.m_progress_gift_buy_toc", m_progress_gift_buy_toc);
        SocketCommand.protoMap[6303746] = "proto.line.m_family_boss_update_toc";
        ClassUtils.regClass("proto.line.m_family_boss_update_toc", m_family_boss_update_toc);
        SocketCommand.protoMap[11622534] = "proto.line.m_wars_operate_toc";
        ClassUtils.regClass("proto.line.m_wars_operate_toc", m_wars_operate_toc);
        SocketCommand.protoMap[11688192] = "proto.line.m_itinerant_shop_info_toc";
        ClassUtils.regClass("proto.line.m_itinerant_shop_info_toc", m_itinerant_shop_info_toc);
        SocketCommand.protoMap[11753860] = "proto.line.m_hongbao_fetch_update_toc";
        ClassUtils.regClass("proto.line.m_hongbao_fetch_update_toc", m_hongbao_fetch_update_toc);
        SocketCommand.protoMap[8864643] = "proto.line.m_medal_update_toc";
        ClassUtils.regClass("proto.line.m_medal_update_toc", m_medal_update_toc);
        SocketCommand.protoMap[7288704] = "proto.line.m_csc_fmsolo_info_toc";
        ClassUtils.regClass("proto.line.m_csc_fmsolo_info_toc", m_csc_fmsolo_info_toc);
        SocketCommand.protoMap[3151873] = "proto.line.m_travel_refresh_toc";
        ClassUtils.regClass("proto.line.m_travel_refresh_toc", m_travel_refresh_toc);
        SocketCommand.protoMap[7748352] = "proto.line.m_hero_pass_info_toc";
        ClassUtils.regClass("proto.line.m_hero_pass_info_toc", m_hero_pass_info_toc);
        SocketCommand.protoMap[3677184] = "proto.line.m_eight_login_info_toc";
        ClassUtils.regClass("proto.line.m_eight_login_info_toc", m_eight_login_info_toc);
        SocketCommand.protoMap[7420033] = "proto.line.m_pass_check_fetch_toc";
        ClassUtils.regClass("proto.line.m_pass_check_fetch_toc", m_pass_check_fetch_toc);
        SocketCommand.protoMap[7945344] = "proto.line.m_maze_info_toc";
        ClassUtils.regClass("proto.line.m_maze_info_toc", m_maze_info_toc);
        SocketCommand.protoMap[2495236] = "proto.line.m_equip_auto_unload_toc";
        ClassUtils.regClass("proto.line.m_equip_auto_unload_toc", m_equip_auto_unload_toc);
        SocketCommand.protoMap[4727810] = "proto.line.m_mission_shop_buy_toc";
        ClassUtils.regClass("proto.line.m_mission_shop_buy_toc", m_mission_shop_buy_toc);
        SocketCommand.protoMap[9586944] = "proto.line.m_team_xswh_info_toc";
        ClassUtils.regClass("proto.line.m_team_xswh_info_toc", m_team_xswh_info_toc);
        SocketCommand.protoMap[1181953] = "proto.line.m_simp_mission_update_toc";
        ClassUtils.regClass("proto.line.m_simp_mission_update_toc", m_simp_mission_update_toc);
        SocketCommand.protoMap[10637570] = "proto.line.m_csclan_self_toc";
        ClassUtils.regClass("proto.line.m_csclan_self_toc", m_csclan_self_toc);
        SocketCommand.protoMap[10637571] = "proto.line.m_csclan_audit_limit_toc";
        ClassUtils.regClass("proto.line.m_csclan_audit_limit_toc", m_csclan_audit_limit_toc);
        SocketCommand.protoMap[8536324] = "proto.line.m_cross_ladder_logs_toc";
        ClassUtils.regClass("proto.line.m_cross_ladder_logs_toc", m_cross_ladder_logs_toc);
        SocketCommand.protoMap[10637573] = "proto.line.m_csclan_set_owner_toc";
        ClassUtils.regClass("proto.line.m_csclan_set_owner_toc", m_csclan_set_owner_toc);
        SocketCommand.protoMap[10637574] = "proto.line.m_csclan_update_notice_toc";
        ClassUtils.regClass("proto.line.m_csclan_update_notice_toc", m_csclan_update_notice_toc);
        SocketCommand.protoMap[10637575] = "proto.line.m_csclan_operate_toc";
        ClassUtils.regClass("proto.line.m_csclan_operate_toc", m_csclan_operate_toc);
        SocketCommand.protoMap[10637576] = "proto.line.m_csclan_members_toc";
        ClassUtils.regClass("proto.line.m_csclan_members_toc", m_csclan_members_toc);
        SocketCommand.protoMap[10637577] = "proto.line.m_csclan_apply_list_toc";
        ClassUtils.regClass("proto.line.m_csclan_apply_list_toc", m_csclan_apply_list_toc);
        SocketCommand.protoMap[10637578] = "proto.line.m_csclan_logs_toc";
        ClassUtils.regClass("proto.line.m_csclan_logs_toc", m_csclan_logs_toc);
        SocketCommand.protoMap[10637579] = "proto.line.m_csclan_rename_toc";
        ClassUtils.regClass("proto.line.m_csclan_rename_toc", m_csclan_rename_toc);
        SocketCommand.protoMap[10637580] = "proto.line.m_csclan_base_toc";
        ClassUtils.regClass("proto.line.m_csclan_base_toc", m_csclan_base_toc);
        SocketCommand.protoMap[10637581] = "proto.line.m_csclan_scene_toc";
        ClassUtils.regClass("proto.line.m_csclan_scene_toc", m_csclan_scene_toc);
        SocketCommand.protoMap[5384448] = "proto.line.m_quick_shop_info_toc";
        ClassUtils.regClass("proto.line.m_quick_shop_info_toc", m_quick_shop_info_toc);
        SocketCommand.protoMap[4924803] = "proto.line.m_daily_gift_tehui_info_toc";
        ClassUtils.regClass("proto.line.m_daily_gift_tehui_info_toc", m_daily_gift_tehui_info_toc);
        SocketCommand.protoMap[11031555] = "proto.line.m_mock_pvp_logs_toc";
        ClassUtils.regClass("proto.line.m_mock_pvp_logs_toc", m_mock_pvp_logs_toc);
        SocketCommand.protoMap[1247620] = "proto.line.m_family_apply_list_toc";
        ClassUtils.regClass("proto.line.m_family_apply_list_toc", m_family_apply_list_toc);
        SocketCommand.protoMap[8339330] = "proto.line.m_multi_lineup_get_power_toc";
        ClassUtils.regClass("proto.line.m_multi_lineup_get_power_toc", m_multi_lineup_get_power_toc);
        SocketCommand.protoMap[4924805] = "proto.line.m_daily_gift_new_discount_info_toc";
        ClassUtils.regClass("proto.line.m_daily_gift_new_discount_info_toc", m_daily_gift_new_discount_info_toc);
        SocketCommand.protoMap[7420035] = "proto.line.m_pass_check_update_toc";
        ClassUtils.regClass("proto.line.m_pass_check_update_toc", m_pass_check_update_toc);
        SocketCommand.protoMap[1247622] = "proto.line.m_family_join_toc";
        ClassUtils.regClass("proto.line.m_family_join_toc", m_family_join_toc);
        SocketCommand.protoMap[7945346] = "proto.line.m_maze_start_toc";
        ClassUtils.regClass("proto.line.m_maze_start_toc", m_maze_start_toc);
        SocketCommand.protoMap[2692226] = "proto.line.m_fight_set_speed_toc";
        ClassUtils.regClass("proto.line.m_fight_set_speed_toc", m_fight_set_speed_toc);
        SocketCommand.protoMap[8995968] = "proto.line.m_small_game_info_toc";
        ClassUtils.regClass("proto.line.m_small_game_info_toc", m_small_game_info_toc);
        SocketCommand.protoMap[9652615] = "proto.line.m_master_card_notice_toc";
        ClassUtils.regClass("proto.line.m_master_card_notice_toc", m_master_card_notice_toc);
        SocketCommand.protoMap[1247624] = "proto.line.m_family_reply_join_toc";
        ClassUtils.regClass("proto.line.m_family_reply_join_toc", m_family_reply_join_toc);
        SocketCommand.protoMap[10571906] = "proto.line.m_lord_skill_set_toc";
        ClassUtils.regClass("proto.line.m_lord_skill_set_toc", m_lord_skill_set_toc);
        SocketCommand.protoMap[9652617] = "proto.line.m_master_card_power_toc";
        ClassUtils.regClass("proto.line.m_master_card_power_toc", m_master_card_power_toc);
        SocketCommand.protoMap[9652618] = "proto.line.m_master_card_spe_effect_toc";
        ClassUtils.regClass("proto.line.m_master_card_spe_effect_toc", m_master_card_spe_effect_toc);
        SocketCommand.protoMap[10112263] = "proto.line.m_boat_race_rank_toc";
        ClassUtils.regClass("proto.line.m_boat_race_rank_toc", m_boat_race_rank_toc);
        SocketCommand.protoMap[7551364] = "proto.line.m_lcqs_fight_result_toc";
        ClassUtils.regClass("proto.line.m_lcqs_fight_result_toc", m_lcqs_fight_result_toc);
        SocketCommand.protoMap[984960] = "proto.line.m_bag_goods_list_toc";
        ClassUtils.regClass("proto.line.m_bag_goods_list_toc", m_bag_goods_list_toc);
        SocketCommand.protoMap[1247627] = "proto.line.m_family_set_owner_toc";
        ClassUtils.regClass("proto.line.m_family_set_owner_toc", m_family_set_owner_toc);
        SocketCommand.protoMap[4727809] = "proto.line.m_mission_shop_update_toc";
        ClassUtils.regClass("proto.line.m_mission_shop_update_toc", m_mission_shop_update_toc);
        SocketCommand.protoMap[5253120] = "proto.line.m_world_level_info_toc";
        ClassUtils.regClass("proto.line.m_world_level_info_toc", m_world_level_info_toc);
        SocketCommand.protoMap[9652620] = "proto.line.m_master_card_decoration_op_toc";
        ClassUtils.regClass("proto.line.m_master_card_decoration_op_toc", m_master_card_decoration_op_toc);
        SocketCommand.protoMap[9521280] = "proto.line.m_battle_trial_info_toc";
        ClassUtils.regClass("proto.line.m_battle_trial_info_toc", m_battle_trial_info_toc);
        SocketCommand.protoMap[1116289] = "proto.line.m_role_attr_change_toc";
        ClassUtils.regClass("proto.line.m_role_attr_change_toc", m_role_attr_change_toc);
        SocketCommand.protoMap[10834562] = "proto.line.m_fish_lottery_toc";
        ClassUtils.regClass("proto.line.m_fish_lottery_toc", m_fish_lottery_toc);
        SocketCommand.protoMap[10112257] = "proto.line.m_boat_race_bonus_toc";
        ClassUtils.regClass("proto.line.m_boat_race_bonus_toc", m_boat_race_bonus_toc);
        SocketCommand.protoMap[2626566] = "proto.line.m_main_battle_mission_status_toc";
        ClassUtils.regClass("proto.line.m_main_battle_mission_status_toc", m_main_battle_mission_status_toc);
        SocketCommand.protoMap[10112264] = "proto.line.m_boat_race_logs_toc";
        ClassUtils.regClass("proto.line.m_boat_race_logs_toc", m_boat_race_logs_toc);
        SocketCommand.protoMap[7945348] = "proto.line.m_maze_fetch_toc";
        ClassUtils.regClass("proto.line.m_maze_fetch_toc", m_maze_fetch_toc);
        SocketCommand.protoMap[5778432] = "proto.line.m_god_trial_info_toc";
        ClassUtils.regClass("proto.line.m_god_trial_info_toc", m_god_trial_info_toc);
        SocketCommand.protoMap[9521281] = "proto.line.m_battle_trial_fetch_info_toc";
        ClassUtils.regClass("proto.line.m_battle_trial_fetch_info_toc", m_battle_trial_fetch_info_toc);
        SocketCommand.protoMap[10046592] = "proto.line.m_hero_zhouyin_info_toc";
        ClassUtils.regClass("proto.line.m_hero_zhouyin_info_toc", m_hero_zhouyin_info_toc);
        SocketCommand.protoMap[2626567] = "proto.line.m_main_battle_box_info_toc";
        ClassUtils.regClass("proto.line.m_main_battle_box_info_toc", m_main_battle_box_info_toc);
        SocketCommand.protoMap[7748353] = "proto.line.m_hero_pass_fetch_toc";
        ClassUtils.regClass("proto.line.m_hero_pass_fetch_toc", m_hero_pass_fetch_toc);
        SocketCommand.protoMap[10112265] = "proto.line.m_boat_race_result_toc";
        ClassUtils.regClass("proto.line.m_boat_race_result_toc", m_boat_race_result_toc);
        SocketCommand.protoMap[4399488] = "proto.line.m_hero_comment_info_toc";
        ClassUtils.regClass("proto.line.m_hero_comment_info_toc", m_hero_comment_info_toc);
        SocketCommand.protoMap[7551361] = "proto.line.m_lcqs_chapter_fetch_toc";
        ClassUtils.regClass("proto.line.m_lcqs_chapter_fetch_toc", m_lcqs_chapter_fetch_toc);
        SocketCommand.protoMap[6500738] = "proto.line.m_guandu_update_event_toc";
        ClassUtils.regClass("proto.line.m_guandu_update_event_toc", m_guandu_update_event_toc);
        SocketCommand.protoMap[11753859] = "proto.line.m_hongbao_info_update_toc";
        ClassUtils.regClass("proto.line.m_hongbao_info_update_toc", m_hongbao_info_update_toc);
        SocketCommand.protoMap[6500740] = "proto.line.m_guandu_next_toc";
        ClassUtils.regClass("proto.line.m_guandu_next_toc", m_guandu_next_toc);
        SocketCommand.protoMap[9652613] = "proto.line.m_master_card_replace_toc";
        ClassUtils.regClass("proto.line.m_master_card_replace_toc", m_master_card_replace_toc);
        SocketCommand.protoMap[9652614] = "proto.line.m_master_card_up_official_toc";
        ClassUtils.regClass("proto.line.m_master_card_up_official_toc", m_master_card_up_official_toc);
        SocketCommand.protoMap[1247623] = "proto.line.m_family_cancel_join_toc";
        ClassUtils.regClass("proto.line.m_family_cancel_join_toc", m_family_cancel_join_toc);
        SocketCommand.protoMap[9652616] = "proto.line.m_master_card_preview_attr_toc";
        ClassUtils.regClass("proto.line.m_master_card_preview_attr_toc", m_master_card_preview_attr_toc);
        SocketCommand.protoMap[1378950] = "proto.line.m_change_day_toc";
        ClassUtils.regClass("proto.line.m_change_day_toc", m_change_day_toc);
        SocketCommand.protoMap[8076672] = "proto.line.m_casting_soul_op_toc";
        ClassUtils.regClass("proto.line.m_casting_soul_op_toc", m_casting_soul_op_toc);
        SocketCommand.protoMap[9652619] = "proto.line.m_master_card_decoration_info_toc";
        ClassUtils.regClass("proto.line.m_master_card_decoration_info_toc", m_master_card_decoration_info_toc);
        SocketCommand.protoMap[1247628] = "proto.line.m_family_update_notice_toc";
        ClassUtils.regClass("proto.line.m_family_update_notice_toc", m_family_update_notice_toc);
        SocketCommand.protoMap[1247629] = "proto.line.m_family_operate_toc";
        ClassUtils.regClass("proto.line.m_family_operate_toc", m_family_operate_toc);
        SocketCommand.protoMap[1247630] = "proto.line.m_family_info_change_toc";
        ClassUtils.regClass("proto.line.m_family_info_change_toc", m_family_info_change_toc);
        SocketCommand.protoMap[1247631] = "proto.line.m_family_logs_toc";
        ClassUtils.regClass("proto.line.m_family_logs_toc", m_family_logs_toc);
        SocketCommand.protoMap[1247632] = "proto.line.m_family_update_member_toc";
        ClassUtils.regClass("proto.line.m_family_update_member_toc", m_family_update_member_toc);
        SocketCommand.protoMap[1247633] = "proto.line.m_family_rename_toc";
        ClassUtils.regClass("proto.line.m_family_rename_toc", m_family_rename_toc);
        SocketCommand.protoMap[1247634] = "proto.line.m_family_info_toc";
        ClassUtils.regClass("proto.line.m_family_info_toc", m_family_info_toc);
        SocketCommand.protoMap[1247635] = "proto.line.m_family_attr_tip_toc";
        ClassUtils.regClass("proto.line.m_family_attr_tip_toc", m_family_attr_tip_toc);
        SocketCommand.protoMap[1247636] = "proto.line.m_family_flagname_toc";
        ClassUtils.regClass("proto.line.m_family_flagname_toc", m_family_flagname_toc);
        SocketCommand.protoMap[1247637] = "proto.line.m_family_transfer_count_toc";
        ClassUtils.regClass("proto.line.m_family_transfer_count_toc", m_family_transfer_count_toc);
        SocketCommand.protoMap[1247638] = "proto.line.m_family_transfer_join_toc";
        ClassUtils.regClass("proto.line.m_family_transfer_join_toc", m_family_transfer_join_toc);
        SocketCommand.protoMap[1247639] = "proto.line.m_family_hot_toc";
        ClassUtils.regClass("proto.line.m_family_hot_toc", m_family_hot_toc);
        SocketCommand.protoMap[1247640] = "proto.line.m_family_hot_op_toc";
        ClassUtils.regClass("proto.line.m_family_hot_op_toc", m_family_hot_op_toc);
        SocketCommand.protoMap[4136832] = "proto.line.m_time_activity_info_toc";
        ClassUtils.regClass("proto.line.m_time_activity_info_toc", m_time_activity_info_toc);
        SocketCommand.protoMap[2035585] = "proto.line.m_hint_show_tip_toc";
        ClassUtils.regClass("proto.line.m_hint_show_tip_toc", m_hint_show_tip_toc);
        SocketCommand.protoMap[5778434] = "proto.line.m_god_trial_buff_toc";
        ClassUtils.regClass("proto.line.m_god_trial_buff_toc", m_god_trial_buff_toc);
        SocketCommand.protoMap[9521283] = "proto.line.m_battle_trial_daily_end_info_toc";
        ClassUtils.regClass("proto.line.m_battle_trial_daily_end_info_toc", m_battle_trial_daily_end_info_toc);
        SocketCommand.protoMap[6829056] = "proto.line.m_monster_group_power_toc";
        ClassUtils.regClass("proto.line.m_monster_group_power_toc", m_monster_group_power_toc);
        SocketCommand.protoMap[4268162] = "proto.line.m_vip_free_gift_info_toc";
        ClassUtils.regClass("proto.line.m_vip_free_gift_info_toc", m_vip_free_gift_info_toc);
        SocketCommand.protoMap[10571905] = "proto.line.m_lord_info_toc";
        ClassUtils.regClass("proto.line.m_lord_info_toc", m_lord_info_toc);
        SocketCommand.protoMap[10112267] = "proto.line.m_boat_race_alloc_items_toc";
        ClassUtils.regClass("proto.line.m_boat_race_alloc_items_toc", m_boat_race_alloc_items_toc);
        SocketCommand.protoMap[8011014] = "proto.line.m_hzzd_info_history_toc";
        ClassUtils.regClass("proto.line.m_hzzd_info_history_toc", m_hzzd_info_history_toc);
        SocketCommand.protoMap[7945351] = "proto.line.m_maze_hero_toc";
        ClassUtils.regClass("proto.line.m_maze_hero_toc", m_maze_hero_toc);
        SocketCommand.protoMap[2560897] = "proto.line.m_god_equip_compose_toc";
        ClassUtils.regClass("proto.line.m_god_equip_compose_toc", m_god_equip_compose_toc);
        SocketCommand.protoMap[9521284] = "proto.line.m_battle_trial_offline_info_toc";
        ClassUtils.regClass("proto.line.m_battle_trial_offline_info_toc", m_battle_trial_offline_info_toc);
        SocketCommand.protoMap[6829057] = "proto.line.m_monster_group_power_list_toc";
        ClassUtils.regClass("proto.line.m_monster_group_power_list_toc", m_monster_group_power_list_toc);
        SocketCommand.protoMap[7354368] = "proto.line.m_world_boss_info_toc";
        ClassUtils.regClass("proto.line.m_world_boss_info_toc", m_world_boss_info_toc);
        SocketCommand.protoMap[8404994] = "proto.line.m_peak_info_member_toc";
        ClassUtils.regClass("proto.line.m_peak_info_member_toc", m_peak_info_member_toc);
        SocketCommand.protoMap[10571907] = "proto.line.m_lord_set_lineup_toc";
        ClassUtils.regClass("proto.line.m_lord_set_lineup_toc", m_lord_set_lineup_toc);
        SocketCommand.protoMap[1904258] = "proto.line.m_gmcmd_battle_edit_toc";
        ClassUtils.regClass("proto.line.m_gmcmd_battle_edit_toc", m_gmcmd_battle_edit_toc);
        SocketCommand.protoMap[590726] = "proto.line.m_chat_get_goods_toc";
        ClassUtils.regClass("proto.line.m_chat_get_goods_toc", m_chat_get_goods_toc);
        SocketCommand.protoMap[1510276] = "proto.line.m_ranking_history_list_toc";
        ClassUtils.regClass("proto.line.m_ranking_history_list_toc", m_ranking_history_list_toc);
        SocketCommand.protoMap[10243588] = "proto.line.m_story_maze_fetch_toc";
        ClassUtils.regClass("proto.line.m_story_maze_fetch_toc", m_story_maze_fetch_toc);
        SocketCommand.protoMap[2560898] = "proto.line.m_god_equip_select_toc";
        ClassUtils.regClass("proto.line.m_god_equip_select_toc", m_god_equip_select_toc);
        SocketCommand.protoMap[6303747] = "proto.line.m_family_boss_look_toc";
        ClassUtils.regClass("proto.line.m_family_boss_look_toc", m_family_boss_look_toc);
        SocketCommand.protoMap[7354369] = "proto.line.m_world_boss_buy_times_toc";
        ClassUtils.regClass("proto.line.m_world_boss_buy_times_toc", m_world_boss_buy_times_toc);
        SocketCommand.protoMap[7879680] = "proto.line.m_random_pvp_op_toc";
        ClassUtils.regClass("proto.line.m_random_pvp_op_toc", m_random_pvp_op_toc);
        SocketCommand.protoMap[11622529] = "proto.line.m_wars_init_toc";
        ClassUtils.regClass("proto.line.m_wars_init_toc", m_wars_init_toc);
        SocketCommand.protoMap[2560896] = "proto.line.m_god_equip_recast_toc";
        ClassUtils.regClass("proto.line.m_god_equip_recast_toc", m_god_equip_recast_toc);
        SocketCommand.protoMap[5975429] = "proto.line.m_pass_behead_battle_toc";
        ClassUtils.regClass("proto.line.m_pass_behead_battle_toc", m_pass_behead_battle_toc);
        SocketCommand.protoMap[10506249] = "proto.line.m_treasure_pull_list_toc";
        ClassUtils.regClass("proto.line.m_treasure_pull_list_toc", m_treasure_pull_list_toc);
        SocketCommand.protoMap[11359875] = "proto.line.m_large_peak_info_look_member_toc";
        ClassUtils.regClass("proto.line.m_large_peak_info_look_member_toc", m_large_peak_info_look_member_toc);
        SocketCommand.protoMap[9521286] = "proto.line.m_battle_trial_battle_up_toc";
        ClassUtils.regClass("proto.line.m_battle_trial_battle_up_toc", m_battle_trial_battle_up_toc);
        SocketCommand.protoMap[1313280] = "proto.line.m_broadcast_normal_toc";
        ClassUtils.regClass("proto.line.m_broadcast_normal_toc", m_broadcast_normal_toc);
        SocketCommand.protoMap[9849600] = "proto.line.m_stage_breed_info_toc";
        ClassUtils.regClass("proto.line.m_stage_breed_info_toc", m_stage_breed_info_toc);
        SocketCommand.protoMap[10768898] = "proto.line.m_boat_peak_member_toc";
        ClassUtils.regClass("proto.line.m_boat_peak_member_toc", m_boat_peak_member_toc);
        SocketCommand.protoMap[2363907] = "proto.line.m_shop_update_toc";
        ClassUtils.regClass("proto.line.m_shop_update_toc", m_shop_update_toc);
        SocketCommand.protoMap[4465156] = "proto.line.m_family_hongbao_fetch_update_toc";
        ClassUtils.regClass("proto.line.m_family_hongbao_fetch_update_toc", m_family_hongbao_fetch_update_toc);
        SocketCommand.protoMap[4465157] = "proto.line.m_family_hongbao_mission_info_toc";
        ClassUtils.regClass("proto.line.m_family_hongbao_mission_info_toc", m_family_hongbao_mission_info_toc);
        SocketCommand.protoMap[8404992] = "proto.line.m_peak_info_toc";
        ClassUtils.regClass("proto.line.m_peak_info_toc", m_peak_info_toc);
        SocketCommand.protoMap[1444609] = "proto.line.m_activity_update_fetch_toc";
        ClassUtils.regClass("proto.line.m_activity_update_fetch_toc", m_activity_update_fetch_toc);
        SocketCommand.protoMap[7617032] = "proto.line.m_theme_activity_up_star_reward_info_toc";
        ClassUtils.regClass("proto.line.m_theme_activity_up_star_reward_info_toc", m_theme_activity_up_star_reward_info_toc);
        SocketCommand.protoMap[10768906] = "proto.line.m_boat_peak_look_pre_score_toc";
        ClassUtils.regClass("proto.line.m_boat_peak_look_pre_score_toc", m_boat_peak_look_pre_score_toc);
        SocketCommand.protoMap[10768907] = "proto.line.m_boat_peak_join_members_toc";
        ClassUtils.regClass("proto.line.m_boat_peak_join_members_toc", m_boat_peak_join_members_toc);
        SocketCommand.protoMap[10768908] = "proto.line.m_boat_peak_fetch_toc";
        ClassUtils.regClass("proto.line.m_boat_peak_fetch_toc", m_boat_peak_fetch_toc);
        SocketCommand.protoMap[3020546] = "proto.line.m_profile_update_toc";
        ClassUtils.regClass("proto.line.m_profile_update_toc", m_profile_update_toc);
        SocketCommand.protoMap[9455620] = "proto.line.m_team_info_my_team_update_toc";
        ClassUtils.regClass("proto.line.m_team_info_my_team_update_toc", m_team_info_my_team_update_toc);
        SocketCommand.protoMap[8273667] = "proto.line.m_hero_resonate_dhyana_op_toc";
        ClassUtils.regClass("proto.line.m_hero_resonate_dhyana_op_toc", m_hero_resonate_dhyana_op_toc);
        SocketCommand.protoMap[8273668] = "proto.line.m_hero_resonate_equip_op_toc";
        ClassUtils.regClass("proto.line.m_hero_resonate_equip_op_toc", m_hero_resonate_equip_op_toc);
        SocketCommand.protoMap[2560900] = "proto.line.m_god_equip_enchant_toc";
        ClassUtils.regClass("proto.line.m_god_equip_enchant_toc", m_god_equip_enchant_toc);
        SocketCommand.protoMap[9521287] = "proto.line.m_battle_trial_battle_reset_toc";
        ClassUtils.regClass("proto.line.m_battle_trial_battle_reset_toc", m_battle_trial_battle_reset_toc);
        SocketCommand.protoMap[2495237] = "proto.line.m_equip_reinforce_toc";
        ClassUtils.regClass("proto.line.m_equip_reinforce_toc", m_equip_reinforce_toc);
        SocketCommand.protoMap[7879682] = "proto.line.m_random_pvp_match_toc";
        ClassUtils.regClass("proto.line.m_random_pvp_match_toc", m_random_pvp_match_toc);
        SocketCommand.protoMap[8404993] = "proto.line.m_peak_info_opp_toc";
        ClassUtils.regClass("proto.line.m_peak_info_opp_toc", m_peak_info_opp_toc);
        SocketCommand.protoMap[8930304] = "proto.line.m_player_strategy_info_toc";
        ClassUtils.regClass("proto.line.m_player_strategy_info_toc", m_player_strategy_info_toc);
        SocketCommand.protoMap[2495239] = "proto.line.m_equip_recycle_toc";
        ClassUtils.regClass("proto.line.m_equip_recycle_toc", m_equip_recycle_toc);
        SocketCommand.protoMap[2626573] = "proto.line.m_main_battle_box_upgrade_toc";
        ClassUtils.regClass("proto.line.m_main_battle_box_upgrade_toc", m_main_battle_box_upgrade_toc);
        SocketCommand.protoMap[6172418] = "proto.line.m_war_flag_op_toc";
        ClassUtils.regClass("proto.line.m_war_flag_op_toc", m_war_flag_op_toc);
        SocketCommand.protoMap[2495240] = "proto.line.m_equip_decompose_toc";
        ClassUtils.regClass("proto.line.m_equip_decompose_toc", m_equip_decompose_toc);
        SocketCommand.protoMap[2495241] = "proto.line.m_equip_compose_toc";
        ClassUtils.regClass("proto.line.m_equip_compose_toc", m_equip_compose_toc);
        SocketCommand.protoMap[6303750] = "proto.line.m_family_boss_attr_toc";
        ClassUtils.regClass("proto.line.m_family_boss_attr_toc", m_family_boss_attr_toc);
        SocketCommand.protoMap[3611523] = "proto.line.m_arena_list_toc";
        ClassUtils.regClass("proto.line.m_arena_list_toc", m_arena_list_toc);
        SocketCommand.protoMap[1444618] = "proto.line.m_activity_limit_sign_info_toc";
        ClassUtils.regClass("proto.line.m_activity_limit_sign_info_toc", m_activity_limit_sign_info_toc);
        SocketCommand.protoMap[4136834] = "proto.line.m_time_activity_update_toc";
        ClassUtils.regClass("proto.line.m_time_activity_update_toc", m_time_activity_update_toc);
        SocketCommand.protoMap[4662145] = "proto.line.m_squad_lineup_set_toc";
        ClassUtils.regClass("proto.line.m_squad_lineup_set_toc", m_squad_lineup_set_toc);
        SocketCommand.protoMap[5187456] = "proto.line.m_bingfu_info_toc";
        ClassUtils.regClass("proto.line.m_bingfu_info_toc", m_bingfu_info_toc);
        SocketCommand.protoMap[1444619] = "proto.line.m_activity_yueka_info_toc";
        ClassUtils.regClass("proto.line.m_activity_yueka_info_toc", m_activity_yueka_info_toc);
        SocketCommand.protoMap[4202496] = "proto.line.m_page_list_toc";
        ClassUtils.regClass("proto.line.m_page_list_toc", m_page_list_toc);
        SocketCommand.protoMap[2495245] = "proto.line.m_equip_info_update_toc";
        ClassUtils.regClass("proto.line.m_equip_info_update_toc", m_equip_info_update_toc);
        SocketCommand.protoMap[6303751] = "proto.line.m_family_boss_gather_toc";
        ClassUtils.regClass("proto.line.m_family_boss_gather_toc", m_family_boss_gather_toc);
        SocketCommand.protoMap[2692227] = "proto.line.m_fight_simp_result_toc";
        ClassUtils.regClass("proto.line.m_fight_simp_result_toc", m_fight_simp_result_toc);
        SocketCommand.protoMap[10571911] = "proto.line.m_lord_star_toc";
        ClassUtils.regClass("proto.line.m_lord_star_toc", m_lord_star_toc);
        SocketCommand.protoMap[1444608] = "proto.line.m_activity_list_toc";
        ClassUtils.regClass("proto.line.m_activity_list_toc", m_activity_list_toc);
        SocketCommand.protoMap[9455621] = "proto.line.m_team_operate_toc";
        ClassUtils.regClass("proto.line.m_team_operate_toc", m_team_operate_toc);
        SocketCommand.protoMap[5187457] = "proto.line.m_bingfu_breed_toc";
        ClassUtils.regClass("proto.line.m_bingfu_breed_toc", m_bingfu_breed_toc);
        SocketCommand.protoMap[5712768] = "proto.line.m_daily_ad_code_info_toc";
        ClassUtils.regClass("proto.line.m_daily_ad_code_info_toc", m_daily_ad_code_info_toc);
        SocketCommand.protoMap[7879684] = "proto.line.m_random_pvp_palace_toc";
        ClassUtils.regClass("proto.line.m_random_pvp_palace_toc", m_random_pvp_palace_toc);
        SocketCommand.protoMap[3611525] = "proto.line.m_arena_fight_result_toc";
        ClassUtils.regClass("proto.line.m_arena_fight_result_toc", m_arena_fight_result_toc);
        SocketCommand.protoMap[919298] = "proto.line.m_item_show_gains_toc";
        ClassUtils.regClass("proto.line.m_item_show_gains_toc", m_item_show_gains_toc);
        SocketCommand.protoMap[10112259] = "proto.line.m_boat_race_boats_toc";
        ClassUtils.regClass("proto.line.m_boat_race_boats_toc", m_boat_race_boats_toc);
        SocketCommand.protoMap[1969920] = "proto.line.m_complaints_info_toc";
        ClassUtils.regClass("proto.line.m_complaints_info_toc", m_complaints_info_toc);
        SocketCommand.protoMap[6632064] = "proto.line.m_time_achievement_info_toc";
        ClassUtils.regClass("proto.line.m_time_achievement_info_toc", m_time_achievement_info_toc);
        SocketCommand.protoMap[4530817] = "proto.line.m_fuli_token_buy_toc";
        ClassUtils.regClass("proto.line.m_fuli_token_buy_toc", m_fuli_token_buy_toc);
        SocketCommand.protoMap[3480194] = "proto.line.m_hunt_gift_toc";
        ClassUtils.regClass("proto.line.m_hunt_gift_toc", m_hunt_gift_toc);
        SocketCommand.protoMap[7682691] = "proto.line.m_star_plan_update_toc";
        ClassUtils.regClass("proto.line.m_star_plan_update_toc", m_star_plan_update_toc);
        SocketCommand.protoMap[2429572] = "proto.line.m_hero_lock_toc";
        ClassUtils.regClass("proto.line.m_hero_lock_toc", m_hero_lock_toc);
        SocketCommand.protoMap[1378949] = "proto.line.m_system_setting_toc";
        ClassUtils.regClass("proto.line.m_system_setting_toc", m_system_setting_toc);
        SocketCommand.protoMap[2429574] = "proto.line.m_hero_recycle_toc";
        ClassUtils.regClass("proto.line.m_hero_recycle_toc", m_hero_recycle_toc);
        SocketCommand.protoMap[1378951] = "proto.line.m_system_time_toc";
        ClassUtils.regClass("proto.line.m_system_time_toc", m_system_time_toc);
        SocketCommand.protoMap[2429576] = "proto.line.m_hero_down_lineup_toc";
        ClassUtils.regClass("proto.line.m_hero_down_lineup_toc", m_hero_down_lineup_toc);
        SocketCommand.protoMap[10834569] = "proto.line.m_fish_notice_toc";
        ClassUtils.regClass("proto.line.m_fish_notice_toc", m_fish_notice_toc);
        SocketCommand.protoMap[10834570] = "proto.line.m_fish_preview_attr_toc";
        ClassUtils.regClass("proto.line.m_fish_preview_attr_toc", m_fish_preview_attr_toc);
        SocketCommand.protoMap[10834571] = "proto.line.m_fish_power_toc";
        ClassUtils.regClass("proto.line.m_fish_power_toc", m_fish_power_toc);
        SocketCommand.protoMap[10834572] = "proto.line.m_fish_spe_effect_toc";
        ClassUtils.regClass("proto.line.m_fish_spe_effect_toc", m_fish_spe_effect_toc);
        SocketCommand.protoMap[10834573] = "proto.line.m_fish_logs_toc";
        ClassUtils.regClass("proto.line.m_fish_logs_toc", m_fish_logs_toc);
        SocketCommand.protoMap[10834574] = "proto.line.m_fish_handbook_info_toc";
        ClassUtils.regClass("proto.line.m_fish_handbook_info_toc", m_fish_handbook_info_toc);
        SocketCommand.protoMap[2429583] = "proto.line.m_hero_recycle_change_toc";
        ClassUtils.regClass("proto.line.m_hero_recycle_change_toc", m_hero_recycle_change_toc);
        SocketCommand.protoMap[2429584] = "proto.line.m_hero_my_rank_toc";
        ClassUtils.regClass("proto.line.m_hero_my_rank_toc", m_hero_my_rank_toc);
        SocketCommand.protoMap[2495256] = "proto.line.m_wing_info_toc";
        ClassUtils.regClass("proto.line.m_wing_info_toc", m_wing_info_toc);
        SocketCommand.protoMap[2429586] = "proto.line.m_hero_inherit_update_toc";
        ClassUtils.regClass("proto.line.m_hero_inherit_update_toc", m_hero_inherit_update_toc);
        SocketCommand.protoMap[2429587] = "proto.line.m_hero_recycle_down_toc";
        ClassUtils.regClass("proto.line.m_hero_recycle_down_toc", m_hero_recycle_down_toc);
        SocketCommand.protoMap[2429588] = "proto.line.m_hero_recycle_times_toc";
        ClassUtils.regClass("proto.line.m_hero_recycle_times_toc", m_hero_recycle_times_toc);
        SocketCommand.protoMap[2429589] = "proto.line.m_hero_act_fourteen_toc";
        ClassUtils.regClass("proto.line.m_hero_act_fourteen_toc", m_hero_act_fourteen_toc);
        SocketCommand.protoMap[2429590] = "proto.line.m_hero_evolve_op_toc";
        ClassUtils.regClass("proto.line.m_hero_evolve_op_toc", m_hero_evolve_op_toc);
        SocketCommand.protoMap[2429591] = "proto.line.m_hero_evolve_info_toc";
        ClassUtils.regClass("proto.line.m_hero_evolve_info_toc", m_hero_evolve_info_toc);
        SocketCommand.protoMap[919299] = "proto.line.m_item_sale_toc";
        ClassUtils.regClass("proto.line.m_item_sale_toc", m_item_sale_toc);
        SocketCommand.protoMap[7879686] = "proto.line.m_random_pvp_update_toc";
        ClassUtils.regClass("proto.line.m_random_pvp_update_toc", m_random_pvp_update_toc);
        SocketCommand.protoMap[5187459] = "proto.line.m_bingfu_shift_toc";
        ClassUtils.regClass("proto.line.m_bingfu_shift_toc", m_bingfu_shift_toc);
        SocketCommand.protoMap[2495232] = "proto.line.m_equip_info_toc";
        ClassUtils.regClass("proto.line.m_equip_info_toc", m_equip_info_toc);
        SocketCommand.protoMap[9455619] = "proto.line.m_team_info_my_team_apply_toc";
        ClassUtils.regClass("proto.line.m_team_info_my_team_apply_toc", m_team_info_my_team_apply_toc);
        SocketCommand.protoMap[6763392] = "proto.line.m_rank_mission_info_toc";
        ClassUtils.regClass("proto.line.m_rank_mission_info_toc", m_rank_mission_info_toc);
        SocketCommand.protoMap[2101248] = "proto.line.m_daily_mission_info_toc";
        ClassUtils.regClass("proto.line.m_daily_mission_info_toc", m_daily_mission_info_toc);
        SocketCommand.protoMap[10506241] = "proto.line.m_treasure_dispatch_toc";
        ClassUtils.regClass("proto.line.m_treasure_dispatch_toc", m_treasure_dispatch_toc);
        SocketCommand.protoMap[11031552] = "proto.line.m_mock_pvp_op_toc";
        ClassUtils.regClass("proto.line.m_mock_pvp_op_toc", m_mock_pvp_op_toc);
        SocketCommand.protoMap[722305] = "proto.line.m_common_tips_toc";
        ClassUtils.regClass("proto.line.m_common_tips_toc", m_common_tips_toc);
        SocketCommand.protoMap[3611526] = "proto.line.m_arena_max_reward_info_toc";
        ClassUtils.regClass("proto.line.m_arena_max_reward_info_toc", m_arena_max_reward_info_toc);
        SocketCommand.protoMap[7879687] = "proto.line.m_random_pvp_fight_result_toc";
        ClassUtils.regClass("proto.line.m_random_pvp_fight_result_toc", m_random_pvp_fight_result_toc);
        SocketCommand.protoMap[8404998] = "proto.line.m_peak_info_bet_toc";
        ClassUtils.regClass("proto.line.m_peak_info_bet_toc", m_peak_info_bet_toc);
        SocketCommand.protoMap[2495233] = "proto.line.m_equip_load_toc";
        ClassUtils.regClass("proto.line.m_equip_load_toc", m_equip_load_toc);
        SocketCommand.protoMap[3020544] = "proto.line.m_profile_info_toc";
        ClassUtils.regClass("proto.line.m_profile_info_toc", m_profile_info_toc);
        SocketCommand.protoMap[10506242] = "proto.line.m_treasure_other_toc";
        ClassUtils.regClass("proto.line.m_treasure_other_toc", m_treasure_other_toc);
        SocketCommand.protoMap[11031553] = "proto.line.m_mock_pvp_info_toc";
        ClassUtils.regClass("proto.line.m_mock_pvp_info_toc", m_mock_pvp_info_toc);
        SocketCommand.protoMap[6303748] = "proto.line.m_family_boss_rank_toc";
        ClassUtils.regClass("proto.line.m_family_boss_rank_toc", m_family_boss_rank_toc);
        SocketCommand.protoMap[10703234] = "proto.line.m_fight_share_chat_toc";
        ClassUtils.regClass("proto.line.m_fight_share_chat_toc", m_fight_share_chat_toc);
        SocketCommand.protoMap[4268164] = "proto.line.m_vip_buy_recommend_toc";
        ClassUtils.regClass("proto.line.m_vip_buy_recommend_toc", m_vip_buy_recommend_toc);
        SocketCommand.protoMap[10112260] = "proto.line.m_boat_race_event_toc";
        ClassUtils.regClass("proto.line.m_boat_race_event_toc", m_boat_race_event_toc);
        SocketCommand.protoMap[7879688] = "proto.line.m_random_pvp_new_season_toc";
        ClassUtils.regClass("proto.line.m_random_pvp_new_season_toc", m_random_pvp_new_season_toc);
        SocketCommand.protoMap[8404999] = "proto.line.m_peak_info_history_toc";
        ClassUtils.regClass("proto.line.m_peak_info_history_toc", m_peak_info_history_toc);
        SocketCommand.protoMap[2495234] = "proto.line.m_equip_unload_toc";
        ClassUtils.regClass("proto.line.m_equip_unload_toc", m_equip_unload_toc);
        SocketCommand.protoMap[3020545] = "proto.line.m_profile_change_toc";
        ClassUtils.regClass("proto.line.m_profile_change_toc", m_profile_change_toc);
        SocketCommand.protoMap[3545856] = "proto.line.m_shortcut_shop_info_toc";
        ClassUtils.regClass("proto.line.m_shortcut_shop_info_toc", m_shortcut_shop_info_toc);
        SocketCommand.protoMap[7288705] = "proto.line.m_csc_fmsolo_group_toc";
        ClassUtils.regClass("proto.line.m_csc_fmsolo_group_toc", m_csc_fmsolo_group_toc);
        SocketCommand.protoMap[11031554] = "proto.line.m_mock_pvp_look_hero_toc";
        ClassUtils.regClass("proto.line.m_mock_pvp_look_hero_toc", m_mock_pvp_look_hero_toc);
        SocketCommand.protoMap[11556865] = "proto.line.m_csclan_solo_info_toc";
        ClassUtils.regClass("proto.line.m_csclan_solo_info_toc", m_csclan_solo_info_toc);
        SocketCommand.protoMap[10243590] = "proto.line.m_story_maze_hero_toc";
        ClassUtils.regClass("proto.line.m_story_maze_hero_toc", m_story_maze_hero_toc);
        SocketCommand.protoMap[2495235] = "proto.line.m_equip_auto_load_toc";
        ClassUtils.regClass("proto.line.m_equip_auto_load_toc", m_equip_auto_load_toc);
        SocketCommand.protoMap[9455622] = "proto.line.m_team_lineup_get_toc";
        ClassUtils.regClass("proto.line.m_team_lineup_get_toc", m_team_lineup_get_toc);
        SocketCommand.protoMap[4071168] = "proto.line.m_guide_mission_toc";
        ClassUtils.regClass("proto.line.m_guide_mission_toc", m_guide_mission_toc);
        SocketCommand.protoMap[6369408] = "proto.line.m_red_cliff_info_toc";
        ClassUtils.regClass("proto.line.m_red_cliff_info_toc", m_red_cliff_info_toc);
        SocketCommand.protoMap[8798976] = "proto.line.m_soul_hero_info_toc";
        ClassUtils.regClass("proto.line.m_soul_hero_info_toc", m_soul_hero_info_toc);
        SocketCommand.protoMap[3545857] = "proto.line.m_shortcut_shop_buy_toc";
        ClassUtils.regClass("proto.line.m_shortcut_shop_buy_toc", m_shortcut_shop_buy_toc);
        SocketCommand.protoMap[1444610] = "proto.line.m_activity_info_toc";
        ClassUtils.regClass("proto.line.m_activity_info_toc", m_activity_info_toc);
        SocketCommand.protoMap[7748355] = "proto.line.m_hero_pass_gift_toc";
        ClassUtils.regClass("proto.line.m_hero_pass_gift_toc", m_hero_pass_gift_toc);
        SocketCommand.protoMap[7748356] = "proto.line.m_hero_pass_update_toc";
        ClassUtils.regClass("proto.line.m_hero_pass_update_toc", m_hero_pass_update_toc);
        SocketCommand.protoMap[1444613] = "proto.line.m_activity_shop_update_toc";
        ClassUtils.regClass("proto.line.m_activity_shop_update_toc", m_activity_shop_update_toc);
        SocketCommand.protoMap[2495238] = "proto.line.m_equip_auto_reinforce_toc";
        ClassUtils.regClass("proto.line.m_equip_auto_reinforce_toc", m_equip_auto_reinforce_toc);
        SocketCommand.protoMap[5318785] = "proto.line.m_rent_hero_op_toc";
        ClassUtils.regClass("proto.line.m_rent_hero_op_toc", m_rent_hero_op_toc);
        SocketCommand.protoMap[1444616] = "proto.line.m_activity_exam_answer_toc";
        ClassUtils.regClass("proto.line.m_activity_exam_answer_toc", m_activity_exam_answer_toc);
        SocketCommand.protoMap[1444617] = "proto.line.m_activity_gift_toc";
        ClassUtils.regClass("proto.line.m_activity_gift_toc", m_activity_gift_toc);
        SocketCommand.protoMap[2495242] = "proto.line.m_equip_auto_compose_info_toc";
        ClassUtils.regClass("proto.line.m_equip_auto_compose_info_toc", m_equip_auto_compose_info_toc);
        SocketCommand.protoMap[2495243] = "proto.line.m_equip_compose_logs_toc";
        ClassUtils.regClass("proto.line.m_equip_compose_logs_toc", m_equip_compose_logs_toc);
        SocketCommand.protoMap[2495244] = "proto.line.m_equip_replace_toc";
        ClassUtils.regClass("proto.line.m_equip_replace_toc", m_equip_replace_toc);
        SocketCommand.protoMap[7420034] = "proto.line.m_pass_check_gift_toc";
        ClassUtils.regClass("proto.line.m_pass_check_gift_toc", m_pass_check_gift_toc);
        SocketCommand.protoMap[2495246] = "proto.line.m_equip_filter_info_toc";
        ClassUtils.regClass("proto.line.m_equip_filter_info_toc", m_equip_filter_info_toc);
        SocketCommand.protoMap[2495248] = "proto.line.m_equip_star_op_toc";
        ClassUtils.regClass("proto.line.m_equip_star_op_toc", m_equip_star_op_toc);
        SocketCommand.protoMap[2495249] = "proto.line.m_equip_bingfu_recast_toc";
        ClassUtils.regClass("proto.line.m_equip_bingfu_recast_toc", m_equip_bingfu_recast_toc);
        SocketCommand.protoMap[2495250] = "proto.line.m_equip_bingfu_decompose_toc";
        ClassUtils.regClass("proto.line.m_equip_bingfu_decompose_toc", m_equip_bingfu_decompose_toc);
        SocketCommand.protoMap[7945347] = "proto.line.m_maze_spoils_toc";
        ClassUtils.regClass("proto.line.m_maze_spoils_toc", m_maze_spoils_toc);
        SocketCommand.protoMap[2495252] = "proto.line.m_equip_bingfa_op_toc";
        ClassUtils.regClass("proto.line.m_equip_bingfa_op_toc", m_equip_bingfa_op_toc);
        SocketCommand.protoMap[2495253] = "proto.line.m_equip_bingfa_exchange_toc";
        ClassUtils.regClass("proto.line.m_equip_bingfa_exchange_toc", m_equip_bingfa_exchange_toc);
        SocketCommand.protoMap[2495254] = "proto.line.m_equip_lock_toc";
        ClassUtils.regClass("proto.line.m_equip_lock_toc", m_equip_lock_toc);
        SocketCommand.protoMap[2495255] = "proto.line.m_wing_op_toc";
        ClassUtils.regClass("proto.line.m_wing_op_toc", m_wing_op_toc);
        SocketCommand.protoMap[9455623] = "proto.line.m_team_lineup_list_toc";
        ClassUtils.regClass("proto.line.m_team_lineup_list_toc", m_team_lineup_list_toc);
        SocketCommand.protoMap[10571908] = "proto.line.m_lord_lineup_list_toc";
        ClassUtils.regClass("proto.line.m_lord_lineup_list_toc", m_lord_lineup_list_toc);
        SocketCommand.protoMap[2495258] = "proto.line.m_deputy_info_toc";
        ClassUtils.regClass("proto.line.m_deputy_info_toc", m_deputy_info_toc);
        SocketCommand.protoMap[1444611] = "proto.line.m_activity_shop_info_toc";
        ClassUtils.regClass("proto.line.m_activity_shop_info_toc", m_activity_shop_info_toc);
        SocketCommand.protoMap[4071169] = "proto.line.m_guide_hint_toc";
        ClassUtils.regClass("proto.line.m_guide_hint_toc", m_guide_hint_toc);
        SocketCommand.protoMap[11031556] = "proto.line.m_mock_pvp_schemes_toc";
        ClassUtils.regClass("proto.line.m_mock_pvp_schemes_toc", m_mock_pvp_schemes_toc);
        SocketCommand.protoMap[9521285] = "proto.line.m_battle_trial_hanging_info_toc";
        ClassUtils.regClass("proto.line.m_battle_trial_hanging_info_toc", m_battle_trial_hanging_info_toc);
        SocketCommand.protoMap[11556867] = "proto.line.m_csclan_solo_fetch_toc";
        ClassUtils.regClass("proto.line.m_csclan_solo_fetch_toc", m_csclan_solo_fetch_toc);
        SocketCommand.protoMap[11556866] = "proto.line.m_csclan_solo_group_toc";
        ClassUtils.regClass("proto.line.m_csclan_solo_group_toc", m_csclan_solo_group_toc);
        SocketCommand.protoMap[8864640] = "proto.line.m_medal_list_toc";
        ClassUtils.regClass("proto.line.m_medal_list_toc", m_medal_list_toc);
        SocketCommand.protoMap[3742854] = "proto.line.m_fuli_fund_fetch_toc";
        ClassUtils.regClass("proto.line.m_fuli_fund_fetch_toc", m_fuli_fund_fetch_toc);
        SocketCommand.protoMap[1575937] = "proto.line.m_welfare_sdk_share_toc";
        ClassUtils.regClass("proto.line.m_welfare_sdk_share_toc", m_welfare_sdk_share_toc);
        SocketCommand.protoMap[11622535] = "proto.line.m_wars_sign_toc";
        ClassUtils.regClass("proto.line.m_wars_sign_toc", m_wars_sign_toc);
        SocketCommand.protoMap[1444615] = "proto.line.m_activity_exam_list_toc";
        ClassUtils.regClass("proto.line.m_activity_exam_list_toc", m_activity_exam_list_toc);
        SocketCommand.protoMap[1116296] = "proto.line.m_role_family_change_toc";
        ClassUtils.regClass("proto.line.m_role_family_change_toc", m_role_family_change_toc);
        SocketCommand.protoMap[5384450] = "proto.line.m_quick_shop_tips_toc";
        ClassUtils.regClass("proto.line.m_quick_shop_tips_toc", m_quick_shop_tips_toc);
        SocketCommand.protoMap[9652609] = "proto.line.m_master_card_lottery_toc";
        ClassUtils.regClass("proto.line.m_master_card_lottery_toc", m_master_card_lottery_toc);
        SocketCommand.protoMap[11622537] = "proto.line.m_wars_team_info_toc";
        ClassUtils.regClass("proto.line.m_wars_team_info_toc", m_wars_team_info_toc);
        SocketCommand.protoMap[853632] = "proto.line.m_letter_get_toc";
        ClassUtils.regClass("proto.line.m_letter_get_toc", m_letter_get_toc);
        SocketCommand.protoMap[11031557] = "proto.line.m_mock_pvp_config_toc";
        ClassUtils.regClass("proto.line.m_mock_pvp_config_toc", m_mock_pvp_config_toc);
        SocketCommand.protoMap[11622538] = "proto.line.m_wars_team_hero_info_toc";
        ClassUtils.regClass("proto.line.m_wars_team_hero_info_toc", m_wars_team_hero_info_toc);
        SocketCommand.protoMap[5121792] = "proto.line.m_hero_skin_info_toc";
        ClassUtils.regClass("proto.line.m_hero_skin_info_toc", m_hero_skin_info_toc);
        SocketCommand.protoMap[9389952] = "proto.line.m_microterminal_info_toc";
        ClassUtils.regClass("proto.line.m_microterminal_info_toc", m_microterminal_info_toc);
        SocketCommand.protoMap[1116299] = "proto.line.m_role_update_ext_toc";
        ClassUtils.regClass("proto.line.m_role_update_ext_toc", m_role_update_ext_toc);
        SocketCommand.protoMap[1116300] = "proto.line.m_role_best_hero_toc";
        ClassUtils.regClass("proto.line.m_role_best_hero_toc", m_role_best_hero_toc);
        SocketCommand.protoMap[9455625] = "proto.line.m_team_merge_reward_toc";
        ClassUtils.regClass("proto.line.m_team_merge_reward_toc", m_team_merge_reward_toc);
        SocketCommand.protoMap[1116302] = "proto.line.m_role_look_lineup_toc";
        ClassUtils.regClass("proto.line.m_role_look_lineup_toc", m_role_look_lineup_toc);
        SocketCommand.protoMap[4071171] = "proto.line.m_guide_step_toc";
        ClassUtils.regClass("proto.line.m_guide_step_toc", m_guide_step_toc);
        SocketCommand.protoMap[10177922] = "proto.line.m_master_talent_science_update_toc";
        ClassUtils.regClass("proto.line.m_master_talent_science_update_toc", m_master_talent_science_update_toc);
        SocketCommand.protoMap[1378944] = "proto.line.m_system_heartbeat_toc";
        ClassUtils.regClass("proto.line.m_system_heartbeat_toc", m_system_heartbeat_toc);
        SocketCommand.protoMap[11622543] = "proto.line.m_wars_look_op_army_toc";
        ClassUtils.regClass("proto.line.m_wars_look_op_army_toc", m_wars_look_op_army_toc);
        SocketCommand.protoMap[5647104] = "proto.line.m_daily_fuli_info_toc";
        ClassUtils.regClass("proto.line.m_daily_fuli_info_toc", m_daily_fuli_info_toc);
        SocketCommand.protoMap[8536325] = "proto.line.m_cross_ladder_fight_result_toc";
        ClassUtils.regClass("proto.line.m_cross_ladder_fight_result_toc", m_cross_ladder_fight_result_toc);
        SocketCommand.protoMap[9389953] = "proto.line.m_microterminal_sign_fetch_toc";
        ClassUtils.regClass("proto.line.m_microterminal_sign_fetch_toc", m_microterminal_sign_fetch_toc);
        SocketCommand.protoMap[11622544] = "proto.line.m_wars_look_op_score_rank_toc";
        ClassUtils.regClass("proto.line.m_wars_look_op_score_rank_toc", m_wars_look_op_score_rank_toc);
        SocketCommand.protoMap[9915264] = "proto.line.m_stage_copy_info_toc";
        ClassUtils.regClass("proto.line.m_stage_copy_info_toc", m_stage_copy_info_toc);
        SocketCommand.protoMap[11622545] = "proto.line.m_wars_look_op_fight_log_toc";
        ClassUtils.regClass("proto.line.m_wars_look_op_fight_log_toc", m_wars_look_op_fight_log_toc);
        SocketCommand.protoMap[11622546] = "proto.line.m_wars_look_op_city_log_toc";
        ClassUtils.regClass("proto.line.m_wars_look_op_city_log_toc", m_wars_look_op_city_log_toc);
        SocketCommand.protoMap[11622542] = "proto.line.m_wars_look_op_sign_toc";
        ClassUtils.regClass("proto.line.m_wars_look_op_sign_toc", m_wars_look_op_sign_toc);
        SocketCommand.protoMap[9455626] = "proto.line.m_team_share_list_toc";
        ClassUtils.regClass("proto.line.m_team_share_list_toc", m_team_share_list_toc);
        SocketCommand.protoMap[11622547] = "proto.line.m_wars_look_op_camp_rank_toc";
        ClassUtils.regClass("proto.line.m_wars_look_op_camp_rank_toc", m_wars_look_op_camp_rank_toc);
        SocketCommand.protoMap[4071172] = "proto.line.m_guide_event_toc";
        ClassUtils.regClass("proto.line.m_guide_event_toc", m_guide_event_toc);
        SocketCommand.protoMap[1378945] = "proto.line.m_system_error_toc";
        ClassUtils.regClass("proto.line.m_system_error_toc", m_system_error_toc);
        SocketCommand.protoMap[1116308] = "proto.line.m_role_cross_group_toc";
        ClassUtils.regClass("proto.line.m_role_cross_group_toc", m_role_cross_group_toc);
        SocketCommand.protoMap[1904256] = "proto.line.m_gmcmd_do_toc";
        ClassUtils.regClass("proto.line.m_gmcmd_do_toc", m_gmcmd_do_toc);
        SocketCommand.protoMap[10243591] = "proto.line.m_story_maze_pub_toc";
        ClassUtils.regClass("proto.line.m_story_maze_pub_toc", m_story_maze_pub_toc);
        SocketCommand.protoMap[6172416] = "proto.line.m_war_flag_info_toc";
        ClassUtils.regClass("proto.line.m_war_flag_info_toc", m_war_flag_info_toc);
        SocketCommand.protoMap[1510272] = "proto.line.m_ranking_list_toc";
        ClassUtils.regClass("proto.line.m_ranking_list_toc", m_ranking_list_toc);
        SocketCommand.protoMap[6763393] = "proto.line.m_rank_mission_update_toc";
        ClassUtils.regClass("proto.line.m_rank_mission_update_toc", m_rank_mission_update_toc);
        SocketCommand.protoMap[3611522] = "proto.line.m_arena_update_toc";
        ClassUtils.regClass("proto.line.m_arena_update_toc", m_arena_update_toc);
        SocketCommand.protoMap[9915267] = "proto.line.m_stage_copy_drop_group_toc";
        ClassUtils.regClass("proto.line.m_stage_copy_drop_group_toc", m_stage_copy_drop_group_toc);
        SocketCommand.protoMap[3611524] = "proto.line.m_arena_logs_toc";
        ClassUtils.regClass("proto.line.m_arena_logs_toc", m_arena_logs_toc);
        SocketCommand.protoMap[590725] = "proto.line.m_chat_auth_toc";
        ClassUtils.regClass("proto.line.m_chat_auth_toc", m_chat_auth_toc);
        SocketCommand.protoMap[10965894] = "proto.line.m_td_simp_info_toc";
        ClassUtils.regClass("proto.line.m_td_simp_info_toc", m_td_simp_info_toc);
        SocketCommand.protoMap[10965895] = "proto.line.m_td_mission_info_toc";
        ClassUtils.regClass("proto.line.m_td_mission_info_toc", m_td_mission_info_toc);
        SocketCommand.protoMap[10571910] = "proto.line.m_lord_recycle_preview_toc";
        ClassUtils.regClass("proto.line.m_lord_recycle_preview_toc", m_lord_recycle_preview_toc);
        SocketCommand.protoMap[590736] = "proto.line.m_chat_channel_toc";
        ClassUtils.regClass("proto.line.m_chat_channel_toc", m_chat_channel_toc);
        SocketCommand.protoMap[853635] = "proto.line.m_letter_operate_toc";
        ClassUtils.regClass("proto.line.m_letter_operate_toc", m_letter_operate_toc);
        SocketCommand.protoMap[1378946] = "proto.line.m_system_message_toc";
        ClassUtils.regClass("proto.line.m_system_message_toc", m_system_message_toc);
        SocketCommand.protoMap[5121795] = "proto.line.m_hero_skin_upgrade_toc";
        ClassUtils.regClass("proto.line.m_hero_skin_upgrade_toc", m_hero_skin_upgrade_toc);
        SocketCommand.protoMap[590745] = "proto.line.m_chat_private_history_toc";
        ClassUtils.regClass("proto.line.m_chat_private_history_toc", m_chat_private_history_toc);
        SocketCommand.protoMap[2429568] = "proto.line.m_hero_list_toc";
        ClassUtils.regClass("proto.line.m_hero_list_toc", m_hero_list_toc);
        SocketCommand.protoMap[590747] = "proto.line.m_chat_misc_toc";
        ClassUtils.regClass("proto.line.m_chat_misc_toc", m_chat_misc_toc);
        SocketCommand.protoMap[6172417] = "proto.line.m_war_flag_active_toc";
        ClassUtils.regClass("proto.line.m_war_flag_active_toc", m_war_flag_active_toc);
        SocketCommand.protoMap[590749] = "proto.line.m_chat_cost_toc";
        ClassUtils.regClass("proto.line.m_chat_cost_toc", m_chat_cost_toc);
        SocketCommand.protoMap[9915266] = "proto.line.m_stage_copy_fetch_toc";
        ClassUtils.regClass("proto.line.m_stage_copy_fetch_toc", m_stage_copy_fetch_toc);
        SocketCommand.protoMap[656647] = "proto.line.m_login_scene_toc";
        ClassUtils.regClass("proto.line.m_login_scene_toc", m_login_scene_toc);
        SocketCommand.protoMap[10440577] = "proto.line.m_acc_gift_info_toc";
        ClassUtils.regClass("proto.line.m_acc_gift_info_toc", m_acc_gift_info_toc);
        SocketCommand.protoMap[7091723] = "proto.line.m_qxzl_info_get_msg_toc";
        ClassUtils.regClass("proto.line.m_qxzl_info_get_msg_toc", m_qxzl_info_get_msg_toc);
        SocketCommand.protoMap[6697728] = "proto.line.m_sys_daily_info_toc";
        ClassUtils.regClass("proto.line.m_sys_daily_info_toc", m_sys_daily_info_toc);
        SocketCommand.protoMap[10112261] = "proto.line.m_boat_race_op_toc";
        ClassUtils.regClass("proto.line.m_boat_race_op_toc", m_boat_race_op_toc);
        SocketCommand.protoMap[8142345] = "proto.line.m_modular_activity_wish_lottery_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_wish_lottery_info_toc", m_modular_activity_wish_lottery_info_toc);
        SocketCommand.protoMap[11622557] = "proto.line.m_wars_honor_wall_toc";
        ClassUtils.regClass("proto.line.m_wars_honor_wall_toc", m_wars_honor_wall_toc);
        SocketCommand.protoMap[10506250] = "proto.line.m_treasure_exist_toc";
        ClassUtils.regClass("proto.line.m_treasure_exist_toc", m_treasure_exist_toc);
        SocketCommand.protoMap[1378947] = "proto.line.m_system_config_change_toc";
        ClassUtils.regClass("proto.line.m_system_config_change_toc", m_system_config_change_toc);
        SocketCommand.protoMap[5121796] = "proto.line.m_hero_skin_reset_toc";
        ClassUtils.regClass("proto.line.m_hero_skin_reset_toc", m_hero_skin_reset_toc);
        SocketCommand.protoMap[8864645] = "proto.line.m_medal_detail_toc";
        ClassUtils.regClass("proto.line.m_medal_detail_toc", m_medal_detail_toc);
        SocketCommand.protoMap[2954880] = "proto.line.m_tax_silver_info_toc";
        ClassUtils.regClass("proto.line.m_tax_silver_info_toc", m_tax_silver_info_toc);
        SocketCommand.protoMap[11622539] = "proto.line.m_wars_role_presonal_info_toc";
        ClassUtils.regClass("proto.line.m_wars_role_presonal_info_toc", m_wars_role_presonal_info_toc);
        SocketCommand.protoMap[6697729] = "proto.line.m_sys_daily_fetch_toc";
        ClassUtils.regClass("proto.line.m_sys_daily_fetch_toc", m_sys_daily_fetch_toc);
        SocketCommand.protoMap[7223040] = "proto.line.m_cycle_activity_info_toc";
        ClassUtils.regClass("proto.line.m_cycle_activity_info_toc", m_cycle_activity_info_toc);
        SocketCommand.protoMap[10965889] = "proto.line.m_td_lineup_toc";
        ClassUtils.regClass("proto.line.m_td_lineup_toc", m_td_lineup_toc);
        SocketCommand.protoMap[11491200] = "proto.line.m_divine_copy_sweep_toc";
        ClassUtils.regClass("proto.line.m_divine_copy_sweep_toc", m_divine_copy_sweep_toc);
        SocketCommand.protoMap[9652612] = "proto.line.m_master_card_shop_toc";
        ClassUtils.regClass("proto.line.m_master_card_shop_toc", m_master_card_shop_toc);
        SocketCommand.protoMap[4333824] = "proto.line.m_family_ranking_list_toc";
        ClassUtils.regClass("proto.line.m_family_ranking_list_toc", m_family_ranking_list_toc);
        SocketCommand.protoMap[1378948] = "proto.line.m_system_config_toc";
        ClassUtils.regClass("proto.line.m_system_config_toc", m_system_config_toc);
        SocketCommand.protoMap[2429570] = "proto.line.m_hero_update_fight_toc";
        ClassUtils.regClass("proto.line.m_hero_update_fight_toc", m_hero_update_fight_toc);
        SocketCommand.protoMap[6172419] = "proto.line.m_war_flag_link_toc";
        ClassUtils.regClass("proto.line.m_war_flag_link_toc", m_war_flag_link_toc);
        SocketCommand.protoMap[3480192] = "proto.line.m_hunt_info_toc";
        ClassUtils.regClass("proto.line.m_hunt_info_toc", m_hunt_info_toc);
        SocketCommand.protoMap[7223041] = "proto.line.m_cycle_activity_fetch_toc";
        ClassUtils.regClass("proto.line.m_cycle_activity_fetch_toc", m_cycle_activity_fetch_toc);
        SocketCommand.protoMap[2429569] = "proto.line.m_hero_update_list_toc";
        ClassUtils.regClass("proto.line.m_hero_update_list_toc", m_hero_update_list_toc);
        SocketCommand.protoMap[10965890] = "proto.line.m_td_skill_toc";
        ClassUtils.regClass("proto.line.m_td_skill_toc", m_td_skill_toc);
        SocketCommand.protoMap[11491201] = "proto.line.m_divine_copy_info_toc";
        ClassUtils.regClass("proto.line.m_divine_copy_info_toc", m_divine_copy_info_toc);
        SocketCommand.protoMap[3874176] = "proto.line.m_first_pay_info_toc";
        ClassUtils.regClass("proto.line.m_first_pay_info_toc", m_first_pay_info_toc);
        SocketCommand.protoMap[7288708] = "proto.line.m_csc_fmsolo_box_toc";
        ClassUtils.regClass("proto.line.m_csc_fmsolo_box_toc", m_csc_fmsolo_box_toc);
        SocketCommand.protoMap[9521282] = "proto.line.m_battle_trial_pass_info_toc";
        ClassUtils.regClass("proto.line.m_battle_trial_pass_info_toc", m_battle_trial_pass_info_toc);
        SocketCommand.protoMap[2429571] = "proto.line.m_hero_upgrade_toc";
        ClassUtils.regClass("proto.line.m_hero_upgrade_toc", m_hero_upgrade_toc);
        SocketCommand.protoMap[2954882] = "proto.line.m_tax_forage_info_toc";
        ClassUtils.regClass("proto.line.m_tax_forage_info_toc", m_tax_forage_info_toc);
        SocketCommand.protoMap[3480193] = "proto.line.m_hunt_run_toc";
        ClassUtils.regClass("proto.line.m_hunt_run_toc", m_hunt_run_toc);
        SocketCommand.protoMap[4005504] = "proto.line.m_daily_pay_info_toc";
        ClassUtils.regClass("proto.line.m_daily_pay_info_toc", m_daily_pay_info_toc);
        SocketCommand.protoMap[10965891] = "proto.line.m_td_fight_toc";
        ClassUtils.regClass("proto.line.m_td_fight_toc", m_td_fight_toc);
        SocketCommand.protoMap[853633] = "proto.line.m_letter_open_toc";
        ClassUtils.regClass("proto.line.m_letter_open_toc", m_letter_open_toc);
        SocketCommand.protoMap[8273664] = "proto.line.m_hero_resonate_level_info_toc";
        ClassUtils.regClass("proto.line.m_hero_resonate_level_info_toc", m_hero_resonate_level_info_toc);
        SocketCommand.protoMap[10243586] = "proto.line.m_story_maze_start_toc";
        ClassUtils.regClass("proto.line.m_story_maze_start_toc", m_story_maze_start_toc);
        SocketCommand.protoMap[9980928] = "proto.line.m_sgame_info_toc";
        ClassUtils.regClass("proto.line.m_sgame_info_toc", m_sgame_info_toc);
        SocketCommand.protoMap[8667648] = "proto.line.m_dawanka_info_toc";
        ClassUtils.regClass("proto.line.m_dawanka_info_toc", m_dawanka_info_toc);
        SocketCommand.protoMap[8930306] = "proto.line.m_player_strategy_update_toc";
        ClassUtils.regClass("proto.line.m_player_strategy_update_toc", m_player_strategy_update_toc);
        SocketCommand.protoMap[7879683] = "proto.line.m_random_pvp_peak_toc";
        ClassUtils.regClass("proto.line.m_random_pvp_peak_toc", m_random_pvp_peak_toc);
        SocketCommand.protoMap[2626564] = "proto.line.m_main_battle_mission_info_toc";
        ClassUtils.regClass("proto.line.m_main_battle_mission_info_toc", m_main_battle_mission_info_toc);
        SocketCommand.protoMap[2626565] = "proto.line.m_main_battle_mission_fetch_toc";
        ClassUtils.regClass("proto.line.m_main_battle_mission_fetch_toc", m_main_battle_mission_fetch_toc);
        SocketCommand.protoMap[11031558] = "proto.line.m_mock_pvp_show_tops_toc";
        ClassUtils.regClass("proto.line.m_mock_pvp_show_tops_toc", m_mock_pvp_show_tops_toc);
        SocketCommand.protoMap[2363905] = "proto.line.m_shop_buy_toc";
        ClassUtils.regClass("proto.line.m_shop_buy_toc", m_shop_buy_toc);
        SocketCommand.protoMap[11031560] = "proto.line.m_mock_pvp_fight_result_toc";
        ClassUtils.regClass("proto.line.m_mock_pvp_fight_result_toc", m_mock_pvp_fight_result_toc);
        SocketCommand.protoMap[2626569] = "proto.line.m_main_battle_box_open_toc";
        ClassUtils.regClass("proto.line.m_main_battle_box_open_toc", m_main_battle_box_open_toc);
        SocketCommand.protoMap[11031562] = "proto.line.m_mock_pvp_clean_chat_toc";
        ClassUtils.regClass("proto.line.m_mock_pvp_clean_chat_toc", m_mock_pvp_clean_chat_toc);
        SocketCommand.protoMap[2626571] = "proto.line.m_main_battle_auto_end_toc";
        ClassUtils.regClass("proto.line.m_main_battle_auto_end_toc", m_main_battle_auto_end_toc);
        SocketCommand.protoMap[11031564] = "proto.line.m_mock_pvp_lineup_preview_toc";
        ClassUtils.regClass("proto.line.m_mock_pvp_lineup_preview_toc", m_mock_pvp_lineup_preview_toc);
        SocketCommand.protoMap[2889218] = "proto.line.m_daily_copy_sweep_toc";
        ClassUtils.regClass("proto.line.m_daily_copy_sweep_toc", m_daily_copy_sweep_toc);
        SocketCommand.protoMap[2626574] = "proto.line.m_main_battle_box_rate_toc";
        ClassUtils.regClass("proto.line.m_main_battle_box_rate_toc", m_main_battle_box_rate_toc);
        SocketCommand.protoMap[2626576] = "proto.line.m_main_battle_missions_toc";
        ClassUtils.regClass("proto.line.m_main_battle_missions_toc", m_main_battle_missions_toc);
        SocketCommand.protoMap[8142339] = "proto.line.m_modular_activity_update_mission_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_update_mission_toc", m_modular_activity_update_mission_toc);
        SocketCommand.protoMap[4005505] = "proto.line.m_daily_pay_fetch_toc";
        ClassUtils.regClass("proto.line.m_daily_pay_fetch_toc", m_daily_pay_fetch_toc);
        SocketCommand.protoMap[10768900] = "proto.line.m_boat_peak_battle_toc";
        ClassUtils.regClass("proto.line.m_boat_peak_battle_toc", m_boat_peak_battle_toc);
        SocketCommand.protoMap[10965892] = "proto.line.m_td_trial_info_toc";
        ClassUtils.regClass("proto.line.m_td_trial_info_toc", m_td_trial_info_toc);
        SocketCommand.protoMap[8273665] = "proto.line.m_hero_resonate_info_toc";
        ClassUtils.regClass("proto.line.m_hero_resonate_info_toc", m_hero_resonate_info_toc);
        SocketCommand.protoMap[7617029] = "proto.line.m_theme_activity_zhouka_info_toc";
        ClassUtils.regClass("proto.line.m_theme_activity_zhouka_info_toc", m_theme_activity_zhouka_info_toc);
        SocketCommand.protoMap[7617030] = "proto.line.m_theme_activity_wish_lottery_info_toc";
        ClassUtils.regClass("proto.line.m_theme_activity_wish_lottery_info_toc", m_theme_activity_wish_lottery_info_toc);
        SocketCommand.protoMap[787975] = "proto.line.m_friend_fetch_toc";
        ClassUtils.regClass("proto.line.m_friend_fetch_toc", m_friend_fetch_toc);
        SocketCommand.protoMap[4465159] = "proto.line.m_family_hongbao_mission_update_toc";
        ClassUtils.regClass("proto.line.m_family_hongbao_mission_update_toc", m_family_hongbao_mission_update_toc);
        SocketCommand.protoMap[8142344] = "proto.line.m_modular_activity_rare_lottery_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_rare_lottery_info_toc", m_modular_activity_rare_lottery_info_toc);
        SocketCommand.protoMap[787968] = "proto.line.m_friend_list_toc";
        ClassUtils.regClass("proto.line.m_friend_list_toc", m_friend_list_toc);
        SocketCommand.protoMap[787977] = "proto.line.m_friend_best_list_toc";
        ClassUtils.regClass("proto.line.m_friend_best_list_toc", m_friend_best_list_toc);
        SocketCommand.protoMap[7814016] = "proto.line.m_login_activity_info_toc";
        ClassUtils.regClass("proto.line.m_login_activity_info_toc", m_login_activity_info_toc);
        SocketCommand.protoMap[8273666] = "proto.line.m_hero_resonate_operate_toc";
        ClassUtils.regClass("proto.line.m_hero_resonate_operate_toc", m_hero_resonate_operate_toc);
        SocketCommand.protoMap[8798977] = "proto.line.m_soul_hero_link_toc";
        ClassUtils.regClass("proto.line.m_soul_hero_link_toc", m_soul_hero_link_toc);
        SocketCommand.protoMap[7617035] = "proto.line.m_theme_activity_up_star_reward_update_toc";
        ClassUtils.regClass("proto.line.m_theme_activity_up_star_reward_update_toc", m_theme_activity_up_star_reward_update_toc);
        SocketCommand.protoMap[8142348] = "proto.line.m_modular_activity_skin_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_skin_info_toc", m_modular_activity_skin_info_toc);
        SocketCommand.protoMap[11556868] = "proto.line.m_csclan_solo_state_toc";
        ClassUtils.regClass("proto.line.m_csclan_solo_state_toc", m_csclan_solo_state_toc);
        SocketCommand.protoMap[8142349] = "proto.line.m_modular_activity_skin_lottery_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_skin_lottery_toc", m_modular_activity_skin_lottery_toc);
        SocketCommand.protoMap[3480196] = "proto.line.m_hunt_logs_toc";
        ClassUtils.regClass("proto.line.m_hunt_logs_toc", m_hunt_logs_toc);
        SocketCommand.protoMap[4530818] = "proto.line.m_fuli_token_fetch_toc";
        ClassUtils.regClass("proto.line.m_fuli_token_fetch_toc", m_fuli_token_fetch_toc);
        SocketCommand.protoMap[5056129] = "proto.line.m_vip_kefu_op_toc";
        ClassUtils.regClass("proto.line.m_vip_kefu_op_toc", m_vip_kefu_op_toc);
        SocketCommand.protoMap[8798978] = "proto.line.m_soul_hero_reset_toc";
        ClassUtils.regClass("proto.line.m_soul_hero_reset_toc", m_soul_hero_reset_toc);
        SocketCommand.protoMap[3217538] = "proto.line.m_family_task_do_toc";
        ClassUtils.regClass("proto.line.m_family_task_do_toc", m_family_task_do_toc);
        SocketCommand.protoMap[9980932] = "proto.line.m_sgame_wx_club_toc";
        ClassUtils.regClass("proto.line.m_sgame_wx_club_toc", m_sgame_wx_club_toc);
        SocketCommand.protoMap[787970] = "proto.line.m_friend_request_toc";
        ClassUtils.regClass("proto.line.m_friend_request_toc", m_friend_request_toc);
        SocketCommand.protoMap[4662144] = "proto.line.m_squad_lineup_get_toc";
        ClassUtils.regClass("proto.line.m_squad_lineup_get_toc", m_squad_lineup_get_toc);
        SocketCommand.protoMap[1313281] = "proto.line.m_broadcast_show_toc";
        ClassUtils.regClass("proto.line.m_broadcast_show_toc", m_broadcast_show_toc);
        SocketCommand.protoMap[8798979] = "proto.line.m_soul_hero_unlock_toc";
        ClassUtils.regClass("proto.line.m_soul_hero_unlock_toc", m_soul_hero_unlock_toc);
        SocketCommand.protoMap[8142356] = "proto.line.m_modular_activity_war_log_update_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_war_log_update_toc", m_modular_activity_war_log_update_toc);
        SocketCommand.protoMap[6106752] = "proto.line.m_family_science_info_toc";
        ClassUtils.regClass("proto.line.m_family_science_info_toc", m_family_science_info_toc);
        SocketCommand.protoMap[9849601] = "proto.line.m_stage_breed_cost_toc";
        ClassUtils.regClass("proto.line.m_stage_breed_cost_toc", m_stage_breed_cost_toc);
        SocketCommand.protoMap[10374912] = "proto.line.m_story_tower_battle_info_toc";
        ClassUtils.regClass("proto.line.m_story_tower_battle_info_toc", m_story_tower_battle_info_toc);
        SocketCommand.protoMap[6894720] = "proto.line.m_progress_gift_info_toc";
        ClassUtils.regClass("proto.line.m_progress_gift_info_toc", m_progress_gift_info_toc);
        SocketCommand.protoMap[11097217] = "proto.line.m_retrieval_info_toc";
        ClassUtils.regClass("proto.line.m_retrieval_info_toc", m_retrieval_info_toc);
        SocketCommand.protoMap[3742850] = "proto.line.m_fuli_sign_acc_toc";
        ClassUtils.regClass("proto.line.m_fuli_sign_acc_toc", m_fuli_sign_acc_toc);
        SocketCommand.protoMap[3742851] = "proto.line.m_fuli_yueka_info_toc";
        ClassUtils.regClass("proto.line.m_fuli_yueka_info_toc", m_fuli_yueka_info_toc);
        SocketCommand.protoMap[2692228] = "proto.line.m_fight_finish_times_toc";
        ClassUtils.regClass("proto.line.m_fight_finish_times_toc", m_fight_finish_times_toc);
        SocketCommand.protoMap[3742853] = "proto.line.m_fuli_fund_info_toc";
        ClassUtils.regClass("proto.line.m_fuli_fund_info_toc", m_fuli_fund_info_toc);
        SocketCommand.protoMap[2692230] = "proto.line.m_fight_start_pass_toc";
        ClassUtils.regClass("proto.line.m_fight_start_pass_toc", m_fight_start_pass_toc);
        SocketCommand.protoMap[2692231] = "proto.line.m_fight_times_toc";
        ClassUtils.regClass("proto.line.m_fight_times_toc", m_fight_times_toc);
        SocketCommand.protoMap[7945352] = "proto.line.m_maze_pub_toc";
        ClassUtils.regClass("proto.line.m_maze_pub_toc", m_maze_pub_toc);
        SocketCommand.protoMap[7945353] = "proto.line.m_maze_monster_toc";
        ClassUtils.regClass("proto.line.m_maze_monster_toc", m_maze_monster_toc);
        SocketCommand.protoMap[7945354] = "proto.line.m_maze_fight_result_toc";
        ClassUtils.regClass("proto.line.m_maze_fight_result_toc", m_maze_fight_result_toc);
        SocketCommand.protoMap[7945355] = "proto.line.m_maze_use_item_toc";
        ClassUtils.regClass("proto.line.m_maze_use_item_toc", m_maze_use_item_toc);
        SocketCommand.protoMap[787971] = "proto.line.m_friend_agree_toc";
        ClassUtils.regClass("proto.line.m_friend_agree_toc", m_friend_agree_toc);
        SocketCommand.protoMap[3151874] = "proto.line.m_travel_accept_toc";
        ClassUtils.regClass("proto.line.m_travel_accept_toc", m_travel_accept_toc);
        SocketCommand.protoMap[7617028] = "proto.line.m_theme_activity_famous_lottery_update_toc";
        ClassUtils.regClass("proto.line.m_theme_activity_famous_lottery_update_toc", m_theme_activity_famous_lottery_update_toc);
        SocketCommand.protoMap[2363904] = "proto.line.m_shop_info_toc";
        ClassUtils.regClass("proto.line.m_shop_info_toc", m_shop_info_toc);
        SocketCommand.protoMap[2823552] = "proto.line.m_hero_convert_info_toc";
        ClassUtils.regClass("proto.line.m_hero_convert_info_toc", m_hero_convert_info_toc);
        SocketCommand.protoMap[6106753] = "proto.line.m_family_science_op_toc";
        ClassUtils.regClass("proto.line.m_family_science_op_toc", m_family_science_op_toc);
        SocketCommand.protoMap[9849602] = "proto.line.m_stage_breed_upgrade_toc";
        ClassUtils.regClass("proto.line.m_stage_breed_upgrade_toc", m_stage_breed_upgrade_toc);
        SocketCommand.protoMap[10374913] = "proto.line.m_story_tower_battle_fetch_toc";
        ClassUtils.regClass("proto.line.m_story_tower_battle_fetch_toc", m_story_tower_battle_fetch_toc);
        SocketCommand.protoMap[10900224] = "proto.line.m_day_acc_pay_gift_info_toc";
        ClassUtils.regClass("proto.line.m_day_acc_pay_gift_info_toc", m_day_acc_pay_gift_info_toc);
        SocketCommand.protoMap[787972] = "proto.line.m_friend_operate_toc";
        ClassUtils.regClass("proto.line.m_friend_operate_toc", m_friend_operate_toc);
        SocketCommand.protoMap[2889216] = "proto.line.m_daily_copy_process_toc";
        ClassUtils.regClass("proto.line.m_daily_copy_process_toc", m_daily_copy_process_toc);
        SocketCommand.protoMap[10571912] = "proto.line.m_lord_skill_attack_inc_calc_toc";
        ClassUtils.regClass("proto.line.m_lord_skill_attack_inc_calc_toc", m_lord_skill_attack_inc_calc_toc);
        SocketCommand.protoMap[6632065] = "proto.line.m_time_achievement_update_toc";
        ClassUtils.regClass("proto.line.m_time_achievement_update_toc", m_time_achievement_update_toc);
        SocketCommand.protoMap[7157376] = "proto.line.m_hero_come_info_toc";
        ClassUtils.regClass("proto.line.m_hero_come_info_toc", m_hero_come_info_toc);
        SocketCommand.protoMap[11425536] = "proto.line.m_divine_equip_activate_hero_toc";
        ClassUtils.regClass("proto.line.m_divine_equip_activate_hero_toc", m_divine_equip_activate_hero_toc);
        SocketCommand.protoMap[2035584] = "proto.line.m_hint_list_toc";
        ClassUtils.regClass("proto.line.m_hint_list_toc", m_hint_list_toc);
        SocketCommand.protoMap[787973] = "proto.line.m_friend_find_toc";
        ClassUtils.regClass("proto.line.m_friend_find_toc", m_friend_find_toc);
        SocketCommand.protoMap[2889217] = "proto.line.m_daily_copy_enter_toc";
        ClassUtils.regClass("proto.line.m_daily_copy_enter_toc", m_daily_copy_enter_toc);
        SocketCommand.protoMap[6632066] = "proto.line.m_time_achievement_share_toc";
        ClassUtils.regClass("proto.line.m_time_achievement_share_toc", m_time_achievement_share_toc);
        SocketCommand.protoMap[11425537] = "proto.line.m_divine_equip_info_toc";
        ClassUtils.regClass("proto.line.m_divine_equip_info_toc", m_divine_equip_info_toc);
        SocketCommand.protoMap[8142336] = "proto.line.m_modular_activity_list_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_list_toc", m_modular_activity_list_toc);
        SocketCommand.protoMap[7091721] = "proto.line.m_qxzl_info_last_battle_toc";
        ClassUtils.regClass("proto.line.m_qxzl_info_last_battle_toc", m_qxzl_info_last_battle_toc);
        SocketCommand.protoMap[5121794] = "proto.line.m_hero_skin_active_toc";
        ClassUtils.regClass("proto.line.m_hero_skin_active_toc", m_hero_skin_active_toc);
        SocketCommand.protoMap[3414529] = "proto.line.m_online_reward_fetch_toc";
        ClassUtils.regClass("proto.line.m_online_reward_fetch_toc", m_online_reward_fetch_toc);
        SocketCommand.protoMap[3939840] = "proto.line.m_month_fund_info_toc";
        ClassUtils.regClass("proto.line.m_month_fund_info_toc", m_month_fund_info_toc);
        SocketCommand.protoMap[8208000] = "proto.line.m_general_info_toc";
        ClassUtils.regClass("proto.line.m_general_info_toc", m_general_info_toc);
        SocketCommand.protoMap[11162880] = "proto.line.m_stage_skill_info_toc";
        ClassUtils.regClass("proto.line.m_stage_skill_info_toc", m_stage_skill_info_toc);
        SocketCommand.protoMap[6960385] = "proto.line.m_family_active_up_toc";
        ClassUtils.regClass("proto.line.m_family_active_up_toc", m_family_active_up_toc);
        SocketCommand.protoMap[656642] = "proto.line.m_login_relogin_toc";
        ClassUtils.regClass("proto.line.m_login_relogin_toc", m_login_relogin_toc);
        SocketCommand.protoMap[656643] = "proto.line.m_login_gen_name_toc";
        ClassUtils.regClass("proto.line.m_login_gen_name_toc", m_login_gen_name_toc);
        SocketCommand.protoMap[656644] = "proto.line.m_login_auth_toc";
        ClassUtils.regClass("proto.line.m_login_auth_toc", m_login_auth_toc);
        SocketCommand.protoMap[2757893] = "proto.line.m_lottery_supreme_info_toc";
        ClassUtils.regClass("proto.line.m_lottery_supreme_info_toc", m_lottery_supreme_info_toc);
        SocketCommand.protoMap[10112262] = "proto.line.m_boat_race_broadcast_toc";
        ClassUtils.regClass("proto.line.m_boat_race_broadcast_toc", m_boat_race_broadcast_toc);
        SocketCommand.protoMap[7814017] = "proto.line.m_login_activity_fetch_toc";
        ClassUtils.regClass("proto.line.m_login_activity_fetch_toc", m_login_activity_fetch_toc);
        SocketCommand.protoMap[5909768] = "proto.line.m_test_tower_auto_sweep_toc";
        ClassUtils.regClass("proto.line.m_test_tower_auto_sweep_toc", m_test_tower_auto_sweep_toc);
        SocketCommand.protoMap[8011017] = "proto.line.m_hzzd_info_bet_toc";
        ClassUtils.regClass("proto.line.m_hzzd_info_bet_toc", m_hzzd_info_bet_toc);
        SocketCommand.protoMap[8011018] = "proto.line.m_hzzd_route_select_info_toc";
        ClassUtils.regClass("proto.line.m_hzzd_route_select_info_toc", m_hzzd_route_select_info_toc);
        SocketCommand.protoMap[656651] = "proto.line.m_login_re_connect_toc";
        ClassUtils.regClass("proto.line.m_login_re_connect_toc", m_login_re_connect_toc);
        SocketCommand.protoMap[8011020] = "proto.line.m_hzzd_look_kill_toc";
        ClassUtils.regClass("proto.line.m_hzzd_look_kill_toc", m_hzzd_look_kill_toc);
        SocketCommand.protoMap[1510274] = "proto.line.m_ranking_worship_toc";
        ClassUtils.regClass("proto.line.m_ranking_worship_toc", m_ranking_worship_toc);
        SocketCommand.protoMap[11425539] = "proto.line.m_divine_equip_op_toc";
        ClassUtils.regClass("proto.line.m_divine_equip_op_toc", m_divine_equip_op_toc);
        SocketCommand.protoMap[2560899] = "proto.line.m_god_equip_convert_toc";
        ClassUtils.regClass("proto.line.m_god_equip_convert_toc", m_god_equip_convert_toc);
        SocketCommand.protoMap[4465152] = "proto.line.m_family_hongbao_info_toc";
        ClassUtils.regClass("proto.line.m_family_hongbao_info_toc", m_family_hongbao_info_toc);
        SocketCommand.protoMap[3874177] = "proto.line.m_first_pay_fetch_toc";
        ClassUtils.regClass("proto.line.m_first_pay_fetch_toc", m_first_pay_fetch_toc);
        SocketCommand.protoMap[9915268] = "proto.line.m_stage_copy_set_toc";
        ClassUtils.regClass("proto.line.m_stage_copy_set_toc", m_stage_copy_set_toc);
        SocketCommand.protoMap[8733312] = "proto.line.m_ingenious_plan_info_toc";
        ClassUtils.regClass("proto.line.m_ingenious_plan_info_toc", m_ingenious_plan_info_toc);
        SocketCommand.protoMap[10965893] = "proto.line.m_td_main_info_toc";
        ClassUtils.regClass("proto.line.m_td_main_info_toc", m_td_main_info_toc);
        SocketCommand.protoMap[8142384] = "proto.line.m_modular_activity_dice_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_dice_info_toc", m_modular_activity_dice_info_toc);
        SocketCommand.protoMap[8864646] = "proto.line.m_medal_retire_update_toc";
        ClassUtils.regClass("proto.line.m_medal_retire_update_toc", m_medal_retire_update_toc);
        SocketCommand.protoMap[8142385] = "proto.line.m_modular_activity_dice_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_dice_toc", m_modular_activity_dice_toc);
        SocketCommand.protoMap[8142386] = "proto.line.m_modular_activity_dice_unlock_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_dice_unlock_toc", m_modular_activity_dice_unlock_toc);
        SocketCommand.protoMap[722304] = "proto.line.m_common_error_toc";
        ClassUtils.regClass("proto.line.m_common_error_toc", m_common_error_toc);
        SocketCommand.protoMap[7091719] = "proto.line.m_qxzl_info_history_toc";
        ClassUtils.regClass("proto.line.m_qxzl_info_history_toc", m_qxzl_info_history_toc);
        SocketCommand.protoMap[3414528] = "proto.line.m_online_reward_info_toc";
        ClassUtils.regClass("proto.line.m_online_reward_info_toc", m_online_reward_info_toc);
        SocketCommand.protoMap[4990464] = "proto.line.m_qqvip_info_toc";
        ClassUtils.regClass("proto.line.m_qqvip_info_toc", m_qqvip_info_toc);
        SocketCommand.protoMap[1247626] = "proto.line.m_family_set_title_toc";
        ClassUtils.regClass("proto.line.m_family_set_title_toc", m_family_set_title_toc);
        SocketCommand.protoMap[9258624] = "proto.line.m_sdk_reward_info_toc";
        ClassUtils.regClass("proto.line.m_sdk_reward_info_toc", m_sdk_reward_info_toc);
        SocketCommand.protoMap[10506247] = "proto.line.m_treasure_use_item_toc";
        ClassUtils.regClass("proto.line.m_treasure_use_item_toc", m_treasure_use_item_toc);
        SocketCommand.protoMap[1510273] = "proto.line.m_ranking_reward_toc";
        ClassUtils.regClass("proto.line.m_ranking_reward_toc", m_ranking_reward_toc);
        SocketCommand.protoMap[3939843] = "proto.line.m_month_fund_update_toc";
        ClassUtils.regClass("proto.line.m_month_fund_update_toc", m_month_fund_update_toc);
        SocketCommand.protoMap[4465154] = "proto.line.m_family_hongbao_fetch_toc";
        ClassUtils.regClass("proto.line.m_family_hongbao_fetch_toc", m_family_hongbao_fetch_toc);
        SocketCommand.protoMap[4990465] = "proto.line.m_qqvip_fetch_toc";
        ClassUtils.regClass("proto.line.m_qqvip_fetch_toc", m_qqvip_fetch_toc);
        SocketCommand.protoMap[5515776] = "proto.line.m_share_single_info_toc";
        ClassUtils.regClass("proto.line.m_share_single_info_toc", m_share_single_info_toc);
        SocketCommand.protoMap[8601986] = "proto.line.m_xswh_rank_toc";
        ClassUtils.regClass("proto.line.m_xswh_rank_toc", m_xswh_rank_toc);
        SocketCommand.protoMap[9783936] = "proto.line.m_playing_preview_info_toc";
        ClassUtils.regClass("proto.line.m_playing_preview_info_toc", m_playing_preview_info_toc);
        SocketCommand.protoMap[7879685] = "proto.line.m_random_pvp_logs_toc";
        ClassUtils.regClass("proto.line.m_random_pvp_logs_toc", m_random_pvp_logs_toc);
        SocketCommand.protoMap[7157378] = "proto.line.m_hero_come_update_toc";
        ClassUtils.regClass("proto.line.m_hero_come_update_toc", m_hero_come_update_toc);
        SocketCommand.protoMap[722306] = "proto.line.m_common_show_attr_toc";
        ClassUtils.regClass("proto.line.m_common_show_attr_toc", m_common_show_attr_toc);
        SocketCommand.protoMap[8142397] = "proto.line.m_modular_activity_story_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_story_info_toc", m_modular_activity_story_info_toc);
        SocketCommand.protoMap[4465155] = "proto.line.m_family_hongbao_info_update_toc";
        ClassUtils.regClass("proto.line.m_family_hongbao_info_update_toc", m_family_hongbao_info_update_toc);
        SocketCommand.protoMap[8733315] = "proto.line.m_ingenious_plan_star_toc";
        ClassUtils.regClass("proto.line.m_ingenious_plan_star_toc", m_ingenious_plan_star_toc);
        SocketCommand.protoMap[6041088] = "proto.line.m_family_sign_info_toc";
        ClassUtils.regClass("proto.line.m_family_sign_info_toc", m_family_sign_info_toc);
        SocketCommand.protoMap[5844097] = "proto.line.m_ares_palace_log_toc";
        ClassUtils.regClass("proto.line.m_ares_palace_log_toc", m_ares_palace_log_toc);
        SocketCommand.protoMap[9783937] = "proto.line.m_playing_preview_award_toc";
        ClassUtils.regClass("proto.line.m_playing_preview_award_toc", m_playing_preview_award_toc);
        SocketCommand.protoMap[10309248] = "proto.line.m_story_siegelord_info_toc";
        ClassUtils.regClass("proto.line.m_story_siegelord_info_toc", m_story_siegelord_info_toc);
        SocketCommand.protoMap[1772928] = "proto.line.m_payment_shop_toc";
        ClassUtils.regClass("proto.line.m_payment_shop_toc", m_payment_shop_toc);
        SocketCommand.protoMap[1772929] = "proto.line.m_payment_request_toc";
        ClassUtils.regClass("proto.line.m_payment_request_toc", m_payment_request_toc);
        SocketCommand.protoMap[7026050] = "proto.line.m_worship_do_toc";
        ClassUtils.regClass("proto.line.m_worship_do_toc", m_worship_do_toc);
        SocketCommand.protoMap[8470656] = "proto.line.m_hero_strengthen_info_toc";
        ClassUtils.regClass("proto.line.m_hero_strengthen_info_toc", m_hero_strengthen_info_toc);
        SocketCommand.protoMap[1772932] = "proto.line.m_payment_buy_toc";
        ClassUtils.regClass("proto.line.m_payment_buy_toc", m_payment_buy_toc);
        SocketCommand.protoMap[1772933] = "proto.line.m_payment_shop_info_toc";
        ClassUtils.regClass("proto.line.m_payment_shop_info_toc", m_payment_shop_info_toc);
        SocketCommand.protoMap[1772934] = "proto.line.m_payment_auto_buy_toc";
        ClassUtils.regClass("proto.line.m_payment_auto_buy_toc", m_payment_auto_buy_toc);
        SocketCommand.protoMap[2495257] = "proto.line.m_deputy_op_toc";
        ClassUtils.regClass("proto.line.m_deputy_op_toc", m_deputy_op_toc);
        SocketCommand.protoMap[1247618] = "proto.line.m_family_members_toc";
        ClassUtils.regClass("proto.line.m_family_members_toc", m_family_members_toc);
        SocketCommand.protoMap[2298240] = "proto.line.m_tequan_info_toc";
        ClassUtils.regClass("proto.line.m_tequan_info_toc", m_tequan_info_toc);
        SocketCommand.protoMap[8142403] = "proto.line.m_modular_activity_customized_gift_select_list_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_customized_gift_select_list_toc", m_modular_activity_customized_gift_select_list_toc);
        SocketCommand.protoMap[6041089] = "proto.line.m_family_sign_fetch_toc";
        ClassUtils.regClass("proto.line.m_family_sign_fetch_toc", m_family_sign_fetch_toc);
        SocketCommand.protoMap[6566400] = "proto.line.m_hero_handbook_list_toc";
        ClassUtils.regClass("proto.line.m_hero_handbook_list_toc", m_hero_handbook_list_toc);
        SocketCommand.protoMap[6369410] = "proto.line.m_red_cliff_fight_result_toc";
        ClassUtils.regClass("proto.line.m_red_cliff_fight_result_toc", m_red_cliff_fight_result_toc);
        SocketCommand.protoMap[5778436] = "proto.line.m_god_trial_fight_result_toc";
        ClassUtils.regClass("proto.line.m_god_trial_fight_result_toc", m_god_trial_fight_result_toc);
        SocketCommand.protoMap[5778433] = "proto.line.m_god_trial_fetch_toc";
        ClassUtils.regClass("proto.line.m_god_trial_fetch_toc", m_god_trial_fetch_toc);
        SocketCommand.protoMap[590748] = "proto.line.m_chat_clean_toc";
        ClassUtils.regClass("proto.line.m_chat_clean_toc", m_chat_clean_toc);
        SocketCommand.protoMap[1247619] = "proto.line.m_family_self_toc";
        ClassUtils.regClass("proto.line.m_family_self_toc", m_family_self_toc);
        SocketCommand.protoMap[6041090] = "proto.line.m_family_sign_do_toc";
        ClassUtils.regClass("proto.line.m_family_sign_do_toc", m_family_sign_do_toc);
        SocketCommand.protoMap[10637572] = "proto.line.m_csclan_set_title_toc";
        ClassUtils.regClass("proto.line.m_csclan_set_title_toc", m_csclan_set_title_toc);
        SocketCommand.protoMap[7091712] = "proto.line.m_qxzl_info_toc";
        ClassUtils.regClass("proto.line.m_qxzl_info_toc", m_qxzl_info_toc);
        SocketCommand.protoMap[10834561] = "proto.line.m_fish_fishbowl_attr_toc";
        ClassUtils.regClass("proto.line.m_fish_fishbowl_attr_toc", m_fish_fishbowl_attr_toc);
        SocketCommand.protoMap[11359872] = "proto.line.m_large_peak_info_toc";
        ClassUtils.regClass("proto.line.m_large_peak_info_toc", m_large_peak_info_toc);
        SocketCommand.protoMap[4202497] = "proto.line.m_page_switch_toc";
        ClassUtils.regClass("proto.line.m_page_switch_toc", m_page_switch_toc);
        SocketCommand.protoMap[6566402] = "proto.line.m_hero_handbook_all_fetch_toc";
        ClassUtils.regClass("proto.line.m_hero_handbook_all_fetch_toc", m_hero_handbook_all_fetch_toc);
        SocketCommand.protoMap[4465158] = "proto.line.m_family_hongbao_mission_send_toc";
        ClassUtils.regClass("proto.line.m_family_hongbao_mission_send_toc", m_family_hongbao_mission_send_toc);
        SocketCommand.protoMap[4530816] = "proto.line.m_fuli_token_info_toc";
        ClassUtils.regClass("proto.line.m_fuli_token_info_toc", m_fuli_token_info_toc);
        SocketCommand.protoMap[1772931] = "proto.line.m_payment_pay_result_toc";
        ClassUtils.regClass("proto.line.m_payment_pay_result_toc", m_payment_pay_result_toc);
        SocketCommand.protoMap[3348864] = "proto.line.m_hanging_info_toc";
        ClassUtils.regClass("proto.line.m_hanging_info_toc", m_hanging_info_toc);
        SocketCommand.protoMap[7091713] = "proto.line.m_qxzl_info_opp_toc";
        ClassUtils.regClass("proto.line.m_qxzl_info_opp_toc", m_qxzl_info_opp_toc);
        SocketCommand.protoMap[8142414] = "proto.line.m_modular_activity_six_bless_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_six_bless_info_toc", m_modular_activity_six_bless_info_toc);
        SocketCommand.protoMap[7617024] = "proto.line.m_theme_activity_skin_info_toc";
        ClassUtils.regClass("proto.line.m_theme_activity_skin_info_toc", m_theme_activity_skin_info_toc);
        SocketCommand.protoMap[11359873] = "proto.line.m_large_peak_info_opp_toc";
        ClassUtils.regClass("proto.line.m_large_peak_info_opp_toc", m_large_peak_info_opp_toc);
        SocketCommand.protoMap[11885184] = "proto.line.m_zero_buy_op_toc";
        ClassUtils.regClass("proto.line.m_zero_buy_op_toc", m_zero_buy_op_toc);
        SocketCommand.protoMap[1247621] = "proto.line.m_family_uplevel_toc";
        ClassUtils.regClass("proto.line.m_family_uplevel_toc", m_family_uplevel_toc);
        SocketCommand.protoMap[3348865] = "proto.line.m_hanging_reward_toc";
        ClassUtils.regClass("proto.line.m_hanging_reward_toc", m_hanging_reward_toc);
        SocketCommand.protoMap[7091714] = "proto.line.m_qxzl_info_member_toc";
        ClassUtils.regClass("proto.line.m_qxzl_info_member_toc", m_qxzl_info_member_toc);
        SocketCommand.protoMap[7617025] = "proto.line.m_theme_activity_skin_lottery_toc";
        ClassUtils.regClass("proto.line.m_theme_activity_skin_lottery_toc", m_theme_activity_skin_lottery_toc);
        SocketCommand.protoMap[11359874] = "proto.line.m_large_peak_info_member_toc";
        ClassUtils.regClass("proto.line.m_large_peak_info_member_toc", m_large_peak_info_member_toc);
        SocketCommand.protoMap[10046593] = "proto.line.m_hero_zhouyin_upgrade_toc";
        ClassUtils.regClass("proto.line.m_hero_zhouyin_upgrade_toc", m_hero_zhouyin_upgrade_toc);
        SocketCommand.protoMap[9192960] = "proto.line.m_hero_cost_info_toc";
        ClassUtils.regClass("proto.line.m_hero_cost_info_toc", m_hero_cost_info_toc);
        SocketCommand.protoMap[787969] = "proto.line.m_friend_refresh_toc";
        ClassUtils.regClass("proto.line.m_friend_refresh_toc", m_friend_refresh_toc);
        SocketCommand.protoMap[8142338] = "proto.line.m_modular_activity_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_info_toc", m_modular_activity_info_toc);
        SocketCommand.protoMap[7091715] = "proto.line.m_qxzl_info_look_member_toc";
        ClassUtils.regClass("proto.line.m_qxzl_info_look_member_toc", m_qxzl_info_look_member_toc);
        SocketCommand.protoMap[7091716] = "proto.line.m_qxzl_info_battle_toc";
        ClassUtils.regClass("proto.line.m_qxzl_info_battle_toc", m_qxzl_info_battle_toc);
        SocketCommand.protoMap[11294213] = "proto.line.m_dominate_pvp_fight_result_toc";
        ClassUtils.regClass("proto.line.m_dominate_pvp_fight_result_toc", m_dominate_pvp_fight_result_toc);
        SocketCommand.protoMap[787974] = "proto.line.m_friend_give_toc";
        ClassUtils.regClass("proto.line.m_friend_give_toc", m_friend_give_toc);
        SocketCommand.protoMap[2232577] = "proto.line.m_acc_pay_fetch_toc";
        ClassUtils.regClass("proto.line.m_acc_pay_fetch_toc", m_acc_pay_fetch_toc);
        SocketCommand.protoMap[787976] = "proto.line.m_friend_online_toc";
        ClassUtils.regClass("proto.line.m_friend_online_toc", m_friend_online_toc);
        SocketCommand.protoMap[10243593] = "proto.line.m_story_maze_use_item_toc";
        ClassUtils.regClass("proto.line.m_story_maze_use_item_toc", m_story_maze_use_item_toc);
        SocketCommand.protoMap[8142347] = "proto.line.m_modular_activity_lottery_tips_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_lottery_tips_toc", m_modular_activity_lottery_tips_toc);
        SocketCommand.protoMap[10243596] = "proto.line.m_story_maze_report_toc";
        ClassUtils.regClass("proto.line.m_story_maze_report_toc", m_story_maze_report_toc);
        SocketCommand.protoMap[5909762] = "proto.line.m_test_tower_into_floor_toc";
        ClassUtils.regClass("proto.line.m_test_tower_into_floor_toc", m_test_tower_into_floor_toc);
        SocketCommand.protoMap[8142350] = "proto.line.m_modular_activity_star_plan_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_star_plan_info_toc", m_modular_activity_star_plan_info_toc);
        SocketCommand.protoMap[8142351] = "proto.line.m_modular_activity_star_plan_update_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_star_plan_update_toc", m_modular_activity_star_plan_update_toc);
        SocketCommand.protoMap[8142352] = "proto.line.m_modular_activity_general_pass_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_general_pass_info_toc", m_modular_activity_general_pass_info_toc);
        SocketCommand.protoMap[8142353] = "proto.line.m_modular_activity_general_pass_fetch_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_general_pass_fetch_toc", m_modular_activity_general_pass_fetch_toc);
        SocketCommand.protoMap[8142354] = "proto.line.m_modular_activity_star_repay_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_star_repay_info_toc", m_modular_activity_star_repay_info_toc);
        SocketCommand.protoMap[5909763] = "proto.line.m_test_tower_fight_result_toc";
        ClassUtils.regClass("proto.line.m_test_tower_fight_result_toc", m_test_tower_fight_result_toc);
        SocketCommand.protoMap[8142337] = "proto.line.m_modular_activity_fetch_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_fetch_toc", m_modular_activity_fetch_toc);
        SocketCommand.protoMap[8142357] = "proto.line.m_modular_activity_maze_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_maze_info_toc", m_modular_activity_maze_info_toc);
        SocketCommand.protoMap[8142359] = "proto.line.m_modular_activity_maze_start_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_maze_start_toc", m_modular_activity_maze_start_toc);
        SocketCommand.protoMap[8142360] = "proto.line.m_modular_activity_maze_spoils_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_maze_spoils_toc", m_modular_activity_maze_spoils_toc);
        SocketCommand.protoMap[8142361] = "proto.line.m_modular_activity_maze_fetch_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_maze_fetch_toc", m_modular_activity_maze_fetch_toc);
        SocketCommand.protoMap[8142364] = "proto.line.m_modular_activity_maze_hero_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_maze_hero_toc", m_modular_activity_maze_hero_toc);
        SocketCommand.protoMap[8142365] = "proto.line.m_modular_activity_maze_pub_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_maze_pub_toc", m_modular_activity_maze_pub_toc);
        SocketCommand.protoMap[8142366] = "proto.line.m_modular_activity_maze_monster_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_maze_monster_toc", m_modular_activity_maze_monster_toc);
        SocketCommand.protoMap[8142367] = "proto.line.m_modular_activity_maze_fight_result_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_maze_fight_result_toc", m_modular_activity_maze_fight_result_toc);
        SocketCommand.protoMap[8142368] = "proto.line.m_modular_activity_maze_use_item_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_maze_use_item_toc", m_modular_activity_maze_use_item_toc);
        SocketCommand.protoMap[8142371] = "proto.line.m_modular_activity_report_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_report_toc", m_modular_activity_report_toc);
        SocketCommand.protoMap[8142372] = "proto.line.m_modular_activity_maze_auto_rolling_start_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_maze_auto_rolling_start_toc", m_modular_activity_maze_auto_rolling_start_toc);
        SocketCommand.protoMap[8142373] = "proto.line.m_modular_activity_maze_auto_rolling_end_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_maze_auto_rolling_end_toc", m_modular_activity_maze_auto_rolling_end_toc);
        SocketCommand.protoMap[8142374] = "proto.line.m_modular_activity_seven_goal_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_seven_goal_info_toc", m_modular_activity_seven_goal_info_toc);
        SocketCommand.protoMap[8142375] = "proto.line.m_modular_activity_seven_goal_update_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_seven_goal_update_toc", m_modular_activity_seven_goal_update_toc);
        SocketCommand.protoMap[8142376] = "proto.line.m_modular_activity_seven_goal_fetch_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_seven_goal_fetch_toc", m_modular_activity_seven_goal_fetch_toc);
        SocketCommand.protoMap[8142377] = "proto.line.m_modular_activity_seven_goal_gift_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_seven_goal_gift_toc", m_modular_activity_seven_goal_gift_toc);
        SocketCommand.protoMap[8142378] = "proto.line.m_modular_activity_hunt_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_hunt_info_toc", m_modular_activity_hunt_info_toc);
        SocketCommand.protoMap[8142379] = "proto.line.m_modular_activity_hunt_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_hunt_toc", m_modular_activity_hunt_toc);
        SocketCommand.protoMap[8142380] = "proto.line.m_modular_activity_hunt_logs_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_hunt_logs_toc", m_modular_activity_hunt_logs_toc);
        SocketCommand.protoMap[8142381] = "proto.line.m_modular_activity_hunt_refresh_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_hunt_refresh_toc", m_modular_activity_hunt_refresh_toc);
        SocketCommand.protoMap[8142382] = "proto.line.m_modular_activity_carnival_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_carnival_toc", m_modular_activity_carnival_toc);
        SocketCommand.protoMap[8142383] = "proto.line.m_modular_activity_login_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_login_info_toc", m_modular_activity_login_info_toc);
        SocketCommand.protoMap[7617027] = "proto.line.m_theme_activity_famous_lottery_info_toc";
        ClassUtils.regClass("proto.line.m_theme_activity_famous_lottery_info_toc", m_theme_activity_famous_lottery_info_toc);
        SocketCommand.protoMap[8011016] = "proto.line.m_hzzd_info_opp_toc";
        ClassUtils.regClass("proto.line.m_hzzd_info_opp_toc", m_hzzd_info_opp_toc);
        SocketCommand.protoMap[4924800] = "proto.line.m_daily_gift_info_toc";
        ClassUtils.regClass("proto.line.m_daily_gift_info_toc", m_daily_gift_info_toc);
        SocketCommand.protoMap[8142387] = "proto.line.m_modular_activity_dice_boss_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_dice_boss_toc", m_modular_activity_dice_boss_toc);
        SocketCommand.protoMap[8142388] = "proto.line.m_modular_activity_holiday_welfare_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_holiday_welfare_info_toc", m_modular_activity_holiday_welfare_info_toc);
        SocketCommand.protoMap[8142389] = "proto.line.m_modular_activity_holiday_welfare_update_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_holiday_welfare_update_toc", m_modular_activity_holiday_welfare_update_toc);
        SocketCommand.protoMap[8142390] = "proto.line.m_modular_activity_sign_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_sign_info_toc", m_modular_activity_sign_info_toc);
        SocketCommand.protoMap[8142391] = "proto.line.m_modular_activity_sign_update_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_sign_update_toc", m_modular_activity_sign_update_toc);
        SocketCommand.protoMap[8142392] = "proto.line.m_modular_activity_hero_challenge_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_hero_challenge_info_toc", m_modular_activity_hero_challenge_info_toc);
        SocketCommand.protoMap[8142394] = "proto.line.m_modular_activity_hero_challenge_report_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_hero_challenge_report_toc", m_modular_activity_hero_challenge_report_toc);
        SocketCommand.protoMap[8142395] = "proto.line.m_modular_activity_preview_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_preview_toc", m_modular_activity_preview_toc);
        SocketCommand.protoMap[8142396] = "proto.line.m_modular_activity_preview_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_preview_info_toc", m_modular_activity_preview_info_toc);
        SocketCommand.protoMap[10112266] = "proto.line.m_boat_race_role_items_toc";
        ClassUtils.regClass("proto.line.m_boat_race_role_items_toc", m_boat_race_role_items_toc);
        SocketCommand.protoMap[8142399] = "proto.line.m_modular_activity_story_group_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_story_group_toc", m_modular_activity_story_group_toc);
        SocketCommand.protoMap[8142400] = "proto.line.m_modular_activity_heaven_give_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_heaven_give_info_toc", m_modular_activity_heaven_give_info_toc);
        SocketCommand.protoMap[8142401] = "proto.line.m_modular_activity_customized_gift_select_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_customized_gift_select_toc", m_modular_activity_customized_gift_select_toc);
        SocketCommand.protoMap[8011019] = "proto.line.m_hzzd_battle_info_toc";
        ClassUtils.regClass("proto.line.m_hzzd_battle_info_toc", m_hzzd_battle_info_toc);
        SocketCommand.protoMap[8142404] = "proto.line.m_modular_activity_brick_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_brick_info_toc", m_modular_activity_brick_info_toc);
        SocketCommand.protoMap[8142405] = "proto.line.m_modular_activity_brick_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_brick_toc", m_modular_activity_brick_toc);
        SocketCommand.protoMap[8142406] = "proto.line.m_modular_activity_bless_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_bless_info_toc", m_modular_activity_bless_info_toc);
        SocketCommand.protoMap[8142407] = "proto.line.m_modular_activity_target_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_target_info_toc", m_modular_activity_target_info_toc);
        SocketCommand.protoMap[8142408] = "proto.line.m_modular_activity_wall_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_wall_info_toc", m_modular_activity_wall_info_toc);
        SocketCommand.protoMap[8142409] = "proto.line.m_modular_activity_exchange_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_exchange_info_toc", m_modular_activity_exchange_info_toc);
        SocketCommand.protoMap[8142410] = "proto.line.m_modular_activity_strategy_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_strategy_info_toc", m_modular_activity_strategy_info_toc);
        SocketCommand.protoMap[8142411] = "proto.line.m_modular_activity_lottery_target_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_lottery_target_info_toc", m_modular_activity_lottery_target_info_toc);
        SocketCommand.protoMap[656641] = "proto.line.m_login_chose_toc";
        ClassUtils.regClass("proto.line.m_login_chose_toc", m_login_chose_toc);
        SocketCommand.protoMap[8142413] = "proto.line.m_modular_activity_day_shop_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_day_shop_toc", m_modular_activity_day_shop_toc);
        SocketCommand.protoMap[1181952] = "proto.line.m_simp_mission_list_toc";
        ClassUtils.regClass("proto.line.m_simp_mission_list_toc", m_simp_mission_list_toc);
        SocketCommand.protoMap[8142416] = "proto.line.m_modular_activity_festival_wish_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_festival_wish_info_toc", m_modular_activity_festival_wish_info_toc);
        SocketCommand.protoMap[8142417] = "proto.line.m_modular_activity_festival_wish_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_festival_wish_toc", m_modular_activity_festival_wish_toc);
        SocketCommand.protoMap[5450112] = "proto.line.m_sys_concern_info_toc";
        ClassUtils.regClass("proto.line.m_sys_concern_info_toc", m_sys_concern_info_toc);
        SocketCommand.protoMap[9718272] = "proto.line.m_week_target_info_toc";
        ClassUtils.regClass("proto.line.m_week_target_info_toc", m_week_target_info_toc);
        SocketCommand.protoMap[1247625] = "proto.line.m_family_audit_limit_toc";
        ClassUtils.regClass("proto.line.m_family_audit_limit_toc", m_family_audit_limit_toc);
        SocketCommand.protoMap[10506244] = "proto.line.m_treasure_result_toc";
        ClassUtils.regClass("proto.line.m_treasure_result_toc", m_treasure_result_toc);
        SocketCommand.protoMap[7091718] = "proto.line.m_qxzl_info_bet_toc";
        ClassUtils.regClass("proto.line.m_qxzl_info_bet_toc", m_qxzl_info_bet_toc);
        SocketCommand.protoMap[10834567] = "proto.line.m_fish_replace_toc";
        ClassUtils.regClass("proto.line.m_fish_replace_toc", m_fish_replace_toc);
        SocketCommand.protoMap[8142340] = "proto.line.m_modular_activity_update_fetch_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_update_fetch_toc", m_modular_activity_update_fetch_toc);
        SocketCommand.protoMap[1510275] = "proto.line.m_ranking_simple_toc";
        ClassUtils.regClass("proto.line.m_ranking_simple_toc", m_ranking_simple_toc);
        SocketCommand.protoMap[3742849] = "proto.line.m_fuli_sign_fetch_toc";
        ClassUtils.regClass("proto.line.m_fuli_sign_fetch_toc", m_fuli_sign_fetch_toc);
        SocketCommand.protoMap[5975424] = "proto.line.m_pass_behead_info_toc";
        ClassUtils.regClass("proto.line.m_pass_behead_info_toc", m_pass_behead_info_toc);
        SocketCommand.protoMap[11622530] = "proto.line.m_wars_init_personal_toc";
        ClassUtils.regClass("proto.line.m_wars_init_personal_toc", m_wars_init_personal_toc);
        SocketCommand.protoMap[10243584] = "proto.line.m_story_maze_info_toc";
        ClassUtils.regClass("proto.line.m_story_maze_info_toc", m_story_maze_info_toc);
        SocketCommand.protoMap[2954881] = "proto.line.m_tax_silver_fetch_toc";
        ClassUtils.regClass("proto.line.m_tax_silver_fetch_toc", m_tax_silver_fetch_toc);
        SocketCommand.protoMap[5056128] = "proto.line.m_vip_kefu_qq_toc";
        ClassUtils.regClass("proto.line.m_vip_kefu_qq_toc", m_vip_kefu_qq_toc);
        SocketCommand.protoMap[8208001] = "proto.line.m_general_pass_gift_toc";
        ClassUtils.regClass("proto.line.m_general_pass_gift_toc", m_general_pass_gift_toc);
        SocketCommand.protoMap[853634] = "proto.line.m_letter_send_toc";
        ClassUtils.regClass("proto.line.m_letter_send_toc", m_letter_send_toc);
        SocketCommand.protoMap[2954883] = "proto.line.m_tax_forage_fetch_toc";
        ClassUtils.regClass("proto.line.m_tax_forage_fetch_toc", m_tax_forage_fetch_toc);
        SocketCommand.protoMap[11359876] = "proto.line.m_large_peak_info_battle_toc";
        ClassUtils.regClass("proto.line.m_large_peak_info_battle_toc", m_large_peak_info_battle_toc);
        SocketCommand.protoMap[11359878] = "proto.line.m_large_peak_info_bet_toc";
        ClassUtils.regClass("proto.line.m_large_peak_info_bet_toc", m_large_peak_info_bet_toc);
        SocketCommand.protoMap[11359879] = "proto.line.m_large_peak_info_history_toc";
        ClassUtils.regClass("proto.line.m_large_peak_info_history_toc", m_large_peak_info_history_toc);
        SocketCommand.protoMap[11359880] = "proto.line.m_large_peak_personal_info_toc";
        ClassUtils.regClass("proto.line.m_large_peak_personal_info_toc", m_large_peak_personal_info_toc);
        SocketCommand.protoMap[11359883] = "proto.line.m_large_peak_info_get_msg_toc";
        ClassUtils.regClass("proto.line.m_large_peak_info_get_msg_toc", m_large_peak_info_get_msg_toc);
        SocketCommand.protoMap[8142341] = "proto.line.m_modular_activity_shop_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_shop_toc", m_modular_activity_shop_toc);
        SocketCommand.protoMap[2166913] = "proto.line.m_pay_gift_fetch_toc";
        ClassUtils.regClass("proto.line.m_pay_gift_fetch_toc", m_pay_gift_fetch_toc);
        SocketCommand.protoMap[2232576] = "proto.line.m_acc_pay_info_toc";
        ClassUtils.regClass("proto.line.m_acc_pay_info_toc", m_acc_pay_info_toc);
        SocketCommand.protoMap[6500736] = "proto.line.m_guandu_info_toc";
        ClassUtils.regClass("proto.line.m_guandu_info_toc", m_guandu_info_toc);
        SocketCommand.protoMap[10768896] = "proto.line.m_boat_peak_info_toc";
        ClassUtils.regClass("proto.line.m_boat_peak_info_toc", m_boat_peak_info_toc);
        SocketCommand.protoMap[5909765] = "proto.line.m_test_tower_skip_toc";
        ClassUtils.regClass("proto.line.m_test_tower_skip_toc", m_test_tower_skip_toc);
        SocketCommand.protoMap[7091720] = "proto.line.m_qxzl_info_last_rank_toc";
        ClassUtils.regClass("proto.line.m_qxzl_info_last_rank_toc", m_qxzl_info_last_rank_toc);
        SocketCommand.protoMap[8011013] = "proto.line.m_hzzd_team_info_toc";
        ClassUtils.regClass("proto.line.m_hzzd_team_info_toc", m_hzzd_team_info_toc);
        SocketCommand.protoMap[8142342] = "proto.line.m_modular_activity_lottery_start_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_lottery_start_toc", m_modular_activity_lottery_start_toc);
        SocketCommand.protoMap[2757888] = "proto.line.m_lottery_start_toc";
        ClassUtils.regClass("proto.line.m_lottery_start_toc", m_lottery_start_toc);
        SocketCommand.protoMap[6500737] = "proto.line.m_guandu_op_toc";
        ClassUtils.regClass("proto.line.m_guandu_op_toc", m_guandu_op_toc);
        SocketCommand.protoMap[7026048] = "proto.line.m_worship_online_toc";
        ClassUtils.regClass("proto.line.m_worship_online_toc", m_worship_online_toc);
        SocketCommand.protoMap[10768904] = "proto.line.m_boat_peak_shop_toc";
        ClassUtils.regClass("proto.line.m_boat_peak_shop_toc", m_boat_peak_shop_toc);
        SocketCommand.protoMap[10768897] = "proto.line.m_boat_peak_opp_toc";
        ClassUtils.regClass("proto.line.m_boat_peak_opp_toc", m_boat_peak_opp_toc);
        SocketCommand.protoMap[11294208] = "proto.line.m_dominate_pvp_op_toc";
        ClassUtils.regClass("proto.line.m_dominate_pvp_op_toc", m_dominate_pvp_op_toc);
        SocketCommand.protoMap[919296] = "proto.line.m_item_use_toc";
        ClassUtils.regClass("proto.line.m_item_use_toc", m_item_use_toc);
        SocketCommand.protoMap[656645] = "proto.line.m_login_give_name_toc";
        ClassUtils.regClass("proto.line.m_login_give_name_toc", m_login_give_name_toc);
        SocketCommand.protoMap[8142343] = "proto.line.m_modular_activity_famous_lottery_info_toc";
        ClassUtils.regClass("proto.line.m_modular_activity_famous_lottery_info_toc", m_modular_activity_famous_lottery_info_toc);
        SocketCommand.protoMap[2757889] = "proto.line.m_lottery_info_toc";
        ClassUtils.regClass("proto.line.m_lottery_info_toc", m_lottery_info_toc);
        SocketCommand.protoMap[4596480] = "proto.line.m_recommend_lineup_info_toc";
        ClassUtils.regClass("proto.line.m_recommend_lineup_info_toc", m_recommend_lineup_info_toc);
        SocketCommand.protoMap[3283200] = "proto.line.m_title_list_toc";
        ClassUtils.regClass("proto.line.m_title_list_toc", m_title_list_toc);
        SocketCommand.protoMap[7026049] = "proto.line.m_worship_info_toc";
        ClassUtils.regClass("proto.line.m_worship_info_toc", m_worship_info_toc);
        SocketCommand.protoMap[7551360] = "proto.line.m_lcqs_chapter_info_toc";
        ClassUtils.regClass("proto.line.m_lcqs_chapter_info_toc", m_lcqs_chapter_info_toc);
        SocketCommand.protoMap[11294209] = "proto.line.m_dominate_pvp_info_toc";
        ClassUtils.regClass("proto.line.m_dominate_pvp_info_toc", m_dominate_pvp_info_toc);
        SocketCommand.protoMap[11819520] = "proto.line.m_building_info_toc";
        ClassUtils.regClass("proto.line.m_building_info_toc", m_building_info_toc);
        SocketCommand.protoMap[8339328] = "proto.line.m_multi_lineup_set_toc";
        ClassUtils.regClass("proto.line.m_multi_lineup_set_toc", m_multi_lineup_set_toc);
        SocketCommand.protoMap[2757890] = "proto.line.m_lottery_score_fetch_toc";
        ClassUtils.regClass("proto.line.m_lottery_score_fetch_toc", m_lottery_score_fetch_toc);
        SocketCommand.protoMap[3283201] = "proto.line.m_title_load_toc";
        ClassUtils.regClass("proto.line.m_title_load_toc", m_title_load_toc);
        SocketCommand.protoMap[10243587] = "proto.line.m_story_maze_spoils_toc";
        ClassUtils.regClass("proto.line.m_story_maze_spoils_toc", m_story_maze_spoils_toc);
        SocketCommand.protoMap[3808512] = "proto.line.m_seven_goal_info_toc";
        ClassUtils.regClass("proto.line.m_seven_goal_info_toc", m_seven_goal_info_toc);
        SocketCommand.protoMap[10768899] = "proto.line.m_boat_peak_look_member_toc";
        ClassUtils.regClass("proto.line.m_boat_peak_look_member_toc", m_boat_peak_look_member_toc);
        SocketCommand.protoMap[11294210] = "proto.line.m_dominate_pvp_match_toc";
        ClassUtils.regClass("proto.line.m_dominate_pvp_match_toc", m_dominate_pvp_match_toc);
        SocketCommand.protoMap[8339329] = "proto.line.m_multi_lineup_list_toc";
        ClassUtils.regClass("proto.line.m_multi_lineup_list_toc", m_multi_lineup_list_toc);
        SocketCommand.protoMap[11819521] = "proto.line.m_building_op_toc";
        ClassUtils.regClass("proto.line.m_building_op_toc", m_building_op_toc);
        SocketCommand.protoMap[11294212] = "proto.line.m_dominate_pvp_update_toc";
        ClassUtils.regClass("proto.line.m_dominate_pvp_update_toc", m_dominate_pvp_update_toc);
        SocketCommand.protoMap[9324288] = "proto.line.m_sdk_sns_reward_info_toc";
        ClassUtils.regClass("proto.line.m_sdk_sns_reward_info_toc", m_sdk_sns_reward_info_toc);
        SocketCommand.protoMap[7682688] = "proto.line.m_star_plan_info_toc";
        ClassUtils.regClass("proto.line.m_star_plan_info_toc", m_star_plan_info_toc);
        SocketCommand.protoMap[4071170] = "proto.line.m_guide_finish_toc";
        ClassUtils.regClass("proto.line.m_guide_finish_toc", m_guide_finish_toc);
        SocketCommand.protoMap[7223043] = "proto.line.m_cycle_activity_update_toc";
        ClassUtils.regClass("proto.line.m_cycle_activity_update_toc", m_cycle_activity_update_toc);
        SocketCommand.protoMap[6172420] = "proto.line.m_war_flag_exchange_toc";
        ClassUtils.regClass("proto.line.m_war_flag_exchange_toc", m_war_flag_exchange_toc);
        SocketCommand.protoMap[4071173] = "proto.line.m_guide_get_hero_toc";
        ClassUtils.regClass("proto.line.m_guide_get_hero_toc", m_guide_get_hero_toc);
        SocketCommand.protoMap[2626563] = "proto.line.m_main_battle_fetch_pass_toc";
        ClassUtils.regClass("proto.line.m_main_battle_fetch_pass_toc", m_main_battle_fetch_pass_toc);
        SocketCommand.protoMap[1904257] = "proto.line.m_gmcmd_panel_toc";
        ClassUtils.regClass("proto.line.m_gmcmd_panel_toc", m_gmcmd_panel_toc);
        SocketCommand.protoMap[2757891] = "proto.line.m_lottery_nation_start_toc";
        ClassUtils.regClass("proto.line.m_lottery_nation_start_toc", m_lottery_nation_start_toc);
        SocketCommand.protoMap[3283202] = "proto.line.m_title_update_toc";
        ClassUtils.regClass("proto.line.m_title_update_toc", m_title_update_toc);
        SocketCommand.protoMap[11622531] = "proto.line.m_wars_walk_list_toc";
        ClassUtils.regClass("proto.line.m_wars_walk_list_toc", m_wars_walk_list_toc);
        SocketCommand.protoMap[3808513] = "proto.line.m_seven_goal_update_toc";
        ClassUtils.regClass("proto.line.m_seven_goal_update_toc", m_seven_goal_update_toc);
        SocketCommand.protoMap[8733314] = "proto.line.m_ingenious_plan_update_toc";
        ClassUtils.regClass("proto.line.m_ingenious_plan_update_toc", m_ingenious_plan_update_toc);
        SocketCommand.protoMap[7551362] = "proto.line.m_lcqs_chapter_list_toc";
        ClassUtils.regClass("proto.line.m_lcqs_chapter_list_toc", m_lcqs_chapter_list_toc);
        SocketCommand.protoMap[11294211] = "proto.line.m_dominate_pvp_logs_toc";
        ClassUtils.regClass("proto.line.m_dominate_pvp_logs_toc", m_dominate_pvp_logs_toc);
        SocketCommand.protoMap[8601984] = "proto.line.m_xswh_info_toc";
        ClassUtils.regClass("proto.line.m_xswh_info_toc", m_xswh_info_toc);
        SocketCommand.protoMap[3480195] = "proto.line.m_hunt_refresh_toc";
        ClassUtils.regClass("proto.line.m_hunt_refresh_toc", m_hunt_refresh_toc);
        SocketCommand.protoMap[10834564] = "proto.line.m_fish_update_toc";
        ClassUtils.regClass("proto.line.m_fish_update_toc", m_fish_update_toc);
        SocketCommand.protoMap[656640] = "proto.line.m_login_add_toc";
        ClassUtils.regClass("proto.line.m_login_add_toc", m_login_add_toc);
        SocketCommand.protoMap[656648] = "proto.line.m_login_fetch_vip_toc";
        ClassUtils.regClass("proto.line.m_login_fetch_vip_toc", m_login_fetch_vip_toc);
        SocketCommand.protoMap[2429573] = "proto.line.m_hero_recycle_preview_toc";
        ClassUtils.regClass("proto.line.m_hero_recycle_preview_toc", m_hero_recycle_preview_toc);
        SocketCommand.protoMap[10834566] = "proto.line.m_fish_shop_toc";
        ClassUtils.regClass("proto.line.m_fish_shop_toc", m_fish_shop_toc);
        SocketCommand.protoMap[3808514] = "proto.line.m_seven_goal_fetch_toc";
        ClassUtils.regClass("proto.line.m_seven_goal_fetch_toc", m_seven_goal_fetch_toc);
        SocketCommand.protoMap[2429575] = "proto.line.m_hero_set_lineup_toc";
        ClassUtils.regClass("proto.line.m_hero_set_lineup_toc", m_hero_set_lineup_toc);
        SocketCommand.protoMap[4859136] = "proto.line.m_rank_activity_info_toc";
        ClassUtils.regClass("proto.line.m_rank_activity_info_toc", m_rank_activity_info_toc);
        SocketCommand.protoMap[3086208] = "proto.line.m_achievement_info_toc";
        ClassUtils.regClass("proto.line.m_achievement_info_toc", m_achievement_info_toc);
        SocketCommand.protoMap[8601985] = "proto.line.m_xswh_fight_result_toc";
        ClassUtils.regClass("proto.line.m_xswh_fight_result_toc", m_xswh_fight_result_toc);
        SocketCommand.protoMap[10834568] = "proto.line.m_fish_up_official_toc";
        ClassUtils.regClass("proto.line.m_fish_up_official_toc", m_fish_up_official_toc);
        SocketCommand.protoMap[9127296] = "proto.line.m_sys_use_times_info_toc";
        ClassUtils.regClass("proto.line.m_sys_use_times_info_toc", m_sys_use_times_info_toc);
        SocketCommand.protoMap[2429577] = "proto.line.m_hero_lineup_info_toc";
        ClassUtils.regClass("proto.line.m_hero_lineup_info_toc", m_hero_lineup_info_toc);
        SocketCommand.protoMap[656649] = "proto.line.m_login_filter_fun_toc";
        ClassUtils.regClass("proto.line.m_login_filter_fun_toc", m_login_filter_fun_toc);
        SocketCommand.protoMap[1707265] = "proto.line.m_role_score_update_toc";
        ClassUtils.regClass("proto.line.m_role_score_update_toc", m_role_score_update_toc);
        SocketCommand.protoMap[9586946] = "proto.line.m_team_xswh_rank_toc";
        ClassUtils.regClass("proto.line.m_team_xswh_rank_toc", m_team_xswh_rank_toc);
        SocketCommand.protoMap[3808515] = "proto.line.m_seven_goal_gift_toc";
        ClassUtils.regClass("proto.line.m_seven_goal_gift_toc", m_seven_goal_gift_toc);
        SocketCommand.protoMap[2429580] = "proto.line.m_hero_bag_expansion_toc";
        ClassUtils.regClass("proto.line.m_hero_bag_expansion_toc", m_hero_bag_expansion_toc);
        SocketCommand.protoMap[10768902] = "proto.line.m_boat_peak_bet_toc";
        ClassUtils.regClass("proto.line.m_boat_peak_bet_toc", m_boat_peak_bet_toc);
        SocketCommand.protoMap[919297] = "proto.line.m_item_compose_toc";
        ClassUtils.regClass("proto.line.m_item_compose_toc", m_item_compose_toc);
        SocketCommand.protoMap[2429581] = "proto.line.m_hero_recycle_all_toc";
        ClassUtils.regClass("proto.line.m_hero_recycle_all_toc", m_hero_recycle_all_toc);
        SocketCommand.protoMap[9127297] = "proto.line.m_sys_use_times_update_toc";
        ClassUtils.regClass("proto.line.m_sys_use_times_update_toc", m_sys_use_times_update_toc);
        SocketCommand.protoMap[9652608] = "proto.line.m_master_card_info_toc";
        ClassUtils.regClass("proto.line.m_master_card_info_toc", m_master_card_info_toc);
        SocketCommand.protoMap[9915265] = "proto.line.m_stage_copy_sweep_toc";
        ClassUtils.regClass("proto.line.m_stage_copy_sweep_toc", m_stage_copy_sweep_toc);
        SocketCommand.protoMap[2429582] = "proto.line.m_hero_recycle_gains_item_toc";
        ClassUtils.regClass("proto.line.m_hero_recycle_gains_item_toc", m_hero_recycle_gains_item_toc);
        SocketCommand.protoMap[1247616] = "proto.line.m_family_list_toc";
        ClassUtils.regClass("proto.line.m_family_list_toc", m_family_list_toc);
        SocketCommand.protoMap[2757894] = "proto.line.m_lottery_supreme_start_toc";
        ClassUtils.regClass("proto.line.m_lottery_supreme_start_toc", m_lottery_supreme_start_toc);
        SocketCommand.protoMap[10243592] = "proto.line.m_story_maze_fight_result_toc";
        ClassUtils.regClass("proto.line.m_story_maze_fight_result_toc", m_story_maze_fight_result_toc);
        SocketCommand.protoMap[10768903] = "proto.line.m_boat_peak_his_toc";
        ClassUtils.regClass("proto.line.m_boat_peak_his_toc", m_boat_peak_his_toc);
        SocketCommand.protoMap[5581441] = "proto.line.m_share_fetch_toc";
        ClassUtils.regClass("proto.line.m_share_fetch_toc", m_share_fetch_toc);
        SocketCommand.protoMap[1641600] = "proto.line.m_icon_list_toc";
        ClassUtils.regClass("proto.line.m_icon_list_toc", m_icon_list_toc);
        SocketCommand.protoMap[8601987] = "proto.line.m_xswh_best_rank_toc";
        ClassUtils.regClass("proto.line.m_xswh_best_rank_toc", m_xswh_best_rank_toc);
        SocketCommand.protoMap[8536322] = "proto.line.m_cross_ladder_opp_info_toc";
        ClassUtils.regClass("proto.line.m_cross_ladder_opp_info_toc", m_cross_ladder_opp_info_toc);
        SocketCommand.protoMap[5909760] = "proto.line.m_test_tower_process_toc";
        ClassUtils.regClass("proto.line.m_test_tower_process_toc", m_test_tower_process_toc);
        SocketCommand.protoMap[7223042] = "proto.line.m_cycle_activity_gift_toc";
        ClassUtils.regClass("proto.line.m_cycle_activity_gift_toc", m_cycle_activity_gift_toc);
        SocketCommand.protoMap[4727808] = "proto.line.m_mission_shop_info_toc";
        ClassUtils.regClass("proto.line.m_mission_shop_info_toc", m_mission_shop_info_toc);
        SocketCommand.protoMap[6435074] = "proto.line.m_buy_times_update_toc";
        ClassUtils.regClass("proto.line.m_buy_times_update_toc", m_buy_times_update_toc);
        SocketCommand.protoMap[6238080] = "proto.line.m_god_weapon_info_toc";
        ClassUtils.regClass("proto.line.m_god_weapon_info_toc", m_god_weapon_info_toc);
        SocketCommand.protoMap[6238081] = "proto.line.m_god_weapon_upgrade_toc";
        ClassUtils.regClass("proto.line.m_god_weapon_upgrade_toc", m_god_weapon_upgrade_toc);
        SocketCommand.protoMap[5187458] = "proto.line.m_bingfu_operate_toc";
        ClassUtils.regClass("proto.line.m_bingfu_operate_toc", m_bingfu_operate_toc);
        SocketCommand.protoMap[7288707] = "proto.line.m_csc_fmsolo_state_toc";
        ClassUtils.regClass("proto.line.m_csc_fmsolo_state_toc", m_csc_fmsolo_state_toc);
        SocketCommand.protoMap[5187460] = "proto.line.m_bingfu_compose_toc";
        ClassUtils.regClass("proto.line.m_bingfu_compose_toc", m_bingfu_compose_toc);
        SocketCommand.protoMap[7288709] = "proto.line.m_csc_fmsolo_shop_lv_toc";
        ClassUtils.regClass("proto.line.m_csc_fmsolo_shop_lv_toc", m_csc_fmsolo_shop_lv_toc);
        SocketCommand.protoMap[5187462] = "proto.line.m_bingfu_refine_toc";
        ClassUtils.regClass("proto.line.m_bingfu_refine_toc", m_bingfu_refine_toc);
        SocketCommand.protoMap[1641601] = "proto.line.m_icon_switch_toc";
        ClassUtils.regClass("proto.line.m_icon_switch_toc", m_icon_switch_toc);
        SocketCommand.protoMap[2166912] = "proto.line.m_pay_gift_info_toc";
        ClassUtils.regClass("proto.line.m_pay_gift_info_toc", m_pay_gift_info_toc);
        SocketCommand.protoMap[5909761] = "proto.line.m_test_tower_fetch_toc";
        ClassUtils.regClass("proto.line.m_test_tower_fetch_toc", m_test_tower_fetch_toc);
        SocketCommand.protoMap[6435072] = "proto.line.m_buy_times_info_toc";
        ClassUtils.regClass("proto.line.m_buy_times_info_toc", m_buy_times_info_toc);
        SocketCommand.protoMap[10177921] = "proto.line.m_master_talent_science_info_toc";
        ClassUtils.regClass("proto.line.m_master_talent_science_info_toc", m_master_talent_science_info_toc);
        SocketCommand.protoMap[10703232] = "proto.line.m_fight_share_operate_toc";
        ClassUtils.regClass("proto.line.m_fight_share_operate_toc", m_fight_share_operate_toc);
        SocketCommand.protoMap[8404995] = "proto.line.m_peak_info_look_member_toc";
        ClassUtils.regClass("proto.line.m_peak_info_look_member_toc", m_peak_info_look_member_toc);
        SocketCommand.protoMap[7288706] = "proto.line.m_csc_fmsolo_fetch_toc";
        ClassUtils.regClass("proto.line.m_csc_fmsolo_fetch_toc", m_csc_fmsolo_fetch_toc);
        SocketCommand.protoMap[10506245] = "proto.line.m_treasure_role_info_toc";
        ClassUtils.regClass("proto.line.m_treasure_role_info_toc", m_treasure_role_info_toc);
        SocketCommand.protoMap[5581440] = "proto.line.m_share_info_toc";
        ClassUtils.regClass("proto.line.m_share_info_toc", m_share_info_toc);
        SocketCommand.protoMap[1116291] = "proto.line.m_role_rename_toc";
        ClassUtils.regClass("proto.line.m_role_rename_toc", m_role_rename_toc);
        SocketCommand.protoMap[1707264] = "proto.line.m_role_score_list_toc";
        ClassUtils.regClass("proto.line.m_role_score_list_toc", m_role_score_list_toc);
        SocketCommand.protoMap[1641602] = "proto.line.m_icon_switch_list_toc";
        ClassUtils.regClass("proto.line.m_icon_switch_list_toc", m_icon_switch_list_toc);
        SocketCommand.protoMap[3348866] = "proto.line.m_hanging_quick_toc";
        ClassUtils.regClass("proto.line.m_hanging_quick_toc", m_hanging_quick_toc);
        SocketCommand.protoMap[2692224] = "proto.line.m_fight_start_toc";
        ClassUtils.regClass("proto.line.m_fight_start_toc", m_fight_start_toc);
        SocketCommand.protoMap[9652611] = "proto.line.m_master_card_update_toc";
        ClassUtils.regClass("proto.line.m_master_card_update_toc", m_master_card_update_toc);
        SocketCommand.protoMap[6960384] = "proto.line.m_family_active_info_toc";
        ClassUtils.regClass("proto.line.m_family_active_info_toc", m_family_active_info_toc);
        SocketCommand.protoMap[10703233] = "proto.line.m_fight_share_stat_toc";
        ClassUtils.regClass("proto.line.m_fight_share_stat_toc", m_fight_share_stat_toc);
        SocketCommand.protoMap[11228544] = "proto.line.m_hero_cheer_info_toc";
        ClassUtils.regClass("proto.line.m_hero_cheer_info_toc", m_hero_cheer_info_toc);
        SocketCommand.protoMap[11425538] = "proto.line.m_divine_equip_compose_toc";
        ClassUtils.regClass("proto.line.m_divine_equip_compose_toc", m_divine_equip_compose_toc);
        SocketCommand.protoMap[1116292] = "proto.line.m_role_rename_info_toc";
        ClassUtils.regClass("proto.line.m_role_rename_info_toc", m_role_rename_info_toc);
        SocketCommand.protoMap[3611521] = "proto.line.m_arena_info_toc";
        ClassUtils.regClass("proto.line.m_arena_info_toc", m_arena_info_toc);
        SocketCommand.protoMap[2692225] = "proto.line.m_fight_finish_toc";
        ClassUtils.regClass("proto.line.m_fight_finish_toc", m_fight_finish_toc);
        SocketCommand.protoMap[3217536] = "proto.line.m_family_task_info_toc";
        ClassUtils.regClass("proto.line.m_family_task_info_toc", m_family_task_info_toc);
        SocketCommand.protoMap[7485696] = "proto.line.m_option_lottery_start_toc";
        ClassUtils.regClass("proto.line.m_option_lottery_start_toc", m_option_lottery_start_toc);
        SocketCommand.protoMap[11228545] = "proto.line.m_hero_cheer_op_toc";
        ClassUtils.regClass("proto.line.m_hero_cheer_op_toc", m_hero_cheer_op_toc);
        SocketCommand.protoMap[11753856] = "proto.line.m_hongbao_info_toc";
        ClassUtils.regClass("proto.line.m_hongbao_info_toc", m_hongbao_info_toc);
        SocketCommand.protoMap[9586947] = "proto.line.m_team_xswh_best_rank_toc";
        ClassUtils.regClass("proto.line.m_team_xswh_best_rank_toc", m_team_xswh_best_rank_toc);
        SocketCommand.protoMap[1116293] = "proto.line.m_role_acclogin_toc";
        ClassUtils.regClass("proto.line.m_role_acclogin_toc", m_role_acclogin_toc);
        SocketCommand.protoMap[5909764] = "proto.line.m_test_tower_sweep_toc";
        ClassUtils.regClass("proto.line.m_test_tower_sweep_toc", m_test_tower_sweep_toc);
        SocketCommand.protoMap[3217537] = "proto.line.m_family_task_update_toc";
        ClassUtils.regClass("proto.line.m_family_task_update_toc", m_family_task_update_toc);
        SocketCommand.protoMap[3742848] = "proto.line.m_fuli_sign_info_toc";
        ClassUtils.regClass("proto.line.m_fuli_sign_info_toc", m_fuli_sign_info_toc);
        SocketCommand.protoMap[7485697] = "proto.line.m_option_lottery_info_toc";
        ClassUtils.regClass("proto.line.m_option_lottery_info_toc", m_option_lottery_info_toc);
        SocketCommand.protoMap[8011008] = "proto.line.m_hzzd_info_toc";
        ClassUtils.regClass("proto.line.m_hzzd_info_toc", m_hzzd_info_toc);
        SocketCommand.protoMap[1247617] = "proto.line.m_family_create_toc";
        ClassUtils.regClass("proto.line.m_family_create_toc", m_family_create_toc);
        SocketCommand.protoMap[8601988] = "proto.line.m_xswh_fetch_toc";
        ClassUtils.regClass("proto.line.m_xswh_fetch_toc", m_xswh_fetch_toc);
        SocketCommand.protoMap[3677185] = "proto.line.m_eight_login_fetch_toc";
        ClassUtils.regClass("proto.line.m_eight_login_fetch_toc", m_eight_login_fetch_toc);
        SocketCommand.protoMap[1116294] = "proto.line.m_role_look_profile_toc";
        ClassUtils.regClass("proto.line.m_role_look_profile_toc", m_role_look_profile_toc);
    }
}
SocketCommand.BINGFU_INFO = 5187456;
SocketCommand.STAGE_COPY_DROP_GROUP = 9915267;
SocketCommand.SHOP_UPDATE = 2363907;
SocketCommand.MINI_GAME_GET_STR = 9061632;
SocketCommand.FULI_YUEKA_INFO = 3742851;
SocketCommand.MAIN_BATTLE_BOX_RATE = 2626574;
SocketCommand.MODULAR_ACTIVITY_EXCHANGE_INFO = 8142409;
SocketCommand.ARENA_INFO = 3611521;
SocketCommand.FAMILY_ACTIVE_UP = 6960385;
SocketCommand.MODULAR_ACTIVITY_BLESS_INFO = 8142406;
SocketCommand.TREASURE_INFO = 10506240;
SocketCommand.FISH_NOTICE = 10834569;
SocketCommand.QQVIP_FETCH = 4990465;
SocketCommand.WORLD_BOSS_FIGHT_RESULT = 7354370;
SocketCommand.MODULAR_ACTIVITY_MAZE_AUTO_ROLLING_START = 8142372;
SocketCommand.PLAYER_STRATEGY_UPDATE = 8930306;
SocketCommand.FIGHT_START = 2692224;
SocketCommand.XSWH_FIGHT_RESULT = 8601985;
SocketCommand.MOCK_PVP_CLEAN_CHAT = 11031562;
SocketCommand.LOGIN_FILTER_FUN = 656649;
SocketCommand.TAX_SILVER_INFO = 2954880;
SocketCommand.HONGBAO_INFO_UPDATE = 11753859;
SocketCommand.PAYMENT_PAY_RESULT = 1772931;
SocketCommand.HZZD_INFO = 8011008;
SocketCommand.RETRIEVAL_INFO = 11097217;
SocketCommand.ROLE_SETTING2 = 1116305;
SocketCommand.GUANDU_INFO = 6500736;
SocketCommand.BOAT_PEAK_JOIN_MEMBERS = 10768907;
SocketCommand.MOCK_PVP_LINEUP_PREVIEW = 11031564;
SocketCommand.QXZL_INFO_MEMBER = 7091714;
SocketCommand.ONLINE_REWARD_FETCH = 3414529;
SocketCommand.LOGIN_FETCH_VIP = 656648;
SocketCommand.CSCLAN_SOLO_SHOP_LV = 11556870;
SocketCommand.BOAT_PEAK_LOOK_MEMBER = 10768899;
SocketCommand.ACTIVITY_SHOP_UPDATE = 1444613;
SocketCommand.WARS_DAILY_REPORT = 11622558;
SocketCommand.WARS_SELECT_ARMY = 11622549;
SocketCommand.MODULAR_ACTIVITY_HOLIDAY_WELFARE_UPDATE = 8142389;
SocketCommand.PROGRESS_GIFT_INFO = 6894720;
SocketCommand.MAIN_BATTLE_AUTO_END = 2626571;
SocketCommand.FAMILY_BOSS_INFO = 6303744;
SocketCommand.RANKING_REWARD = 1510273;
SocketCommand.PAYMENT_BUY = 1772932;
SocketCommand.GUANDU_NEXT = 6500740;
SocketCommand.MODULAR_ACTIVITY_UPDATE_MISSION = 8142339;
SocketCommand.ACTIVITY_LIMIT_SIGN_INFO = 1444618;
SocketCommand.CSCLAN_OPERATE = 10637575;
SocketCommand.CSCLAN_BASE = 10637580;
SocketCommand.TIME_ACHIEVEMENT_INFO = 6632064;
SocketCommand.PEAK_INFO_BATTLE = 8404996;
SocketCommand.ROLE_LOOK_HERO_ATTR_SOURCE = 1116304;
SocketCommand.HERO_COMMENT_INFO = 4399488;
SocketCommand.OPTION_LOTTERY_START = 7485696;
SocketCommand.FRIEND_GIVE = 787974;
SocketCommand.ICON_SWITCH_LIST = 1641602;
SocketCommand.STAGE_COPY_INFO = 9915264;
SocketCommand.DEPUTY_INFO = 2495258;
SocketCommand.RENT_HERO_OP = 5318785;
SocketCommand.MODULAR_ACTIVITY_INFO = 8142338;
SocketCommand.TREASURE_CAL_TIME = 10506246;
SocketCommand.CSCLAN_SET_OWNER = 10637573;
SocketCommand.PEAK_PERSONAL_INFO = 8405002;
SocketCommand.TEQUAN_INFO = 2298240;
SocketCommand.TRAVEL_SPEED = 3151875;
SocketCommand.PASS_CHECK_GIFT = 7420034;
SocketCommand.FAMILY_UPDATE_MEMBER = 1247632;
SocketCommand.HERO_EVOLVE_INFO = 2429591;
SocketCommand.HERO_EVOLVE_OP = 2429590;
SocketCommand.PEAK_INFO = 8404992;
SocketCommand.BROADCAST_NORMAL = 1313280;
SocketCommand.MICROTERMINAL_INFO = 9389952;
SocketCommand.PAYMENT_SHOP_INFO = 1772933;
SocketCommand.HERO_RECYCLE = 2429574;
SocketCommand.GOD_TRIAL_BUFF = 5778434;
SocketCommand.BATTLE_TRIAL_HANGING_INFO = 9521285;
SocketCommand.FAMILY_HONGBAO_FETCH_UPDATE = 4465156;
SocketCommand.SOUL_HERO_INFO = 8798976;
SocketCommand.HUNT_GIFT = 3480194;
SocketCommand.MAIN_BATTLE_MISSION_INFO = 2626564;
SocketCommand.GOD_TRIAL_INFO = 5778432;
SocketCommand.SYSTEM_TIME = 1378951;
SocketCommand.HONGBAO_FETCH = 11753858;
SocketCommand.DIVINE_COPY_SWEEP = 11491200;
SocketCommand.CSCLAN_LIST = 10637568;
SocketCommand.MODULAR_ACTIVITY_MAZE_FIGHT_RESULT = 8142367;
SocketCommand.ROLE_LOOK_SDK_PROFILE = 1116306;
SocketCommand.MAIN_BATTLE_AUTO = 2626570;
SocketCommand.SEVEN_GOAL_UPDATE = 3808513;
SocketCommand.TREASURE_WORKER_ACTIVE = 10506252;
SocketCommand.PASS_CHECK_INFO = 7420032;
SocketCommand.INGENIOUS_PLAN_UPDATE = 8733314;
SocketCommand.BOAT_RACE_BROADCAST = 10112262;
SocketCommand.MODULAR_ACTIVITY_FESTIVAL_WISH = 8142417;
SocketCommand.BINGFU_OPERATE = 5187458;
SocketCommand.FAMILY_APPLY_LIST = 1247620;
SocketCommand.STAGE_BREED_UPGRADE = 9849602;
SocketCommand.ACTIVITY_INFO = 1444610;
SocketCommand.CSC_FMSOLO_STATE = 7288707;
SocketCommand.CHAT_PRIVATE_HISTORY = 590745;
SocketCommand.RANK_MISSION_UPDATE = 6763393;
SocketCommand.CHAT_AUTH = 590725;
SocketCommand.LCQS_CHAPTER_LIST = 7551362;
SocketCommand.BATTLE_TRIAL_OFFLINE_INFO = 9521284;
SocketCommand.MODULAR_ACTIVITY_SEVEN_GOAL_UPDATE = 8142375;
SocketCommand.RANKING_LIST = 1510272;
SocketCommand.HERO_COST_INFO = 9192960;
SocketCommand.FRIEND_REFRESH = 787969;
SocketCommand.CSCLAN_SELF = 10637570;
SocketCommand.GOD_WEAPON_INFO = 6238080;
SocketCommand.WARS_UPDATE_CITY = 11622532;
SocketCommand.MOCK_PVP_INFO = 11031553;
SocketCommand.FAMILY_HONGBAO_MISSION_UPDATE = 4465159;
SocketCommand.MAIN_BATTLE_BOX_OPEN = 2626569;
SocketCommand.DAILY_COPY_SWEEP = 2889218;
SocketCommand.TAX_FORAGE_FETCH = 2954883;
SocketCommand.MOCK_PVP_SCHEMES = 11031556;
SocketCommand.EQUIP_AUTO_COMPOSE_INFO = 2495242;
SocketCommand.HZZD_LOOK_KILL = 8011020;
SocketCommand.MODULAR_ACTIVITY_MAZE_FETCH = 8142361;
SocketCommand.LORD_LINEUP_LIST = 10571908;
SocketCommand.FAMILY_RENAME = 1247633;
SocketCommand.BINGFU_COMPOSE = 5187460;
SocketCommand.STORY_MAZE_USE_ITEM = 10243593;
SocketCommand.PAYMENT_SHOP = 1772928;
SocketCommand.GOD_EQUIP_SELECT = 2560898;
SocketCommand.MODULAR_ACTIVITY_TARGET_INFO = 8142407;
SocketCommand.BOAT_RACE_UPDATE = 10112258;
SocketCommand.BUILDING_OP = 11819521;
SocketCommand.FAMILY_BOSS_FIGHT_RESULT = 6303749;
SocketCommand.EQUIP_COMPOSE = 2495241;
SocketCommand.MODULAR_ACTIVITY_HUNT_INFO = 8142378;
SocketCommand.RANKING_WORSHIP = 1510274;
SocketCommand.LOGIN_RE_CONNECT = 656651;
SocketCommand.BOAT_PEAK_INFO = 10768896;
SocketCommand.PROFILE_INFO = 3020544;
SocketCommand.THEME_ACTIVITY_WISH_LOTTERY_INFO = 7617030;
SocketCommand.MAIN_BATTLE_FETCH_PASS = 2626563;
SocketCommand.STAGE_BREED_INFO = 9849600;
SocketCommand.MODULAR_ACTIVITY_MAZE_MONSTER = 8142366;
SocketCommand.FIGHT_FINISH_TIMES = 2692228;
SocketCommand.HZZD_ROUTE_INFO = 8011009;
SocketCommand.FULI_SIGN_ACC = 3742850;
SocketCommand.QXZL_INFO_OPP = 7091713;
SocketCommand.MOCK_PVP_READY = 11031559;
SocketCommand.DOMINATE_PVP_MATCH = 11294210;
SocketCommand.TD_MISSION_INFO = 10965895;
SocketCommand.HERO_RECYCLE_TIMES = 2429588;
SocketCommand.GUANDU_UPDATE_EVENT = 6500738;
SocketCommand.FAMILY_HOT = 1247639;
SocketCommand.HERO_PASS_UPDATE = 7748356;
SocketCommand.CSCLAN_SCENE = 10637581;
SocketCommand.TEST_TOWER_INTO_FLOOR = 5909762;
SocketCommand.DAILY_FULI_INFO = 5647104;
SocketCommand.FAMILY_ATTR_TIP = 1247635;
SocketCommand.HERO_SKIN_ACTIVE = 5121794;
SocketCommand.GMCMD_PANEL = 1904257;
SocketCommand.SDK_REWARD_INFO = 9258624;
SocketCommand.WARS_ACT_INFO = 11622540;
SocketCommand.PAYMENT_REQUEST = 1772929;
SocketCommand.THEME_ACTIVITY_UP_STAR_REWARD_INFO = 7617032;
SocketCommand.ICON_SWITCH = 1641601;
SocketCommand.MODULAR_ACTIVITY_WALL_INFO = 8142408;
SocketCommand.LOGIN_RELOGIN = 656642;
SocketCommand.FISH_LOGS = 10834573;
SocketCommand.DAILY_GIFT_TEHUI_INFO = 4924803;
SocketCommand.TRAVEL_FETCH = 3151876;
SocketCommand.MODULAR_ACTIVITY_MAZE_START = 8142359;
SocketCommand.WARS_INIT_CAMP = 11622555;
SocketCommand.MODULAR_ACTIVITY_DICE_BOSS = 8142387;
SocketCommand.LOGIN_GEN_NAME = 656643;
SocketCommand.ACTIVITY_GIFT = 1444617;
SocketCommand.LOTTERY_SUPREME_INFO = 2757893;
SocketCommand.TD_SIMP_INFO = 10965894;
SocketCommand.HERO_STRENGTHEN_INFO = 8470656;
SocketCommand.WARS_KILL_INFO = 11622548;
SocketCommand.RANDOM_PVP_PEAK = 7879683;
SocketCommand.HINT_SHOW_TIP = 2035585;
SocketCommand.WARS_SIGN = 11622535;
SocketCommand.FRIEND_ONLINE = 787976;
SocketCommand.VIP_BUY_GIFT = 4268161;
SocketCommand.GENERAL_PASS_GIFT = 8208001;
SocketCommand.SYSTEM_SETTING = 1378949;
SocketCommand.TD_FIGHT = 10965891;
SocketCommand.SHOP_BUY = 2363905;
SocketCommand.MICROTERMINAL_SIGN_FETCH = 9389953;
SocketCommand.GUIDE_STEP = 4071171;
SocketCommand.CHANGE_DAY = 1378950;
SocketCommand.HERO_RESONATE_DHYANA_OP = 8273667;
SocketCommand.STAGE_COPY_FETCH = 9915266;
SocketCommand.WARS_LOOK_OP_CITY_LOG = 11622546;
SocketCommand.PASS_CHECK_UPDATE = 7420035;
SocketCommand.ROLE_BASE_RELOAD = 1116290;
SocketCommand.ROLE_BEST_HERO = 1116300;
SocketCommand.GOD_EQUIP_CONVERT = 2560899;
SocketCommand.LARGE_PEAK_INFO_GET_MSG = 11359883;
SocketCommand.HERO_RECYCLE_DOWN = 2429587;
SocketCommand.CSCLAN_AUDIT_LIMIT = 10637571;
SocketCommand.SMALL_GAME_INFO = 8995968;
SocketCommand.TREASURE_LOG = 10506243;
SocketCommand.ROLE_LOOK_HERO = 1116303;
SocketCommand.DOMINATE_PVP_FIGHT_RESULT = 11294213;
SocketCommand.MODULAR_ACTIVITY_LOTTERY_TARGET_INFO = 8142411;
SocketCommand.BOAT_PEAK_BET = 10768902;
SocketCommand.WEEK_TARGET_INFO = 9718272;
SocketCommand.WARS_TEAM_OP = 11622536;
SocketCommand.STAGE_SKILL_INFO = 11162880;
SocketCommand.ROLE_LOOK_PROFILE = 1116294;
SocketCommand.HERO_LINEUP_INFO = 2429577;
SocketCommand.EQUIP_LOCK = 2495254;
SocketCommand.MISSION_SHOP_BUY = 4727810;
SocketCommand.FAMILY_CANCEL_JOIN = 1247623;
SocketCommand.CSCLAN_LOGS = 10637578;
SocketCommand.ACC_PAY_FETCH = 2232577;
SocketCommand.EQUIP_BINGFU_DECOMPOSE = 2495250;
SocketCommand.TEAM_INFO_MY_TEAM = 9455617;
SocketCommand.MODULAR_ACTIVITY_STRATEGY_INFO = 8142410;
SocketCommand.RANDOM_PVP_INFO = 7879681;
SocketCommand.DAILY_PAY_INFO = 4005504;
SocketCommand.MODULAR_ACTIVITY_STAR_PLAN_INFO = 8142350;
SocketCommand.LORD_INFO = 10571905;
SocketCommand.STORY_MAZE_REPORT = 10243596;
SocketCommand.FISH_FISHBOWL_ATTR = 10834561;
SocketCommand.TEST_TOWER_AUTO_SWEEP = 5909768;
SocketCommand.MAZE_MONSTER = 7945353;
SocketCommand.TRAVEL_INFO = 3151872;
SocketCommand.HERO_UPDATE_FIGHT = 2429570;
SocketCommand.CSCLAN_SOLO_BOX = 11556869;
SocketCommand.MODULAR_ACTIVITY_HUNT = 8142379;
SocketCommand.WELFARE_SDK_SHARE_INFO = 1575938;
SocketCommand.FAMILY_TRANSFER_COUNT = 1247637;
SocketCommand.MODULAR_ACTIVITY_SIGN_UPDATE = 8142391;
SocketCommand.ACTIVITY_UPDATE_FETCH = 1444609;
SocketCommand.CSCLAN_MEMBERS = 10637576;
SocketCommand.LARGE_PEAK_INFO_BET = 11359878;
SocketCommand.RENT_HERO_LIST = 5318784;
SocketCommand.RANDOM_PVP_OP = 7879680;
SocketCommand.LOGIN_GIVE_NAME = 656645;
SocketCommand.MASTER_CARD_DECORATION_INFO = 9652619;
SocketCommand.ROLE_SETTING_LIST = 1116295;
SocketCommand.CSCLAN_SET_TITLE = 10637572;
SocketCommand.MODULAR_ACTIVITY_MAZE_HERO = 8142364;
SocketCommand.BOAT_RACE_INFO = 10112256;
SocketCommand.MAZE_FETCH = 7945348;
SocketCommand.WAR_FLAG_EXCHANGE = 6172420;
SocketCommand.FAMILY_HONGBAO_MISSION_INFO = 4465157;
SocketCommand.LCQS_CHAPTER_INFO = 7551360;
SocketCommand.BATTLE_TRIAL_DAILY_END_INFO = 9521283;
SocketCommand.WORLD_BOSS_BUY_TIMES = 7354369;
SocketCommand.WARS_TEAM_HERO_INFO = 11622538;
SocketCommand.CSCLAN_UPDATE_NOTICE = 10637574;
SocketCommand.HZZD_BATTLE_INFO = 8011019;
SocketCommand.FAMILY_BOSS_LOOK = 6303747;
SocketCommand.SEVEN_GOAL_INFO = 3808512;
SocketCommand.RENT_HERO_LOOK = 5318786;
SocketCommand.FAMILY_TASK_REWARD = 3217539;
SocketCommand.QXZL_INFO_BATTLE = 7091716;
SocketCommand.HERO_LOCK = 2429572;
SocketCommand.STAR_PLAN_INFO = 7682688;
SocketCommand.MOCK_PVP_LOOK_HERO = 11031554;
SocketCommand.MOCK_PVP_CONTEST_CHAT = 11031561;
SocketCommand.TREASURE_FETCH = 10506253;
SocketCommand.HZZD_ROUTE_SELECT_INFO = 8011018;
SocketCommand.GOODS_SHOW_GOODS = 1050626;
SocketCommand.ICON_LIST = 1641600;
SocketCommand.ARENA_UPDATE = 3611522;
SocketCommand.WARS_CITY_INFO = 11622533;
SocketCommand.TREASURE_OTHER = 10506242;
SocketCommand.MAZE_HERO = 7945351;
SocketCommand.HERO_SET_LINEUP = 2429575;
SocketCommand.CSC_FMSOLO_SHOP_LV = 7288709;
SocketCommand.FULI_TOKEN_BUY = 4530817;
SocketCommand.FULI_FUND_FETCH = 3742854;
SocketCommand.HERO_MY_RANK = 2429584;
SocketCommand.QXZL_INFO_LOOK_MEMBER = 7091715;
SocketCommand.FAMILY_TRANSFER_JOIN = 1247638;
SocketCommand.PROGRESS_GIFT_BUY = 6894721;
SocketCommand.VIP_KEFU_QQ = 5056128;
SocketCommand.BOAT_PEAK_BATTLE = 10768900;
SocketCommand.GOD_WEAPON_UPGRADE = 6238081;
SocketCommand.WARS_INFO_NTY_RANK_DATA = 11622552;
SocketCommand.TIME_ACHIEVEMENT_SHARE = 6632066;
SocketCommand.TEST_TOWER_FIGHT_RESULT = 5909763;
SocketCommand.SDK_SNS_REWARD_INFO = 9324288;
SocketCommand.FAMILY_UPDATE_NOTICE = 1247628;
SocketCommand.HZZD_INFO_OPP = 8011016;
SocketCommand.CROSS_LADDER_LOGS = 8536324;
SocketCommand.FAMILY_RANKING_LIST = 4333824;
SocketCommand.MAZE_USE_ITEM = 7945355;
SocketCommand.MODULAR_ACTIVITY_BRICK_INFO = 8142404;
SocketCommand.QXZL_INFO_BET = 7091718;
SocketCommand.BOAT_PEAK_MEMBER = 10768898;
SocketCommand.SGAME_INFO = 9980928;
SocketCommand.FRIEND_BEST_LIST = 787977;
SocketCommand.TEAM_XSWH_INFO = 9586944;
SocketCommand.HZZD_INFO_BET = 8011017;
SocketCommand.TEAM_INFO_MY_TEAM_APPLY = 9455619;
SocketCommand.MODULAR_ACTIVITY_MAZE_PUB = 8142365;
SocketCommand.FAMILY_HONGBAO_INFO = 4465152;
SocketCommand.TRAVEL_REFRESH = 3151873;
SocketCommand.FISH_SHOP = 10834566;
SocketCommand.ROLE_RENAME_INFO = 1116292;
SocketCommand.EQUIP_REINFORCE = 2495237;
SocketCommand.CSC_FMSOLO_GROUP = 7288705;
SocketCommand.MODULAR_ACTIVITY_HUNT_REFRESH = 8142381;
SocketCommand.STAGE_BREED_COST = 9849601;
SocketCommand.LORD_TREASURE_OP = 10571915;
SocketCommand.BATTLE_TRIAL_BATTLE_RESET = 9521287;
SocketCommand.PROFILE_UPDATE = 3020546;
SocketCommand.SHORTCUT_SHOP_BUY = 3545857;
SocketCommand.ARENA_LOGS = 3611524;
SocketCommand.THEME_ACTIVITY_SKIN_LOTTERY = 7617025;
SocketCommand.STAGE_COPY_SET = 9915268;
SocketCommand.FAMILY_TASK_GIFT = 3217540;
SocketCommand.SIMP_MISSION_UPDATE = 1181953;
SocketCommand.HUNT_REFRESH = 3480195;
SocketCommand.LOTTERY_INFO = 2757889;
SocketCommand.HERO_ACT_FOURTEEN = 2429589;
SocketCommand.LOGIN_ACTIVITY_FETCH = 7814017;
SocketCommand.LCQS_CHAPTER_FETCH = 7551361;
SocketCommand.VIP_BUY_RECOMMEND = 4268164;
SocketCommand.MASTER_CARD_UP_OFFICIAL = 9652614;
SocketCommand.FISH_POWER = 10834571;
SocketCommand.DAWANKA_INFO = 8667648;
SocketCommand.THEME_ACTIVITY_ZHOUKA_INFO = 7617029;
SocketCommand.MODULAR_ACTIVITY_STAR_REPAY_INFO = 8142354;
SocketCommand.FIGHT_TIMES = 2692231;
SocketCommand.HERO_RECYCLE_ALL = 2429581;
SocketCommand.LOGIN_AUTH = 656644;
SocketCommand.GUIDE_FINISH = 4071170;
SocketCommand.MAZE_SPOILS = 7945347;
SocketCommand.STAR_PLAN_UPDATE = 7682691;
SocketCommand.GMCMD_DO = 1904256;
SocketCommand.EQUIP_UNLOAD = 2495234;
SocketCommand.WORSHIP_DO = 7026050;
SocketCommand.CYCLE_ACTIVITY_UPDATE = 7223043;
SocketCommand.ACTIVITY_EXAM_ANSWER = 1444616;
SocketCommand.DIVINE_EQUIP_ACTIVATE_HERO = 11425536;
SocketCommand.BOAT_RACE_RESULT = 10112265;
SocketCommand.FISH_PREVIEW_ATTR = 10834570;
SocketCommand.LORD_EQUIP_COMPOSE = 10571914;
SocketCommand.MOCK_PVP_SHOW_TOPS = 11031558;
SocketCommand.FIGHT_START_PASS = 2692230;
SocketCommand.MODULAR_ACTIVITY_MAZE_AUTO_ROLLING_END = 8142373;
SocketCommand.DAILY_COPY_PROCESS = 2889216;
SocketCommand.COMPLAINTS_INFO = 1969920;
SocketCommand.MAIN_BATTLE_MISSIONS = 2626576;
SocketCommand.FIGHT_SHARE_STAT = 10703233;
SocketCommand.MEDAL_LIST = 8864640;
SocketCommand.SIMP_MISSION_LIST = 1181952;
SocketCommand.FAMILY_HOT_OP = 1247640;
SocketCommand.CHAT_CHANNEL = 590736;
SocketCommand.HERO_SKIN_UPGRADE = 5121795;
SocketCommand.MODULAR_ACTIVITY_GENERAL_PASS_INFO = 8142352;
SocketCommand.ROLE_LOOK_LORD = 1116307;
SocketCommand.RANK_ACTIVITY_INFO = 4859136;
SocketCommand.VIP_INFO = 4268160;
SocketCommand.RANDOM_PVP_FIGHT_RESULT = 7879687;
SocketCommand.HUNT_RUN = 3480193;
SocketCommand.FRIEND_REQUEST = 787970;
SocketCommand.LEVEL_GIFT_INFO = 4793472;
SocketCommand.LETTER_GET = 853632;
SocketCommand.HERO_RESONATE_INFO = 8273665;
SocketCommand.MODULAR_ACTIVITY_REPORT = 8142371;
SocketCommand.TD_MAIN_INFO = 10965893;
SocketCommand.MASTER_CARD_REPLACE = 9652613;
SocketCommand.BINGFU_REFINE = 5187462;
SocketCommand.SOUL_HERO_LINK = 8798977;
SocketCommand.MASTER_CARD_INFO = 9652608;
SocketCommand.EQUIP_DECOMPOSE = 2495240;
SocketCommand.PEAK_INFO_OPP = 8404993;
SocketCommand.FAMILY_SCIENCE_INFO = 6106752;
SocketCommand.ARES_PALACE_INFO = 5844096;
SocketCommand.LARGE_PEAK_INFO_BATTLE = 11359876;
SocketCommand.HERO_RECYCLE_CHANGE = 2429583;
SocketCommand.CSCLAN_CREATE = 10637569;
SocketCommand.BATTLE_TRIAL_BATTLE_UP = 9521286;
SocketCommand.CSCLAN_SOLO_FETCH = 11556867;
SocketCommand.HERO_RECYCLE_PREVIEW = 2429573;
SocketCommand.FISH_HANDBOOK_INFO = 10834574;
SocketCommand.PLAYER_STRATEGY_INFO = 8930304;
SocketCommand.MODULAR_ACTIVITY_SHOP = 8142341;
SocketCommand.FULI_SIGN_INFO = 3742848;
SocketCommand.FIGHT_SHARE_OPERATE = 10703232;
SocketCommand.MODULAR_ACTIVITY_LOGIN_INFO = 8142383;
SocketCommand.HERO_PASS_GIFT = 7748355;
SocketCommand.GOODS_UPDATE = 1050625;
SocketCommand.HZZD_INFO_HISTORY = 8011014;
SocketCommand.FISH_REPLACE = 10834567;
SocketCommand.MASTER_CARD_DECORATION_OP = 9652620;
SocketCommand.MASTER_CARD_PREVIEW_ATTR = 9652616;
SocketCommand.EQUIP_BINGFA_EXCHANGE = 2495253;
SocketCommand.DAILY_GIFT_NEW_DISCOUNT_INFO = 4924805;
SocketCommand.MODULAR_ACTIVITY_SEVEN_GOAL_FETCH = 8142376;
SocketCommand.SYSTEM_CONFIG_CHANGE = 1378947;
SocketCommand.FAMILY_TASK_DO = 3217538;
SocketCommand.DAILY_MISSION_GIFT = 2101249;
SocketCommand.LETTER_SEND = 853634;
SocketCommand.MONTH_FUND_INFO = 3939840;
SocketCommand.HERO_ZHOUYIN_INFO = 10046592;
SocketCommand.FRIEND_AGREE = 787971;
SocketCommand.DAILY_GIFT_INFO = 4924800;
SocketCommand.BOAT_RACE_RANK = 10112263;
SocketCommand.COMMON_ERROR = 722304;
SocketCommand.MODULAR_ACTIVITY_DICE_UNLOCK = 8142386;
SocketCommand.EQUIP_INFO_UPDATE = 2495245;
SocketCommand.FAMILY_SIGN_INFO = 6041088;
SocketCommand.FAMILY_JOIN = 1247622;
SocketCommand.HERO_RECYCLE_GAINS_ITEM = 2429582;
SocketCommand.HANGING_INFO = 3348864;
SocketCommand.CYCLE_ACTIVITY_FETCH = 7223041;
SocketCommand.SYSTEM_MESSAGE = 1378946;
SocketCommand.STORY_MAZE_FETCH = 10243588;
SocketCommand.LORD_STAR = 10571911;
SocketCommand.FAMILY_BOSS_SWEEP = 6303745;
SocketCommand.TREASURE_DISPATCH = 10506241;
SocketCommand.BOAT_RACE_ROLE_ITEMS = 10112266;
SocketCommand.SHARE_SINGLE_INFO = 5515776;
SocketCommand.MOCK_PVP_OP = 11031552;
SocketCommand.FAMILY_TASK_INFO = 3217536;
SocketCommand.TEST_TOWER_FETCH = 5909761;
SocketCommand.RED_CLIFF_INFO = 6369408;
SocketCommand.ACTIVITY_LIST = 1444608;
SocketCommand.ROLE_SCORE_UPDATE = 1707265;
SocketCommand.HANGING_QUICK = 3348866;
SocketCommand.LARGE_PEAK_INFO_MEMBER = 11359874;
SocketCommand.SYS_DAILY_FETCH = 6697729;
SocketCommand.MODULAR_ACTIVITY_HOLIDAY_WELFARE_INFO = 8142388;
SocketCommand.RANDOM_PVP_PALACE = 7879684;
SocketCommand.LORD_SET_LINEUP = 10571907;
SocketCommand.LARGE_PEAK_PERSONAL_INFO = 11359880;
SocketCommand.PAGE_SWITCH = 4202497;
SocketCommand.HERO_COME_INFO = 7157376;
SocketCommand.CYCLE_ACTIVITY_INFO = 7223040;
SocketCommand.SEVEN_GOAL_GIFT = 3808515;
SocketCommand.FAMILY_BOSS_RANK = 6303748;
SocketCommand.TIME_ACTIVITY_UPDATE = 4136834;
SocketCommand.FAMILY_ACTIVE_INFO = 6960384;
SocketCommand.CSCLAN_RENAME = 10637579;
SocketCommand.FAMILY_SIGN_FETCH = 6041089;
SocketCommand.GOD_EQUIP_RECAST = 2560896;
SocketCommand.HZZD_FETCH = 8011012;
SocketCommand.OPTION_LOTTERY_INFO = 7485697;
SocketCommand.FAMILY_MEMBERS = 1247618;
SocketCommand.HONGBAO_INFO = 11753856;
SocketCommand.FIRST_PAY_FETCH = 3874177;
SocketCommand.WARS_LOOK_OP_FIGHT_LOG = 11622545;
SocketCommand.PEAK_INFO_HISTORY = 8404999;
SocketCommand.TD_LINEUP = 10965889;
SocketCommand.TD_SKILL = 10965890;
SocketCommand.PEAK_INFO_GET_MSG = 8405001;
SocketCommand.MODULAR_ACTIVITY_DICE = 8142385;
SocketCommand.FAMILY_SET_OWNER = 1247627;
SocketCommand.WARS_LOOK_OP_ARMY = 11622543;
SocketCommand.MODULAR_ACTIVITY_MAZE_USE_ITEM = 8142368;
SocketCommand.GUANDU_OP = 6500737;
SocketCommand.CASTING_SOUL_OP = 8076672;
SocketCommand.ROLE_LOOK_LINEUP = 1116302;
SocketCommand.CYCLE_ACTIVITY_GIFT = 7223042;
SocketCommand.EQUIP_LOAD = 2495233;
SocketCommand.RED_CLIFF_FIGHT_RESULT = 6369410;
SocketCommand.MODULAR_ACTIVITY_RARE_LOTTERY_INFO = 8142344;
SocketCommand.ITEM_COMPOSE = 919297;
SocketCommand.EIGHT_LOGIN_INFO = 3677184;
SocketCommand.EQUIP_BINGFA_OP = 2495252;
SocketCommand.EQUIP_FILTER_INFO = 2495246;
SocketCommand.WARS_WALK_LIST = 11622531;
SocketCommand.RANK_MISSION_INFO = 6763392;
SocketCommand.TEAM_INFO_TEAMS = 9455618;
SocketCommand.LCQS_FIGHT_RESULT = 7551364;
SocketCommand.ROLE_CROSS_GROUP = 1116308;
SocketCommand.SQUAD_LINEUP_SET = 4662145;
SocketCommand.STORY_MAZE_FIGHT_RESULT = 10243592;
SocketCommand.MODULAR_ACTIVITY_PREVIEW_INFO = 8142396;
SocketCommand.HONGBAO_FETCH_UPDATE = 11753860;
SocketCommand.RANDOM_PVP_MATCH = 7879682;
SocketCommand.MODULAR_ACTIVITY_FETCH = 8142337;
SocketCommand.FAMILY_SCIENCE_OP = 6106753;
SocketCommand.THEME_ACTIVITY_FAMOUS_LOTTERY_INFO = 7617027;
SocketCommand.QXZL_INFO = 7091712;
SocketCommand.FISH_SPE_EFFECT = 10834572;
SocketCommand.PAYMENT_AUTO_BUY = 1772934;
SocketCommand.MEDAL_RETIRE_UPDATE = 8864646;
SocketCommand.MODULAR_ACTIVITY_SKIN_INFO = 8142348;
SocketCommand.STORY_TOWER_BATTLE_FETCH = 10374913;
SocketCommand.INGENIOUS_PLAN_STAR = 8733315;
SocketCommand.WARS_LOOK_OP_SCORE_RANK = 11622544;
SocketCommand.MODULAR_ACTIVITY_STORY_INFO = 8142397;
SocketCommand.BOAT_RACE_BONUS = 10112257;
SocketCommand.BATTLE_TRIAL_PASS_INFO = 9521282;
SocketCommand.ACC_PAY_INFO = 2232576;
SocketCommand.XSWH_RANK = 8601986;
SocketCommand.CSC_FMSOLO_INFO = 7288704;
SocketCommand.HERO_CHEER_INFO = 11228544;
SocketCommand.HERO_PASS_FETCH = 7748353;
SocketCommand.SYS_USE_TIMES_UPDATE = 9127297;
SocketCommand.SOUL_HERO_UNLOCK = 8798979;
SocketCommand.HERO_LIST = 2429568;
SocketCommand.MODULAR_ACTIVITY_STORY_GROUP = 8142399;
SocketCommand.FAMILY_HONGBAO_INFO_UPDATE = 4465155;
SocketCommand.TEAM_XSWH_FIGHT_RESULT = 9586945;
SocketCommand.MODULAR_ACTIVITY_MAZE_INFO = 8142357;
SocketCommand.TEST_TOWER_SWEEP = 5909764;
SocketCommand.TIME_ACHIEVEMENT_UPDATE = 6632065;
SocketCommand.WAR_FLAG_LINK = 6172419;
SocketCommand.GUIDE_HINT = 4071169;
SocketCommand.TD_TRIAL_INFO = 10965892;
SocketCommand.GOD_EQUIP_ENCHANT = 2560900;
SocketCommand.SEVEN_GOAL_FETCH = 3808514;
SocketCommand.FAMILY_SELF = 1247619;
SocketCommand.FULI_TOKEN_INFO = 4530816;
SocketCommand.HERO_RESONATE_EQUIP_OP = 8273668;
SocketCommand.HERO_HANDBOOK_ALL_FETCH = 6566402;
SocketCommand.MODULAR_ACTIVITY_SKIN_LOTTERY = 8142349;
SocketCommand.LETTER_OPEN = 853633;
SocketCommand.WORSHIP_ONLINE = 7026048;
SocketCommand.SYSTEM_ERROR = 1378945;
SocketCommand.ROLE_FAMILY_CHANGE = 1116296;
SocketCommand.PASS_BEHEAD_INFO = 5975424;
SocketCommand.MODULAR_ACTIVITY_HERO_CHALLENGE_REPORT = 8142394;
SocketCommand.SYSTEM_HEARTBEAT = 1378944;
SocketCommand.HERO_RESONATE_LEVEL_INFO = 8273664;
SocketCommand.PAY_GIFT_INFO = 2166912;
SocketCommand.TEAM_INFO_MY_TEAM_UPDATE = 9455620;
SocketCommand.FAMILY_INFO_CHANGE = 1247630;
SocketCommand.PROFILE_CHANGE = 3020545;
SocketCommand.FAMILY_FLAGNAME = 1247636;
SocketCommand.TEAM_XSWH_FETCH = 9586948;
SocketCommand.ARENA_MAX_REWARD_INFO = 3611526;
SocketCommand.PASS_CHECK_FETCH = 7420033;
SocketCommand.LOTTERY_START = 2757888;
SocketCommand.MASTER_CARD_POWER = 9652617;
SocketCommand.FIGHT_FINISH = 2692225;
SocketCommand.DAY_ACC_PAY_GIFT_INFO = 10900224;
SocketCommand.WARS_MAKE_PATH = 11622554;
SocketCommand.FAMILY_TASK_UPDATE = 3217537;
SocketCommand.ROLE_UPDATE_EXT = 1116299;
SocketCommand.CSCLAN_APPLY_LIST = 10637577;
SocketCommand.FAMILY_SIGN_DO = 6041090;
SocketCommand.LOGIN_CHOSE = 656641;
SocketCommand.CHAT_COST = 590749;
SocketCommand.PEAK_INFO_MEMBER = 8404994;
SocketCommand.BINGFU_SHIFT = 5187459;
SocketCommand.CROSS_LADDER_LIST = 8536323;
SocketCommand.LOTTERY_SCORE_FETCH = 2757890;
SocketCommand.HUNT_INFO = 3480192;
SocketCommand.BAG_GOODS_LIST = 984960;
SocketCommand.ITEM_SHOW_GAINS = 919298;
SocketCommand.MODULAR_ACTIVITY_MAZE_SPOILS = 8142360;
SocketCommand.SYS_DAILY_INFO = 6697728;
SocketCommand.MEDAL_DETAIL = 8864645;
SocketCommand.MASTER_CARD_SPE_EFFECT = 9652618;
SocketCommand.HERO_UPGRADE = 2429571;
SocketCommand.MAIN_BATTLE_INFO = 2626560;
SocketCommand.LOTTERY_NATION_START = 2757891;
SocketCommand.DIVINE_EQUIP_INFO = 11425537;
SocketCommand.WAR_FLAG_ACTIVE = 6172417;
SocketCommand.TEST_TOWER_OP = 5909766;
SocketCommand.ARENA_FIGHT_RESULT = 3611525;
SocketCommand.RED_CLIFF_SWEEP = 6369409;
SocketCommand.WARS_INFO_GET_MSG = 11622551;
SocketCommand.MOCK_PVP_LOGS = 11031555;
SocketCommand.BOAT_RACE_OP = 10112261;
SocketCommand.MOCK_PVP_HEAT = 11031563;
SocketCommand.WORLD_BOSS_INFO = 7354368;
SocketCommand.ONLINE_REWARD_INFO = 3414528;
SocketCommand.DOMINATE_PVP_NEW_SEASON = 11294214;
SocketCommand.DAWANKA_FETCH = 8667649;
SocketCommand.RECOMMEND_LINEUP_INFO = 4596480;
SocketCommand.MONSTER_GROUP_POWER = 6829056;
SocketCommand.WAR_FLAG_INFO = 6172416;
SocketCommand.FAMILY_REPLY_JOIN = 1247624;
SocketCommand.FAMILY_HONGBAO_FETCH = 4465154;
SocketCommand.CHAT_GET_GOODS = 590726;
SocketCommand.BOAT_RACE_ALLOC_ITEMS = 10112267;
SocketCommand.LORD_SKILL_ATTACK_INC_CALC = 10571912;
SocketCommand.LOGIN_SCENE = 656647;
SocketCommand.EQUIP_COMPOSE_LOGS = 2495243;
SocketCommand.EQUIP_AUTO_LOAD = 2495235;
SocketCommand.ITEM_SALE = 919299;
SocketCommand.GUIDE_GET_HERO = 4071173;
SocketCommand.HZZD_TEAM_INFO = 8011013;
SocketCommand.MODULAR_ACTIVITY_BRICK = 8142405;
SocketCommand.XSWH_FETCH = 8601988;
SocketCommand.EQUIP_RECYCLE = 2495239;
SocketCommand.EQUIP_AUTO_UNLOAD = 2495236;
SocketCommand.WELFARE_SDK_SHARE = 1575937;
SocketCommand.LARGE_PEAK_INFO_HISTORY = 11359879;
SocketCommand.FAMILY_UPLEVEL = 1247621;
SocketCommand.FRIEND_FETCH = 787975;
SocketCommand.MODULAR_ACTIVITY_STAR_PLAN_UPDATE = 8142351;
SocketCommand.MASTER_TALENT_SCIENCE_UPDATE = 10177922;
SocketCommand.MODULAR_ACTIVITY_SIX_BLESS_INFO = 8142414;
SocketCommand.MAIN_BATTLE_MISSION_FETCH = 2626565;
SocketCommand.LORD_RECYCLE_PREVIEW = 10571910;
SocketCommand.TAX_SILVER_FETCH = 2954881;
SocketCommand.MASTER_CARD_SHOP = 9652612;
SocketCommand.COMMON_SHOW_ATTR = 722306;
SocketCommand.PASS_BEHEAD_BATTLE = 5975429;
SocketCommand.BOAT_PEAK_HIS = 10768903;
SocketCommand.MODULAR_ACTIVITY_HEAVEN_GIVE_INFO = 8142400;
SocketCommand.TAX_FORAGE_INFO = 2954882;
SocketCommand.DIVINE_COPY_INFO = 11491201;
SocketCommand.ROLE_ACCLOGIN = 1116293;
SocketCommand.CSC_FMSOLO_BOX = 7288708;
SocketCommand.BATTLE_TRIAL_FETCH_INFO = 9521281;
SocketCommand.WORSHIP_INFO = 7026049;
SocketCommand.BATTLE_TRIAL_INFO = 9521280;
SocketCommand.STORY_MAZE_PUB = 10243591;
SocketCommand.PAGE_LIST = 4202496;
SocketCommand.TITLE_LIST = 3283200;
SocketCommand.MASTER_CARD_NOTICE = 9652615;
SocketCommand.MODULAR_ACTIVITY_GENERAL_PASS_FETCH = 8142353;
SocketCommand.FIGHT_SIMP_RESULT = 2692227;
SocketCommand.QXZL_INFO_LAST_RANK = 7091720;
SocketCommand.HERO_SKIN_RESET = 5121796;
SocketCommand.FRIEND_FIND = 787973;
SocketCommand.BUY_TIMES_UPDATE = 6435074;
SocketCommand.LETTER_OPERATE = 853635;
SocketCommand.EQUIP_INFO = 2495232;
SocketCommand.WARS_EVENT = 11622553;
SocketCommand.HERO_PASS_INFO = 7748352;
SocketCommand.LARGE_PEAK_INFO = 11359872;
SocketCommand.THEME_ACTIVITY_SKIN_INFO = 7617024;
SocketCommand.QUICK_SHOP_TIPS = 5384450;
SocketCommand.STORY_MAZE_HERO = 10243590;
SocketCommand.MAZE_INFO = 7945344;
SocketCommand.HERO_HANDBOOK_LIST = 6566400;
SocketCommand.TITLE_LOAD = 3283201;
SocketCommand.FISH_UPDATE = 10834564;
SocketCommand.STAGE_COPY_SWEEP = 9915265;
SocketCommand.SHORTCUT_SHOP_INFO = 3545856;
SocketCommand.TREASURE_REFRESH = 10506248;
SocketCommand.PAY_GIFT_FETCH = 2166913;
SocketCommand.HUNT_LOGS = 3480196;
SocketCommand.MODULAR_ACTIVITY_WAR_LOG_UPDATE = 8142356;
SocketCommand.LOTTERY_SUPREME_START = 2757894;
SocketCommand.MODULAR_ACTIVITY_CARNIVAL = 8142382;
SocketCommand.GOD_EQUIP_COMPOSE = 2560897;
SocketCommand.MODULAR_ACTIVITY_LOTTERY_TIPS = 8142347;
SocketCommand.WAR_FLAG_OP = 6172418;
SocketCommand.LARGE_PEAK_INFO_LOOK_MEMBER = 11359875;
SocketCommand.WARS_LOOK_OP_SIGN = 11622542;
SocketCommand.TEAM_XSWH_BEST_RANK = 9586947;
SocketCommand.FAMILY_SET_TITLE = 1247626;
SocketCommand.GUIDE_MISSION = 4071168;
SocketCommand.HINT_LIST = 2035584;
SocketCommand.LORD_OP = 10571913;
SocketCommand.EQUIP_STAR_OP = 2495248;
SocketCommand.CROSS_LADDER_OPP_INFO = 8536322;
SocketCommand.FAMILY_BOSS_GATHER = 6303751;
SocketCommand.WARS_UPDATE_ROLE = 11622556;
SocketCommand.MODULAR_ACTIVITY_HUNT_LOGS = 8142380;
SocketCommand.TEAM_MERGE_REWARD = 9455625;
SocketCommand.MODULAR_ACTIVITY_CUSTOMIZED_GIFT_SELECT = 8142401;
SocketCommand.MASTER_CARD_LOTTERY = 9652609;
SocketCommand.TREASURE_EXIST = 10506250;
SocketCommand.BOAT_PEAK_OPP = 10768897;
SocketCommand.DOMINATE_PVP_OP = 11294208;
SocketCommand.WARS_OPERATE = 11622534;
SocketCommand.BOAT_PEAK_LOOK_PRE_SCORE = 10768906;
SocketCommand.LORD_SKILL_SET = 10571906;
SocketCommand.STORY_MAZE_INFO = 10243584;
SocketCommand.TEAM_LINEUP_SET = 9455624;
SocketCommand.GUIDE_EVENT = 4071172;
SocketCommand.WARS_TEAM_INFO = 11622537;
SocketCommand.FAMILY_CREATE = 1247617;
SocketCommand.GMCMD_BATTLE_EDIT = 1904258;
SocketCommand.BUILDING_INFO = 11819520;
SocketCommand.VIP_KEFU_OP = 5056129;
SocketCommand.CSCLAN_SOLO_INFO = 11556865;
SocketCommand.FISH_INFO = 10834560;
SocketCommand.ACC_GIFT_INFO = 10440577;
SocketCommand.RANDOM_PVP_UPDATE = 7879686;
SocketCommand.RANKING_HISTORY_LIST = 1510276;
SocketCommand.MODULAR_ACTIVITY_LIST = 8142336;
SocketCommand.TEAM_SHARE_LIST = 9455626;
SocketCommand.SHOP_INFO = 2363904;
SocketCommand.THEME_ACTIVITY_FAMOUS_LOTTERY_UPDATE = 7617028;
SocketCommand.MODULAR_ACTIVITY_DICE_INFO = 8142384;
SocketCommand.STORY_TOWER_BATTLE_INFO = 10374912;
SocketCommand.BOAT_RACE_BOATS = 10112259;
SocketCommand.FAMILY_AUDIT_LIMIT = 1247625;
SocketCommand.MODULAR_ACTIVITY_UPDATE_FETCH = 8142340;
SocketCommand.EQUIP_AUTO_REINFORCE = 2495238;
SocketCommand.HERO_UPDATE_LIST = 2429569;
SocketCommand.ITINERANT_SHOP_INFO = 11688192;
SocketCommand.FULI_FUND_INFO = 3742853;
SocketCommand.BOAT_PEAK_FETCH = 10768908;
SocketCommand.MODULAR_ACTIVITY_LOTTERY_START = 8142342;
SocketCommand.FAMILY_BOSS_UPDATE = 6303746;
SocketCommand.FAMILY_LOGS = 1247631;
SocketCommand.DEPUTY_OP = 2495257;
SocketCommand.STORY_MAZE_START = 10243586;
SocketCommand.FIGHT_SHARE_CHAT = 10703234;
SocketCommand.BOAT_RACE_LOGS = 10112264;
SocketCommand.SYS_CONCERN_INFO = 5450112;
SocketCommand.FAMILY_LIST = 1247616;
SocketCommand.TREASURE_WORKER_INFO = 10506254;
SocketCommand.ACTIVITY_YUEKA_INFO = 1444619;
SocketCommand.MODULAR_ACTIVITY_CUSTOMIZED_GIFT_SELECT_LIST = 8142403;
SocketCommand.MODULAR_ACTIVITY_FAMOUS_LOTTERY_INFO = 8142343;
SocketCommand.MASTER_TALENT_SCIENCE_INFO = 10177921;
SocketCommand.FISH_LOTTERY = 10834562;
SocketCommand.MASTER_CARD_UPDATE = 9652611;
SocketCommand.FULI_SIGN_FETCH = 3742849;
SocketCommand.TEAM_LINEUP_LIST = 9455623;
SocketCommand.TEAM_OPERATE = 9455621;
SocketCommand.WING_OP = 2495255;
SocketCommand.MODULAR_ACTIVITY_SEVEN_GOAL_INFO = 8142374;
SocketCommand.LEVEL_GIFT_FETCH = 4793473;
SocketCommand.FAMILY_INFO = 1247634;
SocketCommand.CHAT_CLEAN = 590748;
SocketCommand.SYS_USE_TIMES_INFO = 9127296;
SocketCommand.TREASURE_GIFT_ACTIVE = 10506251;
SocketCommand.DAILY_AD_CODE_INFO = 5712768;
SocketCommand.DAILY_COPY_ENTER = 2889217;
SocketCommand.ROLE_SETTING = 1116301;
SocketCommand.SYSTEM_CONFIG = 1378948;
SocketCommand.HERO_INHERIT_UPDATE = 2429586;
SocketCommand.BINGFU_BREED = 5187457;
SocketCommand.WARS_ROLE_PRESONAL_INFO = 11622539;
SocketCommand.FAMILY_HONGBAO_MISSION_SEND = 4465158;
SocketCommand.INGENIOUS_PLAN_INFO = 8733312;
SocketCommand.MODULAR_ACTIVITY_DAY_SHOP = 8142413;
SocketCommand.BOAT_RACE_EVENT = 10112260;
SocketCommand.MONTH_FUND_UPDATE = 3939843;
SocketCommand.BUY_TIMES_INFO = 6435072;
SocketCommand.TEST_TOWER_PROCESS = 5909760;
SocketCommand.HERO_BAG_EXPANSION = 2429580;
SocketCommand.QUICK_SHOP_INFO = 5384448;
SocketCommand.ARENA_LIST = 3611523;
SocketCommand.PEAK_INFO_BET = 8404998;
SocketCommand.HERO_CHEER_OP = 11228545;
SocketCommand.PEAK_INFO_LOOK_MEMBER = 8404995;
SocketCommand.DIVINE_EQUIP_OP = 11425539;
SocketCommand.GENERAL_INFO = 8208000;
SocketCommand.CSCLAN_SOLO_GROUP = 11556866;
SocketCommand.SHARE_FETCH = 5581441;
SocketCommand.MAZE_FIGHT_RESULT = 7945354;
SocketCommand.MODULAR_ACTIVITY_SEVEN_GOAL_GIFT = 8142377;
SocketCommand.TRAVEL_ACCEPT = 3151874;
SocketCommand.EQUIP_REPLACE = 2495244;
SocketCommand.TREASURE_USE_ITEM = 10506247;
SocketCommand.MULTI_LINEUP_LIST = 8339329;
SocketCommand.MONSTER_GROUP_POWER_LIST = 6829057;
SocketCommand.QXZL_INFO_HISTORY = 7091719;
SocketCommand.MOCK_PVP_CONFIG = 11031557;
SocketCommand.WARS_LOOK_OP_CAMP_RANK = 11622547;
SocketCommand.MODULAR_ACTIVITY_PREVIEW = 8142395;
SocketCommand.CHAT_MISC = 590747;
SocketCommand.GOD_TRIAL_FIGHT_RESULT = 5778436;
SocketCommand.MAZE_START = 7945346;
SocketCommand.ROLE_SCORE_LIST = 1707264;
SocketCommand.FULI_TOKEN_FETCH = 4530818;
SocketCommand.FIGHT_SET_SPEED = 2692226;
SocketCommand.MAIN_BATTLE_BOX_INFO = 2626567;
SocketCommand.THEME_ACTIVITY_UP_STAR_REWARD_UPDATE = 7617035;
SocketCommand.TEAM_LINEUP_GET = 9455622;
SocketCommand.HANGING_REWARD = 3348865;
SocketCommand.CROSS_LADDER_INFO = 8536321;
SocketCommand.FIRST_PAY_INFO = 3874176;
SocketCommand.QXZL_INFO_GET_MSG = 7091723;
SocketCommand.MODULAR_ACTIVITY_FESTIVAL_WISH_INFO = 8142416;
SocketCommand.BROADCAST_SHOW = 1313281;
SocketCommand.HERO_DOWN_LINEUP = 2429576;
SocketCommand.FAMILY_BOSS_ATTR = 6303750;
SocketCommand.HERO_SKIN_INFO = 5121792;
SocketCommand.DAILY_MISSION_INFO = 2101248;
SocketCommand.MULTI_LINEUP_SET = 8339328;
SocketCommand.GOD_TRIAL_FETCH = 5778433;
SocketCommand.RANDOM_PVP_LOGS = 7879685;
SocketCommand.LOGIN_ACTIVITY_INFO = 7814016;
SocketCommand.SQUAD_LINEUP_GET = 4662144;
SocketCommand.QXZL_INFO_LAST_BATTLE = 7091721;
SocketCommand.COMMON_TIPS = 722305;
SocketCommand.MODULAR_ACTIVITY_WISH_LOTTERY_INFO = 8142345;
SocketCommand.DAILY_PAY_FETCH = 4005505;
SocketCommand.QQVIP_INFO = 4990464;
SocketCommand.MOCK_PVP_FIGHT_RESULT = 11031560;
SocketCommand.ROLE_ATTR_CHANGE = 1116289;
SocketCommand.FAMILY_OPERATE = 1247629;
SocketCommand.MAIN_BATTLE_MISSION_STATUS = 2626566;
SocketCommand.RANDOM_PVP_NEW_SEASON = 7879688;
SocketCommand.ZERO_BUY_OP = 11885184;
SocketCommand.XSWH_BEST_RANK = 8601987;
SocketCommand.ARES_PALACE_LOG = 5844097;
SocketCommand.MAIN_BATTLE_BOX_UPGRADE = 2626573;
SocketCommand.BOAT_PEAK_SHOP = 10768904;
SocketCommand.WARS_INIT_PERSONAL = 11622530;
SocketCommand.TEST_TOWER_SKIP = 5909765;
SocketCommand.TIME_ACTIVITY_INFO = 4136832;
SocketCommand.FRIEND_LIST = 787968;
SocketCommand.WORLD_LEVEL_INFO = 5253120;
SocketCommand.TEAM_XSWH_RANK = 9586946;
SocketCommand.STORY_MAZE_SPOILS = 10243587;
SocketCommand.MAIN_BATTLE_FIGHT_RESULT = 2626561;
SocketCommand.GOODS_LIST = 1050624;
SocketCommand.MAZE_PUB = 7945352;
SocketCommand.MODULAR_ACTIVITY_WAR_LOG_INFO = 8142355;
SocketCommand.ACTIVITY_EXAM_LIST = 1444615;
SocketCommand.FISH_LINEUP_LIST = 10834565;
SocketCommand.SGAME_WX_CLUB = 9980932;
SocketCommand.PLAYING_PREVIEW_INFO = 9783936;
SocketCommand.CSCLAN_SOLO_STATE = 11556868;
SocketCommand.HERO_CONVERT_INFO = 2823552;
SocketCommand.HERO_ZHOUYIN_UPGRADE = 10046593;
SocketCommand.ITEM_USE = 919296;
SocketCommand.TREASURE_PULL_LIST = 10506249;
SocketCommand.ROLE_LEVEL_UP = 1116288;
SocketCommand.HERO_RESONATE_OPERATE = 8273666;
SocketCommand.MODULAR_ACTIVITY_HERO_CHALLENGE_INFO = 8142392;
SocketCommand.TREASURE_ROLE_INFO = 10506245;
SocketCommand.STORY_SIEGELORD_INFO = 10309248;
SocketCommand.SOUL_HERO_RESET = 8798978;
SocketCommand.MISSION_SHOP_INFO = 4727808;
SocketCommand.ACTIVITY_SHOP_INFO = 1444611;
SocketCommand.EIGHT_LOGIN_FETCH = 3677185;
SocketCommand.CROSS_LADDER_FIGHT_RESULT = 8536325;
SocketCommand.VIP_FREE_GIFT_INFO = 4268162;
SocketCommand.DOMINATE_PVP_UPDATE = 11294212;
SocketCommand.WELFARE_ACTIVATE_CODE = 1575936;
SocketCommand.RANKING_SIMPLE = 1510275;
SocketCommand.WARS_HONOR_WALL = 11622557;
SocketCommand.DIVINE_EQUIP_COMPOSE = 11425538;
SocketCommand.MEDAL_UPDATE = 8864643;
SocketCommand.PLAYING_PREVIEW_AWARD = 9783937;
SocketCommand.FISH_UP_OFFICIAL = 10834568;
SocketCommand.LARGE_PEAK_INFO_OPP = 11359873;
SocketCommand.WARS_INIT = 11622529;
SocketCommand.TREASURE_RESULT = 10506244;
SocketCommand.DOMINATE_PVP_LOGS = 11294211;
SocketCommand.DOMINATE_PVP_INFO = 11294209;
SocketCommand.TEST_TOWER_CONTINUOUS_FIGHT_RESULT = 5909767;
SocketCommand.XSWH_INFO = 8601984;
SocketCommand.MODULAR_ACTIVITY_SIGN_INFO = 8142390;
SocketCommand.FULI_YUEKA_FETCH = 3742852;
SocketCommand.FRIEND_OPERATE = 787972;
SocketCommand.LOGIN_ADD = 656640;
SocketCommand.TITLE_UPDATE = 3283202;
SocketCommand.HERO_COME_UPDATE = 7157378;
SocketCommand.CSC_FMSOLO_FETCH = 7288706;
SocketCommand.MISSION_SHOP_UPDATE = 4727809;
SocketCommand.SHARE_INFO = 5581440;
SocketCommand.ACHIEVEMENT_INFO = 3086208;
SocketCommand.MULTI_LINEUP_GET_POWER = 8339330;
SocketCommand.ROLE_RENAME = 1116291;
SocketCommand.WING_INFO = 2495256;
SocketCommand.EQUIP_BINGFU_RECAST = 2495249;
SocketCommand.protoMap = null;
