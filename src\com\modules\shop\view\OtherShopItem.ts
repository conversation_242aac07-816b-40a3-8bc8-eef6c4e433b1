import { Ease } from "laya/utils/Ease";
import { Tween } from "laya/utils/Tween";
import { cfg_item } from "../../../cfg/vo/cfg_item";
import { cfg_shop_item_tips } from "../../../cfg/vo/cfg_shop_item_tips";
import { ConfigManager } from "../../../managers/ConfigManager";
import { p_shop_item } from "../../../proto/common/p_shop_item";
import { m_shop_update_toc } from "../../../proto/line/m_shop_update_toc";
import { com } from "../../../ui/layaMaxUI";
import { ColorUtil } from "../../../util/ColorUtil";
import { HtmlUtil } from "../../../util/HtmlUtil";
import { LocalStorageUtil } from "../../../util/LocalStorageUtil";
import { StringUtil } from "../../../util/StringUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { IUIListItem } from "../../baseModules/IUListItem";
import { UIHTMLDiv } from "../../baseModules/UIHTMLDiv";
import { UIListItemData } from "../../baseModules/UIListItemData";
import { CommonQiPaoTipsView } from "../../common/CommonQiPaoTipsView";
import { DataCenter } from "../../DataCenter";
import { EquipDataCenter } from "../../equip/data/EquipDataCenter";
import GoodsItem from "../../goods/GoodsItem";
import { GoodsVO } from "../../goods/GoodsVO";
import { ItemConst } from "../../goods/ItemConst";
import { GoodsUtil } from "../../goods/vo/GoodsUtil";
import { ModuleCommand } from "../../ModuleCommand";
import { ETequanKeyType } from "../../payment/PaymentConsts";
import PaymentTeQuanUtil from "../../payment/PaymentTeQuanUtil";
import { GoodsChangeManager } from "../../test_bag/GoodsChangeManager";
import { ShopDataCenter } from "../data/ShopDataCenter";
import { ShopGoodsResetType, ShopItemParamKey, ShopItemTipsType } from "../ShopConst";
import { ShopItemDataVO } from "../vo/ShopItemDataVO";
import { ShopGroupId, ShopVO } from "../vo/ShopVO";
import { ShopUtil } from "../util/ShopUtil";
import { LangConst } from "../../../auto/LangConst";
import { YueKaDataCenter} from "../../welfare/data/YueKaDataCenter";
import { JingjieDataCenter } from "../../jingjie/data/JingjieDataCenter";
import { SentryUtils } from "../../../../game/SentryUtils";
import {EShopId, EYueKaType} from "../../../auto/ConstAuto";
import { ItemMacro } from "../../../auto/ItemMacro";


export default class OtherShopItem extends com.ui.res.shop.OtherShopItemUI implements IUIListItem {
    protected shopItem: ShopItemDataVO;
    private goodItem: GoodsItem;
    private goodVo: GoodsVO;
    private tipView: CommonQiPaoTipsView;

    private isAdSucc = false;

    constructor() {
        super();
    }
    getClassName(): string {
        return "OtherShopItem";
    }
    onOpen(param: any): void {

    }

    onClose(): void {
        super.onClose();
        if (!this.shopItem) return;
        let goodKey: string = this.shopItem.shop_id + "_" + this.shopItem.goods_id;
        let cfg: cfg_shop_item_tips = ConfigManager.cfg_shop_item_tipsCache.m_get(this.shopItem.shop_id, this.shopItem.goods_id);
        if (cfg) {
            //判断条件是否满足
            if (DataCenter.myLevel < cfg.level_limit || DataCenter.serverOpenedDay < cfg.open_days_limit) {
                return;
            }
            //弹过气泡提示了，下次就不弹了
            LocalStorageUtil.SetLocalStorage(DataCenter.myRoleID + "_" + goodKey, true);
        }
    }

    initUI(): void {
        if (this.goodsItemUI instanceof GoodsItem) {
            this.goodItem = this.goodsItemUI;
        }
        UIHTMLDiv.SetUIHTMLDiv(this.txtLimitDesc, 20, "#905c3a", 8);
        UIHTMLDiv.SetUIHTMLDiv(this.txtOpenLimit, 22, ColorUtil.FONT_RED, 8);
    }

    addClick(): void {
        this.addOnClick(this, this.btn, this.onBtnClick);
    }

    addEvent(): void {
        this.addEventListener(ModuleCommand.SHOP_BUY_SUCCEED, this, this.onBuyUpdate);
        this.addEventListener(ModuleCommand.UPDATE_WX_REWARD_AD_STATE, this, this.onVideoAdFinish);
        this.addEventListener(ModuleCommand.UPDATE_WELFARE_CARD_INFO, this, this.SetData);
    }

    private onVideoAdFinish(params) {
        //{ type: 1, advert_info: self.advert_info }

        if (!params) {
            let msg = "----OtherShopItem.onVideoAdFinish params is null";
            SentryUtils.sendWarningMsg(msg);
            return;
        }

        let type = params.type;
        let shopItemVo = params.shopItemVo as ShopItemDataVO;
        if (shopItemVo.info.goods_id == this.shopItem.info.goods_id) {

            this.isAdSucc = type == 2;

            //TODO 判断shopItemVo跟this.shopItem是否一致
            if (this.isAdSucc) {
                //直接购买
                //this.onBtnClick();
                ShopDataCenter.instance.m_shop_buy_tos(this.shopItem, 1);
            }
            this.isAdSucc = false;
        }
    }

    private onBuyUpdate(vo: m_shop_update_toc): void {
        if (this.shopItem && this.shopItem.shop_id == vo.shop_id && vo.item.goods_id == this.shopItem.info.goods_id) {
            if (this.shopItem.isSellOut == true) {
                Tween.from(this.imgSellOut, { scaleX: 10, scaleY: 10 }, 450, Ease.backOut);
                // if (this.shopItem.shop_id == ShopVO.ARENA_REWARD_SHOP) {
                //     this.shopItem.isBtnGray = true;
                // }
            } else {
                this.imgSellOut.scale(1, 1);
            }

            this.SetData();
        }

    }
    private showIndex: number = -1;
    UpdateItem(listItemData: UIListItemData, select: boolean): void {
        this.showIndex = listItemData.index;
        this.shopItem = listItemData.data;

        let width = this.getParameterValue(listItemData.parameter, "width");
        if (width) {
            this.width = width;
        }

        this.SetData();

        //判断是否是获取途径跳转过来的物品
        let get_type_id: number = this.getParameterValue(listItemData.parameter, "get_type_id");
        if (this.shopItem && this.shopItem.info && this.shopItem.info.item_id == get_type_id) {
            // if (!this["get_item_eff"]) {
            //     this["get_item_eff"] = this.ShowEffectCenter("shop_bglight", this, true);
            // }
            // this["get_item_eff"].visible = true;

            //显示引导特效
            GoodsUtil.addGuideEff(this.goodItem);
        } else {
            if (this["get_item_eff"]) {
                this["get_item_eff"].visible = false;
            }
        }
    }

    private SetData(): void {
        if (this.shopItem) {
            let info: p_shop_item = this.shopItem.info;
            let goodVO: GoodsVO = GoodsVO.GetVoByTypeId(info.item_id, info.item_num);
            this.goodVo = goodVO;
            if (this.goodItem) {
                this.goodItem.SetGoodsVoData(goodVO);
            }



            if (this.checkIsVideoAdItem()) {
                if (!this.isAdSucc) {
                    this.imgBtnIcon.skin = "";
                    this.imgVideoIcon.visible = true;
                } else {
                    this.imgBtnIcon.skin = this.shopItem.costIcon;
                    this.imgVideoIcon.visible = false;
                }
            } else {
                this.imgBtnIcon.skin = this.shopItem.costIcon;
                this.imgVideoIcon.visible = false;
            }

            this.txtName.text = goodVO.name;
            // this.txtName.color = ColorUtil.GetQualityToColor(goodVO.color);


            // if (this.shopItem.shop_id == ShopVO.MYSTER_SHOP) {
            //     //神秘商店折扣低于等于4折 就显示超值图标
            //     this.imgCornerMark2.skin = this.shopItem.getTipUrl(2);
            //     this.imgCornerMark2.visible = this.shopItem.discount <= 4;
            // } else {
            //     this.imgCornerMark2.visible = this.shopItem.tipsType == 2;
            // }

            this.btn.disabled = this.shopItem.isSellOut;
            this.grayMark.visible = this.imgSellOut.visible = this.shopItem.isSellOut;
            let showPrice = this.shopItem.showPrice;
            if (showPrice <= 0) {
                this.txtPrice.text = window.iLang.L2_MIAN_FEI.il();
                this.imgZheKou.visible = false;
                this.SetRedPoint(this, !this.shopItem.isSellOut, 270, 45);
            } else {
                this.txtPrice.text = StringUtil.numberToString(showPrice, 2).toString();
                this.txtZheKou.text = LangConst.L_P0_DISCOUNT.il([this.shopItem.discount]);
                this.imgZheKou.visible = this.shopItem.isDiscount && this.shopItem.shop_id != EShopId.HUFU_SHOP;
            }
            this.updateLimitDesc();
            //碎片数量提示
            this.setChipDesc(goodVO.item);
            if (goodVO.kind == ItemMacro.ITEM_KIND_HERO_CHIP || goodVO.kind == ItemMacro.ITEM_KIND_EQUIP_CHIP) {
                GoodsChangeManager.ins.addItemChangeListener(goodVO.typeId, this, this.onChipItemChange);
            } else {
                GoodsChangeManager.ins.removeItemChangeAllByCaller(this);
            }
            // if (this.shopItem.shop_id == ShopVO.ARENA_REWARD_SHOP) {
            //     this.btn.gray = this.shopItem.isBtnGray;
            // }

            //判断气泡
            this.checkOpenQipaoTip();
            this.setBingFaShow();
            this.UpdateRedPoint();
            //热门标签
            this.setHotMarkShow(this.shopItem);
            //特权专属道具
            this.setTequanShopItemShow(this.shopItem);
            this.setGodStageShow();
        }
    }
    /**某些商店需要显示红点 */
    private UpdateRedPoint(): void {

        //判断是否满足购买条件
        switch (this.shopItem.shop_id) {
            case EShopId.TEST_TOWER_SHOP://试炼塔商店
                if (ShopDataCenter.TEST_TOWER_SHOW_FIRST_RED && this.shopItem.showPrice == 0 && !this.shopItem.isSellOut) {
                    this.SetRedPoint(this, true);
                }
                else {
                    this.SetRedPoint(this, false);
                }
                break;

        }
    }

    /**
     * 视频广告商品
     * 
     *  1、道具商店增加看广告类型商品，看完广告后可领取
        2、每天有限购次数，每天0点重置，领取完有冷却时间
        3、王者月卡增加新特权：跳过广告；购买王者月卡的玩家可跳过广告，直接领取奖励，且无冷却时间
        4、需支持配置平台名，配了平台名的道具商店才显示该类商品
     */
    private checkIsVideoAdItem() {

        let ret = false;
        if (!ShopDataCenter.instance.isVideoAdItem(this.shopItem)) {
            return ret;
        }

        /** 王者月卡特权*/
        // let isYueka = YueKaDataCenter.instance.getFuliYuekaState(EYueKaType.TYPE_5) != 0;
        let isYueka = YueKaDataCenter.instance.getPremanentCardState(EYueKaType.TYPE_101_FOREVER) != 0;
        if (isYueka) {
            ret = false;
        } else {
            ret = true;
        }
        return ret;
    }


    private setTequanShopItemShow(shopItem: ShopItemDataVO): void {
        if (!this.shopItem || shopItem.getParamByKey(ShopItemParamKey.TEQUAN_GOODS) != 1) return;
        //判断有没有购买特权
        if (!PaymentTeQuanUtil.isPrivilegeOpen(ETequanKeyType.goods_shop)) {
            this.imgTipIcon.visible = true;
            this.imgTipIcon.skin = "shop/img_tequan.png";
            this.grayMark.visible = true;
        }
    }
    /**判断成神阶位是否满足 */
    private setGodStageShow(): void {
        if (this.shopItem.god_stage_limit > JingjieDataCenter.instance.JingjieStage) {
            this.txtLimitDesc.innerHTML = "";
            this.txtOpenLimit.visible = true;
            this.txtOpenLimit.innerHTML = StringUtil.Format(window.iLang.L2_CHENG_SHEN_JIE_WEI_P0_JIE.il(), [this.shopItem.god_stage_limit]);
            this.grayMark.visible = true;
        }
    }

    /**是否显示热门标签 */
    private setHotMarkShow(shopItem: ShopItemDataVO): void {
        if (!shopItem) return;
        this.imgHot.visible = shopItem.tipsType == ShopItemTipsType.HOT;
    }

    private setBingFaShow(): void {
        if (!this.goodVo || this.goodVo.kind != ItemMacro.ITEM_KIND_BING_FA) {
            this.bingFaBox.visible = false;
            return;
        }
        this.bingFaBox.visible = true;
        this.imgLvUp.visible = this.checkBingFaLvUp();
        this.imgLingWu.visible = this.checkBingFaLingWu() && !this.imgLvUp.visible;
        this.imgBingFaExc.visible = ConfigManager.is_bingfa_ex(this.goodVo.typeId);
        this.imgBingFaXiyou.visible = ConfigManager.is_bingfa_rare(this.goodVo.typeId);
    }

    /**判断天赋是否领悟 */
    private checkBingFaLingWu(): boolean {
        if (!this.goodVo || this.goodVo.kind != ItemMacro.ITEM_KIND_BING_FA) {
            return false;
        }
        let res: boolean = EquipDataCenter.instance.checkBingFaLingWu(this.goodVo.typeId);
        return res;
    }

    /**判断天赋是否升级 */
    private checkBingFaLvUp(): boolean {
        if (!this.goodVo || this.goodVo.kind != ItemMacro.ITEM_KIND_BING_FA) {
            return false;
        }
        let res: boolean = EquipDataCenter.instance.checkBingFaLvUp(this.goodVo.typeId);
        return res;
    }

    private checkOpenQipaoTip(): void {
        this.zOrder = 1;
        if (!this.shopItem) return;
        let cfg: cfg_shop_item_tips = ConfigManager.cfg_shop_item_tipsCache.m_get(this.shopItem.shop_id, this.shopItem.goods_id);
        if (!cfg) return;
        //判断条件是否满足
        if (DataCenter.myLevel < cfg.level_limit || DataCenter.serverOpenedDay < cfg.open_days_limit) {
            return;
        }
        let localStorageKey: string = DataCenter.myRoleID + "_" + this.shopItem.shop_id + "_" + this.shopItem.goods_id;
        //判断是否弹过
        if (LocalStorageUtil.CheckLocalStorage(localStorageKey)) {
            return;
        }
        if (!this.tipView) {
            this.tipView = new CommonQiPaoTipsView();
            this.tipView.pos(this.width / 2, this.btn.y - this.btn.height / 2);
            this.tipView.zOrder = 1000;
            this.addChild(this.tipView);
        }
        this.tipView.visible = true;
        this.tipView.setDesc(cfg.desc, "left");
        this.tipView.startTween();
        this.zOrder = 10;
        // LocalStorageUtil.SetLocalStorage(localStorageKey,true);
    }

    private onChipItemChange(): void {
        if (this.shopItem && this.shopItem.info) {
            let cfgItem: cfg_item = ConfigManager.cfg_itemCache.get(this.shopItem.info.item_id);
            this.setChipDesc(cfgItem);
        }
    }

    private setChipDesc(cfg: cfg_item): void {
        // if (!cfg) {
        //     this.txtChipDesc.visible = false;
        //     return;
        // }
        // let kind: number = cfg.kind;
        // if (kind != ItemMacro.ITEM_KIND_HERO_CHIP && kind != ItemMacro.ITEM_KIND_EQUIP_CHIP
        //     && kind != ItemConst.ITEM_KING_SWEAPON) {
        //     this.txtChipDesc.visible = false;
        //     return;
        // }
        // this.txtChipDesc.visible = true;
        // let count: number = GoodsManager.instance.GetGoodsNumByTypeId(cfg.type_id);
        // let maxCout: number = 0;
        // if (kind == ItemMacro.ITEM_KIND_HERO_CHIP) {
        //     // w7 屏蔽
        //     // let cfgHero: cfg_hero_base = ConfigManager.cfg_hero_base_chipIdCache.get(cfg.type_id);
        //     // let haveHero: p_hero;
        //     // if (cfgHero) {
        //     //     haveHero = HeroDataCenter.instance.getHeroByTypeId(cfgHero.type_id);
        //     //     if (haveHero != null) {

        //     //         if (haveHero.star < cfgHero.max_star) {
        //     //             // w7 屏蔽
        //     //             // let star: cfg_hero_star = ConfigManager.getCfgHeroStar2(haveHero.type_id, haveHero.star);
        //     //             // let xmlVo: XmlFormatVo = XmlFormatVo.GetVo(star.cost_chip_1);
        //     //             // maxCout = Number(xmlVo.vlaue1);
        //     //         }
        //     //     } else {
        //     //         maxCout = cfgHero.compose_chip_num;
        //     //     }
        //     // }
        // } else if (kind == ItemMacro.ITEM_KIND_EQUIP_CHIP) {
        //     let cfgItem: cfg_item_compose = ConfigManager.cfg_item_composeCache.get(cfg.type_id);
        //     if (cfgItem) {
        //         maxCout = cfgItem.need_num;
        //     }
        // } else if (kind == ItemConst.ITEM_KING_SWEAPON) {
        //     if (ItemConst.isSweaponChipId(cfg.type_id)) {
        //         //神器碎片
        //         let sweaponId: number = ConfigManager.cfg_sweaponChipComposeCache.get(cfg.type_id);
        //         if (!MasterDataCenter.instance.isWeaponMaxStar(sweaponId)) {
        //             let sweaponVo: SweaponVo = MasterDataCenter.instance.getIdToSweapon(sweaponId);
        //             let star: number = 1;
        //             if (sweaponVo) {
        //                 //下一星级需要多少碎片
        //                 star = sweaponVo.star + 1;
        //             }
        //             let cfg = ConfigManager.getSweaponStar(sweaponId, star);
        //             cfg && (maxCout = cfg.chip_num);
        //         }
        //     } else {
        //         this.txtLimitDesc.innerHTML = "敬请期待";
        //         this.txtChipDesc.text = "";
        //         this.btn.visible = false;
        //         return;
        //     }
        // }

        // let desc: string = maxCout > 0 ? "（{0}/{1}）" : "（已满星）";
        // this.txtChipDesc.text = StringUtil.Format2(desc, count, maxCout);
    }

    /**
     * 限购的描述
     */
    private updateLimitDesc() {
        this.btn.disabled = false;
        //判断开服天数
        let d: number = this.shopItem.getParamByKey(ShopItemParamKey.KEY_OPEN_DAY) - DataCenter.serverOpenedDay;
        if (d > 0) {
            this.txtLimitDesc.innerHTML = StringUtil.Format(window.iLang.L2_P0_TIAN_HOU_JIE_SUO_GOU_MAI.il(), d);
            this.btn.disabled = true;
            return;
        }
        //判断创角天数
        d = this.shopItem.getParamByKey(ShopItemParamKey.KEY_CREATE_ROLE_DAY) - DataCenter.roleCreateDay;
        if (d > 0) {
            this.txtLimitDesc.innerHTML = StringUtil.Format(window.iLang.L2_P0_TIAN_HOU_JIE_SUO_GOU_MAI.il(), d);
            this.btn.disabled = true;
            return;
        }


        //判断是否满足购买条件（无双试炼、南蛮入侵商店等）
        let shopVo: ShopVO = ShopDataCenter.instance.getShopVoByShopID(this.shopItem.shop_id, this.shopItem.big_type);
        if (shopVo) {
            switch (shopVo.shop_group) {
                case ShopGroupId.GROUP_GOODS_SHOP://道具商店
                    if (this.shopItem.info.other_limit != 0 && this.shopItem.info.other_limit > DataCenter.vipLevel) {
                        this.txtLimitDesc.innerHTML = HtmlUtil.font(StringUtil.Format(window.iLang.L2_VIP_P0_ZHUAN_SHU.il(), this.shopItem.info.other_limit), ColorUtil.FONT_RED);
                        return;
                    }
                    break;
            }

            switch (shopVo.shop_id) {
                case EShopId.FMSOLO_SHOP_1://公会战商店
                case EShopId.FMSOLO_SHOP_2://公会战商店
                case EShopId.CLANSOLO_SHOP_1://家族战商店
                case EShopId.CLANSOLO_SHOP_2://家族战商店
                    if (shopVo.other_params < this.shopItem.info.other_limit) {
                        this.btn.gray = true;
                        this.txtLimitDesc.innerHTML = HtmlUtil.font(StringUtil.Format(window.iLang.L2_SHANG_DIAN_P0_JI_JIE_SUO.il(), StringUtil.numberToString(this.shopItem.info.other_limit)), ColorUtil.FONT_RED);
                        return;
                    }
                    break;
                case EShopId.TEST_TOWER_SHOP://试炼商店
                    if (shopVo.other_params < this.shopItem.info.other_limit) {
                        this.btn.gray = true;
                        this.txtLimitDesc.innerHTML = HtmlUtil.font(StringUtil.Format(window.iLang.L2_P0_CENG_JIE_SUO.il(), StringUtil.numberToString(this.shopItem.info.other_limit)), ColorUtil.FONT_RED);
                        return;
                    }
                    break;
            }
        }

        if (this.shopItem && (this.shopItem.reset_type > 0 || this.shopItem.info.max_buy_num > 0)) {

            let curBuyNum: number = 0;
            let maxBuyNum: number = 0;
            if (this.shopItem.info) {
                curBuyNum = this.shopItem.info.buy_num;
                maxBuyNum = this.shopItem.info.max_buy_num;
            }
            if (maxBuyNum <= 0) {
                this.txtLimitDesc.innerHTML = "";
                return;
            }

            // this.nextLevelTxt.innerHTML = "下一等级：" + ColorUtil.GetColorHtml(+ (this.currentGood.strength+1).toString() + "/" +maxLevel,ColorUtil.GREEN,22);
            let strLimitNum = ColorUtil.GetColorHtml(+curBuyNum.toString() + "/" + maxBuyNum.toString(), ColorUtil.GREEN, 22);
            if (this.shopItem.reset_type == ShopGoodsResetType.DAILY) {
                this.txtLimitDesc.innerHTML = window.iLang.L2_MEI_RI_XIAN_GOU_ch05.il() + strLimitNum;
            } else if (this.shopItem.reset_type == ShopGoodsResetType.WEEK) {
                this.txtLimitDesc.innerHTML = window.iLang.L2_MEI_ZHOU_XIAN_GOU_ch05.il() + strLimitNum;
            } else if (this.shopItem.reset_type == ShopGoodsResetType.LIMITE) {
                this.txtLimitDesc.innerHTML = window.iLang.L2_XIAN_GOU_ch05.il() + strLimitNum;
            } else if (this.shopItem.reset_type == ShopGoodsResetType.SEASON) {
                if (shopVo.cfg_shop.act_sub_type > 0) {
                    this.txtLimitDesc.innerHTML = window.iLang.L2_HUO_DONG_XIAN_GOU_ch05.il() + strLimitNum;
                } else {
                    this.txtLimitDesc.innerHTML = window.iLang.L2_MEI_SAI_JI_XIAN_GOU_ch05.il() + strLimitNum;
                }
            } else if (this.shopItem.reset_type == ShopGoodsResetType.MONTH) {
                this.txtLimitDesc.innerHTML = window.iLang.L2_MEI_YUE_XIAN_GOU_ch10.il() + strLimitNum;
            } else {
                if (shopVo.shop_type == 1) {
                    if (shopVo.cfg_shop.act_sub_type > 0) {
                        this.txtLimitDesc.innerHTML = window.iLang.L2_HUO_DONG_XIAN_GOU_ch05.il() + strLimitNum;
                    } else {
                        this.txtLimitDesc.innerHTML = window.iLang.L2_ZHONG_SHEN_XIAN_GOU_ch05.il() + strLimitNum;
                    }
                } else {
                    this.txtLimitDesc.innerHTML = window.iLang.L2_XIAN_GOU_ch05.il() + strLimitNum;
                }
            }
        } else {
            this.txtLimitDesc.innerHTML = "";
        }

    }

    protected onBtnClick(): void {
        if (!this.shopItem) {
            return;
        }
        if (this.shopItem.shop_id == EShopId.STAGE_COPY_SHOP) {
            ShopUtil.stageCopyFuBenBuy(this.shopItem.shop_id, this.shopItem.big_type, this.shopItem.itemId);
        }

        else if (this.checkIsVideoAdItem()) {
            if (this.shopItem.isSellOut) {//已售罄
                TipsUtil.showTips(window.iLang.L2_YI_JING_DA_DAO_GOU_MAI_SHANG_XIAN.il());
                return;
            }

            if (this.isAdSucc) {
                // ShopUtil.buyShopItem(this.shopItem);'
                ShopDataCenter.instance.m_shop_buy_tos(this.shopItem, 1);
            } else {
                //看广告
                this.dispatchEvent(ModuleCommand.OPEN_SHOP_ITEM_AD_DIALOG, this.shopItem);
            }
        }

        else {
            ShopUtil.buyShopItem(this.shopItem);
        }
    }

    Clean(): void {
        this.btn.gray = false;
        this.txtLimitDesc.innerHTML = "";
        // this.txtChipDesc.text = "";
        this.btn.visible = true;
        this.bingFaBox.visible = false;
        this.imgTipIcon.visible = false;
        this.imgHot.visible = false;
        this.txtOpenLimit.visible = false;
        if (this.tipView) {
            this.tipView.visible = false;
            this.tipView.clearTween();
        }
        this.SetRedPoint(this, false);
    }

    public set isSelect(value: boolean) {

    }
}