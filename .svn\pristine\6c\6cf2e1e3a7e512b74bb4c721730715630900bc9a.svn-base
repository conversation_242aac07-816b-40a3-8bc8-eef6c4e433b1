import { cfg_fuli_yueka } from "../../../cfg/vo/cfg_fuli_yueka";
import { ConfigManager } from "../../../managers/ConfigManager";
import { com } from "../../../ui/layaMaxUI";
import { UIList } from "../../baseModules/UIList";
import { XListView } from "../../baseModules/xListView/XListView";
import { XViewHelper } from "../../baseModules/xListView/XViewHelper";
import { ModuleCommand } from "../../ModuleCommand";
import { YueKaDataCenter} from "../data/YueKaDataCenter";
import { WelfarePermanentCardItem } from "./WelfarePermanentCardItem";
import { WelfareVipBigCardItem } from "./WelfareVipBigCardItem";
import { WelfareVipCardItem } from "./WelfareVipCardItem";
import {EYueKaType} from "../../../auto/ConstAuto";

export class WelfareCardView extends com.ui.res.welfare.WelfareCardViewUI {
    private _list: XListView<WelfareCardViewHelper,any>;
    private Idx:number = 0;

    initUI(): void {
        this._list = XListView.setUIXList(this, WelfareCardViewHelper, this.boxList);
        this._list.setSpace(0, 20)
    }

    addEvent(): void {
        this.addEventListener(ModuleCommand.UPDATE_WELFARE_CARD_INFO, this, this.updateView);
    }

    onOpen(param: any): void {
        if(param){
            this.Idx = param.data["ListIdx"];
            this.updateView();
        }
    }
    private updateView(): void {
        let cfgArr: cfg_fuli_yueka[] = Array.from(ConfigManager.cfg_fuli_yuekaCache.values());
        cfgArr.sort((cfg1,cfg2)=>{
            let t1 = YueKaDataCenter.instance.getFuliYuekaState(cfg1.yueka_type) == 2 ? 999 : 1;
            let t2 = YueKaDataCenter.instance.getFuliYuekaState(cfg2.yueka_type) == 2 ? 999 : 1;
            //永久招募特权卡不随着领取状态改变位置
            if(t1 == t2 || cfg1.yueka_type == EYueKaType.TYPE_101_FOREVER && cfg2.yueka_type != EYueKaType.TYPE_101_FOREVER){
                return cfg1.sort - cfg2.sort;
            }
            return t1 - t2;
        })
        this._list.array = cfgArr;
        this._list.selectedIndex = this.Idx||0
        this._list.scrollTo(this.Idx||0);
    }
}

class WelfareCardViewHelper extends XViewHelper<WelfareVipCardItem|WelfareVipBigCardItem|WelfarePermanentCardItem> {
    get contextHeight(): number {
        return this._itemView.height;
    }
    get contextWidth(): number {
        return this._itemView.width;
    }

    getViewType():number{
        let tequanType = Number(ConfigManager.cfg_misc_configCache.get("boat_privilege_discount_need_yeuka_type").value);
        if(this.dataSourse.yueka_type == tequanType){
            return 2;
        } else if(this.dataSourse.yueka_type == EYueKaType.TYPE_101_FOREVER){
            //永久招募卡
            return 3;
        } 
        return 1;
    }

    createView(): WelfareVipCardItem|WelfareVipBigCardItem|WelfarePermanentCardItem {
        let viewType = this.getViewType();
        switch(viewType){
            case 1: return new WelfareVipCardItem();
            case 2: return new WelfareVipBigCardItem();
            case 3: return new WelfarePermanentCardItem();
        }
    }
}