// import { Laya } from "Laya";
// import { Component } from "laya/components/Component";
// import { Sprite } from "laya/display/Sprite";
// import { Handler } from "laya/utils/Handler";
// import { Tween } from "laya/utils/Tween";
// import { TimerUtils } from "../../../util/TimerUtils";
// import { BaseScript } from "../../BaseScript";
// import { IUIListItem } from "../IUListItem";
// import { UIListItemData } from "../UIListItemData";
// import { Point } from "laya/maths/Point";
// import { Ease } from "laya/utils/Ease";


// /**
//  * 界面动画脚本,例如渐进,缩放
//  */
// export class UIAnimationScript extends BaseScript {

//     private _handler: Handler;
//     // private _twFadeIn: Tween;
//     private _duration = 0;
//     private _isCalled: boolean = false;
//     private _isCallInFreeFrame: boolean = false;

//     private _tween: Tween;

//     private isAnchorCenter = false;
//     private oriAlpha = -1;
//     private oriScaleX = 1;
//     private oriScaleY = 1;
//     private oriX = 0;
//     private oriY = 0;

//     private deltaX = 0;
//     private deltaY = 0;

//     public get ownerSpr(): Sprite {
//         return this.owner as Sprite;
//     }

//     _onAwake(): void {
//     }

//     _onEnable(): void {

//     }

//     _destroy(): void {
//         super._destroy();
//     }

//     onReset(): void {
//         if (this._handler){
//             this._handler.clear();
//             this._handler = null;
//         }
     
//         this.clearTween();
//         this._isCalled = false;
//         this._isCallInFreeFrame = false;
//         this.oriAlpha = -1;
//     }

//     public callInFreeFrame(minDelayMs: number, maxDelayMs: number, caller: any, method: Function, args: any[] = null, { priority = 100 } = {}) {

//         this._isCallInFreeFrame = true;

//         if (this.oriAlpha < 0) {
//             this.oriAlpha = this.ownerSpr.alpha;
//         }

//         this.pauseTween();

//         this._handler = Handler.create(caller, method, args, false);
//         if (this._isCalled == false) {
//             this.ownerSpr.alpha = 0;
//             TimerUtils.callInFreeFrame(minDelayMs, maxDelayMs, this, this.onLaterCall, null, { repeat: false, coverBefore: true, priority: priority });
//         } else {
//             TimerUtils.clearLaterTimer(this, this.onLaterCall);
//             this.onLaterCall();
//         }
//     }

//     private onLaterCall() {
//         this._handler.run();
//         this._isCalled = true;
//         this.ownerSpr.alpha = this.oriAlpha;
//         this.oriAlpha = -1;
//         this._isCallInFreeFrame = false;

//         this.doTween();
//     }

//     private doTween() {
//         if (this._tween) {
//             this._tween.restart();
//         }
//     }

//     private pauseTween() {
//         if (this._tween) {
//             this._tween.pause();
//         }
//     }

//     private clearTween() {
//         if (this._tween) {
//             this._tween.complete();
//         }
//         if (this._tween) {
//             this._tween.clear();
//             this._tween = null;
//         }
//     }

//     /**alpha渐进显示 */
//     public setFadeIn(duration: number, ease: Function = null) {
//         this.clearTween();

//         if (this.oriAlpha >= 0) {
//             var oriAlpha = this.oriAlpha;
//         } else {
//             var oriAlpha = this.ownerSpr.alpha;
//         }

//         this._tween = Tween.from(this.ownerSpr, { alpha: 0.01 }, duration, ease, Handler.create(this, () => {
//             this.ownerSpr.alpha = oriAlpha;
//             this._tween.clear();
//             this._tween = null;
//         }), 0, true, true, false);

//         if (!this._isCallInFreeFrame) {
//             this.doTween();
//         }
//     }

//     /**scale渐出显示 */
//     public setScaleIn(duration: number, ease: Function = Ease.backOut) {
//         this.clearTween();
//         if (!this.ownUi.anchorX && !this.ownUi.anchorY && !this.ownUi.pivotX && !this.ownUi.pivotY) {
//             this.isAnchorCenter = true;

//             this.ownUi.anchorX = 0.5;
//             this.ownUi.anchorY = 0.5;

//             this.saveAnchorAttrs();

//             this.ownUi.x = this.oriX + this.deltaX;
//             this.ownUi.y = this.oriY + this.deltaY;
//         } else {
//             this.isAnchorCenter = false;
//         }

//         this._tween = Tween.from(this.ownerSpr, { scaleX: 0.1, scaleY: 0.1 }, duration, ease, Handler.create(this, () => {
//             this.revertAnchorAttrs();
//             this._tween.clear();
//             this._tween = null;
//         }), 0, true, true, false);

//         if (!this._isCallInFreeFrame) {
//             this.doTween();
//         }
//     }

//     private revertAnchorAttrs() {
//         if (this.isAnchorCenter) {
//             this.ownUi.anchorX = 0;
//             this.ownUi.anchorY = 0;

//             if (this.ownUi.x == this.oriX + this.deltaX) {
//                 this.ownUi.x = this.oriX;
//             }
//             if (this.ownUi.y == this.oriY + this.deltaY) {
//                 this.ownUi.y = this.oriY;
//             }
//         }

//         this.ownerSpr.scaleX = this.oriScaleX;
//         this.ownerSpr.scaleY = this.oriScaleY;
//     }

//     private saveAnchorAttrs() {

//         this.oriScaleX = this.ownUi.scaleX;
//         this.oriScaleY = this.ownUi.scaleY;
//         this.oriX = this.ownUi.x;
//         this.oriY = this.ownUi.y;
//         this.deltaX = Math.round(this.ownUi.width * this.oriScaleX * this.ownUi.anchorX);
//         this.deltaY = Math.round(this.ownUi.height * this.oriScaleY * this.ownUi.anchorY);
//     }
// }