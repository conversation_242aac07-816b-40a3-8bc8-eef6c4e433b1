import {CCMessage} from "../CCMessage";
import { Byte } from "laya/utils/Byte";


export class m_zero_buy_op_tos extends CCMessage
    {
        op_type:number = 0;

       constructor()
       {
         super();
         
       }

       pack(result:Byte):void 
       {
         result.writeInt32(this.op_type);
       }

 
       protoId():number 
       {
           return 181;
       }

        childProtoId():number 
       {
           return 23168;
       }
}
