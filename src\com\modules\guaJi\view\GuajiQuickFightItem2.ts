import {cfg_fuli_yueka} from "../../../cfg/vo/cfg_fuli_yueka";
import {p_fuli_yueka} from "../../../proto/common/p_fuli_yueka";
import {com} from "../../../ui/layaMaxUI";
import {GameUtil} from "../../../util/GameUtil";
import {HtmlUtil} from "../../../util/HtmlUtil";
import {ModuleCommand} from "../../ModuleCommand";
import {PanelEventConstants} from "../../PanelEventConstants";
import {IUIListItem} from "../../baseModules/IUListItem";
import UIButton from "../../baseModules/UIButton";
import {UIHTMLDiv} from "../../baseModules/UIHTMLDiv";
import {UIListItemData} from "../../baseModules/UIListItemData";
import {UIProgressBar} from "../../baseModules/UIProgressBar";
import {MenuDataCenter} from "../../menu/data/MenuDataCenter";
import {PaymentLinkUtil} from "../../payment/PaymentLinkUtil";
import {WelfareDataCenter} from "../../welfare/data/WelfareDataCenter";
import {YueKaDataCenter} from "../../welfare/data/YueKaDataCenter";
import {CommonButton} from "../../BaseDialog";
import {EYueKaType} from "../../../auto/ConstAuto";

/**特权商店item */
export class GuajiQuickFightItem2 extends com.ui.res.guaJi.GuajiQuickFightItem2UI implements IUIListItem {

    private state: number = 0;
    private yueka_type: number = 0;
    private kv: p_fuli_yueka;
    //private effBg: CCAnimation;
    private sumDay: number = 0;
    private expBar: UIProgressBar;

    initUI(): void {
        UIHTMLDiv.SetUIHTMLDiv(this.rechargeProgressTxt, 20, "#ffffff", 6, "center");
        this.rechargeProgressTxt.style.stroke = 2;
        // this.rechargeProgressTxt.style.wordWrap = false;

        this.expBar = UIProgressBar.SetUIProgressBar(this, this.progress);

    }

    UpdateItem(itemData: UIListItemData, select: boolean): void {
        if (!itemData.data)
            return;

        let info: cfg_fuli_yueka = itemData.data
        this.setData(info);
    }

    public setData(info: cfg_fuli_yueka) {
        let kv = YueKaDataCenter.instance.getFuliYuekaInfo(info.yueka_type);
        if (!kv) return;
        this.labName.text = window.iLang.L2_JI_HUO.il() + info.yueka_name;
        this.sumDay = info.duration;
        this.kv = kv;
        this.yueka_type = this.kv.type;
        this.txtGold.text = GameUtil.gold(info.gold).toString();

        this.showtypeicon.skin = "payment/img_quickitem_bg_" + info.yueka_type + ".png"

        let tequanDesc: string = "";
        if (info.quick_hanging_times > 0) {
            tequanDesc = window.iLang.L2_MEI_TIAN_MIAN_FEI_E_WAI_KUAI_SU_GUA_JI_CI_SHU_ch17.il() + info.quick_hanging_times;
            if (info.yueka_type == EYueKaType.TYPE_4_KING) {
                tequanDesc = window.iLang.L2_MEI_TIAN_E_WAI_KUAI_SU_GUA_JI_CI_SHU_ch17.il() + info.quick_hanging_times;
            }
            this.txtTequanDesc.text = tequanDesc;
            this.bgText.width = this.txtTequanDesc.textField.textWidth + 100;
        }
        if (info.hero_bag_num > 0) {
            tequanDesc = window.iLang.L2_WU_JIANG_BEI_BAO_ch17.il() + info.hero_bag_num;
            this.txtBagDesc.text = tequanDesc;
            this.bgBagDesc.width = this.txtBagDesc.textField.textWidth + 100;
            this.bgBagDesc.visible = true;
            this.bgText.y = 100;
        } else {
            this.bgBagDesc.visible = false;
            this.bgText.y = 100;
        }

        //充值 领取 已领取
        this.state = YueKaDataCenter.instance.getFuliYuekaState(this.yueka_type);
        let btnSkin = CommonButton.BtnYellow;
        if (this.state == 0) {
            btnSkin = CommonButton.BtnGreen;
            this.btnRecharge.label = window.iLang.L2_TUI_JIAN_CHONG_ZHI.il();
            this.rechargeBox.visible = true;
            this.expBar.SetValue(this.kv.price, info.need_recharge);
            this.expBar.SetValueTxt(window.iLang.L2_DANG_QIAN_CHONG_ZHI_ch05.il() + this.kv.price + "/" + info.need_recharge);
            this.rechargeProgressTxt.innerHTML = window.iLang.L2_ZAI_CHONG_ZHI_P0_YUAN_JI_KE_JIE_SUO.il([HtmlUtil.font((info.need_recharge - this.kv.price) + "", "#ffd74d")]);

        } else {
            this.rechargeBox.visible = false;
        }
        this.btnRecharge.skin = btnSkin;
        UIButton.ResetButtonLabel(this.btnRecharge);

    }

    addClick(): void {
        this.addOnClick(this, this.btnRecharge, this.onClick);
    }

    private onClick(): void {
        if (this.state == 0) {
            if (this.yueka_type === EYueKaType.TYPE_4_KING) {
                PaymentLinkUtil.onYueKaRecommendPayment(this.yueka_type);
                return;
            }
            //判断新手之路是否开启
            if (GameUtil.isSysOpen(PanelEventConstants.WELFARE_LIMIT_HERO, 0, false) && MenuDataCenter.instance.checkIconCfgCondition(PanelEventConstants.WELFARE_LIMIT_HERO)) {
                if (WelfareDataCenter.instance.limit_sign_info.status === 1) {
                    PaymentLinkUtil.onYueKaRecommendPayment(this.yueka_type);
                } else {
                    this.dispatchEvent(ModuleCommand.OPEN_WELFARE_LIMIT_HERO_DIALOG);
                }
            } else {
                PaymentLinkUtil.onYueKaRecommendPayment(this.yueka_type);
            }

        } else if (this.state == 1) {
            WelfareDataCenter.instance.m_fuli_yueka_fetch_tos(0);
        } else {

        }
    }

    Clean(): void {

    }

    isSelect: boolean;
}