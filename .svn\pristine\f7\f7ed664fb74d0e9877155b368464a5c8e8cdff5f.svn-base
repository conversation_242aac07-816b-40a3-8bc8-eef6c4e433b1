import { BaseScript } from "../../BaseScript";
import { XmlFormatVo } from "../../../util/XmlFormatVo";
import { GSkeleton } from "../../baseModules/skeleton/GSkeleton";
import CommonTalkView from "../../common/CommonTalkView";
import { ModuleCommand } from "../../ModuleCommand";
import { TDPathPointVo } from "../../tdBase/game/vo/TDPathPointVo";
import { m_fight_simp_result_toc } from "../../../proto/line/m_fight_simp_result_toc";
import { GlobalConfig } from "../../../../game/GlobalConfig";
import { GuideMgr } from "../../guide/GuideMgr";
import { cfg_guide_helper } from "../../../cfg/vo/cfg_guide_helper";
import GuaJiDialog from "../dialog/GuaJiDialog";
import { GuajiBuildGuideScript_StepBase } from "./GuajiBuildGuideScript_StepBase";
import { GuajiBuildGuideScript_guide_wakeup_300001 } from "./GuajiBuildGuideScript_guide_wakeup_300001";
import { GuajiBuildGuideScript_guide_story_3000011 } from "./GuajiBuildGuideScript_guide_story_3000011";
import { GuaJiBuildDataCenter } from "../data/GuaJiBuildDataCenter";
import { Image } from "laya/ui/Image";
import { Laya } from "Laya";
import { LayerManager } from "../../../managers/LayerManager";
import { GuajiBuildWorker } from "../game/GuajiBuildWorker";
import { LordDataCenter } from "../../Lord/data/LordDataCenter";

export class GuajiBuildGuideScript extends BaseScript {

    public static guide_wakeup_300001 = 300001;             //唤醒引导
    public static guide_story_3000011 = 3000011;             //唤醒引导

    public currGuideStep: GuajiBuildGuideScript_StepBase = null;
    protected guideMap: Map<number, typeof GuajiBuildGuideScript_StepBase> = new Map();// public isFingerGuide: boolean = true;

    private isStart = false;
    private _guideId: number = 0;
    private preGuideId: number = 0;
    public isNewLogin = true;
    public isPlayerSkipGuide = false;
    private isWorkerTalked = false;
    public dialog:GuaJiDialog;

    private blackImageBg:Image;

    public static get isAllFinish() {
        //兼容外服玩家
        if (GuideMgr.ins.cmdCloseGuide) {
            return true;
        }
        if (GuideMgr.ins.isFinishMission(GuajiBuildGuideScript.guide_story_3000011)) {
            return true;
        }

        // let isFinish = this.curShowPass >= this.allGuideFinishPass;
        // return isFinish;
        return false;
    }

    get isAllFinish() {
        return GuajiBuildGuideScript.isAllFinish;
    }
    get guideId(){
        if(this.isStart){
            return this._guideId;
        }else{
            return 0;
        }
        return this._guideId;
    }

    private forceMapLoaded = false;
    get isMapLoaded(){

        if (this.forceMapLoaded){
            return true;
        }

        let voList = GuaJiBuildDataCenter.instance.getBuildVoList();
        let isMapLoaded = this.dialog?.buildView?.isMapLoaded;
        isMapLoaded = isMapLoaded && voList.length > 0;

        return !!isMapLoaded;
    }

    protected initUI(): void {
        // this.isNewHeroGuide = TdMainDataCenter.ins.isFirstGuideAllFinish == false;
    }

    protected addClick(): void {
    }

    protected addEvent(): void {
        // this.addEventListener(ModuleCommand.GUIDE_SKIP, this, this.onPlayerSkipGuide);
    }

    public init(isFirstEnterDialog: boolean, dialog:GuaJiDialog) {
        this.isNewLogin = isFirstEnterDialog;
        this.dialog = dialog;

        if (this.guideMap.size <= 0) {
            this.guideMap.set(GuajiBuildGuideScript.guide_wakeup_300001, GuajiBuildGuideScript_guide_wakeup_300001);
            this.guideMap.set(GuajiBuildGuideScript.guide_story_3000011, GuajiBuildGuideScript_guide_story_3000011);
        }

        this.onTimerGuide();

        //防止超时
        this.timer.once(6000, this, () => {
            this.forceMapLoaded = true;
        })
    }

    private startGuide(){

        if(this.isStart){
            return;
        }

        if(this.isMapLoaded == false){

            if(!this.blackImageBg){

                let width = this.dialog.relativeWidth;
                let height = this.dialog.relativeHeight;

                let blackImage = new Image();

                // maskImg.loadImage("common/guide_mask_bg.png");
                blackImage.name = "blackImage";
                blackImage.width = width;
                blackImage.height = height;
                blackImage.zOrder = LayerManager.STAGE_ZORDE_TOPEST;
                blackImage.graphics.drawRect(0, 0, width, height, "#000000");
                Laya.stage.addChild(blackImage);
                this.blackImageBg = blackImage;
            }
            return;
        }

        this.isStart = true;

        let isFinish = GuideMgr.ins.isFinishMission(GuajiBuildGuideScript.guide_wakeup_300001);
        if (isFinish == false) {
            GuideMgr.ins.setGranchGuideId(GuajiBuildGuideScript.guide_wakeup_300001);
        } else {
            isFinish = GuideMgr.ins.isFinishMission(GuajiBuildGuideScript.guide_story_3000011);
            if (isFinish == false) {
                GuideMgr.ins.setGranchGuideId(GuajiBuildGuideScript.guide_story_3000011);
            }
        }

        if (this.blackImageBg) {
            this.blackImageBg.destroy();
            this.blackImageBg = null;
        }
    }

    isOpenGuide() {
        return true;
    }

    checkGuide(currCfg: cfg_guide_helper) {

        if(currCfg){
            this._guideId = currCfg.guide_id;
        }

        if (this.isStart == false) {
            return;
        }

        this.setCurrGuideStep();
        this.currGuideStep?.checkGuide(currCfg);

        let guideTip = GuideMgr.ins.getGuideTip(this.dialog.guideTar);
        if(this.isAllFinish == false && guideTip && guideTip.guideMask){
            // guideTip.guideMask.visible = true;
            // if (guideTip.guideMask.visible){
            //     guideTip.guideMask.alpha = 1;
            // }
        }
    }

    onGuideEnd(guideId) {
        this.currGuideStep?.onGuideEnd(guideId);

        if (guideId == GuajiBuildGuideScript.guide_wakeup_300001) {
            GuideMgr.ins.setGranchGuideId(GuajiBuildGuideScript.guide_story_3000011);
        }
    }

    onUpdate(): void {
        this.onTimerGuide();
        this.checkWorkerTalk();
    }

    protected onTimerGuide(): void {

        this.startGuide();

        if(!this.guideId){
            return;
        }

        this.setCurrGuideStep();
        this.isNewLogin = false;

    }

    private checkWorkerTalk() {
        if(this.isWorkerTalked){
            return;
        }

        let buildView = this.dialog?.buildView;
        if(!buildView){
            return;
        }

        // this.timer.once(2000, this, () => {
        let allWorker: GuajiBuildWorker[] = [];

        for (let buildItem of buildView.buildItemMap.values()) {
            for (let worker of buildItem.workerList) {
                allWorker.push(worker);
                continue;
            }
        }

        if (allWorker.length) {

            allWorker.forEach(worker => {
                //干活!干活!
                let talk = GuaJiBuildDataCenter.instance.getPlotDialogue(2, LordDataCenter.instance.camp);
                worker.talkView.setTalkText(talk, 20000, true);
                worker.talkView.pos(10, -140);
            })

            this.isWorkerTalked = true;
        }


        // let building = this.buildView.buildItemMap.get(EBuildingType.BUILD_TYPE_GOLD_MINE);
        // if (building && building.workerList[0]) {
        //     let firstWorker = building.workerList[0];

        //     //干活!干活!
        //     let talk = GuaJiBuildDataCenter.instance.getPlotDialogue(2, this.camp);
        //     firstWorker.talkView.setDesc(talk, 2000, true);
        //     firstWorker.talkView.pos(10, -140);
        // }
        // // })
    }

    private setCurrGuideStep() {

        if (this.isStart == false) {
            return;
        }

        if(!this.guideId){
            return;
        }

        if (this.preGuideId != this.guideId) {
            this.preGuideId = this.guideId;

            let _cls = this.guideMap.get(this.guideId);
            if (!_cls) {
                _cls = GuajiBuildGuideScript_StepBase;
            }

            this.currGuideStep?.destroy();
            this.currGuideStep = new _cls();
            this.currGuideStep.init(this.isNewLogin, this.dialog);
        } else {
            this.currGuideStep?.onTimerGuide();
        }
    }

    public onFightResult(toc: m_fight_simp_result_toc) {
        this.currGuideStep?.onFightResult(toc);
    }

    destroy(): void {

        if (this.currGuideStep && this.currGuideStep.destroyed == false){
            this.currGuideStep.destroy();
        }

        if (this.blackImageBg) {
            this.blackImageBg.destroy();
            this.blackImageBg = null;
        }

        super.destroy();
    }
}
