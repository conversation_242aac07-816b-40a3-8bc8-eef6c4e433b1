import CommonTalkView from "../../common/CommonTalkView";
import {Point} from "../../../../../libs/laya/maths/Point";
import {TdMainDialog} from "../dialog/TdMainDialog";
import {TDSceneElementMgr} from "../../tdBase/game/TDSceneElementMgr";
import {MiscConstAuto} from "../../../auto/MiscConstAuto";
import {CfgCacheMapMgr} from "../../../cfg/CfgCacheMapMgr";
import {cfg_td_monster_talk} from "../../../cfg/vo/cfg_td_monster_talk";
import {timer} from "Laya";
import {TdMainDataCenter} from "../data/TdMainDataCenter";

export class TdMainLordTalk {
    private lordTalkView: CommonTalkView;
    private dialog;
    private cfgPassTalks: cfg_td_monster_talk[];
    public myPos: Point;

    constructor(dialog: TdMainDialog) {
        this.dialog = dialog;
    }

    public static getTalkArg(index: number): number {
        let talkArgs: string[] = MiscConstAuto.td_main_fail_lord_talk_interval;
        let args: number[] = [];
        for (let arg of talkArgs) {
            args.push(parseInt(arg));
        }

        if (args && args.length > index) {
            return args[index];
        }

        return 0;
    }

    public initTalkView() {
        if (!this.dialog) {
            return false;
        }

        let lord = this.dialog.lord;
        if (!lord) {
            return false;
        }

        if (!this.lordTalkView) {
            this.lordTalkView = new CommonTalkView();
            this.lordTalkView.bg.skin = "common/lord_tips_bg.png";
            this.lordTalkView.bg.sizeGrid = "11,139,29,43";
            let elementMgr: TDSceneElementMgr = this.dialog.elementMgr;
            let stagePos = elementMgr.battleLayer.localToGlobal(Point.TEMP.setTo(lord.heroPointVo.targetX, lord.heroPointVo.targetY));
            let showpos = this.dialog.globalToLocal(stagePos);
            this.myPos = new Point(showpos.x, showpos.y);
            this.lordTalkView.visible = false;
            this.dialog.addChild(this.lordTalkView);
        }
    }

    public sayLordFailTalk(): void {
        this.initTalkView();

        if (!TdMainDataCenter.ins.isFightWin) {
            let duration = TdMainLordTalk.getTalkArg(1);
            if (duration > 0) {
                let content = MiscConstAuto.td_main_fail_lord_talk_text;
                this.showTalk(content, duration);
            }
        }
    }

    public showTalk(content: string, duration: number = 3000) {
        if (this.lordTalkView) {
            this.lordTalkView.setTalkText(content, duration);
            this.lordTalkView.pos(this.myPos.x  -150, this.myPos.y - this.lordTalkView.htmlHeight - 120);
            this.lordTalkView.visible = true;
        }
    }

    /** 战斗失败时，说的话 */
    public showLordFailTalks() {
        this.sayLordFailTalk();

        let interval = TdMainLordTalk.getTalkArg(0);
        interval > 0 && timer.loop(interval, this, this.sayLordFailTalk);
    }

    /** 战斗胜利时，说的话 */
    public showLordWinTalks(nowPass: number): void {
        this.initTalkView();
        let talkList = CfgCacheMapMgr.cfg_td_monster_talkGroupCache.get(nowPass);
        if (talkList) {
            this.cfgPassTalks = talkList.concat([]);
        }
        this.sayLordWinTalk();

        let interval = MiscConstAuto.td_monster_talk_interval;
        interval > 0 && timer.loop(interval * 1000, this, this.sayLordWinTalk);
    }

    private sayLordWinTalk() {
        if (!this.cfgPassTalks || this.cfgPassTalks.length <= 0) {
            timer.clear(this, this.sayLordWinTalk);
            return;
        }

        this.showTalk(this.cfgPassTalks.shift().desc, (MiscConstAuto.td_monster_talk_interval - 1) * 1000);
    }

    public clearLordBattleTalk() {
        if (this.lordTalkView) {
            this.lordTalkView.visible = false;
            timer.clear(this, this.sayLordFailTalk)
            timer.clear(this, this.sayLordWinTalk)
            if (this.cfgPassTalks) {
                this.cfgPassTalks.length = 0;
            }
        }
    }

}
