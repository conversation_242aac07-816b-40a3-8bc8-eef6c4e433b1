import { CCMessage } from "../CCMessage";
import { p_kv } from "../common/p_kv";
export class m_zero_buy_op_toc extends CCMessage {
    constructor() {
        super();
        this.op_type = 0;
        this.login_days = 0;
        this.up_stage_status = 0;
        this.end_time = 0;
        this.fetch_list = [];
    }
    unpack(result) {
        this.op_type = result.readInt32();
        this.login_days = result.readInt16();
        this.up_stage_status = result.readInt32();
        this.end_time = result.readInt32();
        let fetch_listlen = result.readUint16();
        let fetch_listTemp;
        for (let fetch_listIndex = 0; fetch_listIndex < fetch_listlen; fetch_listIndex++) {
            fetch_listTemp = new p_kv();
            fetch_listTemp.unpack(result);
            this.fetch_list.push(fetch_listTemp);
        }
    }
    protoId() {
        return 181;
    }
    childProtoId() {
        return 23168;
    }
}
