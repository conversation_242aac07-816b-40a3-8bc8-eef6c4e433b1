export abstract class MatchConst { 
		public static readonly MATCH_TYPE_MAIN_BATTLE:number = 101;//征战关卡
		public static readonly MATCH_TYPE_ARENA:number = 102;//竞技场
		public static readonly MATCH_TYPE_CONTEST:number = 103;//切磋
		public static readonly MATCH_TYPE_GM_MONSTER:number = 110;//GM怪物组战斗
		public static readonly MATCH_TYPE_GM_EXPORT_ROLE:number = 111;//GM挑战镜像
		public static readonly MATCH_TYPE_GM_EXPORT_ROLE_MULTI:number = 112;//GM挑战多阵容镜像
		public static readonly MATCH_TYPE_TEST_TOWER:number = 115;//试练塔
		public static readonly MATCH_TYPE_FAMILY_BOSS:number = 116;//公会副本
		public static readonly MATCH_TYPE_RED_CLIFF:number = 117;//元素圣殿
		public static readonly MATCH_TYPE_GUAN_DU:number = 118;//暗黑地牢
		public static readonly MATCH_TYPE_CROSS_FAMILY_SOLO:number = 119;//公会战
		public static readonly MATCH_TYPE_WORLD_BOSS:number = 120;//深渊入侵
		public static readonly MATCH_TYPE_GHOST_TOWER:number = 121;//神魔塔
		public static readonly MATCH_TYPE_CROSS_CONTEST:number = 122;//战区争霸切磋
		public static readonly MATCH_TYPE_SKY_TOWER:number = 123;//天选塔
		public static readonly MATCH_TYPE_CROSS_FRIEND_CONTEST:number = 124;//战区好友切磋
		public static readonly MATCH_TYPE_CROSS_DOMINATE_CONTEST:number = 125;//诸神战场切磋
		public static readonly MATCH_TYPE_DAILY_FB:number = 201;//日常副本
		public static readonly MATCH_TYPE_GOD_TRIAL:number = 501;//无尽试炼
		public static readonly MATCH_TYPE_ARES_PALACE:number = 502;//神位争夺
		public static readonly MATCH_TYPE_PASS_BEHEAD:number = 503;//勇闯异境
		public static readonly MATCH_TYPE_QXZL:number = 504;//冠军赛
		public static readonly MATCH_TYPE_HZZD:number = 505;//王者峡谷战
		public static readonly MATCH_TYPE_PEAK:number = 506;//巅峰赛
		public static readonly MATCH_TYPE_CROSS_LADDER:number = 507;//荣耀天梯赛
		public static readonly MATCH_TYPE_BATTLE_TRIAL:number = 508;//组队征战
		public static readonly MATCH_TYPE_LARGE_PEAK:number = 509;//战场主宰
		public static readonly MATCH_TYPE_NATION_WAR:number = 510;//尾兽争夺战
		public static readonly MATCH_TYPE_FIGHT_SHOW:number = 601;//战斗展示
		public static readonly MATCH_TYPE_NEW_STORY:number = 602;//新手剧情动画演示
		public static readonly MATCH_TYPE_LCQS:number = 701;//神装纷争
		public static readonly MATCH_TYPE_CS_RANDOM_PVP:number = 801;//战区争霸
		public static readonly MATCH_TYPE_MAZE:number = 802;//主题玩法
		public static readonly MATCH_TYPE_MAZE_ACT:number = 803;//主题玩法
		public static readonly MATCH_TYPE_DICE_ACT:number = 804;//密境藏宝殿
		public static readonly MATCH_TYPE_XSWH:number = 805;//跨服首领战
		public static readonly MATCH_TYPE_HERO_CHALLENGE:number = 806;//主题英雄挑战
		public static readonly MATCH_TYPE_STORY_ACT:number = 807;//剧情活动战斗
		public static readonly MATCH_TYPE_TEAM_XSWH:number = 808;//组队首领战
		public static readonly MATCH_TYPE_DOMINATE_PVP:number = 811;//诸神战场
		public static readonly MATCH_TYPE_NATION_TOWER_1:number = 901;//跨服试炼场・冰
		public static readonly MATCH_TYPE_NATION_TOWER_2:number = 902;//跨服试炼场・森
		public static readonly MATCH_TYPE_NATION_TOWER_3:number = 903;//跨服试炼场・炎
		public static readonly MATCH_TYPE_NATION_TOWER_4:number = 904;//跨服试炼场・神
		public static readonly MATCH_TYPE_NATION_TOWER_5:number = 905;//跨服试炼场・魔
		public static readonly MATCH_TYPE_MASTER_CARD:number = 1001;//魂卡晋升考核
		public static readonly MATCH_TYPE_FIGHT_PLOT:number = 9999;//剧情战斗
		public static readonly MATCH_TYPE_BOAT_RACE:number = 551;//公会竞渡
		public static readonly MATCH_TYPE_STORY_MAZE:number = 552;//争霸副本
		public static readonly MATCH_TYPE_STAGE_COPY:number = 553;//成神之路副本
		public static readonly MATCH_TYPE_MOCK_BATTLE:number = 554;//模拟对战
		public static readonly MATCH_TYPE_TD_TRIAL:number = 555;//一个都不能跑
		public static readonly MATCH_TYPE_TD_MAIN:number = 556;//征战塔防
		public static readonly MATCH_TYPE_TD_DAILY_COPY:number = 557;//日常副本
		public static readonly MATCH_TYPE_DIVINE_COPY:number = 1101;//神临副本
		public static readonly MATCH_TYPE_CROSS_CLAN_SOLO:number = 1102;//家族战
}
export abstract class RankConst { 
		public static readonly RANK_KEY_FAST_POWER:number = 803;//快速排行榜
		public static readonly RANK_KEY_MAIN_BATTLE:number = 202;//普通排行榜
		public static readonly RANK_KEY_SKY_TOWER:number = 813;//快速排行榜
		public static readonly RANK_KEY_TEST_TOWER:number = 801;//快速排行榜
		public static readonly RANK_KEY_GHOST_TOWER:number = 800;//快速排行榜
		public static readonly RANK_KEY_ARENA:number = 101;//特殊排行榜
		public static readonly RANK_KEY_FAMILY_POWER:number = 701;//公会排行榜
		public static readonly RANK_KEY_GOD_TRIAL:number = 802;//快速排行榜
		public static readonly RANK_KEY_FAMILY_BOSS:number = 901;//公会副本排行榜（写死占用key，不走通用）
		public static readonly RANK_KEY_RED_CLIFF_FAST:number = 804;//元素圣殿排行榜
		public static readonly RANK_KEY_FAMILY_INTERIOR_HONGBAO:number = 301;//公会内部红包排行榜
		public static readonly RANK_KEY_GUANDU_PASS_FAST:number = 805;//黄巾之战排行榜
		public static readonly RANK_KEY_QXZL_FAST:number = 806;//冠军赛排行榜
		public static readonly RANK_KEY_CSC_FMSOLO:number = 807;//公会战战绩排行榜
		public static readonly RANK_KEY_CSC_CLANSOLO:number = 8071;//家族战战绩排行榜
		public static readonly RANK_KEY_LCQS:number = 808;//快速排行榜
		public static readonly RANK_KEY_QXZL_CROSS_FAST:number = 809;//冠军赛排行榜
		public static readonly RANK_KEY_PEAK_FAST:number = 810;//冠军赛排行榜
		public static readonly RANK_KEY_CSC_LADDER:number = 812;//跨服天梯排行榜
		public static readonly RANK_KEY_BATTLE_TRIAL:number = 821;//跨服天梯排行榜
		public static readonly RANK_KEY_LARGE_AUDITION_PEAK_1:number = 83101;//百服主宰海选赛排行榜
		public static readonly RANK_KEY_LARGE_AUDITION_PEAK_2:number = 83102;//百服主宰海选赛排行榜
		public static readonly RANK_KEY_LARGE_AUDITION_PEAK_3:number = 83103;//百服主宰海选赛排行榜
		public static readonly RANK_KEY_LARGE_AUDITION_PEAK_4:number = 83104;//百服主宰海选赛排行榜
		public static readonly RANK_KEY_LARGE_AUDITION_PEAK_5:number = 83105;//百服主宰海选赛排行榜
		public static readonly RANK_KEY_LARGE_AUDITION_PEAK_6:number = 83106;//百服主宰海选赛排行榜
		public static readonly RANK_KEY_LARGE_AUDITION_PEAK_7:number = 83107;//百服主宰海选赛排行榜
		public static readonly RANK_KEY_LARGE_PEAK_1:number = 83201;//百服主宰排行榜
		public static readonly RANK_KEY_LARGE_PEAK_2:number = 83202;//百服主宰排行榜
		public static readonly RANK_KEY_LARGE_PEAK_3:number = 83203;//百服主宰排行榜
		public static readonly RANK_KEY_LARGE_PEAK_4:number = 83204;//百服主宰排行榜
		public static readonly RANK_KEY_LARGE_PEAK_5:number = 83205;//百服主宰排行榜
		public static readonly RANK_KEY_LARGE_PEAK_6:number = 83206;//百服主宰排行榜
		public static readonly RANK_KEY_LARGE_PEAK_7:number = 83207;//百服主宰排行榜
		public static readonly RANK_KEY_MASTER_CARD_POWER:number = 833;//快速排行榜
		public static readonly RANK_KEY_FISH_POWER:number = 834;//快速排行榜
		public static readonly RANK_KEY_NORMAL_WAR:number = 851;//尾兽战个人积分排行
		public static readonly RANK_KEY_NORMAL_CAMP_WAR:number = 852;//尾兽战阵营积分排行
		public static readonly RANK_KEY_DAY_NORMAL_WAR:number = 3002;//尾兽战个人每日积分排行
		public static readonly RANK_KEY_DAY_NORMAL_CAMP_WAR:number = 3003;//尾兽战阵营每日积分排行
		public static readonly RANK_KEY_ACTIVITY_QUICK_PASS:number = 1010;//速战排行榜
		public static readonly RANK_KEY_ACTIVITY_TRAVEL:number = 1011;//探索排行榜
		public static readonly RANK_KEY_ACTIVITY_TAX:number = 1012;//点金排行榜
		public static readonly RANK_KEY_ACTIVITY_BEHEAD:number = 1013;//异境排行榜
		public static readonly RANK_KEY_ACTIVITY_HUANG_JIN:number = 1014;//黄巾排行榜
		public static readonly RANK_KEY_ACTIVITY_DRUM:number = 1015;//开箱排行榜
		public static readonly RANK_KEY_WORLD_BOSS_ROLE_HURT:number = 1020;//南蛮入侵排行榜
		public static readonly RANK_KEY_RANDOM_PVP:number = 406;//战前争霸排行
		public static readonly RANK_KEY_DOMINATE_PVP_1:number = 40701;//百服争霸排行
		public static readonly RANK_KEY_DOMINATE_PVP_2:number = 40702;//百服争霸排行
		public static readonly RANK_KEY_DOMINATE_PVP_3:number = 40703;//百服争霸排行
		public static readonly RANK_KEY_DOMINATE_PVP_4:number = 40704;//百服争霸排行
		public static readonly RANK_KEY_DOMINATE_PVP_5:number = 40705;//百服争霸排行
		public static readonly RANK_KEY_DOMINATE_PVP_6:number = 40706;//百服争霸排行
		public static readonly RANK_KEY_DOMINATE_PVP_7:number = 40707;//百服争霸排行
		public static readonly RANK_KEY_MERGE_ACTIVE_SCORE:number = 5001;//合服活动：活跃积分排行榜
		public static readonly RANK_KEY_XSWH_FAST:number = 1030;//跨服首领战七天总伤害排行榜
		public static readonly RANK_KEY_XSWH_BEST_FAST_1:number = 1031;//跨服领主名将堂排行榜
		public static readonly RANK_KEY_XSWH_BEST_FAST_2:number = 1032;//跨服领主名将堂排行榜
		public static readonly RANK_KEY_XSWH_BEST_FAST_3:number = 1033;//跨服领主名将堂排行榜
		public static readonly RANK_KEY_XSWH_BEST_FAST_4:number = 1034;//跨服领主名将堂排行榜
		public static readonly RANK_KEY_XSWH_BEST_FAST_5:number = 1035;//跨服领主名将堂排行榜
		public static readonly RANK_KEY_XSWH_BEST_FAST_6:number = 1036;//跨服领主名将堂排行榜
		public static readonly RANK_KEY_XSWH_BEST_FAST_7:number = 1037;//跨服领主名将堂排行榜
		public static readonly RANK_KEY_TEAM_XSWH_SEASON_FAST:number = 1040;//组队跨服领主战赛季排行榜
		public static readonly RANK_KEY_TEAM_XSWH_FAST:number = 1041;//组队跨服领主战每日排行榜
		public static readonly RANK_KEY_TEAM_XSWH_BEST_FAST:number = 1042;//组队跨服领主战名将堂排行榜
		public static readonly RANK_KEY_NATION_TOWER_1:number = 1101;//快速排行榜
		public static readonly RANK_KEY_NATION_TOWER_2:number = 1102;//快速排行榜
		public static readonly RANK_KEY_NATION_TOWER_3:number = 1103;//快速排行榜
		public static readonly RANK_KEY_NATION_TOWER_4:number = 1104;//快速排行榜
		public static readonly RANK_KEY_NATION_TOWER_5:number = 1105;//快速排行榜
		public static readonly RANK_KEY_BOAT_RACE:number = 2055;//公会竞渡
		public static readonly RANK_KEY_BOAT_PEAK_PREVIEW:number = 2056;//巅峰竞渡入围赛
		public static readonly RANK_KEY_BOAT_PEAK:number = 2057;//巅峰竞渡
		public static readonly RANK_KEY_STAGE_COPY:number = 3001;//普通排行榜
		public static readonly RANK_KEY_CSCLAN_POWER:number = 3004;//家族平均战力排行榜
		public static readonly RANK_KEY_MOCK_PVP:number = 3005;//模拟对战排行榜
		public static readonly RANK_KEY_TD_TRIAL:number = 3006;//塔防副本
		public static readonly RANK_KEY_CSCLAN_INTERIOR_HONGBAO:number = 3007;//家族内部红包排行榜
}
export enum EBuyTimesType { 
		FAMILY_BOSS  = 1,//公会副本
		RED_CLIFF  = 2,//元素圣殿
		TEST_TOWER  = 3,//试练塔
		WORLD_BOSS = 4,//蛮族入侵
		FAMILY_WAR  = 5,//公会战
		CHALLENGE_CHAPTER  = 6,//神装纷争
		RANDOM_PVP  = 7,//跨服段位
		CROSS_LADDER  = 8,//跨服天梯
		XSWH  = 9,//跨服首领战
		SP_TEST_TOWER  = 10,//神魔塔
		SKY_TEST_TOWER  = 11,//天选塔
		NATION_TEST_TOWER  = 12,//跨服试练赛
		NATION_TEST_TOWER_1  = 13,//跨服试练赛-魏
		NATION_TEST_TOWER_2  = 14,//跨服试练赛-蜀
		NATION_TEST_TOWER_3  = 15,//跨服试练赛-吴
		NATION_TEST_TOWER_4  = 16,//跨服试练赛-神
		NATION_TEST_TOWER_5  = 17,//跨服试练赛-魔
		NATION_TEAM_XSWH  = 18,//组队跨服领主
		MOCK_PVP  = 19,//模拟对战匹配
		MOCK_PVP_SPARRING  = 20,//模拟对战切磋
		DOMINATE_PVP  = 21,//百服争霸
		DIVINE_COPY  = 22,//神临副本
		CSCLAN_SOLO = 23,//家族战
}
export enum EShopId { 
		ITEM_SHOP = 101,//道具
		BING_FA_SHOP = 402,//天赋
		HERO_SHOP = 102,//英雄
		ELIT_HERO_SHOP = 401,//精锐
		ARENA_SHOP = 302,//竞技场
		FAMILY_SHOP = 303,//公会
		HUNT_SHOP = 304,//寻宝
		HZZD_SHOP = 1601,//峡谷
		RANDOM_BOSS_SHOP = 801,//深渊
		RANDOM_SHOP = 201,//神秘集市
		HUFU_SHOP = 501,//荣耀商店
		LOTTERY_NATION_SHOP = 601,//先知商店
		YUNTAI_CONVERT_SHOP = 701,//转换商店
		FMSOLO_SHOP_1 = 901,//每周限购
		FMSOLO_SHOP_2 = 902,//终生限购
		CLANSOLO_SHOP_1 = 903,//每周限购
		CLANSOLO_SHOP_2 = 904,//终生限购
		TEST_TOWER_SHOP = 1101,//试练商店
		GOD_EQUIP_SHOP = 1201,//神装商店
		GOD_EQUIP_TREASURE_SHOP = 1202,//神装秘宝商店
		HERO_SKIN_SHOP = 1302,//皮肤商店
		MAZE_OLD_SHOP = 1501,//征途商店
		MODULAR_ACT_SHOP_MAZE = 1502,//征途商店
		MODULAR_ACT_SHOP_DICE = 1503,//摸金商店
		CROSS_LADDER_SHOP = 1701,//天梯
		CROSS_XSWH_SHOP = 1801,//首领
		CROSS_TEST_TOWER_SHOP = 2101,//跨服试练
		RANDOM_PVP_SHOP = 1901,//战区商店
		SAN_XIAO_SHOP = 2001,//三消商店
		BATTLE_TRIAL_SHOP = 2201,//组队征战
		RANDOM_DOMINATE_1 = 2301,//主宰商店（废弃）
		DOMINATE_PVP_SHOP = 2501,//百服商店
		BOAT_RACE_SHOP = 5101,//公会竞渡拍卖行
		STAGE_COPY_SHOP = 5102,//境界副本商店
		RIVER_TREASURE_SHOP = 5103,//运河大力士商店
		BOAT_PEAK_SHOP = 5104,//巅峰竞渡商店
		SUPREME_LOTTERY_SHOP = 5105,//至尊商店
		ITINERANT_SHOP = 2601,//游商商店
}
export enum EBuildingType { 
		BUILD_TYPE_BASE = 1,//城堡
		BUILD_TYPE_ALTAR = 2,//祭坛
		BUILD_TYPE_GOLD_MINE = 3,//金矿
		BUILD_TYPE_GUILD = 4,//公会
		BUILD_TYPE_BARRACKS = 5,//军营
		BUILD_TYPE_FORGE = 6,//铁匠铺
		BUILD_TYPE_CRYSTAL_MINE = 7,//晶矿
}
export enum EYueKaType { 
		TYPE_1 = 1,//畅游月卡
		TYPE_2 = 2,//豪华月卡
		TYPE_3 = 3,//尊贵月卡
		TYPE_4_KING = 4,//王者月卡
		TYPE_101_FOREVER = 101,//永久招募卡
}
