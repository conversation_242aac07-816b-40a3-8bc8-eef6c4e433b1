import {ILaya} from "ILaya";
import {<PERSON><PERSON>} from "Laya";
import {PhpStatUtil} from "../../../../game/a2reg/PhpStatUtil";
import {GlobalConfig} from "../../../../game/GlobalConfig";
import {cfg_guaji_box_time} from "../../../cfg/vo/cfg_guaji_box_time";
import {cfg_main_battle} from "../../../cfg/vo/cfg_main_battle";
import {cfg_monster} from "../../../cfg/vo/cfg_monster";
import {ConfigManager} from "../../../managers/ConfigManager";
import {DispatchManager} from "../../../managers/DispatchManager";
import {LayerManager} from "../../../managers/LayerManager";
import {Connection} from "../../../net/Connection";
import {p_item} from "../../../proto/common/p_item";
import {p_kv} from "../../../proto/common/p_kv";
import {p_kv3} from "../../../proto/common/p_kv3";
import {p_kv_64} from "../../../proto/common/p_kv_64";
import {p_simp_mission} from "../../../proto/common/p_simp_mission";
import {m_hanging_quick_tos} from "../../../proto/line/m_hanging_quick_tos";
import {m_hanging_reward_tos} from "../../../proto/line/m_hanging_reward_tos";
import {m_main_battle_box_compose_tos} from "../../../proto/line/m_main_battle_box_compose_tos";
import {m_main_battle_box_fetch_tos} from "../../../proto/line/m_main_battle_box_fetch_tos";
import {m_main_battle_box_info_toc} from "../../../proto/line/m_main_battle_box_info_toc";
import {m_main_battle_box_open_tos} from "../../../proto/line/m_main_battle_box_open_tos";
import {m_main_battle_box_rate_tos} from "../../../proto/line/m_main_battle_box_rate_tos";
import {m_main_battle_box_upgrade_tos} from "../../../proto/line/m_main_battle_box_upgrade_tos";
import {m_main_battle_fetch_pass_tos} from "../../../proto/line/m_main_battle_fetch_pass_tos";
import {m_main_battle_fight_result_toc} from "../../../proto/line/m_main_battle_fight_result_toc";
import {m_main_battle_info_toc} from "../../../proto/line/m_main_battle_info_toc";
import {m_main_battle_info_tos} from "../../../proto/line/m_main_battle_info_tos";
import {m_main_battle_mission_fetch_tos} from "../../../proto/line/m_main_battle_mission_fetch_tos";
import {m_main_battle_mission_info_tos} from "../../../proto/line/m_main_battle_mission_info_tos";
import {m_main_battle_next_tos} from "../../../proto/line/m_main_battle_next_tos";
import {m_simp_mission_fetch_tos} from "../../../proto/line/m_simp_mission_fetch_tos";
import {m_small_game_info_tos} from "../../../proto/line/m_small_game_info_tos";
import {ArrayUtil} from "../../../util/ArrayUtil";
import {ColorUtil} from "../../../util/ColorUtil";
import {GameUtil} from "../../../util/GameUtil";
import {HtmlUtil} from "../../../util/HtmlUtil";
import {TipsUtil} from "../../../util/TipsUtil";
import {DataCenter} from "../../DataCenter";
import {FightType} from "../../fight/data/FightConst";
import {FightDataCenter} from "../../fight/data/FightDataCenter";
import {GameConst} from "../../GameConst";
import {GoodsVO} from "../../goods/GoodsVO";
import {LineUpVO} from "../../lineUp/vo/LineUpVO";
import {MiscConst} from "../../misc_config/MiscConst";
import MissionConst from "../../mission/MissionConst";
import {ModuleCommand} from "../../ModuleCommand";
import {PanelEventConstants} from "../../PanelEventConstants";
import {PaymentDataCenter} from "../../payment/data/PaymentDataCenter";
import {VipTeQuanConst} from "../../payment/VipTeQuanConst";
import VipTeQuanUtil from "../../payment/VipTeQuanUtil";
import {PaymentVO} from "../../payment/vo/PaymentVO";
import {RedPointMgr} from "../../redPoint/RedPointMgr";
import {GoodsManager} from "../../test_bag/GoodsManager";
import {YueKaDataCenter} from "../../welfare/data/YueKaDataCenter";
import {ItemConst} from "../../goods/ItemConst";
import {com} from "../../../ui/layaMaxUI";
import { MatchConst } from "../../../auto/ConstAuto";
import { m_main_battle_auto_tos } from "../../../proto/line/m_main_battle_auto_tos";
import { m_main_battle_auto_toc } from "../../../proto/line/m_main_battle_auto_toc";
import { LineUpDataCenter } from "../../lineUp/data/LineUpDataCenter";
export enum BUILD_SHOW_TYPE {
    BUILD = 1,
    GUAJI = 2,
}
//数据不要用静态类型的
//可以在本模块引用，不可跨模块引用
//本模块引用的时候不要缓存instance，每次通过instance获取即可
export class GuaJiDataCenter {

    static TYPE_ID_EXP: number = 1001;//经验
    static TYPE_ID_GOLD: number = 1002;//钻石
    static TYPE_ID_SILVER: number = 1004;//金币
    static TYPE_ID_HERO_EXP: number = 1011;//英雄经验
    static TYPE_ID_TRAVEL_SCORE: number = 1013;//政务值
    static TYPE_ID_FORGE: number = 1026;//粮草

    public static PASS_REWARD_RED_POINT: number = 1;
    public static FIGHT_START_RED_POINT: number = 2;
    public static QUICK_FIGHT_RED_POINT: number = 3;
    public static TRAVEL_RED_POINT: number = 4;
    public static SUB_MISSION_RED_POINT: number = 5;
    public static CHAPTER_RED_POINT: number = 5;

    public static FIGHT_STEP_TIME: number = 1500;
    public static MULTI_FIGHT_OPREA1: number = 1;

    /**触发技能的时机 */
    static ACTION_TYPE1: number = 1; //行动前的
    static ACTION_TYPE15: number = 15; //行动后的（触发伤害时）

    static TRIG_SKILL_TYPE1: number = 1;//主动技能
    static TRIG_SKILL_TYPE2: number = 2;//追加技能
    static TRIG_SKILL_TYPE10: number = 10;//触发技能
    static TRIG_SKILL_TYPE11: number = 11;
    static TRIG_SKILL_TYPE12: number = 12;
    static TRIG_SKILL_TYPE13: number = 13;
    static TRIG_SKILL_TYPE14: number = 14;
    static TRIG_SKILL_TYPE51: number = 51;//领主反击

    static readonly GUA_JI_REDPOINT_TIME: number = 3600;

    public static RECOMMEND_LINE_UP_TYPE_POWER: number = 1;
    public static RECOMMEND_LINE_UP_TYPE_STAR: number = 2;

    public static get RECOMMEND_LINE_UP_TYPE_NAME(): string[] {
        return ["", window.iLang.L2_ZUI_DI_ZHAN_LI.il(), window.iLang.L2_ZUI_DI_XING_JI.il()]
    }

    public static readonly FIGHT_TIPS_JUMP: string = "fight_tips_jump";

    public start_time: number = 0;//开始挂机时间（时间戳）
    public max_duration: number = 0;//挂机最高持续时间（默认是12小时）
    public free_quick_times: number = 0;//当天免费快速挂机的剩余次数
    public quick_times: number = 0;//当天剩余快速挂机的次数
    public cost_gold: number = 0; //快速挂机消耗钻石数
    public rewards_times: number = 0;//累计的奖励次数（每次重新上线的时候会结算）
    public is_activity: boolean = false;
    public is_quick_fighting: boolean = false;

    public _main_battle_info: m_main_battle_info_toc; //挂机信息
    private _pass_rewards = []; //通关奖励已领取的列表
    public main_battle_mission_status: p_kv;//新手通关奖励信息
    public fight_finish_data: m_main_battle_fight_result_toc;
    public main_battle_Box_data: m_main_battle_box_info_toc;
    public show_box_red_point: boolean = true;
    private static _isInFight: boolean = false;
    static isfixcamea: boolean = false;
    static isautorotateback: boolean = true;
    static manulrotate: boolean = false;
    static isFirstPopNavigation: boolean = true;

    public OPEN_FAIL_PANEL: string = "";
    /**跳过免费次数 */
    public finish_times: number = 0;
    /**章节奖励list */
    public chapter_info_list: p_kv[];
    static cachetest: Map<string, number> = new Map<string, number>();

    static guajiSkyBgX: number = 0;
    static guajiFloorBgX: number = 0;

    public mission_status: boolean = true; //关卡是否开启 true 表开启

    public autoBattleInfo: m_main_battle_auto_toc;

    public static isNeedChangeScene: boolean = false;
    public static changeSceneType: number = 0;
    public guaji_box_rates: p_kv[] = [];
    public guaji_box_tequan_rates: p_kv[] = [];
    public guaji_box_level_rates: p_kv3[] = [];
    public small_soldier_game_fetch_list: number[] = [];
    public guaji_box_hanging_rates: Map<number, p_kv_64> = new Map<number, p_kv_64>(); // 宝箱概率


    public main_battle_show_time: number = 0;//3min
    public show_main_battle: boolean = true;

    public gainRewards: p_item[] = []; //实际挂机奖励
    public showFixedItems: p_item[] = []; //显示的每分钟奖励

    static _instance: GuaJiDataCenter = null;

    private autoFight: boolean = false;
    
    public guaJiDialogShowType: number = 1;
    
    static get instance(): GuaJiDataCenter {
        if (GuaJiDataCenter._instance == null) {
            GuaJiDataCenter._instance = new GuaJiDataCenter();
        }
        return GuaJiDataCenter._instance;
    }

    reset(): void {
        GuaJiDataCenter._instance = null;
    }

    private static _isInGuaJiDialog: boolean = false;
    public static set isInGuaJiDialog(value: boolean) {
        GuaJiDataCenter._isInGuaJiDialog = value;
        if (value == false) {
            DispatchManager.dispatchEvent(ModuleCommand.CLOSED_GUA_JI);
        }
    }

    public static get isInGuaJiDialog(): boolean {
        return GuaJiDataCenter._isInGuaJiDialog && GuaJiDataCenter.instance.guaJiDialogShowType == BUILD_SHOW_TYPE.GUAJI;
    }

    private static _isManuChange: boolean = false;
    public static get isManuChange(): boolean {
        return GuaJiDataCenter._isManuChange;
    }

    public static set isManuChange(val: boolean) {
        GuaJiDataCenter._isManuChange = val;
    }

    private static _firstEnterDialog: boolean = false;
    public static get firstEnterDialog(): boolean {
        return GuaJiDataCenter._firstEnterDialog;
    }
    public static set firstEnterDialog(isEnter: boolean) {
        if (!GuaJiDataCenter._firstEnterDialog && isEnter) {
            GuaJiDataCenter._firstEnterDialog = isEnter;
            //只有在 没有剩余自动挑战次数，并且当前没有进行主线战斗时，才会在第一次进入挂机界面，发送结束自动推关请求
            if (GuaJiDataCenter.instance.laveAutoPassNum <= 0 && !GuaJiDataCenter.instance.isRunningMainBattle()) {
                GuaJiDataCenter.instance.m_main_battle_auto_tos(false);
            }
        }
    }

    public isRunningMainBattle(): boolean {
        return LayerManager.runningFight(MatchConst.MATCH_TYPE_MAIN_BATTLE)
    }

    public isRunningMasterCardBattle(): boolean {
        return LayerManager.runningFight(MatchConst.MATCH_TYPE_MASTER_CARD)
    }

    public isCanFightNextPass(isDo: boolean = false, isAuto: boolean = false): boolean {
        let cfg: cfg_main_battle = GuaJiDataCenter.instance.main_battle;
        let isMaxFight = GuaJiDataCenter.instance.checkIsMaxFightPass();
        if (cfg.need_level > DataCenter.myLevel) {
            if (isDo) {
                if (GuaJiDataCenter.instance.quick_times > 0) {
                    DispatchManager.dispatchEvent(ModuleCommand.OPEN_GUA_JI_QUICK_FIGHT_DIALOG);
                } else if (!YueKaDataCenter.instance.isOpenAllQuickFightYueKa()) {
                    TipsUtil.showDialog(this, window.iLang.L2_JI_HUO_YUE_KA_HUO_QU_GENG_DUO_KUAI_SU_GUA_JI_CI_SHU.il(), window.iLang.L2_TI_ch11_SHI.il(), () => {
                        GameUtil.gotoSystemPanelById(PanelEventConstants.PAYMENT, PaymentVO.KEY_WELFARE_CARD);
                    }, {okName: window.iLang.L2_QIAN_WANG_JI_HUO.il()});
                } else {
                    TipsUtil.showTips(window.iLang.L2_DENG_JI_BU_ZU_ch31_QING_TI_SHENG_DENG_JI.il());
                }
            }
            return false;
        } else if (!isAuto && GuaJiDataCenter.instance.isNeedNewChapter()) {
            if (isDo) {
                DispatchManager.dispatchEvent(ModuleCommand.OPEN_GUA_JI_WROLD_MAP_DIALOG);
            }
            return false;
        } else if (isMaxFight) {
            TipsUtil.showTips(window.iLang.L2_YI_DA_DANG_QIAN_ZUI_DA_GUAN_KA_ch31_JING_QING_QI_DAI_XIN_GUAN_QIA_TUI_CHU.il());
            return false;
        }
        return true;
    }

    public onFightMainBattle({
                                 isInBackFight = false,
                                 isAuto = false,
                                 checkReback = true,
                                 isLineUp = true,
                                 isDo = true,
                                 isCheckTravel = true,
                                 lineUpVo = null,
                             }: {
        isInBackFight?: boolean,
        isAuto?: boolean,
        checkReback?: boolean,
        isLineUp?: boolean,
        isDo?: boolean,
        isCheckTravel?: boolean,
        lineUpVo?: LineUpVO,
    } = {}): void {
        if (this.isCanFightNextPass(isDo, isAuto)) {
            let fight_type = isInBackFight ? FightType.BACK_FIGHT : FightType.MAKE_WAR;
            let cfg: cfg_main_battle = ConfigManager.cfg_main_battleCache.get(GuaJiDataCenter.instance.curGuajiPass);
            if (isAuto || !isCheckTravel || !this.checkTravelScore(0, this, this._fightMainBattle, [cfg, fight_type, checkReback, isLineUp])) {
                this._fightMainBattle(cfg, fight_type, checkReback, isLineUp, lineUpVo);
            }
        } else {
            GuaJiDataCenter.instance.m_main_battle_auto_tos(false);
            GuaJiDataCenter.instance.laveAutoPassNum = 0;
        }
    }

    private _fightMainBattle(cfg: cfg_main_battle, fight_type: FightType, checkReback: boolean, isLineUp: boolean, lineUpVo: LineUpVO): void {
        let isshowSkip: boolean = GuaJiDataCenter.instance.curPass >= MiscConst.main_battle_quick_fight_pass;
        FightDataCenter.instance.m_fight_start_tos(MatchConst.MATCH_TYPE_MAIN_BATTLE, cfg.id, {
            scenename: cfg.scenename,
            targetname: cfg.name + window.iLang.L2_DI_P0_GUAN.il([cfg.id]),
            finishevent: ModuleCommand.OPEN_FIGHT_RESULT_DIALOG,
            fightype: fight_type,
            is_show_skip: isshowSkip,
            checkReback: checkReback,
            isLineUp: isLineUp,
            lineUpVo: lineUpVo,
        });
    }

    public set main_battle_info(info: m_main_battle_info_toc) {
        GameUtil.endTimeLog("login");
        PhpStatUtil.act2Reg("finish_login");

        this._main_battle_info = info;
        let isRed: boolean = false;
        if (info) {
            //排序
            info.pass_status.sort(function (item1: p_kv, item2: p_kv): number {
                if (item1.val == 1 && item2.val != 1) {
                    return -1;
                } else if (item1.val != 1 && item2.val == 1) {
                    return 1;
                } else if (item1.val == 2 && item2.val != 2) {
                    return 1;
                } else if (item1.val != 2 && item2.val == 2) {
                    return -1;
                } else {
                    return item1.key - item2.key;
                }
            });
            //判断红点
            for (let item of info.pass_status) {
                if (item.val == 1) {
                    isRed = true;
                    break;
                }
            }
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.PASS_REWARD, 0, isRed ? 1 : 0);
    }

    public get main_battle_info(): m_main_battle_info_toc {
        return this._main_battle_info;
    }


    /** 初始化快速挂机的宝箱概率 */
    public set_guaji_box_hanging_rates(rates: p_kv_64[]) {
        rates.forEach((rate) => {
            this.guaji_box_hanging_rates.set(rate.key, rate);
        })
    }

    /** 挂机时间 */
    public get guaJiTime(): number {
        let time: number = GuaJiDataCenter.instance.rewards_times * 60 + (GlobalConfig.serverTime - GuaJiDataCenter.instance.start_time);
        return time > this.max_duration ? this.max_duration : time;
    }

    /**是否正在进行自动推关 */
    public get isAutoPass(): boolean {
        if (this.autoBattleInfo) {
            return this.autoBattleInfo.is_open;
        }
        return this.autoFight;
    }

    /**剩余自动推关次数 */
    public set laveAutoPassNum(num: number) {
        if (this.autoBattleInfo) {
            this.autoBattleInfo.lave_auto_pass = num;
        }
        this.autoFight = num > 0;
    }

    /**剩余自动推关次数 */
    public get laveAutoPassNum(): number {
        if (this.autoBattleInfo) {
            return this.autoBattleInfo.lave_auto_pass;
        }
        return this.isAutoPass?1:0;
    }

    /**当前宝箱等级 */
    public get boxLevel(): number {
        if (this.main_battle_Box_data) {
            return this.main_battle_Box_data.level || 0;
        }
        return 0;
    }
    
    public get lineUpHeroNum(): number {
        let lineupList: number[] = LineUpDataCenter.instance.getLineUpListByType(MatchConst.MATCH_TYPE_MAIN_BATTLE);
        return lineupList.length;
    }

    /**检测是否可以挑战关卡 */
    public checkIsCanFightPass(pass: number): boolean {
        let cfg: cfg_main_battle = ConfigManager.cfg_main_battleCache.get(pass);
        if (cfg) {
            return DataCenter.myLevel >= cfg.need_level;
        }
        return false;
    }


    /**检测是否最大关卡 */
    public checkIsMaxFightPass(): boolean {
        let max_pass = ConfigManager.cfg_main_battleCache.get_all().length;
        return (GuaJiDataCenter.instance.curPass + 1) == max_pass;
    }

    /**
     * 检查政务值是否会超出限制
     * @param guaJiTime 挂机时间(秒数)
     * @param confirmFun
     */
    public checkTravelScore(guaJiTime: number, caller: any, confirmFun: Function = null, args: any[] = []): boolean {
        let cfgVO: cfg_main_battle = ConfigManager.cfg_main_battleCache.get(this.curGuajiPass);
        if (cfgVO) {
            let fetchScore: number = Math.floor(guaJiTime / 60) * cfgVO.travel;
            if (fetchScore + DataCenter.myTravelScore > DataCenter.maxTravelScore) {
                let txt: string = window.iLang.L2_DANG_QIAN_YI_YOU.il() + HtmlUtil.font(DataCenter.myTravelScore + "/" + DataCenter.maxTravelScore, ColorUtil.GREEN) +
                    window.iLang.L2_TAN_SUO_DIAN_ch31_RECEIVE_HOU_CHAO_CHU_SHANG_XIAN_BU_FEN_JIANG_SUN_SHI_ch31_SHI_FOU.il();
                //弹窗提示政务值溢出
                TipsUtil.showDialog(caller, txt, window.iLang.L2_TI_SHI.il(), confirmFun, {
                    args: args,
                });
                return true;
            }
        }
        return false;
    }

    public onQuickFight(delay: number = 0, isShowTips: boolean = true, caller: any = null, callBack: Function = null): void {
        if (this.is_quick_fighting) {
            TipsUtil.showTips(window.iLang.L2_KUAI_SU_GUA_JI_ZHONG.il());
            return;
        }

        if (this.quick_times <= 0) {
            this.m_hanging_quick_tos();
            return;
        }

        if (isShowTips) {
            let b: boolean = this.checkTravelScore(60 * 60 * 2, this, function () {
                this._quickFightTos(delay, caller, callBack);
            });
            if (b) {
                return;
            }
        }

        this._quickFightTos(delay, caller, callBack);
    }

    private _quickFightTos(delay: number = 0, caller: any, callBack: Function): void {
        if (delay > 0) {
            this.is_quick_fighting = true;
            ILaya.timer.once(delay, this, this.m_hanging_quick_tos);
            if (callBack) {
                callBack.call(caller);
            }
        } else {
            this.m_hanging_quick_tos();
        }
    }

    //-------------协议发送 start -------------
    m_hanging_reward_tos(op_type: number = 2): void {
        let tos: m_hanging_reward_tos = new m_hanging_reward_tos();
        tos.op_type = op_type;
        Connection.instance.sendMessage(tos);
    }

    m_hanging_quick_tos(): void {
        let tos: m_hanging_quick_tos = new m_hanging_quick_tos();
        Connection.instance.sendMessage(tos);
    }

    m_main_battle_info_tos(): void {
        let tos: m_main_battle_info_tos = new m_main_battle_info_tos();
        Connection.instance.sendMessage(tos);
    }

    m_main_battle_next_tos(): void {
        let tos: m_main_battle_next_tos = new m_main_battle_next_tos();
        Connection.instance.sendMessage(tos);
    }

    m_main_battle_fetch_pass_tos(pass: number, isShow: boolean = true): void {
        let tos: m_main_battle_fetch_pass_tos = new m_main_battle_fetch_pass_tos();
        tos.pass = pass;
        tos.is_show_reward = isShow;
        Connection.instance.sendMessage(tos);
    }


    /**领取新手通关奖励 */
    m_main_battle_mission_fetch_tos(mission_id: number): void {
        let tos: m_main_battle_mission_fetch_tos = new m_main_battle_mission_fetch_tos();
        tos.id = mission_id;
        Connection.instance.sendMessage(tos);
    }

    m_main_battle_mission_info_tos(): void {
        let tos: m_main_battle_mission_info_tos = new m_main_battle_mission_info_tos();
        Connection.instance.sendMessage(tos);
    }

    m_main_battle_auto_tos(is_open: boolean): void {
        let tos: m_main_battle_auto_tos = new m_main_battle_auto_tos();
        tos.is_open = is_open;
        Connection.instance.sendMessage(tos);

        this.autoFight = is_open;
        DispatchManager.dispatchEvent(ModuleCommand.UPDATE_MAIN_BATTLE_AUTO);
    }

    /**1=开始自动，2=停止自动，3=领奖，4=达到最大关卡了 */
    // m_main_battle_auto_pass_tos(op_type: number): void {
    //     let tos: m_main_battle_auto_pass_tos = new m_main_battle_auto_pass_tos();
    //     tos.op_type = op_type;
    //     Connection.instance.sendMessage(tos);
    // }

    // m_main_battle_chapter_fetch_tos(id: number): void {
    //     let tos: m_main_battle_chapter_fetch_tos = new m_main_battle_chapter_fetch_tos();
    //     tos.id = id;
    //     Connection.instance.sendMessage(tos);
    // }

    //----支线任务   start----

    /**
     * 支线任务
     * @param op_type 发送类型 1领奖
     * @param id 任务id
     */
    m_simp_mission_fetch_tos(id: number): void {
        let tos: m_simp_mission_fetch_tos = new m_simp_mission_fetch_tos();
        tos.mission_type = MissionConst.MISSION_TYPE_SUB;
        tos.id = id;
        Connection.instance.sendMessage(tos);
    }

    m_main_battle_box_fetch_tos(type: number): void {
        let tos: m_main_battle_box_fetch_tos = new m_main_battle_box_fetch_tos();
        tos.type = type;
        Connection.instance.sendMessage(tos);
    }

    m_main_battle_box_open_tos(box_type: number, num: number): void {
        let tos: m_main_battle_box_open_tos = new m_main_battle_box_open_tos();
        tos.id = box_type;
        tos.num = num;
        Connection.instance.sendMessage(tos);
    }

    m_main_battle_box_upgrade_tos(): void {
        let tos = new m_main_battle_box_upgrade_tos();
        Connection.instance.sendMessage(tos);
    }

    m_main_battle_box_rate_tos(): void {
        let tos = new m_main_battle_box_rate_tos();
        Connection.instance.sendMessage(tos);
    }

    m_small_game_info_tos(id: number, op_type: number): void {
        let tos: m_small_game_info_tos = new m_small_game_info_tos();
        tos.id = id;
        tos.op_type = op_type;//0挑战成功
        Connection.instance.sendMessage(tos);
    }

    //----支线任务   end----

    //-------------协议发送 end ---------------

    /**
     * 获取当前关卡所在的章节有多少关
     * @param num 当前关卡
     */
    public static getGroupMaxProgress(num: number): number {
        let cfg: cfg_main_battle = ConfigManager.cfg_main_battleCache.get(num);
        if (cfg) {
            let list: any[] = ConfigManager.cfg_main_battle_groupCache.get(cfg.group);
            if (list) {
                return list.length;
            }
        }
        return 0;
    }

    /**
     * 获取当前关卡所在的章节进度
     */
    public static getGroupCurProgress(): number {
        let num: number = GuaJiDataCenter.instance.curGuajiPass;
        let result: number = 0;
        let cfg: cfg_main_battle = ConfigManager.cfg_main_battleCache.get(num);
        let list: cfg_main_battle[] = ConfigManager.cfg_main_battle_groupCache.get(cfg.group);
        if (list) {
            list.forEach(cfg => {
                if (num > cfg.id) {
                    result++;
                }
            });
        }
        result = GuaJiDataCenter.instance.isNeedNewChapter() ? result + 1 : result;
        return result;
    }

    public getShowPassRewardInfo(): p_kv {
        if (this.main_battle_info) {
            let len: number = this.main_battle_info.pass_status.length;
            let info: p_kv = null;
            for (let i: number = 0; i < len; i++) {
                info = this.main_battle_info.pass_status[i];
                if (info && info.val != 2) {
                    return info;
                }
            }
        }
        return null;
    }
    
     private makePassKv(id: number, val: number) {
        let kv = new p_kv();
        kv.key = id;
        kv.val = val;
        return kv;
    }

    public getPassRewardList(): Array<p_kv> {
        return this._pass_rewards;

        // let result: Array<p_kv>;
        // if (this.main_battle_info) {
        //     // let len: number = this.main_battle_info.pass_status.length;
        //     // let info: p_kv = null;
        //     // result = [];
        //     // for (let i: number = 0; i < len; i++) {
        //     //     info = this.main_battle_info.pass_status[i];
        //     //     if (info && info.val != 2) {
        //     //         result.push(info);
        //     //     }
        //     // }
        //     return this.main_battle_info.pass_status;
        // }
        // // return result;
        // return [];
    }

    /**获取指定关卡的通关奖励 */
    public getPassReward(round: number): p_kv {
        if (this.main_battle_info) {
            for (let info of this.main_battle_info.pass_status) {
                if (info.key == round) {
                    return info;
                }
            }
        }
        return null;
    }

    /**当前所在关卡 */
    public get curPass(): number {
        if (this.main_battle_info) {
            return this.main_battle_info.cur_pass;
        }
        return 1;
    }

    /**当前挂机显示关卡 */
    public get curGuajiPass(): number {
        if (this.main_battle_info) {
            if (this.main_battle_info.need_new_chapter) {
                return this.main_battle_info.cur_pass;
            } else {
                return this.main_battle_info.cur_pass + 1;
            }
        }
        return 1;
    }

    /**当前已过关了的章节 */
    public get curPassChapter(): number {
        let round: number = GuaJiDataCenter.instance.curPass + 1;
        let cfgBattle: cfg_main_battle = ConfigManager.cfg_main_battleCache.get(round);
        /**通关章节 */
        let chapterFinish: number = cfgBattle.group - 1;
        return chapterFinish;
    }

    /**当前所在章节 */
    public get curChapter(): number {
        if (this.main_battle_info) {
            let cfg: cfg_main_battle = ConfigManager.cfg_main_battleCache.get(this.curGuajiPass);
            return cfg.group;
        }
        return 1;
    }

    public get needLevel(): number {
        if (this.main_battle_info) {
            let cfg: cfg_main_battle = ConfigManager.cfg_main_battleCache.get(this.curGuajiPass);
            return cfg.need_level;
        }
        return 1;
    }

    public get main_battle(): cfg_main_battle {
        return ConfigManager.cfg_main_battleCache.get(this.curGuajiPass);
    }

    public getMinLevelByChapter(chapter: number): number {
        let list: cfg_main_battle[] = ConfigManager.cfg_main_battle_groupCache.get(chapter);
        let minLevel: number = 9999999999;
        list.forEach(cfg => {
            if (minLevel > cfg.id) {
                minLevel = cfg.id;
            }
        });
        return minLevel;
    }

    public getMaxLevelByChapter(chapter: number): number {
        let list: cfg_main_battle[] = ConfigManager.cfg_main_battle_groupCache.get(chapter);
        let minLevel: number = -1;
        list.forEach(cfg => {
            if (minLevel < cfg.id) {
                minLevel = cfg.id;
            }
        });
        return minLevel;
    }

    /**是否需要前往下一章节 */
    public isNeedNewChapter(): boolean {
        if (this.main_battle_info) {
            return this.main_battle_info.need_new_chapter;
        }
        return false;
    }

    /**
     * 判断"快速挂机"的按钮红点
     */
    public loginRedPoint: boolean = true;

    public checkRedQuickFight(): boolean {
        //let bool: boolean = this.free_quick_times > 0 || (this.quick_times > 0 && DataCenter.myGold >= this.cost_gold);
        let count: number = 0;
        let cfg: cfg_main_battle = GuaJiDataCenter.instance.main_battle;
        if (!cfg || DataCenter.myLevel >= cfg.need_level) {
            if (this.free_quick_times > 0) {
                count++;
            } else if (this.loginRedPoint == true && this.quick_times > 0 && DataCenter.myGold >= this.cost_gold) {
                count++;
            }
        }
        if (this.guaJiTime > GuaJiDataCenter.GUA_JI_REDPOINT_TIME) {   //挂机1小时以上提示红点
            count++;
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.GUA_JI, GuaJiDataCenter.QUICK_FIGHT_RED_POINT, count);
        return count > 0;
    }

    /**
     * 判断"攻击城池"的按钮红点
     */
    public checkRedFightStart(): boolean {
        let count: number = 0;
        if (GuaJiDataCenter.instance.mission_status == true) {
            count = DataCenter.myLevel >= this.needLevel ? 1 : 0;
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.GUA_JI, GuaJiDataCenter.FIGHT_START_RED_POINT, count);
        return count > 0;
    }

    /**自动推图领取奖励红点 */
    // public checkAutoPassRedPoint(): boolean {
    //     let count: number = this.auto_pass_status == 2 ? 1 : 0;
    //     RedPointMgr.ins.SetRedData(PanelEventConstants.GUAJI_AUTO_PASS, 0, count);
    //     return count > 0;
    // }

    /**挂机战斗 */
    public isGuaJiRedPoint(): void {
        this.checkRedQuickFight();
        // this.checkRedFightStart();
        this.checkPassRewardRedPoint();
        // this.isPassRewardRedPoint();
        // this.checkAutoPassRedPoint();
    }

    getAllMonsterUrls(result: string[]) {
        // var cfg_base: cfg_monster;
        // var cfg_show: cfg_client_show;
        // var tempclienteffect: cfg_client_effect;
        // var temp: any;
        // var min: number = GuaJiDataCenter.instance.getMinLevelByChapter(GuaJiDataCenter.instance.curChapter);
        // var max: number = GuaJiDataCenter.instance.getMaxLevelByChapter(GuaJiDataCenter.instance.curChapter);
        // for (var i: number = min; i < max; i++) {
        //     let cfg: cfg_main_battle = ConfigManager.cfg_main_battleCache.get(i);
        //     if (cfg) {
        //         let moninfo: string[] = cfg.guaji_monsters.split("|");
        //         for (var j = 0; j < moninfo.length; j++) {
        //             var mono = moninfo[j].split(",");
        //
        //             var mid: number = Number(mono[0]);
        //
        //             if (mid > 0) {
        //                 cfg_base = ConfigManager.cfg_monsterCache.get(mid);
        //
        //                 if (!AnimationManager.cacheskillid[cfg_base.afk_skill]) {
        //                     AnimationManager.cacheskillid[cfg_base.afk_skill] = true;
        //                     cfg_show = ConfigManager.cfg_client_showCache.get(cfg_base.afk_skill);
        //
        //                     if (cfg_show) {
        //                         temp = cfg_show.effects;
        //
        //                         for (var k = 0; k < temp.length; k++) {
        //                             tempclienteffect = ConfigManager.cfg_client_effectCache.get(temp[k]);
        //                             if (tempclienteffect.is2d == 1) {
        //                                 continue;
        //                             }
        //                             result.push(UrlConfig.EFFECT_PATH_3D + tempclienteffect.name + UrlConfig.LH_EXT);
        //                         }
        //                     }
        //
        //                 }
        //
        //             }
        //         }
        //
        //     }
        // }
    }

    getAllMonsterSkins(result: string[]) {
        var cfg_base: cfg_monster;
        var min: number = GuaJiDataCenter.instance.getMinLevelByChapter(GuaJiDataCenter.instance.curChapter);
        var max: number = GuaJiDataCenter.instance.getMaxLevelByChapter(GuaJiDataCenter.instance.curChapter);
        for (var i: number = min; i < max; i++) {
            let cfg: cfg_main_battle = ConfigManager.cfg_main_battleCache.get(i);
            if (cfg) {
                let moninfo: string[] = cfg.guaji_monsters.split("|");
                for (var j = 0; j < moninfo.length; j++) {
                    var mono = moninfo[j].split(",");
                    var mid: number = Number(mono[0]);
                    if (mid > 0) {
                        cfg_base = ConfigManager.cfg_monsterCache.get(mid);
                        result.push(cfg_base.skin);
                    }

                }
            }

        }
    }

    getNextMonsterSkins(result: string[]) {
        var cfg_base: cfg_monster;
        let cfg: cfg_main_battle = ConfigManager.cfg_main_battleCache.get(GuaJiDataCenter.instance.curGuajiPass);
        var currentlevel: number = GuaJiDataCenter.instance.getMinLevelByChapter(cfg.group);
        cfg = ConfigManager.cfg_main_battleCache.get(currentlevel);
        if (!cfg) {
            cfg = ConfigManager.cfg_main_battleCache.get(GuaJiDataCenter.instance.curGuajiPass);
            currentlevel = GuaJiDataCenter.instance.getMinLevelByChapter(cfg.group);
            cfg = ConfigManager.cfg_main_battleCache.get(currentlevel);
        } else {
            if (cfg.group != GuaJiDataCenter.instance.curChapter) {
                currentlevel = GuaJiDataCenter.instance.getMinLevelByChapter(GuaJiDataCenter.instance.curChapter);
                cfg = ConfigManager.cfg_main_battleCache.get(currentlevel);
            }
        }
        let moninfo: string[] = cfg.guaji_monsters.split("|");
        for (var j = 0; j < moninfo.length; j++) {
            var mono = moninfo[j].split(",");
            var mid: number = Number(mono[0]);
            if (mid > 0) {
                cfg_base = ConfigManager.cfg_monsterCache.get(mid);
                result.push(cfg_base.skin);
            }

        }

    }


    /**
     * 是否占有；如果占有则有旗子
     */
    public isOwn(cityId: number): boolean {
        // let cfg: cfg_world_city = ConfigManager.cfg_world_cityCache.get(cityId);
        // if (GuaJiDataCenter.instance.curChapter > cfg.group && GuaJiDataCenter.instance.curChapter != cfg.id) {
        //     return true;
        // }
        return false;
    }

    /**通过奖励红点 */
    public isPassRewardRedPoint(): boolean {
        //通关奖励
        let count: number = 0;
        let info: p_kv = this.getShowPassRewardInfo();
        if (info && info.val == 1) {
            count = 1;
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.GUA_JI, GuaJiDataCenter.PASS_REWARD_RED_POINT, count);
        return count > 0;
    }

    public isMissionReward2RedPoint(): boolean {
        let red_count = 0;
        let stateInfo = GuaJiDataCenter.instance.main_battle_mission_status;
        ConfigManager.cfg_main_battle_missionCache.get_all().forEach(cfg => {
            let status = cfg.id < stateInfo.key ? 2 : cfg.id == stateInfo.key ? stateInfo.val : 0;
            if (status == 1) {
                red_count = 1;
            }
        });
        RedPointMgr.ins.SetRedData(PanelEventConstants.GUA_JI, GuaJiDataCenter.PASS_REWARD_RED_POINT, red_count);
        RedPointMgr.ins.SetRedData(PanelEventConstants.PASS_REWARD, 1, red_count);
        return red_count > 0;
    }

    /**
     * 获取挂机宝箱数量
     */
    public getGuajiBoxCount(): number {
        let boxCount = 0;
        ConfigManager.cfg_main_battle_boxCache.get_all().forEach(cfg => {
            boxCount += GoodsManager.instance.GetGoodsNumByTypeId(cfg.item_id);
        });
        return boxCount;
    }

    public static parseGuajiReward(gains: p_item[]): GoodsVO[] {
        let res: GoodsVO[] = [];
        gains.forEach(info => {
            res.push(GoodsVO.GetVoByTypeId(info.type_id, info.num));
        });
        // if (msg.role_exp != 0) {
        //     res.push(GoodsVO.GetVoByTypeId(GuaJiDataCenter.TYPE_ID_EXP, msg.role_exp));
        // }
        // if (msg.hero_exp != 0) {
        //     res.push(GoodsVO.GetVoByTypeId(GuaJiDataCenter.TYPE_ID_HERO_EXP, msg.hero_exp));
        // }
        // if (msg.silver != 0) {
        //     res.push(GoodsVO.GetVoByTypeId(GuaJiDataCenter.TYPE_ID_SILVER, msg.silver));
        // }
        // if (msg.travel != 0) {
        //     res.push(GoodsVO.GetVoByTypeId(GuaJiDataCenter.TYPE_ID_TRAVEL_SCORE, msg.travel));
        // }
        // if (msg.forage != 0) {
        //     res.push(GoodsVO.GetVoByTypeId(GuaJiDataCenter.TYPE_ID_FORGE, msg.forage));
        // }

        return res;
    }

    /**
     *
     * @param guaji_time 挂机时间 （秒）
     */
    public static getGuajiBoxEffName(guaji_time: number): string {
        let res: string;
        let cfg = this.getCfgGuajiBoxTime(guaji_time);
        if(cfg){
            res = cfg.eff_name;
        }
        return res;
    }

    public static getCfgGuajiBoxTime(guaji_time: number): cfg_guaji_box_time {
        let res: cfg_guaji_box_time;
        let list: cfg_guaji_box_time[] = ConfigManager.cfg_guaji_box_timeCache.get_all();
        if (!list) return res;
        guaji_time = guaji_time / 60;
        let cfg: cfg_guaji_box_time;
        for (let i: number = 0; i < list.length; i++) {
            cfg = list[i];
            if (guaji_time >= cfg.min_time && guaji_time < cfg.max_time) {
                res = cfg;
                break;
            }
        }
        return res;
    }

    /**是否可跳过 */
    public static checkPlotSkip(match_type: number) {
        if (match_type == MatchConst.MATCH_TYPE_MAIN_BATTLE) {
            let cfg: cfg_main_battle = ConfigManager.cfg_main_battleCache.get(GuaJiDataCenter.instance.curGuajiPass);
            if (cfg.is_skip == 1)
                return false;
        }
        return true;
    }

    public static isShowFreePass(): boolean {
        if (VipTeQuanUtil.isOpenPrivilege(VipTeQuanConst.FINISH_FIGHT))
            return false;
        if (DataCenter.myLevel >= MiscConst.finish_fight_lv_limit || DataCenter.myLevel < MiscConst.free_finish_fight_lv)
            return false;
        return true;
    }

    //----支线任务   start----


    private _submissionmap: Map<number, p_simp_mission>;

    public get subMissionMap(): Map<number, p_simp_mission> {
        return this._submissionmap;
    }

    public SetSubMissionMap(dataList: p_simp_mission[]) {
        this._submissionmap = ArrayUtil.getMapByArray(dataList, "id");
        this.checkSubMissionRedPoint();
    }

    public UpdateSubMissionMap(data: p_simp_mission) {
        this._submissionmap.set(data.id, data);
        this.checkSubMissionRedPoint();
    }

    private checkSubMissionRedPoint() {
        let count: number = 0;
        if (this.subMissionMap) {
            let values = ArrayUtil.getArrByMapSortKey(this.subMissionMap);
            for (let data of values) {
                if (data.status == 1) {
                    count++;
                }
            }
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.GUA_JI, GuaJiDataCenter.SUB_MISSION_RED_POINT, count);
    }

    //----支线任务   end----


    public checkGuajiBoxTeQuanRedPoint(): boolean {
        let vo: PaymentVO = PaymentDataCenter.instance.getPaymentVoByID(PaymentVO.KEY_GUAJI_BOX_CODE);
        if (vo && GuaJiDataCenter.instance.boxLevel >= 10) {
            for (let i = 0; i < vo.list.length; i++) {
                let item_vo = vo.list[i];
                let cfg = ConfigManager.cfg_main_battle_box_tequanCache.get(item_vo.item_id);
                if (item_vo.isCanBuy && item_vo.price_type != ItemConst.COST_RMB && DataCenter.myGold >= GameUtil.gold(item_vo.price) && cfg && GuaJiDataCenter.instance.boxLevel >= cfg.level) {
                    return true;
                }
            }
        } else {
            return false;
        }
    }

    public startShowMainBattleTip(): void {
        if (GuaJiDataCenter.instance.main_battle_show_time <= 0) {
            Laya.timer.clear(GuaJiDataCenter.instance, GuaJiDataCenter.instance.updateLoop);
            GuaJiDataCenter.instance.main_battle_show_time = MiscConst.main_battle_reward_tips_show_time + DataCenter.serverTimeSeconds;
            GuaJiDataCenter.instance.updateLoop();
            Laya.timer.loop(1000, GuaJiDataCenter.instance, GuaJiDataCenter.instance.updateLoop);
        }
    }

    private updateLoop(): void {
        let t = GuaJiDataCenter.instance.main_battle_show_time - DataCenter.serverTimeSeconds;
        if (t > 0 && t != MiscConst.main_battle_reward_tips_show_time) {
            GuaJiDataCenter.instance.show_main_battle = false;
        } else {
            GuaJiDataCenter.instance.show_main_battle = true;
        }
        if (t <= 0) {
            Laya.timer.clear(GuaJiDataCenter.instance, GuaJiDataCenter.instance.updateLoop);
            GuaJiDataCenter.instance.main_battle_show_time = 0;
        }
    }

    public m_main_battle_box_compose_tos(targetId: number, num: number) {
        let tos = new m_main_battle_box_compose_tos;
        tos.target_type_id = targetId;
        tos.num = num;
        Connection.instance.sendMessage(tos);
    }


    public updatePassRewards(pass_rewards: p_kv[]) {
        let isRed: boolean = false;
        let allPassList = ConfigManager.cfg_main_battle_fetchCache.get_all();

        //已通关的关卡ID列表
        let fetchPassIdList = [];
        let newPassRewards = [];
        for (const passItem of pass_rewards) {
            fetchPassIdList.push(passItem.key);
            newPassRewards.push(passItem);
        }

        for (const item of allPassList) {
            if (fetchPassIdList.indexOf(item.pass_id) === -1) {
                if (this.curPass >= item.pass_id) {
                    isRed = true;
                    //加上可领奖的
                    newPassRewards.push(this.makePassKv(item.pass_id, 1));
                } else {
                    //加上未通关的
                    newPassRewards.push(this.makePassKv(item.pass_id, 0));
                }
            }
        }
        this._pass_rewards = newPassRewards;

        this.checkPassRewardRedPoint();

        //排序
        this._pass_rewards.sort(function (item1: p_kv, item2: p_kv): number {
            if (item1.val == 1 && item2.val != 1) {
                return -1;
            } else if (item1.val != 1 && item2.val == 1) {
                return 1;
            } else if (item1.val == 2 && item2.val != 2) {
                return 1;
            } else if (item1.val != 2 && item2.val == 2) {
                return -1;
            } else {
                return item1.key - item2.key;
            }
        });

        RedPointMgr.ins.SetRedData(PanelEventConstants.PASS_REWARD, 0, isRed ? 1 : 0);
    }

    //通关奖励的红点
    private checkPassRewardRedPoint() {
        //通关奖励
        let count: number = 0;
        for (const item of this._pass_rewards) {
            if (item.val == 1) {
                count = 1;
                break;
            }
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.GUA_JI, GuaJiDataCenter.PASS_REWARD_RED_POINT, count);
        return count > 0;
    }
}