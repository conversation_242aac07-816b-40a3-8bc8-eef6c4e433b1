import { ILaya } from "ILaya";
import { Event } from "laya/events/Event";
import { Dialog } from "laya/ui/Dialog";
import { UIConfig } from "UIConfig";
import { MatchConst } from "../../../auto/ConstAuto";
import { DispatchManager } from "../../../managers/DispatchManager";
import { LayerManager } from "../../../managers/LayerManager";
import { p_item } from "../../../proto/common/p_item";
import { m_hanging_info_toc } from "../../../proto/line/m_hanging_info_toc";
import { m_hanging_quick_toc } from "../../../proto/line/m_hanging_quick_toc";
import { m_hanging_reward_toc } from "../../../proto/line/m_hanging_reward_toc";
import { m_main_battle_auto_end_toc } from "../../../proto/line/m_main_battle_auto_end_toc";
import { m_main_battle_auto_toc } from "../../../proto/line/m_main_battle_auto_toc";
import { m_main_battle_box_info_toc } from "../../../proto/line/m_main_battle_box_info_toc";
import { m_main_battle_box_open_toc } from "../../../proto/line/m_main_battle_box_open_toc";
import { m_main_battle_box_rate_toc } from "../../../proto/line/m_main_battle_box_rate_toc";
import { m_main_battle_box_upgrade_toc } from "../../../proto/line/m_main_battle_box_upgrade_toc";
import { m_main_battle_info_toc } from "../../../proto/line/m_main_battle_info_toc";
import { m_main_battle_mission_fetch_toc } from "../../../proto/line/m_main_battle_mission_fetch_toc";
import { m_main_battle_mission_info_toc } from "../../../proto/line/m_main_battle_mission_info_toc";
import { m_main_battle_missions_toc } from "../../../proto/line/m_main_battle_missions_toc";
import { m_simp_mission_list_toc } from "../../../proto/line/m_simp_mission_list_toc";
import { m_simp_mission_update_toc } from "../../../proto/line/m_simp_mission_update_toc";
import { m_small_game_info_toc } from "../../../proto/line/m_small_game_info_toc";
import { SocketCommand } from "../../../proto/SocketCommand";
import { SceneOverAnimation } from "../../../scene2d/d3/SceneOverAnimation";
import { GameUtil } from "../../../util/GameUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { UIUtil } from "../../../util/UIUtil";
import { BaseController } from "../../BaseController";
import { BaseDialog } from "../../BaseDialog";
import { DataCenter } from "../../DataCenter";
import { GoodsVO } from "../../goods/GoodsVO";
import MainUIManager, { EMainUIBottomIndex } from "../../menu/MainUIManager";
import MissionConst from "../../mission/MissionConst";
import { ModuleCommand } from "../../ModuleCommand";
import { RoleDataCenter } from "../../role/data/RoleDataCenter";
import AlertDialog from "../../tips/view/AlertDialog";
import { WorldUnlockSmallMapDialog } from "../../worldScene/Dialog/WorldUnlockSmallMapDialog";
import { GuaJiDataCenter } from "../data/GuaJiDataCenter";
import GuajiBoxDialog from "../dialog/GuajiBoxDialog";
import { GuaJiBoxLevelPreviewDialog } from "../dialog/GuaJiBoxLevelPreviewDialog";
import GuaJiBoxTeQuanDialog from "../dialog/GuaJiBoxTeQuanDialog";
import { GuaJiBoxUpgradeResultDialog } from "../dialog/GuaJiBoxUpgradeResultDialog";
import { GuaJiComposeDialog } from "../dialog/GuaJiComposeDialog";
import GuaJiDialog from "../../guajiBuild/dialog/GuaJiDialog";
import { GuaJiDropOutDialog } from "../dialog/GuaJiDropOutDialog";
import { GuaJiNavigationDialog } from "../dialog/GuaJiNavigationDialog";
import GuajiQuickFightDialog from "../dialog/GuajiQuickFightDialog";
import GuajiRewardDialog from "../dialog/GuajiRewardDialog";
import { GuajiUpDialog } from "../dialog/GuajiUpDialog";
import PassRewardDialog from "../dialog/PassRewardDialog";
import { RecommendLineUpDialog } from "../dialog/RecommendLineUpDialog";
import { SoldierGoGameDialog } from "../dialog/SoldierGoGameDialog";
import SoldierGoGameFightFailDialog from "../dialog/SoldierGoGameFightFailDialog";
import { MainBattleRechargeDialog } from "../view/MainBattleRechargeDialog";
import { WorldLvTipDialog } from "../view/WorldLvTipDialog";
import GuaJiDialogM2 from "../dialog/GuaJiDialogM2";


//控制器不在任何类引用(包括本模块，只在ModuleController里面注册)
//模块里面的类其他模块都不会去引用的，删除某个模块代码正常编译，正常游戏
//这个类是无状态的
export class GuaJiController extends BaseController {

    static instance: GuaJiController;
    //   private _newchapterdialog: GuaJiNewChapterDialog = null;
    private _passRewardDialog: PassRewardDialog = null;
    private _worldLvTipDialog: WorldLvTipDialog = null;
    private _guajiQuickFightDialog: GuajiQuickFightDialog = null;
    private _guajiRewardDialog: GuajiRewardDialog = null;
    // private _chapterDialog: ChapterRewardDialog = null;
    private _recommendLineUpDialog: RecommendLineUpDialog = null;
    private _mainBattleRechargeDialog: MainBattleRechargeDialog = null;

    constructor() {
        super();
        GuaJiController.instance = this;
    }

    protected initModuleListeners(): void {
        // this.registerDialog(GuaJiDialog, ModuleCommand.OPEN_GUA_JI_DIALOG, ModuleCommand.CLOSE_GUA_JI_DIALOG, false);
        this.registerDialog(GuaJiDialog, ModuleCommand.OPEN_GUA_JI_DIALOG_M3, ModuleCommand.CLOSE_GUA_JI_DIALOG_M3, false);
        this.registerDialog(GuaJiDialogM2, ModuleCommand.OPEN_GUA_JI_DIALOG_M2, ModuleCommand.CLOSE_GUA_JI_DIALOG_M2, false);

        this.addEventListener(ModuleCommand.OPEN_GUA_JI_UP_DIALOG, this, this.openGuajiUpDialog);
        this.addEventListener(ModuleCommand.OPEN_GUA_JI_QUICK_FIGHT_DIALOG, this, this.openGuajiQuickFightDialog);
        this.addEventListener(ModuleCommand.CLOSE_GUA_JI_QUICK_FIGHT_DIALOG, this, this.closeGuajiQuickFightDialog);
        this.addEventListener(ModuleCommand.OPEN_PASS_REWARD_DIALOG, this, this.openPassRewardDialog);
        this.addEventListener(ModuleCommand.OPEN_WORLD_LV_DIALOG, this, this.openWorldLvTipDialog);
        this.addEventListener(ModuleCommand.CLOSE_PASS_REWARD_DIALOG, this, this.closePassRewardDialog);
        this.addEventListener(ModuleCommand.OPEN_GUA_JI_REWARD_DIALOG, this, this.openGuaJiRewardDialog);
        this.addEventListener(ModuleCommand.HIDE_ALL_UI, this, this.onHideAllUI);
        this.addEventListenerOnce(ModuleCommand.CHECK_GUAJI_REWARD_FULL, this, this.firstGameEnter);
        // this.addEventListener(ModuleCommand.OPEN_CHAPTER_DIALOG, this, this.openChapterDialog);
        // this.addEventListener(ModuleCommand.CLOSE_CHAPTER_DIALOG, this, this.closeChapterDialog);
        this.addEventListener(ModuleCommand.OPEN_MAIN_BATTLE_RECHARGE_DIALOG, this, this.openMainBattleRechargeDialog);
        this.addEventListener(ModuleCommand.CLOSE_MAIN_BATTLE_RECHARGE_DIALOG, this, this.closeMainBattleRechargeDialog);
        this.addEventListener(ModuleCommand.OPEN_RECOMMEND_LINE_UP_DIALOG, this, this.openRecommendLineUpDialog);
        this.addEventListener(ModuleCommand.SIMP_MISSION_UPDATE + MissionConst.MISSION_TYPE_SUB, this, this.simpMissionUpdate);
        this.addEventListener(ModuleCommand.SIMP_MISSION_LIST + MissionConst.MISSION_TYPE_SUB, this, this.simpMissionList);
        this.addEventListener(ModuleCommand.OPEN_GUAJI_REWARD_FULL_TIP_DIALOG, this, this.openGuajiRewardTipFullDialog);
        this.registerDialog(WorldUnlockSmallMapDialog, ModuleCommand.OPEN_WORLD_UNLOCK_SMALL_MAP_DIALOG);
        this.registerDialog(GuaJiDropOutDialog, ModuleCommand.OPEN_GUAJI_DROP_OUT_DIALOG);
        this.registerDialog(GuajiBoxDialog, ModuleCommand.OPEN_GUAJI_BOX_DIALOG);
        // this.registerDialog(GuaJiAutoEndDialog, ModuleCommand.OPEN_GUAJI_AUTO_END_DIALOG);
        this.registerDialog(GuaJiBoxLevelPreviewDialog, ModuleCommand.OPEN_GUAJI_BOX_LEVEL_PREVIEW_DIALOG);
        this.registerDialog(GuaJiBoxUpgradeResultDialog, ModuleCommand.OPEN_GUAJI_BOX_UPGRADE_RESULT_DIALOG);
        this.registerDialog(GuaJiBoxTeQuanDialog, ModuleCommand.OPEN_GUAJI_BOX_TEQUAN_DIALOG, ModuleCommand.CLOSE_GUAJI_BOX_TEQUAN_DIALOG);
        this.registerDialog(GuaJiNavigationDialog, ModuleCommand.OPEN_GUAJI_NAVIGATION_DIALOG, ModuleCommand.CLOSE_GUAJI_NAVIGATION_DIALOG);
        this.addEventListener(ModuleCommand.OPEN_GUAJI_SMALL_GAME, this, this.openGuajiSmallGameDialog);
        this.registerDialog(SoldierGoGameDialog, ModuleCommand.OPEN_SOLDIER_GO_GAME_DIALOG);
        this.registerDialog(SoldierGoGameFightFailDialog, ModuleCommand.OPEN_SOLDIER_GO_GAME_FIGHT_FAIL_DIALOG);
        this.registerDialog(GuaJiComposeDialog, ModuleCommand.OPEN_GUAJI_COMPOSE_BOX_DIALOG);
    }

    protected initNetListeners(): void {
        this.addSocketListener(SocketCommand.HANGING_INFO, this.m_hanging_info_toc);
        this.addSocketListener(SocketCommand.HANGING_REWARD, this.m_hanging_reward_toc);
        this.addSocketListener(SocketCommand.HANGING_QUICK, this.m_hanging_quick_toc);

        this.addSocketListener(SocketCommand.MAIN_BATTLE_INFO, this.m_main_battle_info_toc);
        this.addSocketListener(SocketCommand.MAIN_BATTLE_MISSION_INFO, this.m_main_battle_mission_info_toc);
        this.addSocketListener(SocketCommand.MAIN_BATTLE_MISSIONS, this.m_main_battle_missions_toc);
        this.addSocketListener(SocketCommand.MAIN_BATTLE_BOX_INFO, this.m_main_battle_box_info_toc);
        this.addSocketListener(SocketCommand.MAIN_BATTLE_BOX_OPEN, this.m_main_battle_box_open_toc);
        this.addSocketListener(SocketCommand.MAIN_BATTLE_BOX_UPGRADE, this.m_main_battle_box_level_toc);
        this.addSocketListener(SocketCommand.MAIN_BATTLE_AUTO, this.m_main_battle_auto_toc);
        this.addSocketListener(SocketCommand.MAIN_BATTLE_AUTO_END, this.m_main_battle_auto_end_toc);
        this.addSocketListener(SocketCommand.MAIN_BATTLE_BOX_RATE, this.m_main_battle_box_rate_toc);
        this.addSocketListener(SocketCommand.SMALL_GAME_INFO, this.m_small_game_info_toc);
        this.addSocketListener(SocketCommand.MAIN_BATTLE_MISSION_FETCH, this.m_main_battle_mission_fetch_toc);
    }

    private openGuajiSmallGameDialog(): void {
        let small_game_id: number = RoleDataCenter.instance.getValueByFieldName("small_game_id");
        switch (small_game_id) {
            case 1:
                this.dispatchEvent(ModuleCommand.OPEN_SOLDIER_GO_GAME_DIALOG);
                break;
            default:
                this.dispatchEvent(ModuleCommand.OPEN_SOLDIER_GO_GAME_DIALOG);
                break;
        }
    }

    private openRecommendLineUpDialog(param: any): void {
        if (this._recommendLineUpDialog && !this._recommendLineUpDialog.destroyed) { this._recommendLineUpDialog.reOpen(param); return; }
        this._recommendLineUpDialog = new RecommendLineUpDialog();
        this._recommendLineUpDialog.on(Event.CLOSE, this, function () {
            this._recommendLineUpDialog = null;
        });
        this._recommendLineUpDialog.open(false, param);
    }

    openMainBattleRechargeDialog(param: number): void {
        if (this._mainBattleRechargeDialog && !this._mainBattleRechargeDialog.destroyed) { this._mainBattleRechargeDialog.reOpen(param); return; }
        this._mainBattleRechargeDialog = new MainBattleRechargeDialog();
        this._mainBattleRechargeDialog.on(Event.CLOSE, this, function () {
            this._mainBattleRechargeDialog = null;
        });

        this._mainBattleRechargeDialog.open(false, param);
    }

    closeMainBattleRechargeDialog() {
        if (this._mainBattleRechargeDialog && !this._mainBattleRechargeDialog.destroyed) {
            this._mainBattleRechargeDialog.close();
            this._mainBattleRechargeDialog = null;
        }
    }


    private onHideAllUI(isHide: boolean): void {
        UIUtil.isHideAllUi = isHide;

        let _children: any[] = Dialog.manager._children.concat();
        let num: number = _children.length - 1;
        for (var i: number = num; i > -1; i--) {
            var item: BaseDialog = (<BaseDialog>_children[i]);
            if (item && !item.destroyed && !item["_hold_"] && !item["_visible_"] && item instanceof BaseDialog) {
                item.visible = !isHide;
            }
        }
        if (isHide == true) {
            Dialog.manager.maskLayer.alpha = 0;
        } else {
            Dialog.manager.maskLayer.alpha = UIConfig.popupBgAlpha;
            if (MainUIManager.instance.selectTabIndex == EMainUIBottomIndex.index_4_guaji && LayerManager.runningFight(MatchConst.MATCH_TYPE_MAIN_BATTLE) == false && LayerManager.runningFight(MatchConst.MATCH_TYPE_MASTER_CARD) == false) {
                console.log("GuaJiController.onHideAllUI");
                if (DataCenter.isM3Building) {
                    this.dispatchEvent(ModuleCommand.OPEN_GUA_JI_DIALOG_M3);
                } else if (DataCenter.isM2HuiHeGuaJi) {
                    this.dispatchEvent(ModuleCommand.OPEN_GUA_JI_DIALOG_M2);
                }
               
            }
        }
        LayerManager.checkMask();
        this.dispatchEvent(ModuleCommand.ON_CHECK_NAV_SHOW);
    }

    private firstGameEnter(): void {
        if (GuaJiDataCenter.instance.max_duration == 0 || DataCenter.myLevel <= 1) return;
        //检查挂机时长是否满
        let tip: string = "";
        if (GuaJiDataCenter.instance.guaJiTime >= GuaJiDataCenter.instance.max_duration) {
            tip = window.iLang.L2_NIN_DE_GUA_JI_SHOU_YI_YI_DA_DAO_SHANG_XIAN_ch31_QING_QIAN_WANG_GUA_JI_JIE.il();
        } else if (1.0 * GuaJiDataCenter.instance.guaJiTime / GuaJiDataCenter.instance.max_duration > 0.9) {
            tip = window.iLang.L2_NIN_DE_GUA_JI_SHOU_YI_JI_JIANG_DA_DAO_SHANG_XIAN_ch31_QING_QIAN_WANG_GUA_JI.il();
        }
        if (tip.length > 0) {
            //弹窗提示
            TipsUtil.setPopUpWindows(ModuleCommand.OPEN_GUAJI_REWARD_FULL_TIP_DIALOG, { tip: tip });
        }

        //如果当前没有主线战报，判断是否还有剩余自动推关次数，有则自动发起挑战
        if (!LayerManager.isInBattle(MatchConst.MATCH_TYPE_MAIN_BATTLE) && GuaJiDataCenter.instance.laveAutoPassNum > 0) {
            GuaJiDataCenter.instance.onFightMainBattle({
                isInBackFight: true,
                isAuto: true,
                checkReback: true,
                isDo: false,
                isLineUp: false,
            });
        }
        GuaJiDataCenter.instance.m_main_battle_mission_info_tos();
    }

    private openGuajiRewardTipFullDialog(param: any): void {
        if (!param || !param.tip) return;
        let tip: string = param.tip;
        let dialog: AlertDialog = TipsUtil.showDialog(this, tip, window.iLang.L2_TI_SHI.il(), function () {
            this.dispatchEvent(ModuleCommand.REQUEST_CHANGER_MAIN_TAB, 4);
        });
        if (dialog) {
            dialog.on(Event.CLOSE, this, () => {
                TipsUtil.clearPopUpWindows();
            });
        }
    }

    protected reset(): void {
        super.reset();
    }

    openGuajiUpDialog(param: any): void {
        if (MainUIManager.instance.getCurTabIndex() != 4) {
            TipsUtil.clearPopUpWindows();
            return;
        }

        if (GuaJiDataCenter.isInGuaJiDialog == false) {
            TipsUtil.clearPopUpWindows();
            return;
        }

        let _this = this;
        let openDialogFunc = function (pass: number) {
            let dialog: GuajiUpDialog = new GuajiUpDialog();
            dialog.open(false, pass, false);
            if (_this["GuaJiDialog"]) {
                dialog.zOrder = _this["GuaJiDialog"].zOrder;
            }
        }
        if (param && param.isDelay) {
            let t: number = 1500;
            if (SceneOverAnimation._sp1 == null) {
                t = 0;
            }
            ILaya.timer.once(t, this, openDialogFunc, [GuaJiDataCenter.instance.curGuajiPass]);
        } else {
            openDialogFunc(GuaJiDataCenter.instance.curGuajiPass);
        }

        // if (param && param.isDelay) {
        //   let t: number = 1500;
        //   if (SceneOverAnimation._sp1 == null) {
        //     t = 0;
        //   }
        //   ILaya.timer.once(t, this, function () {
        //     if (GuideMgr.ins.isOpenGuide == false) {
        //       let dialog: GuajiUpDialog = new GuajiUpDialog();
        //       dialog.open(false, GuaJiDataCenter.instance.curGuajiPass, false);
        //     } else {
        //       TipsUtil.clearPopUpWindows();
        //     }
        //   });
        // } else {
        //   if (GuideMgr.ins.isOpenGuide == false) {
        //     let dialog: GuajiUpDialog = new GuajiUpDialog();
        //     dialog.open(false, GuaJiDataCenter.instance.curGuajiPass, false);
        //   } else {
        //     TipsUtil.clearPopUpWindows();
        //   }
        // }
    }

    // openChapterDialog(param:any): void {
    //     if (this._chapterDialog && !this._chapterDialog.destroyed) {this._chapterDialog.reOpen(param);return;}
    //     this._chapterDialog = new ChapterRewardDialog();
    //     this._chapterDialog.on(Event.CLOSE, this, function () {
    //         this._chapterDialog = null;
    //     });
    //
    //     this._chapterDialog.open(false);
    // }

    // closeChapterDialog() {
    //     if (this._chapterDialog && !this._chapterDialog.destroyed) {
    //         this._chapterDialog.close();
    //         this._chapterDialog = null;
    //     }
    // }

    openGuajiQuickFightDialog(param: any) {
        if (this._guajiQuickFightDialog && !this._guajiQuickFightDialog.destroyed) { this._guajiQuickFightDialog.reOpen(param); return; }
        this._guajiQuickFightDialog = new GuajiQuickFightDialog();
        this._guajiQuickFightDialog.on(Event.CLOSE, this, function () {
            this._guajiQuickFightDialog = null;
        });

        this._guajiQuickFightDialog.open(false, param);
    }

    closeGuajiQuickFightDialog() {
        if (this._guajiQuickFightDialog && !this._guajiQuickFightDialog.destroyed) {
            this._guajiQuickFightDialog.close();
            this._guajiQuickFightDialog = null;
        }
    }

    openGuaJiRewardDialog(param: any): void {

        if (this._guajiRewardDialog && !this._guajiRewardDialog.destroyed) { this._guajiRewardDialog.reOpen(param); return; }
        this._guajiRewardDialog = new GuajiRewardDialog();
        this._guajiRewardDialog.on(Event.CLOSE, this, function () {
            this._guajiRewardDialog = null;
        });

        this._guajiRewardDialog.open(false, param);
    }

    openPassRewardDialog(param: any) {
        if (this._passRewardDialog && !this._passRewardDialog.destroyed) { this._passRewardDialog.reOpen(param); return; }
        this._passRewardDialog = new PassRewardDialog();
        this._passRewardDialog.on(Event.CLOSE, this, function () {
            this._passRewardDialog = null;
        });

        this._passRewardDialog.open(false, param);
    }

    openWorldLvTipDialog(...param: any[]) {
        if (this._worldLvTipDialog && !this._worldLvTipDialog.destroyed) { this._worldLvTipDialog.reOpen(param); return; }
        this._worldLvTipDialog = new WorldLvTipDialog();
        this._worldLvTipDialog.on(Event.CLOSE, this, function () {
            this._worldLvTipDialog = null;
        });

        this._worldLvTipDialog.open(false, param);
    }

    closePassRewardDialog() {
        if (this._passRewardDialog) {
            this._passRewardDialog.close();
            this._passRewardDialog = null;
        }
    }

    //-------------协议接收 start -------------
    m_hanging_info_toc(msg: m_hanging_info_toc): void {
        GuaJiDataCenter.instance.start_time = msg.start_time;
        GuaJiDataCenter.instance.max_duration = msg.max_duration;
        GuaJiDataCenter.instance.free_quick_times = msg.free_quick_times;
        GuaJiDataCenter.instance.quick_times = msg.quick_times;
        GuaJiDataCenter.instance.rewards_times = msg.rewards_times;
        GuaJiDataCenter.instance.cost_gold = GameUtil.gold(msg.cost_gold);
        GuaJiDataCenter.instance.is_activity = msg.is_activity;
        DispatchManager.dispatchEvent(ModuleCommand.UPDATE_GUA_JI_INFO);
    }

    m_hanging_reward_toc(msg: m_hanging_reward_toc): void {
        if (msg.op_type == 2) {
            GuaJiDataCenter.instance.gainRewards = [];
            this.showGuajiRewardDialog(msg.gains, msg.rewards_times, 1);
        } else if (msg.op_type == 1) {
            GuaJiDataCenter.instance.gainRewards = msg.gains;
            GuaJiDataCenter.instance.showFixedItems = msg.fixed_items;
            // GuaJiDataCenter.instance.start_time = msg.rewards_times * 60;
        }
        this.dispatchEvent(ModuleCommand.UPDATE_GUA_JI_HANDING_REWARD_INFO);
    }

    m_hanging_quick_toc(msg: m_hanging_quick_toc): void {
        GuaJiDataCenter.instance.is_quick_fighting = false;
        this.showGuajiRewardDialog(msg.gains, msg.rewards_times, 2);
    }

    m_main_battle_box_info_toc(msg: m_main_battle_box_info_toc): void {
        GuaJiDataCenter.instance.main_battle_Box_data = msg;
        DispatchManager.dispatchEvent(ModuleCommand.UPDATE_MAIN_BATTLE_INFO);
        DispatchManager.dispatchEvent(ModuleCommand.UPDATE_MAIN_BATTLE_BOX_INFO);
        let isAutoPass = GuaJiDataCenter.instance.isAutoPass;
        let isFighting = GuaJiDataCenter.instance.isRunningMainBattle();
        if (msg.boxs.length > 0 && !isAutoPass && !isFighting) {
            DispatchManager.dispatchEvent(ModuleCommand.FLY_MAIN_BATTLE_BOX);
            GuaJiDataCenter.instance.m_main_battle_box_fetch_tos(1);
        }
    }

    m_main_battle_box_open_toc(msg: m_main_battle_box_open_toc): void {
        DispatchManager.dispatchEvent(ModuleCommand.UPDATE_MAIN_BATTLE_BOX_INFO);
        let goodsVoList = GoodsVO.GetPItemToVos(msg.gains);
        TipsUtil.showRewardDialog(goodsVoList);
    }

    m_main_battle_box_level_toc(msg: m_main_battle_box_upgrade_toc): void {
        if (GuaJiDataCenter.instance.main_battle_Box_data) {
            GuaJiDataCenter.instance.main_battle_Box_data.level = msg.new_level;
            DispatchManager.dispatchEvent(ModuleCommand.UPDATE_MAIN_BATTLE_BOX_UPGRADE);
            DispatchManager.dispatchEvent(ModuleCommand.OPEN_GUAJI_BOX_UPGRADE_RESULT_DIALOG);
        }
    }

    m_main_battle_auto_toc(msg: m_main_battle_auto_toc): void {
        GuaJiDataCenter.instance.autoBattleInfo = msg;
        this.dispatchEvent(ModuleCommand.UPDATE_MAIN_BATTLE_AUTO, msg);
    }

    m_main_battle_box_rate_toc(msg: m_main_battle_box_rate_toc): void {
        GuaJiDataCenter.instance.guaji_box_rates = msg.rates;
        GuaJiDataCenter.instance.guaji_box_tequan_rates = msg.tequan_rates;
        GuaJiDataCenter.instance.guaji_box_level_rates = msg.level_rates;
        GuaJiDataCenter.instance.set_guaji_box_hanging_rates(msg.hanging_rates);
        this.dispatchEvent(ModuleCommand.UPDATE_MAIN_BATTLE_BOX_INFO);
    }

    m_main_battle_auto_end_toc(msg: m_main_battle_auto_end_toc): void {
        let hasGains = msg && msg.gains && msg.gains.length > 0;
        let passNum = msg.end_pass - msg.start_pass;
        if (hasGains && passNum > 1) {
            this.dispatchEvent(ModuleCommand.OPEN_GUAJI_AUTO_END_DIALOG, msg);
        }
    }

    m_small_game_info_toc(msg: m_small_game_info_toc): void {
        GuaJiDataCenter.instance.small_soldier_game_fetch_list = msg.fetch_list;
        this.dispatchEvent(ModuleCommand.UPDATE_SMALL_GAME_INFO, msg);
    }

    private showGuajiRewardDialog(gains: p_item[], rewards_times: number, type: number): void {

        let goodVOList: GoodsVO[] = GuaJiDataCenter.parseGuajiReward(gains);
        //TODO 弹快速挂机奖励弹窗
        //let dialog: GuajiRewardDialog = new GuajiRewardDialog();
        var obj: any = new Object();
        obj.rewards_times = rewards_times * 60;
        obj.goodVOList = goodVOList;
        obj.type = type;
        //dialog.open(false, obj);

        //this.dispatchEvent(ModuleCommand.OPEN_GUA_JI_REWARD_DIALOG,obj);
        TipsUtil.setPopUpWindows(ModuleCommand.OPEN_GUA_JI_REWARD_DIALOG, obj);
    }

    m_main_battle_info_toc(msg: m_main_battle_info_toc): void {
        GuaJiDataCenter.instance.main_battle_info = msg;
        // GuaJiDataCenter.instance.mission_status = msg.mission_status;
        // GuaJiDataCenter.instance.isPassRewardRedPoint();
        DispatchManager.dispatchEvent(ModuleCommand.UPDATE_MAIN_BATTLE_INFO);

    }


    m_main_battle_mission_info_toc(msg: m_main_battle_mission_info_toc): void {
        GuaJiDataCenter.instance.main_battle_mission_status = msg.mission_status;
        GuaJiDataCenter.instance.isMissionReward2RedPoint();
        DispatchManager.dispatchEvent(ModuleCommand.UPDATE_MAIN_BATTLE_MISSION_STATUS);
    }

    m_main_battle_mission_fetch_toc(msg: m_main_battle_mission_fetch_toc): void {

    }


    private m_main_battle_missions_toc(msg: m_main_battle_missions_toc) {
        GuaJiDataCenter.instance.updatePassRewards(msg.pass_rewards);
    }


    //----支线任务   start----

    private simpMissionUpdate(toc: m_simp_mission_update_toc) {
        GuaJiDataCenter.instance.UpdateSubMissionMap(toc.mission);
        DispatchManager.dispatchEvent(ModuleCommand.UPDATE_GUA_JI_SUB_MISSION);
    }

    private simpMissionList(toc: m_simp_mission_list_toc) {
        GuaJiDataCenter.instance.SetSubMissionMap(toc.mission_list);
        DispatchManager.dispatchEvent(ModuleCommand.UPDATE_GUA_JI_SUB_MISSION);
    }

    //----支线任务   end----

    //-------------协议接收 end ---------------

}
