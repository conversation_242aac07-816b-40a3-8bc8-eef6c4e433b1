import { cfg_fuli_yueka } from "../../cfg/vo/cfg_fuli_yueka";
import { ConfigManager } from "../../managers/ConfigManager";
import { DispatchManager } from "../../managers/DispatchManager";
import { GameUtil } from "../../util/GameUtil";
import { ActModsSubType } from "../actModules/common/ActModsConsts";
import { ActModsStateDataCenter } from "../actModules/common/data/ActModsStateDataCenter";
import { ActModsUtil } from "../actModules/common/utils/ActModsUtil";
import { ActModsHeroPassDataCenter } from "../actModules/Passport/data/ActModsHeroPass/ActModsHeroPassDataCenter";
import { ActModsPaymentShopDataCenter } from "../actModules/paymentShop/data/ActModsPaymentShopDataCenter";
import { MenuDataCenter } from "../menu/data/MenuDataCenter";
import { ModuleCommand } from "../ModuleCommand";
import { PanelEventConstants } from "../PanelEventConstants";
import { FirstPayType, RechargeDataCenter } from "../recharge/data/RechargeDataCenter";
import { WelfareDataCenter } from "../welfare/data/WelfareDataCenter";
import { YueKaDataCenter} from "../welfare/data/YueKaDataCenter";
import { PaymentDataCenter } from "./data/PaymentDataCenter";
import { PaymentVO } from "./vo/PaymentVO";
import {EYueKaType} from "../../auto/ConstAuto";

export class PaymentLinkUtil {
    /**推荐充值 */
    static gotoRecommendPayment():void {
        //首充6元礼包 > 开服特惠6元礼包（已购买或活动过期则作废）> 首充12元礼包 > 首充30元礼包 > 每日礼包
        //首充图标是否存在
        let isHasFirstIcon = MenuDataCenter.instance.isIconOpen(PanelEventConstants.FIRST_RECHARG);
        //1、判断首充是否充值
        if(isHasFirstIcon && !RechargeDataCenter.instance.isFirstPay){
            //打开首充界面
            GameUtil.gotoSystemPanelById(PanelEventConstants.FIRST_RECHARG);
            return;
        }
        //2、判断是否购买开服特惠礼包
        if(MenuDataCenter.instance.isIconOpen(PanelEventConstants.NEW_SERVER)){
            let shopVo = PaymentDataCenter.instance.getPaymentVoByID(PaymentVO.KEY_NEWSERVER_SHOP);
            if(shopVo){
                for(let item of shopVo.list){
                    if(item.price == 6 && item.isCanBuy){
                        GameUtil.gotoSystemPanelById(PanelEventConstants.NEW_SERVER);
                        return;
                    }
                }
            }
        }
        //3、判断首充12、30元档
        if(isHasFirstIcon){
            let cfgList = ConfigManager.cfg_first_payCache.get_all();
            for(let cfg of cfgList){
                if(cfg.money != 12 && cfg.money != 30){
                    continue;
                }
                let state = RechargeDataCenter.instance.getFirstPayTaskStateByGiftId(cfg.gift_id);
                if(state == 0){
                    //打开首充界面
                    GameUtil.gotoSystemPanelById(PanelEventConstants.FIRST_RECHARG);
                    return;
                }
            }
        }
        //4、每日礼包界面
        GameUtil.gotoSystemPanelById(PanelEventConstants.PAYMENT, PaymentVO.KEY_EVERYDAY_SHOP);
    }

    /**月卡推荐充值 */
    static onYueKaRecommendPayment(yueka_type:EYueKaType):void {
        let yueKaNeedRmb = 0;
        let kv = YueKaDataCenter.instance.getFuliYuekaInfo(yueka_type);
        let cfg = ConfigManager.cfg_fuli_yuekaCache.get(yueka_type);
        if(kv && cfg){
            yueKaNeedRmb = cfg.need_recharge - kv.price;
        }
        
        let dangList = [
            {key:6,val:PaymentLinkUtil.payment6Rmb},
            {key:12,val:PaymentLinkUtil.payment12Rmb},
            {key:18,val:PaymentLinkUtil.payment18Rmb},
            {key:30,val:PaymentLinkUtil.payment30Rmb},
            {key:68,val:PaymentLinkUtil.payment68Rmb},
            {key:98,val:PaymentLinkUtil.payment98Rmb},
        ]

        let list1 = [];
        for(let kv of dangList){
            if(kv.key >= yueKaNeedRmb){
                list1.push(kv);
            }
        }
        
        list1.sort((t1,t2) => {
            let abs1 = Math.abs(t1.key - yueKaNeedRmb);
            let abs2 = Math.abs(t2.key - yueKaNeedRmb);
            return abs1 - abs2;
        });

        let pkv = list1[0] || dangList[dangList.length-1];
        pkv.val();
    }

    /**6元档充值 */
    private static payment6Rmb():boolean {
        //首充图标是否存在
        let isHasFirstIcon = MenuDataCenter.instance.isIconOpen(PanelEventConstants.FIRST_RECHARG);
        //1、判断首充是否充值
        if(isHasFirstIcon && !RechargeDataCenter.instance.isFirstPay){
            //打开首充界面
            GameUtil.gotoSystemPanelById(PanelEventConstants.FIRST_RECHARG);
            return true;
        }
        //2、判断是否购买开服特惠礼包
        if(MenuDataCenter.instance.isIconOpen(PanelEventConstants.NEW_SERVER)){
            let shopVo = PaymentDataCenter.instance.getPaymentVoByID(PaymentVO.KEY_NEWSERVER_SHOP);
            if(shopVo){
                for(let item of shopVo.list){
                    if(item.price == 6 && item.isCanBuy){
                        GameUtil.gotoSystemPanelById(PanelEventConstants.NEW_SERVER);
                        return true;
                    }
                }
            }
        }
        //3、每日礼包6元
        let shopVo: PaymentVO = PaymentDataCenter.instance.getPaymentVoByID(PaymentVO.KEY_EVERYDAY_SHOP);
        if(shopVo){
            for(let item of shopVo.list){
                if(item.price == 6 && item.isCanBuy){
                    GameUtil.gotoSystemPanelById(PanelEventConstants.PAYMENT, PaymentVO.KEY_EVERYDAY_SHOP);
                    return true;
                }
            }
        }
        return false;
    }

    //12元档
    private static payment12Rmb():boolean {
        //首充图标是否存在
        let isHasFirstIcon = MenuDataCenter.instance.isIconOpen(PanelEventConstants.FIRST_RECHARG);
        //1、判断首充是否充值
        if(isHasFirstIcon && RechargeDataCenter.instance.isFirstPay){
            //12元连冲是否充值
            let state = RechargeDataCenter.instance.getFirstPayTaskStateByGiftId(8007003);
            //打开首充界面
            if(state == 0){
                GameUtil.gotoSystemPanelById(PanelEventConstants.FIRST_RECHARG);
                return true;
            }
        }

        //2、判断是否购买开服特惠礼包
        if(MenuDataCenter.instance.isIconOpen(PanelEventConstants.NEW_SERVER)){
            let shopVo = PaymentDataCenter.instance.getPaymentVoByID(PaymentVO.KEY_NEWSERVER_SHOP);
            if(shopVo){
                for(let item of shopVo.list){
                    if(item.price == 18 && item.isCanBuy){
                        GameUtil.gotoSystemPanelById(PanelEventConstants.NEW_SERVER);
                        return true;
                    }
                }
            }
        }

        //2、每日礼包12元
        // let shopVo: PaymentVO = PaymentDataCenter.instance.getPaymentVoByID(PaymentVO.KEY_EVERYDAY_SHOP);
        // if(shopVo){
        //     for(let item of shopVo.list){
        //         if(item.price == 12 && item.isCanBuy){
                    GameUtil.gotoSystemPanelById(PanelEventConstants.PAYMENT, PaymentVO.KEY_EVERYDAY_SHOP);
                    return true;
        //         }
        //     }
        // }

        return false;
    }

    private static payment18Rmb():boolean {
        //1、判断是否购买开服特惠礼包
        if(MenuDataCenter.instance.isIconOpen(PanelEventConstants.NEW_SERVER)){
            let shopVo = PaymentDataCenter.instance.getPaymentVoByID(PaymentVO.KEY_NEWSERVER_SHOP);
            if(shopVo){
                for(let item of shopVo.list){
                    if(item.price == 18 && item.isCanBuy){
                        GameUtil.gotoSystemPanelById(PanelEventConstants.NEW_SERVER);
                        return true;
                    }
                }
            }
        }

        //2、每日礼包12元
        // let shopVo: PaymentVO = PaymentDataCenter.instance.getPaymentVoByID(PaymentVO.KEY_EVERYDAY_SHOP);
        // if(shopVo){
        //     for(let item of shopVo.list){
        //         if(item.price == 12 && item.isCanBuy){
                    GameUtil.gotoSystemPanelById(PanelEventConstants.PAYMENT, PaymentVO.KEY_EVERYDAY_SHOP);
                    return true;
        //         }
        //     }
        // }
        return false;
    }

    private static payment30Rmb():boolean {
        let shopVo: PaymentVO 
        //首充图标是否存在
        let isHasFirstIcon = MenuDataCenter.instance.isIconOpen(PanelEventConstants.FIRST_RECHARG);
        //2、判断首充是否充值
        if(isHasFirstIcon && RechargeDataCenter.instance.isFirstPay){
            //30元连冲是否充值
            let state = RechargeDataCenter.instance.getFirstPayTaskStateByGiftId(8007004);
            //打开首充界面
            if(state == 0){
                GameUtil.gotoSystemPanelById(PanelEventConstants.FIRST_RECHARG);
                return true;
            }
        }

        //3、特权商店30元充值
        shopVo = PaymentDataCenter.instance.getPaymentVoByID(PaymentVO.KEY_PRIVILEGE_SHOP);
        if(shopVo){
            for(let item of shopVo.list){
                if(item.price == 30 && item.isCanBuy){
                    GameUtil.gotoSystemPanelById(PanelEventConstants.PAYMENT, PaymentVO.KEY_PRIVILEGE_SHOP);
                    return true;
                }
            }
        }

        //1、每日礼包30元
        // shopVo= PaymentDataCenter.instance.getPaymentVoByID(PaymentVO.KEY_EVERYDAY_SHOP);
        // if(shopVo){
        //     for(let item of shopVo.list){
        //         if(item.price == 30 && item.isCanBuy){
                    GameUtil.gotoSystemPanelById(PanelEventConstants.PAYMENT, PaymentVO.KEY_EVERYDAY_SHOP);
                    return true;
        //         }
        //     }
        // }

        return false;
    }

    private static payment68Rmb():boolean {
        
        // let subTypeList = [ActModsSubType.LOTTERY, ActModsSubType.LOTTERY_WISH, ActModsSubType.LOTTERY_SKIN];
        // for(let sub_type of subTypeList){
        //     let actVoList = ActModsStateDataCenter.instance.getSubActOpenList(sub_type);
        //     if(actVoList && actVoList.length > 0){
        //         for(let actVo of actVoList){
        //             let shopVo  = ActModsPaymentShopDataCenter.instance.getShopVo(actVo.big_type,sub_type);
        //             if(shopVo){
        //                 for(let itemVo of shopVo.itemList){
        //                     if(itemVo.price == 68 && itemVo.isCanBuy){
        //                         ActModsUtil.openActDialog(actVo.big_type,actVo.sub_type,actVo.sn);
        //                         return true;
        //                     }
        //                 }
        //             }
        //         }
        //     }
        // }
        let isBuy = ActModsHeroPassDataCenter.instance.hero_pass_info && ActModsHeroPassDataCenter.instance.hero_pass_info.is_prince;
        if(ActModsHeroPassDataCenter.instance.hero_pass_info && !isBuy){
            DispatchManager.dispatchEvent(ModuleCommand.OPEN_ACT_MODS_HERO_PASS_UNLOCK_TIPS_DIALOG);
            return true;
        }

        //首充图标是否存在
        let isHasFirstIcon = MenuDataCenter.instance.isIconOpen(PanelEventConstants.FIRST_RECHARG);
        //1、判断首充是否充值
        if(isHasFirstIcon){
            //68元连冲是否充值
            let state = RechargeDataCenter.instance.getFirstPayTaskStateByGiftId(8007002);
            //打开首充界面
            if(state == 0){
                GameUtil.gotoSystemPanelById(PanelEventConstants.FIRST_RECHARG,2);
                return true;
            }
        }
        //68 周卡
        let weekcardList = [ActModsSubType.WEEK_YUANBAO,ActModsSubType.WEEK_NAXIAN,ActModsSubType.WEEK_BAIJIANG];
        for(let sub_type of weekcardList){
            let actVoList = ActModsStateDataCenter.instance.getSubActOpenList(sub_type);
            if(actVoList && actVoList.length > 0){
                for(let actVo of actVoList){
                    let shopVo  = ActModsPaymentShopDataCenter.instance.getShopVo(actVo.big_type,sub_type);
                    if(shopVo){
                        for(let itemVo of shopVo.itemList){
                            if(itemVo.price == 68 && itemVo.isCanBuy){
                                ActModsUtil.openActDialog(actVo.big_type,actVo.sub_type,actVo.sn);
                                return true;
                            }
                        }
                    }
                }
            }
        }

        //每日礼包68元
        // let shopVo: PaymentVO = PaymentDataCenter.instance.getPaymentVoByID(PaymentVO.KEY_EVERYDAY_SHOP);
        // if(shopVo){
        //     for(let item of shopVo.list){
        //         if(item.price == 68 && item.isCanBuy){
                    GameUtil.gotoSystemPanelById(PanelEventConstants.PAYMENT, PaymentVO.KEY_EVERYDAY_SHOP);
                    return true;
        //         }
        //     }
        // }
        return false;
    }

    private static payment98Rmb():boolean {
        //98 周卡
        let weekcardList = [ActModsSubType.WEEK_YUANBAO,ActModsSubType.WEEK_NAXIAN,ActModsSubType.WEEK_BAIJIANG];
        for(let sub_type of weekcardList){
            let actVoList = ActModsStateDataCenter.instance.getSubActOpenList(sub_type);
            if(actVoList && actVoList.length > 0){
                for(let actVo of actVoList){
                    let shopVo  = ActModsPaymentShopDataCenter.instance.getShopVo(actVo.big_type,sub_type);
                    if(shopVo){
                        for(let itemVo of shopVo.itemList){
                            if(itemVo.price == 98 && itemVo.isCanBuy){
                                ActModsUtil.openActDialog(actVo.big_type,actVo.sub_type,actVo.sn);
                                return true;
                            }
                        }
                    }
                }
            }
        }

        //2、判断是否购买开服特惠礼包
        if(MenuDataCenter.instance.isIconOpen(PanelEventConstants.NEW_SERVER)){
            let shopVo = PaymentDataCenter.instance.getPaymentVoByID(PaymentVO.KEY_NEWSERVER_SHOP);
            if(shopVo){
                for(let item of shopVo.list){
                    if(item.price == 98 && item.isCanBuy){
                        GameUtil.gotoSystemPanelById(PanelEventConstants.NEW_SERVER);
                        return true;
                    }
                }
            }
        }

        //98成长基金
        let fundsInfo = WelfareDataCenter.instance.war_makes_single_info.get(3 + "_" + 1);
        if(fundsInfo && !fundsInfo.is_buy){
            GameUtil.gotoSystemPanelById(PanelEventConstants.WELFARE,WelfareDataCenter.KEY_WELFARE_LV_MAKES);
            return true;
        }

        //每日礼包
        // let shopVo: PaymentVO = PaymentDataCenter.instance.getPaymentVoByID(PaymentVO.KEY_EVERYDAY_SHOP);
        // if(shopVo){
        //     for(let item of shopVo.list){
        //         if(item.isCanBuy){
                    GameUtil.gotoSystemPanelById(PanelEventConstants.PAYMENT, PaymentVO.KEY_EVERYDAY_SHOP);
                    return true;
        //         }
        //     }
        // }
        return false;
    }
}