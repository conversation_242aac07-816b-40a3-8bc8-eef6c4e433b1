import { Laya } from "Laya";
import { Event } from "laya/events/Event";
import { ConfigManager } from "../../../managers/ConfigManager";
import { DispatchManager } from "../../../managers/DispatchManager";
import { SocketCommand } from "../../../proto/SocketCommand";
import { ObjectUtil } from "../../../util/ObjectUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { BaseController } from "../../BaseController";
import { GodEquipAddPowerDialog } from "../../equip/dialog/GodEquipAddPowerDialog";
import { GodEquipTujianDialog } from "../../equip/dialog/GodEquipTujianDialog";
import FlyFontVo from "../../flyFont/vo/FlyFontVo";
import { ForgeDataCenter } from "../../forge/data/ForgeDataCenter";
import { GeneralMansionDataCenter } from "../../generalMansion/data/GeneralMansionDataCenter";
import CastSoulRecastDialog from "../../goods/dialog/CastSoulRecastDialog";
import { SkillDataCenter } from "../../skill/data/SkillDataCenter";
import { HeroComposeLogsDataCenter } from "../data/HeroComposeLogsDataCenter";
import { HeroConsts } from "../data/HeroConsts";
import { HeroDataCenter } from "../data/HeroDataCenter";
import { HeroTuJianDataCenter } from "../data/HeroTuJianDataCenter";
import BingFaDetailTipDialog from "../dialog/BingFaDetailTipDialog";
import { BingFaStudyOrLvUpDialog } from "../dialog/BingFaStudyOrLvUpDialog";
import { CastSoulDialog } from "../dialog/CastSoulDialog";
import { CastSoulPreAttrDialog } from "../dialog/CastSoulPreAttrDialog";
import { CastSoulSelectActiveDialog } from "../dialog/CastSoulSelectActiveDialog";
import HeroActListDialog from "../dialog/HeroActListDialog";
import HeroAllListDialog from "../dialog/HeroAllListDialog";
import HeroAttrDescTipsDialog from "../dialog/HeroAttrDescTipsDialog";
import HeroAttrTipsDialog from "../dialog/HeroAttrTipsDialog";
import { HeroBingFaLevelUpDialog } from "../dialog/HeroBingFaLevelUpDialog";
import { HeroBingFaLevelUpTipDialog } from "../dialog/HeroBingFaLevelUpTipDialog";
import { HeroBingFaPreviewDialog } from "../dialog/HeroBingFaPreviewDialog";
import { HeroBingFaStudyDialog } from "../dialog/HeroBingFaStudyDialog";
import { HeroComposeLogsDialog } from "../dialog/HeroComposeLogsDialog";
import HeroDebrisSelectDialog from "../dialog/HeroDebrisSelectDialog";
import { HeroDebrisSelectFilterDialog } from "../dialog/HeroDebrisSelectFilterDialog";
import HeroInheritDialog from "../dialog/HeroInheritDialog";
import { HeroInheritSuccDialog } from "../dialog/HeroInheritSuccDialog";
import { HeroLockDialog } from "../dialog/HeroLockDialog";
import HeroMaterialSelectDialog from "../dialog/HeroMaterialSelectDialog";
import HeroRecycleDialog from "../dialog/HeroRecycleDialog";
import HeroResetPreviewDialog from "../dialog/HeroResetPreviewDialog";
import { HeroShowDialog } from "../dialog/HeroShowDialog";
import HeroSingleInfoDialog from "../dialog/HeroSingleInfoDialog";
import HeroStageUpdateCompleteDialog from "../dialog/HeroStageUpdateCompleteDialog";
import { HeroStarStageAdditionDialog } from "../dialog/HeroStarStageAdditionDialog";
import { HeroStarStageUpdateDialog } from "../dialog/HeroStarStageUpdateDialog";
import HeroStarUpdateCompleteDialog from "../dialog/HeroStarUpdateCompleteDialog";
import HeroStarUpdateGrowthRoadDialog from "../dialog/HeroStarUpdateGrowthRoadDialog";
import { HeroUpgradeCompleteDialog } from "../dialog/HeroUpgradeCompleteDialog";
import { HeroUpgradeUnlockTipsDialog } from "../dialog/HeroUpgradeUnlockTipsDialog";
import { HeroWarFlagActiveDialog } from "../dialog/HeroWarFlagActiveDialog";
import { HeroWarFlagLinkAttrDialog } from "../dialog/HeroWarFlagLinkAttrDialog";
import { HeroWarFlagLinkDialog } from "../dialog/HeroWarFlagLinkDialog";
import { HeroWarFlagLinkSelectDialog } from "../dialog/HeroWarFlagLinkSelectDialog";
import { HeroWarFlagLinkSuccessDialog } from "../dialog/HeroWarFlagLinkSuccessDialog";
import { HeroWarFlagLvUpOrStageDialog } from "../dialog/HeroWarFlagLvUpOrStageDialog";
import { HeroWarFlagResetDialog } from "../dialog/HeroWarFlagResetDialog";
import { HeroWarFlagResetLinkDialog } from "../dialog/HeroWarFlagResetLinkDialog";
import { HeroWarFlagSelectActiveDialog } from "../dialog/HeroWarFlagSelectActiveDialog";
import { HeroWarFlagStagePreDialog } from "../dialog/HeroWarFlagStagePreDialog";
import { HeroWarFlagUnlockLinkTipDialog } from "../dialog/HeroWarFlagUnlockLinkTipDialog";
import { SoulHeroLinkConfirmDialog } from "../dialog/SoulHeroLinkConfirmDialog";
import { SoulHeroLinkSelectDialog } from "../dialog/SoulHeroLinkSelectDialog";
import { SoulHeroResetDialog } from "../dialog/SoulHeroResetDialog";
import CastSoulCompareView from "../view/heroSoul/CastSoulCompareView";
import { HeroStageUpdateDialog } from "../view/stageUpdate/HeroStageUpdateDialog";
import HeroStarUpConsumeGoDialog from "../view/starUpdate/HeroStarUpConsumeGoDialog";
import HeroStarUpGrowthRoadDialog from "../view/starUpdate/HeroStarUpGrowthRoadDialog";
import { BingFaExchangeSelectHeroDialog } from "../dialog/BingFaExchangeSelectHeroDialog";
import { HeroBingFaChangeDialog } from "../dialog/HeroBingFaChangeDialog";
import { HeroBingFaChangeTipDialog } from "../dialog/HeroBingFaChangeTipDialog";
import { BingFaExchangePreviewDialog } from "../dialog/BingFaExchangePreviewDialog";
import { HeroWarFlagExchangeDialog } from "../dialog/HeroWarFlagExchangeDialog";
import HeroActualPreviewDialog from "../dialog/HeroActualPreviewDialog";
import { CastSoulPreviewDialog } from "../dialog/CastSoulPreviewDialog";
import HeroEvolveSkillInfoDialog from "../dialog/HeroEvolveSkillInfoDialog";
import { HeroEvolveSkillActiveDialog } from "../dialog/HeroEvolveSkillActiveDialog";
import { HeroEvolveDataCenter } from "../data/HeroEvolveDataCenter";
import HeroWarFlagDialog from "../dialog/HeroWarFlagDialog";
import { ItemMacro } from "../../../auto/ItemMacro";
import { HeroOneKeyCompleteDialog } from "../../generalMansion/dialog/HeroOneKeyCompleteDialog";
import HeroInfoDialog2 from "../dialog/HeroInfoDialog2";
import { HeroTuJianInfoDialog2 } from "../dialog/HeroTuJianInfoDialog2";
//控制器不在任何类引用(包括本模块，只在ModuleController里面注册)
//模块里面的类其他模块都不会去引用的，删除某个模块代码正常编译，正常游戏
//这个类是无状态的
export class HeroController extends BaseController {
    constructor() {
        super();
        this._dialog = null;
        this._actListDialog = null;
        this._infoDialog = null;
        this._infoDialog2 = null;
        this._singleInfoDialog = null;
        this._heroShowDialog = null;
        this._heroAttrTipsDialog = null;
        // private _heroInfoMaxLvDialog: HeroInfoMaxLvDialog = null;
        this._heroBingFaPreviewDialog = null;
        this._heroBingFaLevelUpDialog = null;
        this._heroBingFaLevelUpTipDialog = null;
        this._heroBingFaStudyDialog = null;
        this._bingfaDetailTipDialog = null;
        this._bingfaStudyOrLvUpDialog = null;
        this._warFlagSelectActiveDialog = null;
        this._warFlagActiveDialog = null;
        this._warFlagResetDialog = null;
        this._warFlagStagePreDialog = null;
        this._heroStarUpGrowthRoadDialog = null;
        this._heroComposeLogsDialog = null;
        this._heroStarUpConsumeGoDialog = null;
        HeroController.instance = this;
    }
    initModuleListeners() {
        // this.addEventListener(ModuleCommand.OPEN_HERO_DIALOG, this, this.openDialog);
        // this.addEventListener(ModuleCommand.CLOSE_HERO_DIALOG, this, this.closeDialog);
        this.addEventListener("OPEN_ALL_HERO_DIALOG" /* OPEN_ALL_HERO_DIALOG */, this, this.openAllHeroDialog);
        this.addEventListener("CLOSE_ALL_HERO_DIALOG" /* CLOSE_ALL_HERO_DIALOG */, this, this.closeAllHeroDialog);
        //this.addEventListener(ModuleCommand.OPEN_ACT_LIST_DIALOG, this, this.openActListDialog);
        //this.addEventListener(ModuleCommand.CLOSE_ACT_LIST_DIALOG, this, this.closeActListDialog);
        this.registerDialog(HeroActListDialog, "OPEN_ACT_LIST_DIALOG" /* OPEN_ACT_LIST_DIALOG */, "CLOSE_ACT_LIST_DIALOG" /* CLOSE_ACT_LIST_DIALOG */, false);
        this.registerDialog(GodEquipTujianDialog, "OPEN_GOD_EQUIP_TUJIAN_DIALOG" /* OPEN_GOD_EQUIP_TUJIAN_DIALOG */);
        this.registerDialog(GodEquipAddPowerDialog, "OPEN_GOD_EQUIP_ADD_POWER_DIALOG" /* OPEN_GOD_EQUIP_ADD_POWER_DIALOG */);
        // this.addEventListener(ModuleCommand.OPEN_HERO_IFNO_DIALOG, this, this.openInfoDialog);
        // this.addEventListener(ModuleCommand.CLOSE_HERO_IFNO_DIALOG, this, this.closeInfoDialog);
        this.addEventListener("OPEN_HERO_IFNO_DIALOG" /* OPEN_HERO_IFNO_DIALOG */, this, this.openInfoDialog2);
        this.addEventListener("CLOSE_HERO_IFNO_DIALOG" /* CLOSE_HERO_IFNO_DIALOG */, this, this.closeInfoDialog2);
        this.addEventListener("OPEN_HERO_SINGLE_IFNO_DIALOG" /* OPEN_HERO_SINGLE_IFNO_DIALOG */, this, this.openSingleInfoDialog);
        this.addEventListener("CLOSE_HERO_SINGLE_IFNO_DIALOG" /* CLOSE_HERO_SINGLE_IFNO_DIALOG */, this, this.closeSingleInfoDialog);
        //获得英雄展示界面
        this.addEventListener("OPEN_HERO_SHOW_DIALOG" /* OPEN_HERO_SHOW_DIALOG */, this, this.openHeroShowDialog);
        this.addEventListener("CLOSE_HERO_SHOW_DIALOG" /* CLOSE_HERO_SHOW_DIALOG */, this, this.closeHeroShowDialog);
        this.addEventListener("OPEN_HERO_ATTR_DIALOG" /* OPEN_HERO_ATTR_DIALOG */, this, this.openHeroAttrDialog);
        this.addEventListener("CLOSE_HERO_ATTR_DIALOG" /* CLOSE_HERO_ATTR_DIALOG */, this, this.closeHeroAttrDialog);
        this.addEventListener("ATTR_CHANGE_SILVER" /* ATTR_CHANGE_SILVER */, this, HeroDataCenter.instance.CheckHeroInfoRedPoint);
        this.addEventListener("ATTR_CHANGE_HERO_EXP" /* ATTR_CHANGE_HERO_EXP */, this, HeroDataCenter.instance.CheckHeroInfoRedPoint);
        this.addEventListener("HERO_EQUIP_CHANGE_UPDATE" /* HERO_EQUIP_CHANGE_UPDATE */, this, HeroDataCenter.instance.CheckHeroInfoRedPoint);
        this.addEventListener("LEVEL_UP" /* LEVEL_UP */, this, HeroDataCenter.instance.CheckHeroInfoRedPoint);
        this.addEventListener("LEVEL_UP" /* LEVEL_UP */, this, HeroDataCenter.instance.CheckListRedPoint);
        this.addEventListener("ATTR_CHANGE_SILVER" /* ATTR_CHANGE_SILVER */, this, HeroDataCenter.instance.CheckListRedPoint);
        // this.addEventListener(ModuleCommand.OPEN_HERO_INFO_MAXLV_DIALOG, this, this.openHeroInfoMaxLvDialog);
        this.addEventListener("OPEN_HERO_BINGFA_PREVIEW_DIALOG" /* OPEN_HERO_BINGFA_PREVIEW_DIALOG */, this, this.openHeroBingFaPreDialog);
        this.addEventListener("OPEN_HERO_BINGFA_LEVELUP_DIALOG" /* OPEN_HERO_BINGFA_LEVELUP_DIALOG */, this, this.openHeroBingFaLevelUpDialog);
        this.addEventListener("CLOSE_HERO_BINGFA_LEVELUP_DIALOG" /* CLOSE_HERO_BINGFA_LEVELUP_DIALOG */, this, this.closeHeroBingFaLevelUpDialog);
        this.addEventListener("OPEN_HERO_BINGFA_LEVELUP_TIP_DIALOG" /* OPEN_HERO_BINGFA_LEVELUP_TIP_DIALOG */, this, this.openHeroBingFaLevelUpTipDialog);
        this.addEventListener("OPEN_HERO_BINGFA_STUDY_DIALOG" /* OPEN_HERO_BINGFA_STUDY_DIALOG */, this, this.openHeroBingFaStudyDialog);
        this.registerDialog(HeroBingFaChangeDialog, "OPEN_HERO_BINGFA_CHANGE_DIALOG" /* OPEN_HERO_BINGFA_CHANGE_DIALOG */, "CLOSE_HERO_BINGFA_CHANGE_DIALOG" /* CLOSE_HERO_BINGFA_CHANGE_DIALOG */);
        this.registerDialog(HeroBingFaChangeTipDialog, "OPEN_HERO_BINGFA_CHANGE_TIPS_DIALOG" /* OPEN_HERO_BINGFA_CHANGE_TIPS_DIALOG */);
        this.addEventListener("OPEN_BINGFA_DETAIL_TIP_DIALOG" /* OPEN_BINGFA_DETAIL_TIP_DIALOG */, this, this.openBingFaDetailTipDialog);
        // this.addEventListener(ModuleCommand.OPEN_BINGFA_STUDY_LVUP_DIALOG, this, this.openBingFaStudyOrLvUpDialog);
        this.addEventListener("OPEN_SELECT_ACTIVE_WAR_FLAG_DIALOG" /* OPEN_SELECT_ACTIVE_WAR_FLAG_DIALOG */, this, this.openSelectActiveWarFlagDialog);
        this.addEventListener("OPEN_ACTIVE_WAR_FLAG_DIALOG" /* OPEN_ACTIVE_WAR_FLAG_DIALOG */, this, this.openActiveWarFlagDialog);
        this.addEventListener("OPEN_WAR_FLAG_RESET_DIALOG" /* OPEN_WAR_FLAG_RESET_DIALOG */, this, this.openWarFlagResetDialog);
        this.addEventListener("OPEN_WAR_FLAG_STAGE_PRE_DIALOG" /* OPEN_WAR_FLAG_STAGE_PRE_DIALOG */, this, this.openWarFlagStagePreDialog);
        // this.registerDialog(HeroColorUpdateDialog, ModuleCommand.OPEN_HERO_UPDATE_COLOR_DIALOG, ModuleCommand.CLOSE_HERO_UPDATE_COLOR_DIALOG);
        this.registerDialog(HeroDebrisSelectDialog, "OPEN_HERO_DEBRIS_SELECT_DIALOG" /* OPEN_HERO_DEBRIS_SELECT_DIALOG */);
        this.registerDialog(HeroMaterialSelectDialog, "OPEN_HERO_ITEM_SELECT_DIALOG" /* OPEN_HERO_MATERIAL_SELECT_DIALOG */);
        this.registerDialog(HeroDebrisSelectFilterDialog, "OPEN_HERO_DEBRIS_SELECT_FILTER_DIALOG" /* OPEN_HERO_DEBRIS_SELECT_FILTER_DIALOG */);
        this.registerDialog(HeroRecycleDialog, "OPEN_HERO_RECYCLE_DIALOG" /* OPEN_HERO_RECYCLE_DIALOG */);
        /**英雄继承 */
        this.registerDialog(HeroInheritDialog, "OPEN_HERO_INHERIT_DIALOG" /* OPEN_HERO_INHERIT_DIALOG */);
        this.registerDialog(HeroInheritSuccDialog, "OPEN_HERO_INHERIT_SUCC_DIALOG" /* OPEN_HERO_INHERIT_SUCC_DIALOG */);
        this.registerDialog(HeroStageUpdateDialog, "OPEN_HERO_STAGE_UPDATE_DIALOG" /* OPEN_HERO_STAGE_UPDATE_DIALOG */);
        this.registerDialog(HeroResetPreviewDialog, "OPEN_HERO_RESET_PREVIEW_DISLOG" /* OPEN_HERO_RESET_PREVIEW_DISLOG */);
        this.registerDialog(HeroStageUpdateCompleteDialog, "OPEN_HERO_STAGE_UPDATE_COMPLETE_DIALOG" /* OPEN_HERO_STAGE_UPDATE_COMPLETE_DIALOG */);
        this.registerDialog(HeroStarUpdateCompleteDialog, "OPEN_HERO_STAR_UPDATE_COMPLETE_DIALOG" /* OPEN_HERO_STAR_UPDATE_COMPLETE_DIALOG */);
        this.registerDialog(HeroUpgradeCompleteDialog, "OPEN_HERO_UPGRADE_COMPLETE_DIALOG" /* OPEN_HERO_UPGRADE_COMPLETE_DIALOG */);
        this.registerDialog(HeroStarUpdateGrowthRoadDialog, "OPEN_HERO_STAR_UPDATE_GROWTHROAD_DIALOG" /* OPEN_HERO_STAR_UPDATE_GROWTHROAD_DIALOG */);
        this.registerDialog(HeroStarStageUpdateDialog, "OPEN_HERO_STAR_STAGE_UPDATE_DIALOG" /* OPEN_HERO_STAR_STAGE_UPDATE_DIALOG */);
        this.registerDialog(HeroStarStageAdditionDialog, "OPEN_STAR_STAGE_ADDITION_DIALOG" /* OPEN_HERO_STAR_STAGE_ADDITION_DIALOG */);
        this.registerDialog(HeroLockDialog, "OPEN_HERO_LOCK_DIALOG" /* OPEN_HERO_LOCK_DIALOG */);
        this.registerDialog(HeroAttrDescTipsDialog, "OPEN_HERO_ATTR_DESC_TIP_DIALOG" /* OPEN_HERO_ATTR_DESC_TIP_DIALOG */);
        this.registerDialog(HeroTuJianInfoDialog2, "OPEN_HERO_TUJIAN_INFO_DIALOG" /* OPEN_HERO_TUJIAN_INFO_DIALOG */);
        this.registerDialog(CastSoulDialog, "OPEN_CAST_SOUL_DIALOG" /* OPEN_CAST_SOUL_DIALOG */);
        this.registerDialog(CastSoulCompareView, "OPEN_CAST_SOUL_COMPARE_DIALOG" /* OPEN_CAST_SOUL_COMPARE_DIALOG */);
        this.registerDialog(CastSoulSelectActiveDialog, "OPEN_CAST_SOUL_SELECT_HERO_DIALOG" /* OPEN_CAST_SOUL_SELECT_HERO_DIALOG */);
        this.registerDialog(CastSoulPreAttrDialog, "OPEN_CAST_SOUL_PRE_ATTR_DIALOG" /* OPEN_CAST_SOUL_PRE_ATTR_DIALOG */);
        this.registerDialog(CastSoulRecastDialog, "OPEN_RECAST_SOUL_DIALOG" /* OPEN_RECAST_SOUL_DIALOG */);
        this.registerDialog(HeroUpgradeUnlockTipsDialog, "OPEN_HERO_UPGRADE_UNLOCK_TIPS_DIALOG" /* OPEN_HERO_UPGRADE_UNLOCK_TIPS_DIALOG */);
        this.registerDialog(SoulHeroLinkSelectDialog, "OPEN_SOUL_HERO_LINK_SELECT_DIALOG" /* OPEN_SOUL_HERO_LINK_SELECT_DIALOG */);
        this.registerDialog(SoulHeroLinkConfirmDialog, "OPEN_SOUL_HERO_LINK_CONFIRM_DIALOG" /* OPEN_SOUL_HERO_LINK_CONFIRM_DIALOG */);
        this.registerDialog(SoulHeroResetDialog, "OPEN_SOUL_HERO_RESET_DIALOG" /* OPEN_SOUL_HERO_RESET_DIALOG */);
        this.registerDialog(HeroWarFlagExchangeDialog, "OPEN_WAR_FLAG_EXCHANGE_DIALOG" /* OPEN_WAR_FLAG_EXCHANGE_DIALOG */, "CLOSE_WAR_FLAG_EXCHANGE_DIALOG" /* CLOSE_WAR_FLAG_EXCHANGE_DIALOG */);
        this.registerDialog(HeroWarFlagLinkDialog, "OPEN_WAR_FLAG_LINK_DIALOG" /* OPEN_WAR_FLAG_LINK_DIALOG */, "CLOSE_WAR_FLAG_LINK_DIALOG" /* CLOSE_WAR_FLAG_LINK_DIALOG */);
        this.registerDialog(HeroWarFlagLinkAttrDialog, "OPEN_WAR_FLAG_LINK_ATTR_DIALOG" /* OPEN_WAR_FLAG_LINK_ATTR_DIALOG */, "CLOSE_WAR_FLAG_LINK_ATTR_DIALOG" /* CLOSE_WAR_FLAG_LINK_ATTR_DIALOG */);
        this.registerDialog(HeroWarFlagLinkSelectDialog, "OPEN_WAR_FLAG_LINK_SELECT_DIALOG" /* OPEN_WAR_FLAG_LINK_SELECT_DIALOG */, "CLOSE_WAR_FLAG_LINK_SELECT_DIALOG" /* CLOSE_WAR_FLAG_LINK_SELECT_DIALOG */);
        this.registerDialog(HeroWarFlagLinkSuccessDialog, "OPEN_WAR_FLAG_LINK_SUCCESS_DIALOG" /* OPEN_WAR_FLAG_LINK_SUCCESS_DIALOG */);
        this.registerDialog(HeroWarFlagResetLinkDialog, "OPEN_WAR_FLAG_RESET_LINK_DIALOG" /* OPEN_WAR_FLAG_RESET_LINK_DIALOG */);
        this.registerDialog(HeroWarFlagLvUpOrStageDialog, "OPEN_LVUP_STAGE_WAR_FLAG_DIALOG" /* OPEN_LVUP_STAGE_WAR_FLAG_DIALOG */, "CLOSE_LVUP_STAGE_WAR_FLAG_DIALOG" /* CLOSE_LVUP_STAGE_WAR_FLAG_DIALOG */);
        this.registerDialog(HeroWarFlagUnlockLinkTipDialog, "OPEN_WAR_FLAG_UNLOCK_LINK_TIP_DIALOG" /* OPEN_WAR_FLAG_UNLOCK_LINK_TIP_DIALOG */, "CLOSE_WAR_FLAG_UNLOCK_LINK_TIP_DIALOG" /* CLOSE_WAR_FLAG_UNLOCK_LINK_TIP_DIALOG */);
        this.registerDialog(HeroComposeLogsDialog, "OPEN_HERO_COMPOSE_LOGS_DIALOG" /* OPEN_HERO_COMPOSE_LOGS_DIALOG */, "CLOSE_HERO_COMPOSE_LOGS_DIALOG" /* CLOSE_HERO_COMPOSE_LOGS_DIALOG */);
        this.registerDialog(BingFaExchangeSelectHeroDialog, "OPEN_BINGFA_EXCHANGE_SELECT_HERO_DIALOG" /* OPEN_BINGFA_EXCHANGE_SELECT_HERO_DIALOG */, "CLOSE_BINGFA_EXCHANGE_SELECT_HERO_DIALOG" /* CLOSE_BINGFA_EXCHANGE_SELECT_HERO_DIALOG */);
        this.registerDialog(BingFaExchangePreviewDialog, "OPEN_BINGFA_EXCHANGE_PREVIEW_DIALOG" /* OPEN_BINGFA_EXCHANGE_PREVIEW_DIALOG */);
        //成长之路
        this.addEventListener("OPEN_HERO_STAR_UP_GROWTH_ROAD_DIALOG" /* OPEN_HERO_STAR_UP_GROWTH_ROAD_DIALOG */, this, this.openHeroStarUpGrowthRoadDialog);
        this.addEventListener("CLOSE_HERO_STAR_UP_GROWTH_ROAD_DIALOG" /* CLOSE_HERO_STAR_UP_GROWTH_ROAD_DIALOG */, this, this.closeHeroStarUpGrowthRoadDialog);
        this.addEventListener("OPEN_HERO_STAR_UP_CONSUME_GO_DIALOG" /* OPEN_HERO_STAR_UP_CONSUME_GO_DIALOG */, this, this.openHeroStarUpConsumeGoDialog);
        //英雄消耗
        // this.addEventListener(ModuleCommand.OPEN_HERO_COMPOSE_LOGS_DIALOG, this, this.openHeroComposeLogsDialog);
        // this.addEventListener(ModuleCommand.SHOW_HERO_UPGRADE_STAR_LEGENDS_TIPS, this, this.heroUpgradeStarLegendsTip);
        //实力预览
        this.registerDialog(HeroActualPreviewDialog, "OPEN_HERO_ACTUAL_PREVIEW_DIALOG" /* OPEN_HERO_ACTUAL_PREVIEW_DIALOG */);
        this.registerDialog(CastSoulPreviewDialog, "OPEN_CAST_SOUL_PREVIEW_DIALOG" /* OPEN_CAST_SOUL_PREVIEW_DIALOG */);
        this.registerDialog(HeroEvolveSkillInfoDialog, "OPEN_EVOLVE_SKILL_INFO_DIALOG" /* OPEN_EVOLVE_SKILL_INFO_DIALOG */);
        this.registerDialog(HeroEvolveSkillActiveDialog, "OPEN_EVOLVE_SKILL_ACTIVE_DIALOG" /* OPEN_EVOLVE_SKILL_ACTIVE_DIALOG */);
        this.registerDialog(HeroWarFlagDialog, "OPEN_WAR_FLAG_DIALOG" /* OPEN_WAR_FLAG_DIALOG */);
    }
    initNetListeners() {
        this.addSocketListener(SocketCommand.HERO_LIST, this.m_hero_list_toc);
        this.addSocketListener(SocketCommand.HERO_UPDATE_LIST, this.m_hero_update_list_toc);
        this.addSocketListener(SocketCommand.HERO_UPDATE_FIGHT, this.m_hero_update_fight_toc);
        this.addSocketListener(SocketCommand.HERO_UPGRADE, this.m_hero_upgrade_toc);
        this.addSocketListener(SocketCommand.HERO_RECYCLE, this.m_hero_recycle_toc_handler);
        this.addSocketListener(SocketCommand.EQUIP_AUTO_LOAD, this.m_equip_auto_load_toc);
        this.addSocketListener(SocketCommand.EQUIP_AUTO_UNLOAD, this.m_equip_auto_unload_toc);
        this.addSocketListener(SocketCommand.EQUIP_BINGFA_OP, this.m_equip_bingfa_op_toc);
        this.addSocketListener(SocketCommand.EQUIP_BINGFA_EXCHANGE, this.m_equip_bingfa_exchange_toc);
        this.addSocketListener(SocketCommand.HERO_RECYCLE_PREVIEW, this.m_hero_recycle_preview_toc);
        this.addSocketListener(SocketCommand.HERO_MY_RANK, this.m_hero_my_rank_toc);
        this.addSocketListener(SocketCommand.SOUL_HERO_INFO, this.m_soul_hero_info_toc);
        this.addSocketListener(SocketCommand.SOUL_HERO_LINK, this.m_soul_hero_link_toc);
        this.addSocketListener(SocketCommand.SOUL_HERO_RESET, this.m_soul_hero_reset_toc);
        this.addSocketListener(SocketCommand.HERO_ACT_FOURTEEN, this.m_hero_act_fourteen_toc);
        // this.addSocketListener(SocketCommand.HERO_INHERIT, this.m_hero_inherit_toc);
        this.addSocketListener(SocketCommand.HERO_LOCK, this.m_hero_lock_toc);
        this.addSocketListener(SocketCommand.HERO_BAG_EXPANSION, this.m_hero_bag_expansion_toc);
        this.addSocketListener(SocketCommand.WAR_FLAG_ACTIVE, this.m_war_flag_active_toc);
        this.addSocketListener(SocketCommand.WAR_FLAG_INFO, this.m_war_flag_info_toc);
        this.addSocketListener(SocketCommand.WAR_FLAG_OP, this.m_war_flag_op_toc);
        this.addSocketListener(SocketCommand.WAR_FLAG_LINK, this.m_war_flag_link_toc);
        this.addSocketListener(SocketCommand.WAR_FLAG_EXCHANGE, this.m_war_flag_exchange_toc);
        this.addSocketListener(SocketCommand.HERO_HANDBOOK_LIST, this.m_hero_handbook_list_toc);
        this.addSocketListener(SocketCommand.GOD_EQUIP_RECAST, this.m_god_equip_recast_toc);
        this.addSocketListener(SocketCommand.GOD_EQUIP_COMPOSE, this.m_god_equip_compose_toc);
        this.addSocketListener(SocketCommand.GOD_EQUIP_CONVERT, this.m_god_equip_convert_toc);
        this.addSocketListener(SocketCommand.HERO_HANDBOOK_ALL_FETCH, this.m_hero_handbook_all_fetch_toc);
        this.addSocketListener(SocketCommand.CASTING_SOUL_OP, this.m_casting_soul_op_toc);
        this.addSocketListener(SocketCommand.HERO_COST_INFO, this.m_hero_cost_info_toc);
        this.addSocketListener(SocketCommand.HERO_EVOLVE_INFO, this.m_hero_evolve_info_toc);
    }
    //----------协议发送 start ---------
    //----------协议发送 end   ---------
    //----------协议接收 start ---------
    m_hero_bag_expansion_toc(vo) {
        HeroDataCenter.instance.expansionCount = vo.expansion_count;
        HeroDataCenter.instance.heroCapacity = vo.hero_capacity;
        DispatchManager.dispatchEvent("UPDATE_HERO_BAG_EXPANSION" /* UPDATE_HERO_BAG_EXPANSION */, true);
    }
    m_hero_handbook_all_fetch_toc(vo) {
        HeroTuJianDataCenter.instance.sethandbookreward(vo.rewards);
    }
    m_hero_lock_toc(vo) {
        HeroDataCenter.instance.lockHeroIds = vo.lock_hero_ids;
        HeroDataCenter.instance.lockHeroTypes = vo.lock_hero_types;
        DispatchManager.dispatchEvent("UPDATE_HERO_LOCK" /* UPDATE_HERO_LOCK */);
    }
    // private m_hero_inherit_toc(vo: m_hero_inherit_toc) {
    //     DispatchManager.dispatchEvent(ModuleCommand.UPDATE_HERO_INHERIT_SUCC);
    // }
    m_hero_list_toc(vo) {
        HeroDataCenter.instance.setHeroInfo(vo);
        DispatchManager.dispatchEvent("REFRESH_HERO_LIST" /* REFRESH_HERO_LIST */);
        GeneralMansionDataCenter.instance.checkFusionAltarRedPoint();
        HeroDataCenter.instance.checkHeroRecycleRedPoint();
    }
    m_hero_update_list_toc(msg) {
        HeroDataCenter.instance.updateHeroList(msg.update_type, msg.hero_list);
        if (msg.update_type == 1 && msg.is_show_win) {
            //新增
            if (msg.hero_list && msg.hero_list.length > 0) {
                TipsUtil.showHeroGainDialog(msg.hero_list[0].type_id);
            }
        }
        DispatchManager.dispatchEvent("REFRESH_HERO_LIST" /* REFRESH_HERO_LIST */);
        GeneralMansionDataCenter.instance.checkFusionAltarRedPoint();
        HeroDataCenter.instance.checkHeroRecycleRedPoint();
    }
    m_hero_update_fight_toc(msg) {
        this.statistics(msg);
        HeroDataCenter.instance.updateHeroFight(msg);
        //防止刷新太频繁了 更换英雄会很多个英雄的数据过来，延迟一下统一刷新
        if (this.isApplyed) {
            return;
        }
        this.isApplyed = true;
        Laya.timer.once(100, this, this.FrameRefresh);
    }
    FrameRefresh() {
        this.isApplyed = false;
        this.Refresh();
    }
    Refresh() {
        DispatchManager.dispatchEvent("REFRESH_HERO_INFO" /* REFRESH_HERO_INFO */);
    }
    statistics(msg) {
        let from_type = msg.from_type > 0 ? msg.from_type : 0;
        switch (from_type) {
            case 0:
            case FlyFontVo.TYPE_STRONG:
            case FlyFontVo.TYPE_REFINE:
            case FlyFontVo.TYPE_HERO_STAR:
                return;
            default:
                break;
        }
        //只有跟当前选中的英雄相同的时候，才需要飘属性
        if (msg.hero_id != HeroDataCenter.instance.select_hero_id) {
            return;
        }
        let vo = new FlyFontVo();
        vo.type = msg.from_type;
        let oldData = HeroDataCenter.instance.getHero(msg.hero_id);
        if (oldData) {
            let newAttr = msg.fight;
            let oldAttr = oldData.fight;
            for (const key in newAttr) {
                if (key == "attr_list") {
                    let newarr = newAttr[key];
                    let oldarr = oldAttr[key];
                    let newspecialAttr = new Map();
                    let oldspecialAttr = new Map();
                    for (let t = 0; t < newarr.length; t++) {
                        let p = newarr[t];
                        newspecialAttr.set(p.key, p.val);
                    }
                    for (let t = 0; t < oldarr.length; t++) {
                        let p = oldarr[t];
                        oldspecialAttr.set(p.key, p.val);
                    }
                    for (const code of newspecialAttr.keys()) {
                        let oldValue = 0;
                        if (oldspecialAttr.has(code)) {
                            oldValue = oldspecialAttr.get(code);
                        }
                        let val = newspecialAttr.get(code) - oldValue;
                        let cfg = ConfigManager.cfg_fightAttrByIdCache.get(code);
                        if (cfg) {
                            vo.setAttrVO(cfg.attrID, val);
                        }
                    }
                }
                else if (newAttr.hasOwnProperty(key)) {
                    let val = newAttr[key] - oldAttr[key];
                    let cfg = ConfigManager.cfg_fightAttrByKeyCache.get(key);
                    if (cfg) {
                        vo.setAttrVO(cfg.attrID, val);
                    }
                }
            }
        }
        TipsUtil.showFlyAttr(vo);
    }
    ////提升类型：0=只更新技能,1=升级，2=升阶，3=升星 6=只显示预览 7=高级直升 8=升级同调等级
    m_hero_upgrade_toc(msg) {
        let heroInfo = HeroDataCenter.instance.getHero(msg.hero_id);
        let oldHero = ObjectUtil.clone(heroInfo);
        let oldStage = heroInfo ? heroInfo.stage : 0;
        let oldStar = heroInfo ? heroInfo.star : 0;
        let oldSkillList = heroInfo ? heroInfo.skill_list.concat() : [];
        let oldStarStage = heroInfo ? heroInfo.star_stage.concat() : [];
        if (msg.up_type != 6) {
            HeroDataCenter.instance.upgradeHero(msg.hero_id, msg.up_type, msg.new_level);
        }
        let data = {
            msg: msg,
            oldHero: oldHero,
            isOnlyHeroItem: false,
            isShowLevel: true,
            isShowSkill: true,
            isShowStage: false,
            isShowStarStage: oldStar >= HeroConsts.openStarStageLV,
            isShowSoulLv: false,
            isActiveSkill: false,
        };
        if (msg.up_type == 1) {
        }
        else if (msg.up_type == 2) {
            let isActiveSkill = null;
            msg.skill_list.forEach(function (newKV) {
                if (isActiveSkill) {
                    return;
                }
                for (let i = 0; i < oldSkillList.length; ++i) {
                    let oldKV = oldSkillList[i];
                    if (oldKV.key == newKV.key) {
                        return;
                    }
                }
                isActiveSkill = true;
            });
            if (!isActiveSkill) {
                this.dispatchEvent("OPEN_HERO_STAGE_UPDATE_COMPLETE_DIALOG" /* OPEN_HERO_STAGE_UPDATE_COMPLETE_DIALOG */, [msg, oldStage]);
            }
            else {
                data.isOnlyHeroItem = true;
                data.isActiveSkill = isActiveSkill;
                data.isShowStage = isActiveSkill;
                data.isShowLevel = !isActiveSkill;
                this.dispatchEvent("OPEN_HERO_STAR_UPDATE_COMPLETE_DIALOG" /* OPEN_HERO_STAR_UPDATE_COMPLETE_DIALOG */, [msg, oldStage, oldStar, oldSkillList, oldStarStage, isActiveSkill]);
                // this.dispatchEvent(ModuleCommand.OPEN_HERO_UPGRADE_COMPLETE_DIALOG, data);
            }
        }
        else if (msg.up_type == 3 || msg.up_type == 4) {
            if (msg.is_auto) {
                HeroDataCenter.instance.backOneKeyUpStarList.set(msg.hero_id, msg.hero_id);
                if (!this.getDialogInstance(HeroOneKeyCompleteDialog)) {
                    this.dispatchEvent("OPEN_HERO_ONE_KEY_COMPLETE_DIALOG" /* OPEN_HERO_ONE_KEY_COMPLETE_DIALOG */, msg.hero_id);
                }
            }
            else {
                this.dispatchEvent("OPEN_HERO_STAR_UPDATE_COMPLETE_DIALOG" /* OPEN_HERO_STAR_UPDATE_COMPLETE_DIALOG */, [msg, oldStage, oldStar, oldSkillList, oldStarStage]);
            }
            // this.dispatchEvent(ModuleCommand.OPEN_HERO_UPGRADE_COMPLETE_DIALOG, data);
        }
        else if (msg.up_type == 5) {
            this.dispatchEvent("OPEN_HERO_STAR_UPDATE_COMPLETE_DIALOG" /* OPEN_HERO_STAR_UPDATE_COMPLETE_DIALOG */, [msg, oldStage, oldStar, oldSkillList, oldStarStage]);
            // this.dispatchEvent(ModuleCommand.OPEN_HERO_UPGRADE_COMPLETE_DIALOG, data);
            this.dispatchEvent("UPDATE_STAR_UP_CONSUME_INFO" /* UPDATE_STAR_UP_CONSUME_INFO */);
            if (msg.new_level >= 11) {
                this.dispatchEvent("CLOSE_HERO_STAR_UP_GROWTH_ROAD_DIALOG" /* CLOSE_HERO_STAR_UP_GROWTH_ROAD_DIALOG */);
            }
        }
        else if (msg.up_type == 6) {
            this.dispatchEvent("OPEN_HERO_STAR_UPDATE_GROWTHROAD_DIALOG" /* OPEN_HERO_STAR_UPDATE_GROWTHROAD_DIALOG */, [msg, oldStage, oldStar, oldSkillList, oldStarStage]);
        }
        else if (msg.up_type == 7) {
            this.dispatchEvent("OPEN_HERO_STAR_UPDATE_GROWTHROAD_DIALOG" /* OPEN_HERO_STAR_UPDATE_GROWTHROAD_DIALOG */, [msg, oldStage, oldStar, oldSkillList, oldStarStage]);
        }
        else if (msg.up_type == 8) { //能级
            data.isShowLevel = false;
            data.isShowSkill = false;
            data.isShowStage = false;
            data.isShowStarStage = false;
            data.isShowSoulLv = true;
            data.isActiveSkill = false;
            this.dispatchEvent("OPEN_HERO_UPGRADE_COMPLETE_DIALOG" /* OPEN_HERO_UPGRADE_COMPLETE_DIALOG */, data);
        }
        if (msg.up_type != 6) {
            DispatchManager.dispatchEvent("REFRESH_HERO_INFO" /* REFRESH_HERO_INFO */);
            DispatchManager.dispatchEvent("ON_HERO_UPGRADE" /* ON_HERO_UPGRADE */, msg.up_type);
            //技能变化
            if (msg.skill_list) {
                SkillDataCenter.ins.setHeroSkillList(msg.hero_id, msg.skill_list);
            }
        }
    }
    // private heroUpgradeStarLegendsTip(msg: m_hero_upgrade_toc) {
    //     let heroInfo = HeroDataCenter.instance.getHero(msg.hero_id);
    //     let cfg = ConfigManager.cfg_hero_baseCache.get(heroInfo.type_id);
    //     if(cfg.pillar != 1){
    //         return ;
    //     }
    //     if(msg.new_level < 6){
    //         return ;
    //     }
    //     if(SettingDataCenter.instance.getVal("heroUpgradeStarLegendsTip") > 0){
    //         return ;
    //     }
    //     for (const otherHero of HeroDataCenter.instance.hero_list) {
    //         let otherHeroCfg = ConfigManager.cfg_hero_baseCache.get(otherHero.type_id);
    //         if (otherHeroCfg.pillar != 1 && otherHero.power > heroInfo.power && otherHero.star >= heroInfo.star) {
    //             let desc = "现在可将其他英雄的等级、进阶、装备、铸魂、纹章、天赋、神装等养成状态传承给本英雄，是否前往传承？"
    //             TipsUtil.showDialog(this, desc, "提示", ()=>{
    //                 this.dispatchEvent(ModuleCommand.OPEN_GENERAL_MANSION_DIALOG, {child_id: GeneralMansionDataCenter.SUB_FUSION_LEGENDS});
    //             }, { okName: "前往", onlyOneTipKey: "heroUpgradeStarLegendsTip" });
    //             return ;
    //         }
    //     }
    // }
    m_hero_recycle_toc_handler(msg) {
        TipsUtil.showTips(window.iLang.L2_HERO_CHONG_SHENG_CHENG_GONG_ch25.il());
        if (msg.hero_id > 0) {
            HeroDataCenter.instance.resetCount = msg.recycle_count;
        }
        // DispatchManager.dispatchEvent(ModuleCommand.CLOSE_EQUIP_RETURN_DIALOG);
        DispatchManager.dispatchEvent("HERO_RECYCLE" /* HERO_RECYCLE */);
        // EquipDataCenter.instance.isRecycleHeroTabRedPoint();
    }
    // private m_hero_breed_toc(msg: m_hero_breed_toc) {
    //     //获取当前的养成位列表
    //     let breedList: number[] = HeroDataCenter.instance.getLineUpListByType(HeroDataCenter.LINE_UP_BREED);
    //     let old_hero_id: number = breedList[msg.breed_sn - 1] || 0;
    //     let tempPos: number = -1;
    //     //判断是否是换将
    //     for (let i: number = 0; i < breedList.length; i++) {
    //         if (msg.hero_id == breedList[i]) {
    //             tempPos = i;
    //             break;
    //         }
    //     }
    //     breedList[msg.breed_sn - 1] = msg.hero_id;
    //     if (tempPos != -1) {
    //         breedList[tempPos] = old_hero_id;
    //     }
    //
    //     HeroDataCenter.instance.setLineUpListByType(breedList, HeroDataCenter.LINE_UP_BREED);
    //     msg.new_lineups.forEach(lineUpInfo => {
    //         HeroDataCenter.instance.setLineUpListByType(lineUpInfo.hero_id_list, lineUpInfo.type);
    //     });
    //     //判断是否有缘分激活
    //     HeroDataCenter.instance.onChangeBreedKarma(msg.breed_sn - 1, tempPos);
    //
    //     EquipDataCenter.instance.isRecycleHeroTabRedPoint();
    // }
    m_equip_auto_load_toc(msg) {
        ForgeDataCenter.instance.RefreshAutoLoadEquipInfo(msg.hero_id, msg.load_equips, msg.unload_equips);
        //判断装备激活的缘分
        // HeroKarmaDataCenter.instance.onChangeEquipsKarma(msg.hero_id, msg.load_equips);
    }
    m_equip_auto_unload_toc(msg) {
        ForgeDataCenter.instance.RefreshAutoUnLoadEquipInfo(msg.hero_id, msg.unload_equips);
        //判断装备激活的缘分
        DispatchManager.dispatchEvent("UPDATE_HERO_KARMA" /* UPDATE_HERO_KARMA */);
        Laya.timer.once(200, this, () => { this.dispatchEvent("HERO_EQUIP_CHANGE_UPDATE" /* HERO_EQUIP_CHANGE_UPDATE */); });
    }
    m_equip_bingfa_op_toc(msg) {
        if (msg.new_goods.kind == ItemMacro.ITEM_KIND_BING_FA) {
            DispatchManager.dispatchEvent("OPEN_BINGFA_STUDY_LVUP_DIALOG" /* OPEN_BINGFA_STUDY_LVUP_DIALOG */, msg.new_goods);
        }
        DispatchManager.dispatchEvent("UPDATE_HERO_BINGFA" /* UPDATE_HERO_BINGFA */, msg.new_goods);
    }
    m_equip_bingfa_exchange_toc(msg) {
        TipsUtil.showTips(window.iLang.L2_HU_HUAN_CHENG_GONG.il());
    }
    m_war_flag_active_toc(msg) {
        DispatchManager.dispatchEvent("OPEN_ACTIVE_WAR_FLAG_DIALOG" /* OPEN_ACTIVE_WAR_FLAG_DIALOG */, msg.nation);
    }
    m_war_flag_info_toc(msg) {
        HeroDataCenter.instance.war_flag_infos = msg.list;
        HeroDataCenter.instance.war_flag_link_infos = msg.link_list;
        HeroDataCenter.instance.war_flag_unlink_times = msg.unlink_times;
        HeroDataCenter.instance.war_flag_exchange_times = msg.exchange_times;
        HeroDataCenter.instance.checkAllNationWarFlagRedPoint();
        DispatchManager.dispatchEvent("UPDATE_WAR_FLAG_INFO" /* UPDATE_WAR_FLAG_INFO */);
    }
    m_war_flag_op_toc(msg) {
        HeroDataCenter.instance.checkAllNationWarFlagRedPoint();
        DispatchManager.dispatchEvent("UPDATE_WAR_FLAG_LEVEL_UPGRADE" /* UPDATE_WAR_FLAG_LEVEL_UPGRADE */, msg);
        if (msg.op_type == 3) {
            return;
        }
        if (!msg.is_up && msg.op_type == 2) {
            let attrVo = HeroDataCenter.instance.getWarFlagStageFrontBackAttr(msg.nation, true, false);
            TipsUtil.showTips(attrVo.name + "+" + attrVo.valString);
            return;
        }
        if (!msg.is_up && msg.op_type == 1) {
            let tips = HeroDataCenter.instance.getWarFlagLvFrontAttr(msg.nation);
            TipsUtil.showTips(tips);
            return;
        }
        DispatchManager.dispatchEvent("OPEN_LVUP_STAGE_WAR_FLAG_DIALOG" /* OPEN_LVUP_STAGE_WAR_FLAG_DIALOG */, msg);
    }
    m_war_flag_link_toc(msg) {
        console.log("m_war_flag_link_toc", msg);
        if (msg.op_type == 3 || msg.op_type == 4) {
            DispatchManager.dispatchEvent("UPDATE_WAR_FLAG_LINK_POWER" /* UPDATE_WAR_FLAG_LINK_POWER */, { power: msg.power, new_power: msg.new_power });
            return;
        }
        if (msg.op_type == 1) {
            DispatchManager.dispatchEvent("OPEN_WAR_FLAG_LINK_SUCCESS_DIALOG" /* OPEN_WAR_FLAG_LINK_SUCCESS_DIALOG */, { info: msg });
            DispatchManager.dispatchEvent("UPDATE_WAR_FLAG_INFO" /* UPDATE_WAR_FLAG_INFO */);
            return;
        }
        if (msg.op_type == 2) {
            DispatchManager.dispatchEvent("UPDATE_WAR_FLAG_LINK_NATION" /* UPDATE_WAR_FLAG_LINK_NATION */, 0);
            DispatchManager.dispatchEvent("CLOSE_WAR_FLAG_LINK_ATTR_DIALOG" /* CLOSE_WAR_FLAG_LINK_ATTR_DIALOG */);
            // DispatchManager.dispatchEvent(ModuleCommand.UPDATE_WAR_FLAG_INFO);
            return;
        }
    }
    m_war_flag_exchange_toc(msg) {
        TipsUtil.showTips(window.iLang.L2_ZHI_HUAN_CHENG_GONG.il());
    }
    m_hero_handbook_list_toc(msg) {
        HeroTuJianDataCenter.instance.setHeroTuJianInfo(msg);
        HeroTuJianDataCenter.instance.checkRedPoint();
        DispatchManager.dispatchEvent("UPDATE_HERO_TUJIAN_LIST" /* UPDATE_HERO_TUJIAN_LIST */, msg);
    }
    m_god_equip_recast_toc(msg) {
        console.log("m_god_equip_recast_toc", msg);
    }
    m_god_equip_compose_toc(msg) {
        console.log("m_god_equip_compose_toc", msg);
    }
    m_god_equip_convert_toc(msg) {
        console.log("m_god_equip_convert_toc", msg);
    }
    m_hero_recycle_preview_toc(msg) {
        DispatchManager.dispatchEvent("UPDATE_HERO_RECYCLE_PREVIEW" /* UPDATE_HERO_RECYCLE_PREVIEW */, msg);
    }
    m_hero_my_rank_toc(msg) {
        HeroDataCenter.instance.heroMyRankList = msg.ranks;
        DispatchManager.dispatchEvent("UPDATE_HERO_MY_RANK" /* UPDATE_HERO_MY_RANK */);
    }
    m_casting_soul_op_toc(msg) {
        if (msg.op_type == 2) {
            DispatchManager.dispatchEvent("SHOW_CAST_SOUL_LVUP_EFFECT" /* SHOW_CAST_SOUL_LVUP_EFFECT */, msg);
        }
        DispatchManager.dispatchEvent("UPDATE_CAST_SOUL_INFO" /* UPDATE_CAST_SOUL_INFO */, msg);
    }
    m_soul_hero_info_toc(msg) {
        let isFirstSet = !HeroDataCenter.instance.soulHeroLinkInfo;
        let oldUnlockList = HeroDataCenter.instance.soulHeroUnlockList;
        HeroDataCenter.instance.soulHeroLinkInfo = msg;
        DispatchManager.dispatchEvent("UPDATE_SOUL_HERO_INFO" /* UPDATE_SOUL_HERO_INFO */, msg);
        //查找新解锁同心的异能英雄
        if (!isFirstSet) {
            let temp = {};
            let newUnlockList = HeroDataCenter.instance.soulHeroUnlockList;
            for (let i = 0; i < newUnlockList.length; ++i) {
                let oldKV = oldUnlockList[i];
                if (oldKV)
                    temp[oldKV.key] = !!temp[oldKV.key] ? false : oldKV;
                let newKV = newUnlockList[i];
                if (newKV)
                    temp[newKV.key] = !!temp[newKV.key] ? false : newKV;
            }
            let diffKV = null;
            for (let key in temp) {
                if (temp[key]) {
                    diffKV = temp[key];
                    break;
                }
            }
            if (diffKV) {
                let heroCfg = ConfigManager.cfg_hero_baseCache.get(diffKV.key);
                if (heroCfg) {
                    TipsUtil.showTips(window.iLang.L2_ch23_P0_ch24_YI_CHENG_GONG_JIE_SUO_TONG_XIN_ch31_KE_YU_TONG_DIAO_HERO_TONG_BU.il([heroCfg.name]));
                }
                DispatchManager.dispatchEvent("UPDATE_SOUL_HERO_UNLOCK" /* UPDATE_SOUL_HERO_UNLOCK */, HeroDataCenter.instance.select_soul_hero_id);
                HeroDataCenter.instance.select_soul_hero_id = null;
            }
        }
    }
    m_soul_hero_link_toc(msg) {
        DispatchManager.dispatchEvent("UPDATE_SOUL_HERO_LINK" /* UPDATE_SOUL_HERO_LINK */, msg);
    }
    m_soul_hero_reset_toc(msg) {
        DispatchManager.dispatchEvent("UPDATE_SOUL_HERO_RESET" /* UPDATE_SOUL_HERO_RESET */, msg);
    }
    m_hero_act_fourteen_toc(msg) {
        HeroDataCenter.instance.fourteenInfo = msg;
        DispatchManager.dispatchEvent("UPDATE_HERO_ACT_FOURTEEN_LIST" /* UPDATE_HERO_ACT_FOURTEEN_LIST */, msg);
    }
    m_hero_cost_info_toc(msg) {
        HeroComposeLogsDataCenter.instance.list = msg.list;
        HeroComposeLogsDataCenter.instance.nation = msg.nation;
        DispatchManager.dispatchEvent("UPDATE_HERO_COMPOSELOGS_INFO" /* UPDATE_HERO_COMPOSELOGS_INFO */);
    }
    m_hero_evolve_info_toc(msg) {
        HeroEvolveDataCenter.ins.evolveInfo = msg;
        DispatchManager.dispatchEvent("UPDATE_EVOLVE_SKILL_INFO" /* UPDATE_EVOLVE_SKILL_INFO */);
    }
    //----------协议接收 end   ---------
    reset() {
        super.reset();
    }
    //打开“英雄阵容”的按钮
    openDialog(...param) {
        if (this._dialog && !this._dialog.destroyed) {
            this._dialog.reOpen(param);
            return;
        }
        this._dialog = new HeroAllListDialog();
        this._dialog.on(Event.CLOSE, this, function () {
            this._dialog = null;
        });
        this._dialog.open(false, param);
    }
    closeDialog() {
        if (this._dialog && !this._dialog.destroyed) {
            this._dialog.close();
            this._dialog = null;
        }
    }
    //打开“所有英雄”的按钮
    openAllHeroDialog(...param) {
        HeroController.instance.openDialog(param);
    }
    closeAllHeroDialog() {
        HeroController.instance.closeDialog();
    }
    //打开“所有英雄”的按钮
    openActListDialog(param) {
        if (this._actListDialog && !this._actListDialog.destroyed) {
            this._actListDialog.reOpen(param);
            return;
        }
        this._actListDialog = new HeroActListDialog();
        this._actListDialog.on(Event.CLOSE, this, function () {
            this._actListDialog = null;
        });
        this._actListDialog.open(false, param, false);
    }
    closeActListDialog() {
        if (this._actListDialog && !this._actListDialog.destroyed) {
            this._actListDialog.close();
            this._actListDialog = null;
        }
    }
    //打开“所有英雄”的按钮
    openHeroAttrDialog(param) {
        if (this._heroAttrTipsDialog && !this._heroAttrTipsDialog.destroyed) {
            this._heroAttrTipsDialog.reOpen(param);
            return;
        }
        this._heroAttrTipsDialog = new HeroAttrTipsDialog(param);
        this._heroAttrTipsDialog.on(Event.CLOSE, this, function () {
            this._heroAttrTipsDialog = null;
        });
        this._heroAttrTipsDialog.open(false, param);
    }
    closeHeroAttrDialog() {
        if (this._heroAttrTipsDialog && !this._heroAttrTipsDialog.destroyed) {
            this._heroAttrTipsDialog.close();
            this._heroAttrTipsDialog = null;
        }
    }
    openInfoDialog2(...param) {
        if (this._infoDialog2 && !this._infoDialog2.destroyed) {
            this._infoDialog2.reOpen(param);
            return;
        }
        this._infoDialog2 = new HeroInfoDialog2();
        this._infoDialog2.on(Event.CLOSE, this, function () {
            HeroDataCenter.instance.select_type_id = 0;
            HeroDataCenter.instance.select_hero_id = 0;
            this._infoDialog2 = null;
        });
        if (param != null && param[0].child_id != null) //通过获取途径进入的,重新组装一下
         {
            let obj = {
                hero: null,
                type_id: 0,
                index: param[0].child_id,
                isShowSingleNation: false
            };
            param[0] = obj;
        }
        this._infoDialog2.open(false, param);
    }
    closeInfoDialog2() {
        if (this._infoDialog2 && !this._infoDialog2.destroyed) {
            HeroDataCenter.instance.select_type_id = 0;
            HeroDataCenter.instance.select_hero_id = 0;
            this._infoDialog2.close();
            this._infoDialog2 = null;
        }
    }
    closeInfoDialog() {
        if (this._infoDialog && !this._infoDialog.destroyed) {
            HeroDataCenter.instance.select_type_id = 0;
            HeroDataCenter.instance.select_hero_id = 0;
            this._infoDialog.close();
            this._infoDialog = null;
        }
    }
    openSingleInfoDialog(param) {
        this.closeInfoDialog();
        if (this._singleInfoDialog && !this._singleInfoDialog.destroyed) {
            //这个界面比较特殊，需要刷新显示内容
            if (param) {
                this._singleInfoDialog.ShowSingleHeroList(param);
            }
            return;
        }
        ;
        this._singleInfoDialog = new HeroSingleInfoDialog();
        this._singleInfoDialog.on(Event.CLOSE, this, function () {
            HeroDataCenter.instance.select_type_id = 0;
            this._singleInfoDialog = null;
        });
        this._singleInfoDialog.open(false, param);
    }
    closeSingleInfoDialog() {
        if (this._singleInfoDialog && !this._singleInfoDialog.destroyed) {
            HeroDataCenter.instance.select_type_id = 0;
            this._singleInfoDialog.close();
            this._singleInfoDialog = null;
        }
    }
    closeHeroShowDialog() {
        if (this._heroShowDialog && !this._heroShowDialog.destroyed) {
            this._heroShowDialog.close();
            this._heroShowDialog = null;
        }
    }
    openHeroBingFaPreDialog(param) {
        if (this._heroBingFaPreviewDialog && !this._heroBingFaPreviewDialog.destroyed)
            return;
        this._heroBingFaPreviewDialog = new HeroBingFaPreviewDialog();
        this._heroBingFaPreviewDialog.on(Event.CLOSE, this, function () {
            this._heroBingFaPreviewDialog = null;
        });
        this._heroBingFaPreviewDialog.open(false, param);
    }
    openHeroBingFaLevelUpDialog(param) {
        if (this._heroBingFaLevelUpDialog && !this._heroBingFaLevelUpDialog.destroyed)
            return;
        this._heroBingFaLevelUpDialog = new HeroBingFaLevelUpDialog();
        this._heroBingFaLevelUpDialog.on(Event.CLOSE, this, function () {
            this._heroBingFaLevelUpDialog = null;
        });
        this._heroBingFaLevelUpDialog.open(false, param);
    }
    closeHeroBingFaLevelUpDialog() {
        if (this._heroBingFaLevelUpDialog && !this._heroBingFaLevelUpDialog.destroyed) {
            this._heroBingFaLevelUpDialog.close();
            this._heroBingFaLevelUpDialog = null;
        }
    }
    openHeroBingFaLevelUpTipDialog(param) {
        if (this._heroBingFaLevelUpTipDialog && !this._heroBingFaLevelUpTipDialog.destroyed)
            return;
        this._heroBingFaLevelUpTipDialog = new HeroBingFaLevelUpTipDialog();
        this._heroBingFaLevelUpTipDialog.on(Event.CLOSE, this, function () {
            this._heroBingFaLevelUpTipDialog = null;
        });
        this._heroBingFaLevelUpTipDialog.open(false, param);
    }
    openHeroBingFaStudyDialog(param) {
        if (this._heroBingFaStudyDialog && !this._heroBingFaStudyDialog.destroyed)
            return;
        this._heroBingFaStudyDialog = new HeroBingFaStudyDialog();
        this._heroBingFaStudyDialog.on(Event.CLOSE, this, function () {
            this._heroBingFaStudyDialog = null;
        });
        this._heroBingFaStudyDialog.open(false, param);
    }
    openBingFaDetailTipDialog(param) {
        if (this._bingfaDetailTipDialog && !this._bingfaDetailTipDialog.destroyed)
            return;
        this._bingfaDetailTipDialog = new BingFaDetailTipDialog();
        this._bingfaDetailTipDialog.on(Event.CLOSE, this, function () {
            this._bingfaDetailTipDialog = null;
        });
        this._bingfaDetailTipDialog.open(false, param);
    }
    openBingFaStudyOrLvUpDialog(param) {
        if (this._bingfaStudyOrLvUpDialog && !this._bingfaStudyOrLvUpDialog.destroyed)
            return;
        this._bingfaStudyOrLvUpDialog = new BingFaStudyOrLvUpDialog();
        this._bingfaStudyOrLvUpDialog.on(Event.CLOSE, this, function () {
            this._bingfaStudyOrLvUpDialog = null;
        });
        this._bingfaStudyOrLvUpDialog.open(false, param);
    }
    openSelectActiveWarFlagDialog(param) {
        if (this._warFlagSelectActiveDialog && !this._warFlagSelectActiveDialog.destroyed)
            return;
        this._warFlagSelectActiveDialog = new HeroWarFlagSelectActiveDialog();
        this._warFlagSelectActiveDialog.on(Event.CLOSE, this, function () {
            this._warFlagSelectActiveDialog = null;
        });
        this._warFlagSelectActiveDialog.open(false, param);
    }
    openActiveWarFlagDialog(param) {
        if (this._warFlagActiveDialog && !this._warFlagActiveDialog.destroyed)
            return;
        this._warFlagActiveDialog = new HeroWarFlagActiveDialog();
        this._warFlagActiveDialog.on(Event.CLOSE, this, function () {
            this._warFlagActiveDialog = null;
        });
        this._warFlagActiveDialog.open(false, param);
    }
    openWarFlagResetDialog(param) {
        if (this._warFlagResetDialog && !this._warFlagResetDialog.destroyed)
            return;
        this._warFlagResetDialog = new HeroWarFlagResetDialog();
        this._warFlagResetDialog.on(Event.CLOSE, this, function () {
            this._warFlagResetDialog = null;
        });
        this._warFlagResetDialog.open(false, param);
    }
    openWarFlagStagePreDialog(param) {
        if (this._warFlagStagePreDialog && !this._warFlagStagePreDialog.destroyed)
            return;
        this._warFlagStagePreDialog = new HeroWarFlagStagePreDialog();
        this._warFlagStagePreDialog.on(Event.CLOSE, this, function () {
            this._warFlagStagePreDialog = null;
        });
        this._warFlagStagePreDialog.open(false, param);
    }
    openHeroShowDialog(param) {
        if (this._heroShowDialog && !this._heroShowDialog.destroyed)
            return;
        this._heroShowDialog = new HeroShowDialog();
        this._heroShowDialog.on(Event.CLOSE, this, function () {
            this._heroShowDialog = null;
        });
        this._heroShowDialog.open(false, param);
    }
    openHeroStarUpGrowthRoadDialog(param) {
        if (this._heroStarUpGrowthRoadDialog && !this._heroStarUpGrowthRoadDialog.destroyed)
            return;
        this._heroStarUpGrowthRoadDialog = new HeroStarUpGrowthRoadDialog();
        this._heroStarUpGrowthRoadDialog.on(Event.CLOSE, this, function () {
            this._heroStarUpGrowthRoadDialog = null;
        });
        this._heroStarUpGrowthRoadDialog.open(false, param);
    }
    closeHeroStarUpGrowthRoadDialog() {
        if (this._heroStarUpGrowthRoadDialog && !this._heroStarUpGrowthRoadDialog.destroyed) {
            this._heroStarUpGrowthRoadDialog.close();
            this._heroStarUpGrowthRoadDialog = null;
        }
    }
    openHeroStarUpConsumeGoDialog(param) {
        if (this._heroStarUpConsumeGoDialog && !this._heroStarUpConsumeGoDialog.destroyed)
            return;
        this._heroStarUpConsumeGoDialog = new HeroStarUpConsumeGoDialog();
        this._heroStarUpConsumeGoDialog.on(Event.CLOSE, this, function () {
            this._heroStarUpConsumeGoDialog = null;
        });
        this._heroStarUpConsumeGoDialog.open(false, param);
    }
    openHeroComposeLogsDialog(param) {
        if (this._heroComposeLogsDialog && !this._heroComposeLogsDialog.destroyed) {
            this._heroComposeLogsDialog.reOpen(param);
            return;
        }
        this._heroComposeLogsDialog = new HeroComposeLogsDialog();
        this._heroComposeLogsDialog.on(Event.CLOSE, this, function () {
            this._heroComposeLogsDialog = null;
        });
        this._heroComposeLogsDialog.open(false, param);
    }
}
