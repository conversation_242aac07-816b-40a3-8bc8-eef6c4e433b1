//数据不要用静态类型的
//可以在本模块引用，不可跨模块引用
//本模块引用的时候不要缓存instance，每次通过instance获取即可
import { MatchConst } from "../../../auto/ConstAuto";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { Connection } from "../../../net/Connection";
import { m_lord_treasure_op_tos } from "../../../proto/line/m_lord_treasure_op_tos";
import { GameUtil } from "../../../util/GameUtil";
import { ELordGroupType, LordDataCenter } from "../../Lord/data/LordDataCenter";
/**领主装备菜单 */
export var ELordTreasureMenuType;
(function (ELordTreasureMenuType) {
    /**宝物背包 */
    ELordTreasureMenuType[ELordTreasureMenuType["MENU_BAG"] = 1] = "MENU_BAG";
    /**宝物打造 */
    ELordTreasureMenuType[ELordTreasureMenuType["MENU_WORK"] = 2] = "MENU_WORK";
})(ELordTreasureMenuType || (ELordTreasureMenuType = {}));
export var ELordTreasureOpType;
(function (ELordTreasureOpType) {
    /**打造宝物 */
    ELordTreasureOpType[ELordTreasureOpType["OP_WORK"] = 1] = "OP_WORK";
    /**强化宝物 */
    ELordTreasureOpType[ELordTreasureOpType["OP_STREANGTH"] = 2] = "OP_STREANGTH";
    /**锻造宝物 */
    ELordTreasureOpType[ELordTreasureOpType["OP_FORGE"] = 3] = "OP_FORGE";
    /**重置宝物 */
    ELordTreasureOpType[ELordTreasureOpType["OP_REMAKE"] = 4] = "OP_REMAKE";
    /**装备宝物 */
    ELordTreasureOpType[ELordTreasureOpType["OP_LOAD"] = 5] = "OP_LOAD";
    /**卸下宝物 */
    ELordTreasureOpType[ELordTreasureOpType["OP_UNLOAD"] = 6] = "OP_UNLOAD";
    /**分享宝物 */
    ELordTreasureOpType[ELordTreasureOpType["OP_SHARE"] = 7] = "OP_SHARE";
})(ELordTreasureOpType || (ELordTreasureOpType = {}));
export class LordTreasureDataCenter {
    static get instance() {
        if (LordTreasureDataCenter._instance == null) {
            LordTreasureDataCenter._instance = new LordTreasureDataCenter();
        }
        return LordTreasureDataCenter._instance;
    }
    reset() {
        LordTreasureDataCenter._instance = null;
    }
    get allLordTreasureList() {
        return LordDataCenter.instance.info.lord_treasure_list;
    }
    getLordTreasureById(id) {
        return this.allLordTreasureList.find(v => v.id == id);
    }
    getLordTreasureByTypeId(typeId) {
        return this.allLordTreasureList.find(v => v.type_id == typeId);
    }
    /**
     * 宝物排列顺序：已穿戴>等级（从高到低）>可锻造次数（从少到多）>宝物ID（从大到小）
     * @param a
     * @param b
     * @param groupType
     * @param matchType
     * @returns
     */
    sortLordTreasure(a, b, matchType = MatchConst.MATCH_TYPE_MAIN_BATTLE) {
        var _a, _b;
        let indexA = (_a = this.getGroupTypeInfoBy(a.id, matchType)) === null || _a === void 0 ? void 0 : _a.index;
        let indexB = (_b = this.getGroupTypeInfoBy(b.id, matchType)) === null || _b === void 0 ? void 0 : _b.index;
        if (indexA != indexB) {
            return indexB - indexA;
        }
        if (a.level != b.level) {
            return b.level - a.level;
        }
        if (a.forge_times != b.forge_times) {
            return a.forge_times - b.forge_times;
        }
        return b.id - a.id;
    }
    /**通过treasureID获取主副队信息 */
    getGroupTypeInfoBy(treasureID, matchType) {
        if (LordDataCenter.instance.lordLineUpList.has(matchType)) {
            for (const [index, item] of LordDataCenter.instance.lordLineUpList.get(matchType).entries()) {
                if (item.lord_treasure == treasureID) {
                    return item;
                }
            }
        }
        else {
            return null;
        }
    }
    /**获取领主宝物总强化效果 */
    getTreasureEntry(type_id) {
        let treasureEntryList = [];
        let newTreasureEntry = 0;
        CfgCacheMapMgr.cfg_lord_treasure_levelBytreasure_idCache.get(type_id).forEach(v => {
            let _treasure_entry = GameUtil.parseCfgByField(v, "treasure_entry_", { ingoreEmpty: true });
            if (_treasure_entry.length > newTreasureEntry) {
                treasureEntryList.push(v);
                newTreasureEntry++;
            }
        });
        return treasureEntryList;
    }
    //--------------协议发送------------------------
    /**
     * 发送领主宝物操作协议
     * @param op_type - 宝物操作类型，使用 ELordTreasureOpType 枚举值
     * @param lord_id - 领主 ID，默认为 0
     * @param treasure_id - 宝物 ID，默认为 0
     * @param match_type - 战斗类型，默认为主战斗类型（MatchConst.MATCH_TYPE_MAIN_BATTLE）
     * @param index - 领主分组类型，默认为主领主类型（ELordGroupType.TYPE_MAIN）
     */
    m_lord_treasure_op_tos(op_type, lord_id = 0, treasure_id = 0, match_type = MatchConst.MATCH_TYPE_MAIN_BATTLE, index = ELordGroupType.TYPE_MAIN) {
        let tos = new m_lord_treasure_op_tos();
        tos.op_type = op_type;
        tos.lord_id = lord_id;
        tos.treasure_id = treasure_id;
        tos.match_type = match_type;
        tos.index = index;
        Connection.instance.sendMessage(tos);
    }
}
LordTreasureDataCenter._instance = null;
