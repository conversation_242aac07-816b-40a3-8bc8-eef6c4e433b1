import { TDRoleStateRun } from "../../../../scene2d/role/fsm/state/TDRoleStateRun";
import { ESkeletonAction } from "../../../baseModules/skeleton/SkeletonData";
import { TDRolePathPointScript } from "../../../tdBase/game/script/TDRolePathPointScript";
import { GuajiBuildWorker } from "../GuajiBuildWorker";

export class GuajiBuildWorkerStateRun extends TDRoleStateRun {
    public playAni() {
        let worker = this.roleBase as GuajiBuildWorker;
        let sk = worker?.sk;
        if (sk) {
            if (worker.isGoBack) {
                sk.playAction(ESkeletonAction.SPRINT, true);
            } else {
                sk.playAction(ESkeletonAction.RUN, true);
            }
        }
    }

    /**玩家拥有战斗待机动画**/
    ToStand() {
        // this.currRoleFSMMgr.ChangeState(ESkeletonAction.STAND);
        // this.roleBase.ToStand();
        //强制跑动状态.
        let script = this.roleBase.getComponent(TDRolePathPointScript);
        if (script && script.isEnd == false) {
            let worker = this.roleBase as GuajiBuildWorker;
            let sk = worker?.sk;
            if (worker.isGoBack && sk) {
                sk.playAction(ESkeletonAction.SPRINT, true);
            } else {
                sk.playAction(ESkeletonAction.RUN, true);
            }
        }else{
            this.roleBase.ToStand();
        }
    }
}