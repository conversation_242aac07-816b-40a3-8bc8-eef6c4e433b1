import { Event } from "laya/events/Event";
import { UrlConfig } from "../../../../game/UrlConfig";
import { com } from "../../../ui/layaMaxUI";
import { ESkeletonResType, ESkeletonType } from "../../baseModules/skeleton/SkeletonData";
import { SkeletonManager } from "../../baseModules/skeleton/SkeletonManager";
import { LordDataCenter } from "../../Lord/data/LordDataCenter";
import { BUILD_OP, GuaJiBuildDataCenter } from "../data/GuaJiBuildDataCenter";
import { GuajiBuildWorker } from "../game/GuajiBuildWorker";
import { EGuajiBuildPathPointType, GuajiBuildPathPointVo } from "../vo/GuajiBuildPathPointVo";
import { Rectangle } from "laya/maths/Rectangle";
import { MiscConstAuto } from "../../../auto/MiscConstAuto";
export default class GuaJiBuildItem extends com.ui.res.guaJiBuild.GuaJiBuildItemUI {
    constructor() {
        super(...arguments);
        this.ConstructSkUrl = "building_smoke";
        this.intervalTraversalTime = 3000;
        this.intervalTime = 0;
        this._pathPointGroupIndex = 0;
        this.workerList = [];
    }
    set showDebugRect(value) {
        this.imgDebugRect.visible = value;
    }
    get skBuilding() {
        if (!this._skBuilding) {
            this._skBuilding = SkeletonManager.ins.createSkeletonEmpty(this, 0, 0, 0, 0);
        }
        return this._skBuilding;
    }
    set skBuilding(value) {
        this._skBuilding = value;
    }
    get camp() {
        return LordDataCenter.instance.camp;
    }
    get _centerX() {
        return this.x + this.width / 2;
    }
    get _centerY() {
        return this.y + this.height / 2;
    }
    initUI() {
        this.imgDebugRect.visible = false;
        //随机一个intervalTraversalTime到intervalTraversalTime+5000之间的一个值
        this.intervalTraversalTime = Math.random() * 2000 + MiscConstAuto.build_work_time;
    }
    resetHitArea() {
        if (!this.editorVo) {
            return;
        }
        /**城堡比较大，特殊处理一下 */
        const offValue = this.editorVo.building_offX;
        const x = this.editorVo.building_x / 2 - offValue;
        const y = this.editorVo.building_y / 2 + this.editorVo.building_offY;
        let width = 320 + offValue;
        let height = 255 + offValue;
        this.hitArea = Rectangle.create().setTo(x, y, width, height);
        // // 创建一个 Sprite 用于绘制 hitArea 可视化
        // let debugRect= new Sprite();
        // // 在指定位置绘制矩形（和 hitArea 一致）
        // debugRect.graphics.drawRect(
        //     0, 0, width, height,
        //     "#ff0000", // 填充颜色（红色）
        //     "#00ff00", // 边框颜色（绿色）
        //     2 // 边框宽度
        // );
        // // 将可视化的矩形定位到 hitArea 的坐标上
        // debugRect.pos(x, y);
        // // 添加到当前容器中进行显示
        // this.addChild(debugRect);
    }
    addClick() {
    }
    addEvent() {
        this.addEventListener("GUAJI_BUILD_ON_BACK_MODE" /* GUAJI_BUILD_ON_BACK_MODE */, this, this.onBackMode);
    }
    onBackMode(isBackMode) {
        if (this.skBuilding) {
            if (isBackMode) {
                this._skBuilding.paused();
            }
            else {
                this._skBuilding.resume();
            }
        }
    }
    updateFrame(interval) {
        // if (this.workerList) {
        this.workerList.forEach(worker => {
            worker.updateFrame(interval);
        });
        // if (this.intervalTime > this.intervalTraversalTime) {
        //     this.intervalTime = 0;
        //     this.refreshWorkerAction();
        // } else {
        //     this.intervalTime += interval;
        // }
        // }
    }
    initShow(vo, map) {
        this._map = map;
        this.editorVo = GuaJiBuildDataCenter.instance.getBuildingCfgVo(vo.buildType);
        let editorVo = this.editorVo;
        this.mouseThrough = true;
        this.nullImage.skin = UrlConfig.GUAJI_BUILD_URL + "camp" + this.camp + "/img_0.png";
        this.nullImage.scale(0.8, 0.8);
        this.nullImage.mouseEnabled = true;
        // this.pos(configVo.building_item_x - this.width / 2, configVo.building_item_y - this.height / 2);
        this.pos(editorVo.building_item_x, editorVo.building_item_y);
        this.imgUpgrade.pos(this.editorVo.building_x + 100, this.editorVo.building_y / 2 + this.editorVo.building_offY + 60);
        this.resetHitArea();
    }
    showConstruct() {
        this.nullImage.visible = false;
        let skName = this.ConstructSkUrl;
        let skUrl = UrlConfig.get_sk_url_by_mode(ESkeletonResType.SPINE_SKELETON, skName, ESkeletonType.UI_EFFECT); //SkeletonManager.ins.getSkUrlByCheckMode(skName, ESkeletonType.UI_EFFECT);
        if (UrlConfig.checkResExist(skUrl)) {
            this.skBuilding.updateSkeleton(skName, ESkeletonType.UI_EFFECT);
            this.skBuilding.pos(this.editorVo.building_x, this.editorVo.building_y + this.editorVo.building_offY);
            this.skBuilding.PlayActionByIndex(0, false);
            this.skBuilding.on(Event.STOPPED, this, this.setData, [this.vo]);
        }
        this.scale(1, 1);
    }
    showBuild() {
        let skName = this.editorVo.building_sk;
        //根据阵营判断动画名称
        if (skName) {
            let nameList = skName.split("_");
            nameList[0] = "building" + this.camp;
            skName = nameList.join("_");
        }
        let skUrl = UrlConfig.get_sk_url_by_mode(ESkeletonResType.SPINE_SKELETON, skName, ESkeletonType.UI_EFFECT); //SkeletonManager.ins.getSkUrlByCheckMode(skName, ESkeletonType.UI_EFFECT);
        if (UrlConfig.checkResExist(skUrl)) {
            this.skBuilding.updateSkeleton(skName, ESkeletonType.UI_EFFECT);
            this.skBuilding.pos(this.editorVo.building_x, this.editorVo.building_y + this.editorVo.building_offY);
            this.skBuilding.scale(this.editorVo.building_scale, this.editorVo.building_scale);
            this.skBuilding.mouseEnabled = true;
            this.mouseThrough = false;
        }
        this.refreshWorkerNum();
        let workerSkName = this.editorVo.worker_sk;
        this.workerList.forEach(worker => {
            let firstPoint = worker.firstPathPoint;
            let lastPoint = worker.lastPathPoint;
            worker.setSk(workerSkName, ESkeletonType.MODEL_ACTION);
            worker.setSkScale(this.editorVo.worker_scale * (lastPoint.worker_flip_x ? -1 : 1), this.editorVo.worker_scale);
        });
    }
    addWorker(index) {
        let worker = new GuajiBuildWorker();
        worker.init();
        worker.zOrder = this.zOrder;
        this.showWorker(worker, index);
        // worker.show3d.sp3view.cacheFrameRate = SkeletonData.SPINE_ANIMODE_CACHE_FRAME;
        this._map.addChild(worker);
        this.workerList.push(worker);
        //let tag = this.vo.buildType + "_" + index;
        //GuaJiBuildItem.workerPosMap.set(tag, Point.create().setTo(worker.x, worker.y));
        // if(this.workerList.length == 1){
        //     let talk = GuaJiBuildDataCenter.instance.getPlotDialogue(2, LordDataCenter.instance.camp);
        //     worker.talkView.setDesc(talk, 2000, true);
        //     worker.talkView.pos(10, -140);
        // }
    }
    showWorker(worker, index) {
        let editorVo = this.editorVo;
        editorVo.worker_sk = GuaJiBuildDataCenter.instance.getWorkerSkName(this.vo.buildType); // "build_worker_" + LordDataCenter.instance.camp + "_" + this.vo.buildType;
        let pathPointMap = GuajiBuildPathPointVo.parseCfgPathPoint(this.vo.buildType, EGuajiBuildPathPointType.GO_AND_BACK);
        let pathPointIndexList = Array.from(pathPointMap.values());
        if (index >= pathPointIndexList.length) {
            index = index % pathPointIndexList.length;
        }
        let pathPointList = pathPointIndexList[index].concat();
        // let pathPointMapKeyList: number[] = [];
        // pathPointMap.forEach(
        //     (value, key) => {
        //         pathPointMapKeyList.push(key);
        //     }
        // )
        // let showPathIndex = this._pathPointGroupIndex;
        // if (showPathIndex >= pathPointMapKeyList.length) {
        //     showPathIndex = this._pathPointGroupIndex % pathPointMapKeyList.length;
        // }
        // let pathPointList = pathPointMap.get(pathPointMapKeyList[showPathIndex]).concat([]);
        let firstPoint = pathPointList[0];
        let lastPoint = pathPointList[pathPointList.length - 1];
        if (!pathPointList) {
            this._pathPointGroupIndex = 0;
            pathPointList = pathPointMap.get(this._pathPointGroupIndex);
            firstPoint = pathPointList[0];
        }
        else {
            this._pathPointGroupIndex++;
        }
        worker.setSk(editorVo.worker_sk, ESkeletonType.MODEL_ACTION);
        worker.pointVo = pathPointList[this._pathPointGroupIndex];
        worker.editorVo = editorVo;
        worker.visible = true;
        // worker.work_type = configVo.work_type;
        worker.pathPointList = pathPointList;
        worker.pos(firstPoint.targetX, firstPoint.targetY);
        worker.setSkScale(editorVo.worker_scale, editorVo.worker_scale);
        worker.startPathPoint(pathPointList);
        // this.timer.once(500 + index * 500, this, () => {
        //     worker.isGoBack = true;
        //     worker.startPathPoint(pathPointList);
        // })
        // let isGuideAllFinish = GuaJiBuildDataCenter.instance.isGuideAllFinish;
        // if (isGuideAllFinish == false) {
        //     worker.talkView.setDesc("干活!干活!", 500, true);
        //     let talkScale = 1 / worker.scaleX;
        //     worker.talkView.scale(talkScale, talkScale);
        //     worker.talkView.bottom = 100 * talkScale;
        // }
        return worker;
    }
    removeWorker(worker) {
        if (worker) {
            worker.destroy();
            this.workerList.splice(this.workerList.indexOf(worker), 1);
        }
    }
    refreshWorkerNum() {
        if (this.workerList.length < this.vo.workerNum) {
            //添加工人
            // let index: number = this.vo.workerNum - this.workerList.length;
            for (let i = this.workerList.length; i < this.vo.workerNum; i++) {
                this.addWorker(i);
                // index++;
            }
        }
        else if (this.workerList.length > this.vo.workerNum) {
            //移除多余的
            let workerListLen = this.workerList.length;
            for (let i = workerListLen - 1; i >= this.vo.workerNum; i--) {
                this.removeWorker(this.workerList[i]);
            }
        }
        this.refreshWorker();
    }
    refreshWorker() {
        let index = 0;
        this.workerList.forEach(worker => {
            let worker_sk = GuaJiBuildDataCenter.instance.getWorkerSkName(this.vo.buildType); //"build_worker_" + LordDataCenter.instance.camp + "_" + this.vo.buildType;
            if (worker.editorVo.worker_sk != worker_sk) {
                this.showWorker(worker, index);
                index++;
            }
        });
    }
    // private refreshWorkerAction() {
    //     if (this.workerList) {
    //         let attackWorkerList: GuajiBuildWorker[] = [];
    //         this.workerList.forEach(worker => {
    //             if (worker.curRoleFSMMgr?.curActionName == ESkeletonAction.ATTACK && worker.workType == EGuajiBuildPathPointType.GO_AND_BACK) {
    //                 attackWorkerList.push(worker);
    //             }
    //         })
    //         if (attackWorkerList.length > 0) {
    //             //随机选一个
    //             let randomIndex = Math.floor(Math.random() * attackWorkerList.length);
    //             let randomWorker = attackWorkerList[randomIndex];
    //             randomWorker.startReversePathPoint();
    //         }
    //     }
    // }
    refreshBuildShow(vo, op_type = 0) {
        if (op_type == BUILD_OP.CREATE) {
            this.showConstruct();
            this.skBuilding.visible = true;
            this.imgConstruction.visible = false;
        }
        else {
            this.setData(vo);
        }
    }
    setData(vo) {
        this.vo = vo;
        this.nullImage.visible = false;
        if (this.vo.buildLv == 0) {
            this.nullImage.visible = true;
            this.skBuilding.visible = false;
            this.imgUpgrade.visible = false;
            this.refImgConstruction();
        }
        else {
            this.skBuilding.visible = true;
            this.imgConstruction.visible = false;
            this.refImgUpgrade();
            this.showBuild();
        }
    }
    refImgConstruction() {
        this.imgConstruction.visible = GuaJiBuildDataCenter.instance.getBuildCanUnlock(this.vo.buildType);
    }
    refImgUpgrade() {
        this.imgUpgrade.visible = GuaJiBuildDataCenter.instance.isBuildCanUpgrade(this.vo.buildType);
    }
    onEditorDebug(isVisible) {
        if (isVisible) {
            this.showBuild();
            this.skBuilding.visible = true;
            // this.refreshBuildShow(this.vo, BUILD_OP.CREATE);
        }
        else {
            this.setData(this.vo);
        }
    }
}
GuaJiBuildItem.workerPosMap = new Map();
