import { Event } from "laya/events/Event";
import { GlobalConfig } from "../../../../game/GlobalConfig";
import { SentryUtils } from "../../../../game/SentryUtils";
import { UrlConfig } from "../../../../game/UrlConfig";
import { MiscConstAuto } from "../../../auto/MiscConstAuto";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { cfg_hero_base } from "../../../cfg/vo/cfg_hero_base";
import { ConfigManager } from "../../../managers/ConfigManager";
import { p_hero } from "../../../proto/common/p_hero";
import { com } from "../../../ui/layaMaxUI";
import { ConsoleUtils } from "../../../util/ConsoleUtils";
import { TweenUtil } from "../../../util/TweenUtil";
import UIFactory from "../../../util/UIFactory";
import { DialogNavShow } from "../../BaseDialog";
import { DataCenter } from "../../DataCenter";
import { ModuleCommand } from "../../ModuleCommand";
import { PanelEventConstants } from "../../PanelEventConstants";
import UITab from "../../baseModules/UITab";
import { UITabData } from "../../baseModules/UITabData";
import { GSkeleton } from "../../baseModules/skeleton/GSkeleton";
import { ESkeletonType } from "../../baseModules/skeleton/SkeletonData";
import HeroEquipManager from "../../equip/data/HeroEquipManager";
import { GuideConst } from "../../guide/GuideConst";
import { HeroConsts, HeroNation, PillarEffType, SoulHeroLinkType } from "../data/HeroConsts";
import { HeroDataCenter } from "../data/HeroDataCenter";
import { HeroStarScript } from "../script/HeroStarScript";
import HeroInfoSumView2 from "../view/info/HeroInfoSumView2";
import { EquipUtil } from "../../equip/EquipUtil";
import HeroEquipVo from "../../equip/data/HeroEquipVo";
import { ItemMacro } from "../../../auto/ItemMacro";
import { EquipExtKey } from "../../equip/EquipExtKey";
import { W73DHeroView } from "../../baseModules/w73d/W73DHeroView";
import W73DHeroOffsetEditorView from "../../cmdSkEditor/view/W73DHeroOffsetEditorView";
import { W73DRotationScriptVo } from "../../baseModules/scripts/W73DSpriteRotationScript";
import { GameSoundManager } from "../../../managers/GameSoundManager";
import { DispatchManager } from "../../../managers/DispatchManager";
import { CCAnimation } from "../../../scene2d/d2/CCAnimation";
import { GameConst } from "../../GameConst";
import { MoneyTransformUtil } from "../../../util/MoneyTransformUtil";
import { GoodsManager } from "../../test_bag/GoodsManager";
import { TipsUtil } from "../../../util/TipsUtil";
import { GoodsChangeManager } from "../../test_bag/GoodsChangeManager";
import { MusicUtil } from "../../../util/MusicUtil";
import { EffectUtil } from "../../../util/EffectUtil";
import { ItemConst } from "../../goods/ItemConst";
import { SettingDataCenter } from "../../setting/data/SettingDataCenter";
import RedPointVo from "../../redPoint/RedPointVo";
import { m_soul_hero_link_toc } from "../../../proto/line/m_soul_hero_link_toc";
import { m_hero_resonate_equip_op_toc } from "../../../proto/line/m_hero_resonate_equip_op_toc";
import { SkeletonManager } from "../../baseModules/skeleton/SkeletonManager";
import { Handler } from "laya/utils/Handler";
import HeroInfoTabItem2 from "../view/info/HeroInfoTabItem2";
import { HeroZhouyinDataCenter } from "../../heroZhouyin/data/HeroZhouyinDataCenter";
import { HeroStarUpdateView2 } from "../view/starUpdate/HeroStarUpdateView2";
import { SoulHeroLevelUpdateView2 } from "../view/SoulHeroLevelUpdateView2";
import { HeroStarStageUpdateView2 } from "../view/starUpdate/HeroStarStageUpdateView2";
import { HeroBingFaView2 } from "../view/bingfa/HeroBingFaView2";
import { HeroGodEquipView2 } from "../view/godEquip/HeroGodEquipView2";
import { HeroZhouyinUpgradeView2 } from "../../heroZhouyin/view/HeroZhouyinUpgradeView2";
import { HeroEvolveDataCenter } from "../data/HeroEvolveDataCenter";
import { StringUtil } from "../../../util/StringUtil";
import {HeroUtil} from "../util/HeroUtil";
import { Sprite } from "laya/display/Sprite";
import { Image } from "laya/ui/Image";
import { GameUtil } from "../../../util/GameUtil";
import { TaxDataCenter } from "../../tax/data/TaxDataCenter";
export default class HeroInfoDialog2 extends com.ui.res.hero.HeroInfoDialog2UI {

    private _uiTab: UITab;
    private tabMap: Map<number, number> = new Map();
    private _selTabType: number = HeroDataCenter.TAB_SUM;
    protected hero_list: p_hero[];
    private _selNation: number = DataCenter.NATION_ALL;
    private _hero_info: {hero:p_hero,type_id:number,index:number,isShowSingleNation:boolean};
    private heroInfo:p_hero;
    private _hero_sum_num: number;
    private _select_index: number;
    //当前选择的英雄ID
    private _select_hero_id: number;
    //英雄基础配置
    protected _cfg: cfg_hero_base;
    protected show3d: W73DHeroView;
    private _preurl: string;
    private _presound1: string;
    private _presound2: string;
    private _starBgEff: CCAnimation;
    private _nationEff: CCAnimation;
    private _preNation: number;
    private isWaitRefresh: boolean = false;
    //顶部货币信息
    private type_id_1: number = GameConst.ITEM_TYPE_SILVER;
    private type_id_2: number = GameConst.ITEM_TYPE_HERO_EXP;
    private pillarEff:GSkeleton;
    private _yanWuSk: GSkeleton;
    private wingEff:GSkeleton;
    private _evolveEffect: CCAnimation;
    /**当前星级 */
    private currentStar:number = 0;
    constructor(){
        super();
        this.navShow = DialogNavShow.NONE;
    }
    public addClick(): void {
        this.addOnClick(this,this.closeBtn,this.close);
        this.addOnClick(this, this.btnLeft, this.OnClickChange, Event.CLICK, [-1]);
        this.addOnClick(this, this.btnRight, this.OnClickChange, Event.CLICK, [1]);
        this.addOnClick(this, this.btnItem1Add, this.onItemAddClick,Event.CLICK,[this.type_id_1]);
        this.addOnClick(this, this.btnItem2Add, this.onItemAddClick,Event.CLICK,[this.type_id_2]);
	    this.addOnClick(this, this.boxItem1, this.onItemAddClick,Event.CLICK,[this.type_id_1]);
        this.addOnClick(this, this.boxItem2, this.onItemAddClick,Event.CLICK,[this.type_id_2]);
        this.addOnClick(this,this.imgPillar,this.onClickPillarImg);
    }
    public addEvent(): void {
        this.addEventListener(ModuleCommand.ATTR_CHANGE_SILVER, this, this.onAttrChange);
        this.addEventListener(ModuleCommand.ATTR_CHANGE_HERO_EXP, this, this.onAttrChange);
        this.addEventListener(ModuleCommand.GOODS_UPDATE, this, this.onGoodsUpdate);
        /**英雄信息 */
        this.addEventListener(ModuleCommand.UPDATE_DIVINE_INFO, this, this.UpdateHeroNewInfo);
        this.addEventListener(ModuleCommand.UPDATE_DIVINE_EQUIP_ACTIVATE, this, this.UpdateHeroNewInfo);
        this.addEventListener(ModuleCommand.UPDATE_DUDUFU_INFO, this, this.UpdateHeroNewInfo);
        this.addEventListener(ModuleCommand.UPDATE_SOUL_HERO_INFO, this, this.UpdateHeroNewInfo);
        this.addEventListener(ModuleCommand.UPDATE_FORGE_INFO, this, this.UpdateHeroNewInfo);
        this.addEventListener(ModuleCommand.REFRESH_HERO_INFO, this, this.UpdateHeroNewInfo);
        this.addEventListener(ModuleCommand.UPDATE_HERO_INFO, this, this.updateHeroInfoByHeroId);
        this.addEventListener(ModuleCommand.UPDATE_HERO_WING_INFO,this,this.UpdateBodyShow);
        this.addEventListener(ModuleCommand.HERO_SKIN_UPDATE, this, this.UpdateBodyShow);
        this.addEventListener(ModuleCommand.REFRESH_HERO_LIST, this, this.refreshCurrentHeroInfo);
        this.addEventListener(ModuleCommand.ON_HERO_UPGRADE, this, this.onHeroUpgrade);
        /**道具信息 */
        this.initItemChangeEvent();
        /**红点相关 */
        this.addEventListener(ModuleCommand.UPDATE_SKIN_RED_POINT, this, this.refreshHeroRedPoint);
        this.addEventListener(ModuleCommand.UPDATE_WING_RED_POINT, this, this.refreshHeroRedPoint);
        this.addEventListener(ModuleCommand.RED_CHILD_CHANGE, this, this.onUpdateRed);
        this.addEventListener(ModuleCommand.UPDATE_TAX_INFO, this, this.checkRedTax);
        /**转场特效 */
        this.addEventListener(ModuleCommand.PLAY_ACTIVR_FINAL_STAR_EFF, this, this.playStarEff);
        /**源将 */
        this.addEventListener(ModuleCommand.UPDATE_SOUL_HERO_LINK, this, this.onNetMsg_UpdateSoulHeroLink);
        this.addEventListener(ModuleCommand.UPDATE_HERO_RESONATE_EQUIP, this, this.onNetMsg_HeroResonateEquip);
        /**副将 */
        this.addEventListener(ModuleCommand.UPDATE_HERO_SHOW_DEPUTY_STATE, this, this.OnRefreshDeputy);

    }
    private initItemChangeEvent(): void {
        GoodsChangeManager.ins.removeItemChangeAllByCaller(this);
        GoodsChangeManager.ins.addItemChangeListener(this.type_id_1, this, this.setTopItemInfo);
        GoodsChangeManager.ins.addItemChangeListener(this.type_id_2, this, this.setTopItemInfo);
    }
    private onItemAddClick(type_id: number): void {
        switch (type_id) {
            case GameConst.ITEM_TYPE_SILVER:
                this.dispatchEvent(ModuleCommand.OPEN_TAX_DIALOG);
                break;
            case GameConst.ITEM_TYPE_HERO_EXP:
                this.dispatchEvent(ModuleCommand.OPEN_TAX_HERO_EXP_DIALOG);
                break;
            default:
                break;
        }
    }
    private checkRedTax(): void {
        let tempList: { type_id: number, img: Image }[] = [
          { type_id: this.type_id_1, img: this.imgItem1 },
          { type_id: this.type_id_2, img: this.imgItem2 },
      ];
      let isRed: boolean;
      for (let item of tempList) {
          isRed = false;
          switch (item.type_id) {
              case GameConst.ITEM_TYPE_SILVER:
              case GameConst.ITEM_TYPE_FORAGE:
              case GameConst.ITEM_TYPE_HERO_EXP:
                  isRed = TaxDataCenter.instance.checkRed(item.type_id);
                  break;
              case GameConst.ITEM_TYPE_GOLD:
                  break;
          }
    
          this.SetRedPoint(item.img, isRed, 20, -10, 1);
      }
    }
    public initUI(): void {
        this.height = this.relativeHeight;
        this.viewBox.height = this.height - 224;
        this.y = 0;
        this.callLater(this.resetHitArea, [true]);
        this.topBox.mouseThrough = true;
        this._uiTab = UITab.SetUITab(this, this.tabBox, HeroInfoTabItem2, this.viewBox, this.OnSelectMenuTab, false);
        this._uiTab.checkSelectHandler = Handler.create(this, this.checkClickTab, null, false);
        this._uiTab.SetTabSize(80, 80);
        this._uiTab.SetPadding(5,10,0,0);
        this._uiTab.AddOtherParamete("selectOffsetY", -2);
        this._uiTab.AddOtherParamete("noSelectOffsetY", 2);
        this._uiTab.isBoxRight = true
        this._uiTab.SetRepeat(5, 1);
        this._uiTab.SetSpace(20, 0);
        TweenUtil.setButtonClickScale(this.btnLeft);
        TweenUtil.setButtonClickScale(this.btnRight);

        this.show3d = new W73DHeroView;
        this.show3d.skType = ESkeletonType.MODEL_LIHUI;
        this.show3d.x = this.modelBox.width / 2;
        this.show3d.y = this.modelBox.height;
        this.modelBox.addChild(this.show3d);
        this.show3d.simplified_mesh = this.show3d.useCache = false;
        this.setBoxRoleEditor();
        this.setTopItemInfo();
    }
    checkClickTab(itemData: UITabData) {
        let data = HeroDataCenter.instance.isTabOpen(itemData.childId);
        if (data.flag) {
            return true;
        }
        if (data.tipsText != null) {
            TipsUtil.showTips(data.tipsText);
        }
        return false;
    }
    setTopItemInfo(){
        this.item1Txt.text = this.myMoneyFormat(this.type_id_1);
        this.item2Txt.text = this.myMoneyFormat(this.type_id_2);
        this.imgItem1.skin = UrlConfig.getGoodsIconByTypeId(this.type_id_1);
        this.imgItem2.skin =  UrlConfig.getGoodsIconByTypeId(this.type_id_2);
        this.checkRedTax();
    }
    private myMoneyFormat(itemID: number) {
        if (itemID == GameConst.ITEM_TYPE_HERO_EXP) {
            //武将经验暂时不做小数点的处理
            return MoneyTransformUtil.MoneyFormat(GoodsManager.instance.GetGoodsNumByTypeId(itemID), false, 9999);
        } else {
            return MoneyTransformUtil.MoneyFormat(GoodsManager.instance.GetGoodsNumByTypeId(itemID), true, 99999);
        }
    }
    public onOpen(param: any): void {
        let data = param[0];
        GlobalConfig.IsDebug && ConsoleUtils.log("heroData", data);
        if (data && data.hero == null && data.type_id == 0) {
            this.refreshHeroList();
            let guideHeroId: number = 0;
            if (GuideConst.selectHeroId > 0) {
                let hero: p_hero = HeroDataCenter.instance.getHeroByTypeId(GuideConst.selectHeroId);
                if (hero) {
                    guideHeroId = hero.hero_id;
                }
            }
            HeroDataCenter.instance.select_hero_id = guideHeroId == 0 ? this.hero_list[0].hero_id : guideHeroId;
        } else if (data) {
            HeroDataCenter.instance.select_hero_id = data.hero.hero_id;
            if (data.isShowSingleNation) {
                let pHero: p_hero = HeroDataCenter.instance.getHero(HeroDataCenter.instance.select_hero_id);
                if (pHero) {
                    let cfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(pHero.type_id);
                    this._selNation = cfg.nation;
                }
            }
            this.refreshHeroList();
        }

        if (data && data.navShow != null && data.navShow != undefined) {
            this.navShow = data.navShow;
            this.checkNavShow();
        }
        this._hero_info = data;
        this.setTab();
        this.setHeroInfo();
        this.ShowActiveHeroList(data);
    }
    OnSelectMenuTab(tabData: UITabData){
        this._selTabType = tabData.childId;
        this.RefreshTabViewInfo();
        this.updateHeroNationColorBg();
    }
    OnClickChange(param: any): void {
        let index: number = param;
        let newIndex: number = this._select_index + index;
        if (-1 < newIndex && newIndex < this.hero_list.length) {
            this.RefreshHeroInfoByIndex(newIndex);
        }

    }
    private onClickPillarImg(): void {
        if (this._cfg && this._cfg.pillar != 0) {
            const desc:string = this._cfg.pillar === 1 ? window.iLang.L2_CHUAN_QI_HERO_HUO_QU_NAN_DU_JIAO_DA_ch31_YI_BAN_SHI_GE_ZHEN_YING_HE_XIN.il():"领域类英雄获取途径十分稀缺，养成难度更高，等同于神魔英雄";
            TipsUtil.showDescDialog(desc, {
                fontSize: 24,
                color: "#ed6e3c",
                width: 500,
                height: 200,
                bgSkin: "common2/panel_bg2.png",
            });
        }
    }
    onGoodsUpdate(): void {
        let view: any = this._uiTab.curBaseView;
        if (view != null && view["onGoodsUpdate"]) {
            view.onGoodsUpdate();
        }
    }

    onAttrChange(): void {
        let view: any = this._uiTab.curBaseView;
        if (view != null && view["onAttrChange"]) {
            view.onAttrChange();
        }
    }
    OnRefreshDeputy(){
        if (!this.heroInfo) {
            return;
        }
    }
    refRoleHeroInfoChange(){
        let view: any = this._uiTab.curBaseView;
        if (view != null && view["refRoleHeroInfoChange"]) {
            view.refRoleHeroInfoChange();
        }
    }
    refRoleAwake(){
        let view: any = this._uiTab.curBaseView;
        if (view != null && view["refRoleAwake"]) {
            view.refRoleAwake(this.heroInfo);
        }
    }
    private refreshCurrentHeroInfo(): void {
        this.setHeroInfoWithUpdateScript(this.getHero(this._select_hero_id));
        this.refreshHeroList();
        this.setTab();
        this.RefreshInfo();
        this.UpdateBodyShow();
        this.UpdateBtnState();
    }
    public setTab(): void {
        let tabDatas: UITabData[] = [];
        let i = 0;
        this.tabMap.clear();

        tabDatas.push(UITab.GetItemData(HeroDataCenter.TAB_SUM, window.iLang.L2_PEI_YANG.il(), this._hero_info, HeroInfoSumView2, "v2_heroInfo/btn_tab_1.png", PanelEventConstants.HERO_INFO));
        this.tabMap.set(HeroDataCenter.TAB_SUM, i++);

        let heroId = HeroDataCenter.instance.select_hero_id;
        let isShowUpdateStar = true, isSoulHero = false;
        let heroCfg = null;
        let pHero = HeroDataCenter.instance.getHero(heroId);
        if (!pHero) {
            let msg = `----------HeroInfoDialog.setTab, pHero is null, heroId = ${heroId}`;
            console.error(msg);
            SentryUtils.sendWarningMsg(msg);
            return;
        }
        if (pHero) {
            heroCfg = ConfigManager.cfg_hero_baseCache.get(pHero.type_id);
            isShowUpdateStar = (pHero.star < heroCfg.max_star) || pHero.star == 9 || pHero.star == 13;
            isSoulHero = pHero.ori_nation == HeroNation.NATION_HUN;
        }
        let isLimitHero: boolean = pHero && pHero.limit_type > 0;
        if (heroCfg && heroCfg.nation == HeroNation.NATION_HUN && isLimitHero == false) {
           tabDatas.push(UITab.GetItemData(HeroDataCenter.TAB_SOUL_LEVEL, window.iLang.L2_NENG_JI.il(), null, SoulHeroLevelUpdateView2, "v2_heroInfo/btn_tab_5.png", PanelEventConstants.HERO_INFO));
           this.tabMap.set(HeroDataCenter.TAB_SOUL_LEVEL, i++);
        } else if (isShowUpdateStar && isLimitHero == false) {
            if (pHero && pHero.star >= HeroConsts.openStarStageLV) {
                tabDatas.push(UITab.GetItemData(HeroDataCenter.TAB_STAR, window.iLang.L2_SHENG_XING.il(), null, HeroStarStageUpdateView2, "v2_heroInfo/btn_tab_2.png", PanelEventConstants.HERO_INFO));
            } else {
                 tabDatas.push(UITab.GetItemData(HeroDataCenter.TAB_STAR, window.iLang.L2_SHENG_XING.il(), null, HeroStarUpdateView2, "v2_heroInfo/btn_tab_2.png", PanelEventConstants.HERO_INFO));
            }
            this.tabMap.set(HeroDataCenter.TAB_STAR, i++);
        }

        let isSoulHeroShowBingfa = isSoulHero && HeroEquipManager.instance.getHeroBingFaList(heroId).length > 0;
        let showBf = HeroDataCenter.instance.getMaxStar(pHero.type_id) >= MiscConstAuto.bingfa_slot1_star_limit;//最大星级
        if ((pHero || isSoulHeroShowBingfa) && showBf) {
            tabDatas.push(UITab.GetItemData(HeroDataCenter.TAB_BINGFA, window.iLang.L2_TIAN_FU.il(), null, HeroBingFaView2, "v2_heroInfo/btn_tab_3.png", PanelEventConstants.HERO_INFO));
            this.tabMap.set(HeroDataCenter.TAB_BINGFA, i++);
        }
        let isSoulHeroShowGodEquip = isSoulHero && HeroEquipManager.instance.getHeroGodEquip(heroId).length > 0;
        let showHs = pHero.star >= MiscConstAuto.god_equip_star_load_limit;//最大星级
        if ((pHero || isSoulHeroShowGodEquip) && showHs) {
            tabDatas.push(UITab.GetItemData(HeroDataCenter.TAB_GODEQUIP, window.iLang.L2_SHEN_ZHUANG.il(), null, HeroGodEquipView2, "v2_heroInfo/btn_tab_4.png", PanelEventConstants.HERO_INFO));
            this.tabMap.set(HeroDataCenter.TAB_GODEQUIP, i++);
        }
        let showAwake = HeroZhouyinDataCenter.checkIsOpenZhouyin(pHero.type_id) && HeroZhouyinDataCenter.isSysOpen();
        if (showAwake) {
            tabDatas.push(UITab.GetItemData(HeroDataCenter.TAB_HERO_AWAKE,"觉醒", this._hero_info, HeroZhouyinUpgradeView2, "v2_heroInfo/btn_tab_6.png", PanelEventConstants.HERO_ZHOUYIN));
            this.tabMap.set(HeroDataCenter.TAB_HERO_AWAKE, i++);
        }
        let oriTabDatas: UITabData[] = Array.from(this._uiTab.array.values());
        let isChangeTab = oriTabDatas.length != tabDatas.length;
        let changeViewList = [];
        let index = this.tabMap.get(this._selTabType) || 0;
        //当前英雄星级不满足页签的开启要求，默认选中第一个
        if (this._selTabType == HeroDataCenter.TAB_BINGFA && pHero.star < MiscConstAuto.bingfa_slot1_star_limit) {
            index = 0;
        }
        if (this._selTabType == HeroDataCenter.TAB_GODEQUIP && pHero.star < MiscConstAuto.god_equip_star_load_limit) {
            index = 0;
        }
        if (!isChangeTab) {
            for (let i = 0; i < oriTabDatas.length; ++i) {
                let data = tabDatas[i];
                let oriData = oriTabDatas[i];
                changeViewList[i] = !data || data.index != oriData.index || oriData.cls.name != data.cls.name;
                isChangeTab = isChangeTab || oriData.childId != data.childId || changeViewList[i];
            }
        }
        //星级变化刷新角标
        let refTab = this.currentStar != pHero.star;
        this.currentStar = pHero.star;
        if (isChangeTab || refTab) {
            for (let i = 0; i < changeViewList.length; ++i) {
                if (changeViewList[i] && oriTabDatas[i]) {
                    let view = this._uiTab.viewDict.get(oriTabDatas[i].index);
                    if (view) {
                        view.removeSelf();
                        view.destroy();
                    }
                    this._uiTab.viewDict.delete(oriTabDatas[i].index);
                }
            }
            this._uiTab.array = tabDatas;
            this._uiTab.selectedIndex = index;
            if (this._uiTab.curBaseView) {
                this._uiTab.curBaseView.visible = true;
            }
        }
    }
    setHeroInfo(){
        if (!this.heroInfo) {
            return;
        }
        if (!this._cfg) {
            return;
        }
        this.imgPillar.visible = this._cfg.pillar !== 0;
        this.imgPillar.skin = HeroUtil.getPillarSkinUrl(this._cfg.pillar);
        this.fcPower.value = StringUtil.powerFormat(this.heroInfo.power);
        this.lbHeroName.text = this._cfg.name;
        let curNation = HeroDataCenter.instance.getHeroCurrNation(this.heroInfo);
        this.backGround.skin = `${UrlConfig.HERO_INFO_V2_UI_URL}nation_${this._cfg.nation}_bg.jpg`;
        this._setNation(curNation,this._cfg.nation);
        this.showPillarEff(this._cfg.pillar === 2,curNation);
        let linkHero = HeroDataCenter.instance.getBeLinkHero(this.heroInfo.hero_id);
        // let isSoulHero = this._cfg.nation == HeroNation.NATION_HUN;
        let oriStar = linkHero && linkHero.star || 0;
        this.starScript.setStar(this.heroInfo.star, oriStar);
        // if (isSoulHero && !linkHero) {
        //     this.soulLvScript.setSoulLv(this.heroInfo.soul_level, "hero_fire");
        // }
        // else{
        //     this.starScript.setStar(this.heroInfo.star, oriStar);
        // }

    }
    /**设置领域英雄特效 */
    private showPillarEff(isShowEff:boolean,nation: number){
        if (this.pillarEff) {
            this.pillarEff.removeSelf();
            this.pillarEff.destroy();
            this.pillarEff = null;
        }
        if (isShowEff) {
            const skName = PillarEffType[nation];
            this.pillarEff = this.showGSkeleton(this.nationImg,"hunyu",this.pillarEff,{x:30,y:8});
            this.pillarEff.playAction(skName);
        }

    }
    private updateHeroInfoByHeroId(hero_info: p_hero): void {
        if (!hero_info) {
            return;
        }
        if (hero_info) {
            HeroDataCenter.instance.select_hero_id = hero_info.hero_id;
            this.setHeroInfoWithUpdateScript(hero_info);
            this.setTab();
            this.RefreshInfo();
            this.UpdateBodyShow();
            this.UpdateBtnState();
        }
    }
    RefreshTabViewInfo(isUpdate: boolean = false): void {
        let view: any = this._uiTab.curBaseView;
        if (view != null) {
            view.UpdateInfo(isUpdate);
        }
    }
    //延迟处理一下，因为有人物属性变化，多个英雄属性变化同时派发，延迟时间统一刷新
    UpdateHeroNewInfo(isUpdate: boolean = false): void {
        if (this.isWaitRefresh == true && isUpdate == false) {
            return;
        }

        this.isWaitRefresh = true;
        this.refreshHeroList();
        this.setTab();
        this.RefreshInfo(isUpdate);
        this.UpdateBtnState();
    }
    private refreshHeroList(): void {
        let typeList: p_hero[] = [];
        switch (this._selNation) {
            case DataCenter.NATION_ALL:
                typeList = HeroDataCenter.instance.hero_list;
                break;
            default:
                for (let i: number = 0; i < HeroDataCenter.instance.hero_list.length; i++) {
                    let single: p_hero = HeroDataCenter.instance.hero_list[i];
                    let heroCfg: cfg_hero_base = ConfigManager.cfg_hero_baseCache.get(single.type_id);
                    if (this._selNation == heroCfg.nation) {
                        typeList.push(single);
                    }
                }
                break;
        }
        this.hero_list = typeList;
        this._hero_sum_num = this.hero_list.length;
    }
    RefreshHeroInfoByIndex(index: number) {
        let vo: p_hero = this.heroInfo = this.hero_list[index];
        this._hero_info.hero = this.heroInfo;
        HeroDataCenter.instance.select_hero_id = vo.hero_id;
        this.setHeroInfoWithUpdateScript(vo);
        this._select_index = index;
        this.setTab();
        this.RefreshInfo();
        this.UpdateBodyShow();
        this.UpdateBtnState();
    }
    ShowActiveHeroList(param: any): void {
        this._hero_sum_num = this.hero_list.length;

        for (let i: number = 0; i < this.hero_list.length; i++) {
            let vo: p_hero = this.hero_list[i];
            if (vo.hero_id == HeroDataCenter.instance.select_hero_id) {
                this._select_index = i;
                this.setHeroInfoWithUpdateScript(vo);
                break;
            }
        }

        this.RefreshInfo();
        this.UpdateBodyShow();
        this.UpdateBtnState();

        if (param && param.index) {
            let index: number = this._uiTab.getIdToIndex(param.index);
            this._uiTab.selectedIndex = index;
        } else {
            this._uiTab.selectedIndex = 0;
        }
    }
    private setHeroInfoWithUpdateScript(hero_info: p_hero): void {
        this.heroInfo = hero_info;
        this._hero_info.hero = this.heroInfo;
    }
    RefreshInfo(isUpdate: boolean = false): void {
        this.isWaitRefresh = false;
        this._select_hero_id = HeroDataCenter.instance.select_hero_id;

        if (this.heroInfo != null) {
            this._cfg = ConfigManager.cfg_hero_baseCache.get(this.heroInfo.type_id);
            this.setHeroInfo();
            this.RefreshTabViewInfo(isUpdate);
            this.updateHeroNationColorBg();
        }
        this.refRoleHeroInfoChange();
        this.refRoleAwake();
        this.refreshHeroRedPoint();
        this.updateHeroGodEquipTabRed();
        this.updateHeroBingfaTabRed();
        this.updateAwakeRedPoint();
        this.OnRefreshDeputy();
    }
    private _setNation(nation: number, oriNation: number = null): void {
        let isShowEff = oriNation != null && nation != null && oriNation != nation;
        this.showNationEff(isShowEff, nation);
        this.nationImg.visible = !isShowEff;
        this.nationImg.skin = UrlConfig.COMMON_PATH + "hero_nation_" + nation + ".png";

        if (this._evolveEffect) {
            this._evolveEffect.destroy();
            this._evolveEffect = null;
        }

        let isEvolve = HeroEvolveDataCenter.ins.checkHasEvolveSkill(this.heroInfo.hero_id);
        if (isEvolve && this.nationImg.visible) {
            this._evolveEffect = this.ShowEffectCenter("hero_evolve_" + nation, this.nationImg, true, 0, 20);
        }
    }
    /**设置阵营变换特效 */
    private showNationEff(isVisible: boolean, nation: number = null): void {
        if (this._nationEff) {
            this._nationEff.visible = isVisible;
        }
        if (isVisible && nation != null) {
            this._nationEff = this.ShowEffect("hero_nation_" + nation, this.nationBox, true,30,30);
            this._nationEff.scale(1.2,1.2);
            this._nationEff.isPingPong = true;
        }
    }
    UpdateBodyShow(force: boolean = false): void {
        this.heroInfo = this.getHero(this._select_hero_id);
        this._hero_info.hero = this.heroInfo;
        if (this.heroInfo != null) {
            this._cfg = ConfigManager.cfg_hero_baseCache.get(this.heroInfo.type_id);
            this.show3d.offAllCaller(this);
            this.show3d.once(Event.COMPLETE, this, () => {
                if (!this._preurl || this._preurl != this._cfg.skin) {
                    this._preurl = this._cfg.skin;
                    if (this._presound1) GameSoundManager.instance.stopEffect(this._presound1);
                    if (this._presound2) GameSoundManager.instance.stopEffect(this._presound2);
                    if (this._cfg.sound1) GameSoundManager.instance.playEffect(this._cfg.sound1);
                    if (this._cfg.sound2) GameSoundManager.instance.playEffect(this._cfg.sound2);
                    this._presound1 = this._cfg.sound1;
                    this._presound2 = this._cfg.sound2;

                    this.setBoxRoleEditor();

                }
            });
            if (this.heroInfo.skin_id !== 0) {
                this.show3d.refreshHeroSkin(this.heroInfo.skin_id);
            } else{
                this.show3d.refreshHero(this.heroInfo.type_id);
            }
            let equipVo: HeroEquipVo = HeroEquipManager.instance.getHeroEquipVo(this._select_hero_id);
            if (equipVo) {
                let equipData = equipVo.getEquipByKind(ItemMacro.ITEM_KIND_WING);
                let wing_level = EquipUtil.getEquipExtVal(equipData, EquipExtKey.WING_LEVEL);
                if (equipData) {
                    this.wingEff = this.createHeroWingEff(this.wingEffBox, this.wingEff,this.heroInfo.type_id,wing_level);
                }
                else{
                    if (this.wingEff) {
                        this.wingEff.removeSelf();
                        this.wingEff.destroy();
                        this.wingEff = null;
                    }
                }
            }
            this.updateHeroNationColorBg();
        }
    }
    updateHeroNationColorBg() {
        if (this.heroInfo && this.heroInfo.star >= 10) {
            if (this._cfg.nation != this._preNation && this._starBgEff) {
                this._starBgEff.removeSelf();
                this._starBgEff.destroy();
                this._starBgEff = null;
            }
            if (!this._starBgEff) {
                this._starBgEff = this.ShowEffectCenter("hero_back_" + this._cfg.nation, this.effectBox, true);
            }
            this._starBgEff.resume();
            this._starBgEff.visible = true;
        } else if (this._starBgEff) {
            this._starBgEff.stop();
            this._starBgEff.visible = false;
        }
    }
    UpdateBtnState(): void {
        this.btnLeft.disabled = this._select_index == 0;
        this.btnRight.disabled = this._select_index >= this._hero_sum_num - 1;
    }
    public getHero(hero_id: number): p_hero {
        return HeroDataCenter.instance.getHero(hero_id);
    }
        /**
     * w10优化 立绘编辑
     */
    private heroOffsetEditor: W73DHeroOffsetEditorView;
    private setBoxRoleEditor(data: any = null) {
        if (!GlobalConfig.IsDebug) {
            return;
        }

        this.drawCallOptimize = false;

        if (!this.heroOffsetEditor) {
            this.heroOffsetEditor = new W73DHeroOffsetEditorView();
            this.heroOffsetEditor.pos(295, 653);
            this.addChild(this.heroOffsetEditor);
        }

        this.heroOffsetEditor.setData(this.show3d);
        let vo: W73DRotationScriptVo = data;
        if (vo && vo.rotateY && vo.mouseTarget == this.modelBox) {
            this.heroOffsetEditor.setRotation(data.rotateY);
        }
    }
    private _starScript: HeroStarScript;
    protected get starScript(): HeroStarScript {
        if (!this._starScript) {
            this._starScript = UIFactory.bindHeroStarScript(this.boxStar, UIFactory.SIZE_38);
        }
        return this._starScript;
    }
    private _soulLvScript: HeroStarScript;
    private get soulLvScript(): HeroStarScript {
        if (!this._soulLvScript) {
            this._soulLvScript = UIFactory.bindHeroStarScript(this.boxStar,  UIFactory.SIZE_28, {isBoxCenter: false});
            this._soulLvScript.setSpace(10, 0);
        }
        return this._soulLvScript;
    }
    public close(): void {
        if (this._starBgEff) {
            this._starBgEff.removeSelf();
            this._starBgEff.destroy();
            this._starBgEff = null;
        }
        if (this._presound1) GameSoundManager.instance.stopEffect(this._presound1);
        if (this._presound2) GameSoundManager.instance.stopEffect(this._presound2);
        if (this.show3d) this.show3d.offAllCaller(this);
        this.timer.clear(this, this.RefreshInfo);
        this.timer.clear(this, this.UpdateBtnState);
        super.close();
        DispatchManager.dispatchEvent(ModuleCommand.UPDATE_FORGE_INFO);
        DispatchManager.dispatchEvent(ModuleCommand.HERO_INFO_DIALOG_CLOSE);
    }
        /**
     * 英雄提升
     * @param up_type 提升类型：1=升级，2=升阶，3=升星
     */
    onHeroUpgrade(up_type: number): void {
        let musicId: number = 0;

        switch (up_type) {
            case HeroDataCenter.LEVEL_UPDATE:
                let effect: CCAnimation = this.ShowEffect("shengji", this.upgradeEffBox, false, this.upgradeEffBox.width / 2, -60);
                EffectUtil.ScaleEffect(effect);
                musicId = MusicUtil.HERO_LEVEL_UPDATE;
                break;
            case HeroDataCenter.STAGE_UPDATE:
                musicId = MusicUtil.HEOR_STAGE_UPDATE;
                break;
            case HeroDataCenter.STAR_UPDATE:
                musicId = MusicUtil.HEOR_STAR_UPDATE;
                break;
            default:
                break;
        }
        if (musicId > 0) {
            GameSoundManager.instance.musicIdPlay(musicId);
        }

        this.setHeroInfoWithUpdateScript(this.getHero(this._select_hero_id));
        this.UpdateHeroNewInfo(true);
        this.heroUpgradeTips(up_type);
    }
    private heroUpgradeTips(upgrade_type: number) {
        if (this._cfg && this._cfg.pillar === 0) {
            return;
        }

        let temp_unlock_ids = [];
        let unlock_ids = [];
        if (upgrade_type == HeroDataCenter.LEVEL_UPDATE) {

        } else if (upgrade_type == HeroDataCenter.STAR_UPDATE) {
            if (this.heroInfo.star == MiscConstAuto.bingfu_slot1_star_limit) {
                temp_unlock_ids.push(ItemConst.ITEM_KIND_L_RIDE);
            }
            if (this.heroInfo.star == MiscConstAuto.bingfa_slot1_star_limit) {
                temp_unlock_ids.push(ItemConst.ITEM_KIND_BINGFA_1);
                if (DataCenter.myLevel >= 105 && DataCenter.world_level >= 110) {
                    temp_unlock_ids.push(ItemMacro.ITEM_KIND_GOD_EQUIP_1);
                }
            }
            if (this.heroInfo.star == MiscConstAuto.bingfu_slot2_star_limit) {
                temp_unlock_ids.push(ItemConst.ITEM_KIND_R_RIDE);
            }
            if (this.heroInfo.star == MiscConstAuto.bingfa_slot2_star_limit) {
                temp_unlock_ids.push(ItemConst.ITEM_KIND_BINGFA_2);
            }
            if (this.heroInfo.star == MiscConstAuto.casting_soul_limit) {
                temp_unlock_ids.push(ItemMacro.ITEM_KIND_WEAPON_CASTING_SOUL);
            }
            if (this.heroInfo.star == MiscConstAuto.wing_limit) {
                let wing_cfg = CfgCacheMapMgr.cfg_wing_heroAllCache.m_get(this._cfg.type_id, 0);
                if (wing_cfg != undefined) {
                    temp_unlock_ids.push(ItemMacro.ITEM_KIND_WING);
                }
            }
            if (this.heroInfo.star == MiscConstAuto.bingfa_slot3_star_limit) {
                temp_unlock_ids.push(ItemConst.ITEM_KIND_BINGFA_3);
            }
        }

        for (const unlock_id of temp_unlock_ids) {
            let key = this._cfg.type_id.toString() + unlock_id.toString();
            if (SettingDataCenter.instance.getVal(key) > 0) {
                continue;
            }
            unlock_ids.push(unlock_id);
        }
        if (unlock_ids.length > 0) {
            this.dispatchEvent(ModuleCommand.OPEN_HERO_UPGRADE_UNLOCK_TIPS_DIALOG, {
                hero_cfg: this._cfg,
                unlock_ids: unlock_ids
            });
        }

    }
    private onNetMsg_UpdateSoulHeroLink(msg: m_soul_hero_link_toc): void {
        if (msg.type == SoulHeroLinkType.LINK) {
            let effect: CCAnimation = this.ShowEffect("shengji", this.upgradeEffBox, false, this.upgradeEffBox.width / 2, -60);
            EffectUtil.ScaleEffect(effect);
        }
    }
    private onNetMsg_HeroResonateEquip(msg: m_hero_resonate_equip_op_toc): void {
        if (msg.op_type == 1) {
            let effect: CCAnimation = this.ShowEffect("shengji", this.upgradeEffBox, false, this.upgradeEffBox.width / 2, -60);
            EffectUtil.ScaleEffect(effect);
        }
    }
    private playStarEff(): void {
        if (!this._yanWuSk) {
            this._yanWuSk = SkeletonManager.ins.createSkeleton("yanwu", ESkeletonType.EFFECT, {isLoop: false});
            this._yanWuSk.pos(this.width / 2, this.height / 2);
            this._yanWuSk.stop();
            this._yanWuSk.on(Event.COMPLETE, this, this.onLoadedEff);
            this._yanWuSk.on(Event.STOPPED, this, this.onStopEff);
            this.addChild(this._yanWuSk);
        }
        this._yanWuSk.playNameOrIndex(0, false);
        if (this._yanWuSk.isLoaded) {
            this.onLoadedEff();
        }
    }

    private onLoadedEff(): void {
        console.log("加载完成");
        this.timerOnce(500, this, function () {
            console.log("更新页签");
            this.RefreshTabViewInfo();
        });
    }

    private onStopEff(): void {
        if (this._yanWuSk) {
            this._yanWuSk.removeSelf();
            this._yanWuSk.destroy();
            this._yanWuSk = null;
        }
    }
    //刷新红点状态
    public refreshHeroRedPoint(): void {
        if (!this.heroInfo) {
            return;
        }

        let item: HeroInfoTabItem2 = this._uiTab.GetItem(this.tabMap.get(HeroDataCenter.TAB_SUM)) as HeroInfoTabItem2;
        item.SetRedPoint(item, HeroDataCenter.instance.IsCanTabSumSingle(this.heroInfo), item.width - 15, -10);

        item = this._uiTab.GetItem(this.tabMap.get(HeroDataCenter.TAB_STAR)) as HeroInfoTabItem2;
        if (item) {
            let hero_id = this.heroInfo.hero_id;
            let type_id = this.heroInfo.type_id;
            let star = this.heroInfo.star;
            let star_stage = this.heroInfo.star_stage;
            let isCanUpdate = star < HeroConsts.openStarStageLV ? HeroDataCenter.instance.IsCanStarUpdate(type_id, star, hero_id) : HeroDataCenter.instance.isCanStarStageUpdate(type_id, star, star_stage, hero_id, this.heroInfo);
            item.SetRedPoint(item, isCanUpdate, item.width - 15, -10);
        }

    }
    onUpdateRed(vo: RedPointVo): void {
        if (!this || this.destroyed || !vo) return;
        if (!this.heroInfo || HeroDataCenter.checkIsInTemple(this.heroInfo)) {
            return;
        }
        switch (vo.eventId) {
            case PanelEventConstants.HERO_SOUL:
                this.updateHeroSoulTabRed();
                break;
            case PanelEventConstants.HERO_INFO:
                this.updateHeroGodEquipTabRed();
                this.updateHeroBingfaTabRed();
                break;
            case PanelEventConstants.HERO_ZHOUYIN:
                this.updateAwakeRedPoint();
                break;
        }
    }
    private updateHeroSoulTabRed(): void {
        this.updateTabRedPoint(HeroDataCenter.TAB_FIGHT_SOUL,false);
    }

    private updateHeroBingfaTabRed(): void {
        let bingfa_redPoint = HeroDataCenter.instance.checkHeroBingFaRedPoint(this.heroInfo.hero_id);
        this.updateTabRedPoint(HeroDataCenter.TAB_BINGFA,bingfa_redPoint);

    }

    private updateHeroGodEquipTabRed(): void {
        let isRed = HeroDataCenter.instance.checkHeroWearGodEquip(this.heroInfo.hero_id);
        isRed = isRed || HeroDataCenter.instance.checkHeroGodEquipEnchant(this.heroInfo.hero_id);
        this.updateTabRedPoint(HeroDataCenter.TAB_GODEQUIP,isRed);
    }
    private updateAwakeRedPoint() {
        if (this.heroInfo && this.heroInfo.type_id) {
            let isRed = HeroZhouyinDataCenter.instance.isRedPoint(this.heroInfo.type_id);
            this.updateTabRedPoint(HeroDataCenter.TAB_HERO_AWAKE,isRed);
        }
    }
    private updateTabRedPoint(tabKey: number, showRedPoint: boolean): void {
        if (!this || this.destroyed) return;
        const itemKey = this.tabMap ? this.tabMap.get(tabKey) : tabKey;
        const item: HeroInfoTabItem2 = this._uiTab.GetItem(itemKey) as HeroInfoTabItem2;
        if (!item) return;
        item.SetRedPoint(item, showRedPoint);
    }
    /**创建显示英雄对应的精灵 */
    private createHeroWingEff(parent: Sprite,oriSkeleton: GSkeleton,typeId: number, wingLv: number):GSkeleton{
        const heroCfg = CfgCacheMapMgr.cfg_wing_heroAllCache.m_get(typeId, wingLv);
        if (heroCfg) {
            const levelCfg = CfgCacheMapMgr.cfg_wing_levelAllCache.m_get(heroCfg.wing_id, wingLv);
            if (levelCfg) {
                const eff3d = levelCfg.eff3d;
                if (!eff3d) {
                    return;
                }
                let modelCfg = CfgCacheMapMgr.cfg_wing_hero_skinCache.get(eff3d);
                const scale = (modelCfg ? modelCfg.eff3d_scale : heroCfg.eff3d_scale) || 1;
                const offset3d_x = modelCfg ? modelCfg.offset3d_x : heroCfg.offset3d_x;
                const offset3d_y = modelCfg ? modelCfg.offset3d_y : heroCfg.offset3d_y;
                oriSkeleton = this.showGSkeleton(parent,eff3d,oriSkeleton,{
                    type:ESkeletonType.EFFECT,
                    x:0,
                    y:0,
                    scale:scale,
                });
                return oriSkeleton;
            }
        }
    }
}