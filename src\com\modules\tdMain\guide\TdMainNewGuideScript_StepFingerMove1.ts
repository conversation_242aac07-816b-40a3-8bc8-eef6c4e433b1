import { Point } from "laya/maths/Point";
import { TdMainDialog } from "../dialog/TdMainDialog";
import CommonTalkView from "../../common/CommonTalkView";
import { ETdMainGuideStep } from "../data/TdMainDataCenter";
import { ESkeletonType } from "../../baseModules/skeleton/SkeletonData";
import { m_fight_simp_result_toc } from "../../../proto/line/m_fight_simp_result_toc";
import { TdMainNewGuideScript_StepBase } from "./TdMainNewGuideScript_StepBase";
import { Sprite } from "laya/display/Sprite";
import { Image } from "laya/ui/Image";
import { HitArea } from "laya/utils/HitArea";

export class TdMainNewGuideScript_StepFingerMove1 extends TdMainNewGuideScript_StepBase {

    private monsterNearState = 0;

    private talk: CommonTalkView;

    private maskBox: Sprite;
    private holeArea: Sprite;

    private maskDialogBox: Sprite;
    private dialogHoleArea: Sprite;

    private get battleLayer() {
        return this.elementMgr.battleLayer;
    }

    public init(step: ETdMainGuideStep, dialog: TdMainDialog, isNewLogin: boolean): void {
        super.init(step, dialog, isNewLogin);

        // if(isNewLogin){
        //     this.isFinish = true;
        // }else{
        this.timer.once(1000, this, () => {
            this.setBattleResumeOrPause(false);
            let fromPos_toPos = this.dialog.fingerMove(5, 3);
            this.showMaskAndTalk(fromPos_toPos, window.iLang.L2_LING_HUO_TIAO_ZHENG_HERO_WEI_ZHI_ch31_KE_YI_GENG_YOU_XIAO_SHU_CHU_ch31_GUAN_JIAN.il());
        })
        // }
    }

    protected onNewLogin() {
        // this.timer.once(1000, this, () => {
        //     GuideMgr.ins.skipGuide(TdMainNewGuideScript.guideLineup3_200008);
        // })
    }

    onGuideEnd(guideId) {

    }

    public onFingerGuideEnd(): void {

        if (this.monsterNearState == 0) {
            //上阵后战斗开始，出现换位指引
            this.dialog.onStartGameClick();
            this.monsterNearState++;
        }

        this.destroyMaskAndTalk();
        this.setBattleResumeOrPause(true);
    }

    public onTimerGuide(): void {
        this.checkMonsterNear6();
    }

    private checkMonsterNear6() {
        if (this.monsterNearState != 1) {
            return false;
        }

        let isNear6 = this.checkMonsterPosNear(6);
        if (isNear6) {
            //怪物走到对应位置出现换位指引（引导期间暂停战斗,背景变暗）
            this.setBattleResumeOrPause(false);
            let fromPos_toPos = this.dialog.fingerMove(1, 6);
            this.showMaskAndTalk(fromPos_toPos, window.iLang.L2_GUAI_WU_TUO_LI_LE_ATTACK_FAN_WEI_ch31_YI_DONG_HERO_ZHUI_JI_ch25.il());

            this.monsterNearState++;
        }
    }

    public checkMonsterPosNear(posIndex: number): boolean {
        let isNear = super.checkMonsterPosNear(posIndex);
        return isNear;
    }

    public onFightResult(toc: m_fight_simp_result_toc): void {

        if (toc.is_success) {
            this.isFinish = true;
        } else {
            this.dialog.onStartGameClick();
        }

        //战斗结束后，点击胜利窗口的下一关进入第4关
        // this.dialog.onStartGameClick();

        this.destroyMaskAndTalk();
    }

    private showMaskAndTalk(fromPos_toPos: Point[], talkContent: string) {

        if (fromPos_toPos) {
            this.showRectMask(fromPos_toPos[0], fromPos_toPos[1]);

            //给比较大值的
            if (fromPos_toPos[0].y > fromPos_toPos[1].y) {
                var talkPos = fromPos_toPos[0];
            } else {
                var talkPos = fromPos_toPos[1];
            }
            this.showTalk(talkContent, talkPos);
        }
    }
    public showRectMask(stageFromPos: Point, stageToPos: Point) {

        this.destroyMaskAndTalk();

        // let sceneLayerPos = this.dialog.elementMgr.battleLayer.localToGlobal(Point.TEMP.setTo(stageFromPos.x, stageFromPos.y));
        let localFrom = this.battleLayer.globalToLocal(stageFromPos, true);
        let localTo = this.battleLayer.globalToLocal(stageToPos, true);

        let scale = 1 / this.battleLayer.scaleX;
        localFrom.x /= scale;
        localFrom.y /= scale; 

        this.maskBox = this.createMaskImg(1100, 2000, 0.7);
        this.maskBox.name = "maskImg";
        this.maskBox.scale(scale, scale)
        this.battleLayer.addChild(this.maskBox);

        this.holeArea = this.createInteractionArea();
        this.holeArea.pos(localFrom.x, localFrom.y - 20);
        this.holeArea.graphics.drawCircle(0, 0, 80, "#000000");
        this.maskBox.addChild(this.holeArea);

        this.dialog.addOnClick(this, this.maskBox, () => { console.log("-----onClick maskBox-----") }).setIsClickScale(false);

        // //给dialog一个mask,让其他元素不能点击
        let y = Math.min(stageFromPos.y, stageToPos.y);
        let hitWidth = 350;
        let hitHeight = this.dialog.height - y; //600;//Math.abs(stageFromPos.y - stageToPos.y) + 200;
        let hitX = 180;
        let hitY = y - 100;

        this.maskDialogBox = new Sprite()//this.createMaskImg(1100, 2000, 0.5);
        this.maskDialogBox.name = "maskDialogBox";
        this.maskDialogBox.zOrder = 500;
        this.maskDialogBox.width = this.dialog.width;
        this.maskDialogBox.height = this.dialog.height;
        this.dialog.addChild(this.maskDialogBox);

        // this.dialogHoleArea = this.createInteractionArea();
        // this.dialogHoleArea.pos(hitX, hitY);
        // this.dialogHoleArea.graphics.drawRect(0, 0, hitWidth, hitHeight, "#000000");
        // this.maskDialogBox.addChild(this.dialogHoleArea);

        let _hitArea = new HitArea();
        _hitArea.hit.drawRect(0, 0, this.maskDialogBox.width, this.maskDialogBox.height, "#000000");
        _hitArea.unHit.drawRect(hitX, hitY, hitWidth, hitHeight, "#000000");
        this.maskDialogBox.hitArea = _hitArea;
        this.maskDialogBox.mouseEnabled = true;
    }

    private createMaskImg(width:number, height:number, alpha:number){
        let maskImg = new Image();
        maskImg.loadImage("common/guide_mask_bg.png");
        maskImg.cacheAs = "bitmap";
        maskImg.width = width;
        maskImg.height = height;
        maskImg.alpha = alpha;
        return maskImg;
    }

    private createInteractionArea() {
        let interactionArea = new Image();
        interactionArea.blendMode = "destination-out";
        interactionArea.graphics.clear();
        return interactionArea;
    }

    public showTalk(content: string, stagePos: Point) {
        this.talk = this.talk || new CommonTalkView();
        this.dialog.addChild(this.talk);
        this.talk.size(250, 150);
        this.talk.setTalkText(content);

        let localPos = this.dialog.globalToLocal(stagePos, true);
        let y = Math.min(localPos.y + 300, this.dialog.height - 350);
        this.talk.pos(360, y);
        this.talk.bg.skin = "common/guide_3.png";
        let sk = this.talk.showSk("H306", ESkeletonType.MODEL_LIHUI);
        sk.pos(30, 70);
        sk.scale(0.4, 0.4);
    }

    private destroyMaskAndTalk() {

        this.maskBox?.destroy();
        this.holeArea?.destroy();
        this.maskDialogBox?.destroy();
        this.dialogHoleArea?.destroy();

        this.talk?.destroy();

        this.maskBox = null;
        this.holeArea = null;
        this.maskDialogBox = null;
        this.dialogHoleArea = null;
        this.talk = null;
    }
    onDestroy(): void {
        super.onDestroy();

        this.destroyMaskAndTalk();
    }
}
