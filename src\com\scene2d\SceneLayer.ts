import { Laya } from "Laya";
import { Sprite } from "laya/display/Sprite";
import { Image } from "laya/ui/Image";
import { Handler } from "laya/utils/Handler";
import { Tween } from "laya/utils/Tween";
import { GlobalConfig } from "../../game/GlobalConfig";
import { UrlConfig } from "../../game/UrlConfig";
import { cfg_skill } from "../cfg/vo/cfg_skill";
import { ConfigManager } from "../managers/ConfigManager";
import { FightDuoPingView } from "../modules/fight/view/FightDuoPingView";
import { FightQiMouEffectView } from "../modules/fight/view/FightQiMouEffectView";
import { FightWhiteScreenView } from "../modules/fight/view/FightWhiteScreenView";
import { GameConst } from "../modules/GameConst";
import { GuaJiCamera2d } from "../modules/guajiMap/view/GuaJiCamera2d";
import { p_step_skill } from "../proto/common/p_step_skill";
import { m_fight_start_toc } from "../proto/line/m_fight_start_toc";
import { CameraShock } from "./CameraShock";
import { RoleBase } from "./role/RoleBase";
import { SceneBgLayer} from "./SceneConst";
import { SceneElementMgr } from "./SceneElementMgr";
import { StoryGameDataCenter } from "../modules/storyGame/data/StoryGameDataCenter";
import { UIRenderRectUtil, UIRenderRectVo } from "../util/UIRenderRectUtil";
import { Point } from "laya/maths/Point";
import { Dialog } from "laya/ui/Dialog";
import { DispatchManager } from "../managers/DispatchManager";
import { ModuleCommand } from "../modules/ModuleCommand";
import { Box } from "laya/ui/Box";
import { ItemDropScript } from "../modules/baseModules/scripts/ItemDropScript";
import { MatchConst } from "../auto/ConstAuto";




export class SceneLayer extends Sprite {

    public mapId:number = 0;
    /**场景元素管理 */
    protected _elementMgr: SceneElementMgr;

    protected _camera2d: GuaJiCamera2d;

    /**相机抖动相关 */
    private _cameraShock: CameraShock;

    private _fight_data: m_fight_start_toc;

    /**角色底部层,例如影子,用于合批渲染,减少drawcall */
    public roleBottomLayerData: Map<string, Sprite> = new Map<string,Sprite>();
    private _roleBottomLayer: Sprite;

    /**角色顶部层,例如血条,用于合批渲染,减少drawcall */
    public roleTopLayerData: Map<string, Sprite> = new Map<string, Sprite>();
    private _roleTopLayer: Sprite;

    public get roleBottomLayer(): Sprite {
        if (!this._roleBottomLayer) {
            this._roleBottomLayer = new Sprite();
            this._roleBottomLayer.name = "roleBottomLayer";
            this.addChild(this._roleBottomLayer);
            this._roleBottomLayer.drawCallOptimize = true;
            this._roleBottomLayer.zOrder = SceneBgLayer.ROLE_BOTTOM;
        }
        return this._roleBottomLayer;
    }

    public get roleTopLayer(): Sprite {
        if (!this._roleTopLayer) {
            this._roleTopLayer = new Sprite();
            this._roleTopLayer.name = "roleTopLayer";
            this.addChild(this._roleTopLayer);
            this._roleTopLayer.drawCallOptimize = true;
            this._roleTopLayer.zOrder = SceneBgLayer.ROLE_TOP;
        }
        return this._roleTopLayer;

    }


    /**场景黑底 */
    private _bgBlack: Image;
    /**远景**/
    private _bgSky: Image;
    /**近景**/
    protected _bgFloor: Image;
    /**用在技能夺屏 */
    private _maskImg: Image;
    public _whiteScreen: FightWhiteScreenView;
    /**夺屏 */
    private _duoPingView: FightDuoPingView;
    /**龙魂特效 */
    private _qiMouEffView: FightQiMouEffectView;

    /**我方主角相关 */
    private _leftShenBing: RoleBase;
    /**敌方主角相关 */
    private _rightShenBing: RoleBase;

    /**战斗加速 */
    private _playSpeed: number = 1;

    //----------渲染优化----------
    // private renderRectScript: UIRenderRectScript = null;
    private RenderVo: UIRenderRectVo = null;
    /**是否开启渲染优化 */
    private _isRenderVo:boolean = true;
    public get camera2d(): GuaJiCamera2d {
        return this._camera2d;
    }

    constructor(elementMgr: SceneElementMgr,isRenderVo = true) {
        super();
        this._elementMgr = elementMgr;
        this._isRenderVo = isRenderVo;
        this.init();
        this.addEvent();
    }

    public onFront() {

    }
    public onBack() {

    }

    public init(): void {
        //减少text渲染打断
        // this.drawCallOptimize = true;

       this.setRenderVo(true);

    }

    addEvent(): void {
        DispatchManager.addEventListener(ModuleCommand.ROLE_BASE_CHANGE, this, this.onRoleChange);
    }

    removeEvent(): void {
        DispatchManager.removeEventListener(ModuleCommand.ROLE_BASE_CHANGE, this, this.onRoleChange);
    }

    protected getRoleTag(role: RoleBase): string {
        return role.actor_sn + "_shadow";
    }

    protected onRoleChange(role:RoleBase){
        if (role.sceneType != this._elementMgr.sceneType) {
            return;
        }
        
        let shadowTag = this.getRoleTag(role);
        let shadow = this.roleBottomLayerData.get(shadowTag);

        if(shadow){
            shadow.visible = role.visible;
            if(shadow.visible){
                shadow.pos(role.x, role.y + 15);
            }
        }
    }

    protected setRenderVo(isCreate:boolean){
        if (isCreate && this._isRenderVo){
            if (this.RenderVo == null){
                /**渲染检查 */
                this.RenderVo = UIRenderRectUtil.addRenderRectListener(this);
                this.RenderVo.beforeCheck = Handler.create(this, this.beforeRenderRectCheck, null, false);
                this.RenderVo.afterCheck = Handler.create(this, this.afterRenderRectCheck, null, false);
            }
        }else{
            if (this.RenderVo){
                this.RenderVo.clear();
                this.RenderVo = null;
            }
        }
    }

    public setRoleRender(role: RoleBase): void {

        try {
            if (role._shadowImg.destroyed == false) {
                role._shadowImg.visible = role.isShowShadow;
                let shadowTag = this.getRoleTag(role);
                this.roleBottomLayerData.set(shadowTag, role._shadowImg);
                this.roleBottomLayer.addChild(role._shadowImg);
            }

        } catch (error) {
            console.error(error);
        }

    }

    protected beforeRenderRectCheck(vo: UIRenderRectVo) {

        if(this.visible == false){
            this.setRenderVo(false);
            return false;
        }

        let allRole = this._elementMgr.allRole();
        if (allRole.size <= 0) {
            this.setRenderVo(false);
            return false;
        }

        //特殊处理GuaJiDialog
        let dialogName = vo.topDialog?.name;
        if (dialogName == "GuaJiDialog" || dialogName == "GuaJiDialogM2") {
            allRole.forEach(role => {
                role.isRender = true;
            });

            return false;
        }

        return true;
    }

    protected afterRenderRectCheck(vo: UIRenderRectVo) {

        let allRole = this._elementMgr.allRole();
        if (!allRole || allRole.size <= 0) {
            return;
        }

        let renderRect = vo.renderRect;
        let offsetY = this._elementMgr.mapLayer.y;
        if (renderRect && renderRect.height >= 400) {

            allRole.forEach(role => {

                if (role.isLord) {
                    var pos = this._elementMgr.getLordPos(role.camp);
                } else {
                    var pos = this._elementMgr.getRolePos(role.actor_sn);
                }
                if (pos) {
                    // let globalPos = this.elementMgr.guaJiLayer.localToGlobal(Point.TEMP.setTo(pos[0], pos[1]));
                    let globalPos = Point.TEMP.setTo(pos[0], pos[1] + offsetY);
                    if (renderRect.contains(globalPos.x, globalPos.y)) {

                        role.isRender = false;
                    } else {
                        role.isRender = true;
                    }
                }
            })
        } else {
            allRole.forEach(role => {
                role.isRender = true;
            });
        }
    }

    public resize(width: number, height: number): void {
        if (this._camera2d) {
            this._camera2d.resetSize(width, height);
        }
    }

    /**设置相机 */
    public setCamera2d(maxW: number, maxH: number, width: number, height: number, parent: Sprite = null): void {
        this._camera2d = new GuaJiCamera2d(parent, maxW, maxH, width, height);
    }

    public addChildToLayer(obj: Sprite): void {
        if (obj && this._maskImg && this._maskImg.visible == true) {
            obj.zOrder += this._maskImg.zOrder + 1;
        }
        this.addChild(obj);
    }

    protected get blackBgImg(): Image {
        if (!this._bgBlack) {
            let offsetX = SceneElementMgr.offsetX;
            let offsetY = SceneElementMgr.offsetY;
            this._bgBlack = new Image();
            this._bgBlack.name = "bgBlack";
            this._bgBlack.anchorX = 0.5;
            this._bgBlack.anchorY = 0.5;
            this._bgBlack.zOrder = SceneBgLayer.BLACK;
            this._bgBlack.size(720 + 120 - offsetX * 2, 1760 - offsetY * 2);
            this._bgBlack.pos(360, 640);
            this._bgBlack.skin = "common/black.png";
            this.addChild(this._bgBlack);
        }
        return this._bgBlack;
    }

    /**设置地图背景 */
    public setMapBg(matchType: number, fadeOut: boolean = false): void {
        let loadUrls: string[] = this.genMapBg(matchType);

        if (this.isBgLoaded(loadUrls)) {
            this.refreshMask(fadeOut, false);
        }
        else {
            this.showMask();
            Laya.loader.load(loadUrls, Handler.create(this, this.refreshMask, [true, true]));
        }

        this.setRenderVo(true);
    }

    protected genMapBg(matchType: number): string[] {
        let loadUrls: string[] = [];

        let isRedCLIFF = matchType == MatchConst.MATCH_TYPE_RED_CLIFF;//元素圣殿背景处理 to do
        this._bgSky && (this._bgSky.removeSelf(), this._bgSky = null);
        this._bgFloor && (this._bgFloor.removeSelf(), this._bgFloor = null);

        let cfg = ConfigManager.cfg_match_typeCache.get(matchType);
        let sky_res = cfg && (GlobalConfig.isYeGame ? cfg.web_sky_res : cfg.sky_res);
        //支持审核服的战斗背景配置
        let floor_res = GlobalConfig.is_majia_shenhe ? StoryGameDataCenter.instance.scene_floor_res :
            (cfg && (GlobalConfig.isYeGame ? cfg.web_floor_res : cfg.floor_res));

        if (isRedCLIFF && this._fight_data != null) {
            sky_res = sky_res.split("|")[this._fight_data.target_id - 1];
            floor_res = floor_res.split("|")[this._fight_data.target_id - 1];
        }

        if (sky_res) {
            let skyUrl = UrlConfig.FIGHT_MAP_URL + sky_res;
            if (!this._bgSky) {
                this._bgSky = new Image();
                this._bgSky.name = "bgSky";
                this._bgSky.pos(360, 640);
                this._bgSky.zOrder = SceneBgLayer.SKY;
                this._bgSky.anchorX = 0.5;
                this._bgSky.anchorY = 0.5;
                this.addChild(this._bgSky);
            }

            this._bgSky.skin = skyUrl;
            loadUrls.push(skyUrl);
        }

        if (floor_res) {
            let floorUrl = UrlConfig.FIGHT_MAP_URL + floor_res;
            if (!this._bgFloor) {
                this._bgFloor = new Image();
                this._bgFloor.name = "bgFloor";
                this._bgFloor.zOrder = SceneBgLayer.FLOOR;
                this._bgFloor.anchorX = 0.5;
                this._bgFloor.anchorY = 0.5;
                this._bgFloor.pos(360, 640);
                this.addChild(this._bgFloor);
            }
            let width = cfg && (GlobalConfig.isYeGame ? cfg.web_floor_w : cfg.floor_w);
            let height = cfg && (GlobalConfig.isYeGame ? cfg.web_floor_h : cfg.floor_h);
            this._bgFloor.size(width, height);
            this._bgFloor.skin = floorUrl;
            loadUrls.push(floorUrl);
        }

        this._bgSky && (this._bgSky.visible = true);
        this._bgFloor && (this._bgFloor.visible = true);

        return loadUrls;
    }

    protected isBgLoaded(urls: string[]): boolean {
        for (let i = 0; i < urls.length; ++i) {
            let url = urls[i];
            if (!Laya.loader.getRes(url)) {
                return false;
            }
        }
        return true;
    }

    protected showMask(): void {
        this.blackBgImg.visible = true;
        this.blackBgImg.alpha = 1;
    }

    protected refreshMask(fadeOut: boolean, visible: boolean) {
        if (this.destroyed) {
            return;
        }

        Tween.clearAll(this.blackBgImg);

        if (fadeOut) {
            this.blackBgImg.visible = true;
            this.blackBgImg.alpha = 1;

            Tween.to(this.blackBgImg, { alpha: 0 }, 200, null, Handler.create(this, () => {
                Tween.clearAll(this.blackBgImg);
                this.blackBgImg.visible = false;
            }))
        }
        else {
            this.blackBgImg.visible = visible;
        }
    }

    /**设置战斗数据 */
    public setFightInfo(toc: m_fight_start_toc = null, indexstep: number, indexround: number, lasttime: number = 0, initturnindex: number = 0, review: boolean = false): void {
        this.reset();
        this._fight_data = toc;

        if (this._camera2d != null) {
            this._camera2d.setPos(0, 0);
        }

        this.timer.once(1000, this, () => {
            DispatchManager.dispatchEvent(ModuleCommand.RESET_DIALOG_RENDER_RECT);
        })

        this.setRenderVo(true);
    }

    public playSpeed(val: number): void {
        this._playSpeed = val;
        // this.qiMouEffView.playBackRate(val);
    }

    /**每帧更新，毫秒*/
    public updateFrame(interval: number): void {
        if (this._camera2d != null && this._cameraShock) {
            if (this._cameraShock.isRangeEnd == true) {
                this._camera2d.setPos(0, 0);
            }
            else if (this._cameraShock.isRange == true) {
                this._cameraShock.updateFrame(interval);
                let nx: number = this._cameraShock.x;
                let ny: number = this._cameraShock.y;
                this._camera2d.setPos(nx, ny);
            }
        }
        // if (this._qiMouEffView) {
        //     this._qiMouEffView.updateFrame(interval);
        // }

        // this.getComponent(ItemDropScript)?._updateFrame(interval);
    }

    /**夺屏相关 */
    public get duoPingView(): FightDuoPingView {
        if (this._duoPingView == null) {
            this._duoPingView = new FightDuoPingView();
            this._duoPingView.zOrder = 9999;
            this._duoPingView.pos(0, 0);
            this.addChild(this._duoPingView);
        }
        return this._duoPingView;
    }

    /**龙魂特效 */
    public get qiMouEffView(): FightQiMouEffectView {
        if (this._qiMouEffView == null) {
            this._qiMouEffView = new FightQiMouEffectView();
            this._qiMouEffView.zOrder = 9999;
            this._qiMouEffView.pos(0, 0);
            this._qiMouEffView.visible = false;
            this.addChild(this._qiMouEffView);
        }
        return this._qiMouEffView;
    }

    /**当前步骤无关角色都会在该遮罩挡住 */
    public get maskImg(): Image {
        if (this._maskImg == null) {
            this._maskImg = new Image();
            this._maskImg.name = "maskImg";
            // this._maskImg.anchorX = 0.5;
            // this._maskImg.anchorY = 0.5;
            this._maskImg.zOrder = 999;
            this._maskImg.alpha = 0;
            this._maskImg.sizeGrid = "4,4,4,4";
            this._maskImg.skin = "common/black.png";
            this._maskImg.visible = false;
            // this._maskImg.pos(360, 640);
            this.addChild(this.maskImg);
        }
        let offsetX = SceneElementMgr.offsetX;
        let offsetY = SceneElementMgr.offsetY;
        // this._maskImg.size(840 - offsetX * 2, 1760 - offsetY * 2);
        this._maskImg.size(this.width, this.height);
        return this._maskImg;
    }

    public get whiteScreen(): FightWhiteScreenView {
        if (this._whiteScreen == null) {
            this._whiteScreen = new FightWhiteScreenView();
            this._whiteScreen.zOrder = 9999;
            this._whiteScreen.alpha = 0;
            this.addChild(this._whiteScreen);
            this._whiteScreen.seLayer(this);
        }
        return this._whiteScreen;
    }

    /**设置相机抖动 */
    public setCameraShock(shock: string): void {
        if (shock.length > 1 && this._camera2d) {
            this.cameraShock.setShock(shock);
        }
    }

    /**相机抖动相关 */
    public get cameraShock(): CameraShock {
        if (this._cameraShock == null) {
            this._cameraShock = new CameraShock();
        }
        return this._cameraShock;
    }

    private delayTime(val: number): number {
        return val / this._playSpeed;
    }

    private getStepSkillId(stepSkill: p_step_skill): number {
        return stepSkill ? stepSkill.skill_id || stepSkill.trig_skill_id : 0;
    }

    /**调整与当前战斗相关角色的层次 */
    public fightRoleZOrder(actor_sn_list: number[], {
        atkRole = null,
        tarZOrder = 0,
        skillId = 0,
    } = {}): void {
        let useActorSn: number = 0
        let useCamp: number = 0;
        if (atkRole) {
            useActorSn = atkRole.actor_sn;
            useCamp = atkRole.camp;
        }

        let zOrder: number = this.maskImg.zOrder;

        for (let actor_sn of actor_sn_list) {
            let role: RoleBase = this._elementMgr.findRole(actor_sn);
            if (role) {
                role.setZOrder(zOrder + role.birthZOrder);
                role.setfloorSkin(useActorSn, useCamp);
                this._elementMgr.buffMgr.setBuffZOrder(role, zOrder);
            }
        }

        if (atkRole) {
            if (atkRole.birthZOrder > tarZOrder) {
                let cfg: cfg_skill = ConfigManager.cfg_skillCache.get(skillId);
                if (cfg && cfg.type == 1) {
                    atkRole.setZOrder(zOrder + atkRole.birthZOrder + 1);
                } else {
                    atkRole.setZOrder(zOrder + atkRole.birthZOrder - 1);
                }
            } else {
                atkRole.setZOrder(tarZOrder + zOrder);
            }
            this._elementMgr.buffMgr.setBuffZOrder(atkRole, zOrder);
            atkRole.setfloorSkin(useActorSn, useCamp);

        }
    }

    private _alpha: number = -1;
    public setMaskAlpha(tarAlpha: number, time: number = 200, maskZorder = 999): void {
        if (this._alpha == tarAlpha) {
            return;
        }
        this._alpha = tarAlpha;
        Tween.to(this.maskImg, { alpha: tarAlpha }, this.delayTime(time), null, null, 0, true);
        if (tarAlpha > 0) {
            this.maskImg.visible = true;
            this.maskImg.zOrder = maskZorder;
        }
        this.timer.once(this.delayTime(time), this, ()=> {
            this.maskImg.visible = tarAlpha > 0;
            this.maskImg.zOrder = 999;
        });
    }

    public clearBg(): void {
        this._bgSky && this._bgSky.removeSelf();
        this._bgSky = null;
        this._bgFloor && this._bgFloor.removeSelf();
        this._bgFloor = null;
    }

    public clear(): void {
        this.reset();
        if (this._duoPingView) {
            this._duoPingView.clear();
            this._duoPingView.visible = false;
        }
        if (this.whiteScreen) {
            this.whiteScreen.clear();
        }
        if (this._maskImg) {
            this._maskImg.visible = false;
        }
        if (this._bgBlack) {
            this._bgBlack.visible = false;
        }
        if (this._cameraShock) {
            this._cameraShock.clear();
        }
        

        this.roleBottomLayerData.clear();
        this.roleTopLayerData.clear();
        if (this.roleBottomLayer) {
            this.roleBottomLayer.removeChildren();
        }
        if (this.roleTopLayer) {
            this.roleTopLayer.removeChildren();
        }

        this.getComponent(ItemDropScript)?.destroy();
    }

    private reset(): void {
        this._alpha = -1;
        this.clearBg();

        if (this._leftShenBing) {
            this._leftShenBing.destroy();
            this._leftShenBing = null;
        }

        if (this._rightShenBing) {
            this._rightShenBing.destroy();
            this._rightShenBing = null;
        }

        this.timer.clearAll(this);
    }

    destroy(destroyChild: boolean = true): void {
        super.destroy(destroyChild);
        this._duoPingView = null;
        this._maskImg = null;
        this._bgBlack = null;
        this.clearBg();
        if (this._camera2d) {
            this._camera2d.destroy();
            this._camera2d = null;
        }

        this.removeEvent();
    }
}