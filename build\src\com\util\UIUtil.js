import { Sprite } from "laya/display/Sprite";
import { Box } from "laya/ui/Box";
import { Button } from "laya/ui/Button";
import { Dialog } from "laya/ui/Dialog";
import { HScrollBar } from "laya/ui/HScrollBar";
import { Image } from "laya/ui/Image";
import { VScrollBar } from "laya/ui/VScrollBar";
import { BaseDialog } from "../modules/BaseDialog";
import { ItemConst } from "../modules/goods/ItemConst";
import { GameUtil } from "./GameUtil";
import UIFactory from "./UIFactory";
import { ClickScaleScript } from "../modules/baseModules/scripts/ClickScaleScript";
/**
 * UI相关工具类
 */
export class UIUtil {
    /**
     * 创建带滚动条的box
     * @param scrollBarType 0.无滚动条, 1.横向滚动条, 2.纵向滚动条
     */
    static createBoxWithScrollBar(scrollBarType) {
        let box = new Box();
        if (scrollBarType == 1) {
            let scrollBar = new HScrollBar();
            scrollBar.left = scrollBar.right = 0;
            scrollBar.name = "bar";
            box.addChild(scrollBar);
        }
        else if (scrollBarType == 2) {
            let scrollBar = new VScrollBar();
            scrollBar.top = scrollBar.bottom = 0;
            scrollBar.name = "bar";
            box.addChild(scrollBar);
        }
        return box;
    }
    /**
     * 设置购买按钮,显示"x元"或者"x元宝"
     * Label不要定义宽度和锚点,否则位置算不对
     */
    static setPayButtonUiByPriceType(btnBuy, imgCost, labCost, price_type, price) {
        //TODO 先判断 image 和label的宽度和锚点
        if (price == 0) {
            imgCost.visible = false;
            labCost.text = "免 费";
            labCost.x = (btnBuy.width - labCost.width) / 2;
        }
        else {
            if (price_type == ItemConst.COST_RMB) {
                imgCost.visible = false;
                labCost.text = price + "元";
                labCost.x = btnBuy.width / 2 - labCost.width / 2;
            }
            else {
                imgCost.visible = true;
                imgCost.skin = GameUtil.getItemIconUrl(price_type);
                labCost.text = GameUtil.gold(price).toString();
                let totalWidth = imgCost.width + labCost.width;
                imgCost.x = btnBuy.width / 2 - totalWidth / 2;
                labCost.x = imgCost.x + imgCost.width - 4;
                // labCost.x = imgCost.width + imgCost.x + labCost.width / 2 -5;
            }
        }
    }
    /**
     * 判断节点是否可视,包括判断父节点
     *
     * @param checkParentNum 判断多少层父节点
     */
    static checkSpriteVisible(target, checkParentNum) {
        let ret = true;
        //不要使用visible 因为有些控件重写了set visible 但是没写get visible 会导致返回undefined
        if (target._visible == false || target.alpha == 0) {
            ret = false;
        }
        else {
            if (checkParentNum > 0) {
                checkParentNum--;
                if (target.parent && target.parent instanceof Sprite) {
                    ret = this.checkSpriteVisible(target.parent, checkParentNum);
                }
            }
        }
        return ret;
    }
    /**
     * 获取node的完整路径(以BaseDialog为起点,用下划线分隔)
     */
    static getViewPathNameUntilBaseDialog(node) {
        let ret = node.name;
        let parent = node.parent;
        if (parent) {
            let prePath = "";
            if (parent instanceof BaseDialog) {
                prePath = parent.name;
            }
            else {
                prePath = UIUtil.getViewPathNameUntilBaseDialog(parent);
            }
            ret = prePath + "_" + ret;
        }
        return ret;
    }
    /**
     * 获取node所属的BaseDialog对象
     */
    static getViewBelongBaseDialog(node) {
        let ret = null;
        let parent = node.parent;
        if (parent) {
            if (parent instanceof BaseDialog) {
                ret = parent;
            }
            else {
                ret = this.getViewBelongBaseDialog(parent);
            }
        }
        return ret;
    }
    //-----------------------------------------界面自动隐藏--------------------------------------------------------------------------
    /**
     * 设置自动隐藏,降低不可见界面的渲染
     */
    static setIsDialogAutoHide(dialog, isAutoHide) {
        if (isAutoHide) {
            if (this.autoHideDialogList.indexOf(dialog) < 0) {
                this.autoHideDialogList.push(dialog);
            }
        }
        else {
            let index = this.autoHideDialogList.indexOf(dialog);
            if (index >= 0) {
                this.autoHideDialogList.splice(index, 1);
            }
            dialog.visible = true;
        }
    }
    static onDialogOpenOrClose(dialog) {
        //先测试,稳定之后再打开开关
        // if (!GlobalConfig.IsDebug) {
        //     return
        // }
        if (UIUtil.isHideAllUi) {
            return;
        }
        if (dialog.isFullScreen == false) {
            //可做可不做, 后面的for循环也会判断到(会延迟一些时间)
            if (dialog.destroyed) {
                let index = this.autoHideDialogList.indexOf(dialog);
                if (index >= 0) {
                    this.autoHideDialogList.splice(index, 1);
                }
            }
            return;
        }
        let len = this.autoHideDialogList.length;
        let topDialogIndex = this.getIndexOf_topDialogWithFullScreen();
        if (topDialogIndex >= 0) {
            var topDialog = Dialog.manager.getChildAt(topDialogIndex);
        }
        for (let i = len - 1; i >= 0; i--) {
            let isVisible = true;
            let view = this.autoHideDialogList[i];
            if (view.destroyed || !view["_viewCreated"]) {
                this.autoHideDialogList.splice(i, 1);
            }
            else {
                if (topDialog) {
                    if (topDialog === view) {
                    }
                    else {
                        if (view["_visible_"]) {
                            isVisible = true;
                        }
                        else {
                            if (view.zOrder < topDialog.zOrder) {
                                isVisible = false;
                            }
                            else if (view.zOrder > topDialog.zOrder) {
                                isVisible = true;
                            }
                            else {
                                let index = Dialog.manager.getChildIndex(view);
                                if (index >= 0 && index < topDialogIndex) {
                                    isVisible = false;
                                }
                            }
                        }
                    }
                }
                view.visible = isVisible;
                // if(isVisible){
                //     ConsoleUtils.warn("---------------UIUtil onDialogOpenOrClose show1 = " + view.name);
                // }else{
                //     ConsoleUtils.warn("---------------UIUtil onDialogOpenOrClose hide = " + view.name);
                // }
            }
        }
    }
    // private static isDialogFullScreenBg(dialog:BaseDialog){
    //     return dialog.isFullScreen;
    // }
    /**
     * 获取dialogManager里最顶层的 全屏dialog
     */
    static getIndexOf_topDialogWithFullScreen() {
        // let baseDialog:BaseDialog;
        let dialogIndex = -1;
        let len = Dialog.manager.numChildren;
        for (let i = len - 1; i >= 0; i--) {
            let dialog = Dialog.manager.getChildAt(i);
            if (dialog instanceof BaseDialog) {
                if (dialog.isFullScreen && dialog.isPopEffectFinished) {
                    // baseDialog = dialog;
                    dialogIndex = i;
                    //ConsoleUtils.warn("---------------UIUtil getZorderOfTopDialogHasFullScreenBg dialog.name = " + dialog.name + ", zorder = " + dialog.zOrder);
                    break;
                }
            }
        }
        return dialogIndex;
    }
    static getTopDialogWithFullScreen() {
        // let baseDialog:BaseDialog;
        let retDialog;
        let len = Dialog.manager.numChildren;
        for (let i = len - 1; i >= 0; i--) {
            let dialog = Dialog.manager.getChildAt(i);
            if (dialog instanceof BaseDialog) {
                if (dialog.isFullScreen) {
                    retDialog = dialog;
                    break;
                }
            }
        }
        return retDialog;
    }
    /**获取当前显示在最顶层的dialog */
    static getTopShowDialog() {
        let result;
        for (let idx = Dialog.manager.numChildren - 1; idx > -1; idx--) {
            var item = Dialog.manager.getChildAt(idx);
            if (item && item instanceof BaseDialog) {
                result = item;
                break;
            }
        }
        return result;
    }
    static getOpenedDialogByName(dialogName) {
        // let baseDialog:BaseDialog;
        let retDialog;
        let len = Dialog.manager.numChildren;
        for (let i = len - 1; i >= 0; i--) {
            let dialog = Dialog.manager.getChildAt(i);
            if (dialog instanceof BaseDialog) {
                if (dialog.name == dialogName) {
                    retDialog = dialog;
                    break;
                }
            }
        }
        return retDialog;
    }
    /**
     * 控件布局居中
     * @param objs 控件列表
     * @param param1
     * @return 总长度
     */
    static layoutUI(objs, { space = 0, offsetPos = 0, parentSize = null, isVertical = false, isCenter = false, isReverse = false, checkVisible = true, } = {}) {
        if (!objs || objs.length <= 0) {
            return 0;
        }
        //获取父控件的尺寸
        if (parentSize === null) {
            let parent = (objs[0] && objs[0].parent);
            parentSize = parent ? (isVertical ? parent.height : parent.width) : 0;
        }
        //计算总尺寸
        let totalSize = 0;
        for (let i = 0; i < objs.length; ++i) {
            let obj = objs[i];
            if (obj && (checkVisible == false || obj.visible == true)) {
                totalSize += (isVertical ? obj.height * obj.scaleY : obj.width * obj.scaleX) + space;
            }
        }
        totalSize -= space; //减去多出来的间隔
        //调整布局
        let centerOffPos = isCenter ? (parentSize - totalSize) / 2 : 0;
        let startPos = offsetPos + (isReverse ? parentSize - centerOffPos : centerOffPos);
        for (let i = 0; i < objs.length; ++i) {
            let obj = objs[i];
            if (obj && (checkVisible == false || obj.visible == true)) {
                let scaleX = obj.scaleX;
                let scaleY = obj.scaleY;
                if (isVertical) {
                    if (isReverse) {
                        obj.y = startPos - (obj.height - obj.pivotY) * scaleY;
                        startPos = obj.y - obj.pivotY * scaleY - space;
                    }
                    else {
                        obj.y = startPos + obj.pivotY * scaleY;
                        startPos = obj.y + (obj.height - obj.pivotY) * scaleY + space;
                    }
                }
                else {
                    if (isReverse) {
                        obj.x = startPos - (obj.width - obj.pivotX) * scaleX;
                        startPos = obj.x - obj.pivotX * scaleX - space;
                    }
                    else {
                        obj.x = startPos + obj.pivotX * scaleX;
                        startPos = obj.x + (obj.width - obj.pivotX) * scaleX + space;
                    }
                }
            }
        }
        return totalSize;
    }
    static followLabel(label, follower, space = 0) {
        follower.x = label.x + label.text.length * label.fontSize + space;
    }
    static checkIsDialogOpened(dialogName) {
        let ret = false;
        let len = Dialog.manager.numChildren;
        for (let i = len - 1; i >= 0; i--) {
            let dialog = Dialog.manager.getChildAt(i);
            if (dialog instanceof BaseDialog) {
                if (dialog.name == dialogName) {
                    ret = true;
                    break;
                }
            }
        }
        return ret;
    }
    static getOpenedDialog(dialogName) {
        let ret = null;
        let len = Dialog.manager.numChildren;
        for (let i = len - 1; i >= 0; i--) {
            let dialog = Dialog.manager.getChildAt(i);
            if (dialog instanceof BaseDialog) {
                if (dialog.name == dialogName) {
                    ret = dialog;
                    break;
                }
            }
        }
        return ret;
    }
    static SetTabStyle(nameLabel, value, lablesize = 0) {
        if (!UIUtil.TabLableColorList) {
            UIUtil.TabLableColorList = UIFactory.NORMAL_TAB_LABLE_COLOR.split(",");
        }
        if (!UIUtil.TabStrokeColorList) {
            UIUtil.TabStrokeColorList = UIFactory.NORMAL_TAB_STROKE_COLOR.split(",");
        }
        if (lablesize == 0) {
            lablesize = UIFactory.NORMAL_TAB_LABLE_SIZE;
        }
        if (value == true) {
            nameLabel.fontSize = lablesize + 2;
            nameLabel.stroke = UIFactory.NORMAL_TAB_STROKE_SIZE;
            nameLabel.strokeColor = UIUtil.TabStrokeColorList[2];
            nameLabel.color = UIUtil.TabLableColorList[2];
        }
        else {
            nameLabel.fontSize = lablesize;
            nameLabel.stroke = UIFactory.NORMAL_TAB_STROKE_SIZE;
            nameLabel.strokeColor = UIUtil.TabStrokeColorList[0];
            nameLabel.color = UIUtil.TabLableColorList[0];
        }
    }
    /**
     * 点击缩放
     */
    static setClickScale(btn) {
        if (!btn) {
            return;
        }
        // w9w10优化,添加点击动态特效 搜索EffectAnimation.EFFECT_BEGIN
        if (btn.hasListener("effectbegin")) {
            return;
        }
        //图片或者按钮
        if (!(btn instanceof Image) && !(btn instanceof Button && btn.stateNum <= 1)) {
            return;
        }
        btn.addComponent(ClickScaleScript);
        // let eff = new EffectAnimation();
        // eff.effectData = { "type": "View", "props": {}, "compId": 1, "child": [{ "type": "Image", "props": { "y": 0, "x": 0 }, "compId": 2 }], "animations": [{ "nodes": [{ "target": 2, "keyframes": { "scaleY": [{ "value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 0 }, { "value": 0.9, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 3 }, { "value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 6 }], "scaleX": [{ "value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 0 }, { "value": 0.9, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 3 }, { "value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 6 }] } }], "name": "ani1", "id": 1, "frameRate": 24, "action": 0 }], "loadList": [], "loadList3D": [] };
        // eff.target = btn;
        // eff.playEvent = Event.MOUSE_DOWN;
        // btn.addChild(eff);
    }
    //限制拖动范围在屏幕内
    static setFixDragRange(dragBox, limitWidth, limitHeight) {
        //确保地图在屏幕中
        let box = dragBox;
        let x = box.x;
        let y = box.y;
        //考虑缩放比
        let width = box.width * box.scaleX;
        let height = box.height * box.scaleY;
        let deltaX = limitWidth - width;
        let deltaY = limitHeight - height;
        //如果地图小于屏幕
        if (deltaX > 0) {
            var minX = 0;
            var maxX = deltaX;
        }
        else {
            var minX = deltaX;
            var maxX = 0;
        }
        if (deltaY > 0) {
            var minY = 0;
            var maxY = deltaY;
        }
        else {
            var minY = deltaY;
            var maxY = 0;
        }
        if (x > maxX) {
            box.x = maxX;
        }
        else if (x < minX) {
            box.x = minX;
        }
        if (y > maxY) {
            box.y = maxY;
        }
        else if (y < minY) {
            box.y = minY;
        }
    }
    static refreshFontClipWidth(fc) {
        if (!fc)
            return;
        fc.width = 0;
        fc.width = fc.get_width() - fc.spaceX;
    }
    /**按钮横向排列布局 */
    static layoutItems(container, items, { startOffX = 0, //起始偏移
    itemWidth = 76, //按钮宽度 
    spacing = 0, //按钮间距
    rowHeight = 80, //行间距
    maxRowPerLine = 4, //每行数量
    rtl = false, //是否从右到左
    singleRow = false, } = {}) {
        // 清除已有子元素
        container.removeChildren();
        // 过滤出可见项
        const visibleItems = items.filter(item => item._visible);
        if (visibleItems.length === 0)
            return;
        if (singleRow) {
            let x = rtl ? container.width - (itemWidth + spacing) + startOffX : startOffX;
            let y = 0;
            for (let i = 0; i < visibleItems.length; i++) {
                const item = visibleItems[i];
                item.x = x;
                item.y = y;
                container.addChild(item);
                x += rtl ? -(itemWidth + spacing) : itemWidth + spacing;
            }
        }
        else {
            let y = 0;
            let rowIndex = 0;
            while (rowIndex * maxRowPerLine < visibleItems.length) {
                const start = rowIndex * maxRowPerLine;
                const end = start + maxRowPerLine;
                const rowItems = visibleItems.slice(start, end);
                let x = rtl ? container.width - (itemWidth + spacing) + startOffX : startOffX;
                for (let i = 0; i < rowItems.length; i++) {
                    const item = rowItems[i];
                    item.y = y;
                    item.x = x;
                    x += rtl ? -(itemWidth + spacing) : itemWidth + spacing;
                    container.addChild(item);
                }
                y += rowHeight;
                rowIndex++;
            }
        }
    }
}
UIUtil.autoHideDialogList = [];
UIUtil.isHideAllUi = false;
//避免与BaseDialog 循环引用
// ClassUtils.regClass("W7.UIUtil", UIUtil);
