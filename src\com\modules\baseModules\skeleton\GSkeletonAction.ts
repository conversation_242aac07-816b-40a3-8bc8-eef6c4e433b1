import {RandomUtil} from "../../../util/RandUtil";
import {GSkeleton} from "./GSkeleton";
import {IJyAni} from "../animation/interface/IJyAni";
import {ESkeletonAction} from "./SkeletonData";

export class GSkeletonAction extends GSkeleton {

    public actionName: ESkeletonAction = ESkeletonAction.STAND;
    //空闲帧才加载,避免卡顿
    public isLoadInFreeFrame = false;
    private oriY:number = 0;

    private randFloat: number = 0;

    protected _isFadeIn = true;

    constructor() {
        super();
        this.randFloat = RandomUtil.rangeFloat();
    }

    get alpha(){
        return super.alpha;
    }
    set alpha(value:number){
        super.alpha = value;
    }
    
    public setBaseSk(skeleton: IJyAni) {
        super.setBaseSk(skeleton);

        // if(this._isFadeIn){
            this.setFadeIn(this._isFadeIn);
        // }
    }

    //站立的时候动作不要那么统一, 弄个时间差
    // protected getActionStartTime(): number {
    //     let startTime = 0;
    //     if (this.actionName == ESkeletonAction.STAND) {
    //         startTime = this.randFloat * 700;
    //     // } else if (this.actionName == ESkeletonAction.DEATH) {
    //     //     startTime = this.randFloat * 400;
    //     } else if (this.actionName == ESkeletonAction.RUN) {
    //         startTime = this.randFloat * 200;
    //     }
    //     return Math.floor(startTime);
    // }


    /**
    * 设置每秒最大帧数,降低内存占用
    * 子类继承
    */
    protected setLayaSkActionMaxFps() {
        // if (this.baseSkeleton){
        //     this.baseSkeleton.setActionMaxFps(ESkeletonAction.GET_HIT, 15);
        //     this.baseSkeleton.setActionMaxFps(ESkeletonAction.DEATH, 15);
        //     this.baseSkeleton.setActionMaxFps(ESkeletonAction.ATTACK, 25);
        //     this.baseSkeleton.setActionMaxFps(ESkeletonAction.STAND, 25);
        //     this.baseSkeleton.setActionMaxFps(ESkeletonAction.KILL, 25);
        //     this.baseSkeleton.setActionMaxFps(ESkeletonAction.RUN, 18);
        // }
    }
}
