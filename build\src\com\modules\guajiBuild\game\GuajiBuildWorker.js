import { RoleBase } from "../../../scene2d/role/RoleBase";
import { RoleFSMMgr } from "../../../scene2d/role/fsm/RoleFSMMgr";
import { SkeletonManager } from "../../baseModules/skeleton/SkeletonManager";
import { ESkeletonAction, ESkeletonResType, ESkeletonType } from "../../baseModules/skeleton/SkeletonData";
import { <PERSON><PERSON> } from "laya/utils/Handler";
import { TDRolePathPointScript } from "../../tdBase/game/script/TDRolePathPointScript";
import { TDRolePatrolScript } from "../../tdBase/game/script/TDRolePatrolScript";
import { GuajiBuildWorkerStateRun } from "./fms/GuajiBuildWorkerStateRun";
import { EGuajiBuildPathPointType } from "../vo/GuajiBuildPathPointVo";
import { GuajiBuildWorkerStateWorking } from "./fms/GuajiBuildWorkerStateWorking";
import { ERoleAttrKind, ERoleAttrType } from "../../../scene2d/role/data/RoleConsts";
import { UrlConfig } from "../../../../game/UrlConfig";
import { GuajiBuildWorkerStateStand } from "./fms/GuajiBuildWorkerStateStand";
import CommonTalkView from "../../common/CommonTalkView";
import { W73DHeroView } from "../../baseModules/w73d/W73DHeroView";
import { Image } from "laya/ui/Image";
import { Rectangle } from "laya/maths/Rectangle";
import { DispatchManager } from "../../../managers/DispatchManager";
import { ConsoleUtils } from "../../../util/ConsoleUtils";
import { MathUtil2 } from "../../../util/MathUtil2";
export class GuajiBuildWorker extends RoleBase {
    constructor() {
        super();
        // public work_type: EGuajiBuildPathPointType = EGuajiBuildPathPointType.GO_AND_BACK;
        this.pathPointList = [];
        this.pathEndHandler = null;
        this.outPathEndHandler = null;
        /** 是否在回建筑,比如回祭坛 */
        this.isGoBack = false;
        /**工作时间后返回 */
        this.workDuration = -1;
        this.skName = "";
        this.width = this.height = 100;
        this.hitArea = Rectangle.create().setTo(-this.width / 2, -this.height, this.width, this.height);
        let imgClick = new Image();
        // imgClick.skin = CommonButton.BtnYellow;
        imgClick.name = "imgClick";
        imgClick.size(this.width, this.height);
        imgClick.pos(-this.width / 2, -this.height);
        imgClick.mouseEnabled = true;
        this.imgClick = imgClick;
        this.addChildAt(imgClick, 0);
        this.addEvent();
    }
    get firstPathPoint() {
        return this.pathPointList[0];
    }
    get lastPathPoint() {
        return this.pathPointList[this.pathPointList.length - 1];
    }
    get talkView() {
        if (!this._talkView) {
            this._talkView = new CommonTalkView();
            // this._talkView.bottom = 100;
            this._talkView.visible = false;
            this.addChild(this._talkView);
        }
        return this._talkView;
    }
    get workType() {
        return this.editorVo.work_type;
    }
    addEvent() {
        DispatchManager.addEventListener("GUAJI_BUILD_ON_BACK_MODE" /* GUAJI_BUILD_ON_BACK_MODE */, this, this.onBackMode);
    }
    removeEvent() {
        DispatchManager.removeEventListener("GUAJI_BUILD_ON_BACK_MODE" /* GUAJI_BUILD_ON_BACK_MODE */, this, this.onBackMode);
    }
    onBackMode(isBackMode) {
        if (this.sk) {
            if (isBackMode) {
                this.sk.paused();
            }
            else {
                this.sk.resume();
            }
        }
    }
    init() {
        this.addAttrValue(ERoleAttrKind.base, ERoleAttrType.moveSpeed, 100);
        this.curRoleFSMMgr = this.curRoleFSMMgr || new RoleFSMMgr(this);
        this.curRoleFSMMgr.setRoleState(ESkeletonAction.IDLE, new GuajiBuildWorkerStateStand(this.curRoleFSMMgr, ESkeletonAction.IDLE));
        this.curRoleFSMMgr.setRoleState(ESkeletonAction.RUN, new GuajiBuildWorkerStateRun(this.curRoleFSMMgr, ESkeletonAction.RUN));
        this.curRoleFSMMgr.setRoleState(ESkeletonAction.ATTACK, new GuajiBuildWorkerStateWorking(this.curRoleFSMMgr, ESkeletonAction.ATTACK));
        this.bloodView.visible = false;
        this.isShowShadow = false;
    }
    scale(x, y) {
        ConsoleUtils.error("----------GuajiBuildWorker.scale");
        return super.scale(x, y);
    }
    setSkScale(scaleX, scaleY) {
        this.sk.scaleX = scaleX;
        this.sk.scaleY = scaleY;
    }
    setSk(skName, skType) {
        if (this.skName == skName) {
            return;
        }
        this.skName = skName;
        let skUrl = UrlConfig.get_sk_url_by_mode(ESkeletonResType.SPINE_SKELETON, skName, skType); //SkeletonManager.ins.getSkUrlByCheckMode(skName, ESkeletonType.UI_EFFECT);
        if (UrlConfig.checkResExist(skUrl) == false) {
            return;
        }
        // if(this.skAni){
        //     this.skAni.destroy();
        // }
        // this.skAni = SkeletonManager.ins.createSkeleton(skName, skType);
        // this.addChild(this.skAni);
        if (this.sk) {
            this.sk.destroy();
        }
        this.sk = SkeletonManager.ins.createSkeleton(skName, ESkeletonType.MODEL_ACTION);
        // this.sk._refresh2d(skName);
        // let aniFrames = this.sk?.baseSkeleton;
        // if (aniFrames instanceof JySpineSkeleton) {
        //     aniFrames.cacheFrameRate = SkeletonData.SPINE_ANIMODE_CACHE_FRAME;
        // }
        // this.show3d.pos(this.width / 2, this.height);
        this.addChild(this.sk);
    }
    set3d(skName) {
        if (this.skName == skName) {
            return;
        }
        this.skName = skName;
        if (this.show3d) {
            this.show3d.destroy();
        }
        this.show3d = new W73DHeroView();
        this.show3d._refresh(skName);
        // this.show3d.pos(this.width / 2, this.height);
        this.addChild(this.show3d);
    }
    playAni(actionName, loop = false) {
        // super.playAni(actionName, loop);
        // if(this.skAni){
        //     this.skAni.playAction(actionName, loop);
        // }
        if (this.sk) {
            this.sk.playAction(actionName, loop);
        }
    }
    ToStand() {
        this.playAni(ESkeletonAction.STAND, true);
    }
    move(x, y, baseSpeed = 0) {
        if (baseSpeed > 0) {
            this.baseAttr.moveSpeed = baseSpeed;
        }
        this.tarX = x;
        this.tarY = y;
        this.curRoleFSMMgr.moveTarPos(x, y, this.baseAttr.moveSpeed);
        if (this.isGoBack && this.sk) {
            this.sk.playAction(ESkeletonAction.SPRINT, true);
        }
        else {
            this.sk.playAction(ESkeletonAction.RUN, true);
        }
        // this.zOrder = y;
        this.rotateTo(x, y);
        this.setRunAction();
    }
    setRunAction() {
        let sk = this.sk;
        if (sk) {
            sk.playAction(ESkeletonAction.SPRINT, true);
        }
        else {
            sk.playAction(ESkeletonAction.RUN, true);
        }
    }
    rotateTo(x, y) {
        if (this.sk) {
            let flipX = x < this.x ? -1 : 1;
            let scaleX = Math.abs(this.sk.scaleX);
            // this.show3d.scaleX = scaleX * flipX;
            this.setSkScale(scaleX * flipX, this.sk.scaleY);
        }
    }
    changeState(actionName) {
        this.curRoleFSMMgr.ChangeState(actionName);
    }
    checkCanMove() {
        return true;
    }
    /**路点寻路 */
    startPathPoint(pathPointList, baseSpeed = 0, endHandler = null) {
        this.stopPathPointAndPatrol();
        if (this.pathEndHandler) {
            this.pathEndHandler.clear();
        }
        if (this.outPathEndHandler) {
            this.outPathEndHandler.clear();
        }
        this.changeState(ESkeletonAction.RUN);
        this.pathEndHandler = new Handler(this, this.onPathEnd, null, false);
        this.outPathEndHandler = endHandler;
        let script = this.getOrAddComponent(TDRolePathPointScript);
        script.startPathPoint(pathPointList, false, baseSpeed, this.pathEndHandler);
        return script;
    }
    /**返回路点 */
    startReversePathPoint() {
        this.isGoBack = !this.isGoBack;
        let speed = this.baseAttr.moveSpeed;
        this.startPathPoint(this.pathPointList.reverse(), speed, this.outPathEndHandler);
    }
    stopPathPointAndPatrol() {
        let pathScript = this.getComponent(TDRolePathPointScript);
        if (pathScript) {
            pathScript.destroy();
        }
        let patrolScript = this.getComponent(TDRolePatrolScript);
        if (patrolScript) {
            patrolScript.destroy();
        }
        if (this.pathEndHandler) {
            this.pathEndHandler.clear();
        }
        if (this.outPathEndHandler) {
            this.outPathEndHandler.clear();
        }
    }
    onPathEnd() {
        let workType = this.editorVo.work_type;
        // if (workType == EGuajiBuildPathPointType.STAND_POSITION) {
        //     this.changeState(ESkeletonAction.ATTACK);
        // } else {
        //     if (this.isGoBack) {
        //         DispatchManager.dispatchEvent(ModuleCommand.GUAJI_BUILD_WORKER_PATH_END, { cfg: this.editorVo.cfg, point: this.lastPathPoint });
        //     }else{
        //         this.workDuration = MathUtil2.rangeInt(3000, 5000);
        //     }
        //     this.startReversePathPoint();
        // }
        // if (this.isGoBack) {
        //     DispatchManager.dispatchEvent(ModuleCommand.GUAJI_BUILD_WORKER_PATH_END, { cfg: this.editorVo.cfg, point: this.lastPathPoint });
        // }
        this.stopPathPointAndPatrol();
        if (workType == EGuajiBuildPathPointType.STAND_POSITION) {
            this.workDuration = -1;
            this.changeState(ESkeletonAction.ATTACK);
        }
        else {
            if (this.isGoBack) {
                DispatchManager.dispatchEvent("GUAJI_BUILD_WORKER_PATH_END" /* GUAJI_BUILD_WORKER_PATH_END */, { cfg: this.editorVo.cfg, point: this.lastPathPoint });
                this.startReversePathPoint();
            }
            else {
                this.workDuration = MathUtil2.rangeInt(1000, 2000);
                this.changeState(ESkeletonAction.ATTACK);
            }
        }
        if (this.outPathEndHandler) {
            this.outPathEndHandler.run();
        }
    }
    updateFrame(interval) {
        var _a, _b;
        if (this.destroyed) {
            return;
        }
        super.updateFrame(interval);
        (_a = this.getComponent(TDRolePathPointScript)) === null || _a === void 0 ? void 0 : _a._updateFrame(interval);
        (_b = this.getComponent(TDRolePatrolScript)) === null || _b === void 0 ? void 0 : _b._updateFrame(interval);
    }
    destroy(destroyChild) {
        this.removeEvent();
        super.destroy(destroyChild);
    }
}
