import { Ease } from "laya/utils/Ease";
import { Tween } from "laya/utils/Tween";
import { BaseScript } from "../../BaseScript";
import { Point } from "laya/maths/Point";
import { DialogManager } from "laya/ui/DialogManager";
import { Laya } from "Laya";
export var EUIPopupEffectType;
(function (EUIPopupEffectType) {
    EUIPopupEffectType["None"] = "";
    EUIPopupEffectType["FadeIn"] = "FadeIn";
    EUIPopupEffectType["Scale"] = "Scale";
    EUIPopupEffectType["MouseScale"] = "MouseScale";
    EUIPopupEffectType["CenterScale"] = "CenterScale";
    EUIPopupEffectType["LeftToRight"] = "LeftToRight";
    EUIPopupEffectType["FromPosition"] = "FromPosition";
})(EUIPopupEffectType || (EUIPopupEffectType = {}));
// export class UIPopupEffectArgs {
//     type: EUIPopupEffectType = EUIPopupEffectType.None;
//     delay = 0;
//     duration = 300;
//     oriVisible = true;
//     oriScaleX = 1;
//     oriScaleY = 1;
//     oriAlpha = 1;
//     oriX = 0;
//     oriY = 0;
//     fromX:number = null;
//     fromY:number = null;
//     ease:Function = null;
//     private constructor(type:EUIPopupEffectType){
//         this.type = type;
//     }
//     static create(type:EUIPopupEffectType){
//         //TODO 对象池
//         return new UIPopupEffectArgs(type)
//     }
// }
// export class UIPopupEffectScript extends BaseScript {
//     private script: UIPopupEffect_Base;
//     get view() {
//         return this.ownerSpr as View;
//     }
//     get args() {
//         return this.script?.args;
//     }
//     public setPopupEffect(effect: EUIPopupEffectType, args: UIPopupEffectArgs = null, openCallback: Handler = null, closeCallback: Handler = null) {
//         if (this.script) {
//             return;
//         }
//         args = args || UIPopupEffectArgs.create(EUIPopupEffectType.None);
//         if (effect == EUIPopupEffectType.CenterScale) {
//             this.script = this.view.addComponent(UIPopupEffect_CenterScale);
//         }
//         else if (effect == EUIPopupEffectType.FadeIn) {
//             this.script = this.view.addComponent(UIPopupEffect_FadeIn);
//         }
//         else if (effect == EUIPopupEffectType.Scale) {
//             this.script = this.view.addComponent(UIPopupEffect_Scale);
//         }
//         else if (effect == EUIPopupEffectType.FromPosition) {
//             this.script = this.view.addComponent(UIPopupEffect_FromPosition);
//         }
//         if(this.script){
//             this.script.setPopupEffect(effect, args, openCallback, closeCallback);
//         }
//     }
//     public onOpenView() {
//         this.script?.onOpenView();
//     }
//     public onCloseView() {
//         this.script?.onCloseView();
//     }
//     onDestroy(): void {
//         if (this.script) {
//             this.script.destroy();
//             this.script = null;
//         }
//     }
// }
export class UIPopupEffectScript extends BaseScript {
    constructor() {
        super();
        this.effType = EUIPopupEffectType.None;
        this._duration_ = 300;
        this._delay_ = 0;
        this._easeOpen_ = Ease.linearNone;
        this._easeClose_ = Ease.linearNone;
        this.tw = null;
        this.isOpening = false;
        this.isClosing = false;
        this.openCallback = null;
        this.closeCallback = null;
    }
    get fromScale() {
        return 0.3 * Math.min(DialogManager.ScaleX, DialogManager.ScaleY);
    }
    get view() {
        return this.ownerSpr;
    }
    _cloneTo(dest) {
        //获取所有key,判断以_开头和_结尾
        let keys = Object.keys(this) || [];
        for (let key of keys) {
            if (key[0] == "_" && key[key.length - 1] == "_") {
                dest[key] = this[key];
            }
        }
    }
    setPopupEffect(openCallback = null, closeCallback = null) {
        this.openCallback = openCallback;
        this.closeCallback = closeCallback;
    }
    onOpenView() {
        this.openCallback && this.openCallback.run();
    }
    _checkIsOpening() {
        if (this.isOpening) {
            return true;
        }
        this.isOpening = true;
        this.resetViewProps();
        return false;
    }
    onCloseView() {
        this.isClosing = true;
        this.closeCallback && this.closeCallback.run();
    }
    getCenterFromPos(oriScaleX, oriScaleY) {
        let view = this.view;
        // let pos = Point.create();
        // pos.x = (GlobalConfig.DeviceW - view.width * this.fromScale) / 2 + view.pivotX * this.fromScale;
        // pos.y = (GlobalConfig.DeviceH - view.height * this.fromScale) / 2 + view.pivotY * this.fromScale;
        // return pos;
        let fromX = (oriScaleX - this.fromScale) * view.width / 2 + view.x;
        let fromY = (oriScaleY - this.fromScale) * view.height / 2 + view.y;
        return Point.create().setTo(fromX, fromY);
    }
    getMousePos() {
        if (this.clickPos) {
            return this.clickPos;
        }
    }
    resetViewProps() {
    }
    to(props, ease = null, complete = null) {
        this._clearTw();
        this.tw = Tween.to(this.view, props, this._duration_, ease, complete, this._delay_, true, false);
    }
    from(props, ease = null, complete = null) {
        this._clearTw();
        this.tw = Tween.from(this.view, props, this._duration_, ease, complete, this._delay_, true, false);
    }
    _clearTw() {
        if (this.tw) {
            this.tw.clear();
            this.tw = null;
        }
    }
    onDestroy() {
        if (this.openCallback) {
            this.openCallback.clear();
            this.openCallback = null;
        }
        if (this.closeCallback) {
            this.closeCallback.clear();
            this.closeCallback = null;
        }
        this._clearTw();
    }
    static create(type) {
        if (window.MainUIManager.instance.is_ui_pop_effect_open == false) {
            return new UIPopupEffectScript();
        }
        let ret;
        switch (type) {
            case EUIPopupEffectType.None:
                ret = new UIPopupEffectScript();
                break;
            case EUIPopupEffectType.FadeIn:
                ret = new UIPopupEffect_FadeIn();
                break;
            case EUIPopupEffectType.Scale:
                ret = new UIPopupEffect_Scale();
                break;
            case EUIPopupEffectType.CenterScale:
                ret = new UIPopupEffect_CenterScale();
                break;
            case EUIPopupEffectType.MouseScale:
                ret = new UIPopupEffect_MouseScale();
                break;
            case EUIPopupEffectType.FromPosition:
                ret = new UIPopupEffect_FromPosition();
                break;
            default:
                ret = new UIPopupEffectScript();
                break;
        }
        return ret;
    }
}
export class UIPopupEffect_Scale extends UIPopupEffectScript {
    constructor() {
        super(...arguments);
        this.effType = EUIPopupEffectType.Scale;
        this._oriScaleX_ = 1;
        this._oriScaleY_ = 1;
    }
    resetViewProps() {
        super.resetViewProps();
        this._oriScaleX_ = this.view.scaleX;
        this._oriScaleY_ = this.view.scaleY;
        this._easeOpen_ = this._backOut;
        this._easeClose_ = Ease.backIn;
    }
    //幅度s小一点
    _backOut(t, b, c, d, s = 1.2) {
        return Ease.backOut(t, b, c, d, s);
    }
    onOpenView() {
        if (super._checkIsOpening()) {
            return true;
        }
        let view = this.view;
        let fromScale = this.fromScale;
        view.scale(fromScale, fromScale);
        this.to({ scaleX: this._oriScaleX_, scaleY: this._oriScaleX_ }, this._easeOpen_, this.openCallback);
    }
    onCloseView() {
        let view = this.view;
        let fromScale = this._oriScaleX_;
        view.scale(fromScale, fromScale);
        this.to({ scaleX: this.fromScale, scaleY: this.fromScale }, this._easeClose_, this.closeCallback);
    }
    onDestroy() {
        super.onDestroy();
    }
}
/**
 * 从界面中央弹出
 */
export class UIPopupEffect_CenterScale extends UIPopupEffect_Scale {
    constructor() {
        super(...arguments);
        this.effType = EUIPopupEffectType.CenterScale;
    }
    onOpenView() {
        if (super._checkIsOpening()) {
            return true;
        }
        let pos = this.getCenterFromPos(this._oriScaleX_, this._oriScaleY_);
        this.from({ x: pos.x, y: pos.y, scaleX: this.fromScale, scaleY: this.fromScale }, this._easeOpen_, this.openCallback);
    }
    onCloseView() {
        let pos = this.getCenterFromPos(this._oriScaleX_, this._oriScaleY_);
        this.to({ x: pos.x, y: pos.y, scaleX: this.fromScale, scaleY: this.fromScale }, this._easeClose_, this.closeCallback);
    }
}
export class UIPopupEffect_MouseScale extends UIPopupEffect_Scale {
    constructor() {
        super(...arguments);
        this.effType = EUIPopupEffectType.CenterScale;
        this._fromX_ = 0;
        this._fromY_ = 0;
    }
    //幅度s小一点
    _backOut(t, b, c, d, s = 1.1) {
        return Ease.backOut(t, b, c, d, s);
    }
    resetViewProps() {
        super.resetViewProps();
        let view = this.view;
        let mouseX = Laya.stage.mouseX;
        let mouseY = Laya.stage.mouseY;
        //计算跟stage的偏移
        // let globalPos = this.ownerSpr.parentPointToGlobal(Point.create().setTo(this.ownerSpr.x, this.ownerSpr.y));
        let parent = this.ownerSpr.parent;
        if (parent) {
            var localPos = parent.globalToLocal(Point.create().setTo(mouseX, mouseY));
        }
        else {
            var localPos = Point.create().setTo(0, 0);
        }
        this._fromX_ = localPos.x;
        this._fromY_ = localPos.y;
    }
    onOpenView() {
        if (super._checkIsOpening()) {
            return true;
        }
        this.from({ x: this._fromX_, y: this._fromY_, scaleX: this.fromScale, scaleY: this.fromScale }, this._easeOpen_, this.openCallback);
    }
    onCloseView() {
        this.to({ x: this._fromX_, y: this._fromY_, scaleX: this.fromScale, scaleY: this.fromScale }, this._easeClose_, this.closeCallback);
    }
}
/**
 * 淡入淡出
 */
export class UIPopupEffect_FadeIn extends UIPopupEffectScript {
    constructor() {
        super(...arguments);
        this.effType = EUIPopupEffectType.FadeIn;
        this._oriAlpha_ = 1;
    }
    onOpenView() {
        if (super._checkIsOpening()) {
            return true;
        }
        this.view.alpha = 0.1;
        this.to({ alpha: this._oriAlpha_ }, null, this.openCallback);
    }
    onCloseView() {
        this.view.alpha = this._oriAlpha_;
        this.to({ alpha: 0.1 }, null, this.closeCallback);
    }
}
/**
 * 位置变化
 */
export class UIPopupEffect_FromPosition extends UIPopupEffectScript {
    constructor() {
        super(...arguments);
        this.effType = EUIPopupEffectType.FromPosition;
        this._oriX_ = 0;
        this._oriY_ = 0;
        this._fromX_ = 0;
        this._fromY_ = 0;
    }
    resetViewProps() {
        super.resetViewProps();
    }
    onOpenView() {
        if (super._checkIsOpening()) {
            return true;
        }
        let view = this.view;
        this._oriX_ = view.x;
        this._oriY_ = view.y;
        view.x = this._fromX_ == null ? view.x : this._fromX_;
        view.y = this._fromY_ == null ? view.y : this._fromY_;
        this.to({ x: this._oriX_, y: this._oriY_ }, this._easeOpen_, this.openCallback);
    }
    onCloseView() {
        this.to({ x: this._fromX_, y: this._fromY_ }, this._easeClose_, this.closeCallback);
    }
}
