import { ClassUtils } from "laya/utils/ClassUtils";
import { m_treasure_info_toc } from "./line/m_treasure_info_toc";
import { m_family_boss_info_toc } from "./line/m_family_boss_info_toc";
import { m_world_boss_fight_result_toc } from "./line/m_world_boss_fight_result_toc";
import { m_travel_speed_toc } from "./line/m_travel_speed_toc";
import { m_peak_info_battle_toc } from "./line/m_peak_info_battle_toc";
import { m_family_boss_fight_result_toc } from "./line/m_family_boss_fight_result_toc";
import { m_csclan_solo_shop_lv_toc } from "./line/m_csclan_solo_shop_lv_toc";
import { m_team_info_my_team_toc } from "./line/m_team_info_my_team_toc";
import { m_treasure_refresh_toc } from "./line/m_treasure_refresh_toc";
import { m_peak_info_get_msg_toc } from "./line/m_peak_info_get_msg_toc";
import { m_peak_personal_info_toc } from "./line/m_peak_personal_info_toc";
import { m_treasure_gift_active_toc } from "./line/m_treasure_gift_active_toc";
import { m_hzzd_route_info_toc } from "./line/m_hzzd_route_info_toc";
import { m_goods_show_goods_toc } from "./line/m_goods_show_goods_toc";
import { m_hongbao_fetch_toc } from "./line/m_hongbao_fetch_toc";
import { m_treasure_log_toc } from "./line/m_treasure_log_toc";
import { m_dawanka_fetch_toc } from "./line/m_dawanka_fetch_toc";
import { m_travel_fetch_toc } from "./line/m_travel_fetch_toc";
import { m_csclan_solo_box_toc } from "./line/m_csclan_solo_box_toc";
import { m_test_tower_op_toc } from "./line/m_test_tower_op_toc";
import { m_random_pvp_info_toc } from "./line/m_random_pvp_info_toc";
import { m_treasure_cal_time_toc } from "./line/m_treasure_cal_time_toc";
import { m_dominate_pvp_new_season_toc } from "./line/m_dominate_pvp_new_season_toc";
import { m_level_gift_info_toc } from "./line/m_level_gift_info_toc";
import { m_mock_pvp_ready_toc } from "./line/m_mock_pvp_ready_toc";
import { m_cross_ladder_info_toc } from "./line/m_cross_ladder_info_toc";
import { m_mini_game_get_str_toc } from "./line/m_mini_game_get_str_toc";
import { m_modular_activity_war_log_info_toc } from "./line/m_modular_activity_war_log_info_toc";
import { m_team_lineup_set_toc } from "./line/m_team_lineup_set_toc";
import { m_mock_pvp_contest_chat_toc } from "./line/m_mock_pvp_contest_chat_toc";
import { m_welfare_activate_code_toc } from "./line/m_welfare_activate_code_toc";
import { m_main_battle_auto_toc } from "./line/m_main_battle_auto_toc";
import { m_test_tower_continuous_fight_result_toc } from "./line/m_test_tower_continuous_fight_result_toc";
import { m_family_boss_sweep_toc } from "./line/m_family_boss_sweep_toc";
import { m_family_task_gift_toc } from "./line/m_family_task_gift_toc";
import { m_mock_pvp_heat_toc } from "./line/m_mock_pvp_heat_toc";
import { m_fish_lineup_list_toc } from "./line/m_fish_lineup_list_toc";
import { m_goods_list_toc } from "./line/m_goods_list_toc";
import { m_level_gift_fetch_toc } from "./line/m_level_gift_fetch_toc";
import { m_treasure_worker_active_toc } from "./line/m_treasure_worker_active_toc";
import { m_rent_hero_list_toc } from "./line/m_rent_hero_list_toc";
import { m_fish_info_toc } from "./line/m_fish_info_toc";
import { m_treasure_fetch_toc } from "./line/m_treasure_fetch_toc";
import { m_treasure_worker_info_toc } from "./line/m_treasure_worker_info_toc";
import { m_fuli_yueka_fetch_toc } from "./line/m_fuli_yueka_fetch_toc";
import { m_goods_update_toc } from "./line/m_goods_update_toc";
import { m_hzzd_fetch_toc } from "./line/m_hzzd_fetch_toc";
import { m_cross_ladder_list_toc } from "./line/m_cross_ladder_list_toc";
import { m_ares_palace_info_toc } from "./line/m_ares_palace_info_toc";
import { m_team_xswh_fight_result_toc } from "./line/m_team_xswh_fight_result_toc";
import { m_boat_race_info_toc } from "./line/m_boat_race_info_toc";
import { m_team_xswh_fetch_toc } from "./line/m_team_xswh_fetch_toc";
import { m_vip_info_toc } from "./line/m_vip_info_toc";
import { m_pass_check_info_toc } from "./line/m_pass_check_info_toc";
import { m_vip_buy_gift_toc } from "./line/m_vip_buy_gift_toc";
import { m_role_base_reload_toc } from "./line/m_role_base_reload_toc";
import { m_family_task_reward_toc } from "./line/m_family_task_reward_toc";
import { m_wars_update_city_toc } from "./line/m_wars_update_city_toc";
import { m_wars_city_info_toc } from "./line/m_wars_city_info_toc";
import { m_rent_hero_look_toc } from "./line/m_rent_hero_look_toc";
import { m_role_setting_list_toc } from "./line/m_role_setting_list_toc";
import { m_wars_team_op_toc } from "./line/m_wars_team_op_toc";
import { m_lord_op_toc } from "./line/m_lord_op_toc";
import { m_lord_equip_compose_toc } from "./line/m_lord_equip_compose_toc";
import { m_lord_treasure_op_toc } from "./line/m_lord_treasure_op_toc";
import { m_wars_act_info_toc } from "./line/m_wars_act_info_toc";
import { m_role_setting_toc } from "./line/m_role_setting_toc";
import { m_csclan_list_toc } from "./line/m_csclan_list_toc";
import { m_role_look_hero_toc } from "./line/m_role_look_hero_toc";
import { m_role_look_hero_attr_source_toc } from "./line/m_role_look_hero_attr_source_toc";
import { m_role_setting2_toc } from "./line/m_role_setting2_toc";
import { m_role_look_sdk_profile_toc } from "./line/m_role_look_sdk_profile_toc";
import { m_role_look_lord_toc } from "./line/m_role_look_lord_toc";
import { m_wars_kill_info_toc } from "./line/m_wars_kill_info_toc";
import { m_wars_select_army_toc } from "./line/m_wars_select_army_toc";
import { m_wars_info_get_msg_toc } from "./line/m_wars_info_get_msg_toc";
import { m_wars_info_nty_rank_data_toc } from "./line/m_wars_info_nty_rank_data_toc";
import { m_wars_event_toc } from "./line/m_wars_event_toc";
import { m_wars_make_path_toc } from "./line/m_wars_make_path_toc";
import { m_wars_init_camp_toc } from "./line/m_wars_init_camp_toc";
import { m_wars_update_role_toc } from "./line/m_wars_update_role_toc";
import { m_team_info_teams_toc } from "./line/m_team_info_teams_toc";
import { m_wars_daily_report_toc } from "./line/m_wars_daily_report_toc";
import { m_welfare_sdk_share_info_toc } from "./line/m_welfare_sdk_share_info_toc";
import { m_daily_mission_gift_toc } from "./line/m_daily_mission_gift_toc";
import { m_main_battle_info_toc } from "./line/m_main_battle_info_toc";
import { m_red_cliff_sweep_toc } from "./line/m_red_cliff_sweep_toc";
import { m_boat_race_update_toc } from "./line/m_boat_race_update_toc";
import { m_csclan_create_toc } from "./line/m_csclan_create_toc";
import { m_role_level_up_toc } from "./line/m_role_level_up_toc";
import { m_main_battle_fight_result_toc } from "./line/m_main_battle_fight_result_toc";
import { m_travel_info_toc } from "./line/m_travel_info_toc";
import { m_progress_gift_buy_toc } from "./line/m_progress_gift_buy_toc";
import { m_family_boss_update_toc } from "./line/m_family_boss_update_toc";
import { m_wars_operate_toc } from "./line/m_wars_operate_toc";
import { m_itinerant_shop_info_toc } from "./line/m_itinerant_shop_info_toc";
import { m_hongbao_fetch_update_toc } from "./line/m_hongbao_fetch_update_toc";
import { m_medal_update_toc } from "./line/m_medal_update_toc";
import { m_csc_fmsolo_info_toc } from "./line/m_csc_fmsolo_info_toc";
import { m_travel_refresh_toc } from "./line/m_travel_refresh_toc";
import { m_hero_pass_info_toc } from "./line/m_hero_pass_info_toc";
import { m_eight_login_info_toc } from "./line/m_eight_login_info_toc";
import { m_pass_check_fetch_toc } from "./line/m_pass_check_fetch_toc";
import { m_maze_info_toc } from "./line/m_maze_info_toc";
import { m_equip_auto_unload_toc } from "./line/m_equip_auto_unload_toc";
import { m_mission_shop_buy_toc } from "./line/m_mission_shop_buy_toc";
import { m_team_xswh_info_toc } from "./line/m_team_xswh_info_toc";
import { m_simp_mission_update_toc } from "./line/m_simp_mission_update_toc";
import { m_csclan_self_toc } from "./line/m_csclan_self_toc";
import { m_csclan_audit_limit_toc } from "./line/m_csclan_audit_limit_toc";
import { m_cross_ladder_logs_toc } from "./line/m_cross_ladder_logs_toc";
import { m_csclan_set_owner_toc } from "./line/m_csclan_set_owner_toc";
import { m_csclan_update_notice_toc } from "./line/m_csclan_update_notice_toc";
import { m_csclan_operate_toc } from "./line/m_csclan_operate_toc";
import { m_csclan_members_toc } from "./line/m_csclan_members_toc";
import { m_csclan_apply_list_toc } from "./line/m_csclan_apply_list_toc";
import { m_csclan_logs_toc } from "./line/m_csclan_logs_toc";
import { m_csclan_rename_toc } from "./line/m_csclan_rename_toc";
import { m_csclan_base_toc } from "./line/m_csclan_base_toc";
import { m_csclan_scene_toc } from "./line/m_csclan_scene_toc";
import { m_quick_shop_info_toc } from "./line/m_quick_shop_info_toc";
import { m_daily_gift_tehui_info_toc } from "./line/m_daily_gift_tehui_info_toc";
import { m_mock_pvp_logs_toc } from "./line/m_mock_pvp_logs_toc";
import { m_family_apply_list_toc } from "./line/m_family_apply_list_toc";
import { m_multi_lineup_get_power_toc } from "./line/m_multi_lineup_get_power_toc";
import { m_daily_gift_new_discount_info_toc } from "./line/m_daily_gift_new_discount_info_toc";
import { m_pass_check_update_toc } from "./line/m_pass_check_update_toc";
import { m_family_join_toc } from "./line/m_family_join_toc";
import { m_maze_start_toc } from "./line/m_maze_start_toc";
import { m_fight_set_speed_toc } from "./line/m_fight_set_speed_toc";
import { m_small_game_info_toc } from "./line/m_small_game_info_toc";
import { m_master_card_notice_toc } from "./line/m_master_card_notice_toc";
import { m_family_reply_join_toc } from "./line/m_family_reply_join_toc";
import { m_lord_skill_set_toc } from "./line/m_lord_skill_set_toc";
import { m_master_card_power_toc } from "./line/m_master_card_power_toc";
import { m_master_card_spe_effect_toc } from "./line/m_master_card_spe_effect_toc";
import { m_boat_race_rank_toc } from "./line/m_boat_race_rank_toc";
import { m_lcqs_fight_result_toc } from "./line/m_lcqs_fight_result_toc";
import { m_bag_goods_list_toc } from "./line/m_bag_goods_list_toc";
import { m_family_set_owner_toc } from "./line/m_family_set_owner_toc";
import { m_mission_shop_update_toc } from "./line/m_mission_shop_update_toc";
import { m_world_level_info_toc } from "./line/m_world_level_info_toc";
import { m_master_card_decoration_op_toc } from "./line/m_master_card_decoration_op_toc";
import { m_battle_trial_info_toc } from "./line/m_battle_trial_info_toc";
import { m_role_attr_change_toc } from "./line/m_role_attr_change_toc";
import { m_fish_lottery_toc } from "./line/m_fish_lottery_toc";
import { m_boat_race_bonus_toc } from "./line/m_boat_race_bonus_toc";
import { m_main_battle_mission_status_toc } from "./line/m_main_battle_mission_status_toc";
import { m_boat_race_logs_toc } from "./line/m_boat_race_logs_toc";
import { m_maze_fetch_toc } from "./line/m_maze_fetch_toc";
import { m_god_trial_info_toc } from "./line/m_god_trial_info_toc";
import { m_battle_trial_fetch_info_toc } from "./line/m_battle_trial_fetch_info_toc";
import { m_hero_zhouyin_info_toc } from "./line/m_hero_zhouyin_info_toc";
import { m_main_battle_box_info_toc } from "./line/m_main_battle_box_info_toc";
import { m_hero_pass_fetch_toc } from "./line/m_hero_pass_fetch_toc";
import { m_boat_race_result_toc } from "./line/m_boat_race_result_toc";
import { m_hero_comment_info_toc } from "./line/m_hero_comment_info_toc";
import { m_lcqs_chapter_fetch_toc } from "./line/m_lcqs_chapter_fetch_toc";
import { m_guandu_update_event_toc } from "./line/m_guandu_update_event_toc";
import { m_hongbao_info_update_toc } from "./line/m_hongbao_info_update_toc";
import { m_guandu_next_toc } from "./line/m_guandu_next_toc";
import { m_master_card_replace_toc } from "./line/m_master_card_replace_toc";
import { m_master_card_up_official_toc } from "./line/m_master_card_up_official_toc";
import { m_family_cancel_join_toc } from "./line/m_family_cancel_join_toc";
import { m_master_card_preview_attr_toc } from "./line/m_master_card_preview_attr_toc";
import { m_change_day_toc } from "./line/m_change_day_toc";
import { m_casting_soul_op_toc } from "./line/m_casting_soul_op_toc";
import { m_master_card_decoration_info_toc } from "./line/m_master_card_decoration_info_toc";
import { m_family_update_notice_toc } from "./line/m_family_update_notice_toc";
import { m_family_operate_toc } from "./line/m_family_operate_toc";
import { m_family_info_change_toc } from "./line/m_family_info_change_toc";
import { m_family_logs_toc } from "./line/m_family_logs_toc";
import { m_family_update_member_toc } from "./line/m_family_update_member_toc";
import { m_family_rename_toc } from "./line/m_family_rename_toc";
import { m_family_info_toc } from "./line/m_family_info_toc";
import { m_family_attr_tip_toc } from "./line/m_family_attr_tip_toc";
import { m_family_flagname_toc } from "./line/m_family_flagname_toc";
import { m_family_transfer_count_toc } from "./line/m_family_transfer_count_toc";
import { m_family_transfer_join_toc } from "./line/m_family_transfer_join_toc";
import { m_family_hot_toc } from "./line/m_family_hot_toc";
import { m_family_hot_op_toc } from "./line/m_family_hot_op_toc";
import { m_time_activity_info_toc } from "./line/m_time_activity_info_toc";
import { m_hint_show_tip_toc } from "./line/m_hint_show_tip_toc";
import { m_god_trial_buff_toc } from "./line/m_god_trial_buff_toc";
import { m_battle_trial_daily_end_info_toc } from "./line/m_battle_trial_daily_end_info_toc";
import { m_monster_group_power_toc } from "./line/m_monster_group_power_toc";
import { m_vip_free_gift_info_toc } from "./line/m_vip_free_gift_info_toc";
import { m_lord_info_toc } from "./line/m_lord_info_toc";
import { m_boat_race_alloc_items_toc } from "./line/m_boat_race_alloc_items_toc";
import { m_hzzd_info_history_toc } from "./line/m_hzzd_info_history_toc";
import { m_maze_hero_toc } from "./line/m_maze_hero_toc";
import { m_god_equip_compose_toc } from "./line/m_god_equip_compose_toc";
import { m_battle_trial_offline_info_toc } from "./line/m_battle_trial_offline_info_toc";
import { m_monster_group_power_list_toc } from "./line/m_monster_group_power_list_toc";
import { m_world_boss_info_toc } from "./line/m_world_boss_info_toc";
import { m_peak_info_member_toc } from "./line/m_peak_info_member_toc";
import { m_lord_set_lineup_toc } from "./line/m_lord_set_lineup_toc";
import { m_gmcmd_battle_edit_toc } from "./line/m_gmcmd_battle_edit_toc";
import { m_chat_get_goods_toc } from "./line/m_chat_get_goods_toc";
import { m_ranking_history_list_toc } from "./line/m_ranking_history_list_toc";
import { m_story_maze_fetch_toc } from "./line/m_story_maze_fetch_toc";
import { m_god_equip_select_toc } from "./line/m_god_equip_select_toc";
import { m_family_boss_look_toc } from "./line/m_family_boss_look_toc";
import { m_world_boss_buy_times_toc } from "./line/m_world_boss_buy_times_toc";
import { m_random_pvp_op_toc } from "./line/m_random_pvp_op_toc";
import { m_wars_init_toc } from "./line/m_wars_init_toc";
import { m_god_equip_recast_toc } from "./line/m_god_equip_recast_toc";
import { m_pass_behead_battle_toc } from "./line/m_pass_behead_battle_toc";
import { m_treasure_pull_list_toc } from "./line/m_treasure_pull_list_toc";
import { m_large_peak_info_look_member_toc } from "./line/m_large_peak_info_look_member_toc";
import { m_battle_trial_battle_up_toc } from "./line/m_battle_trial_battle_up_toc";
import { m_broadcast_normal_toc } from "./line/m_broadcast_normal_toc";
import { m_stage_breed_info_toc } from "./line/m_stage_breed_info_toc";
import { m_boat_peak_member_toc } from "./line/m_boat_peak_member_toc";
import { m_shop_update_toc } from "./line/m_shop_update_toc";
import { m_family_hongbao_fetch_update_toc } from "./line/m_family_hongbao_fetch_update_toc";
import { m_family_hongbao_mission_info_toc } from "./line/m_family_hongbao_mission_info_toc";
import { m_peak_info_toc } from "./line/m_peak_info_toc";
import { m_activity_update_fetch_toc } from "./line/m_activity_update_fetch_toc";
import { m_theme_activity_up_star_reward_info_toc } from "./line/m_theme_activity_up_star_reward_info_toc";
import { m_boat_peak_look_pre_score_toc } from "./line/m_boat_peak_look_pre_score_toc";
import { m_boat_peak_join_members_toc } from "./line/m_boat_peak_join_members_toc";
import { m_boat_peak_fetch_toc } from "./line/m_boat_peak_fetch_toc";
import { m_profile_update_toc } from "./line/m_profile_update_toc";
import { m_team_info_my_team_update_toc } from "./line/m_team_info_my_team_update_toc";
import { m_hero_resonate_dhyana_op_toc } from "./line/m_hero_resonate_dhyana_op_toc";
import { m_hero_resonate_equip_op_toc } from "./line/m_hero_resonate_equip_op_toc";
import { m_god_equip_enchant_toc } from "./line/m_god_equip_enchant_toc";
import { m_battle_trial_battle_reset_toc } from "./line/m_battle_trial_battle_reset_toc";
import { m_equip_reinforce_toc } from "./line/m_equip_reinforce_toc";
import { m_random_pvp_match_toc } from "./line/m_random_pvp_match_toc";
import { m_peak_info_opp_toc } from "./line/m_peak_info_opp_toc";
import { m_player_strategy_info_toc } from "./line/m_player_strategy_info_toc";
import { m_equip_recycle_toc } from "./line/m_equip_recycle_toc";
import { m_main_battle_box_upgrade_toc } from "./line/m_main_battle_box_upgrade_toc";
import { m_war_flag_op_toc } from "./line/m_war_flag_op_toc";
import { m_equip_decompose_toc } from "./line/m_equip_decompose_toc";
import { m_equip_compose_toc } from "./line/m_equip_compose_toc";
import { m_family_boss_attr_toc } from "./line/m_family_boss_attr_toc";
import { m_arena_list_toc } from "./line/m_arena_list_toc";
import { m_activity_limit_sign_info_toc } from "./line/m_activity_limit_sign_info_toc";
import { m_time_activity_update_toc } from "./line/m_time_activity_update_toc";
import { m_squad_lineup_set_toc } from "./line/m_squad_lineup_set_toc";
import { m_bingfu_info_toc } from "./line/m_bingfu_info_toc";
import { m_activity_yueka_info_toc } from "./line/m_activity_yueka_info_toc";
import { m_page_list_toc } from "./line/m_page_list_toc";
import { m_equip_info_update_toc } from "./line/m_equip_info_update_toc";
import { m_family_boss_gather_toc } from "./line/m_family_boss_gather_toc";
import { m_fight_simp_result_toc } from "./line/m_fight_simp_result_toc";
import { m_lord_star_toc } from "./line/m_lord_star_toc";
import { m_activity_list_toc } from "./line/m_activity_list_toc";
import { m_team_operate_toc } from "./line/m_team_operate_toc";
import { m_bingfu_breed_toc } from "./line/m_bingfu_breed_toc";
import { m_daily_ad_code_info_toc } from "./line/m_daily_ad_code_info_toc";
import { m_random_pvp_palace_toc } from "./line/m_random_pvp_palace_toc";
import { m_arena_fight_result_toc } from "./line/m_arena_fight_result_toc";
import { m_item_show_gains_toc } from "./line/m_item_show_gains_toc";
import { m_boat_race_boats_toc } from "./line/m_boat_race_boats_toc";
import { m_complaints_info_toc } from "./line/m_complaints_info_toc";
import { m_time_achievement_info_toc } from "./line/m_time_achievement_info_toc";
import { m_fuli_token_buy_toc } from "./line/m_fuli_token_buy_toc";
import { m_hunt_gift_toc } from "./line/m_hunt_gift_toc";
import { m_star_plan_update_toc } from "./line/m_star_plan_update_toc";
import { m_hero_lock_toc } from "./line/m_hero_lock_toc";
import { m_system_setting_toc } from "./line/m_system_setting_toc";
import { m_hero_recycle_toc } from "./line/m_hero_recycle_toc";
import { m_system_time_toc } from "./line/m_system_time_toc";
import { m_hero_down_lineup_toc } from "./line/m_hero_down_lineup_toc";
import { m_fish_notice_toc } from "./line/m_fish_notice_toc";
import { m_fish_preview_attr_toc } from "./line/m_fish_preview_attr_toc";
import { m_fish_power_toc } from "./line/m_fish_power_toc";
import { m_fish_spe_effect_toc } from "./line/m_fish_spe_effect_toc";
import { m_fish_logs_toc } from "./line/m_fish_logs_toc";
import { m_fish_handbook_info_toc } from "./line/m_fish_handbook_info_toc";
import { m_hero_recycle_change_toc } from "./line/m_hero_recycle_change_toc";
import { m_hero_my_rank_toc } from "./line/m_hero_my_rank_toc";
import { m_wing_info_toc } from "./line/m_wing_info_toc";
import { m_hero_inherit_update_toc } from "./line/m_hero_inherit_update_toc";
import { m_hero_recycle_down_toc } from "./line/m_hero_recycle_down_toc";
import { m_hero_recycle_times_toc } from "./line/m_hero_recycle_times_toc";
import { m_hero_act_fourteen_toc } from "./line/m_hero_act_fourteen_toc";
import { m_hero_evolve_op_toc } from "./line/m_hero_evolve_op_toc";
import { m_hero_evolve_info_toc } from "./line/m_hero_evolve_info_toc";
import { m_item_sale_toc } from "./line/m_item_sale_toc";
import { m_random_pvp_update_toc } from "./line/m_random_pvp_update_toc";
import { m_bingfu_shift_toc } from "./line/m_bingfu_shift_toc";
import { m_equip_info_toc } from "./line/m_equip_info_toc";
import { m_team_info_my_team_apply_toc } from "./line/m_team_info_my_team_apply_toc";
import { m_rank_mission_info_toc } from "./line/m_rank_mission_info_toc";
import { m_daily_mission_info_toc } from "./line/m_daily_mission_info_toc";
import { m_treasure_dispatch_toc } from "./line/m_treasure_dispatch_toc";
import { m_mock_pvp_op_toc } from "./line/m_mock_pvp_op_toc";
import { m_common_tips_toc } from "./line/m_common_tips_toc";
import { m_arena_max_reward_info_toc } from "./line/m_arena_max_reward_info_toc";
import { m_random_pvp_fight_result_toc } from "./line/m_random_pvp_fight_result_toc";
import { m_peak_info_bet_toc } from "./line/m_peak_info_bet_toc";
import { m_equip_load_toc } from "./line/m_equip_load_toc";
import { m_profile_info_toc } from "./line/m_profile_info_toc";
import { m_treasure_other_toc } from "./line/m_treasure_other_toc";
import { m_mock_pvp_info_toc } from "./line/m_mock_pvp_info_toc";
import { m_family_boss_rank_toc } from "./line/m_family_boss_rank_toc";
import { m_fight_share_chat_toc } from "./line/m_fight_share_chat_toc";
import { m_vip_buy_recommend_toc } from "./line/m_vip_buy_recommend_toc";
import { m_boat_race_event_toc } from "./line/m_boat_race_event_toc";
import { m_random_pvp_new_season_toc } from "./line/m_random_pvp_new_season_toc";
import { m_peak_info_history_toc } from "./line/m_peak_info_history_toc";
import { m_equip_unload_toc } from "./line/m_equip_unload_toc";
import { m_profile_change_toc } from "./line/m_profile_change_toc";
import { m_shortcut_shop_info_toc } from "./line/m_shortcut_shop_info_toc";
import { m_csc_fmsolo_group_toc } from "./line/m_csc_fmsolo_group_toc";
import { m_mock_pvp_look_hero_toc } from "./line/m_mock_pvp_look_hero_toc";
import { m_csclan_solo_info_toc } from "./line/m_csclan_solo_info_toc";
import { m_story_maze_hero_toc } from "./line/m_story_maze_hero_toc";
import { m_equip_auto_load_toc } from "./line/m_equip_auto_load_toc";
import { m_team_lineup_get_toc } from "./line/m_team_lineup_get_toc";
import { m_guide_mission_toc } from "./line/m_guide_mission_toc";
import { m_red_cliff_info_toc } from "./line/m_red_cliff_info_toc";
import { m_soul_hero_info_toc } from "./line/m_soul_hero_info_toc";
import { m_shortcut_shop_buy_toc } from "./line/m_shortcut_shop_buy_toc";
import { m_activity_info_toc } from "./line/m_activity_info_toc";
import { m_hero_pass_gift_toc } from "./line/m_hero_pass_gift_toc";
import { m_hero_pass_update_toc } from "./line/m_hero_pass_update_toc";
import { m_activity_shop_update_toc } from "./line/m_activity_shop_update_toc";
import { m_equip_auto_reinforce_toc } from "./line/m_equip_auto_reinforce_toc";
import { m_rent_hero_op_toc } from "./line/m_rent_hero_op_toc";
import { m_activity_exam_answer_toc } from "./line/m_activity_exam_answer_toc";
import { m_activity_gift_toc } from "./line/m_activity_gift_toc";
import { m_equip_auto_compose_info_toc } from "./line/m_equip_auto_compose_info_toc";
import { m_equip_compose_logs_toc } from "./line/m_equip_compose_logs_toc";
import { m_equip_replace_toc } from "./line/m_equip_replace_toc";
import { m_pass_check_gift_toc } from "./line/m_pass_check_gift_toc";
import { m_equip_filter_info_toc } from "./line/m_equip_filter_info_toc";
import { m_equip_star_op_toc } from "./line/m_equip_star_op_toc";
import { m_equip_bingfu_recast_toc } from "./line/m_equip_bingfu_recast_toc";
import { m_equip_bingfu_decompose_toc } from "./line/m_equip_bingfu_decompose_toc";
import { m_maze_spoils_toc } from "./line/m_maze_spoils_toc";
import { m_equip_bingfa_op_toc } from "./line/m_equip_bingfa_op_toc";
import { m_equip_bingfa_exchange_toc } from "./line/m_equip_bingfa_exchange_toc";
import { m_equip_lock_toc } from "./line/m_equip_lock_toc";
import { m_wing_op_toc } from "./line/m_wing_op_toc";
import { m_team_lineup_list_toc } from "./line/m_team_lineup_list_toc";
import { m_lord_lineup_list_toc } from "./line/m_lord_lineup_list_toc";
import { m_deputy_info_toc } from "./line/m_deputy_info_toc";
import { m_activity_shop_info_toc } from "./line/m_activity_shop_info_toc";
import { m_guide_hint_toc } from "./line/m_guide_hint_toc";
import { m_mock_pvp_schemes_toc } from "./line/m_mock_pvp_schemes_toc";
import { m_battle_trial_hanging_info_toc } from "./line/m_battle_trial_hanging_info_toc";
import { m_csclan_solo_fetch_toc } from "./line/m_csclan_solo_fetch_toc";
import { m_csclan_solo_group_toc } from "./line/m_csclan_solo_group_toc";
import { m_medal_list_toc } from "./line/m_medal_list_toc";
import { m_fuli_fund_fetch_toc } from "./line/m_fuli_fund_fetch_toc";
import { m_welfare_sdk_share_toc } from "./line/m_welfare_sdk_share_toc";
import { m_wars_sign_toc } from "./line/m_wars_sign_toc";
import { m_activity_exam_list_toc } from "./line/m_activity_exam_list_toc";
import { m_role_family_change_toc } from "./line/m_role_family_change_toc";
import { m_quick_shop_tips_toc } from "./line/m_quick_shop_tips_toc";
import { m_master_card_lottery_toc } from "./line/m_master_card_lottery_toc";
import { m_wars_team_info_toc } from "./line/m_wars_team_info_toc";
import { m_letter_get_toc } from "./line/m_letter_get_toc";
import { m_mock_pvp_config_toc } from "./line/m_mock_pvp_config_toc";
import { m_wars_team_hero_info_toc } from "./line/m_wars_team_hero_info_toc";
import { m_hero_skin_info_toc } from "./line/m_hero_skin_info_toc";
import { m_microterminal_info_toc } from "./line/m_microterminal_info_toc";
import { m_role_update_ext_toc } from "./line/m_role_update_ext_toc";
import { m_role_best_hero_toc } from "./line/m_role_best_hero_toc";
import { m_team_merge_reward_toc } from "./line/m_team_merge_reward_toc";
import { m_role_look_lineup_toc } from "./line/m_role_look_lineup_toc";
import { m_guide_step_toc } from "./line/m_guide_step_toc";
import { m_master_talent_science_update_toc } from "./line/m_master_talent_science_update_toc";
import { m_system_heartbeat_toc } from "./line/m_system_heartbeat_toc";
import { m_wars_look_op_army_toc } from "./line/m_wars_look_op_army_toc";
import { m_daily_fuli_info_toc } from "./line/m_daily_fuli_info_toc";
import { m_cross_ladder_fight_result_toc } from "./line/m_cross_ladder_fight_result_toc";
import { m_microterminal_sign_fetch_toc } from "./line/m_microterminal_sign_fetch_toc";
import { m_wars_look_op_score_rank_toc } from "./line/m_wars_look_op_score_rank_toc";
import { m_stage_copy_info_toc } from "./line/m_stage_copy_info_toc";
import { m_wars_look_op_fight_log_toc } from "./line/m_wars_look_op_fight_log_toc";
import { m_wars_look_op_city_log_toc } from "./line/m_wars_look_op_city_log_toc";
import { m_wars_look_op_sign_toc } from "./line/m_wars_look_op_sign_toc";
import { m_team_share_list_toc } from "./line/m_team_share_list_toc";
import { m_wars_look_op_camp_rank_toc } from "./line/m_wars_look_op_camp_rank_toc";
import { m_guide_event_toc } from "./line/m_guide_event_toc";
import { m_system_error_toc } from "./line/m_system_error_toc";
import { m_role_cross_group_toc } from "./line/m_role_cross_group_toc";
import { m_gmcmd_do_toc } from "./line/m_gmcmd_do_toc";
import { m_story_maze_pub_toc } from "./line/m_story_maze_pub_toc";
import { m_war_flag_info_toc } from "./line/m_war_flag_info_toc";
import { m_ranking_list_toc } from "./line/m_ranking_list_toc";
import { m_rank_mission_update_toc } from "./line/m_rank_mission_update_toc";
import { m_arena_update_toc } from "./line/m_arena_update_toc";
import { m_stage_copy_drop_group_toc } from "./line/m_stage_copy_drop_group_toc";
import { m_arena_logs_toc } from "./line/m_arena_logs_toc";
import { m_chat_auth_toc } from "./line/m_chat_auth_toc";
import { m_td_simp_info_toc } from "./line/m_td_simp_info_toc";
import { m_td_mission_info_toc } from "./line/m_td_mission_info_toc";
import { m_lord_recycle_preview_toc } from "./line/m_lord_recycle_preview_toc";
import { m_chat_channel_toc } from "./line/m_chat_channel_toc";
import { m_letter_operate_toc } from "./line/m_letter_operate_toc";
import { m_system_message_toc } from "./line/m_system_message_toc";
import { m_hero_skin_upgrade_toc } from "./line/m_hero_skin_upgrade_toc";
import { m_chat_private_history_toc } from "./line/m_chat_private_history_toc";
import { m_hero_list_toc } from "./line/m_hero_list_toc";
import { m_chat_misc_toc } from "./line/m_chat_misc_toc";
import { m_war_flag_active_toc } from "./line/m_war_flag_active_toc";
import { m_chat_cost_toc } from "./line/m_chat_cost_toc";
import { m_stage_copy_fetch_toc } from "./line/m_stage_copy_fetch_toc";
import { m_login_scene_toc } from "./line/m_login_scene_toc";
import { m_acc_gift_info_toc } from "./line/m_acc_gift_info_toc";
import { m_qxzl_info_get_msg_toc } from "./line/m_qxzl_info_get_msg_toc";
import { m_sys_daily_info_toc } from "./line/m_sys_daily_info_toc";
import { m_boat_race_op_toc } from "./line/m_boat_race_op_toc";
import { m_modular_activity_wish_lottery_info_toc } from "./line/m_modular_activity_wish_lottery_info_toc";
import { m_wars_honor_wall_toc } from "./line/m_wars_honor_wall_toc";
import { m_treasure_exist_toc } from "./line/m_treasure_exist_toc";
import { m_system_config_change_toc } from "./line/m_system_config_change_toc";
import { m_hero_skin_reset_toc } from "./line/m_hero_skin_reset_toc";
import { m_medal_detail_toc } from "./line/m_medal_detail_toc";
import { m_tax_silver_info_toc } from "./line/m_tax_silver_info_toc";
import { m_wars_role_presonal_info_toc } from "./line/m_wars_role_presonal_info_toc";
import { m_sys_daily_fetch_toc } from "./line/m_sys_daily_fetch_toc";
import { m_cycle_activity_info_toc } from "./line/m_cycle_activity_info_toc";
import { m_td_lineup_toc } from "./line/m_td_lineup_toc";
import { m_divine_copy_sweep_toc } from "./line/m_divine_copy_sweep_toc";
import { m_master_card_shop_toc } from "./line/m_master_card_shop_toc";
import { m_family_ranking_list_toc } from "./line/m_family_ranking_list_toc";
import { m_system_config_toc } from "./line/m_system_config_toc";
import { m_hero_update_fight_toc } from "./line/m_hero_update_fight_toc";
import { m_war_flag_link_toc } from "./line/m_war_flag_link_toc";
import { m_hunt_info_toc } from "./line/m_hunt_info_toc";
import { m_cycle_activity_fetch_toc } from "./line/m_cycle_activity_fetch_toc";
import { m_hero_update_list_toc } from "./line/m_hero_update_list_toc";
import { m_td_skill_toc } from "./line/m_td_skill_toc";
import { m_divine_copy_info_toc } from "./line/m_divine_copy_info_toc";
import { m_first_pay_info_toc } from "./line/m_first_pay_info_toc";
import { m_csc_fmsolo_box_toc } from "./line/m_csc_fmsolo_box_toc";
import { m_battle_trial_pass_info_toc } from "./line/m_battle_trial_pass_info_toc";
import { m_hero_upgrade_toc } from "./line/m_hero_upgrade_toc";
import { m_tax_forage_info_toc } from "./line/m_tax_forage_info_toc";
import { m_hunt_run_toc } from "./line/m_hunt_run_toc";
import { m_daily_pay_info_toc } from "./line/m_daily_pay_info_toc";
import { m_td_fight_toc } from "./line/m_td_fight_toc";
import { m_letter_open_toc } from "./line/m_letter_open_toc";
import { m_hero_resonate_level_info_toc } from "./line/m_hero_resonate_level_info_toc";
import { m_story_maze_start_toc } from "./line/m_story_maze_start_toc";
import { m_sgame_info_toc } from "./line/m_sgame_info_toc";
import { m_dawanka_info_toc } from "./line/m_dawanka_info_toc";
import { m_player_strategy_update_toc } from "./line/m_player_strategy_update_toc";
import { m_random_pvp_peak_toc } from "./line/m_random_pvp_peak_toc";
import { m_main_battle_mission_info_toc } from "./line/m_main_battle_mission_info_toc";
import { m_main_battle_mission_fetch_toc } from "./line/m_main_battle_mission_fetch_toc";
import { m_mock_pvp_show_tops_toc } from "./line/m_mock_pvp_show_tops_toc";
import { m_shop_buy_toc } from "./line/m_shop_buy_toc";
import { m_mock_pvp_fight_result_toc } from "./line/m_mock_pvp_fight_result_toc";
import { m_main_battle_box_open_toc } from "./line/m_main_battle_box_open_toc";
import { m_mock_pvp_clean_chat_toc } from "./line/m_mock_pvp_clean_chat_toc";
import { m_main_battle_auto_end_toc } from "./line/m_main_battle_auto_end_toc";
import { m_mock_pvp_lineup_preview_toc } from "./line/m_mock_pvp_lineup_preview_toc";
import { m_daily_copy_sweep_toc } from "./line/m_daily_copy_sweep_toc";
import { m_main_battle_box_rate_toc } from "./line/m_main_battle_box_rate_toc";
import { m_main_battle_missions_toc } from "./line/m_main_battle_missions_toc";
import { m_modular_activity_update_mission_toc } from "./line/m_modular_activity_update_mission_toc";
import { m_daily_pay_fetch_toc } from "./line/m_daily_pay_fetch_toc";
import { m_boat_peak_battle_toc } from "./line/m_boat_peak_battle_toc";
import { m_td_trial_info_toc } from "./line/m_td_trial_info_toc";
import { m_hero_resonate_info_toc } from "./line/m_hero_resonate_info_toc";
import { m_theme_activity_zhouka_info_toc } from "./line/m_theme_activity_zhouka_info_toc";
import { m_theme_activity_wish_lottery_info_toc } from "./line/m_theme_activity_wish_lottery_info_toc";
import { m_friend_fetch_toc } from "./line/m_friend_fetch_toc";
import { m_family_hongbao_mission_update_toc } from "./line/m_family_hongbao_mission_update_toc";
import { m_modular_activity_rare_lottery_info_toc } from "./line/m_modular_activity_rare_lottery_info_toc";
import { m_friend_list_toc } from "./line/m_friend_list_toc";
import { m_friend_best_list_toc } from "./line/m_friend_best_list_toc";
import { m_login_activity_info_toc } from "./line/m_login_activity_info_toc";
import { m_hero_resonate_operate_toc } from "./line/m_hero_resonate_operate_toc";
import { m_soul_hero_link_toc } from "./line/m_soul_hero_link_toc";
import { m_theme_activity_up_star_reward_update_toc } from "./line/m_theme_activity_up_star_reward_update_toc";
import { m_modular_activity_skin_info_toc } from "./line/m_modular_activity_skin_info_toc";
import { m_csclan_solo_state_toc } from "./line/m_csclan_solo_state_toc";
import { m_modular_activity_skin_lottery_toc } from "./line/m_modular_activity_skin_lottery_toc";
import { m_hunt_logs_toc } from "./line/m_hunt_logs_toc";
import { m_fuli_token_fetch_toc } from "./line/m_fuli_token_fetch_toc";
import { m_vip_kefu_op_toc } from "./line/m_vip_kefu_op_toc";
import { m_soul_hero_reset_toc } from "./line/m_soul_hero_reset_toc";
import { m_family_task_do_toc } from "./line/m_family_task_do_toc";
import { m_sgame_wx_club_toc } from "./line/m_sgame_wx_club_toc";
import { m_friend_request_toc } from "./line/m_friend_request_toc";
import { m_squad_lineup_get_toc } from "./line/m_squad_lineup_get_toc";
import { m_broadcast_show_toc } from "./line/m_broadcast_show_toc";
import { m_soul_hero_unlock_toc } from "./line/m_soul_hero_unlock_toc";
import { m_modular_activity_war_log_update_toc } from "./line/m_modular_activity_war_log_update_toc";
import { m_family_science_info_toc } from "./line/m_family_science_info_toc";
import { m_stage_breed_cost_toc } from "./line/m_stage_breed_cost_toc";
import { m_story_tower_battle_info_toc } from "./line/m_story_tower_battle_info_toc";
import { m_progress_gift_info_toc } from "./line/m_progress_gift_info_toc";
import { m_retrieval_info_toc } from "./line/m_retrieval_info_toc";
import { m_fuli_sign_acc_toc } from "./line/m_fuli_sign_acc_toc";
import { m_fuli_yueka_info_toc } from "./line/m_fuli_yueka_info_toc";
import { m_fight_finish_times_toc } from "./line/m_fight_finish_times_toc";
import { m_fuli_fund_info_toc } from "./line/m_fuli_fund_info_toc";
import { m_fight_start_pass_toc } from "./line/m_fight_start_pass_toc";
import { m_fight_times_toc } from "./line/m_fight_times_toc";
import { m_maze_pub_toc } from "./line/m_maze_pub_toc";
import { m_maze_monster_toc } from "./line/m_maze_monster_toc";
import { m_maze_fight_result_toc } from "./line/m_maze_fight_result_toc";
import { m_maze_use_item_toc } from "./line/m_maze_use_item_toc";
import { m_friend_agree_toc } from "./line/m_friend_agree_toc";
import { m_travel_accept_toc } from "./line/m_travel_accept_toc";
import { m_theme_activity_famous_lottery_update_toc } from "./line/m_theme_activity_famous_lottery_update_toc";
import { m_shop_info_toc } from "./line/m_shop_info_toc";
import { m_hero_convert_info_toc } from "./line/m_hero_convert_info_toc";
import { m_family_science_op_toc } from "./line/m_family_science_op_toc";
import { m_stage_breed_upgrade_toc } from "./line/m_stage_breed_upgrade_toc";
import { m_story_tower_battle_fetch_toc } from "./line/m_story_tower_battle_fetch_toc";
import { m_day_acc_pay_gift_info_toc } from "./line/m_day_acc_pay_gift_info_toc";
import { m_friend_operate_toc } from "./line/m_friend_operate_toc";
import { m_daily_copy_process_toc } from "./line/m_daily_copy_process_toc";
import { m_lord_skill_attack_inc_calc_toc } from "./line/m_lord_skill_attack_inc_calc_toc";
import { m_time_achievement_update_toc } from "./line/m_time_achievement_update_toc";
import { m_hero_come_info_toc } from "./line/m_hero_come_info_toc";
import { m_divine_equip_activate_hero_toc } from "./line/m_divine_equip_activate_hero_toc";
import { m_hint_list_toc } from "./line/m_hint_list_toc";
import { m_friend_find_toc } from "./line/m_friend_find_toc";
import { m_daily_copy_enter_toc } from "./line/m_daily_copy_enter_toc";
import { m_time_achievement_share_toc } from "./line/m_time_achievement_share_toc";
import { m_divine_equip_info_toc } from "./line/m_divine_equip_info_toc";
import { m_modular_activity_list_toc } from "./line/m_modular_activity_list_toc";
import { m_qxzl_info_last_battle_toc } from "./line/m_qxzl_info_last_battle_toc";
import { m_hero_skin_active_toc } from "./line/m_hero_skin_active_toc";
import { m_online_reward_fetch_toc } from "./line/m_online_reward_fetch_toc";
import { m_month_fund_info_toc } from "./line/m_month_fund_info_toc";
import { m_general_info_toc } from "./line/m_general_info_toc";
import { m_stage_skill_info_toc } from "./line/m_stage_skill_info_toc";
import { m_family_active_up_toc } from "./line/m_family_active_up_toc";
import { m_login_relogin_toc } from "./line/m_login_relogin_toc";
import { m_login_gen_name_toc } from "./line/m_login_gen_name_toc";
import { m_login_auth_toc } from "./line/m_login_auth_toc";
import { m_lottery_supreme_info_toc } from "./line/m_lottery_supreme_info_toc";
import { m_boat_race_broadcast_toc } from "./line/m_boat_race_broadcast_toc";
import { m_login_activity_fetch_toc } from "./line/m_login_activity_fetch_toc";
import { m_test_tower_auto_sweep_toc } from "./line/m_test_tower_auto_sweep_toc";
import { m_hzzd_info_bet_toc } from "./line/m_hzzd_info_bet_toc";
import { m_hzzd_route_select_info_toc } from "./line/m_hzzd_route_select_info_toc";
import { m_login_re_connect_toc } from "./line/m_login_re_connect_toc";
import { m_hzzd_look_kill_toc } from "./line/m_hzzd_look_kill_toc";
import { m_ranking_worship_toc } from "./line/m_ranking_worship_toc";
import { m_divine_equip_op_toc } from "./line/m_divine_equip_op_toc";
import { m_god_equip_convert_toc } from "./line/m_god_equip_convert_toc";
import { m_family_hongbao_info_toc } from "./line/m_family_hongbao_info_toc";
import { m_first_pay_fetch_toc } from "./line/m_first_pay_fetch_toc";
import { m_stage_copy_set_toc } from "./line/m_stage_copy_set_toc";
import { m_ingenious_plan_info_toc } from "./line/m_ingenious_plan_info_toc";
import { m_td_main_info_toc } from "./line/m_td_main_info_toc";
import { m_modular_activity_dice_info_toc } from "./line/m_modular_activity_dice_info_toc";
import { m_medal_retire_update_toc } from "./line/m_medal_retire_update_toc";
import { m_modular_activity_dice_toc } from "./line/m_modular_activity_dice_toc";
import { m_modular_activity_dice_unlock_toc } from "./line/m_modular_activity_dice_unlock_toc";
import { m_common_error_toc } from "./line/m_common_error_toc";
import { m_qxzl_info_history_toc } from "./line/m_qxzl_info_history_toc";
import { m_online_reward_info_toc } from "./line/m_online_reward_info_toc";
import { m_qqvip_info_toc } from "./line/m_qqvip_info_toc";
import { m_family_set_title_toc } from "./line/m_family_set_title_toc";
import { m_sdk_reward_info_toc } from "./line/m_sdk_reward_info_toc";
import { m_treasure_use_item_toc } from "./line/m_treasure_use_item_toc";
import { m_ranking_reward_toc } from "./line/m_ranking_reward_toc";
import { m_month_fund_update_toc } from "./line/m_month_fund_update_toc";
import { m_family_hongbao_fetch_toc } from "./line/m_family_hongbao_fetch_toc";
import { m_qqvip_fetch_toc } from "./line/m_qqvip_fetch_toc";
import { m_share_single_info_toc } from "./line/m_share_single_info_toc";
import { m_xswh_rank_toc } from "./line/m_xswh_rank_toc";
import { m_playing_preview_info_toc } from "./line/m_playing_preview_info_toc";
import { m_random_pvp_logs_toc } from "./line/m_random_pvp_logs_toc";
import { m_hero_come_update_toc } from "./line/m_hero_come_update_toc";
import { m_common_show_attr_toc } from "./line/m_common_show_attr_toc";
import { m_modular_activity_story_info_toc } from "./line/m_modular_activity_story_info_toc";
import { m_family_hongbao_info_update_toc } from "./line/m_family_hongbao_info_update_toc";
import { m_ingenious_plan_star_toc } from "./line/m_ingenious_plan_star_toc";
import { m_family_sign_info_toc } from "./line/m_family_sign_info_toc";
import { m_ares_palace_log_toc } from "./line/m_ares_palace_log_toc";
import { m_playing_preview_award_toc } from "./line/m_playing_preview_award_toc";
import { m_story_siegelord_info_toc } from "./line/m_story_siegelord_info_toc";
import { m_payment_shop_toc } from "./line/m_payment_shop_toc";
import { m_payment_request_toc } from "./line/m_payment_request_toc";
import { m_worship_do_toc } from "./line/m_worship_do_toc";
import { m_hero_strengthen_info_toc } from "./line/m_hero_strengthen_info_toc";
import { m_payment_buy_toc } from "./line/m_payment_buy_toc";
import { m_payment_shop_info_toc } from "./line/m_payment_shop_info_toc";
import { m_payment_auto_buy_toc } from "./line/m_payment_auto_buy_toc";
import { m_deputy_op_toc } from "./line/m_deputy_op_toc";
import { m_family_members_toc } from "./line/m_family_members_toc";
import { m_tequan_info_toc } from "./line/m_tequan_info_toc";
import { m_modular_activity_customized_gift_select_list_toc } from "./line/m_modular_activity_customized_gift_select_list_toc";
import { m_family_sign_fetch_toc } from "./line/m_family_sign_fetch_toc";
import { m_hero_handbook_list_toc } from "./line/m_hero_handbook_list_toc";
import { m_red_cliff_fight_result_toc } from "./line/m_red_cliff_fight_result_toc";
import { m_god_trial_fight_result_toc } from "./line/m_god_trial_fight_result_toc";
import { m_god_trial_fetch_toc } from "./line/m_god_trial_fetch_toc";
import { m_chat_clean_toc } from "./line/m_chat_clean_toc";
import { m_family_self_toc } from "./line/m_family_self_toc";
import { m_family_sign_do_toc } from "./line/m_family_sign_do_toc";
import { m_csclan_set_title_toc } from "./line/m_csclan_set_title_toc";
import { m_qxzl_info_toc } from "./line/m_qxzl_info_toc";
import { m_fish_fishbowl_attr_toc } from "./line/m_fish_fishbowl_attr_toc";
import { m_large_peak_info_toc } from "./line/m_large_peak_info_toc";
import { m_page_switch_toc } from "./line/m_page_switch_toc";
import { m_hero_handbook_all_fetch_toc } from "./line/m_hero_handbook_all_fetch_toc";
import { m_family_hongbao_mission_send_toc } from "./line/m_family_hongbao_mission_send_toc";
import { m_fuli_token_info_toc } from "./line/m_fuli_token_info_toc";
import { m_payment_pay_result_toc } from "./line/m_payment_pay_result_toc";
import { m_hanging_info_toc } from "./line/m_hanging_info_toc";
import { m_qxzl_info_opp_toc } from "./line/m_qxzl_info_opp_toc";
import { m_modular_activity_six_bless_info_toc } from "./line/m_modular_activity_six_bless_info_toc";
import { m_theme_activity_skin_info_toc } from "./line/m_theme_activity_skin_info_toc";
import { m_large_peak_info_opp_toc } from "./line/m_large_peak_info_opp_toc";
import { m_zero_buy_op_toc } from "./line/m_zero_buy_op_toc";
import { m_family_uplevel_toc } from "./line/m_family_uplevel_toc";
import { m_hanging_reward_toc } from "./line/m_hanging_reward_toc";
import { m_qxzl_info_member_toc } from "./line/m_qxzl_info_member_toc";
import { m_theme_activity_skin_lottery_toc } from "./line/m_theme_activity_skin_lottery_toc";
import { m_large_peak_info_member_toc } from "./line/m_large_peak_info_member_toc";
import { m_hero_zhouyin_upgrade_toc } from "./line/m_hero_zhouyin_upgrade_toc";
import { m_hero_cost_info_toc } from "./line/m_hero_cost_info_toc";
import { m_friend_refresh_toc } from "./line/m_friend_refresh_toc";
import { m_modular_activity_info_toc } from "./line/m_modular_activity_info_toc";
import { m_qxzl_info_look_member_toc } from "./line/m_qxzl_info_look_member_toc";
import { m_qxzl_info_battle_toc } from "./line/m_qxzl_info_battle_toc";
import { m_dominate_pvp_fight_result_toc } from "./line/m_dominate_pvp_fight_result_toc";
import { m_friend_give_toc } from "./line/m_friend_give_toc";
import { m_acc_pay_fetch_toc } from "./line/m_acc_pay_fetch_toc";
import { m_friend_online_toc } from "./line/m_friend_online_toc";
import { m_story_maze_use_item_toc } from "./line/m_story_maze_use_item_toc";
import { m_modular_activity_lottery_tips_toc } from "./line/m_modular_activity_lottery_tips_toc";
import { m_story_maze_report_toc } from "./line/m_story_maze_report_toc";
import { m_test_tower_into_floor_toc } from "./line/m_test_tower_into_floor_toc";
import { m_modular_activity_star_plan_info_toc } from "./line/m_modular_activity_star_plan_info_toc";
import { m_modular_activity_star_plan_update_toc } from "./line/m_modular_activity_star_plan_update_toc";
import { m_modular_activity_general_pass_info_toc } from "./line/m_modular_activity_general_pass_info_toc";
import { m_modular_activity_general_pass_fetch_toc } from "./line/m_modular_activity_general_pass_fetch_toc";
import { m_modular_activity_star_repay_info_toc } from "./line/m_modular_activity_star_repay_info_toc";
import { m_test_tower_fight_result_toc } from "./line/m_test_tower_fight_result_toc";
import { m_modular_activity_fetch_toc } from "./line/m_modular_activity_fetch_toc";
import { m_modular_activity_maze_info_toc } from "./line/m_modular_activity_maze_info_toc";
import { m_modular_activity_maze_start_toc } from "./line/m_modular_activity_maze_start_toc";
import { m_modular_activity_maze_spoils_toc } from "./line/m_modular_activity_maze_spoils_toc";
import { m_modular_activity_maze_fetch_toc } from "./line/m_modular_activity_maze_fetch_toc";
import { m_modular_activity_maze_hero_toc } from "./line/m_modular_activity_maze_hero_toc";
import { m_modular_activity_maze_pub_toc } from "./line/m_modular_activity_maze_pub_toc";
import { m_modular_activity_maze_monster_toc } from "./line/m_modular_activity_maze_monster_toc";
import { m_modular_activity_maze_fight_result_toc } from "./line/m_modular_activity_maze_fight_result_toc";
import { m_modular_activity_maze_use_item_toc } from "./line/m_modular_activity_maze_use_item_toc";
import { m_modular_activity_report_toc } from "./line/m_modular_activity_report_toc";
import { m_modular_activity_maze_auto_rolling_start_toc } from "./line/m_modular_activity_maze_auto_rolling_start_toc";
import { m_modular_activity_maze_auto_rolling_end_toc } from "./line/m_modular_activity_maze_auto_rolling_end_toc";
import { m_modular_activity_seven_goal_info_toc } from "./line/m_modular_activity_seven_goal_info_toc";
import { m_modular_activity_seven_goal_update_toc } from "./line/m_modular_activity_seven_goal_update_toc";
import { m_modular_activity_seven_goal_fetch_toc } from "./line/m_modular_activity_seven_goal_fetch_toc";
import { m_modular_activity_seven_goal_gift_toc } from "./line/m_modular_activity_seven_goal_gift_toc";
import { m_modular_activity_hunt_info_toc } from "./line/m_modular_activity_hunt_info_toc";
import { m_modular_activity_hunt_toc } from "./line/m_modular_activity_hunt_toc";
import { m_modular_activity_hunt_logs_toc } from "./line/m_modular_activity_hunt_logs_toc";
import { m_modular_activity_hunt_refresh_toc } from "./line/m_modular_activity_hunt_refresh_toc";
import { m_modular_activity_carnival_toc } from "./line/m_modular_activity_carnival_toc";
import { m_modular_activity_login_info_toc } from "./line/m_modular_activity_login_info_toc";
import { m_theme_activity_famous_lottery_info_toc } from "./line/m_theme_activity_famous_lottery_info_toc";
import { m_hzzd_info_opp_toc } from "./line/m_hzzd_info_opp_toc";
import { m_daily_gift_info_toc } from "./line/m_daily_gift_info_toc";
import { m_modular_activity_dice_boss_toc } from "./line/m_modular_activity_dice_boss_toc";
import { m_modular_activity_holiday_welfare_info_toc } from "./line/m_modular_activity_holiday_welfare_info_toc";
import { m_modular_activity_holiday_welfare_update_toc } from "./line/m_modular_activity_holiday_welfare_update_toc";
import { m_modular_activity_sign_info_toc } from "./line/m_modular_activity_sign_info_toc";
import { m_modular_activity_sign_update_toc } from "./line/m_modular_activity_sign_update_toc";
import { m_modular_activity_hero_challenge_info_toc } from "./line/m_modular_activity_hero_challenge_info_toc";
import { m_modular_activity_hero_challenge_report_toc } from "./line/m_modular_activity_hero_challenge_report_toc";
import { m_modular_activity_preview_toc } from "./line/m_modular_activity_preview_toc";
import { m_modular_activity_preview_info_toc } from "./line/m_modular_activity_preview_info_toc";
import { m_boat_race_role_items_toc } from "./line/m_boat_race_role_items_toc";
import { m_modular_activity_story_group_toc } from "./line/m_modular_activity_story_group_toc";
import { m_modular_activity_heaven_give_info_toc } from "./line/m_modular_activity_heaven_give_info_toc";
import { m_modular_activity_customized_gift_select_toc } from "./line/m_modular_activity_customized_gift_select_toc";
import { m_hzzd_battle_info_toc } from "./line/m_hzzd_battle_info_toc";
import { m_modular_activity_brick_info_toc } from "./line/m_modular_activity_brick_info_toc";
import { m_modular_activity_brick_toc } from "./line/m_modular_activity_brick_toc";
import { m_modular_activity_bless_info_toc } from "./line/m_modular_activity_bless_info_toc";
import { m_modular_activity_target_info_toc } from "./line/m_modular_activity_target_info_toc";
import { m_modular_activity_wall_info_toc } from "./line/m_modular_activity_wall_info_toc";
import { m_modular_activity_exchange_info_toc } from "./line/m_modular_activity_exchange_info_toc";
import { m_modular_activity_strategy_info_toc } from "./line/m_modular_activity_strategy_info_toc";
import { m_modular_activity_lottery_target_info_toc } from "./line/m_modular_activity_lottery_target_info_toc";
import { m_login_chose_toc } from "./line/m_login_chose_toc";
import { m_modular_activity_day_shop_toc } from "./line/m_modular_activity_day_shop_toc";
import { m_simp_mission_list_toc } from "./line/m_simp_mission_list_toc";
import { m_modular_activity_festival_wish_info_toc } from "./line/m_modular_activity_festival_wish_info_toc";
import { m_modular_activity_festival_wish_toc } from "./line/m_modular_activity_festival_wish_toc";
import { m_sys_concern_info_toc } from "./line/m_sys_concern_info_toc";
import { m_week_target_info_toc } from "./line/m_week_target_info_toc";
import { m_family_audit_limit_toc } from "./line/m_family_audit_limit_toc";
import { m_treasure_result_toc } from "./line/m_treasure_result_toc";
import { m_qxzl_info_bet_toc } from "./line/m_qxzl_info_bet_toc";
import { m_fish_replace_toc } from "./line/m_fish_replace_toc";
import { m_modular_activity_update_fetch_toc } from "./line/m_modular_activity_update_fetch_toc";
import { m_ranking_simple_toc } from "./line/m_ranking_simple_toc";
import { m_fuli_sign_fetch_toc } from "./line/m_fuli_sign_fetch_toc";
import { m_pass_behead_info_toc } from "./line/m_pass_behead_info_toc";
import { m_wars_init_personal_toc } from "./line/m_wars_init_personal_toc";
import { m_story_maze_info_toc } from "./line/m_story_maze_info_toc";
import { m_tax_silver_fetch_toc } from "./line/m_tax_silver_fetch_toc";
import { m_vip_kefu_qq_toc } from "./line/m_vip_kefu_qq_toc";
import { m_general_pass_gift_toc } from "./line/m_general_pass_gift_toc";
import { m_letter_send_toc } from "./line/m_letter_send_toc";
import { m_tax_forage_fetch_toc } from "./line/m_tax_forage_fetch_toc";
import { m_large_peak_info_battle_toc } from "./line/m_large_peak_info_battle_toc";
import { m_large_peak_info_bet_toc } from "./line/m_large_peak_info_bet_toc";
import { m_large_peak_info_history_toc } from "./line/m_large_peak_info_history_toc";
import { m_large_peak_personal_info_toc } from "./line/m_large_peak_personal_info_toc";
import { m_large_peak_info_get_msg_toc } from "./line/m_large_peak_info_get_msg_toc";
import { m_modular_activity_shop_toc } from "./line/m_modular_activity_shop_toc";
import { m_pay_gift_fetch_toc } from "./line/m_pay_gift_fetch_toc";
import { m_acc_pay_info_toc } from "./line/m_acc_pay_info_toc";
import { m_guandu_info_toc } from "./line/m_guandu_info_toc";
import { m_boat_peak_info_toc } from "./line/m_boat_peak_info_toc";
import { m_test_tower_skip_toc } from "./line/m_test_tower_skip_toc";
import { m_qxzl_info_last_rank_toc } from "./line/m_qxzl_info_last_rank_toc";
import { m_hzzd_team_info_toc } from "./line/m_hzzd_team_info_toc";
import { m_modular_activity_lottery_start_toc } from "./line/m_modular_activity_lottery_start_toc";
import { m_lottery_start_toc } from "./line/m_lottery_start_toc";
import { m_guandu_op_toc } from "./line/m_guandu_op_toc";
import { m_worship_online_toc } from "./line/m_worship_online_toc";
import { m_boat_peak_shop_toc } from "./line/m_boat_peak_shop_toc";
import { m_boat_peak_opp_toc } from "./line/m_boat_peak_opp_toc";
import { m_dominate_pvp_op_toc } from "./line/m_dominate_pvp_op_toc";
import { m_item_use_toc } from "./line/m_item_use_toc";
import { m_login_give_name_toc } from "./line/m_login_give_name_toc";
import { m_modular_activity_famous_lottery_info_toc } from "./line/m_modular_activity_famous_lottery_info_toc";
import { m_lottery_info_toc } from "./line/m_lottery_info_toc";
import { m_recommend_lineup_info_toc } from "./line/m_recommend_lineup_info_toc";
import { m_title_list_toc } from "./line/m_title_list_toc";
import { m_worship_info_toc } from "./line/m_worship_info_toc";
import { m_lcqs_chapter_info_toc } from "./line/m_lcqs_chapter_info_toc";
import { m_dominate_pvp_info_toc } from "./line/m_dominate_pvp_info_toc";
import { m_building_info_toc } from "./line/m_building_info_toc";
import { m_multi_lineup_set_toc } from "./line/m_multi_lineup_set_toc";
import { m_lottery_score_fetch_toc } from "./line/m_lottery_score_fetch_toc";
import { m_title_load_toc } from "./line/m_title_load_toc";
import { m_story_maze_spoils_toc } from "./line/m_story_maze_spoils_toc";
import { m_seven_goal_info_toc } from "./line/m_seven_goal_info_toc";
import { m_boat_peak_look_member_toc } from "./line/m_boat_peak_look_member_toc";
import { m_dominate_pvp_match_toc } from "./line/m_dominate_pvp_match_toc";
import { m_multi_lineup_list_toc } from "./line/m_multi_lineup_list_toc";
import { m_building_op_toc } from "./line/m_building_op_toc";
import { m_dominate_pvp_update_toc } from "./line/m_dominate_pvp_update_toc";
import { m_sdk_sns_reward_info_toc } from "./line/m_sdk_sns_reward_info_toc";
import { m_star_plan_info_toc } from "./line/m_star_plan_info_toc";
import { m_guide_finish_toc } from "./line/m_guide_finish_toc";
import { m_cycle_activity_update_toc } from "./line/m_cycle_activity_update_toc";
import { m_war_flag_exchange_toc } from "./line/m_war_flag_exchange_toc";
import { m_guide_get_hero_toc } from "./line/m_guide_get_hero_toc";
import { m_main_battle_fetch_pass_toc } from "./line/m_main_battle_fetch_pass_toc";
import { m_gmcmd_panel_toc } from "./line/m_gmcmd_panel_toc";
import { m_lottery_nation_start_toc } from "./line/m_lottery_nation_start_toc";
import { m_title_update_toc } from "./line/m_title_update_toc";
import { m_wars_walk_list_toc } from "./line/m_wars_walk_list_toc";
import { m_seven_goal_update_toc } from "./line/m_seven_goal_update_toc";
import { m_ingenious_plan_update_toc } from "./line/m_ingenious_plan_update_toc";
import { m_lcqs_chapter_list_toc } from "./line/m_lcqs_chapter_list_toc";
import { m_dominate_pvp_logs_toc } from "./line/m_dominate_pvp_logs_toc";
import { m_xswh_info_toc } from "./line/m_xswh_info_toc";
import { m_hunt_refresh_toc } from "./line/m_hunt_refresh_toc";
import { m_fish_update_toc } from "./line/m_fish_update_toc";
import { m_login_add_toc } from "./line/m_login_add_toc";
import { m_login_fetch_vip_toc } from "./line/m_login_fetch_vip_toc";
import { m_hero_recycle_preview_toc } from "./line/m_hero_recycle_preview_toc";
import { m_fish_shop_toc } from "./line/m_fish_shop_toc";
import { m_seven_goal_fetch_toc } from "./line/m_seven_goal_fetch_toc";
import { m_hero_set_lineup_toc } from "./line/m_hero_set_lineup_toc";
import { m_rank_activity_info_toc } from "./line/m_rank_activity_info_toc";
import { m_achievement_info_toc } from "./line/m_achievement_info_toc";
import { m_xswh_fight_result_toc } from "./line/m_xswh_fight_result_toc";
import { m_fish_up_official_toc } from "./line/m_fish_up_official_toc";
import { m_sys_use_times_info_toc } from "./line/m_sys_use_times_info_toc";
import { m_hero_lineup_info_toc } from "./line/m_hero_lineup_info_toc";
import { m_login_filter_fun_toc } from "./line/m_login_filter_fun_toc";
import { m_role_score_update_toc } from "./line/m_role_score_update_toc";
import { m_team_xswh_rank_toc } from "./line/m_team_xswh_rank_toc";
import { m_seven_goal_gift_toc } from "./line/m_seven_goal_gift_toc";
import { m_hero_bag_expansion_toc } from "./line/m_hero_bag_expansion_toc";
import { m_boat_peak_bet_toc } from "./line/m_boat_peak_bet_toc";
import { m_item_compose_toc } from "./line/m_item_compose_toc";
import { m_hero_recycle_all_toc } from "./line/m_hero_recycle_all_toc";
import { m_sys_use_times_update_toc } from "./line/m_sys_use_times_update_toc";
import { m_master_card_info_toc } from "./line/m_master_card_info_toc";
import { m_stage_copy_sweep_toc } from "./line/m_stage_copy_sweep_toc";
import { m_hero_recycle_gains_item_toc } from "./line/m_hero_recycle_gains_item_toc";
import { m_family_list_toc } from "./line/m_family_list_toc";
import { m_lottery_supreme_start_toc } from "./line/m_lottery_supreme_start_toc";
import { m_story_maze_fight_result_toc } from "./line/m_story_maze_fight_result_toc";
import { m_boat_peak_his_toc } from "./line/m_boat_peak_his_toc";
import { m_share_fetch_toc } from "./line/m_share_fetch_toc";
import { m_icon_list_toc } from "./line/m_icon_list_toc";
import { m_xswh_best_rank_toc } from "./line/m_xswh_best_rank_toc";
import { m_cross_ladder_opp_info_toc } from "./line/m_cross_ladder_opp_info_toc";
import { m_test_tower_process_toc } from "./line/m_test_tower_process_toc";
import { m_cycle_activity_gift_toc } from "./line/m_cycle_activity_gift_toc";
import { m_mission_shop_info_toc } from "./line/m_mission_shop_info_toc";
import { m_buy_times_update_toc } from "./line/m_buy_times_update_toc";
import { m_god_weapon_info_toc } from "./line/m_god_weapon_info_toc";
import { m_god_weapon_upgrade_toc } from "./line/m_god_weapon_upgrade_toc";
import { m_bingfu_operate_toc } from "./line/m_bingfu_operate_toc";
import { m_csc_fmsolo_state_toc } from "./line/m_csc_fmsolo_state_toc";
import { m_bingfu_compose_toc } from "./line/m_bingfu_compose_toc";
import { m_csc_fmsolo_shop_lv_toc } from "./line/m_csc_fmsolo_shop_lv_toc";
import { m_bingfu_refine_toc } from "./line/m_bingfu_refine_toc";
import { m_icon_switch_toc } from "./line/m_icon_switch_toc";
import { m_pay_gift_info_toc } from "./line/m_pay_gift_info_toc";
import { m_test_tower_fetch_toc } from "./line/m_test_tower_fetch_toc";
import { m_buy_times_info_toc } from "./line/m_buy_times_info_toc";
import { m_master_talent_science_info_toc } from "./line/m_master_talent_science_info_toc";
import { m_fight_share_operate_toc } from "./line/m_fight_share_operate_toc";
import { m_peak_info_look_member_toc } from "./line/m_peak_info_look_member_toc";
import { m_csc_fmsolo_fetch_toc } from "./line/m_csc_fmsolo_fetch_toc";
import { m_treasure_role_info_toc } from "./line/m_treasure_role_info_toc";
import { m_share_info_toc } from "./line/m_share_info_toc";
import { m_role_rename_toc } from "./line/m_role_rename_toc";
import { m_role_score_list_toc } from "./line/m_role_score_list_toc";
import { m_icon_switch_list_toc } from "./line/m_icon_switch_list_toc";
import { m_hanging_quick_toc } from "./line/m_hanging_quick_toc";
import { m_fight_start_toc } from "./line/m_fight_start_toc";
import { m_master_card_update_toc } from "./line/m_master_card_update_toc";
import { m_family_active_info_toc } from "./line/m_family_active_info_toc";
import { m_fight_share_stat_toc } from "./line/m_fight_share_stat_toc";
import { m_hero_cheer_info_toc } from "./line/m_hero_cheer_info_toc";
import { m_divine_equip_compose_toc } from "./line/m_divine_equip_compose_toc";
import { m_role_rename_info_toc } from "./line/m_role_rename_info_toc";
import { m_arena_info_toc } from "./line/m_arena_info_toc";
import { m_fight_finish_toc } from "./line/m_fight_finish_toc";
import { m_family_task_info_toc } from "./line/m_family_task_info_toc";
import { m_option_lottery_start_toc } from "./line/m_option_lottery_start_toc";
import { m_hero_cheer_op_toc } from "./line/m_hero_cheer_op_toc";
import { m_hongbao_info_toc } from "./line/m_hongbao_info_toc";
import { m_team_xswh_best_rank_toc } from "./line/m_team_xswh_best_rank_toc";
import { m_role_acclogin_toc } from "./line/m_role_acclogin_toc";
import { m_test_tower_sweep_toc } from "./line/m_test_tower_sweep_toc";
import { m_family_task_update_toc } from "./line/m_family_task_update_toc";
import { m_fuli_sign_info_toc } from "./line/m_fuli_sign_info_toc";
import { m_option_lottery_info_toc } from "./line/m_option_lottery_info_toc";
import { m_hzzd_info_toc } from "./line/m_hzzd_info_toc";
import { m_family_create_toc } from "./line/m_family_create_toc";
import { m_xswh_fetch_toc } from "./line/m_xswh_fetch_toc";
import { m_eight_login_fetch_toc } from "./line/m_eight_login_fetch_toc";
import { m_role_look_profile_toc } from "./line/m_role_look_profile_toc";


   /**
     *开发者：成畅
     *修改时间:
     *自动生成的代码，请勿修改
   **/
export class SocketCommand
{
       static readonly BINGFU_INFO:number = 5187456;
       static readonly STAGE_COPY_DROP_GROUP:number = 9915267;
       static readonly SHOP_UPDATE:number = 2363907;
       static readonly MINI_GAME_GET_STR:number = 9061632;
       static readonly FULI_YUEKA_INFO:number = 3742851;
       static readonly MAIN_BATTLE_BOX_RATE:number = 2626574;
       static readonly MODULAR_ACTIVITY_EXCHANGE_INFO:number = 8142409;
       static readonly ARENA_INFO:number = 3611521;
       static readonly FAMILY_ACTIVE_UP:number = 6960385;
       static readonly MODULAR_ACTIVITY_BLESS_INFO:number = 8142406;
       static readonly TREASURE_INFO:number = 10506240;
       static readonly FISH_NOTICE:number = 10834569;
       static readonly QQVIP_FETCH:number = 4990465;
       static readonly WORLD_BOSS_FIGHT_RESULT:number = 7354370;
       static readonly MODULAR_ACTIVITY_MAZE_AUTO_ROLLING_START:number = 8142372;
       static readonly PLAYER_STRATEGY_UPDATE:number = 8930306;
       static readonly FIGHT_START:number = 2692224;
       static readonly XSWH_FIGHT_RESULT:number = 8601985;
       static readonly MOCK_PVP_CLEAN_CHAT:number = 11031562;
       static readonly LOGIN_FILTER_FUN:number = 656649;
       static readonly TAX_SILVER_INFO:number = 2954880;
       static readonly HONGBAO_INFO_UPDATE:number = 11753859;
       static readonly PAYMENT_PAY_RESULT:number = 1772931;
       static readonly HZZD_INFO:number = 8011008;
       static readonly RETRIEVAL_INFO:number = 11097217;
       static readonly ROLE_SETTING2:number = 1116305;
       static readonly GUANDU_INFO:number = 6500736;
       static readonly BOAT_PEAK_JOIN_MEMBERS:number = 10768907;
       static readonly MOCK_PVP_LINEUP_PREVIEW:number = 11031564;
       static readonly QXZL_INFO_MEMBER:number = 7091714;
       static readonly ONLINE_REWARD_FETCH:number = 3414529;
       static readonly LOGIN_FETCH_VIP:number = 656648;
       static readonly CSCLAN_SOLO_SHOP_LV:number = 11556870;
       static readonly BOAT_PEAK_LOOK_MEMBER:number = 10768899;
       static readonly ACTIVITY_SHOP_UPDATE:number = 1444613;
       static readonly WARS_DAILY_REPORT:number = 11622558;
       static readonly WARS_SELECT_ARMY:number = 11622549;
       static readonly MODULAR_ACTIVITY_HOLIDAY_WELFARE_UPDATE:number = 8142389;
       static readonly PROGRESS_GIFT_INFO:number = 6894720;
       static readonly MAIN_BATTLE_AUTO_END:number = 2626571;
       static readonly FAMILY_BOSS_INFO:number = 6303744;
       static readonly RANKING_REWARD:number = 1510273;
       static readonly PAYMENT_BUY:number = 1772932;
       static readonly GUANDU_NEXT:number = 6500740;
       static readonly MODULAR_ACTIVITY_UPDATE_MISSION:number = 8142339;
       static readonly ACTIVITY_LIMIT_SIGN_INFO:number = 1444618;
       static readonly CSCLAN_OPERATE:number = 10637575;
       static readonly CSCLAN_BASE:number = 10637580;
       static readonly TIME_ACHIEVEMENT_INFO:number = 6632064;
       static readonly PEAK_INFO_BATTLE:number = 8404996;
       static readonly ROLE_LOOK_HERO_ATTR_SOURCE:number = 1116304;
       static readonly HERO_COMMENT_INFO:number = 4399488;
       static readonly OPTION_LOTTERY_START:number = 7485696;
       static readonly FRIEND_GIVE:number = 787974;
       static readonly ICON_SWITCH_LIST:number = 1641602;
       static readonly STAGE_COPY_INFO:number = 9915264;
       static readonly DEPUTY_INFO:number = 2495258;
       static readonly RENT_HERO_OP:number = 5318785;
       static readonly MODULAR_ACTIVITY_INFO:number = 8142338;
       static readonly TREASURE_CAL_TIME:number = 10506246;
       static readonly CSCLAN_SET_OWNER:number = 10637573;
       static readonly PEAK_PERSONAL_INFO:number = 8405002;
       static readonly TEQUAN_INFO:number = 2298240;
       static readonly TRAVEL_SPEED:number = 3151875;
       static readonly PASS_CHECK_GIFT:number = 7420034;
       static readonly FAMILY_UPDATE_MEMBER:number = 1247632;
       static readonly HERO_EVOLVE_INFO:number = 2429591;
       static readonly HERO_EVOLVE_OP:number = 2429590;
       static readonly PEAK_INFO:number = 8404992;
       static readonly BROADCAST_NORMAL:number = 1313280;
       static readonly MICROTERMINAL_INFO:number = 9389952;
       static readonly PAYMENT_SHOP_INFO:number = 1772933;
       static readonly HERO_RECYCLE:number = 2429574;
       static readonly GOD_TRIAL_BUFF:number = 5778434;
       static readonly BATTLE_TRIAL_HANGING_INFO:number = 9521285;
       static readonly FAMILY_HONGBAO_FETCH_UPDATE:number = 4465156;
       static readonly SOUL_HERO_INFO:number = 8798976;
       static readonly HUNT_GIFT:number = 3480194;
       static readonly MAIN_BATTLE_MISSION_INFO:number = 2626564;
       static readonly GOD_TRIAL_INFO:number = 5778432;
       static readonly SYSTEM_TIME:number = 1378951;
       static readonly HONGBAO_FETCH:number = 11753858;
       static readonly DIVINE_COPY_SWEEP:number = 11491200;
       static readonly CSCLAN_LIST:number = 10637568;
       static readonly MODULAR_ACTIVITY_MAZE_FIGHT_RESULT:number = 8142367;
       static readonly ROLE_LOOK_SDK_PROFILE:number = 1116306;
       static readonly MAIN_BATTLE_AUTO:number = 2626570;
       static readonly SEVEN_GOAL_UPDATE:number = 3808513;
       static readonly TREASURE_WORKER_ACTIVE:number = 10506252;
       static readonly PASS_CHECK_INFO:number = 7420032;
       static readonly INGENIOUS_PLAN_UPDATE:number = 8733314;
       static readonly BOAT_RACE_BROADCAST:number = 10112262;
       static readonly MODULAR_ACTIVITY_FESTIVAL_WISH:number = 8142417;
       static readonly BINGFU_OPERATE:number = 5187458;
       static readonly FAMILY_APPLY_LIST:number = 1247620;
       static readonly STAGE_BREED_UPGRADE:number = 9849602;
       static readonly ACTIVITY_INFO:number = 1444610;
       static readonly CSC_FMSOLO_STATE:number = 7288707;
       static readonly CHAT_PRIVATE_HISTORY:number = 590745;
       static readonly RANK_MISSION_UPDATE:number = 6763393;
       static readonly CHAT_AUTH:number = 590725;
       static readonly LCQS_CHAPTER_LIST:number = 7551362;
       static readonly BATTLE_TRIAL_OFFLINE_INFO:number = 9521284;
       static readonly MODULAR_ACTIVITY_SEVEN_GOAL_UPDATE:number = 8142375;
       static readonly RANKING_LIST:number = 1510272;
       static readonly HERO_COST_INFO:number = 9192960;
       static readonly FRIEND_REFRESH:number = 787969;
       static readonly CSCLAN_SELF:number = 10637570;
       static readonly GOD_WEAPON_INFO:number = 6238080;
       static readonly WARS_UPDATE_CITY:number = 11622532;
       static readonly MOCK_PVP_INFO:number = 11031553;
       static readonly FAMILY_HONGBAO_MISSION_UPDATE:number = 4465159;
       static readonly MAIN_BATTLE_BOX_OPEN:number = 2626569;
       static readonly DAILY_COPY_SWEEP:number = 2889218;
       static readonly TAX_FORAGE_FETCH:number = 2954883;
       static readonly MOCK_PVP_SCHEMES:number = 11031556;
       static readonly EQUIP_AUTO_COMPOSE_INFO:number = 2495242;
       static readonly HZZD_LOOK_KILL:number = 8011020;
       static readonly MODULAR_ACTIVITY_MAZE_FETCH:number = 8142361;
       static readonly LORD_LINEUP_LIST:number = 10571908;
       static readonly FAMILY_RENAME:number = 1247633;
       static readonly BINGFU_COMPOSE:number = 5187460;
       static readonly STORY_MAZE_USE_ITEM:number = 10243593;
       static readonly PAYMENT_SHOP:number = 1772928;
       static readonly GOD_EQUIP_SELECT:number = 2560898;
       static readonly MODULAR_ACTIVITY_TARGET_INFO:number = 8142407;
       static readonly BOAT_RACE_UPDATE:number = 10112258;
       static readonly BUILDING_OP:number = 11819521;
       static readonly FAMILY_BOSS_FIGHT_RESULT:number = 6303749;
       static readonly EQUIP_COMPOSE:number = 2495241;
       static readonly MODULAR_ACTIVITY_HUNT_INFO:number = 8142378;
       static readonly RANKING_WORSHIP:number = 1510274;
       static readonly LOGIN_RE_CONNECT:number = 656651;
       static readonly BOAT_PEAK_INFO:number = 10768896;
       static readonly PROFILE_INFO:number = 3020544;
       static readonly THEME_ACTIVITY_WISH_LOTTERY_INFO:number = 7617030;
       static readonly MAIN_BATTLE_FETCH_PASS:number = 2626563;
       static readonly STAGE_BREED_INFO:number = 9849600;
       static readonly MODULAR_ACTIVITY_MAZE_MONSTER:number = 8142366;
       static readonly FIGHT_FINISH_TIMES:number = 2692228;
       static readonly HZZD_ROUTE_INFO:number = 8011009;
       static readonly FULI_SIGN_ACC:number = 3742850;
       static readonly QXZL_INFO_OPP:number = 7091713;
       static readonly MOCK_PVP_READY:number = 11031559;
       static readonly DOMINATE_PVP_MATCH:number = 11294210;
       static readonly TD_MISSION_INFO:number = 10965895;
       static readonly HERO_RECYCLE_TIMES:number = 2429588;
       static readonly GUANDU_UPDATE_EVENT:number = 6500738;
       static readonly FAMILY_HOT:number = 1247639;
       static readonly HERO_PASS_UPDATE:number = 7748356;
       static readonly CSCLAN_SCENE:number = 10637581;
       static readonly TEST_TOWER_INTO_FLOOR:number = 5909762;
       static readonly DAILY_FULI_INFO:number = 5647104;
       static readonly FAMILY_ATTR_TIP:number = 1247635;
       static readonly HERO_SKIN_ACTIVE:number = 5121794;
       static readonly GMCMD_PANEL:number = 1904257;
       static readonly SDK_REWARD_INFO:number = 9258624;
       static readonly WARS_ACT_INFO:number = 11622540;
       static readonly PAYMENT_REQUEST:number = 1772929;
       static readonly THEME_ACTIVITY_UP_STAR_REWARD_INFO:number = 7617032;
       static readonly ICON_SWITCH:number = 1641601;
       static readonly MODULAR_ACTIVITY_WALL_INFO:number = 8142408;
       static readonly LOGIN_RELOGIN:number = 656642;
       static readonly FISH_LOGS:number = 10834573;
       static readonly DAILY_GIFT_TEHUI_INFO:number = 4924803;
       static readonly TRAVEL_FETCH:number = 3151876;
       static readonly MODULAR_ACTIVITY_MAZE_START:number = 8142359;
       static readonly WARS_INIT_CAMP:number = 11622555;
       static readonly MODULAR_ACTIVITY_DICE_BOSS:number = 8142387;
       static readonly LOGIN_GEN_NAME:number = 656643;
       static readonly ACTIVITY_GIFT:number = 1444617;
       static readonly LOTTERY_SUPREME_INFO:number = 2757893;
       static readonly TD_SIMP_INFO:number = 10965894;
       static readonly HERO_STRENGTHEN_INFO:number = 8470656;
       static readonly WARS_KILL_INFO:number = 11622548;
       static readonly RANDOM_PVP_PEAK:number = 7879683;
       static readonly HINT_SHOW_TIP:number = 2035585;
       static readonly WARS_SIGN:number = 11622535;
       static readonly FRIEND_ONLINE:number = 787976;
       static readonly VIP_BUY_GIFT:number = 4268161;
       static readonly GENERAL_PASS_GIFT:number = 8208001;
       static readonly SYSTEM_SETTING:number = 1378949;
       static readonly TD_FIGHT:number = 10965891;
       static readonly SHOP_BUY:number = 2363905;
       static readonly MICROTERMINAL_SIGN_FETCH:number = 9389953;
       static readonly GUIDE_STEP:number = 4071171;
       static readonly CHANGE_DAY:number = 1378950;
       static readonly HERO_RESONATE_DHYANA_OP:number = 8273667;
       static readonly STAGE_COPY_FETCH:number = 9915266;
       static readonly WARS_LOOK_OP_CITY_LOG:number = 11622546;
       static readonly PASS_CHECK_UPDATE:number = 7420035;
       static readonly ROLE_BASE_RELOAD:number = 1116290;
       static readonly ROLE_BEST_HERO:number = 1116300;
       static readonly GOD_EQUIP_CONVERT:number = 2560899;
       static readonly LARGE_PEAK_INFO_GET_MSG:number = 11359883;
       static readonly HERO_RECYCLE_DOWN:number = 2429587;
       static readonly CSCLAN_AUDIT_LIMIT:number = 10637571;
       static readonly SMALL_GAME_INFO:number = 8995968;
       static readonly TREASURE_LOG:number = 10506243;
       static readonly ROLE_LOOK_HERO:number = 1116303;
       static readonly DOMINATE_PVP_FIGHT_RESULT:number = 11294213;
       static readonly MODULAR_ACTIVITY_LOTTERY_TARGET_INFO:number = 8142411;
       static readonly BOAT_PEAK_BET:number = 10768902;
       static readonly WEEK_TARGET_INFO:number = 9718272;
       static readonly WARS_TEAM_OP:number = 11622536;
       static readonly STAGE_SKILL_INFO:number = 11162880;
       static readonly ROLE_LOOK_PROFILE:number = 1116294;
       static readonly HERO_LINEUP_INFO:number = 2429577;
       static readonly EQUIP_LOCK:number = 2495254;
       static readonly MISSION_SHOP_BUY:number = 4727810;
       static readonly FAMILY_CANCEL_JOIN:number = 1247623;
       static readonly CSCLAN_LOGS:number = 10637578;
       static readonly ACC_PAY_FETCH:number = 2232577;
       static readonly EQUIP_BINGFU_DECOMPOSE:number = 2495250;
       static readonly TEAM_INFO_MY_TEAM:number = 9455617;
       static readonly MODULAR_ACTIVITY_STRATEGY_INFO:number = 8142410;
       static readonly RANDOM_PVP_INFO:number = 7879681;
       static readonly DAILY_PAY_INFO:number = 4005504;
       static readonly MODULAR_ACTIVITY_STAR_PLAN_INFO:number = 8142350;
       static readonly LORD_INFO:number = 10571905;
       static readonly STORY_MAZE_REPORT:number = 10243596;
       static readonly FISH_FISHBOWL_ATTR:number = 10834561;
       static readonly TEST_TOWER_AUTO_SWEEP:number = 5909768;
       static readonly MAZE_MONSTER:number = 7945353;
       static readonly TRAVEL_INFO:number = 3151872;
       static readonly HERO_UPDATE_FIGHT:number = 2429570;
       static readonly CSCLAN_SOLO_BOX:number = 11556869;
       static readonly MODULAR_ACTIVITY_HUNT:number = 8142379;
       static readonly WELFARE_SDK_SHARE_INFO:number = 1575938;
       static readonly FAMILY_TRANSFER_COUNT:number = 1247637;
       static readonly MODULAR_ACTIVITY_SIGN_UPDATE:number = 8142391;
       static readonly ACTIVITY_UPDATE_FETCH:number = 1444609;
       static readonly CSCLAN_MEMBERS:number = 10637576;
       static readonly LARGE_PEAK_INFO_BET:number = 11359878;
       static readonly RENT_HERO_LIST:number = 5318784;
       static readonly RANDOM_PVP_OP:number = 7879680;
       static readonly LOGIN_GIVE_NAME:number = 656645;
       static readonly MASTER_CARD_DECORATION_INFO:number = 9652619;
       static readonly ROLE_SETTING_LIST:number = 1116295;
       static readonly CSCLAN_SET_TITLE:number = 10637572;
       static readonly MODULAR_ACTIVITY_MAZE_HERO:number = 8142364;
       static readonly BOAT_RACE_INFO:number = 10112256;
       static readonly MAZE_FETCH:number = 7945348;
       static readonly WAR_FLAG_EXCHANGE:number = 6172420;
       static readonly FAMILY_HONGBAO_MISSION_INFO:number = 4465157;
       static readonly LCQS_CHAPTER_INFO:number = 7551360;
       static readonly BATTLE_TRIAL_DAILY_END_INFO:number = 9521283;
       static readonly WORLD_BOSS_BUY_TIMES:number = 7354369;
       static readonly WARS_TEAM_HERO_INFO:number = 11622538;
       static readonly CSCLAN_UPDATE_NOTICE:number = 10637574;
       static readonly HZZD_BATTLE_INFO:number = 8011019;
       static readonly FAMILY_BOSS_LOOK:number = 6303747;
       static readonly SEVEN_GOAL_INFO:number = 3808512;
       static readonly RENT_HERO_LOOK:number = 5318786;
       static readonly FAMILY_TASK_REWARD:number = 3217539;
       static readonly QXZL_INFO_BATTLE:number = 7091716;
       static readonly HERO_LOCK:number = 2429572;
       static readonly STAR_PLAN_INFO:number = 7682688;
       static readonly MOCK_PVP_LOOK_HERO:number = 11031554;
       static readonly MOCK_PVP_CONTEST_CHAT:number = 11031561;
       static readonly TREASURE_FETCH:number = 10506253;
       static readonly HZZD_ROUTE_SELECT_INFO:number = 8011018;
       static readonly GOODS_SHOW_GOODS:number = 1050626;
       static readonly ICON_LIST:number = 1641600;
       static readonly ARENA_UPDATE:number = 3611522;
       static readonly WARS_CITY_INFO:number = 11622533;
       static readonly TREASURE_OTHER:number = 10506242;
       static readonly MAZE_HERO:number = 7945351;
       static readonly HERO_SET_LINEUP:number = 2429575;
       static readonly CSC_FMSOLO_SHOP_LV:number = 7288709;
       static readonly FULI_TOKEN_BUY:number = 4530817;
       static readonly FULI_FUND_FETCH:number = 3742854;
       static readonly HERO_MY_RANK:number = 2429584;
       static readonly QXZL_INFO_LOOK_MEMBER:number = 7091715;
       static readonly FAMILY_TRANSFER_JOIN:number = 1247638;
       static readonly PROGRESS_GIFT_BUY:number = 6894721;
       static readonly VIP_KEFU_QQ:number = 5056128;
       static readonly BOAT_PEAK_BATTLE:number = 10768900;
       static readonly GOD_WEAPON_UPGRADE:number = 6238081;
       static readonly WARS_INFO_NTY_RANK_DATA:number = 11622552;
       static readonly TIME_ACHIEVEMENT_SHARE:number = 6632066;
       static readonly TEST_TOWER_FIGHT_RESULT:number = 5909763;
       static readonly SDK_SNS_REWARD_INFO:number = 9324288;
       static readonly FAMILY_UPDATE_NOTICE:number = 1247628;
       static readonly HZZD_INFO_OPP:number = 8011016;
       static readonly CROSS_LADDER_LOGS:number = 8536324;
       static readonly FAMILY_RANKING_LIST:number = 4333824;
       static readonly MAZE_USE_ITEM:number = 7945355;
       static readonly MODULAR_ACTIVITY_BRICK_INFO:number = 8142404;
       static readonly QXZL_INFO_BET:number = 7091718;
       static readonly BOAT_PEAK_MEMBER:number = 10768898;
       static readonly SGAME_INFO:number = 9980928;
       static readonly FRIEND_BEST_LIST:number = 787977;
       static readonly TEAM_XSWH_INFO:number = 9586944;
       static readonly HZZD_INFO_BET:number = 8011017;
       static readonly TEAM_INFO_MY_TEAM_APPLY:number = 9455619;
       static readonly MODULAR_ACTIVITY_MAZE_PUB:number = 8142365;
       static readonly FAMILY_HONGBAO_INFO:number = 4465152;
       static readonly TRAVEL_REFRESH:number = 3151873;
       static readonly FISH_SHOP:number = 10834566;
       static readonly ROLE_RENAME_INFO:number = 1116292;
       static readonly EQUIP_REINFORCE:number = 2495237;
       static readonly CSC_FMSOLO_GROUP:number = 7288705;
       static readonly MODULAR_ACTIVITY_HUNT_REFRESH:number = 8142381;
       static readonly STAGE_BREED_COST:number = 9849601;
       static readonly LORD_TREASURE_OP:number = 10571915;
       static readonly BATTLE_TRIAL_BATTLE_RESET:number = 9521287;
       static readonly PROFILE_UPDATE:number = 3020546;
       static readonly SHORTCUT_SHOP_BUY:number = 3545857;
       static readonly ARENA_LOGS:number = 3611524;
       static readonly THEME_ACTIVITY_SKIN_LOTTERY:number = 7617025;
       static readonly STAGE_COPY_SET:number = 9915268;
       static readonly FAMILY_TASK_GIFT:number = 3217540;
       static readonly SIMP_MISSION_UPDATE:number = 1181953;
       static readonly HUNT_REFRESH:number = 3480195;
       static readonly LOTTERY_INFO:number = 2757889;
       static readonly HERO_ACT_FOURTEEN:number = 2429589;
       static readonly LOGIN_ACTIVITY_FETCH:number = 7814017;
       static readonly LCQS_CHAPTER_FETCH:number = 7551361;
       static readonly VIP_BUY_RECOMMEND:number = 4268164;
       static readonly MASTER_CARD_UP_OFFICIAL:number = 9652614;
       static readonly FISH_POWER:number = 10834571;
       static readonly DAWANKA_INFO:number = 8667648;
       static readonly THEME_ACTIVITY_ZHOUKA_INFO:number = 7617029;
       static readonly MODULAR_ACTIVITY_STAR_REPAY_INFO:number = 8142354;
       static readonly FIGHT_TIMES:number = 2692231;
       static readonly HERO_RECYCLE_ALL:number = 2429581;
       static readonly LOGIN_AUTH:number = 656644;
       static readonly GUIDE_FINISH:number = 4071170;
       static readonly MAZE_SPOILS:number = 7945347;
       static readonly STAR_PLAN_UPDATE:number = 7682691;
       static readonly GMCMD_DO:number = 1904256;
       static readonly EQUIP_UNLOAD:number = 2495234;
       static readonly WORSHIP_DO:number = 7026050;
       static readonly CYCLE_ACTIVITY_UPDATE:number = 7223043;
       static readonly ACTIVITY_EXAM_ANSWER:number = 1444616;
       static readonly DIVINE_EQUIP_ACTIVATE_HERO:number = 11425536;
       static readonly BOAT_RACE_RESULT:number = 10112265;
       static readonly FISH_PREVIEW_ATTR:number = 10834570;
       static readonly LORD_EQUIP_COMPOSE:number = 10571914;
       static readonly MOCK_PVP_SHOW_TOPS:number = 11031558;
       static readonly FIGHT_START_PASS:number = 2692230;
       static readonly MODULAR_ACTIVITY_MAZE_AUTO_ROLLING_END:number = 8142373;
       static readonly DAILY_COPY_PROCESS:number = 2889216;
       static readonly COMPLAINTS_INFO:number = 1969920;
       static readonly MAIN_BATTLE_MISSIONS:number = 2626576;
       static readonly FIGHT_SHARE_STAT:number = 10703233;
       static readonly MEDAL_LIST:number = 8864640;
       static readonly SIMP_MISSION_LIST:number = 1181952;
       static readonly FAMILY_HOT_OP:number = 1247640;
       static readonly CHAT_CHANNEL:number = 590736;
       static readonly HERO_SKIN_UPGRADE:number = 5121795;
       static readonly MODULAR_ACTIVITY_GENERAL_PASS_INFO:number = 8142352;
       static readonly ROLE_LOOK_LORD:number = 1116307;
       static readonly RANK_ACTIVITY_INFO:number = 4859136;
       static readonly VIP_INFO:number = 4268160;
       static readonly RANDOM_PVP_FIGHT_RESULT:number = 7879687;
       static readonly HUNT_RUN:number = 3480193;
       static readonly FRIEND_REQUEST:number = 787970;
       static readonly LEVEL_GIFT_INFO:number = 4793472;
       static readonly LETTER_GET:number = 853632;
       static readonly HERO_RESONATE_INFO:number = 8273665;
       static readonly MODULAR_ACTIVITY_REPORT:number = 8142371;
       static readonly TD_MAIN_INFO:number = 10965893;
       static readonly MASTER_CARD_REPLACE:number = 9652613;
       static readonly BINGFU_REFINE:number = 5187462;
       static readonly SOUL_HERO_LINK:number = 8798977;
       static readonly MASTER_CARD_INFO:number = 9652608;
       static readonly EQUIP_DECOMPOSE:number = 2495240;
       static readonly PEAK_INFO_OPP:number = 8404993;
       static readonly FAMILY_SCIENCE_INFO:number = 6106752;
       static readonly ARES_PALACE_INFO:number = 5844096;
       static readonly LARGE_PEAK_INFO_BATTLE:number = 11359876;
       static readonly HERO_RECYCLE_CHANGE:number = 2429583;
       static readonly CSCLAN_CREATE:number = 10637569;
       static readonly BATTLE_TRIAL_BATTLE_UP:number = 9521286;
       static readonly CSCLAN_SOLO_FETCH:number = 11556867;
       static readonly HERO_RECYCLE_PREVIEW:number = 2429573;
       static readonly FISH_HANDBOOK_INFO:number = 10834574;
       static readonly PLAYER_STRATEGY_INFO:number = 8930304;
       static readonly MODULAR_ACTIVITY_SHOP:number = 8142341;
       static readonly FULI_SIGN_INFO:number = 3742848;
       static readonly FIGHT_SHARE_OPERATE:number = 10703232;
       static readonly MODULAR_ACTIVITY_LOGIN_INFO:number = 8142383;
       static readonly HERO_PASS_GIFT:number = 7748355;
       static readonly GOODS_UPDATE:number = 1050625;
       static readonly HZZD_INFO_HISTORY:number = 8011014;
       static readonly FISH_REPLACE:number = 10834567;
       static readonly MASTER_CARD_DECORATION_OP:number = 9652620;
       static readonly MASTER_CARD_PREVIEW_ATTR:number = 9652616;
       static readonly EQUIP_BINGFA_EXCHANGE:number = 2495253;
       static readonly DAILY_GIFT_NEW_DISCOUNT_INFO:number = 4924805;
       static readonly MODULAR_ACTIVITY_SEVEN_GOAL_FETCH:number = 8142376;
       static readonly SYSTEM_CONFIG_CHANGE:number = 1378947;
       static readonly FAMILY_TASK_DO:number = 3217538;
       static readonly DAILY_MISSION_GIFT:number = 2101249;
       static readonly LETTER_SEND:number = 853634;
       static readonly MONTH_FUND_INFO:number = 3939840;
       static readonly HERO_ZHOUYIN_INFO:number = 10046592;
       static readonly FRIEND_AGREE:number = 787971;
       static readonly DAILY_GIFT_INFO:number = 4924800;
       static readonly BOAT_RACE_RANK:number = 10112263;
       static readonly COMMON_ERROR:number = 722304;
       static readonly MODULAR_ACTIVITY_DICE_UNLOCK:number = 8142386;
       static readonly EQUIP_INFO_UPDATE:number = 2495245;
       static readonly FAMILY_SIGN_INFO:number = 6041088;
       static readonly FAMILY_JOIN:number = 1247622;
       static readonly HERO_RECYCLE_GAINS_ITEM:number = 2429582;
       static readonly HANGING_INFO:number = 3348864;
       static readonly CYCLE_ACTIVITY_FETCH:number = 7223041;
       static readonly SYSTEM_MESSAGE:number = 1378946;
       static readonly STORY_MAZE_FETCH:number = 10243588;
       static readonly LORD_STAR:number = 10571911;
       static readonly FAMILY_BOSS_SWEEP:number = 6303745;
       static readonly TREASURE_DISPATCH:number = 10506241;
       static readonly BOAT_RACE_ROLE_ITEMS:number = 10112266;
       static readonly SHARE_SINGLE_INFO:number = 5515776;
       static readonly MOCK_PVP_OP:number = 11031552;
       static readonly FAMILY_TASK_INFO:number = 3217536;
       static readonly TEST_TOWER_FETCH:number = 5909761;
       static readonly RED_CLIFF_INFO:number = 6369408;
       static readonly ACTIVITY_LIST:number = 1444608;
       static readonly ROLE_SCORE_UPDATE:number = 1707265;
       static readonly HANGING_QUICK:number = 3348866;
       static readonly LARGE_PEAK_INFO_MEMBER:number = 11359874;
       static readonly SYS_DAILY_FETCH:number = 6697729;
       static readonly MODULAR_ACTIVITY_HOLIDAY_WELFARE_INFO:number = 8142388;
       static readonly RANDOM_PVP_PALACE:number = 7879684;
       static readonly LORD_SET_LINEUP:number = 10571907;
       static readonly LARGE_PEAK_PERSONAL_INFO:number = 11359880;
       static readonly PAGE_SWITCH:number = 4202497;
       static readonly HERO_COME_INFO:number = 7157376;
       static readonly CYCLE_ACTIVITY_INFO:number = 7223040;
       static readonly SEVEN_GOAL_GIFT:number = 3808515;
       static readonly FAMILY_BOSS_RANK:number = 6303748;
       static readonly TIME_ACTIVITY_UPDATE:number = 4136834;
       static readonly FAMILY_ACTIVE_INFO:number = 6960384;
       static readonly CSCLAN_RENAME:number = 10637579;
       static readonly FAMILY_SIGN_FETCH:number = 6041089;
       static readonly GOD_EQUIP_RECAST:number = 2560896;
       static readonly HZZD_FETCH:number = 8011012;
       static readonly OPTION_LOTTERY_INFO:number = 7485697;
       static readonly FAMILY_MEMBERS:number = 1247618;
       static readonly HONGBAO_INFO:number = 11753856;
       static readonly FIRST_PAY_FETCH:number = 3874177;
       static readonly WARS_LOOK_OP_FIGHT_LOG:number = 11622545;
       static readonly PEAK_INFO_HISTORY:number = 8404999;
       static readonly TD_LINEUP:number = 10965889;
       static readonly TD_SKILL:number = 10965890;
       static readonly PEAK_INFO_GET_MSG:number = 8405001;
       static readonly MODULAR_ACTIVITY_DICE:number = 8142385;
       static readonly FAMILY_SET_OWNER:number = 1247627;
       static readonly WARS_LOOK_OP_ARMY:number = 11622543;
       static readonly MODULAR_ACTIVITY_MAZE_USE_ITEM:number = 8142368;
       static readonly GUANDU_OP:number = 6500737;
       static readonly CASTING_SOUL_OP:number = 8076672;
       static readonly ROLE_LOOK_LINEUP:number = 1116302;
       static readonly CYCLE_ACTIVITY_GIFT:number = 7223042;
       static readonly EQUIP_LOAD:number = 2495233;
       static readonly RED_CLIFF_FIGHT_RESULT:number = 6369410;
       static readonly MODULAR_ACTIVITY_RARE_LOTTERY_INFO:number = 8142344;
       static readonly ITEM_COMPOSE:number = 919297;
       static readonly EIGHT_LOGIN_INFO:number = 3677184;
       static readonly EQUIP_BINGFA_OP:number = 2495252;
       static readonly EQUIP_FILTER_INFO:number = 2495246;
       static readonly WARS_WALK_LIST:number = 11622531;
       static readonly RANK_MISSION_INFO:number = 6763392;
       static readonly TEAM_INFO_TEAMS:number = 9455618;
       static readonly LCQS_FIGHT_RESULT:number = 7551364;
       static readonly ROLE_CROSS_GROUP:number = 1116308;
       static readonly SQUAD_LINEUP_SET:number = 4662145;
       static readonly STORY_MAZE_FIGHT_RESULT:number = 10243592;
       static readonly MODULAR_ACTIVITY_PREVIEW_INFO:number = 8142396;
       static readonly HONGBAO_FETCH_UPDATE:number = 11753860;
       static readonly RANDOM_PVP_MATCH:number = 7879682;
       static readonly MODULAR_ACTIVITY_FETCH:number = 8142337;
       static readonly FAMILY_SCIENCE_OP:number = 6106753;
       static readonly THEME_ACTIVITY_FAMOUS_LOTTERY_INFO:number = 7617027;
       static readonly QXZL_INFO:number = 7091712;
       static readonly FISH_SPE_EFFECT:number = 10834572;
       static readonly PAYMENT_AUTO_BUY:number = 1772934;
       static readonly MEDAL_RETIRE_UPDATE:number = 8864646;
       static readonly MODULAR_ACTIVITY_SKIN_INFO:number = 8142348;
       static readonly STORY_TOWER_BATTLE_FETCH:number = 10374913;
       static readonly INGENIOUS_PLAN_STAR:number = 8733315;
       static readonly WARS_LOOK_OP_SCORE_RANK:number = 11622544;
       static readonly MODULAR_ACTIVITY_STORY_INFO:number = 8142397;
       static readonly BOAT_RACE_BONUS:number = 10112257;
       static readonly BATTLE_TRIAL_PASS_INFO:number = 9521282;
       static readonly ACC_PAY_INFO:number = 2232576;
       static readonly XSWH_RANK:number = 8601986;
       static readonly CSC_FMSOLO_INFO:number = 7288704;
       static readonly HERO_CHEER_INFO:number = 11228544;
       static readonly HERO_PASS_FETCH:number = 7748353;
       static readonly SYS_USE_TIMES_UPDATE:number = 9127297;
       static readonly SOUL_HERO_UNLOCK:number = 8798979;
       static readonly HERO_LIST:number = 2429568;
       static readonly MODULAR_ACTIVITY_STORY_GROUP:number = 8142399;
       static readonly FAMILY_HONGBAO_INFO_UPDATE:number = 4465155;
       static readonly TEAM_XSWH_FIGHT_RESULT:number = 9586945;
       static readonly MODULAR_ACTIVITY_MAZE_INFO:number = 8142357;
       static readonly TEST_TOWER_SWEEP:number = 5909764;
       static readonly TIME_ACHIEVEMENT_UPDATE:number = 6632065;
       static readonly WAR_FLAG_LINK:number = 6172419;
       static readonly GUIDE_HINT:number = 4071169;
       static readonly TD_TRIAL_INFO:number = 10965892;
       static readonly GOD_EQUIP_ENCHANT:number = 2560900;
       static readonly SEVEN_GOAL_FETCH:number = 3808514;
       static readonly FAMILY_SELF:number = 1247619;
       static readonly FULI_TOKEN_INFO:number = 4530816;
       static readonly HERO_RESONATE_EQUIP_OP:number = 8273668;
       static readonly HERO_HANDBOOK_ALL_FETCH:number = 6566402;
       static readonly MODULAR_ACTIVITY_SKIN_LOTTERY:number = 8142349;
       static readonly LETTER_OPEN:number = 853633;
       static readonly WORSHIP_ONLINE:number = 7026048;
       static readonly SYSTEM_ERROR:number = 1378945;
       static readonly ROLE_FAMILY_CHANGE:number = 1116296;
       static readonly PASS_BEHEAD_INFO:number = 5975424;
       static readonly MODULAR_ACTIVITY_HERO_CHALLENGE_REPORT:number = 8142394;
       static readonly SYSTEM_HEARTBEAT:number = 1378944;
       static readonly HERO_RESONATE_LEVEL_INFO:number = 8273664;
       static readonly PAY_GIFT_INFO:number = 2166912;
       static readonly TEAM_INFO_MY_TEAM_UPDATE:number = 9455620;
       static readonly FAMILY_INFO_CHANGE:number = 1247630;
       static readonly PROFILE_CHANGE:number = 3020545;
       static readonly FAMILY_FLAGNAME:number = 1247636;
       static readonly TEAM_XSWH_FETCH:number = 9586948;
       static readonly ARENA_MAX_REWARD_INFO:number = 3611526;
       static readonly PASS_CHECK_FETCH:number = 7420033;
       static readonly LOTTERY_START:number = 2757888;
       static readonly MASTER_CARD_POWER:number = 9652617;
       static readonly FIGHT_FINISH:number = 2692225;
       static readonly DAY_ACC_PAY_GIFT_INFO:number = 10900224;
       static readonly WARS_MAKE_PATH:number = 11622554;
       static readonly FAMILY_TASK_UPDATE:number = 3217537;
       static readonly ROLE_UPDATE_EXT:number = 1116299;
       static readonly CSCLAN_APPLY_LIST:number = 10637577;
       static readonly FAMILY_SIGN_DO:number = 6041090;
       static readonly LOGIN_CHOSE:number = 656641;
       static readonly CHAT_COST:number = 590749;
       static readonly PEAK_INFO_MEMBER:number = 8404994;
       static readonly BINGFU_SHIFT:number = 5187459;
       static readonly CROSS_LADDER_LIST:number = 8536323;
       static readonly LOTTERY_SCORE_FETCH:number = 2757890;
       static readonly HUNT_INFO:number = 3480192;
       static readonly BAG_GOODS_LIST:number = 984960;
       static readonly ITEM_SHOW_GAINS:number = 919298;
       static readonly MODULAR_ACTIVITY_MAZE_SPOILS:number = 8142360;
       static readonly SYS_DAILY_INFO:number = 6697728;
       static readonly MEDAL_DETAIL:number = 8864645;
       static readonly MASTER_CARD_SPE_EFFECT:number = 9652618;
       static readonly HERO_UPGRADE:number = 2429571;
       static readonly MAIN_BATTLE_INFO:number = 2626560;
       static readonly LOTTERY_NATION_START:number = 2757891;
       static readonly DIVINE_EQUIP_INFO:number = 11425537;
       static readonly WAR_FLAG_ACTIVE:number = 6172417;
       static readonly TEST_TOWER_OP:number = 5909766;
       static readonly ARENA_FIGHT_RESULT:number = 3611525;
       static readonly RED_CLIFF_SWEEP:number = 6369409;
       static readonly WARS_INFO_GET_MSG:number = 11622551;
       static readonly MOCK_PVP_LOGS:number = 11031555;
       static readonly BOAT_RACE_OP:number = 10112261;
       static readonly MOCK_PVP_HEAT:number = 11031563;
       static readonly WORLD_BOSS_INFO:number = 7354368;
       static readonly ONLINE_REWARD_INFO:number = 3414528;
       static readonly DOMINATE_PVP_NEW_SEASON:number = 11294214;
       static readonly DAWANKA_FETCH:number = 8667649;
       static readonly RECOMMEND_LINEUP_INFO:number = 4596480;
       static readonly MONSTER_GROUP_POWER:number = 6829056;
       static readonly WAR_FLAG_INFO:number = 6172416;
       static readonly FAMILY_REPLY_JOIN:number = 1247624;
       static readonly FAMILY_HONGBAO_FETCH:number = 4465154;
       static readonly CHAT_GET_GOODS:number = 590726;
       static readonly BOAT_RACE_ALLOC_ITEMS:number = 10112267;
       static readonly LORD_SKILL_ATTACK_INC_CALC:number = 10571912;
       static readonly LOGIN_SCENE:number = 656647;
       static readonly EQUIP_COMPOSE_LOGS:number = 2495243;
       static readonly EQUIP_AUTO_LOAD:number = 2495235;
       static readonly ITEM_SALE:number = 919299;
       static readonly GUIDE_GET_HERO:number = 4071173;
       static readonly HZZD_TEAM_INFO:number = 8011013;
       static readonly MODULAR_ACTIVITY_BRICK:number = 8142405;
       static readonly XSWH_FETCH:number = 8601988;
       static readonly EQUIP_RECYCLE:number = 2495239;
       static readonly EQUIP_AUTO_UNLOAD:number = 2495236;
       static readonly WELFARE_SDK_SHARE:number = 1575937;
       static readonly LARGE_PEAK_INFO_HISTORY:number = 11359879;
       static readonly FAMILY_UPLEVEL:number = 1247621;
       static readonly FRIEND_FETCH:number = 787975;
       static readonly MODULAR_ACTIVITY_STAR_PLAN_UPDATE:number = 8142351;
       static readonly MASTER_TALENT_SCIENCE_UPDATE:number = 10177922;
       static readonly MODULAR_ACTIVITY_SIX_BLESS_INFO:number = 8142414;
       static readonly MAIN_BATTLE_MISSION_FETCH:number = 2626565;
       static readonly LORD_RECYCLE_PREVIEW:number = 10571910;
       static readonly TAX_SILVER_FETCH:number = 2954881;
       static readonly MASTER_CARD_SHOP:number = 9652612;
       static readonly COMMON_SHOW_ATTR:number = 722306;
       static readonly PASS_BEHEAD_BATTLE:number = 5975429;
       static readonly BOAT_PEAK_HIS:number = 10768903;
       static readonly MODULAR_ACTIVITY_HEAVEN_GIVE_INFO:number = 8142400;
       static readonly TAX_FORAGE_INFO:number = 2954882;
       static readonly DIVINE_COPY_INFO:number = 11491201;
       static readonly ROLE_ACCLOGIN:number = 1116293;
       static readonly CSC_FMSOLO_BOX:number = 7288708;
       static readonly BATTLE_TRIAL_FETCH_INFO:number = 9521281;
       static readonly WORSHIP_INFO:number = 7026049;
       static readonly BATTLE_TRIAL_INFO:number = 9521280;
       static readonly STORY_MAZE_PUB:number = 10243591;
       static readonly PAGE_LIST:number = 4202496;
       static readonly TITLE_LIST:number = 3283200;
       static readonly MASTER_CARD_NOTICE:number = 9652615;
       static readonly MODULAR_ACTIVITY_GENERAL_PASS_FETCH:number = 8142353;
       static readonly FIGHT_SIMP_RESULT:number = 2692227;
       static readonly QXZL_INFO_LAST_RANK:number = 7091720;
       static readonly HERO_SKIN_RESET:number = 5121796;
       static readonly FRIEND_FIND:number = 787973;
       static readonly BUY_TIMES_UPDATE:number = 6435074;
       static readonly LETTER_OPERATE:number = 853635;
       static readonly EQUIP_INFO:number = 2495232;
       static readonly WARS_EVENT:number = 11622553;
       static readonly HERO_PASS_INFO:number = 7748352;
       static readonly LARGE_PEAK_INFO:number = 11359872;
       static readonly THEME_ACTIVITY_SKIN_INFO:number = 7617024;
       static readonly QUICK_SHOP_TIPS:number = 5384450;
       static readonly STORY_MAZE_HERO:number = 10243590;
       static readonly MAZE_INFO:number = 7945344;
       static readonly HERO_HANDBOOK_LIST:number = 6566400;
       static readonly TITLE_LOAD:number = 3283201;
       static readonly FISH_UPDATE:number = 10834564;
       static readonly STAGE_COPY_SWEEP:number = 9915265;
       static readonly SHORTCUT_SHOP_INFO:number = 3545856;
       static readonly TREASURE_REFRESH:number = 10506248;
       static readonly PAY_GIFT_FETCH:number = 2166913;
       static readonly HUNT_LOGS:number = 3480196;
       static readonly MODULAR_ACTIVITY_WAR_LOG_UPDATE:number = 8142356;
       static readonly LOTTERY_SUPREME_START:number = 2757894;
       static readonly MODULAR_ACTIVITY_CARNIVAL:number = 8142382;
       static readonly GOD_EQUIP_COMPOSE:number = 2560897;
       static readonly MODULAR_ACTIVITY_LOTTERY_TIPS:number = 8142347;
       static readonly WAR_FLAG_OP:number = 6172418;
       static readonly LARGE_PEAK_INFO_LOOK_MEMBER:number = 11359875;
       static readonly WARS_LOOK_OP_SIGN:number = 11622542;
       static readonly TEAM_XSWH_BEST_RANK:number = 9586947;
       static readonly FAMILY_SET_TITLE:number = 1247626;
       static readonly GUIDE_MISSION:number = 4071168;
       static readonly HINT_LIST:number = 2035584;
       static readonly LORD_OP:number = 10571913;
       static readonly EQUIP_STAR_OP:number = 2495248;
       static readonly CROSS_LADDER_OPP_INFO:number = 8536322;
       static readonly FAMILY_BOSS_GATHER:number = 6303751;
       static readonly WARS_UPDATE_ROLE:number = 11622556;
       static readonly MODULAR_ACTIVITY_HUNT_LOGS:number = 8142380;
       static readonly TEAM_MERGE_REWARD:number = 9455625;
       static readonly MODULAR_ACTIVITY_CUSTOMIZED_GIFT_SELECT:number = 8142401;
       static readonly MASTER_CARD_LOTTERY:number = 9652609;
       static readonly TREASURE_EXIST:number = 10506250;
       static readonly BOAT_PEAK_OPP:number = 10768897;
       static readonly DOMINATE_PVP_OP:number = 11294208;
       static readonly WARS_OPERATE:number = 11622534;
       static readonly BOAT_PEAK_LOOK_PRE_SCORE:number = 10768906;
       static readonly LORD_SKILL_SET:number = 10571906;
       static readonly STORY_MAZE_INFO:number = 10243584;
       static readonly TEAM_LINEUP_SET:number = 9455624;
       static readonly GUIDE_EVENT:number = 4071172;
       static readonly WARS_TEAM_INFO:number = 11622537;
       static readonly FAMILY_CREATE:number = 1247617;
       static readonly GMCMD_BATTLE_EDIT:number = 1904258;
       static readonly BUILDING_INFO:number = 11819520;
       static readonly VIP_KEFU_OP:number = 5056129;
       static readonly CSCLAN_SOLO_INFO:number = 11556865;
       static readonly FISH_INFO:number = 10834560;
       static readonly ACC_GIFT_INFO:number = 10440577;
       static readonly RANDOM_PVP_UPDATE:number = 7879686;
       static readonly RANKING_HISTORY_LIST:number = 1510276;
       static readonly MODULAR_ACTIVITY_LIST:number = 8142336;
       static readonly TEAM_SHARE_LIST:number = 9455626;
       static readonly SHOP_INFO:number = 2363904;
       static readonly THEME_ACTIVITY_FAMOUS_LOTTERY_UPDATE:number = 7617028;
       static readonly MODULAR_ACTIVITY_DICE_INFO:number = 8142384;
       static readonly STORY_TOWER_BATTLE_INFO:number = 10374912;
       static readonly BOAT_RACE_BOATS:number = 10112259;
       static readonly FAMILY_AUDIT_LIMIT:number = 1247625;
       static readonly MODULAR_ACTIVITY_UPDATE_FETCH:number = 8142340;
       static readonly EQUIP_AUTO_REINFORCE:number = 2495238;
       static readonly HERO_UPDATE_LIST:number = 2429569;
       static readonly ITINERANT_SHOP_INFO:number = 11688192;
       static readonly FULI_FUND_INFO:number = 3742853;
       static readonly BOAT_PEAK_FETCH:number = 10768908;
       static readonly MODULAR_ACTIVITY_LOTTERY_START:number = 8142342;
       static readonly FAMILY_BOSS_UPDATE:number = 6303746;
       static readonly FAMILY_LOGS:number = 1247631;
       static readonly DEPUTY_OP:number = 2495257;
       static readonly STORY_MAZE_START:number = 10243586;
       static readonly FIGHT_SHARE_CHAT:number = 10703234;
       static readonly BOAT_RACE_LOGS:number = 10112264;
       static readonly SYS_CONCERN_INFO:number = 5450112;
       static readonly FAMILY_LIST:number = 1247616;
       static readonly TREASURE_WORKER_INFO:number = 10506254;
       static readonly ACTIVITY_YUEKA_INFO:number = 1444619;
       static readonly MODULAR_ACTIVITY_CUSTOMIZED_GIFT_SELECT_LIST:number = 8142403;
       static readonly MODULAR_ACTIVITY_FAMOUS_LOTTERY_INFO:number = 8142343;
       static readonly MASTER_TALENT_SCIENCE_INFO:number = 10177921;
       static readonly FISH_LOTTERY:number = 10834562;
       static readonly MASTER_CARD_UPDATE:number = 9652611;
       static readonly FULI_SIGN_FETCH:number = 3742849;
       static readonly TEAM_LINEUP_LIST:number = 9455623;
       static readonly TEAM_OPERATE:number = 9455621;
       static readonly WING_OP:number = 2495255;
       static readonly MODULAR_ACTIVITY_SEVEN_GOAL_INFO:number = 8142374;
       static readonly LEVEL_GIFT_FETCH:number = 4793473;
       static readonly FAMILY_INFO:number = 1247634;
       static readonly CHAT_CLEAN:number = 590748;
       static readonly SYS_USE_TIMES_INFO:number = 9127296;
       static readonly TREASURE_GIFT_ACTIVE:number = 10506251;
       static readonly DAILY_AD_CODE_INFO:number = 5712768;
       static readonly DAILY_COPY_ENTER:number = 2889217;
       static readonly ROLE_SETTING:number = 1116301;
       static readonly SYSTEM_CONFIG:number = 1378948;
       static readonly HERO_INHERIT_UPDATE:number = 2429586;
       static readonly BINGFU_BREED:number = 5187457;
       static readonly WARS_ROLE_PRESONAL_INFO:number = 11622539;
       static readonly FAMILY_HONGBAO_MISSION_SEND:number = 4465158;
       static readonly INGENIOUS_PLAN_INFO:number = 8733312;
       static readonly MODULAR_ACTIVITY_DAY_SHOP:number = 8142413;
       static readonly BOAT_RACE_EVENT:number = 10112260;
       static readonly MONTH_FUND_UPDATE:number = 3939843;
       static readonly BUY_TIMES_INFO:number = 6435072;
       static readonly TEST_TOWER_PROCESS:number = 5909760;
       static readonly HERO_BAG_EXPANSION:number = 2429580;
       static readonly QUICK_SHOP_INFO:number = 5384448;
       static readonly ARENA_LIST:number = 3611523;
       static readonly PEAK_INFO_BET:number = 8404998;
       static readonly HERO_CHEER_OP:number = 11228545;
       static readonly PEAK_INFO_LOOK_MEMBER:number = 8404995;
       static readonly DIVINE_EQUIP_OP:number = 11425539;
       static readonly GENERAL_INFO:number = 8208000;
       static readonly CSCLAN_SOLO_GROUP:number = 11556866;
       static readonly SHARE_FETCH:number = 5581441;
       static readonly MAZE_FIGHT_RESULT:number = 7945354;
       static readonly MODULAR_ACTIVITY_SEVEN_GOAL_GIFT:number = 8142377;
       static readonly TRAVEL_ACCEPT:number = 3151874;
       static readonly EQUIP_REPLACE:number = 2495244;
       static readonly TREASURE_USE_ITEM:number = 10506247;
       static readonly MULTI_LINEUP_LIST:number = 8339329;
       static readonly MONSTER_GROUP_POWER_LIST:number = 6829057;
       static readonly QXZL_INFO_HISTORY:number = 7091719;
       static readonly MOCK_PVP_CONFIG:number = 11031557;
       static readonly WARS_LOOK_OP_CAMP_RANK:number = 11622547;
       static readonly MODULAR_ACTIVITY_PREVIEW:number = 8142395;
       static readonly CHAT_MISC:number = 590747;
       static readonly GOD_TRIAL_FIGHT_RESULT:number = 5778436;
       static readonly MAZE_START:number = 7945346;
       static readonly ROLE_SCORE_LIST:number = 1707264;
       static readonly FULI_TOKEN_FETCH:number = 4530818;
       static readonly FIGHT_SET_SPEED:number = 2692226;
       static readonly MAIN_BATTLE_BOX_INFO:number = 2626567;
       static readonly THEME_ACTIVITY_UP_STAR_REWARD_UPDATE:number = 7617035;
       static readonly TEAM_LINEUP_GET:number = 9455622;
       static readonly HANGING_REWARD:number = 3348865;
       static readonly CROSS_LADDER_INFO:number = 8536321;
       static readonly FIRST_PAY_INFO:number = 3874176;
       static readonly QXZL_INFO_GET_MSG:number = 7091723;
       static readonly MODULAR_ACTIVITY_FESTIVAL_WISH_INFO:number = 8142416;
       static readonly BROADCAST_SHOW:number = 1313281;
       static readonly HERO_DOWN_LINEUP:number = 2429576;
       static readonly FAMILY_BOSS_ATTR:number = 6303750;
       static readonly HERO_SKIN_INFO:number = 5121792;
       static readonly DAILY_MISSION_INFO:number = 2101248;
       static readonly MULTI_LINEUP_SET:number = 8339328;
       static readonly GOD_TRIAL_FETCH:number = 5778433;
       static readonly RANDOM_PVP_LOGS:number = 7879685;
       static readonly LOGIN_ACTIVITY_INFO:number = 7814016;
       static readonly SQUAD_LINEUP_GET:number = 4662144;
       static readonly QXZL_INFO_LAST_BATTLE:number = 7091721;
       static readonly COMMON_TIPS:number = 722305;
       static readonly MODULAR_ACTIVITY_WISH_LOTTERY_INFO:number = 8142345;
       static readonly DAILY_PAY_FETCH:number = 4005505;
       static readonly QQVIP_INFO:number = 4990464;
       static readonly MOCK_PVP_FIGHT_RESULT:number = 11031560;
       static readonly ROLE_ATTR_CHANGE:number = 1116289;
       static readonly FAMILY_OPERATE:number = 1247629;
       static readonly MAIN_BATTLE_MISSION_STATUS:number = 2626566;
       static readonly RANDOM_PVP_NEW_SEASON:number = 7879688;
       static readonly ZERO_BUY_OP:number = 11885184;
       static readonly XSWH_BEST_RANK:number = 8601987;
       static readonly ARES_PALACE_LOG:number = 5844097;
       static readonly MAIN_BATTLE_BOX_UPGRADE:number = 2626573;
       static readonly BOAT_PEAK_SHOP:number = 10768904;
       static readonly WARS_INIT_PERSONAL:number = 11622530;
       static readonly TEST_TOWER_SKIP:number = 5909765;
       static readonly TIME_ACTIVITY_INFO:number = 4136832;
       static readonly FRIEND_LIST:number = 787968;
       static readonly WORLD_LEVEL_INFO:number = 5253120;
       static readonly TEAM_XSWH_RANK:number = 9586946;
       static readonly STORY_MAZE_SPOILS:number = 10243587;
       static readonly MAIN_BATTLE_FIGHT_RESULT:number = 2626561;
       static readonly GOODS_LIST:number = 1050624;
       static readonly MAZE_PUB:number = 7945352;
       static readonly MODULAR_ACTIVITY_WAR_LOG_INFO:number = 8142355;
       static readonly ACTIVITY_EXAM_LIST:number = 1444615;
       static readonly FISH_LINEUP_LIST:number = 10834565;
       static readonly SGAME_WX_CLUB:number = 9980932;
       static readonly PLAYING_PREVIEW_INFO:number = 9783936;
       static readonly CSCLAN_SOLO_STATE:number = 11556868;
       static readonly HERO_CONVERT_INFO:number = 2823552;
       static readonly HERO_ZHOUYIN_UPGRADE:number = 10046593;
       static readonly ITEM_USE:number = 919296;
       static readonly TREASURE_PULL_LIST:number = 10506249;
       static readonly ROLE_LEVEL_UP:number = 1116288;
       static readonly HERO_RESONATE_OPERATE:number = 8273666;
       static readonly MODULAR_ACTIVITY_HERO_CHALLENGE_INFO:number = 8142392;
       static readonly TREASURE_ROLE_INFO:number = 10506245;
       static readonly STORY_SIEGELORD_INFO:number = 10309248;
       static readonly SOUL_HERO_RESET:number = 8798978;
       static readonly MISSION_SHOP_INFO:number = 4727808;
       static readonly ACTIVITY_SHOP_INFO:number = 1444611;
       static readonly EIGHT_LOGIN_FETCH:number = 3677185;
       static readonly CROSS_LADDER_FIGHT_RESULT:number = 8536325;
       static readonly VIP_FREE_GIFT_INFO:number = 4268162;
       static readonly DOMINATE_PVP_UPDATE:number = 11294212;
       static readonly WELFARE_ACTIVATE_CODE:number = 1575936;
       static readonly RANKING_SIMPLE:number = 1510275;
       static readonly WARS_HONOR_WALL:number = 11622557;
       static readonly DIVINE_EQUIP_COMPOSE:number = 11425538;
       static readonly MEDAL_UPDATE:number = 8864643;
       static readonly PLAYING_PREVIEW_AWARD:number = 9783937;
       static readonly FISH_UP_OFFICIAL:number = 10834568;
       static readonly LARGE_PEAK_INFO_OPP:number = 11359873;
       static readonly WARS_INIT:number = 11622529;
       static readonly TREASURE_RESULT:number = 10506244;
       static readonly DOMINATE_PVP_LOGS:number = 11294211;
       static readonly DOMINATE_PVP_INFO:number = 11294209;
       static readonly TEST_TOWER_CONTINUOUS_FIGHT_RESULT:number = 5909767;
       static readonly XSWH_INFO:number = 8601984;
       static readonly MODULAR_ACTIVITY_SIGN_INFO:number = 8142390;
       static readonly FULI_YUEKA_FETCH:number = 3742852;
       static readonly FRIEND_OPERATE:number = 787972;
       static readonly LOGIN_ADD:number = 656640;
       static readonly TITLE_UPDATE:number = 3283202;
       static readonly HERO_COME_UPDATE:number = 7157378;
       static readonly CSC_FMSOLO_FETCH:number = 7288706;
       static readonly MISSION_SHOP_UPDATE:number = 4727809;
       static readonly SHARE_INFO:number = 5581440;
       static readonly ACHIEVEMENT_INFO:number = 3086208;
       static readonly MULTI_LINEUP_GET_POWER:number = 8339330;
       static readonly ROLE_RENAME:number = 1116291;
       static readonly WING_INFO:number = 2495256;
       static readonly EQUIP_BINGFU_RECAST:number = 2495249;

       static protoMap:object = null;
       static getProtoNameByCommand(command:number):string
       {
         return SocketCommand.protoMap[command];
       }

       static init():void
       {
         if(SocketCommand.protoMap)
          {
            return
          }

         SocketCommand.protoMap = {};
         SocketCommand.protoMap[10506240] = "proto.line.m_treasure_info_toc";
         ClassUtils.regClass("proto.line.m_treasure_info_toc",m_treasure_info_toc);
         SocketCommand.protoMap[6303744] = "proto.line.m_family_boss_info_toc";
         ClassUtils.regClass("proto.line.m_family_boss_info_toc",m_family_boss_info_toc);
         SocketCommand.protoMap[7354370] = "proto.line.m_world_boss_fight_result_toc";
         ClassUtils.regClass("proto.line.m_world_boss_fight_result_toc",m_world_boss_fight_result_toc);
         SocketCommand.protoMap[3151875] = "proto.line.m_travel_speed_toc";
         ClassUtils.regClass("proto.line.m_travel_speed_toc",m_travel_speed_toc);
         SocketCommand.protoMap[8404996] = "proto.line.m_peak_info_battle_toc";
         ClassUtils.regClass("proto.line.m_peak_info_battle_toc",m_peak_info_battle_toc);
         SocketCommand.protoMap[6303749] = "proto.line.m_family_boss_fight_result_toc";
         ClassUtils.regClass("proto.line.m_family_boss_fight_result_toc",m_family_boss_fight_result_toc);
         SocketCommand.protoMap[11556870] = "proto.line.m_csclan_solo_shop_lv_toc";
         ClassUtils.regClass("proto.line.m_csclan_solo_shop_lv_toc",m_csclan_solo_shop_lv_toc);
         SocketCommand.protoMap[9455617] = "proto.line.m_team_info_my_team_toc";
         ClassUtils.regClass("proto.line.m_team_info_my_team_toc",m_team_info_my_team_toc);
         SocketCommand.protoMap[10506248] = "proto.line.m_treasure_refresh_toc";
         ClassUtils.regClass("proto.line.m_treasure_refresh_toc",m_treasure_refresh_toc);
         SocketCommand.protoMap[8405001] = "proto.line.m_peak_info_get_msg_toc";
         ClassUtils.regClass("proto.line.m_peak_info_get_msg_toc",m_peak_info_get_msg_toc);
         SocketCommand.protoMap[8405002] = "proto.line.m_peak_personal_info_toc";
         ClassUtils.regClass("proto.line.m_peak_personal_info_toc",m_peak_personal_info_toc);
         SocketCommand.protoMap[10506251] = "proto.line.m_treasure_gift_active_toc";
         ClassUtils.regClass("proto.line.m_treasure_gift_active_toc",m_treasure_gift_active_toc);
         SocketCommand.protoMap[8011009] = "proto.line.m_hzzd_route_info_toc";
         ClassUtils.regClass("proto.line.m_hzzd_route_info_toc",m_hzzd_route_info_toc);
         SocketCommand.protoMap[1050626] = "proto.line.m_goods_show_goods_toc";
         ClassUtils.regClass("proto.line.m_goods_show_goods_toc",m_goods_show_goods_toc);
         SocketCommand.protoMap[11753858] = "proto.line.m_hongbao_fetch_toc";
         ClassUtils.regClass("proto.line.m_hongbao_fetch_toc",m_hongbao_fetch_toc);
         SocketCommand.protoMap[10506243] = "proto.line.m_treasure_log_toc";
         ClassUtils.regClass("proto.line.m_treasure_log_toc",m_treasure_log_toc);
         SocketCommand.protoMap[8667649] = "proto.line.m_dawanka_fetch_toc";
         ClassUtils.regClass("proto.line.m_dawanka_fetch_toc",m_dawanka_fetch_toc);
         SocketCommand.protoMap[3151876] = "proto.line.m_travel_fetch_toc";
         ClassUtils.regClass("proto.line.m_travel_fetch_toc",m_travel_fetch_toc);
         SocketCommand.protoMap[11556869] = "proto.line.m_csclan_solo_box_toc";
         ClassUtils.regClass("proto.line.m_csclan_solo_box_toc",m_csclan_solo_box_toc);
         SocketCommand.protoMap[5909766] = "proto.line.m_test_tower_op_toc";
         ClassUtils.regClass("proto.line.m_test_tower_op_toc",m_test_tower_op_toc);
         SocketCommand.protoMap[7879681] = "proto.line.m_random_pvp_info_toc";
         ClassUtils.regClass("proto.line.m_random_pvp_info_toc",m_random_pvp_info_toc);
         SocketCommand.protoMap[10506246] = "proto.line.m_treasure_cal_time_toc";
         ClassUtils.regClass("proto.line.m_treasure_cal_time_toc",m_treasure_cal_time_toc);
         SocketCommand.protoMap[11294214] = "proto.line.m_dominate_pvp_new_season_toc";
         ClassUtils.regClass("proto.line.m_dominate_pvp_new_season_toc",m_dominate_pvp_new_season_toc);
         SocketCommand.protoMap[4793472] = "proto.line.m_level_gift_info_toc";
         ClassUtils.regClass("proto.line.m_level_gift_info_toc",m_level_gift_info_toc);
         SocketCommand.protoMap[11031559] = "proto.line.m_mock_pvp_ready_toc";
         ClassUtils.regClass("proto.line.m_mock_pvp_ready_toc",m_mock_pvp_ready_toc);
         SocketCommand.protoMap[8536321] = "proto.line.m_cross_ladder_info_toc";
         ClassUtils.regClass("proto.line.m_cross_ladder_info_toc",m_cross_ladder_info_toc);
         SocketCommand.protoMap[9061632] = "proto.line.m_mini_game_get_str_toc";
         ClassUtils.regClass("proto.line.m_mini_game_get_str_toc",m_mini_game_get_str_toc);
         SocketCommand.protoMap[8142355] = "proto.line.m_modular_activity_war_log_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_war_log_info_toc",m_modular_activity_war_log_info_toc);
         SocketCommand.protoMap[9455624] = "proto.line.m_team_lineup_set_toc";
         ClassUtils.regClass("proto.line.m_team_lineup_set_toc",m_team_lineup_set_toc);
         SocketCommand.protoMap[11031561] = "proto.line.m_mock_pvp_contest_chat_toc";
         ClassUtils.regClass("proto.line.m_mock_pvp_contest_chat_toc",m_mock_pvp_contest_chat_toc);
         SocketCommand.protoMap[1575936] = "proto.line.m_welfare_activate_code_toc";
         ClassUtils.regClass("proto.line.m_welfare_activate_code_toc",m_welfare_activate_code_toc);
         SocketCommand.protoMap[2626570] = "proto.line.m_main_battle_auto_toc";
         ClassUtils.regClass("proto.line.m_main_battle_auto_toc",m_main_battle_auto_toc);
         SocketCommand.protoMap[5909767] = "proto.line.m_test_tower_continuous_fight_result_toc";
         ClassUtils.regClass("proto.line.m_test_tower_continuous_fight_result_toc",m_test_tower_continuous_fight_result_toc);
         SocketCommand.protoMap[6303745] = "proto.line.m_family_boss_sweep_toc";
         ClassUtils.regClass("proto.line.m_family_boss_sweep_toc",m_family_boss_sweep_toc);
         SocketCommand.protoMap[3217540] = "proto.line.m_family_task_gift_toc";
         ClassUtils.regClass("proto.line.m_family_task_gift_toc",m_family_task_gift_toc);
         SocketCommand.protoMap[11031563] = "proto.line.m_mock_pvp_heat_toc";
         ClassUtils.regClass("proto.line.m_mock_pvp_heat_toc",m_mock_pvp_heat_toc);
         SocketCommand.protoMap[10834565] = "proto.line.m_fish_lineup_list_toc";
         ClassUtils.regClass("proto.line.m_fish_lineup_list_toc",m_fish_lineup_list_toc);
         SocketCommand.protoMap[1050624] = "proto.line.m_goods_list_toc";
         ClassUtils.regClass("proto.line.m_goods_list_toc",m_goods_list_toc);
         SocketCommand.protoMap[4793473] = "proto.line.m_level_gift_fetch_toc";
         ClassUtils.regClass("proto.line.m_level_gift_fetch_toc",m_level_gift_fetch_toc);
         SocketCommand.protoMap[10506252] = "proto.line.m_treasure_worker_active_toc";
         ClassUtils.regClass("proto.line.m_treasure_worker_active_toc",m_treasure_worker_active_toc);
         SocketCommand.protoMap[5318784] = "proto.line.m_rent_hero_list_toc";
         ClassUtils.regClass("proto.line.m_rent_hero_list_toc",m_rent_hero_list_toc);
         SocketCommand.protoMap[10834560] = "proto.line.m_fish_info_toc";
         ClassUtils.regClass("proto.line.m_fish_info_toc",m_fish_info_toc);
         SocketCommand.protoMap[10506253] = "proto.line.m_treasure_fetch_toc";
         ClassUtils.regClass("proto.line.m_treasure_fetch_toc",m_treasure_fetch_toc);
         SocketCommand.protoMap[10506254] = "proto.line.m_treasure_worker_info_toc";
         ClassUtils.regClass("proto.line.m_treasure_worker_info_toc",m_treasure_worker_info_toc);
         SocketCommand.protoMap[3742852] = "proto.line.m_fuli_yueka_fetch_toc";
         ClassUtils.regClass("proto.line.m_fuli_yueka_fetch_toc",m_fuli_yueka_fetch_toc);
         SocketCommand.protoMap[1050625] = "proto.line.m_goods_update_toc";
         ClassUtils.regClass("proto.line.m_goods_update_toc",m_goods_update_toc);
         SocketCommand.protoMap[8011012] = "proto.line.m_hzzd_fetch_toc";
         ClassUtils.regClass("proto.line.m_hzzd_fetch_toc",m_hzzd_fetch_toc);
         SocketCommand.protoMap[8536323] = "proto.line.m_cross_ladder_list_toc";
         ClassUtils.regClass("proto.line.m_cross_ladder_list_toc",m_cross_ladder_list_toc);
         SocketCommand.protoMap[5844096] = "proto.line.m_ares_palace_info_toc";
         ClassUtils.regClass("proto.line.m_ares_palace_info_toc",m_ares_palace_info_toc);
         SocketCommand.protoMap[9586945] = "proto.line.m_team_xswh_fight_result_toc";
         ClassUtils.regClass("proto.line.m_team_xswh_fight_result_toc",m_team_xswh_fight_result_toc);
         SocketCommand.protoMap[10112256] = "proto.line.m_boat_race_info_toc";
         ClassUtils.regClass("proto.line.m_boat_race_info_toc",m_boat_race_info_toc);
         SocketCommand.protoMap[9586948] = "proto.line.m_team_xswh_fetch_toc";
         ClassUtils.regClass("proto.line.m_team_xswh_fetch_toc",m_team_xswh_fetch_toc);
         SocketCommand.protoMap[4268160] = "proto.line.m_vip_info_toc";
         ClassUtils.regClass("proto.line.m_vip_info_toc",m_vip_info_toc);
         SocketCommand.protoMap[7420032] = "proto.line.m_pass_check_info_toc";
         ClassUtils.regClass("proto.line.m_pass_check_info_toc",m_pass_check_info_toc);
         SocketCommand.protoMap[4268161] = "proto.line.m_vip_buy_gift_toc";
         ClassUtils.regClass("proto.line.m_vip_buy_gift_toc",m_vip_buy_gift_toc);
         SocketCommand.protoMap[1116290] = "proto.line.m_role_base_reload_toc";
         ClassUtils.regClass("proto.line.m_role_base_reload_toc",m_role_base_reload_toc);
         SocketCommand.protoMap[3217539] = "proto.line.m_family_task_reward_toc";
         ClassUtils.regClass("proto.line.m_family_task_reward_toc",m_family_task_reward_toc);
         SocketCommand.protoMap[11622532] = "proto.line.m_wars_update_city_toc";
         ClassUtils.regClass("proto.line.m_wars_update_city_toc",m_wars_update_city_toc);
         SocketCommand.protoMap[11622533] = "proto.line.m_wars_city_info_toc";
         ClassUtils.regClass("proto.line.m_wars_city_info_toc",m_wars_city_info_toc);
         SocketCommand.protoMap[5318786] = "proto.line.m_rent_hero_look_toc";
         ClassUtils.regClass("proto.line.m_rent_hero_look_toc",m_rent_hero_look_toc);
         SocketCommand.protoMap[1116295] = "proto.line.m_role_setting_list_toc";
         ClassUtils.regClass("proto.line.m_role_setting_list_toc",m_role_setting_list_toc);
         SocketCommand.protoMap[11622536] = "proto.line.m_wars_team_op_toc";
         ClassUtils.regClass("proto.line.m_wars_team_op_toc",m_wars_team_op_toc);
         SocketCommand.protoMap[10571913] = "proto.line.m_lord_op_toc";
         ClassUtils.regClass("proto.line.m_lord_op_toc",m_lord_op_toc);
         SocketCommand.protoMap[10571914] = "proto.line.m_lord_equip_compose_toc";
         ClassUtils.regClass("proto.line.m_lord_equip_compose_toc",m_lord_equip_compose_toc);
         SocketCommand.protoMap[10571915] = "proto.line.m_lord_treasure_op_toc";
         ClassUtils.regClass("proto.line.m_lord_treasure_op_toc",m_lord_treasure_op_toc);
         SocketCommand.protoMap[11622540] = "proto.line.m_wars_act_info_toc";
         ClassUtils.regClass("proto.line.m_wars_act_info_toc",m_wars_act_info_toc);
         SocketCommand.protoMap[1116301] = "proto.line.m_role_setting_toc";
         ClassUtils.regClass("proto.line.m_role_setting_toc",m_role_setting_toc);
         SocketCommand.protoMap[10637568] = "proto.line.m_csclan_list_toc";
         ClassUtils.regClass("proto.line.m_csclan_list_toc",m_csclan_list_toc);
         SocketCommand.protoMap[1116303] = "proto.line.m_role_look_hero_toc";
         ClassUtils.regClass("proto.line.m_role_look_hero_toc",m_role_look_hero_toc);
         SocketCommand.protoMap[1116304] = "proto.line.m_role_look_hero_attr_source_toc";
         ClassUtils.regClass("proto.line.m_role_look_hero_attr_source_toc",m_role_look_hero_attr_source_toc);
         SocketCommand.protoMap[1116305] = "proto.line.m_role_setting2_toc";
         ClassUtils.regClass("proto.line.m_role_setting2_toc",m_role_setting2_toc);
         SocketCommand.protoMap[1116306] = "proto.line.m_role_look_sdk_profile_toc";
         ClassUtils.regClass("proto.line.m_role_look_sdk_profile_toc",m_role_look_sdk_profile_toc);
         SocketCommand.protoMap[1116307] = "proto.line.m_role_look_lord_toc";
         ClassUtils.regClass("proto.line.m_role_look_lord_toc",m_role_look_lord_toc);
         SocketCommand.protoMap[11622548] = "proto.line.m_wars_kill_info_toc";
         ClassUtils.regClass("proto.line.m_wars_kill_info_toc",m_wars_kill_info_toc);
         SocketCommand.protoMap[11622549] = "proto.line.m_wars_select_army_toc";
         ClassUtils.regClass("proto.line.m_wars_select_army_toc",m_wars_select_army_toc);
         SocketCommand.protoMap[11622551] = "proto.line.m_wars_info_get_msg_toc";
         ClassUtils.regClass("proto.line.m_wars_info_get_msg_toc",m_wars_info_get_msg_toc);
         SocketCommand.protoMap[11622552] = "proto.line.m_wars_info_nty_rank_data_toc";
         ClassUtils.regClass("proto.line.m_wars_info_nty_rank_data_toc",m_wars_info_nty_rank_data_toc);
         SocketCommand.protoMap[11622553] = "proto.line.m_wars_event_toc";
         ClassUtils.regClass("proto.line.m_wars_event_toc",m_wars_event_toc);
         SocketCommand.protoMap[11622554] = "proto.line.m_wars_make_path_toc";
         ClassUtils.regClass("proto.line.m_wars_make_path_toc",m_wars_make_path_toc);
         SocketCommand.protoMap[11622555] = "proto.line.m_wars_init_camp_toc";
         ClassUtils.regClass("proto.line.m_wars_init_camp_toc",m_wars_init_camp_toc);
         SocketCommand.protoMap[11622556] = "proto.line.m_wars_update_role_toc";
         ClassUtils.regClass("proto.line.m_wars_update_role_toc",m_wars_update_role_toc);
         SocketCommand.protoMap[9455618] = "proto.line.m_team_info_teams_toc";
         ClassUtils.regClass("proto.line.m_team_info_teams_toc",m_team_info_teams_toc);
         SocketCommand.protoMap[11622558] = "proto.line.m_wars_daily_report_toc";
         ClassUtils.regClass("proto.line.m_wars_daily_report_toc",m_wars_daily_report_toc);
         SocketCommand.protoMap[1575938] = "proto.line.m_welfare_sdk_share_info_toc";
         ClassUtils.regClass("proto.line.m_welfare_sdk_share_info_toc",m_welfare_sdk_share_info_toc);
         SocketCommand.protoMap[2101249] = "proto.line.m_daily_mission_gift_toc";
         ClassUtils.regClass("proto.line.m_daily_mission_gift_toc",m_daily_mission_gift_toc);
         SocketCommand.protoMap[2626560] = "proto.line.m_main_battle_info_toc";
         ClassUtils.regClass("proto.line.m_main_battle_info_toc",m_main_battle_info_toc);
         SocketCommand.protoMap[6369409] = "proto.line.m_red_cliff_sweep_toc";
         ClassUtils.regClass("proto.line.m_red_cliff_sweep_toc",m_red_cliff_sweep_toc);
         SocketCommand.protoMap[10112258] = "proto.line.m_boat_race_update_toc";
         ClassUtils.regClass("proto.line.m_boat_race_update_toc",m_boat_race_update_toc);
         SocketCommand.protoMap[10637569] = "proto.line.m_csclan_create_toc";
         ClassUtils.regClass("proto.line.m_csclan_create_toc",m_csclan_create_toc);
         SocketCommand.protoMap[1116288] = "proto.line.m_role_level_up_toc";
         ClassUtils.regClass("proto.line.m_role_level_up_toc",m_role_level_up_toc);
         SocketCommand.protoMap[2626561] = "proto.line.m_main_battle_fight_result_toc";
         ClassUtils.regClass("proto.line.m_main_battle_fight_result_toc",m_main_battle_fight_result_toc);
         SocketCommand.protoMap[3151872] = "proto.line.m_travel_info_toc";
         ClassUtils.regClass("proto.line.m_travel_info_toc",m_travel_info_toc);
         SocketCommand.protoMap[6894721] = "proto.line.m_progress_gift_buy_toc";
         ClassUtils.regClass("proto.line.m_progress_gift_buy_toc",m_progress_gift_buy_toc);
         SocketCommand.protoMap[6303746] = "proto.line.m_family_boss_update_toc";
         ClassUtils.regClass("proto.line.m_family_boss_update_toc",m_family_boss_update_toc);
         SocketCommand.protoMap[11622534] = "proto.line.m_wars_operate_toc";
         ClassUtils.regClass("proto.line.m_wars_operate_toc",m_wars_operate_toc);
         SocketCommand.protoMap[11688192] = "proto.line.m_itinerant_shop_info_toc";
         ClassUtils.regClass("proto.line.m_itinerant_shop_info_toc",m_itinerant_shop_info_toc);
         SocketCommand.protoMap[11753860] = "proto.line.m_hongbao_fetch_update_toc";
         ClassUtils.regClass("proto.line.m_hongbao_fetch_update_toc",m_hongbao_fetch_update_toc);
         SocketCommand.protoMap[8864643] = "proto.line.m_medal_update_toc";
         ClassUtils.regClass("proto.line.m_medal_update_toc",m_medal_update_toc);
         SocketCommand.protoMap[7288704] = "proto.line.m_csc_fmsolo_info_toc";
         ClassUtils.regClass("proto.line.m_csc_fmsolo_info_toc",m_csc_fmsolo_info_toc);
         SocketCommand.protoMap[3151873] = "proto.line.m_travel_refresh_toc";
         ClassUtils.regClass("proto.line.m_travel_refresh_toc",m_travel_refresh_toc);
         SocketCommand.protoMap[7748352] = "proto.line.m_hero_pass_info_toc";
         ClassUtils.regClass("proto.line.m_hero_pass_info_toc",m_hero_pass_info_toc);
         SocketCommand.protoMap[3677184] = "proto.line.m_eight_login_info_toc";
         ClassUtils.regClass("proto.line.m_eight_login_info_toc",m_eight_login_info_toc);
         SocketCommand.protoMap[7420033] = "proto.line.m_pass_check_fetch_toc";
         ClassUtils.regClass("proto.line.m_pass_check_fetch_toc",m_pass_check_fetch_toc);
         SocketCommand.protoMap[7945344] = "proto.line.m_maze_info_toc";
         ClassUtils.regClass("proto.line.m_maze_info_toc",m_maze_info_toc);
         SocketCommand.protoMap[2495236] = "proto.line.m_equip_auto_unload_toc";
         ClassUtils.regClass("proto.line.m_equip_auto_unload_toc",m_equip_auto_unload_toc);
         SocketCommand.protoMap[4727810] = "proto.line.m_mission_shop_buy_toc";
         ClassUtils.regClass("proto.line.m_mission_shop_buy_toc",m_mission_shop_buy_toc);
         SocketCommand.protoMap[9586944] = "proto.line.m_team_xswh_info_toc";
         ClassUtils.regClass("proto.line.m_team_xswh_info_toc",m_team_xswh_info_toc);
         SocketCommand.protoMap[1181953] = "proto.line.m_simp_mission_update_toc";
         ClassUtils.regClass("proto.line.m_simp_mission_update_toc",m_simp_mission_update_toc);
         SocketCommand.protoMap[10637570] = "proto.line.m_csclan_self_toc";
         ClassUtils.regClass("proto.line.m_csclan_self_toc",m_csclan_self_toc);
         SocketCommand.protoMap[10637571] = "proto.line.m_csclan_audit_limit_toc";
         ClassUtils.regClass("proto.line.m_csclan_audit_limit_toc",m_csclan_audit_limit_toc);
         SocketCommand.protoMap[8536324] = "proto.line.m_cross_ladder_logs_toc";
         ClassUtils.regClass("proto.line.m_cross_ladder_logs_toc",m_cross_ladder_logs_toc);
         SocketCommand.protoMap[10637573] = "proto.line.m_csclan_set_owner_toc";
         ClassUtils.regClass("proto.line.m_csclan_set_owner_toc",m_csclan_set_owner_toc);
         SocketCommand.protoMap[10637574] = "proto.line.m_csclan_update_notice_toc";
         ClassUtils.regClass("proto.line.m_csclan_update_notice_toc",m_csclan_update_notice_toc);
         SocketCommand.protoMap[10637575] = "proto.line.m_csclan_operate_toc";
         ClassUtils.regClass("proto.line.m_csclan_operate_toc",m_csclan_operate_toc);
         SocketCommand.protoMap[10637576] = "proto.line.m_csclan_members_toc";
         ClassUtils.regClass("proto.line.m_csclan_members_toc",m_csclan_members_toc);
         SocketCommand.protoMap[10637577] = "proto.line.m_csclan_apply_list_toc";
         ClassUtils.regClass("proto.line.m_csclan_apply_list_toc",m_csclan_apply_list_toc);
         SocketCommand.protoMap[10637578] = "proto.line.m_csclan_logs_toc";
         ClassUtils.regClass("proto.line.m_csclan_logs_toc",m_csclan_logs_toc);
         SocketCommand.protoMap[10637579] = "proto.line.m_csclan_rename_toc";
         ClassUtils.regClass("proto.line.m_csclan_rename_toc",m_csclan_rename_toc);
         SocketCommand.protoMap[10637580] = "proto.line.m_csclan_base_toc";
         ClassUtils.regClass("proto.line.m_csclan_base_toc",m_csclan_base_toc);
         SocketCommand.protoMap[10637581] = "proto.line.m_csclan_scene_toc";
         ClassUtils.regClass("proto.line.m_csclan_scene_toc",m_csclan_scene_toc);
         SocketCommand.protoMap[5384448] = "proto.line.m_quick_shop_info_toc";
         ClassUtils.regClass("proto.line.m_quick_shop_info_toc",m_quick_shop_info_toc);
         SocketCommand.protoMap[4924803] = "proto.line.m_daily_gift_tehui_info_toc";
         ClassUtils.regClass("proto.line.m_daily_gift_tehui_info_toc",m_daily_gift_tehui_info_toc);
         SocketCommand.protoMap[11031555] = "proto.line.m_mock_pvp_logs_toc";
         ClassUtils.regClass("proto.line.m_mock_pvp_logs_toc",m_mock_pvp_logs_toc);
         SocketCommand.protoMap[1247620] = "proto.line.m_family_apply_list_toc";
         ClassUtils.regClass("proto.line.m_family_apply_list_toc",m_family_apply_list_toc);
         SocketCommand.protoMap[8339330] = "proto.line.m_multi_lineup_get_power_toc";
         ClassUtils.regClass("proto.line.m_multi_lineup_get_power_toc",m_multi_lineup_get_power_toc);
         SocketCommand.protoMap[4924805] = "proto.line.m_daily_gift_new_discount_info_toc";
         ClassUtils.regClass("proto.line.m_daily_gift_new_discount_info_toc",m_daily_gift_new_discount_info_toc);
         SocketCommand.protoMap[7420035] = "proto.line.m_pass_check_update_toc";
         ClassUtils.regClass("proto.line.m_pass_check_update_toc",m_pass_check_update_toc);
         SocketCommand.protoMap[1247622] = "proto.line.m_family_join_toc";
         ClassUtils.regClass("proto.line.m_family_join_toc",m_family_join_toc);
         SocketCommand.protoMap[7945346] = "proto.line.m_maze_start_toc";
         ClassUtils.regClass("proto.line.m_maze_start_toc",m_maze_start_toc);
         SocketCommand.protoMap[2692226] = "proto.line.m_fight_set_speed_toc";
         ClassUtils.regClass("proto.line.m_fight_set_speed_toc",m_fight_set_speed_toc);
         SocketCommand.protoMap[8995968] = "proto.line.m_small_game_info_toc";
         ClassUtils.regClass("proto.line.m_small_game_info_toc",m_small_game_info_toc);
         SocketCommand.protoMap[9652615] = "proto.line.m_master_card_notice_toc";
         ClassUtils.regClass("proto.line.m_master_card_notice_toc",m_master_card_notice_toc);
         SocketCommand.protoMap[1247624] = "proto.line.m_family_reply_join_toc";
         ClassUtils.regClass("proto.line.m_family_reply_join_toc",m_family_reply_join_toc);
         SocketCommand.protoMap[10571906] = "proto.line.m_lord_skill_set_toc";
         ClassUtils.regClass("proto.line.m_lord_skill_set_toc",m_lord_skill_set_toc);
         SocketCommand.protoMap[9652617] = "proto.line.m_master_card_power_toc";
         ClassUtils.regClass("proto.line.m_master_card_power_toc",m_master_card_power_toc);
         SocketCommand.protoMap[9652618] = "proto.line.m_master_card_spe_effect_toc";
         ClassUtils.regClass("proto.line.m_master_card_spe_effect_toc",m_master_card_spe_effect_toc);
         SocketCommand.protoMap[10112263] = "proto.line.m_boat_race_rank_toc";
         ClassUtils.regClass("proto.line.m_boat_race_rank_toc",m_boat_race_rank_toc);
         SocketCommand.protoMap[7551364] = "proto.line.m_lcqs_fight_result_toc";
         ClassUtils.regClass("proto.line.m_lcqs_fight_result_toc",m_lcqs_fight_result_toc);
         SocketCommand.protoMap[984960] = "proto.line.m_bag_goods_list_toc";
         ClassUtils.regClass("proto.line.m_bag_goods_list_toc",m_bag_goods_list_toc);
         SocketCommand.protoMap[1247627] = "proto.line.m_family_set_owner_toc";
         ClassUtils.regClass("proto.line.m_family_set_owner_toc",m_family_set_owner_toc);
         SocketCommand.protoMap[4727809] = "proto.line.m_mission_shop_update_toc";
         ClassUtils.regClass("proto.line.m_mission_shop_update_toc",m_mission_shop_update_toc);
         SocketCommand.protoMap[5253120] = "proto.line.m_world_level_info_toc";
         ClassUtils.regClass("proto.line.m_world_level_info_toc",m_world_level_info_toc);
         SocketCommand.protoMap[9652620] = "proto.line.m_master_card_decoration_op_toc";
         ClassUtils.regClass("proto.line.m_master_card_decoration_op_toc",m_master_card_decoration_op_toc);
         SocketCommand.protoMap[9521280] = "proto.line.m_battle_trial_info_toc";
         ClassUtils.regClass("proto.line.m_battle_trial_info_toc",m_battle_trial_info_toc);
         SocketCommand.protoMap[1116289] = "proto.line.m_role_attr_change_toc";
         ClassUtils.regClass("proto.line.m_role_attr_change_toc",m_role_attr_change_toc);
         SocketCommand.protoMap[10834562] = "proto.line.m_fish_lottery_toc";
         ClassUtils.regClass("proto.line.m_fish_lottery_toc",m_fish_lottery_toc);
         SocketCommand.protoMap[10112257] = "proto.line.m_boat_race_bonus_toc";
         ClassUtils.regClass("proto.line.m_boat_race_bonus_toc",m_boat_race_bonus_toc);
         SocketCommand.protoMap[2626566] = "proto.line.m_main_battle_mission_status_toc";
         ClassUtils.regClass("proto.line.m_main_battle_mission_status_toc",m_main_battle_mission_status_toc);
         SocketCommand.protoMap[10112264] = "proto.line.m_boat_race_logs_toc";
         ClassUtils.regClass("proto.line.m_boat_race_logs_toc",m_boat_race_logs_toc);
         SocketCommand.protoMap[7945348] = "proto.line.m_maze_fetch_toc";
         ClassUtils.regClass("proto.line.m_maze_fetch_toc",m_maze_fetch_toc);
         SocketCommand.protoMap[5778432] = "proto.line.m_god_trial_info_toc";
         ClassUtils.regClass("proto.line.m_god_trial_info_toc",m_god_trial_info_toc);
         SocketCommand.protoMap[9521281] = "proto.line.m_battle_trial_fetch_info_toc";
         ClassUtils.regClass("proto.line.m_battle_trial_fetch_info_toc",m_battle_trial_fetch_info_toc);
         SocketCommand.protoMap[10046592] = "proto.line.m_hero_zhouyin_info_toc";
         ClassUtils.regClass("proto.line.m_hero_zhouyin_info_toc",m_hero_zhouyin_info_toc);
         SocketCommand.protoMap[2626567] = "proto.line.m_main_battle_box_info_toc";
         ClassUtils.regClass("proto.line.m_main_battle_box_info_toc",m_main_battle_box_info_toc);
         SocketCommand.protoMap[7748353] = "proto.line.m_hero_pass_fetch_toc";
         ClassUtils.regClass("proto.line.m_hero_pass_fetch_toc",m_hero_pass_fetch_toc);
         SocketCommand.protoMap[10112265] = "proto.line.m_boat_race_result_toc";
         ClassUtils.regClass("proto.line.m_boat_race_result_toc",m_boat_race_result_toc);
         SocketCommand.protoMap[4399488] = "proto.line.m_hero_comment_info_toc";
         ClassUtils.regClass("proto.line.m_hero_comment_info_toc",m_hero_comment_info_toc);
         SocketCommand.protoMap[7551361] = "proto.line.m_lcqs_chapter_fetch_toc";
         ClassUtils.regClass("proto.line.m_lcqs_chapter_fetch_toc",m_lcqs_chapter_fetch_toc);
         SocketCommand.protoMap[6500738] = "proto.line.m_guandu_update_event_toc";
         ClassUtils.regClass("proto.line.m_guandu_update_event_toc",m_guandu_update_event_toc);
         SocketCommand.protoMap[11753859] = "proto.line.m_hongbao_info_update_toc";
         ClassUtils.regClass("proto.line.m_hongbao_info_update_toc",m_hongbao_info_update_toc);
         SocketCommand.protoMap[6500740] = "proto.line.m_guandu_next_toc";
         ClassUtils.regClass("proto.line.m_guandu_next_toc",m_guandu_next_toc);
         SocketCommand.protoMap[9652613] = "proto.line.m_master_card_replace_toc";
         ClassUtils.regClass("proto.line.m_master_card_replace_toc",m_master_card_replace_toc);
         SocketCommand.protoMap[9652614] = "proto.line.m_master_card_up_official_toc";
         ClassUtils.regClass("proto.line.m_master_card_up_official_toc",m_master_card_up_official_toc);
         SocketCommand.protoMap[1247623] = "proto.line.m_family_cancel_join_toc";
         ClassUtils.regClass("proto.line.m_family_cancel_join_toc",m_family_cancel_join_toc);
         SocketCommand.protoMap[9652616] = "proto.line.m_master_card_preview_attr_toc";
         ClassUtils.regClass("proto.line.m_master_card_preview_attr_toc",m_master_card_preview_attr_toc);
         SocketCommand.protoMap[1378950] = "proto.line.m_change_day_toc";
         ClassUtils.regClass("proto.line.m_change_day_toc",m_change_day_toc);
         SocketCommand.protoMap[8076672] = "proto.line.m_casting_soul_op_toc";
         ClassUtils.regClass("proto.line.m_casting_soul_op_toc",m_casting_soul_op_toc);
         SocketCommand.protoMap[9652619] = "proto.line.m_master_card_decoration_info_toc";
         ClassUtils.regClass("proto.line.m_master_card_decoration_info_toc",m_master_card_decoration_info_toc);
         SocketCommand.protoMap[1247628] = "proto.line.m_family_update_notice_toc";
         ClassUtils.regClass("proto.line.m_family_update_notice_toc",m_family_update_notice_toc);
         SocketCommand.protoMap[1247629] = "proto.line.m_family_operate_toc";
         ClassUtils.regClass("proto.line.m_family_operate_toc",m_family_operate_toc);
         SocketCommand.protoMap[1247630] = "proto.line.m_family_info_change_toc";
         ClassUtils.regClass("proto.line.m_family_info_change_toc",m_family_info_change_toc);
         SocketCommand.protoMap[1247631] = "proto.line.m_family_logs_toc";
         ClassUtils.regClass("proto.line.m_family_logs_toc",m_family_logs_toc);
         SocketCommand.protoMap[1247632] = "proto.line.m_family_update_member_toc";
         ClassUtils.regClass("proto.line.m_family_update_member_toc",m_family_update_member_toc);
         SocketCommand.protoMap[1247633] = "proto.line.m_family_rename_toc";
         ClassUtils.regClass("proto.line.m_family_rename_toc",m_family_rename_toc);
         SocketCommand.protoMap[1247634] = "proto.line.m_family_info_toc";
         ClassUtils.regClass("proto.line.m_family_info_toc",m_family_info_toc);
         SocketCommand.protoMap[1247635] = "proto.line.m_family_attr_tip_toc";
         ClassUtils.regClass("proto.line.m_family_attr_tip_toc",m_family_attr_tip_toc);
         SocketCommand.protoMap[1247636] = "proto.line.m_family_flagname_toc";
         ClassUtils.regClass("proto.line.m_family_flagname_toc",m_family_flagname_toc);
         SocketCommand.protoMap[1247637] = "proto.line.m_family_transfer_count_toc";
         ClassUtils.regClass("proto.line.m_family_transfer_count_toc",m_family_transfer_count_toc);
         SocketCommand.protoMap[1247638] = "proto.line.m_family_transfer_join_toc";
         ClassUtils.regClass("proto.line.m_family_transfer_join_toc",m_family_transfer_join_toc);
         SocketCommand.protoMap[1247639] = "proto.line.m_family_hot_toc";
         ClassUtils.regClass("proto.line.m_family_hot_toc",m_family_hot_toc);
         SocketCommand.protoMap[1247640] = "proto.line.m_family_hot_op_toc";
         ClassUtils.regClass("proto.line.m_family_hot_op_toc",m_family_hot_op_toc);
         SocketCommand.protoMap[4136832] = "proto.line.m_time_activity_info_toc";
         ClassUtils.regClass("proto.line.m_time_activity_info_toc",m_time_activity_info_toc);
         SocketCommand.protoMap[2035585] = "proto.line.m_hint_show_tip_toc";
         ClassUtils.regClass("proto.line.m_hint_show_tip_toc",m_hint_show_tip_toc);
         SocketCommand.protoMap[5778434] = "proto.line.m_god_trial_buff_toc";
         ClassUtils.regClass("proto.line.m_god_trial_buff_toc",m_god_trial_buff_toc);
         SocketCommand.protoMap[9521283] = "proto.line.m_battle_trial_daily_end_info_toc";
         ClassUtils.regClass("proto.line.m_battle_trial_daily_end_info_toc",m_battle_trial_daily_end_info_toc);
         SocketCommand.protoMap[6829056] = "proto.line.m_monster_group_power_toc";
         ClassUtils.regClass("proto.line.m_monster_group_power_toc",m_monster_group_power_toc);
         SocketCommand.protoMap[4268162] = "proto.line.m_vip_free_gift_info_toc";
         ClassUtils.regClass("proto.line.m_vip_free_gift_info_toc",m_vip_free_gift_info_toc);
         SocketCommand.protoMap[10571905] = "proto.line.m_lord_info_toc";
         ClassUtils.regClass("proto.line.m_lord_info_toc",m_lord_info_toc);
         SocketCommand.protoMap[10112267] = "proto.line.m_boat_race_alloc_items_toc";
         ClassUtils.regClass("proto.line.m_boat_race_alloc_items_toc",m_boat_race_alloc_items_toc);
         SocketCommand.protoMap[8011014] = "proto.line.m_hzzd_info_history_toc";
         ClassUtils.regClass("proto.line.m_hzzd_info_history_toc",m_hzzd_info_history_toc);
         SocketCommand.protoMap[7945351] = "proto.line.m_maze_hero_toc";
         ClassUtils.regClass("proto.line.m_maze_hero_toc",m_maze_hero_toc);
         SocketCommand.protoMap[2560897] = "proto.line.m_god_equip_compose_toc";
         ClassUtils.regClass("proto.line.m_god_equip_compose_toc",m_god_equip_compose_toc);
         SocketCommand.protoMap[9521284] = "proto.line.m_battle_trial_offline_info_toc";
         ClassUtils.regClass("proto.line.m_battle_trial_offline_info_toc",m_battle_trial_offline_info_toc);
         SocketCommand.protoMap[6829057] = "proto.line.m_monster_group_power_list_toc";
         ClassUtils.regClass("proto.line.m_monster_group_power_list_toc",m_monster_group_power_list_toc);
         SocketCommand.protoMap[7354368] = "proto.line.m_world_boss_info_toc";
         ClassUtils.regClass("proto.line.m_world_boss_info_toc",m_world_boss_info_toc);
         SocketCommand.protoMap[8404994] = "proto.line.m_peak_info_member_toc";
         ClassUtils.regClass("proto.line.m_peak_info_member_toc",m_peak_info_member_toc);
         SocketCommand.protoMap[10571907] = "proto.line.m_lord_set_lineup_toc";
         ClassUtils.regClass("proto.line.m_lord_set_lineup_toc",m_lord_set_lineup_toc);
         SocketCommand.protoMap[1904258] = "proto.line.m_gmcmd_battle_edit_toc";
         ClassUtils.regClass("proto.line.m_gmcmd_battle_edit_toc",m_gmcmd_battle_edit_toc);
         SocketCommand.protoMap[590726] = "proto.line.m_chat_get_goods_toc";
         ClassUtils.regClass("proto.line.m_chat_get_goods_toc",m_chat_get_goods_toc);
         SocketCommand.protoMap[1510276] = "proto.line.m_ranking_history_list_toc";
         ClassUtils.regClass("proto.line.m_ranking_history_list_toc",m_ranking_history_list_toc);
         SocketCommand.protoMap[10243588] = "proto.line.m_story_maze_fetch_toc";
         ClassUtils.regClass("proto.line.m_story_maze_fetch_toc",m_story_maze_fetch_toc);
         SocketCommand.protoMap[2560898] = "proto.line.m_god_equip_select_toc";
         ClassUtils.regClass("proto.line.m_god_equip_select_toc",m_god_equip_select_toc);
         SocketCommand.protoMap[6303747] = "proto.line.m_family_boss_look_toc";
         ClassUtils.regClass("proto.line.m_family_boss_look_toc",m_family_boss_look_toc);
         SocketCommand.protoMap[7354369] = "proto.line.m_world_boss_buy_times_toc";
         ClassUtils.regClass("proto.line.m_world_boss_buy_times_toc",m_world_boss_buy_times_toc);
         SocketCommand.protoMap[7879680] = "proto.line.m_random_pvp_op_toc";
         ClassUtils.regClass("proto.line.m_random_pvp_op_toc",m_random_pvp_op_toc);
         SocketCommand.protoMap[11622529] = "proto.line.m_wars_init_toc";
         ClassUtils.regClass("proto.line.m_wars_init_toc",m_wars_init_toc);
         SocketCommand.protoMap[2560896] = "proto.line.m_god_equip_recast_toc";
         ClassUtils.regClass("proto.line.m_god_equip_recast_toc",m_god_equip_recast_toc);
         SocketCommand.protoMap[5975429] = "proto.line.m_pass_behead_battle_toc";
         ClassUtils.regClass("proto.line.m_pass_behead_battle_toc",m_pass_behead_battle_toc);
         SocketCommand.protoMap[10506249] = "proto.line.m_treasure_pull_list_toc";
         ClassUtils.regClass("proto.line.m_treasure_pull_list_toc",m_treasure_pull_list_toc);
         SocketCommand.protoMap[11359875] = "proto.line.m_large_peak_info_look_member_toc";
         ClassUtils.regClass("proto.line.m_large_peak_info_look_member_toc",m_large_peak_info_look_member_toc);
         SocketCommand.protoMap[9521286] = "proto.line.m_battle_trial_battle_up_toc";
         ClassUtils.regClass("proto.line.m_battle_trial_battle_up_toc",m_battle_trial_battle_up_toc);
         SocketCommand.protoMap[1313280] = "proto.line.m_broadcast_normal_toc";
         ClassUtils.regClass("proto.line.m_broadcast_normal_toc",m_broadcast_normal_toc);
         SocketCommand.protoMap[9849600] = "proto.line.m_stage_breed_info_toc";
         ClassUtils.regClass("proto.line.m_stage_breed_info_toc",m_stage_breed_info_toc);
         SocketCommand.protoMap[10768898] = "proto.line.m_boat_peak_member_toc";
         ClassUtils.regClass("proto.line.m_boat_peak_member_toc",m_boat_peak_member_toc);
         SocketCommand.protoMap[2363907] = "proto.line.m_shop_update_toc";
         ClassUtils.regClass("proto.line.m_shop_update_toc",m_shop_update_toc);
         SocketCommand.protoMap[4465156] = "proto.line.m_family_hongbao_fetch_update_toc";
         ClassUtils.regClass("proto.line.m_family_hongbao_fetch_update_toc",m_family_hongbao_fetch_update_toc);
         SocketCommand.protoMap[4465157] = "proto.line.m_family_hongbao_mission_info_toc";
         ClassUtils.regClass("proto.line.m_family_hongbao_mission_info_toc",m_family_hongbao_mission_info_toc);
         SocketCommand.protoMap[8404992] = "proto.line.m_peak_info_toc";
         ClassUtils.regClass("proto.line.m_peak_info_toc",m_peak_info_toc);
         SocketCommand.protoMap[1444609] = "proto.line.m_activity_update_fetch_toc";
         ClassUtils.regClass("proto.line.m_activity_update_fetch_toc",m_activity_update_fetch_toc);
         SocketCommand.protoMap[7617032] = "proto.line.m_theme_activity_up_star_reward_info_toc";
         ClassUtils.regClass("proto.line.m_theme_activity_up_star_reward_info_toc",m_theme_activity_up_star_reward_info_toc);
         SocketCommand.protoMap[10768906] = "proto.line.m_boat_peak_look_pre_score_toc";
         ClassUtils.regClass("proto.line.m_boat_peak_look_pre_score_toc",m_boat_peak_look_pre_score_toc);
         SocketCommand.protoMap[10768907] = "proto.line.m_boat_peak_join_members_toc";
         ClassUtils.regClass("proto.line.m_boat_peak_join_members_toc",m_boat_peak_join_members_toc);
         SocketCommand.protoMap[10768908] = "proto.line.m_boat_peak_fetch_toc";
         ClassUtils.regClass("proto.line.m_boat_peak_fetch_toc",m_boat_peak_fetch_toc);
         SocketCommand.protoMap[3020546] = "proto.line.m_profile_update_toc";
         ClassUtils.regClass("proto.line.m_profile_update_toc",m_profile_update_toc);
         SocketCommand.protoMap[9455620] = "proto.line.m_team_info_my_team_update_toc";
         ClassUtils.regClass("proto.line.m_team_info_my_team_update_toc",m_team_info_my_team_update_toc);
         SocketCommand.protoMap[8273667] = "proto.line.m_hero_resonate_dhyana_op_toc";
         ClassUtils.regClass("proto.line.m_hero_resonate_dhyana_op_toc",m_hero_resonate_dhyana_op_toc);
         SocketCommand.protoMap[8273668] = "proto.line.m_hero_resonate_equip_op_toc";
         ClassUtils.regClass("proto.line.m_hero_resonate_equip_op_toc",m_hero_resonate_equip_op_toc);
         SocketCommand.protoMap[2560900] = "proto.line.m_god_equip_enchant_toc";
         ClassUtils.regClass("proto.line.m_god_equip_enchant_toc",m_god_equip_enchant_toc);
         SocketCommand.protoMap[9521287] = "proto.line.m_battle_trial_battle_reset_toc";
         ClassUtils.regClass("proto.line.m_battle_trial_battle_reset_toc",m_battle_trial_battle_reset_toc);
         SocketCommand.protoMap[2495237] = "proto.line.m_equip_reinforce_toc";
         ClassUtils.regClass("proto.line.m_equip_reinforce_toc",m_equip_reinforce_toc);
         SocketCommand.protoMap[7879682] = "proto.line.m_random_pvp_match_toc";
         ClassUtils.regClass("proto.line.m_random_pvp_match_toc",m_random_pvp_match_toc);
         SocketCommand.protoMap[8404993] = "proto.line.m_peak_info_opp_toc";
         ClassUtils.regClass("proto.line.m_peak_info_opp_toc",m_peak_info_opp_toc);
         SocketCommand.protoMap[8930304] = "proto.line.m_player_strategy_info_toc";
         ClassUtils.regClass("proto.line.m_player_strategy_info_toc",m_player_strategy_info_toc);
         SocketCommand.protoMap[2495239] = "proto.line.m_equip_recycle_toc";
         ClassUtils.regClass("proto.line.m_equip_recycle_toc",m_equip_recycle_toc);
         SocketCommand.protoMap[2626573] = "proto.line.m_main_battle_box_upgrade_toc";
         ClassUtils.regClass("proto.line.m_main_battle_box_upgrade_toc",m_main_battle_box_upgrade_toc);
         SocketCommand.protoMap[6172418] = "proto.line.m_war_flag_op_toc";
         ClassUtils.regClass("proto.line.m_war_flag_op_toc",m_war_flag_op_toc);
         SocketCommand.protoMap[2495240] = "proto.line.m_equip_decompose_toc";
         ClassUtils.regClass("proto.line.m_equip_decompose_toc",m_equip_decompose_toc);
         SocketCommand.protoMap[2495241] = "proto.line.m_equip_compose_toc";
         ClassUtils.regClass("proto.line.m_equip_compose_toc",m_equip_compose_toc);
         SocketCommand.protoMap[6303750] = "proto.line.m_family_boss_attr_toc";
         ClassUtils.regClass("proto.line.m_family_boss_attr_toc",m_family_boss_attr_toc);
         SocketCommand.protoMap[3611523] = "proto.line.m_arena_list_toc";
         ClassUtils.regClass("proto.line.m_arena_list_toc",m_arena_list_toc);
         SocketCommand.protoMap[1444618] = "proto.line.m_activity_limit_sign_info_toc";
         ClassUtils.regClass("proto.line.m_activity_limit_sign_info_toc",m_activity_limit_sign_info_toc);
         SocketCommand.protoMap[4136834] = "proto.line.m_time_activity_update_toc";
         ClassUtils.regClass("proto.line.m_time_activity_update_toc",m_time_activity_update_toc);
         SocketCommand.protoMap[4662145] = "proto.line.m_squad_lineup_set_toc";
         ClassUtils.regClass("proto.line.m_squad_lineup_set_toc",m_squad_lineup_set_toc);
         SocketCommand.protoMap[5187456] = "proto.line.m_bingfu_info_toc";
         ClassUtils.regClass("proto.line.m_bingfu_info_toc",m_bingfu_info_toc);
         SocketCommand.protoMap[1444619] = "proto.line.m_activity_yueka_info_toc";
         ClassUtils.regClass("proto.line.m_activity_yueka_info_toc",m_activity_yueka_info_toc);
         SocketCommand.protoMap[4202496] = "proto.line.m_page_list_toc";
         ClassUtils.regClass("proto.line.m_page_list_toc",m_page_list_toc);
         SocketCommand.protoMap[2495245] = "proto.line.m_equip_info_update_toc";
         ClassUtils.regClass("proto.line.m_equip_info_update_toc",m_equip_info_update_toc);
         SocketCommand.protoMap[6303751] = "proto.line.m_family_boss_gather_toc";
         ClassUtils.regClass("proto.line.m_family_boss_gather_toc",m_family_boss_gather_toc);
         SocketCommand.protoMap[2692227] = "proto.line.m_fight_simp_result_toc";
         ClassUtils.regClass("proto.line.m_fight_simp_result_toc",m_fight_simp_result_toc);
         SocketCommand.protoMap[10571911] = "proto.line.m_lord_star_toc";
         ClassUtils.regClass("proto.line.m_lord_star_toc",m_lord_star_toc);
         SocketCommand.protoMap[1444608] = "proto.line.m_activity_list_toc";
         ClassUtils.regClass("proto.line.m_activity_list_toc",m_activity_list_toc);
         SocketCommand.protoMap[9455621] = "proto.line.m_team_operate_toc";
         ClassUtils.regClass("proto.line.m_team_operate_toc",m_team_operate_toc);
         SocketCommand.protoMap[5187457] = "proto.line.m_bingfu_breed_toc";
         ClassUtils.regClass("proto.line.m_bingfu_breed_toc",m_bingfu_breed_toc);
         SocketCommand.protoMap[5712768] = "proto.line.m_daily_ad_code_info_toc";
         ClassUtils.regClass("proto.line.m_daily_ad_code_info_toc",m_daily_ad_code_info_toc);
         SocketCommand.protoMap[7879684] = "proto.line.m_random_pvp_palace_toc";
         ClassUtils.regClass("proto.line.m_random_pvp_palace_toc",m_random_pvp_palace_toc);
         SocketCommand.protoMap[3611525] = "proto.line.m_arena_fight_result_toc";
         ClassUtils.regClass("proto.line.m_arena_fight_result_toc",m_arena_fight_result_toc);
         SocketCommand.protoMap[919298] = "proto.line.m_item_show_gains_toc";
         ClassUtils.regClass("proto.line.m_item_show_gains_toc",m_item_show_gains_toc);
         SocketCommand.protoMap[10112259] = "proto.line.m_boat_race_boats_toc";
         ClassUtils.regClass("proto.line.m_boat_race_boats_toc",m_boat_race_boats_toc);
         SocketCommand.protoMap[1969920] = "proto.line.m_complaints_info_toc";
         ClassUtils.regClass("proto.line.m_complaints_info_toc",m_complaints_info_toc);
         SocketCommand.protoMap[6632064] = "proto.line.m_time_achievement_info_toc";
         ClassUtils.regClass("proto.line.m_time_achievement_info_toc",m_time_achievement_info_toc);
         SocketCommand.protoMap[4530817] = "proto.line.m_fuli_token_buy_toc";
         ClassUtils.regClass("proto.line.m_fuli_token_buy_toc",m_fuli_token_buy_toc);
         SocketCommand.protoMap[3480194] = "proto.line.m_hunt_gift_toc";
         ClassUtils.regClass("proto.line.m_hunt_gift_toc",m_hunt_gift_toc);
         SocketCommand.protoMap[7682691] = "proto.line.m_star_plan_update_toc";
         ClassUtils.regClass("proto.line.m_star_plan_update_toc",m_star_plan_update_toc);
         SocketCommand.protoMap[2429572] = "proto.line.m_hero_lock_toc";
         ClassUtils.regClass("proto.line.m_hero_lock_toc",m_hero_lock_toc);
         SocketCommand.protoMap[1378949] = "proto.line.m_system_setting_toc";
         ClassUtils.regClass("proto.line.m_system_setting_toc",m_system_setting_toc);
         SocketCommand.protoMap[2429574] = "proto.line.m_hero_recycle_toc";
         ClassUtils.regClass("proto.line.m_hero_recycle_toc",m_hero_recycle_toc);
         SocketCommand.protoMap[1378951] = "proto.line.m_system_time_toc";
         ClassUtils.regClass("proto.line.m_system_time_toc",m_system_time_toc);
         SocketCommand.protoMap[2429576] = "proto.line.m_hero_down_lineup_toc";
         ClassUtils.regClass("proto.line.m_hero_down_lineup_toc",m_hero_down_lineup_toc);
         SocketCommand.protoMap[10834569] = "proto.line.m_fish_notice_toc";
         ClassUtils.regClass("proto.line.m_fish_notice_toc",m_fish_notice_toc);
         SocketCommand.protoMap[10834570] = "proto.line.m_fish_preview_attr_toc";
         ClassUtils.regClass("proto.line.m_fish_preview_attr_toc",m_fish_preview_attr_toc);
         SocketCommand.protoMap[10834571] = "proto.line.m_fish_power_toc";
         ClassUtils.regClass("proto.line.m_fish_power_toc",m_fish_power_toc);
         SocketCommand.protoMap[10834572] = "proto.line.m_fish_spe_effect_toc";
         ClassUtils.regClass("proto.line.m_fish_spe_effect_toc",m_fish_spe_effect_toc);
         SocketCommand.protoMap[10834573] = "proto.line.m_fish_logs_toc";
         ClassUtils.regClass("proto.line.m_fish_logs_toc",m_fish_logs_toc);
         SocketCommand.protoMap[10834574] = "proto.line.m_fish_handbook_info_toc";
         ClassUtils.regClass("proto.line.m_fish_handbook_info_toc",m_fish_handbook_info_toc);
         SocketCommand.protoMap[2429583] = "proto.line.m_hero_recycle_change_toc";
         ClassUtils.regClass("proto.line.m_hero_recycle_change_toc",m_hero_recycle_change_toc);
         SocketCommand.protoMap[2429584] = "proto.line.m_hero_my_rank_toc";
         ClassUtils.regClass("proto.line.m_hero_my_rank_toc",m_hero_my_rank_toc);
         SocketCommand.protoMap[2495256] = "proto.line.m_wing_info_toc";
         ClassUtils.regClass("proto.line.m_wing_info_toc",m_wing_info_toc);
         SocketCommand.protoMap[2429586] = "proto.line.m_hero_inherit_update_toc";
         ClassUtils.regClass("proto.line.m_hero_inherit_update_toc",m_hero_inherit_update_toc);
         SocketCommand.protoMap[2429587] = "proto.line.m_hero_recycle_down_toc";
         ClassUtils.regClass("proto.line.m_hero_recycle_down_toc",m_hero_recycle_down_toc);
         SocketCommand.protoMap[2429588] = "proto.line.m_hero_recycle_times_toc";
         ClassUtils.regClass("proto.line.m_hero_recycle_times_toc",m_hero_recycle_times_toc);
         SocketCommand.protoMap[2429589] = "proto.line.m_hero_act_fourteen_toc";
         ClassUtils.regClass("proto.line.m_hero_act_fourteen_toc",m_hero_act_fourteen_toc);
         SocketCommand.protoMap[2429590] = "proto.line.m_hero_evolve_op_toc";
         ClassUtils.regClass("proto.line.m_hero_evolve_op_toc",m_hero_evolve_op_toc);
         SocketCommand.protoMap[2429591] = "proto.line.m_hero_evolve_info_toc";
         ClassUtils.regClass("proto.line.m_hero_evolve_info_toc",m_hero_evolve_info_toc);
         SocketCommand.protoMap[919299] = "proto.line.m_item_sale_toc";
         ClassUtils.regClass("proto.line.m_item_sale_toc",m_item_sale_toc);
         SocketCommand.protoMap[7879686] = "proto.line.m_random_pvp_update_toc";
         ClassUtils.regClass("proto.line.m_random_pvp_update_toc",m_random_pvp_update_toc);
         SocketCommand.protoMap[5187459] = "proto.line.m_bingfu_shift_toc";
         ClassUtils.regClass("proto.line.m_bingfu_shift_toc",m_bingfu_shift_toc);
         SocketCommand.protoMap[2495232] = "proto.line.m_equip_info_toc";
         ClassUtils.regClass("proto.line.m_equip_info_toc",m_equip_info_toc);
         SocketCommand.protoMap[9455619] = "proto.line.m_team_info_my_team_apply_toc";
         ClassUtils.regClass("proto.line.m_team_info_my_team_apply_toc",m_team_info_my_team_apply_toc);
         SocketCommand.protoMap[6763392] = "proto.line.m_rank_mission_info_toc";
         ClassUtils.regClass("proto.line.m_rank_mission_info_toc",m_rank_mission_info_toc);
         SocketCommand.protoMap[2101248] = "proto.line.m_daily_mission_info_toc";
         ClassUtils.regClass("proto.line.m_daily_mission_info_toc",m_daily_mission_info_toc);
         SocketCommand.protoMap[10506241] = "proto.line.m_treasure_dispatch_toc";
         ClassUtils.regClass("proto.line.m_treasure_dispatch_toc",m_treasure_dispatch_toc);
         SocketCommand.protoMap[11031552] = "proto.line.m_mock_pvp_op_toc";
         ClassUtils.regClass("proto.line.m_mock_pvp_op_toc",m_mock_pvp_op_toc);
         SocketCommand.protoMap[722305] = "proto.line.m_common_tips_toc";
         ClassUtils.regClass("proto.line.m_common_tips_toc",m_common_tips_toc);
         SocketCommand.protoMap[3611526] = "proto.line.m_arena_max_reward_info_toc";
         ClassUtils.regClass("proto.line.m_arena_max_reward_info_toc",m_arena_max_reward_info_toc);
         SocketCommand.protoMap[7879687] = "proto.line.m_random_pvp_fight_result_toc";
         ClassUtils.regClass("proto.line.m_random_pvp_fight_result_toc",m_random_pvp_fight_result_toc);
         SocketCommand.protoMap[8404998] = "proto.line.m_peak_info_bet_toc";
         ClassUtils.regClass("proto.line.m_peak_info_bet_toc",m_peak_info_bet_toc);
         SocketCommand.protoMap[2495233] = "proto.line.m_equip_load_toc";
         ClassUtils.regClass("proto.line.m_equip_load_toc",m_equip_load_toc);
         SocketCommand.protoMap[3020544] = "proto.line.m_profile_info_toc";
         ClassUtils.regClass("proto.line.m_profile_info_toc",m_profile_info_toc);
         SocketCommand.protoMap[10506242] = "proto.line.m_treasure_other_toc";
         ClassUtils.regClass("proto.line.m_treasure_other_toc",m_treasure_other_toc);
         SocketCommand.protoMap[11031553] = "proto.line.m_mock_pvp_info_toc";
         ClassUtils.regClass("proto.line.m_mock_pvp_info_toc",m_mock_pvp_info_toc);
         SocketCommand.protoMap[6303748] = "proto.line.m_family_boss_rank_toc";
         ClassUtils.regClass("proto.line.m_family_boss_rank_toc",m_family_boss_rank_toc);
         SocketCommand.protoMap[10703234] = "proto.line.m_fight_share_chat_toc";
         ClassUtils.regClass("proto.line.m_fight_share_chat_toc",m_fight_share_chat_toc);
         SocketCommand.protoMap[4268164] = "proto.line.m_vip_buy_recommend_toc";
         ClassUtils.regClass("proto.line.m_vip_buy_recommend_toc",m_vip_buy_recommend_toc);
         SocketCommand.protoMap[10112260] = "proto.line.m_boat_race_event_toc";
         ClassUtils.regClass("proto.line.m_boat_race_event_toc",m_boat_race_event_toc);
         SocketCommand.protoMap[7879688] = "proto.line.m_random_pvp_new_season_toc";
         ClassUtils.regClass("proto.line.m_random_pvp_new_season_toc",m_random_pvp_new_season_toc);
         SocketCommand.protoMap[8404999] = "proto.line.m_peak_info_history_toc";
         ClassUtils.regClass("proto.line.m_peak_info_history_toc",m_peak_info_history_toc);
         SocketCommand.protoMap[2495234] = "proto.line.m_equip_unload_toc";
         ClassUtils.regClass("proto.line.m_equip_unload_toc",m_equip_unload_toc);
         SocketCommand.protoMap[3020545] = "proto.line.m_profile_change_toc";
         ClassUtils.regClass("proto.line.m_profile_change_toc",m_profile_change_toc);
         SocketCommand.protoMap[3545856] = "proto.line.m_shortcut_shop_info_toc";
         ClassUtils.regClass("proto.line.m_shortcut_shop_info_toc",m_shortcut_shop_info_toc);
         SocketCommand.protoMap[7288705] = "proto.line.m_csc_fmsolo_group_toc";
         ClassUtils.regClass("proto.line.m_csc_fmsolo_group_toc",m_csc_fmsolo_group_toc);
         SocketCommand.protoMap[11031554] = "proto.line.m_mock_pvp_look_hero_toc";
         ClassUtils.regClass("proto.line.m_mock_pvp_look_hero_toc",m_mock_pvp_look_hero_toc);
         SocketCommand.protoMap[11556865] = "proto.line.m_csclan_solo_info_toc";
         ClassUtils.regClass("proto.line.m_csclan_solo_info_toc",m_csclan_solo_info_toc);
         SocketCommand.protoMap[10243590] = "proto.line.m_story_maze_hero_toc";
         ClassUtils.regClass("proto.line.m_story_maze_hero_toc",m_story_maze_hero_toc);
         SocketCommand.protoMap[2495235] = "proto.line.m_equip_auto_load_toc";
         ClassUtils.regClass("proto.line.m_equip_auto_load_toc",m_equip_auto_load_toc);
         SocketCommand.protoMap[9455622] = "proto.line.m_team_lineup_get_toc";
         ClassUtils.regClass("proto.line.m_team_lineup_get_toc",m_team_lineup_get_toc);
         SocketCommand.protoMap[4071168] = "proto.line.m_guide_mission_toc";
         ClassUtils.regClass("proto.line.m_guide_mission_toc",m_guide_mission_toc);
         SocketCommand.protoMap[6369408] = "proto.line.m_red_cliff_info_toc";
         ClassUtils.regClass("proto.line.m_red_cliff_info_toc",m_red_cliff_info_toc);
         SocketCommand.protoMap[8798976] = "proto.line.m_soul_hero_info_toc";
         ClassUtils.regClass("proto.line.m_soul_hero_info_toc",m_soul_hero_info_toc);
         SocketCommand.protoMap[3545857] = "proto.line.m_shortcut_shop_buy_toc";
         ClassUtils.regClass("proto.line.m_shortcut_shop_buy_toc",m_shortcut_shop_buy_toc);
         SocketCommand.protoMap[1444610] = "proto.line.m_activity_info_toc";
         ClassUtils.regClass("proto.line.m_activity_info_toc",m_activity_info_toc);
         SocketCommand.protoMap[7748355] = "proto.line.m_hero_pass_gift_toc";
         ClassUtils.regClass("proto.line.m_hero_pass_gift_toc",m_hero_pass_gift_toc);
         SocketCommand.protoMap[7748356] = "proto.line.m_hero_pass_update_toc";
         ClassUtils.regClass("proto.line.m_hero_pass_update_toc",m_hero_pass_update_toc);
         SocketCommand.protoMap[1444613] = "proto.line.m_activity_shop_update_toc";
         ClassUtils.regClass("proto.line.m_activity_shop_update_toc",m_activity_shop_update_toc);
         SocketCommand.protoMap[2495238] = "proto.line.m_equip_auto_reinforce_toc";
         ClassUtils.regClass("proto.line.m_equip_auto_reinforce_toc",m_equip_auto_reinforce_toc);
         SocketCommand.protoMap[5318785] = "proto.line.m_rent_hero_op_toc";
         ClassUtils.regClass("proto.line.m_rent_hero_op_toc",m_rent_hero_op_toc);
         SocketCommand.protoMap[1444616] = "proto.line.m_activity_exam_answer_toc";
         ClassUtils.regClass("proto.line.m_activity_exam_answer_toc",m_activity_exam_answer_toc);
         SocketCommand.protoMap[1444617] = "proto.line.m_activity_gift_toc";
         ClassUtils.regClass("proto.line.m_activity_gift_toc",m_activity_gift_toc);
         SocketCommand.protoMap[2495242] = "proto.line.m_equip_auto_compose_info_toc";
         ClassUtils.regClass("proto.line.m_equip_auto_compose_info_toc",m_equip_auto_compose_info_toc);
         SocketCommand.protoMap[2495243] = "proto.line.m_equip_compose_logs_toc";
         ClassUtils.regClass("proto.line.m_equip_compose_logs_toc",m_equip_compose_logs_toc);
         SocketCommand.protoMap[2495244] = "proto.line.m_equip_replace_toc";
         ClassUtils.regClass("proto.line.m_equip_replace_toc",m_equip_replace_toc);
         SocketCommand.protoMap[7420034] = "proto.line.m_pass_check_gift_toc";
         ClassUtils.regClass("proto.line.m_pass_check_gift_toc",m_pass_check_gift_toc);
         SocketCommand.protoMap[2495246] = "proto.line.m_equip_filter_info_toc";
         ClassUtils.regClass("proto.line.m_equip_filter_info_toc",m_equip_filter_info_toc);
         SocketCommand.protoMap[2495248] = "proto.line.m_equip_star_op_toc";
         ClassUtils.regClass("proto.line.m_equip_star_op_toc",m_equip_star_op_toc);
         SocketCommand.protoMap[2495249] = "proto.line.m_equip_bingfu_recast_toc";
         ClassUtils.regClass("proto.line.m_equip_bingfu_recast_toc",m_equip_bingfu_recast_toc);
         SocketCommand.protoMap[2495250] = "proto.line.m_equip_bingfu_decompose_toc";
         ClassUtils.regClass("proto.line.m_equip_bingfu_decompose_toc",m_equip_bingfu_decompose_toc);
         SocketCommand.protoMap[7945347] = "proto.line.m_maze_spoils_toc";
         ClassUtils.regClass("proto.line.m_maze_spoils_toc",m_maze_spoils_toc);
         SocketCommand.protoMap[2495252] = "proto.line.m_equip_bingfa_op_toc";
         ClassUtils.regClass("proto.line.m_equip_bingfa_op_toc",m_equip_bingfa_op_toc);
         SocketCommand.protoMap[2495253] = "proto.line.m_equip_bingfa_exchange_toc";
         ClassUtils.regClass("proto.line.m_equip_bingfa_exchange_toc",m_equip_bingfa_exchange_toc);
         SocketCommand.protoMap[2495254] = "proto.line.m_equip_lock_toc";
         ClassUtils.regClass("proto.line.m_equip_lock_toc",m_equip_lock_toc);
         SocketCommand.protoMap[2495255] = "proto.line.m_wing_op_toc";
         ClassUtils.regClass("proto.line.m_wing_op_toc",m_wing_op_toc);
         SocketCommand.protoMap[9455623] = "proto.line.m_team_lineup_list_toc";
         ClassUtils.regClass("proto.line.m_team_lineup_list_toc",m_team_lineup_list_toc);
         SocketCommand.protoMap[10571908] = "proto.line.m_lord_lineup_list_toc";
         ClassUtils.regClass("proto.line.m_lord_lineup_list_toc",m_lord_lineup_list_toc);
         SocketCommand.protoMap[2495258] = "proto.line.m_deputy_info_toc";
         ClassUtils.regClass("proto.line.m_deputy_info_toc",m_deputy_info_toc);
         SocketCommand.protoMap[1444611] = "proto.line.m_activity_shop_info_toc";
         ClassUtils.regClass("proto.line.m_activity_shop_info_toc",m_activity_shop_info_toc);
         SocketCommand.protoMap[4071169] = "proto.line.m_guide_hint_toc";
         ClassUtils.regClass("proto.line.m_guide_hint_toc",m_guide_hint_toc);
         SocketCommand.protoMap[11031556] = "proto.line.m_mock_pvp_schemes_toc";
         ClassUtils.regClass("proto.line.m_mock_pvp_schemes_toc",m_mock_pvp_schemes_toc);
         SocketCommand.protoMap[9521285] = "proto.line.m_battle_trial_hanging_info_toc";
         ClassUtils.regClass("proto.line.m_battle_trial_hanging_info_toc",m_battle_trial_hanging_info_toc);
         SocketCommand.protoMap[11556867] = "proto.line.m_csclan_solo_fetch_toc";
         ClassUtils.regClass("proto.line.m_csclan_solo_fetch_toc",m_csclan_solo_fetch_toc);
         SocketCommand.protoMap[11556866] = "proto.line.m_csclan_solo_group_toc";
         ClassUtils.regClass("proto.line.m_csclan_solo_group_toc",m_csclan_solo_group_toc);
         SocketCommand.protoMap[8864640] = "proto.line.m_medal_list_toc";
         ClassUtils.regClass("proto.line.m_medal_list_toc",m_medal_list_toc);
         SocketCommand.protoMap[3742854] = "proto.line.m_fuli_fund_fetch_toc";
         ClassUtils.regClass("proto.line.m_fuli_fund_fetch_toc",m_fuli_fund_fetch_toc);
         SocketCommand.protoMap[1575937] = "proto.line.m_welfare_sdk_share_toc";
         ClassUtils.regClass("proto.line.m_welfare_sdk_share_toc",m_welfare_sdk_share_toc);
         SocketCommand.protoMap[11622535] = "proto.line.m_wars_sign_toc";
         ClassUtils.regClass("proto.line.m_wars_sign_toc",m_wars_sign_toc);
         SocketCommand.protoMap[1444615] = "proto.line.m_activity_exam_list_toc";
         ClassUtils.regClass("proto.line.m_activity_exam_list_toc",m_activity_exam_list_toc);
         SocketCommand.protoMap[1116296] = "proto.line.m_role_family_change_toc";
         ClassUtils.regClass("proto.line.m_role_family_change_toc",m_role_family_change_toc);
         SocketCommand.protoMap[5384450] = "proto.line.m_quick_shop_tips_toc";
         ClassUtils.regClass("proto.line.m_quick_shop_tips_toc",m_quick_shop_tips_toc);
         SocketCommand.protoMap[9652609] = "proto.line.m_master_card_lottery_toc";
         ClassUtils.regClass("proto.line.m_master_card_lottery_toc",m_master_card_lottery_toc);
         SocketCommand.protoMap[11622537] = "proto.line.m_wars_team_info_toc";
         ClassUtils.regClass("proto.line.m_wars_team_info_toc",m_wars_team_info_toc);
         SocketCommand.protoMap[853632] = "proto.line.m_letter_get_toc";
         ClassUtils.regClass("proto.line.m_letter_get_toc",m_letter_get_toc);
         SocketCommand.protoMap[11031557] = "proto.line.m_mock_pvp_config_toc";
         ClassUtils.regClass("proto.line.m_mock_pvp_config_toc",m_mock_pvp_config_toc);
         SocketCommand.protoMap[11622538] = "proto.line.m_wars_team_hero_info_toc";
         ClassUtils.regClass("proto.line.m_wars_team_hero_info_toc",m_wars_team_hero_info_toc);
         SocketCommand.protoMap[5121792] = "proto.line.m_hero_skin_info_toc";
         ClassUtils.regClass("proto.line.m_hero_skin_info_toc",m_hero_skin_info_toc);
         SocketCommand.protoMap[9389952] = "proto.line.m_microterminal_info_toc";
         ClassUtils.regClass("proto.line.m_microterminal_info_toc",m_microterminal_info_toc);
         SocketCommand.protoMap[1116299] = "proto.line.m_role_update_ext_toc";
         ClassUtils.regClass("proto.line.m_role_update_ext_toc",m_role_update_ext_toc);
         SocketCommand.protoMap[1116300] = "proto.line.m_role_best_hero_toc";
         ClassUtils.regClass("proto.line.m_role_best_hero_toc",m_role_best_hero_toc);
         SocketCommand.protoMap[9455625] = "proto.line.m_team_merge_reward_toc";
         ClassUtils.regClass("proto.line.m_team_merge_reward_toc",m_team_merge_reward_toc);
         SocketCommand.protoMap[1116302] = "proto.line.m_role_look_lineup_toc";
         ClassUtils.regClass("proto.line.m_role_look_lineup_toc",m_role_look_lineup_toc);
         SocketCommand.protoMap[4071171] = "proto.line.m_guide_step_toc";
         ClassUtils.regClass("proto.line.m_guide_step_toc",m_guide_step_toc);
         SocketCommand.protoMap[10177922] = "proto.line.m_master_talent_science_update_toc";
         ClassUtils.regClass("proto.line.m_master_talent_science_update_toc",m_master_talent_science_update_toc);
         SocketCommand.protoMap[1378944] = "proto.line.m_system_heartbeat_toc";
         ClassUtils.regClass("proto.line.m_system_heartbeat_toc",m_system_heartbeat_toc);
         SocketCommand.protoMap[11622543] = "proto.line.m_wars_look_op_army_toc";
         ClassUtils.regClass("proto.line.m_wars_look_op_army_toc",m_wars_look_op_army_toc);
         SocketCommand.protoMap[5647104] = "proto.line.m_daily_fuli_info_toc";
         ClassUtils.regClass("proto.line.m_daily_fuli_info_toc",m_daily_fuli_info_toc);
         SocketCommand.protoMap[8536325] = "proto.line.m_cross_ladder_fight_result_toc";
         ClassUtils.regClass("proto.line.m_cross_ladder_fight_result_toc",m_cross_ladder_fight_result_toc);
         SocketCommand.protoMap[9389953] = "proto.line.m_microterminal_sign_fetch_toc";
         ClassUtils.regClass("proto.line.m_microterminal_sign_fetch_toc",m_microterminal_sign_fetch_toc);
         SocketCommand.protoMap[11622544] = "proto.line.m_wars_look_op_score_rank_toc";
         ClassUtils.regClass("proto.line.m_wars_look_op_score_rank_toc",m_wars_look_op_score_rank_toc);
         SocketCommand.protoMap[9915264] = "proto.line.m_stage_copy_info_toc";
         ClassUtils.regClass("proto.line.m_stage_copy_info_toc",m_stage_copy_info_toc);
         SocketCommand.protoMap[11622545] = "proto.line.m_wars_look_op_fight_log_toc";
         ClassUtils.regClass("proto.line.m_wars_look_op_fight_log_toc",m_wars_look_op_fight_log_toc);
         SocketCommand.protoMap[11622546] = "proto.line.m_wars_look_op_city_log_toc";
         ClassUtils.regClass("proto.line.m_wars_look_op_city_log_toc",m_wars_look_op_city_log_toc);
         SocketCommand.protoMap[11622542] = "proto.line.m_wars_look_op_sign_toc";
         ClassUtils.regClass("proto.line.m_wars_look_op_sign_toc",m_wars_look_op_sign_toc);
         SocketCommand.protoMap[9455626] = "proto.line.m_team_share_list_toc";
         ClassUtils.regClass("proto.line.m_team_share_list_toc",m_team_share_list_toc);
         SocketCommand.protoMap[11622547] = "proto.line.m_wars_look_op_camp_rank_toc";
         ClassUtils.regClass("proto.line.m_wars_look_op_camp_rank_toc",m_wars_look_op_camp_rank_toc);
         SocketCommand.protoMap[4071172] = "proto.line.m_guide_event_toc";
         ClassUtils.regClass("proto.line.m_guide_event_toc",m_guide_event_toc);
         SocketCommand.protoMap[1378945] = "proto.line.m_system_error_toc";
         ClassUtils.regClass("proto.line.m_system_error_toc",m_system_error_toc);
         SocketCommand.protoMap[1116308] = "proto.line.m_role_cross_group_toc";
         ClassUtils.regClass("proto.line.m_role_cross_group_toc",m_role_cross_group_toc);
         SocketCommand.protoMap[1904256] = "proto.line.m_gmcmd_do_toc";
         ClassUtils.regClass("proto.line.m_gmcmd_do_toc",m_gmcmd_do_toc);
         SocketCommand.protoMap[10243591] = "proto.line.m_story_maze_pub_toc";
         ClassUtils.regClass("proto.line.m_story_maze_pub_toc",m_story_maze_pub_toc);
         SocketCommand.protoMap[6172416] = "proto.line.m_war_flag_info_toc";
         ClassUtils.regClass("proto.line.m_war_flag_info_toc",m_war_flag_info_toc);
         SocketCommand.protoMap[1510272] = "proto.line.m_ranking_list_toc";
         ClassUtils.regClass("proto.line.m_ranking_list_toc",m_ranking_list_toc);
         SocketCommand.protoMap[6763393] = "proto.line.m_rank_mission_update_toc";
         ClassUtils.regClass("proto.line.m_rank_mission_update_toc",m_rank_mission_update_toc);
         SocketCommand.protoMap[3611522] = "proto.line.m_arena_update_toc";
         ClassUtils.regClass("proto.line.m_arena_update_toc",m_arena_update_toc);
         SocketCommand.protoMap[9915267] = "proto.line.m_stage_copy_drop_group_toc";
         ClassUtils.regClass("proto.line.m_stage_copy_drop_group_toc",m_stage_copy_drop_group_toc);
         SocketCommand.protoMap[3611524] = "proto.line.m_arena_logs_toc";
         ClassUtils.regClass("proto.line.m_arena_logs_toc",m_arena_logs_toc);
         SocketCommand.protoMap[590725] = "proto.line.m_chat_auth_toc";
         ClassUtils.regClass("proto.line.m_chat_auth_toc",m_chat_auth_toc);
         SocketCommand.protoMap[10965894] = "proto.line.m_td_simp_info_toc";
         ClassUtils.regClass("proto.line.m_td_simp_info_toc",m_td_simp_info_toc);
         SocketCommand.protoMap[10965895] = "proto.line.m_td_mission_info_toc";
         ClassUtils.regClass("proto.line.m_td_mission_info_toc",m_td_mission_info_toc);
         SocketCommand.protoMap[10571910] = "proto.line.m_lord_recycle_preview_toc";
         ClassUtils.regClass("proto.line.m_lord_recycle_preview_toc",m_lord_recycle_preview_toc);
         SocketCommand.protoMap[590736] = "proto.line.m_chat_channel_toc";
         ClassUtils.regClass("proto.line.m_chat_channel_toc",m_chat_channel_toc);
         SocketCommand.protoMap[853635] = "proto.line.m_letter_operate_toc";
         ClassUtils.regClass("proto.line.m_letter_operate_toc",m_letter_operate_toc);
         SocketCommand.protoMap[1378946] = "proto.line.m_system_message_toc";
         ClassUtils.regClass("proto.line.m_system_message_toc",m_system_message_toc);
         SocketCommand.protoMap[5121795] = "proto.line.m_hero_skin_upgrade_toc";
         ClassUtils.regClass("proto.line.m_hero_skin_upgrade_toc",m_hero_skin_upgrade_toc);
         SocketCommand.protoMap[590745] = "proto.line.m_chat_private_history_toc";
         ClassUtils.regClass("proto.line.m_chat_private_history_toc",m_chat_private_history_toc);
         SocketCommand.protoMap[2429568] = "proto.line.m_hero_list_toc";
         ClassUtils.regClass("proto.line.m_hero_list_toc",m_hero_list_toc);
         SocketCommand.protoMap[590747] = "proto.line.m_chat_misc_toc";
         ClassUtils.regClass("proto.line.m_chat_misc_toc",m_chat_misc_toc);
         SocketCommand.protoMap[6172417] = "proto.line.m_war_flag_active_toc";
         ClassUtils.regClass("proto.line.m_war_flag_active_toc",m_war_flag_active_toc);
         SocketCommand.protoMap[590749] = "proto.line.m_chat_cost_toc";
         ClassUtils.regClass("proto.line.m_chat_cost_toc",m_chat_cost_toc);
         SocketCommand.protoMap[9915266] = "proto.line.m_stage_copy_fetch_toc";
         ClassUtils.regClass("proto.line.m_stage_copy_fetch_toc",m_stage_copy_fetch_toc);
         SocketCommand.protoMap[656647] = "proto.line.m_login_scene_toc";
         ClassUtils.regClass("proto.line.m_login_scene_toc",m_login_scene_toc);
         SocketCommand.protoMap[10440577] = "proto.line.m_acc_gift_info_toc";
         ClassUtils.regClass("proto.line.m_acc_gift_info_toc",m_acc_gift_info_toc);
         SocketCommand.protoMap[7091723] = "proto.line.m_qxzl_info_get_msg_toc";
         ClassUtils.regClass("proto.line.m_qxzl_info_get_msg_toc",m_qxzl_info_get_msg_toc);
         SocketCommand.protoMap[6697728] = "proto.line.m_sys_daily_info_toc";
         ClassUtils.regClass("proto.line.m_sys_daily_info_toc",m_sys_daily_info_toc);
         SocketCommand.protoMap[10112261] = "proto.line.m_boat_race_op_toc";
         ClassUtils.regClass("proto.line.m_boat_race_op_toc",m_boat_race_op_toc);
         SocketCommand.protoMap[8142345] = "proto.line.m_modular_activity_wish_lottery_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_wish_lottery_info_toc",m_modular_activity_wish_lottery_info_toc);
         SocketCommand.protoMap[11622557] = "proto.line.m_wars_honor_wall_toc";
         ClassUtils.regClass("proto.line.m_wars_honor_wall_toc",m_wars_honor_wall_toc);
         SocketCommand.protoMap[10506250] = "proto.line.m_treasure_exist_toc";
         ClassUtils.regClass("proto.line.m_treasure_exist_toc",m_treasure_exist_toc);
         SocketCommand.protoMap[1378947] = "proto.line.m_system_config_change_toc";
         ClassUtils.regClass("proto.line.m_system_config_change_toc",m_system_config_change_toc);
         SocketCommand.protoMap[5121796] = "proto.line.m_hero_skin_reset_toc";
         ClassUtils.regClass("proto.line.m_hero_skin_reset_toc",m_hero_skin_reset_toc);
         SocketCommand.protoMap[8864645] = "proto.line.m_medal_detail_toc";
         ClassUtils.regClass("proto.line.m_medal_detail_toc",m_medal_detail_toc);
         SocketCommand.protoMap[2954880] = "proto.line.m_tax_silver_info_toc";
         ClassUtils.regClass("proto.line.m_tax_silver_info_toc",m_tax_silver_info_toc);
         SocketCommand.protoMap[11622539] = "proto.line.m_wars_role_presonal_info_toc";
         ClassUtils.regClass("proto.line.m_wars_role_presonal_info_toc",m_wars_role_presonal_info_toc);
         SocketCommand.protoMap[6697729] = "proto.line.m_sys_daily_fetch_toc";
         ClassUtils.regClass("proto.line.m_sys_daily_fetch_toc",m_sys_daily_fetch_toc);
         SocketCommand.protoMap[7223040] = "proto.line.m_cycle_activity_info_toc";
         ClassUtils.regClass("proto.line.m_cycle_activity_info_toc",m_cycle_activity_info_toc);
         SocketCommand.protoMap[10965889] = "proto.line.m_td_lineup_toc";
         ClassUtils.regClass("proto.line.m_td_lineup_toc",m_td_lineup_toc);
         SocketCommand.protoMap[11491200] = "proto.line.m_divine_copy_sweep_toc";
         ClassUtils.regClass("proto.line.m_divine_copy_sweep_toc",m_divine_copy_sweep_toc);
         SocketCommand.protoMap[9652612] = "proto.line.m_master_card_shop_toc";
         ClassUtils.regClass("proto.line.m_master_card_shop_toc",m_master_card_shop_toc);
         SocketCommand.protoMap[4333824] = "proto.line.m_family_ranking_list_toc";
         ClassUtils.regClass("proto.line.m_family_ranking_list_toc",m_family_ranking_list_toc);
         SocketCommand.protoMap[1378948] = "proto.line.m_system_config_toc";
         ClassUtils.regClass("proto.line.m_system_config_toc",m_system_config_toc);
         SocketCommand.protoMap[2429570] = "proto.line.m_hero_update_fight_toc";
         ClassUtils.regClass("proto.line.m_hero_update_fight_toc",m_hero_update_fight_toc);
         SocketCommand.protoMap[6172419] = "proto.line.m_war_flag_link_toc";
         ClassUtils.regClass("proto.line.m_war_flag_link_toc",m_war_flag_link_toc);
         SocketCommand.protoMap[3480192] = "proto.line.m_hunt_info_toc";
         ClassUtils.regClass("proto.line.m_hunt_info_toc",m_hunt_info_toc);
         SocketCommand.protoMap[7223041] = "proto.line.m_cycle_activity_fetch_toc";
         ClassUtils.regClass("proto.line.m_cycle_activity_fetch_toc",m_cycle_activity_fetch_toc);
         SocketCommand.protoMap[2429569] = "proto.line.m_hero_update_list_toc";
         ClassUtils.regClass("proto.line.m_hero_update_list_toc",m_hero_update_list_toc);
         SocketCommand.protoMap[10965890] = "proto.line.m_td_skill_toc";
         ClassUtils.regClass("proto.line.m_td_skill_toc",m_td_skill_toc);
         SocketCommand.protoMap[11491201] = "proto.line.m_divine_copy_info_toc";
         ClassUtils.regClass("proto.line.m_divine_copy_info_toc",m_divine_copy_info_toc);
         SocketCommand.protoMap[3874176] = "proto.line.m_first_pay_info_toc";
         ClassUtils.regClass("proto.line.m_first_pay_info_toc",m_first_pay_info_toc);
         SocketCommand.protoMap[7288708] = "proto.line.m_csc_fmsolo_box_toc";
         ClassUtils.regClass("proto.line.m_csc_fmsolo_box_toc",m_csc_fmsolo_box_toc);
         SocketCommand.protoMap[9521282] = "proto.line.m_battle_trial_pass_info_toc";
         ClassUtils.regClass("proto.line.m_battle_trial_pass_info_toc",m_battle_trial_pass_info_toc);
         SocketCommand.protoMap[2429571] = "proto.line.m_hero_upgrade_toc";
         ClassUtils.regClass("proto.line.m_hero_upgrade_toc",m_hero_upgrade_toc);
         SocketCommand.protoMap[2954882] = "proto.line.m_tax_forage_info_toc";
         ClassUtils.regClass("proto.line.m_tax_forage_info_toc",m_tax_forage_info_toc);
         SocketCommand.protoMap[3480193] = "proto.line.m_hunt_run_toc";
         ClassUtils.regClass("proto.line.m_hunt_run_toc",m_hunt_run_toc);
         SocketCommand.protoMap[4005504] = "proto.line.m_daily_pay_info_toc";
         ClassUtils.regClass("proto.line.m_daily_pay_info_toc",m_daily_pay_info_toc);
         SocketCommand.protoMap[10965891] = "proto.line.m_td_fight_toc";
         ClassUtils.regClass("proto.line.m_td_fight_toc",m_td_fight_toc);
         SocketCommand.protoMap[853633] = "proto.line.m_letter_open_toc";
         ClassUtils.regClass("proto.line.m_letter_open_toc",m_letter_open_toc);
         SocketCommand.protoMap[8273664] = "proto.line.m_hero_resonate_level_info_toc";
         ClassUtils.regClass("proto.line.m_hero_resonate_level_info_toc",m_hero_resonate_level_info_toc);
         SocketCommand.protoMap[10243586] = "proto.line.m_story_maze_start_toc";
         ClassUtils.regClass("proto.line.m_story_maze_start_toc",m_story_maze_start_toc);
         SocketCommand.protoMap[9980928] = "proto.line.m_sgame_info_toc";
         ClassUtils.regClass("proto.line.m_sgame_info_toc",m_sgame_info_toc);
         SocketCommand.protoMap[8667648] = "proto.line.m_dawanka_info_toc";
         ClassUtils.regClass("proto.line.m_dawanka_info_toc",m_dawanka_info_toc);
         SocketCommand.protoMap[8930306] = "proto.line.m_player_strategy_update_toc";
         ClassUtils.regClass("proto.line.m_player_strategy_update_toc",m_player_strategy_update_toc);
         SocketCommand.protoMap[7879683] = "proto.line.m_random_pvp_peak_toc";
         ClassUtils.regClass("proto.line.m_random_pvp_peak_toc",m_random_pvp_peak_toc);
         SocketCommand.protoMap[2626564] = "proto.line.m_main_battle_mission_info_toc";
         ClassUtils.regClass("proto.line.m_main_battle_mission_info_toc",m_main_battle_mission_info_toc);
         SocketCommand.protoMap[2626565] = "proto.line.m_main_battle_mission_fetch_toc";
         ClassUtils.regClass("proto.line.m_main_battle_mission_fetch_toc",m_main_battle_mission_fetch_toc);
         SocketCommand.protoMap[11031558] = "proto.line.m_mock_pvp_show_tops_toc";
         ClassUtils.regClass("proto.line.m_mock_pvp_show_tops_toc",m_mock_pvp_show_tops_toc);
         SocketCommand.protoMap[2363905] = "proto.line.m_shop_buy_toc";
         ClassUtils.regClass("proto.line.m_shop_buy_toc",m_shop_buy_toc);
         SocketCommand.protoMap[11031560] = "proto.line.m_mock_pvp_fight_result_toc";
         ClassUtils.regClass("proto.line.m_mock_pvp_fight_result_toc",m_mock_pvp_fight_result_toc);
         SocketCommand.protoMap[2626569] = "proto.line.m_main_battle_box_open_toc";
         ClassUtils.regClass("proto.line.m_main_battle_box_open_toc",m_main_battle_box_open_toc);
         SocketCommand.protoMap[11031562] = "proto.line.m_mock_pvp_clean_chat_toc";
         ClassUtils.regClass("proto.line.m_mock_pvp_clean_chat_toc",m_mock_pvp_clean_chat_toc);
         SocketCommand.protoMap[2626571] = "proto.line.m_main_battle_auto_end_toc";
         ClassUtils.regClass("proto.line.m_main_battle_auto_end_toc",m_main_battle_auto_end_toc);
         SocketCommand.protoMap[11031564] = "proto.line.m_mock_pvp_lineup_preview_toc";
         ClassUtils.regClass("proto.line.m_mock_pvp_lineup_preview_toc",m_mock_pvp_lineup_preview_toc);
         SocketCommand.protoMap[2889218] = "proto.line.m_daily_copy_sweep_toc";
         ClassUtils.regClass("proto.line.m_daily_copy_sweep_toc",m_daily_copy_sweep_toc);
         SocketCommand.protoMap[2626574] = "proto.line.m_main_battle_box_rate_toc";
         ClassUtils.regClass("proto.line.m_main_battle_box_rate_toc",m_main_battle_box_rate_toc);
         SocketCommand.protoMap[2626576] = "proto.line.m_main_battle_missions_toc";
         ClassUtils.regClass("proto.line.m_main_battle_missions_toc",m_main_battle_missions_toc);
         SocketCommand.protoMap[8142339] = "proto.line.m_modular_activity_update_mission_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_update_mission_toc",m_modular_activity_update_mission_toc);
         SocketCommand.protoMap[4005505] = "proto.line.m_daily_pay_fetch_toc";
         ClassUtils.regClass("proto.line.m_daily_pay_fetch_toc",m_daily_pay_fetch_toc);
         SocketCommand.protoMap[10768900] = "proto.line.m_boat_peak_battle_toc";
         ClassUtils.regClass("proto.line.m_boat_peak_battle_toc",m_boat_peak_battle_toc);
         SocketCommand.protoMap[10965892] = "proto.line.m_td_trial_info_toc";
         ClassUtils.regClass("proto.line.m_td_trial_info_toc",m_td_trial_info_toc);
         SocketCommand.protoMap[8273665] = "proto.line.m_hero_resonate_info_toc";
         ClassUtils.regClass("proto.line.m_hero_resonate_info_toc",m_hero_resonate_info_toc);
         SocketCommand.protoMap[7617029] = "proto.line.m_theme_activity_zhouka_info_toc";
         ClassUtils.regClass("proto.line.m_theme_activity_zhouka_info_toc",m_theme_activity_zhouka_info_toc);
         SocketCommand.protoMap[7617030] = "proto.line.m_theme_activity_wish_lottery_info_toc";
         ClassUtils.regClass("proto.line.m_theme_activity_wish_lottery_info_toc",m_theme_activity_wish_lottery_info_toc);
         SocketCommand.protoMap[787975] = "proto.line.m_friend_fetch_toc";
         ClassUtils.regClass("proto.line.m_friend_fetch_toc",m_friend_fetch_toc);
         SocketCommand.protoMap[4465159] = "proto.line.m_family_hongbao_mission_update_toc";
         ClassUtils.regClass("proto.line.m_family_hongbao_mission_update_toc",m_family_hongbao_mission_update_toc);
         SocketCommand.protoMap[8142344] = "proto.line.m_modular_activity_rare_lottery_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_rare_lottery_info_toc",m_modular_activity_rare_lottery_info_toc);
         SocketCommand.protoMap[787968] = "proto.line.m_friend_list_toc";
         ClassUtils.regClass("proto.line.m_friend_list_toc",m_friend_list_toc);
         SocketCommand.protoMap[787977] = "proto.line.m_friend_best_list_toc";
         ClassUtils.regClass("proto.line.m_friend_best_list_toc",m_friend_best_list_toc);
         SocketCommand.protoMap[7814016] = "proto.line.m_login_activity_info_toc";
         ClassUtils.regClass("proto.line.m_login_activity_info_toc",m_login_activity_info_toc);
         SocketCommand.protoMap[8273666] = "proto.line.m_hero_resonate_operate_toc";
         ClassUtils.regClass("proto.line.m_hero_resonate_operate_toc",m_hero_resonate_operate_toc);
         SocketCommand.protoMap[8798977] = "proto.line.m_soul_hero_link_toc";
         ClassUtils.regClass("proto.line.m_soul_hero_link_toc",m_soul_hero_link_toc);
         SocketCommand.protoMap[7617035] = "proto.line.m_theme_activity_up_star_reward_update_toc";
         ClassUtils.regClass("proto.line.m_theme_activity_up_star_reward_update_toc",m_theme_activity_up_star_reward_update_toc);
         SocketCommand.protoMap[8142348] = "proto.line.m_modular_activity_skin_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_skin_info_toc",m_modular_activity_skin_info_toc);
         SocketCommand.protoMap[11556868] = "proto.line.m_csclan_solo_state_toc";
         ClassUtils.regClass("proto.line.m_csclan_solo_state_toc",m_csclan_solo_state_toc);
         SocketCommand.protoMap[8142349] = "proto.line.m_modular_activity_skin_lottery_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_skin_lottery_toc",m_modular_activity_skin_lottery_toc);
         SocketCommand.protoMap[3480196] = "proto.line.m_hunt_logs_toc";
         ClassUtils.regClass("proto.line.m_hunt_logs_toc",m_hunt_logs_toc);
         SocketCommand.protoMap[4530818] = "proto.line.m_fuli_token_fetch_toc";
         ClassUtils.regClass("proto.line.m_fuli_token_fetch_toc",m_fuli_token_fetch_toc);
         SocketCommand.protoMap[5056129] = "proto.line.m_vip_kefu_op_toc";
         ClassUtils.regClass("proto.line.m_vip_kefu_op_toc",m_vip_kefu_op_toc);
         SocketCommand.protoMap[8798978] = "proto.line.m_soul_hero_reset_toc";
         ClassUtils.regClass("proto.line.m_soul_hero_reset_toc",m_soul_hero_reset_toc);
         SocketCommand.protoMap[3217538] = "proto.line.m_family_task_do_toc";
         ClassUtils.regClass("proto.line.m_family_task_do_toc",m_family_task_do_toc);
         SocketCommand.protoMap[9980932] = "proto.line.m_sgame_wx_club_toc";
         ClassUtils.regClass("proto.line.m_sgame_wx_club_toc",m_sgame_wx_club_toc);
         SocketCommand.protoMap[787970] = "proto.line.m_friend_request_toc";
         ClassUtils.regClass("proto.line.m_friend_request_toc",m_friend_request_toc);
         SocketCommand.protoMap[4662144] = "proto.line.m_squad_lineup_get_toc";
         ClassUtils.regClass("proto.line.m_squad_lineup_get_toc",m_squad_lineup_get_toc);
         SocketCommand.protoMap[1313281] = "proto.line.m_broadcast_show_toc";
         ClassUtils.regClass("proto.line.m_broadcast_show_toc",m_broadcast_show_toc);
         SocketCommand.protoMap[8798979] = "proto.line.m_soul_hero_unlock_toc";
         ClassUtils.regClass("proto.line.m_soul_hero_unlock_toc",m_soul_hero_unlock_toc);
         SocketCommand.protoMap[8142356] = "proto.line.m_modular_activity_war_log_update_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_war_log_update_toc",m_modular_activity_war_log_update_toc);
         SocketCommand.protoMap[6106752] = "proto.line.m_family_science_info_toc";
         ClassUtils.regClass("proto.line.m_family_science_info_toc",m_family_science_info_toc);
         SocketCommand.protoMap[9849601] = "proto.line.m_stage_breed_cost_toc";
         ClassUtils.regClass("proto.line.m_stage_breed_cost_toc",m_stage_breed_cost_toc);
         SocketCommand.protoMap[10374912] = "proto.line.m_story_tower_battle_info_toc";
         ClassUtils.regClass("proto.line.m_story_tower_battle_info_toc",m_story_tower_battle_info_toc);
         SocketCommand.protoMap[6894720] = "proto.line.m_progress_gift_info_toc";
         ClassUtils.regClass("proto.line.m_progress_gift_info_toc",m_progress_gift_info_toc);
         SocketCommand.protoMap[11097217] = "proto.line.m_retrieval_info_toc";
         ClassUtils.regClass("proto.line.m_retrieval_info_toc",m_retrieval_info_toc);
         SocketCommand.protoMap[3742850] = "proto.line.m_fuli_sign_acc_toc";
         ClassUtils.regClass("proto.line.m_fuli_sign_acc_toc",m_fuli_sign_acc_toc);
         SocketCommand.protoMap[3742851] = "proto.line.m_fuli_yueka_info_toc";
         ClassUtils.regClass("proto.line.m_fuli_yueka_info_toc",m_fuli_yueka_info_toc);
         SocketCommand.protoMap[2692228] = "proto.line.m_fight_finish_times_toc";
         ClassUtils.regClass("proto.line.m_fight_finish_times_toc",m_fight_finish_times_toc);
         SocketCommand.protoMap[3742853] = "proto.line.m_fuli_fund_info_toc";
         ClassUtils.regClass("proto.line.m_fuli_fund_info_toc",m_fuli_fund_info_toc);
         SocketCommand.protoMap[2692230] = "proto.line.m_fight_start_pass_toc";
         ClassUtils.regClass("proto.line.m_fight_start_pass_toc",m_fight_start_pass_toc);
         SocketCommand.protoMap[2692231] = "proto.line.m_fight_times_toc";
         ClassUtils.regClass("proto.line.m_fight_times_toc",m_fight_times_toc);
         SocketCommand.protoMap[7945352] = "proto.line.m_maze_pub_toc";
         ClassUtils.regClass("proto.line.m_maze_pub_toc",m_maze_pub_toc);
         SocketCommand.protoMap[7945353] = "proto.line.m_maze_monster_toc";
         ClassUtils.regClass("proto.line.m_maze_monster_toc",m_maze_monster_toc);
         SocketCommand.protoMap[7945354] = "proto.line.m_maze_fight_result_toc";
         ClassUtils.regClass("proto.line.m_maze_fight_result_toc",m_maze_fight_result_toc);
         SocketCommand.protoMap[7945355] = "proto.line.m_maze_use_item_toc";
         ClassUtils.regClass("proto.line.m_maze_use_item_toc",m_maze_use_item_toc);
         SocketCommand.protoMap[787971] = "proto.line.m_friend_agree_toc";
         ClassUtils.regClass("proto.line.m_friend_agree_toc",m_friend_agree_toc);
         SocketCommand.protoMap[3151874] = "proto.line.m_travel_accept_toc";
         ClassUtils.regClass("proto.line.m_travel_accept_toc",m_travel_accept_toc);
         SocketCommand.protoMap[7617028] = "proto.line.m_theme_activity_famous_lottery_update_toc";
         ClassUtils.regClass("proto.line.m_theme_activity_famous_lottery_update_toc",m_theme_activity_famous_lottery_update_toc);
         SocketCommand.protoMap[2363904] = "proto.line.m_shop_info_toc";
         ClassUtils.regClass("proto.line.m_shop_info_toc",m_shop_info_toc);
         SocketCommand.protoMap[2823552] = "proto.line.m_hero_convert_info_toc";
         ClassUtils.regClass("proto.line.m_hero_convert_info_toc",m_hero_convert_info_toc);
         SocketCommand.protoMap[6106753] = "proto.line.m_family_science_op_toc";
         ClassUtils.regClass("proto.line.m_family_science_op_toc",m_family_science_op_toc);
         SocketCommand.protoMap[9849602] = "proto.line.m_stage_breed_upgrade_toc";
         ClassUtils.regClass("proto.line.m_stage_breed_upgrade_toc",m_stage_breed_upgrade_toc);
         SocketCommand.protoMap[10374913] = "proto.line.m_story_tower_battle_fetch_toc";
         ClassUtils.regClass("proto.line.m_story_tower_battle_fetch_toc",m_story_tower_battle_fetch_toc);
         SocketCommand.protoMap[10900224] = "proto.line.m_day_acc_pay_gift_info_toc";
         ClassUtils.regClass("proto.line.m_day_acc_pay_gift_info_toc",m_day_acc_pay_gift_info_toc);
         SocketCommand.protoMap[787972] = "proto.line.m_friend_operate_toc";
         ClassUtils.regClass("proto.line.m_friend_operate_toc",m_friend_operate_toc);
         SocketCommand.protoMap[2889216] = "proto.line.m_daily_copy_process_toc";
         ClassUtils.regClass("proto.line.m_daily_copy_process_toc",m_daily_copy_process_toc);
         SocketCommand.protoMap[10571912] = "proto.line.m_lord_skill_attack_inc_calc_toc";
         ClassUtils.regClass("proto.line.m_lord_skill_attack_inc_calc_toc",m_lord_skill_attack_inc_calc_toc);
         SocketCommand.protoMap[6632065] = "proto.line.m_time_achievement_update_toc";
         ClassUtils.regClass("proto.line.m_time_achievement_update_toc",m_time_achievement_update_toc);
         SocketCommand.protoMap[7157376] = "proto.line.m_hero_come_info_toc";
         ClassUtils.regClass("proto.line.m_hero_come_info_toc",m_hero_come_info_toc);
         SocketCommand.protoMap[11425536] = "proto.line.m_divine_equip_activate_hero_toc";
         ClassUtils.regClass("proto.line.m_divine_equip_activate_hero_toc",m_divine_equip_activate_hero_toc);
         SocketCommand.protoMap[2035584] = "proto.line.m_hint_list_toc";
         ClassUtils.regClass("proto.line.m_hint_list_toc",m_hint_list_toc);
         SocketCommand.protoMap[787973] = "proto.line.m_friend_find_toc";
         ClassUtils.regClass("proto.line.m_friend_find_toc",m_friend_find_toc);
         SocketCommand.protoMap[2889217] = "proto.line.m_daily_copy_enter_toc";
         ClassUtils.regClass("proto.line.m_daily_copy_enter_toc",m_daily_copy_enter_toc);
         SocketCommand.protoMap[6632066] = "proto.line.m_time_achievement_share_toc";
         ClassUtils.regClass("proto.line.m_time_achievement_share_toc",m_time_achievement_share_toc);
         SocketCommand.protoMap[11425537] = "proto.line.m_divine_equip_info_toc";
         ClassUtils.regClass("proto.line.m_divine_equip_info_toc",m_divine_equip_info_toc);
         SocketCommand.protoMap[8142336] = "proto.line.m_modular_activity_list_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_list_toc",m_modular_activity_list_toc);
         SocketCommand.protoMap[7091721] = "proto.line.m_qxzl_info_last_battle_toc";
         ClassUtils.regClass("proto.line.m_qxzl_info_last_battle_toc",m_qxzl_info_last_battle_toc);
         SocketCommand.protoMap[5121794] = "proto.line.m_hero_skin_active_toc";
         ClassUtils.regClass("proto.line.m_hero_skin_active_toc",m_hero_skin_active_toc);
         SocketCommand.protoMap[3414529] = "proto.line.m_online_reward_fetch_toc";
         ClassUtils.regClass("proto.line.m_online_reward_fetch_toc",m_online_reward_fetch_toc);
         SocketCommand.protoMap[3939840] = "proto.line.m_month_fund_info_toc";
         ClassUtils.regClass("proto.line.m_month_fund_info_toc",m_month_fund_info_toc);
         SocketCommand.protoMap[8208000] = "proto.line.m_general_info_toc";
         ClassUtils.regClass("proto.line.m_general_info_toc",m_general_info_toc);
         SocketCommand.protoMap[11162880] = "proto.line.m_stage_skill_info_toc";
         ClassUtils.regClass("proto.line.m_stage_skill_info_toc",m_stage_skill_info_toc);
         SocketCommand.protoMap[6960385] = "proto.line.m_family_active_up_toc";
         ClassUtils.regClass("proto.line.m_family_active_up_toc",m_family_active_up_toc);
         SocketCommand.protoMap[656642] = "proto.line.m_login_relogin_toc";
         ClassUtils.regClass("proto.line.m_login_relogin_toc",m_login_relogin_toc);
         SocketCommand.protoMap[656643] = "proto.line.m_login_gen_name_toc";
         ClassUtils.regClass("proto.line.m_login_gen_name_toc",m_login_gen_name_toc);
         SocketCommand.protoMap[656644] = "proto.line.m_login_auth_toc";
         ClassUtils.regClass("proto.line.m_login_auth_toc",m_login_auth_toc);
         SocketCommand.protoMap[2757893] = "proto.line.m_lottery_supreme_info_toc";
         ClassUtils.regClass("proto.line.m_lottery_supreme_info_toc",m_lottery_supreme_info_toc);
         SocketCommand.protoMap[10112262] = "proto.line.m_boat_race_broadcast_toc";
         ClassUtils.regClass("proto.line.m_boat_race_broadcast_toc",m_boat_race_broadcast_toc);
         SocketCommand.protoMap[7814017] = "proto.line.m_login_activity_fetch_toc";
         ClassUtils.regClass("proto.line.m_login_activity_fetch_toc",m_login_activity_fetch_toc);
         SocketCommand.protoMap[5909768] = "proto.line.m_test_tower_auto_sweep_toc";
         ClassUtils.regClass("proto.line.m_test_tower_auto_sweep_toc",m_test_tower_auto_sweep_toc);
         SocketCommand.protoMap[8011017] = "proto.line.m_hzzd_info_bet_toc";
         ClassUtils.regClass("proto.line.m_hzzd_info_bet_toc",m_hzzd_info_bet_toc);
         SocketCommand.protoMap[8011018] = "proto.line.m_hzzd_route_select_info_toc";
         ClassUtils.regClass("proto.line.m_hzzd_route_select_info_toc",m_hzzd_route_select_info_toc);
         SocketCommand.protoMap[656651] = "proto.line.m_login_re_connect_toc";
         ClassUtils.regClass("proto.line.m_login_re_connect_toc",m_login_re_connect_toc);
         SocketCommand.protoMap[8011020] = "proto.line.m_hzzd_look_kill_toc";
         ClassUtils.regClass("proto.line.m_hzzd_look_kill_toc",m_hzzd_look_kill_toc);
         SocketCommand.protoMap[1510274] = "proto.line.m_ranking_worship_toc";
         ClassUtils.regClass("proto.line.m_ranking_worship_toc",m_ranking_worship_toc);
         SocketCommand.protoMap[11425539] = "proto.line.m_divine_equip_op_toc";
         ClassUtils.regClass("proto.line.m_divine_equip_op_toc",m_divine_equip_op_toc);
         SocketCommand.protoMap[2560899] = "proto.line.m_god_equip_convert_toc";
         ClassUtils.regClass("proto.line.m_god_equip_convert_toc",m_god_equip_convert_toc);
         SocketCommand.protoMap[4465152] = "proto.line.m_family_hongbao_info_toc";
         ClassUtils.regClass("proto.line.m_family_hongbao_info_toc",m_family_hongbao_info_toc);
         SocketCommand.protoMap[3874177] = "proto.line.m_first_pay_fetch_toc";
         ClassUtils.regClass("proto.line.m_first_pay_fetch_toc",m_first_pay_fetch_toc);
         SocketCommand.protoMap[9915268] = "proto.line.m_stage_copy_set_toc";
         ClassUtils.regClass("proto.line.m_stage_copy_set_toc",m_stage_copy_set_toc);
         SocketCommand.protoMap[8733312] = "proto.line.m_ingenious_plan_info_toc";
         ClassUtils.regClass("proto.line.m_ingenious_plan_info_toc",m_ingenious_plan_info_toc);
         SocketCommand.protoMap[10965893] = "proto.line.m_td_main_info_toc";
         ClassUtils.regClass("proto.line.m_td_main_info_toc",m_td_main_info_toc);
         SocketCommand.protoMap[8142384] = "proto.line.m_modular_activity_dice_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_dice_info_toc",m_modular_activity_dice_info_toc);
         SocketCommand.protoMap[8864646] = "proto.line.m_medal_retire_update_toc";
         ClassUtils.regClass("proto.line.m_medal_retire_update_toc",m_medal_retire_update_toc);
         SocketCommand.protoMap[8142385] = "proto.line.m_modular_activity_dice_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_dice_toc",m_modular_activity_dice_toc);
         SocketCommand.protoMap[8142386] = "proto.line.m_modular_activity_dice_unlock_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_dice_unlock_toc",m_modular_activity_dice_unlock_toc);
         SocketCommand.protoMap[722304] = "proto.line.m_common_error_toc";
         ClassUtils.regClass("proto.line.m_common_error_toc",m_common_error_toc);
         SocketCommand.protoMap[7091719] = "proto.line.m_qxzl_info_history_toc";
         ClassUtils.regClass("proto.line.m_qxzl_info_history_toc",m_qxzl_info_history_toc);
         SocketCommand.protoMap[3414528] = "proto.line.m_online_reward_info_toc";
         ClassUtils.regClass("proto.line.m_online_reward_info_toc",m_online_reward_info_toc);
         SocketCommand.protoMap[4990464] = "proto.line.m_qqvip_info_toc";
         ClassUtils.regClass("proto.line.m_qqvip_info_toc",m_qqvip_info_toc);
         SocketCommand.protoMap[1247626] = "proto.line.m_family_set_title_toc";
         ClassUtils.regClass("proto.line.m_family_set_title_toc",m_family_set_title_toc);
         SocketCommand.protoMap[9258624] = "proto.line.m_sdk_reward_info_toc";
         ClassUtils.regClass("proto.line.m_sdk_reward_info_toc",m_sdk_reward_info_toc);
         SocketCommand.protoMap[10506247] = "proto.line.m_treasure_use_item_toc";
         ClassUtils.regClass("proto.line.m_treasure_use_item_toc",m_treasure_use_item_toc);
         SocketCommand.protoMap[1510273] = "proto.line.m_ranking_reward_toc";
         ClassUtils.regClass("proto.line.m_ranking_reward_toc",m_ranking_reward_toc);
         SocketCommand.protoMap[3939843] = "proto.line.m_month_fund_update_toc";
         ClassUtils.regClass("proto.line.m_month_fund_update_toc",m_month_fund_update_toc);
         SocketCommand.protoMap[4465154] = "proto.line.m_family_hongbao_fetch_toc";
         ClassUtils.regClass("proto.line.m_family_hongbao_fetch_toc",m_family_hongbao_fetch_toc);
         SocketCommand.protoMap[4990465] = "proto.line.m_qqvip_fetch_toc";
         ClassUtils.regClass("proto.line.m_qqvip_fetch_toc",m_qqvip_fetch_toc);
         SocketCommand.protoMap[5515776] = "proto.line.m_share_single_info_toc";
         ClassUtils.regClass("proto.line.m_share_single_info_toc",m_share_single_info_toc);
         SocketCommand.protoMap[8601986] = "proto.line.m_xswh_rank_toc";
         ClassUtils.regClass("proto.line.m_xswh_rank_toc",m_xswh_rank_toc);
         SocketCommand.protoMap[9783936] = "proto.line.m_playing_preview_info_toc";
         ClassUtils.regClass("proto.line.m_playing_preview_info_toc",m_playing_preview_info_toc);
         SocketCommand.protoMap[7879685] = "proto.line.m_random_pvp_logs_toc";
         ClassUtils.regClass("proto.line.m_random_pvp_logs_toc",m_random_pvp_logs_toc);
         SocketCommand.protoMap[7157378] = "proto.line.m_hero_come_update_toc";
         ClassUtils.regClass("proto.line.m_hero_come_update_toc",m_hero_come_update_toc);
         SocketCommand.protoMap[722306] = "proto.line.m_common_show_attr_toc";
         ClassUtils.regClass("proto.line.m_common_show_attr_toc",m_common_show_attr_toc);
         SocketCommand.protoMap[8142397] = "proto.line.m_modular_activity_story_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_story_info_toc",m_modular_activity_story_info_toc);
         SocketCommand.protoMap[4465155] = "proto.line.m_family_hongbao_info_update_toc";
         ClassUtils.regClass("proto.line.m_family_hongbao_info_update_toc",m_family_hongbao_info_update_toc);
         SocketCommand.protoMap[8733315] = "proto.line.m_ingenious_plan_star_toc";
         ClassUtils.regClass("proto.line.m_ingenious_plan_star_toc",m_ingenious_plan_star_toc);
         SocketCommand.protoMap[6041088] = "proto.line.m_family_sign_info_toc";
         ClassUtils.regClass("proto.line.m_family_sign_info_toc",m_family_sign_info_toc);
         SocketCommand.protoMap[5844097] = "proto.line.m_ares_palace_log_toc";
         ClassUtils.regClass("proto.line.m_ares_palace_log_toc",m_ares_palace_log_toc);
         SocketCommand.protoMap[9783937] = "proto.line.m_playing_preview_award_toc";
         ClassUtils.regClass("proto.line.m_playing_preview_award_toc",m_playing_preview_award_toc);
         SocketCommand.protoMap[10309248] = "proto.line.m_story_siegelord_info_toc";
         ClassUtils.regClass("proto.line.m_story_siegelord_info_toc",m_story_siegelord_info_toc);
         SocketCommand.protoMap[1772928] = "proto.line.m_payment_shop_toc";
         ClassUtils.regClass("proto.line.m_payment_shop_toc",m_payment_shop_toc);
         SocketCommand.protoMap[1772929] = "proto.line.m_payment_request_toc";
         ClassUtils.regClass("proto.line.m_payment_request_toc",m_payment_request_toc);
         SocketCommand.protoMap[7026050] = "proto.line.m_worship_do_toc";
         ClassUtils.regClass("proto.line.m_worship_do_toc",m_worship_do_toc);
         SocketCommand.protoMap[8470656] = "proto.line.m_hero_strengthen_info_toc";
         ClassUtils.regClass("proto.line.m_hero_strengthen_info_toc",m_hero_strengthen_info_toc);
         SocketCommand.protoMap[1772932] = "proto.line.m_payment_buy_toc";
         ClassUtils.regClass("proto.line.m_payment_buy_toc",m_payment_buy_toc);
         SocketCommand.protoMap[1772933] = "proto.line.m_payment_shop_info_toc";
         ClassUtils.regClass("proto.line.m_payment_shop_info_toc",m_payment_shop_info_toc);
         SocketCommand.protoMap[1772934] = "proto.line.m_payment_auto_buy_toc";
         ClassUtils.regClass("proto.line.m_payment_auto_buy_toc",m_payment_auto_buy_toc);
         SocketCommand.protoMap[2495257] = "proto.line.m_deputy_op_toc";
         ClassUtils.regClass("proto.line.m_deputy_op_toc",m_deputy_op_toc);
         SocketCommand.protoMap[1247618] = "proto.line.m_family_members_toc";
         ClassUtils.regClass("proto.line.m_family_members_toc",m_family_members_toc);
         SocketCommand.protoMap[2298240] = "proto.line.m_tequan_info_toc";
         ClassUtils.regClass("proto.line.m_tequan_info_toc",m_tequan_info_toc);
         SocketCommand.protoMap[8142403] = "proto.line.m_modular_activity_customized_gift_select_list_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_customized_gift_select_list_toc",m_modular_activity_customized_gift_select_list_toc);
         SocketCommand.protoMap[6041089] = "proto.line.m_family_sign_fetch_toc";
         ClassUtils.regClass("proto.line.m_family_sign_fetch_toc",m_family_sign_fetch_toc);
         SocketCommand.protoMap[6566400] = "proto.line.m_hero_handbook_list_toc";
         ClassUtils.regClass("proto.line.m_hero_handbook_list_toc",m_hero_handbook_list_toc);
         SocketCommand.protoMap[6369410] = "proto.line.m_red_cliff_fight_result_toc";
         ClassUtils.regClass("proto.line.m_red_cliff_fight_result_toc",m_red_cliff_fight_result_toc);
         SocketCommand.protoMap[5778436] = "proto.line.m_god_trial_fight_result_toc";
         ClassUtils.regClass("proto.line.m_god_trial_fight_result_toc",m_god_trial_fight_result_toc);
         SocketCommand.protoMap[5778433] = "proto.line.m_god_trial_fetch_toc";
         ClassUtils.regClass("proto.line.m_god_trial_fetch_toc",m_god_trial_fetch_toc);
         SocketCommand.protoMap[590748] = "proto.line.m_chat_clean_toc";
         ClassUtils.regClass("proto.line.m_chat_clean_toc",m_chat_clean_toc);
         SocketCommand.protoMap[1247619] = "proto.line.m_family_self_toc";
         ClassUtils.regClass("proto.line.m_family_self_toc",m_family_self_toc);
         SocketCommand.protoMap[6041090] = "proto.line.m_family_sign_do_toc";
         ClassUtils.regClass("proto.line.m_family_sign_do_toc",m_family_sign_do_toc);
         SocketCommand.protoMap[10637572] = "proto.line.m_csclan_set_title_toc";
         ClassUtils.regClass("proto.line.m_csclan_set_title_toc",m_csclan_set_title_toc);
         SocketCommand.protoMap[7091712] = "proto.line.m_qxzl_info_toc";
         ClassUtils.regClass("proto.line.m_qxzl_info_toc",m_qxzl_info_toc);
         SocketCommand.protoMap[10834561] = "proto.line.m_fish_fishbowl_attr_toc";
         ClassUtils.regClass("proto.line.m_fish_fishbowl_attr_toc",m_fish_fishbowl_attr_toc);
         SocketCommand.protoMap[11359872] = "proto.line.m_large_peak_info_toc";
         ClassUtils.regClass("proto.line.m_large_peak_info_toc",m_large_peak_info_toc);
         SocketCommand.protoMap[4202497] = "proto.line.m_page_switch_toc";
         ClassUtils.regClass("proto.line.m_page_switch_toc",m_page_switch_toc);
         SocketCommand.protoMap[6566402] = "proto.line.m_hero_handbook_all_fetch_toc";
         ClassUtils.regClass("proto.line.m_hero_handbook_all_fetch_toc",m_hero_handbook_all_fetch_toc);
         SocketCommand.protoMap[4465158] = "proto.line.m_family_hongbao_mission_send_toc";
         ClassUtils.regClass("proto.line.m_family_hongbao_mission_send_toc",m_family_hongbao_mission_send_toc);
         SocketCommand.protoMap[4530816] = "proto.line.m_fuli_token_info_toc";
         ClassUtils.regClass("proto.line.m_fuli_token_info_toc",m_fuli_token_info_toc);
         SocketCommand.protoMap[1772931] = "proto.line.m_payment_pay_result_toc";
         ClassUtils.regClass("proto.line.m_payment_pay_result_toc",m_payment_pay_result_toc);
         SocketCommand.protoMap[3348864] = "proto.line.m_hanging_info_toc";
         ClassUtils.regClass("proto.line.m_hanging_info_toc",m_hanging_info_toc);
         SocketCommand.protoMap[7091713] = "proto.line.m_qxzl_info_opp_toc";
         ClassUtils.regClass("proto.line.m_qxzl_info_opp_toc",m_qxzl_info_opp_toc);
         SocketCommand.protoMap[8142414] = "proto.line.m_modular_activity_six_bless_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_six_bless_info_toc",m_modular_activity_six_bless_info_toc);
         SocketCommand.protoMap[7617024] = "proto.line.m_theme_activity_skin_info_toc";
         ClassUtils.regClass("proto.line.m_theme_activity_skin_info_toc",m_theme_activity_skin_info_toc);
         SocketCommand.protoMap[11359873] = "proto.line.m_large_peak_info_opp_toc";
         ClassUtils.regClass("proto.line.m_large_peak_info_opp_toc",m_large_peak_info_opp_toc);
         SocketCommand.protoMap[11885184] = "proto.line.m_zero_buy_op_toc";
         ClassUtils.regClass("proto.line.m_zero_buy_op_toc",m_zero_buy_op_toc);
         SocketCommand.protoMap[1247621] = "proto.line.m_family_uplevel_toc";
         ClassUtils.regClass("proto.line.m_family_uplevel_toc",m_family_uplevel_toc);
         SocketCommand.protoMap[3348865] = "proto.line.m_hanging_reward_toc";
         ClassUtils.regClass("proto.line.m_hanging_reward_toc",m_hanging_reward_toc);
         SocketCommand.protoMap[7091714] = "proto.line.m_qxzl_info_member_toc";
         ClassUtils.regClass("proto.line.m_qxzl_info_member_toc",m_qxzl_info_member_toc);
         SocketCommand.protoMap[7617025] = "proto.line.m_theme_activity_skin_lottery_toc";
         ClassUtils.regClass("proto.line.m_theme_activity_skin_lottery_toc",m_theme_activity_skin_lottery_toc);
         SocketCommand.protoMap[11359874] = "proto.line.m_large_peak_info_member_toc";
         ClassUtils.regClass("proto.line.m_large_peak_info_member_toc",m_large_peak_info_member_toc);
         SocketCommand.protoMap[10046593] = "proto.line.m_hero_zhouyin_upgrade_toc";
         ClassUtils.regClass("proto.line.m_hero_zhouyin_upgrade_toc",m_hero_zhouyin_upgrade_toc);
         SocketCommand.protoMap[9192960] = "proto.line.m_hero_cost_info_toc";
         ClassUtils.regClass("proto.line.m_hero_cost_info_toc",m_hero_cost_info_toc);
         SocketCommand.protoMap[787969] = "proto.line.m_friend_refresh_toc";
         ClassUtils.regClass("proto.line.m_friend_refresh_toc",m_friend_refresh_toc);
         SocketCommand.protoMap[8142338] = "proto.line.m_modular_activity_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_info_toc",m_modular_activity_info_toc);
         SocketCommand.protoMap[7091715] = "proto.line.m_qxzl_info_look_member_toc";
         ClassUtils.regClass("proto.line.m_qxzl_info_look_member_toc",m_qxzl_info_look_member_toc);
         SocketCommand.protoMap[7091716] = "proto.line.m_qxzl_info_battle_toc";
         ClassUtils.regClass("proto.line.m_qxzl_info_battle_toc",m_qxzl_info_battle_toc);
         SocketCommand.protoMap[11294213] = "proto.line.m_dominate_pvp_fight_result_toc";
         ClassUtils.regClass("proto.line.m_dominate_pvp_fight_result_toc",m_dominate_pvp_fight_result_toc);
         SocketCommand.protoMap[787974] = "proto.line.m_friend_give_toc";
         ClassUtils.regClass("proto.line.m_friend_give_toc",m_friend_give_toc);
         SocketCommand.protoMap[2232577] = "proto.line.m_acc_pay_fetch_toc";
         ClassUtils.regClass("proto.line.m_acc_pay_fetch_toc",m_acc_pay_fetch_toc);
         SocketCommand.protoMap[787976] = "proto.line.m_friend_online_toc";
         ClassUtils.regClass("proto.line.m_friend_online_toc",m_friend_online_toc);
         SocketCommand.protoMap[10243593] = "proto.line.m_story_maze_use_item_toc";
         ClassUtils.regClass("proto.line.m_story_maze_use_item_toc",m_story_maze_use_item_toc);
         SocketCommand.protoMap[8142347] = "proto.line.m_modular_activity_lottery_tips_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_lottery_tips_toc",m_modular_activity_lottery_tips_toc);
         SocketCommand.protoMap[10243596] = "proto.line.m_story_maze_report_toc";
         ClassUtils.regClass("proto.line.m_story_maze_report_toc",m_story_maze_report_toc);
         SocketCommand.protoMap[5909762] = "proto.line.m_test_tower_into_floor_toc";
         ClassUtils.regClass("proto.line.m_test_tower_into_floor_toc",m_test_tower_into_floor_toc);
         SocketCommand.protoMap[8142350] = "proto.line.m_modular_activity_star_plan_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_star_plan_info_toc",m_modular_activity_star_plan_info_toc);
         SocketCommand.protoMap[8142351] = "proto.line.m_modular_activity_star_plan_update_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_star_plan_update_toc",m_modular_activity_star_plan_update_toc);
         SocketCommand.protoMap[8142352] = "proto.line.m_modular_activity_general_pass_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_general_pass_info_toc",m_modular_activity_general_pass_info_toc);
         SocketCommand.protoMap[8142353] = "proto.line.m_modular_activity_general_pass_fetch_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_general_pass_fetch_toc",m_modular_activity_general_pass_fetch_toc);
         SocketCommand.protoMap[8142354] = "proto.line.m_modular_activity_star_repay_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_star_repay_info_toc",m_modular_activity_star_repay_info_toc);
         SocketCommand.protoMap[5909763] = "proto.line.m_test_tower_fight_result_toc";
         ClassUtils.regClass("proto.line.m_test_tower_fight_result_toc",m_test_tower_fight_result_toc);
         SocketCommand.protoMap[8142337] = "proto.line.m_modular_activity_fetch_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_fetch_toc",m_modular_activity_fetch_toc);
         SocketCommand.protoMap[8142357] = "proto.line.m_modular_activity_maze_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_maze_info_toc",m_modular_activity_maze_info_toc);
         SocketCommand.protoMap[8142359] = "proto.line.m_modular_activity_maze_start_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_maze_start_toc",m_modular_activity_maze_start_toc);
         SocketCommand.protoMap[8142360] = "proto.line.m_modular_activity_maze_spoils_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_maze_spoils_toc",m_modular_activity_maze_spoils_toc);
         SocketCommand.protoMap[8142361] = "proto.line.m_modular_activity_maze_fetch_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_maze_fetch_toc",m_modular_activity_maze_fetch_toc);
         SocketCommand.protoMap[8142364] = "proto.line.m_modular_activity_maze_hero_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_maze_hero_toc",m_modular_activity_maze_hero_toc);
         SocketCommand.protoMap[8142365] = "proto.line.m_modular_activity_maze_pub_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_maze_pub_toc",m_modular_activity_maze_pub_toc);
         SocketCommand.protoMap[8142366] = "proto.line.m_modular_activity_maze_monster_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_maze_monster_toc",m_modular_activity_maze_monster_toc);
         SocketCommand.protoMap[8142367] = "proto.line.m_modular_activity_maze_fight_result_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_maze_fight_result_toc",m_modular_activity_maze_fight_result_toc);
         SocketCommand.protoMap[8142368] = "proto.line.m_modular_activity_maze_use_item_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_maze_use_item_toc",m_modular_activity_maze_use_item_toc);
         SocketCommand.protoMap[8142371] = "proto.line.m_modular_activity_report_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_report_toc",m_modular_activity_report_toc);
         SocketCommand.protoMap[8142372] = "proto.line.m_modular_activity_maze_auto_rolling_start_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_maze_auto_rolling_start_toc",m_modular_activity_maze_auto_rolling_start_toc);
         SocketCommand.protoMap[8142373] = "proto.line.m_modular_activity_maze_auto_rolling_end_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_maze_auto_rolling_end_toc",m_modular_activity_maze_auto_rolling_end_toc);
         SocketCommand.protoMap[8142374] = "proto.line.m_modular_activity_seven_goal_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_seven_goal_info_toc",m_modular_activity_seven_goal_info_toc);
         SocketCommand.protoMap[8142375] = "proto.line.m_modular_activity_seven_goal_update_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_seven_goal_update_toc",m_modular_activity_seven_goal_update_toc);
         SocketCommand.protoMap[8142376] = "proto.line.m_modular_activity_seven_goal_fetch_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_seven_goal_fetch_toc",m_modular_activity_seven_goal_fetch_toc);
         SocketCommand.protoMap[8142377] = "proto.line.m_modular_activity_seven_goal_gift_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_seven_goal_gift_toc",m_modular_activity_seven_goal_gift_toc);
         SocketCommand.protoMap[8142378] = "proto.line.m_modular_activity_hunt_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_hunt_info_toc",m_modular_activity_hunt_info_toc);
         SocketCommand.protoMap[8142379] = "proto.line.m_modular_activity_hunt_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_hunt_toc",m_modular_activity_hunt_toc);
         SocketCommand.protoMap[8142380] = "proto.line.m_modular_activity_hunt_logs_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_hunt_logs_toc",m_modular_activity_hunt_logs_toc);
         SocketCommand.protoMap[8142381] = "proto.line.m_modular_activity_hunt_refresh_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_hunt_refresh_toc",m_modular_activity_hunt_refresh_toc);
         SocketCommand.protoMap[8142382] = "proto.line.m_modular_activity_carnival_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_carnival_toc",m_modular_activity_carnival_toc);
         SocketCommand.protoMap[8142383] = "proto.line.m_modular_activity_login_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_login_info_toc",m_modular_activity_login_info_toc);
         SocketCommand.protoMap[7617027] = "proto.line.m_theme_activity_famous_lottery_info_toc";
         ClassUtils.regClass("proto.line.m_theme_activity_famous_lottery_info_toc",m_theme_activity_famous_lottery_info_toc);
         SocketCommand.protoMap[8011016] = "proto.line.m_hzzd_info_opp_toc";
         ClassUtils.regClass("proto.line.m_hzzd_info_opp_toc",m_hzzd_info_opp_toc);
         SocketCommand.protoMap[4924800] = "proto.line.m_daily_gift_info_toc";
         ClassUtils.regClass("proto.line.m_daily_gift_info_toc",m_daily_gift_info_toc);
         SocketCommand.protoMap[8142387] = "proto.line.m_modular_activity_dice_boss_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_dice_boss_toc",m_modular_activity_dice_boss_toc);
         SocketCommand.protoMap[8142388] = "proto.line.m_modular_activity_holiday_welfare_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_holiday_welfare_info_toc",m_modular_activity_holiday_welfare_info_toc);
         SocketCommand.protoMap[8142389] = "proto.line.m_modular_activity_holiday_welfare_update_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_holiday_welfare_update_toc",m_modular_activity_holiday_welfare_update_toc);
         SocketCommand.protoMap[8142390] = "proto.line.m_modular_activity_sign_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_sign_info_toc",m_modular_activity_sign_info_toc);
         SocketCommand.protoMap[8142391] = "proto.line.m_modular_activity_sign_update_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_sign_update_toc",m_modular_activity_sign_update_toc);
         SocketCommand.protoMap[8142392] = "proto.line.m_modular_activity_hero_challenge_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_hero_challenge_info_toc",m_modular_activity_hero_challenge_info_toc);
         SocketCommand.protoMap[8142394] = "proto.line.m_modular_activity_hero_challenge_report_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_hero_challenge_report_toc",m_modular_activity_hero_challenge_report_toc);
         SocketCommand.protoMap[8142395] = "proto.line.m_modular_activity_preview_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_preview_toc",m_modular_activity_preview_toc);
         SocketCommand.protoMap[8142396] = "proto.line.m_modular_activity_preview_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_preview_info_toc",m_modular_activity_preview_info_toc);
         SocketCommand.protoMap[10112266] = "proto.line.m_boat_race_role_items_toc";
         ClassUtils.regClass("proto.line.m_boat_race_role_items_toc",m_boat_race_role_items_toc);
         SocketCommand.protoMap[8142399] = "proto.line.m_modular_activity_story_group_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_story_group_toc",m_modular_activity_story_group_toc);
         SocketCommand.protoMap[8142400] = "proto.line.m_modular_activity_heaven_give_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_heaven_give_info_toc",m_modular_activity_heaven_give_info_toc);
         SocketCommand.protoMap[8142401] = "proto.line.m_modular_activity_customized_gift_select_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_customized_gift_select_toc",m_modular_activity_customized_gift_select_toc);
         SocketCommand.protoMap[8011019] = "proto.line.m_hzzd_battle_info_toc";
         ClassUtils.regClass("proto.line.m_hzzd_battle_info_toc",m_hzzd_battle_info_toc);
         SocketCommand.protoMap[8142404] = "proto.line.m_modular_activity_brick_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_brick_info_toc",m_modular_activity_brick_info_toc);
         SocketCommand.protoMap[8142405] = "proto.line.m_modular_activity_brick_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_brick_toc",m_modular_activity_brick_toc);
         SocketCommand.protoMap[8142406] = "proto.line.m_modular_activity_bless_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_bless_info_toc",m_modular_activity_bless_info_toc);
         SocketCommand.protoMap[8142407] = "proto.line.m_modular_activity_target_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_target_info_toc",m_modular_activity_target_info_toc);
         SocketCommand.protoMap[8142408] = "proto.line.m_modular_activity_wall_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_wall_info_toc",m_modular_activity_wall_info_toc);
         SocketCommand.protoMap[8142409] = "proto.line.m_modular_activity_exchange_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_exchange_info_toc",m_modular_activity_exchange_info_toc);
         SocketCommand.protoMap[8142410] = "proto.line.m_modular_activity_strategy_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_strategy_info_toc",m_modular_activity_strategy_info_toc);
         SocketCommand.protoMap[8142411] = "proto.line.m_modular_activity_lottery_target_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_lottery_target_info_toc",m_modular_activity_lottery_target_info_toc);
         SocketCommand.protoMap[656641] = "proto.line.m_login_chose_toc";
         ClassUtils.regClass("proto.line.m_login_chose_toc",m_login_chose_toc);
         SocketCommand.protoMap[8142413] = "proto.line.m_modular_activity_day_shop_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_day_shop_toc",m_modular_activity_day_shop_toc);
         SocketCommand.protoMap[1181952] = "proto.line.m_simp_mission_list_toc";
         ClassUtils.regClass("proto.line.m_simp_mission_list_toc",m_simp_mission_list_toc);
         SocketCommand.protoMap[8142416] = "proto.line.m_modular_activity_festival_wish_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_festival_wish_info_toc",m_modular_activity_festival_wish_info_toc);
         SocketCommand.protoMap[8142417] = "proto.line.m_modular_activity_festival_wish_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_festival_wish_toc",m_modular_activity_festival_wish_toc);
         SocketCommand.protoMap[5450112] = "proto.line.m_sys_concern_info_toc";
         ClassUtils.regClass("proto.line.m_sys_concern_info_toc",m_sys_concern_info_toc);
         SocketCommand.protoMap[9718272] = "proto.line.m_week_target_info_toc";
         ClassUtils.regClass("proto.line.m_week_target_info_toc",m_week_target_info_toc);
         SocketCommand.protoMap[1247625] = "proto.line.m_family_audit_limit_toc";
         ClassUtils.regClass("proto.line.m_family_audit_limit_toc",m_family_audit_limit_toc);
         SocketCommand.protoMap[10506244] = "proto.line.m_treasure_result_toc";
         ClassUtils.regClass("proto.line.m_treasure_result_toc",m_treasure_result_toc);
         SocketCommand.protoMap[7091718] = "proto.line.m_qxzl_info_bet_toc";
         ClassUtils.regClass("proto.line.m_qxzl_info_bet_toc",m_qxzl_info_bet_toc);
         SocketCommand.protoMap[10834567] = "proto.line.m_fish_replace_toc";
         ClassUtils.regClass("proto.line.m_fish_replace_toc",m_fish_replace_toc);
         SocketCommand.protoMap[8142340] = "proto.line.m_modular_activity_update_fetch_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_update_fetch_toc",m_modular_activity_update_fetch_toc);
         SocketCommand.protoMap[1510275] = "proto.line.m_ranking_simple_toc";
         ClassUtils.regClass("proto.line.m_ranking_simple_toc",m_ranking_simple_toc);
         SocketCommand.protoMap[3742849] = "proto.line.m_fuli_sign_fetch_toc";
         ClassUtils.regClass("proto.line.m_fuli_sign_fetch_toc",m_fuli_sign_fetch_toc);
         SocketCommand.protoMap[5975424] = "proto.line.m_pass_behead_info_toc";
         ClassUtils.regClass("proto.line.m_pass_behead_info_toc",m_pass_behead_info_toc);
         SocketCommand.protoMap[11622530] = "proto.line.m_wars_init_personal_toc";
         ClassUtils.regClass("proto.line.m_wars_init_personal_toc",m_wars_init_personal_toc);
         SocketCommand.protoMap[10243584] = "proto.line.m_story_maze_info_toc";
         ClassUtils.regClass("proto.line.m_story_maze_info_toc",m_story_maze_info_toc);
         SocketCommand.protoMap[2954881] = "proto.line.m_tax_silver_fetch_toc";
         ClassUtils.regClass("proto.line.m_tax_silver_fetch_toc",m_tax_silver_fetch_toc);
         SocketCommand.protoMap[5056128] = "proto.line.m_vip_kefu_qq_toc";
         ClassUtils.regClass("proto.line.m_vip_kefu_qq_toc",m_vip_kefu_qq_toc);
         SocketCommand.protoMap[8208001] = "proto.line.m_general_pass_gift_toc";
         ClassUtils.regClass("proto.line.m_general_pass_gift_toc",m_general_pass_gift_toc);
         SocketCommand.protoMap[853634] = "proto.line.m_letter_send_toc";
         ClassUtils.regClass("proto.line.m_letter_send_toc",m_letter_send_toc);
         SocketCommand.protoMap[2954883] = "proto.line.m_tax_forage_fetch_toc";
         ClassUtils.regClass("proto.line.m_tax_forage_fetch_toc",m_tax_forage_fetch_toc);
         SocketCommand.protoMap[11359876] = "proto.line.m_large_peak_info_battle_toc";
         ClassUtils.regClass("proto.line.m_large_peak_info_battle_toc",m_large_peak_info_battle_toc);
         SocketCommand.protoMap[11359878] = "proto.line.m_large_peak_info_bet_toc";
         ClassUtils.regClass("proto.line.m_large_peak_info_bet_toc",m_large_peak_info_bet_toc);
         SocketCommand.protoMap[11359879] = "proto.line.m_large_peak_info_history_toc";
         ClassUtils.regClass("proto.line.m_large_peak_info_history_toc",m_large_peak_info_history_toc);
         SocketCommand.protoMap[11359880] = "proto.line.m_large_peak_personal_info_toc";
         ClassUtils.regClass("proto.line.m_large_peak_personal_info_toc",m_large_peak_personal_info_toc);
         SocketCommand.protoMap[11359883] = "proto.line.m_large_peak_info_get_msg_toc";
         ClassUtils.regClass("proto.line.m_large_peak_info_get_msg_toc",m_large_peak_info_get_msg_toc);
         SocketCommand.protoMap[8142341] = "proto.line.m_modular_activity_shop_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_shop_toc",m_modular_activity_shop_toc);
         SocketCommand.protoMap[2166913] = "proto.line.m_pay_gift_fetch_toc";
         ClassUtils.regClass("proto.line.m_pay_gift_fetch_toc",m_pay_gift_fetch_toc);
         SocketCommand.protoMap[2232576] = "proto.line.m_acc_pay_info_toc";
         ClassUtils.regClass("proto.line.m_acc_pay_info_toc",m_acc_pay_info_toc);
         SocketCommand.protoMap[6500736] = "proto.line.m_guandu_info_toc";
         ClassUtils.regClass("proto.line.m_guandu_info_toc",m_guandu_info_toc);
         SocketCommand.protoMap[10768896] = "proto.line.m_boat_peak_info_toc";
         ClassUtils.regClass("proto.line.m_boat_peak_info_toc",m_boat_peak_info_toc);
         SocketCommand.protoMap[5909765] = "proto.line.m_test_tower_skip_toc";
         ClassUtils.regClass("proto.line.m_test_tower_skip_toc",m_test_tower_skip_toc);
         SocketCommand.protoMap[7091720] = "proto.line.m_qxzl_info_last_rank_toc";
         ClassUtils.regClass("proto.line.m_qxzl_info_last_rank_toc",m_qxzl_info_last_rank_toc);
         SocketCommand.protoMap[8011013] = "proto.line.m_hzzd_team_info_toc";
         ClassUtils.regClass("proto.line.m_hzzd_team_info_toc",m_hzzd_team_info_toc);
         SocketCommand.protoMap[8142342] = "proto.line.m_modular_activity_lottery_start_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_lottery_start_toc",m_modular_activity_lottery_start_toc);
         SocketCommand.protoMap[2757888] = "proto.line.m_lottery_start_toc";
         ClassUtils.regClass("proto.line.m_lottery_start_toc",m_lottery_start_toc);
         SocketCommand.protoMap[6500737] = "proto.line.m_guandu_op_toc";
         ClassUtils.regClass("proto.line.m_guandu_op_toc",m_guandu_op_toc);
         SocketCommand.protoMap[7026048] = "proto.line.m_worship_online_toc";
         ClassUtils.regClass("proto.line.m_worship_online_toc",m_worship_online_toc);
         SocketCommand.protoMap[10768904] = "proto.line.m_boat_peak_shop_toc";
         ClassUtils.regClass("proto.line.m_boat_peak_shop_toc",m_boat_peak_shop_toc);
         SocketCommand.protoMap[10768897] = "proto.line.m_boat_peak_opp_toc";
         ClassUtils.regClass("proto.line.m_boat_peak_opp_toc",m_boat_peak_opp_toc);
         SocketCommand.protoMap[11294208] = "proto.line.m_dominate_pvp_op_toc";
         ClassUtils.regClass("proto.line.m_dominate_pvp_op_toc",m_dominate_pvp_op_toc);
         SocketCommand.protoMap[919296] = "proto.line.m_item_use_toc";
         ClassUtils.regClass("proto.line.m_item_use_toc",m_item_use_toc);
         SocketCommand.protoMap[656645] = "proto.line.m_login_give_name_toc";
         ClassUtils.regClass("proto.line.m_login_give_name_toc",m_login_give_name_toc);
         SocketCommand.protoMap[8142343] = "proto.line.m_modular_activity_famous_lottery_info_toc";
         ClassUtils.regClass("proto.line.m_modular_activity_famous_lottery_info_toc",m_modular_activity_famous_lottery_info_toc);
         SocketCommand.protoMap[2757889] = "proto.line.m_lottery_info_toc";
         ClassUtils.regClass("proto.line.m_lottery_info_toc",m_lottery_info_toc);
         SocketCommand.protoMap[4596480] = "proto.line.m_recommend_lineup_info_toc";
         ClassUtils.regClass("proto.line.m_recommend_lineup_info_toc",m_recommend_lineup_info_toc);
         SocketCommand.protoMap[3283200] = "proto.line.m_title_list_toc";
         ClassUtils.regClass("proto.line.m_title_list_toc",m_title_list_toc);
         SocketCommand.protoMap[7026049] = "proto.line.m_worship_info_toc";
         ClassUtils.regClass("proto.line.m_worship_info_toc",m_worship_info_toc);
         SocketCommand.protoMap[7551360] = "proto.line.m_lcqs_chapter_info_toc";
         ClassUtils.regClass("proto.line.m_lcqs_chapter_info_toc",m_lcqs_chapter_info_toc);
         SocketCommand.protoMap[11294209] = "proto.line.m_dominate_pvp_info_toc";
         ClassUtils.regClass("proto.line.m_dominate_pvp_info_toc",m_dominate_pvp_info_toc);
         SocketCommand.protoMap[11819520] = "proto.line.m_building_info_toc";
         ClassUtils.regClass("proto.line.m_building_info_toc",m_building_info_toc);
         SocketCommand.protoMap[8339328] = "proto.line.m_multi_lineup_set_toc";
         ClassUtils.regClass("proto.line.m_multi_lineup_set_toc",m_multi_lineup_set_toc);
         SocketCommand.protoMap[2757890] = "proto.line.m_lottery_score_fetch_toc";
         ClassUtils.regClass("proto.line.m_lottery_score_fetch_toc",m_lottery_score_fetch_toc);
         SocketCommand.protoMap[3283201] = "proto.line.m_title_load_toc";
         ClassUtils.regClass("proto.line.m_title_load_toc",m_title_load_toc);
         SocketCommand.protoMap[10243587] = "proto.line.m_story_maze_spoils_toc";
         ClassUtils.regClass("proto.line.m_story_maze_spoils_toc",m_story_maze_spoils_toc);
         SocketCommand.protoMap[3808512] = "proto.line.m_seven_goal_info_toc";
         ClassUtils.regClass("proto.line.m_seven_goal_info_toc",m_seven_goal_info_toc);
         SocketCommand.protoMap[10768899] = "proto.line.m_boat_peak_look_member_toc";
         ClassUtils.regClass("proto.line.m_boat_peak_look_member_toc",m_boat_peak_look_member_toc);
         SocketCommand.protoMap[11294210] = "proto.line.m_dominate_pvp_match_toc";
         ClassUtils.regClass("proto.line.m_dominate_pvp_match_toc",m_dominate_pvp_match_toc);
         SocketCommand.protoMap[8339329] = "proto.line.m_multi_lineup_list_toc";
         ClassUtils.regClass("proto.line.m_multi_lineup_list_toc",m_multi_lineup_list_toc);
         SocketCommand.protoMap[11819521] = "proto.line.m_building_op_toc";
         ClassUtils.regClass("proto.line.m_building_op_toc",m_building_op_toc);
         SocketCommand.protoMap[11294212] = "proto.line.m_dominate_pvp_update_toc";
         ClassUtils.regClass("proto.line.m_dominate_pvp_update_toc",m_dominate_pvp_update_toc);
         SocketCommand.protoMap[9324288] = "proto.line.m_sdk_sns_reward_info_toc";
         ClassUtils.regClass("proto.line.m_sdk_sns_reward_info_toc",m_sdk_sns_reward_info_toc);
         SocketCommand.protoMap[7682688] = "proto.line.m_star_plan_info_toc";
         ClassUtils.regClass("proto.line.m_star_plan_info_toc",m_star_plan_info_toc);
         SocketCommand.protoMap[4071170] = "proto.line.m_guide_finish_toc";
         ClassUtils.regClass("proto.line.m_guide_finish_toc",m_guide_finish_toc);
         SocketCommand.protoMap[7223043] = "proto.line.m_cycle_activity_update_toc";
         ClassUtils.regClass("proto.line.m_cycle_activity_update_toc",m_cycle_activity_update_toc);
         SocketCommand.protoMap[6172420] = "proto.line.m_war_flag_exchange_toc";
         ClassUtils.regClass("proto.line.m_war_flag_exchange_toc",m_war_flag_exchange_toc);
         SocketCommand.protoMap[4071173] = "proto.line.m_guide_get_hero_toc";
         ClassUtils.regClass("proto.line.m_guide_get_hero_toc",m_guide_get_hero_toc);
         SocketCommand.protoMap[2626563] = "proto.line.m_main_battle_fetch_pass_toc";
         ClassUtils.regClass("proto.line.m_main_battle_fetch_pass_toc",m_main_battle_fetch_pass_toc);
         SocketCommand.protoMap[1904257] = "proto.line.m_gmcmd_panel_toc";
         ClassUtils.regClass("proto.line.m_gmcmd_panel_toc",m_gmcmd_panel_toc);
         SocketCommand.protoMap[2757891] = "proto.line.m_lottery_nation_start_toc";
         ClassUtils.regClass("proto.line.m_lottery_nation_start_toc",m_lottery_nation_start_toc);
         SocketCommand.protoMap[3283202] = "proto.line.m_title_update_toc";
         ClassUtils.regClass("proto.line.m_title_update_toc",m_title_update_toc);
         SocketCommand.protoMap[11622531] = "proto.line.m_wars_walk_list_toc";
         ClassUtils.regClass("proto.line.m_wars_walk_list_toc",m_wars_walk_list_toc);
         SocketCommand.protoMap[3808513] = "proto.line.m_seven_goal_update_toc";
         ClassUtils.regClass("proto.line.m_seven_goal_update_toc",m_seven_goal_update_toc);
         SocketCommand.protoMap[8733314] = "proto.line.m_ingenious_plan_update_toc";
         ClassUtils.regClass("proto.line.m_ingenious_plan_update_toc",m_ingenious_plan_update_toc);
         SocketCommand.protoMap[7551362] = "proto.line.m_lcqs_chapter_list_toc";
         ClassUtils.regClass("proto.line.m_lcqs_chapter_list_toc",m_lcqs_chapter_list_toc);
         SocketCommand.protoMap[11294211] = "proto.line.m_dominate_pvp_logs_toc";
         ClassUtils.regClass("proto.line.m_dominate_pvp_logs_toc",m_dominate_pvp_logs_toc);
         SocketCommand.protoMap[8601984] = "proto.line.m_xswh_info_toc";
         ClassUtils.regClass("proto.line.m_xswh_info_toc",m_xswh_info_toc);
         SocketCommand.protoMap[3480195] = "proto.line.m_hunt_refresh_toc";
         ClassUtils.regClass("proto.line.m_hunt_refresh_toc",m_hunt_refresh_toc);
         SocketCommand.protoMap[10834564] = "proto.line.m_fish_update_toc";
         ClassUtils.regClass("proto.line.m_fish_update_toc",m_fish_update_toc);
         SocketCommand.protoMap[656640] = "proto.line.m_login_add_toc";
         ClassUtils.regClass("proto.line.m_login_add_toc",m_login_add_toc);
         SocketCommand.protoMap[656648] = "proto.line.m_login_fetch_vip_toc";
         ClassUtils.regClass("proto.line.m_login_fetch_vip_toc",m_login_fetch_vip_toc);
         SocketCommand.protoMap[2429573] = "proto.line.m_hero_recycle_preview_toc";
         ClassUtils.regClass("proto.line.m_hero_recycle_preview_toc",m_hero_recycle_preview_toc);
         SocketCommand.protoMap[10834566] = "proto.line.m_fish_shop_toc";
         ClassUtils.regClass("proto.line.m_fish_shop_toc",m_fish_shop_toc);
         SocketCommand.protoMap[3808514] = "proto.line.m_seven_goal_fetch_toc";
         ClassUtils.regClass("proto.line.m_seven_goal_fetch_toc",m_seven_goal_fetch_toc);
         SocketCommand.protoMap[2429575] = "proto.line.m_hero_set_lineup_toc";
         ClassUtils.regClass("proto.line.m_hero_set_lineup_toc",m_hero_set_lineup_toc);
         SocketCommand.protoMap[4859136] = "proto.line.m_rank_activity_info_toc";
         ClassUtils.regClass("proto.line.m_rank_activity_info_toc",m_rank_activity_info_toc);
         SocketCommand.protoMap[3086208] = "proto.line.m_achievement_info_toc";
         ClassUtils.regClass("proto.line.m_achievement_info_toc",m_achievement_info_toc);
         SocketCommand.protoMap[8601985] = "proto.line.m_xswh_fight_result_toc";
         ClassUtils.regClass("proto.line.m_xswh_fight_result_toc",m_xswh_fight_result_toc);
         SocketCommand.protoMap[10834568] = "proto.line.m_fish_up_official_toc";
         ClassUtils.regClass("proto.line.m_fish_up_official_toc",m_fish_up_official_toc);
         SocketCommand.protoMap[9127296] = "proto.line.m_sys_use_times_info_toc";
         ClassUtils.regClass("proto.line.m_sys_use_times_info_toc",m_sys_use_times_info_toc);
         SocketCommand.protoMap[2429577] = "proto.line.m_hero_lineup_info_toc";
         ClassUtils.regClass("proto.line.m_hero_lineup_info_toc",m_hero_lineup_info_toc);
         SocketCommand.protoMap[656649] = "proto.line.m_login_filter_fun_toc";
         ClassUtils.regClass("proto.line.m_login_filter_fun_toc",m_login_filter_fun_toc);
         SocketCommand.protoMap[1707265] = "proto.line.m_role_score_update_toc";
         ClassUtils.regClass("proto.line.m_role_score_update_toc",m_role_score_update_toc);
         SocketCommand.protoMap[9586946] = "proto.line.m_team_xswh_rank_toc";
         ClassUtils.regClass("proto.line.m_team_xswh_rank_toc",m_team_xswh_rank_toc);
         SocketCommand.protoMap[3808515] = "proto.line.m_seven_goal_gift_toc";
         ClassUtils.regClass("proto.line.m_seven_goal_gift_toc",m_seven_goal_gift_toc);
         SocketCommand.protoMap[2429580] = "proto.line.m_hero_bag_expansion_toc";
         ClassUtils.regClass("proto.line.m_hero_bag_expansion_toc",m_hero_bag_expansion_toc);
         SocketCommand.protoMap[10768902] = "proto.line.m_boat_peak_bet_toc";
         ClassUtils.regClass("proto.line.m_boat_peak_bet_toc",m_boat_peak_bet_toc);
         SocketCommand.protoMap[919297] = "proto.line.m_item_compose_toc";
         ClassUtils.regClass("proto.line.m_item_compose_toc",m_item_compose_toc);
         SocketCommand.protoMap[2429581] = "proto.line.m_hero_recycle_all_toc";
         ClassUtils.regClass("proto.line.m_hero_recycle_all_toc",m_hero_recycle_all_toc);
         SocketCommand.protoMap[9127297] = "proto.line.m_sys_use_times_update_toc";
         ClassUtils.regClass("proto.line.m_sys_use_times_update_toc",m_sys_use_times_update_toc);
         SocketCommand.protoMap[9652608] = "proto.line.m_master_card_info_toc";
         ClassUtils.regClass("proto.line.m_master_card_info_toc",m_master_card_info_toc);
         SocketCommand.protoMap[9915265] = "proto.line.m_stage_copy_sweep_toc";
         ClassUtils.regClass("proto.line.m_stage_copy_sweep_toc",m_stage_copy_sweep_toc);
         SocketCommand.protoMap[2429582] = "proto.line.m_hero_recycle_gains_item_toc";
         ClassUtils.regClass("proto.line.m_hero_recycle_gains_item_toc",m_hero_recycle_gains_item_toc);
         SocketCommand.protoMap[1247616] = "proto.line.m_family_list_toc";
         ClassUtils.regClass("proto.line.m_family_list_toc",m_family_list_toc);
         SocketCommand.protoMap[2757894] = "proto.line.m_lottery_supreme_start_toc";
         ClassUtils.regClass("proto.line.m_lottery_supreme_start_toc",m_lottery_supreme_start_toc);
         SocketCommand.protoMap[10243592] = "proto.line.m_story_maze_fight_result_toc";
         ClassUtils.regClass("proto.line.m_story_maze_fight_result_toc",m_story_maze_fight_result_toc);
         SocketCommand.protoMap[10768903] = "proto.line.m_boat_peak_his_toc";
         ClassUtils.regClass("proto.line.m_boat_peak_his_toc",m_boat_peak_his_toc);
         SocketCommand.protoMap[5581441] = "proto.line.m_share_fetch_toc";
         ClassUtils.regClass("proto.line.m_share_fetch_toc",m_share_fetch_toc);
         SocketCommand.protoMap[1641600] = "proto.line.m_icon_list_toc";
         ClassUtils.regClass("proto.line.m_icon_list_toc",m_icon_list_toc);
         SocketCommand.protoMap[8601987] = "proto.line.m_xswh_best_rank_toc";
         ClassUtils.regClass("proto.line.m_xswh_best_rank_toc",m_xswh_best_rank_toc);
         SocketCommand.protoMap[8536322] = "proto.line.m_cross_ladder_opp_info_toc";
         ClassUtils.regClass("proto.line.m_cross_ladder_opp_info_toc",m_cross_ladder_opp_info_toc);
         SocketCommand.protoMap[5909760] = "proto.line.m_test_tower_process_toc";
         ClassUtils.regClass("proto.line.m_test_tower_process_toc",m_test_tower_process_toc);
         SocketCommand.protoMap[7223042] = "proto.line.m_cycle_activity_gift_toc";
         ClassUtils.regClass("proto.line.m_cycle_activity_gift_toc",m_cycle_activity_gift_toc);
         SocketCommand.protoMap[4727808] = "proto.line.m_mission_shop_info_toc";
         ClassUtils.regClass("proto.line.m_mission_shop_info_toc",m_mission_shop_info_toc);
         SocketCommand.protoMap[6435074] = "proto.line.m_buy_times_update_toc";
         ClassUtils.regClass("proto.line.m_buy_times_update_toc",m_buy_times_update_toc);
         SocketCommand.protoMap[6238080] = "proto.line.m_god_weapon_info_toc";
         ClassUtils.regClass("proto.line.m_god_weapon_info_toc",m_god_weapon_info_toc);
         SocketCommand.protoMap[6238081] = "proto.line.m_god_weapon_upgrade_toc";
         ClassUtils.regClass("proto.line.m_god_weapon_upgrade_toc",m_god_weapon_upgrade_toc);
         SocketCommand.protoMap[5187458] = "proto.line.m_bingfu_operate_toc";
         ClassUtils.regClass("proto.line.m_bingfu_operate_toc",m_bingfu_operate_toc);
         SocketCommand.protoMap[7288707] = "proto.line.m_csc_fmsolo_state_toc";
         ClassUtils.regClass("proto.line.m_csc_fmsolo_state_toc",m_csc_fmsolo_state_toc);
         SocketCommand.protoMap[5187460] = "proto.line.m_bingfu_compose_toc";
         ClassUtils.regClass("proto.line.m_bingfu_compose_toc",m_bingfu_compose_toc);
         SocketCommand.protoMap[7288709] = "proto.line.m_csc_fmsolo_shop_lv_toc";
         ClassUtils.regClass("proto.line.m_csc_fmsolo_shop_lv_toc",m_csc_fmsolo_shop_lv_toc);
         SocketCommand.protoMap[5187462] = "proto.line.m_bingfu_refine_toc";
         ClassUtils.regClass("proto.line.m_bingfu_refine_toc",m_bingfu_refine_toc);
         SocketCommand.protoMap[1641601] = "proto.line.m_icon_switch_toc";
         ClassUtils.regClass("proto.line.m_icon_switch_toc",m_icon_switch_toc);
         SocketCommand.protoMap[2166912] = "proto.line.m_pay_gift_info_toc";
         ClassUtils.regClass("proto.line.m_pay_gift_info_toc",m_pay_gift_info_toc);
         SocketCommand.protoMap[5909761] = "proto.line.m_test_tower_fetch_toc";
         ClassUtils.regClass("proto.line.m_test_tower_fetch_toc",m_test_tower_fetch_toc);
         SocketCommand.protoMap[6435072] = "proto.line.m_buy_times_info_toc";
         ClassUtils.regClass("proto.line.m_buy_times_info_toc",m_buy_times_info_toc);
         SocketCommand.protoMap[10177921] = "proto.line.m_master_talent_science_info_toc";
         ClassUtils.regClass("proto.line.m_master_talent_science_info_toc",m_master_talent_science_info_toc);
         SocketCommand.protoMap[10703232] = "proto.line.m_fight_share_operate_toc";
         ClassUtils.regClass("proto.line.m_fight_share_operate_toc",m_fight_share_operate_toc);
         SocketCommand.protoMap[8404995] = "proto.line.m_peak_info_look_member_toc";
         ClassUtils.regClass("proto.line.m_peak_info_look_member_toc",m_peak_info_look_member_toc);
         SocketCommand.protoMap[7288706] = "proto.line.m_csc_fmsolo_fetch_toc";
         ClassUtils.regClass("proto.line.m_csc_fmsolo_fetch_toc",m_csc_fmsolo_fetch_toc);
         SocketCommand.protoMap[10506245] = "proto.line.m_treasure_role_info_toc";
         ClassUtils.regClass("proto.line.m_treasure_role_info_toc",m_treasure_role_info_toc);
         SocketCommand.protoMap[5581440] = "proto.line.m_share_info_toc";
         ClassUtils.regClass("proto.line.m_share_info_toc",m_share_info_toc);
         SocketCommand.protoMap[1116291] = "proto.line.m_role_rename_toc";
         ClassUtils.regClass("proto.line.m_role_rename_toc",m_role_rename_toc);
         SocketCommand.protoMap[1707264] = "proto.line.m_role_score_list_toc";
         ClassUtils.regClass("proto.line.m_role_score_list_toc",m_role_score_list_toc);
         SocketCommand.protoMap[1641602] = "proto.line.m_icon_switch_list_toc";
         ClassUtils.regClass("proto.line.m_icon_switch_list_toc",m_icon_switch_list_toc);
         SocketCommand.protoMap[3348866] = "proto.line.m_hanging_quick_toc";
         ClassUtils.regClass("proto.line.m_hanging_quick_toc",m_hanging_quick_toc);
         SocketCommand.protoMap[2692224] = "proto.line.m_fight_start_toc";
         ClassUtils.regClass("proto.line.m_fight_start_toc",m_fight_start_toc);
         SocketCommand.protoMap[9652611] = "proto.line.m_master_card_update_toc";
         ClassUtils.regClass("proto.line.m_master_card_update_toc",m_master_card_update_toc);
         SocketCommand.protoMap[6960384] = "proto.line.m_family_active_info_toc";
         ClassUtils.regClass("proto.line.m_family_active_info_toc",m_family_active_info_toc);
         SocketCommand.protoMap[10703233] = "proto.line.m_fight_share_stat_toc";
         ClassUtils.regClass("proto.line.m_fight_share_stat_toc",m_fight_share_stat_toc);
         SocketCommand.protoMap[11228544] = "proto.line.m_hero_cheer_info_toc";
         ClassUtils.regClass("proto.line.m_hero_cheer_info_toc",m_hero_cheer_info_toc);
         SocketCommand.protoMap[11425538] = "proto.line.m_divine_equip_compose_toc";
         ClassUtils.regClass("proto.line.m_divine_equip_compose_toc",m_divine_equip_compose_toc);
         SocketCommand.protoMap[1116292] = "proto.line.m_role_rename_info_toc";
         ClassUtils.regClass("proto.line.m_role_rename_info_toc",m_role_rename_info_toc);
         SocketCommand.protoMap[3611521] = "proto.line.m_arena_info_toc";
         ClassUtils.regClass("proto.line.m_arena_info_toc",m_arena_info_toc);
         SocketCommand.protoMap[2692225] = "proto.line.m_fight_finish_toc";
         ClassUtils.regClass("proto.line.m_fight_finish_toc",m_fight_finish_toc);
         SocketCommand.protoMap[3217536] = "proto.line.m_family_task_info_toc";
         ClassUtils.regClass("proto.line.m_family_task_info_toc",m_family_task_info_toc);
         SocketCommand.protoMap[7485696] = "proto.line.m_option_lottery_start_toc";
         ClassUtils.regClass("proto.line.m_option_lottery_start_toc",m_option_lottery_start_toc);
         SocketCommand.protoMap[11228545] = "proto.line.m_hero_cheer_op_toc";
         ClassUtils.regClass("proto.line.m_hero_cheer_op_toc",m_hero_cheer_op_toc);
         SocketCommand.protoMap[11753856] = "proto.line.m_hongbao_info_toc";
         ClassUtils.regClass("proto.line.m_hongbao_info_toc",m_hongbao_info_toc);
         SocketCommand.protoMap[9586947] = "proto.line.m_team_xswh_best_rank_toc";
         ClassUtils.regClass("proto.line.m_team_xswh_best_rank_toc",m_team_xswh_best_rank_toc);
         SocketCommand.protoMap[1116293] = "proto.line.m_role_acclogin_toc";
         ClassUtils.regClass("proto.line.m_role_acclogin_toc",m_role_acclogin_toc);
         SocketCommand.protoMap[5909764] = "proto.line.m_test_tower_sweep_toc";
         ClassUtils.regClass("proto.line.m_test_tower_sweep_toc",m_test_tower_sweep_toc);
         SocketCommand.protoMap[3217537] = "proto.line.m_family_task_update_toc";
         ClassUtils.regClass("proto.line.m_family_task_update_toc",m_family_task_update_toc);
         SocketCommand.protoMap[3742848] = "proto.line.m_fuli_sign_info_toc";
         ClassUtils.regClass("proto.line.m_fuli_sign_info_toc",m_fuli_sign_info_toc);
         SocketCommand.protoMap[7485697] = "proto.line.m_option_lottery_info_toc";
         ClassUtils.regClass("proto.line.m_option_lottery_info_toc",m_option_lottery_info_toc);
         SocketCommand.protoMap[8011008] = "proto.line.m_hzzd_info_toc";
         ClassUtils.regClass("proto.line.m_hzzd_info_toc",m_hzzd_info_toc);
         SocketCommand.protoMap[1247617] = "proto.line.m_family_create_toc";
         ClassUtils.regClass("proto.line.m_family_create_toc",m_family_create_toc);
         SocketCommand.protoMap[8601988] = "proto.line.m_xswh_fetch_toc";
         ClassUtils.regClass("proto.line.m_xswh_fetch_toc",m_xswh_fetch_toc);
         SocketCommand.protoMap[3677185] = "proto.line.m_eight_login_fetch_toc";
         ClassUtils.regClass("proto.line.m_eight_login_fetch_toc",m_eight_login_fetch_toc);
         SocketCommand.protoMap[1116294] = "proto.line.m_role_look_profile_toc";
         ClassUtils.regClass("proto.line.m_role_look_profile_toc",m_role_look_profile_toc);
       }

}
