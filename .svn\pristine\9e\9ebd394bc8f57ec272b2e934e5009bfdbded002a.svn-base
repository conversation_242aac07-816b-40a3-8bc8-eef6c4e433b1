
import { Box } from "laya/ui/Box";
import { UrlConfig } from "../../../../game/UrlConfig";
import { cfg_master_card_drum_level } from "../../../cfg/vo/cfg_master_card_drum_level";
import { ConfigManager } from "../../../managers/ConfigManager";
import { m_item_use_toc } from "../../../proto/line/m_item_use_toc";
import { com } from "../../../ui/layaMaxUI";
import { DateUtil } from "../../../util/DateUtil";
import { GameUtil } from "../../../util/GameUtil";
import { MoneyTransformUtil } from "../../../util/MoneyTransformUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { UIList } from "../../baseModules/UIList";
import { DataCenter } from "../../DataCenter";
import GoodsItem from "../../goods/GoodsItem";
import { GoodsVO } from "../../goods/GoodsVO";
import { ItemConst } from "../../goods/ItemConst";
import { MiscConst } from "../../misc_config/MiscConst";
import { ModuleCommand } from "../../ModuleCommand";
import { GoodsManager } from "../../test_bag/GoodsManager";
import WingStrengthCostItem from "../../wing/view/WingStrengthCostItem";
import { MasterCardDataCenter, MASTER_CONST, MASTER_OP_CONST } from "../data/MasterCardDataCenter";
import MasterCardExtraGoodsItem from "../view/MasterCardExtraGoodsItem";
import MasterCardLevelItem from "../view/MasterCardLevelItem";
import { SdkManager } from "../../../managers/SdkManager";
import { ESdkSupportFunc } from "../../../sdk/data/ESdkSupportFunc";
import { GlobalConfig } from "../../../../game/GlobalConfig";
import { Button } from "laya/ui/Button";
import CommonCostItem1 from "../../common/CommonCostItem1";
import { MasterCardConst } from "../data/MasterCardConst";
import { YueKaDataCenter} from "../../welfare/data/YueKaDataCenter";
import { WelfareDataCenter } from "../../welfare/data/WelfareDataCenter";
import { CommonCostItemScript } from "../../common/CommonCostItemScript";
import { ColorUtil } from "../../../util/ColorUtil";
import { Sprite } from "laya/display/Sprite";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { PanelEventConstants } from "../../PanelEventConstants";
import { PaymentVO } from "../../payment/vo/PaymentVO";

export default class MasterCardLevelDialog extends com.ui.res.matercart.MasterCartLevelDialogUI {

    private goodList: UIList;
    private goodList2: UIList;
    private levelLIst: UIList;
    private nowCfg: cfg_master_card_drum_level;
    private nextCfg: cfg_master_card_drum_level;
    private boxList: Array<Box>;
    private levelList_2: UIList;
    private fasterBtnList: Array<Button>;
    private _itemCost: CommonCostItem1;
    private _fasterCostItem: CommonCostItemScript;


    constructor() {
        super();
        this.navShow = 0;
        this.resName = [UrlConfig.HERO_ATTR_TIPS_RES, UrlConfig.FIGHT_ATTR_RES, UrlConfig.MASTER_CARD_RES];

    }

    getClassName(): string {
        return "MasterCardLevelDialog";
    }

    initUI(): void {

        //减少text渲染打断
        this.drawCallOptimize = true;
        
        this._itemCost = this.itemCost;
                this._itemCost.isCenter = true;
                this._itemCost.padding = [9,5,9,-5];
                this._itemCost.setTxtStyle({
                    size: 24,
                    stroke: 2,
                });
        // this._itemCost.visible = false;

        this.goodList = UIList.SetUIList(this, this.costList, MasterCardLevelCostItem, null, false);
        this.goodList.SetSpace(30, 0);
        this.goodList.SetRepeat(4, 1);
        this.goodList.isBoxCenter = true;

        this.levelList_2 = UIList.SetUIList(this, this.levelShowLIst, MasterCardLevelItem, null, false);
        this.levelList_2.SetRepeat(1, 10);
        this.levelList_2.isBoxCenter = true;
        this.boxList = [this.dataCompareBox, this.rewardBox, this.labTimeBox, this.costBox, this.oprateBox, this.upBox, this.fasterBox];

        this.goodList2 = UIList.SetUIList(this, this.rewardList, MasterCardExtraGoodsItem, null, false);
        this.goodList2.SetSpace(3, 0);
        this.goodList2.SetRepeat(6, 1);
        this.goodList2.isFillGrid = false;
        this.goodList2.isBoxCenter = true;
        this.fasterBtnList = [this.btnVdieo, this.btnGoldFaster, this.btnFaster]

        this._fasterCostItem = this.fasterCostItem.addComponent(CommonCostItemScript);
        this._fasterCostItem.isShowHave = true;
        this._fasterCostItem.isCenter = true;
        this._fasterCostItem.padding = [12, 0, 0, 0];
        this._fasterCostItem.setStyle({
            isChangeColor: true,
            greenColor: ColorUtil.FONT_GREEN_2,
            redColor: ColorUtil.FONT_RED_ITEM_2
        });
        // this._fasterCostItem.setDataByTypeId(MasterCardDataCenter.COMMON_FASTER_TOOL, 0);
        
    }

    addClick(): void {
        this.addOnClick(this, this.btnDo, this.onClickDo);
        this.addOnClick(this, this.topPanelUI.closeBtn, this.close);
        this.addOnClick(this, this.closeBtn, this.close);
        this.addOnClick(this, this.btnFaster, this.onClickFasterTool);
        // this.addOnClick(this, this.btnGoldFaster, this.onClickGoldFaster)
        this.addOnClick(this, this.btnVdieo, this.onClickVideo);
        this.addOnClick(this, this.btnYueKa, this.onClickBtnYueKa);
    }

    addEvent(): void {
        this.addEventListener(ModuleCommand.UPDATE_MASTER_CART_LEVEL, this, this.refreshLevelDataInfo);
        this.addEventListener(ModuleCommand.UPDATE_MASTER_CART_LEVEL, this, this.refreshLevelDataInfo);
        this.addEventListener(ModuleCommand.UPDATE_MASTER_CART_DIALOG, this, this.refreshLevelDataInfo);

    }

    onOpen(param: any): void {
        this.refreshLevelDataInfo();
    }

    private onClickFasterTool() {
        let restime = MasterCardDataCenter.instance.guUpLevelTime;
        let getTimeStr: number = DataCenter.serverTimeSeconds;
        let levelCfg = ConfigManager.cfg_master_card_drum_levelCache.get(MasterCardDataCenter.instance.guLevel + 1);
        let count = ((restime - getTimeStr) > levelCfg?.need_time) ? (levelCfg?.need_time) : (restime - getTimeStr);
        if (count >= 3) {
            this.dispatchEvent(ModuleCommand.OPEN_MASTER_CART_FASTER_TOOL_DIALOG);
            this.dispatchEvent(ModuleCommand.CLOSE_MASTER_CART_LEVEL_DIALOG);
        }
    }

    private onClickDo() {
        MasterCardDataCenter.instance.m_master_card_op_tos(MASTER_OP_CONST.TYPE_GU_UPDATE, []);
    }

    private onMatercardUpLevel() {
        if (MasterCardDataCenter.instance.guLevel != MasterCardDataCenter.autoStopColor(MasterCardDataCenter.wheaterUpLevel)) {
            if (MasterCardDataCenter.instance.guLevel != MasterCardDataCenter.autoStopColor(MasterCardDataCenter.wheaterUpLevel)) {
                MasterCardDataCenter.setNumberSkip(`${MasterCardDataCenter.wheaterUpLevel}`, MasterCardDataCenter.instance.guLevel)
                MasterCardDataCenter.asyncMasterCardColor(`${MasterCardDataCenter.wheaterUpLevel}`, MasterCardDataCenter.instance.guLevel)
                if (MasterCardDataCenter.instance.guLevel > 1) {
                    TipsUtil.showTips(window.iLang.L2_HUN_KA_DENG_JI_YI_TI_SHENG_ZHI_P0_JI.il([MasterCardDataCenter.instance.guLevel]));
                }
                this.dispatchEvent(ModuleCommand.UPDATE_LEVEL_ONT_ON_LINE);
            }
        }
    }

    private onClickVideo() {
                let miscCfg = ConfigManager.cfg_misc_configCache.get("master_card_quicken_video").value.split("|");        
                if (MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT)?.val < parseInt(miscCfg[1]) 
                && DataCenter.serverTimeSeconds > MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT)?.three) {
                    if(GlobalConfig.IsDebug){
                        MasterCardDataCenter.instance.m_master_card_op_tos(MASTER_OP_CONST.TYPE_VIEO_CUT,[]);
                    } else {
                        MasterCardDataCenter.instance.m_master_card_op_tos(MASTER_OP_CONST.TYPE_VIEO_CUT,[]);
                    }
                }
    }

    private onClickBtnYueKa() {
        this.dispatchEvent(ModuleCommand.OPEN_PAYMENT_DIALOG, { child_id: PaymentVO.KEY_WELFARE_CARD })
    }
        
    private refreshLevelDataInfo() {
        this.fasterBox.visible = false;
        let level = MasterCardDataCenter.instance.guLevel;

        //广告加速
        let split: string[] = ConfigManager.cfg_misc_configCache.get("master_card_quicken_cost_item").value.split("|");
        let itemId = Number(split[0]);
        let itemNum = Number(split[1]);
        this._itemCost.setData(itemId, itemNum);
        if((YueKaDataCenter.instance.getFuliYuekaState(MiscConst.master_card_quicken_free_yueka)==1||YueKaDataCenter.instance.getFuliYuekaState(MiscConst.master_card_quicken_free_yueka)==2)){
            this.btnVdieo.label=window.iLang.L2_MIAN_FEI_JIA_SU.il()
            this.itemCost.visible=false;
        }else{
            this.btnVdieo.label=""
            this.itemCost.visible=true;
        }
        let miscCfg = ConfigManager.cfg_misc_configCache.get("master_card_quicken_video").value.split("|");
        if (MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT)?.val >= parseInt(miscCfg[1])) {
            this.btnVdieo.gray = true
            this.limitLabel.visible=true
            this.limitLabel.text = window.iLang.L2_MING_TIAN_ZAI_LAI.il()
        } else if (MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT)?.val < parseInt(miscCfg[1]) && MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT)?.three > 0 && DataCenter.serverTimeSeconds <= MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT)?.three - 2) {
            this.btnVdieo.gray = true;
            let restime = MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT).three;
            let getTimeStr: number = DataCenter.serverTimeSeconds;
            if (restime > 0 && getTimeStr <= restime) {
                this.autoTimeCutDown2();
                this.timerLoop(1000, this, this.autoTimeCutDown2);
            } else if (restime == 0 || getTimeStr > restime) {
                this.limitLabel.text = "";
            } else {
                this.limitLabel.text = "";
            }
        } else if (MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT)?.val < parseInt(miscCfg[1]) && MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT)?.three < DataCenter.serverTimeSeconds + 2) {
            this.btnVdieo.gray = false;
            this.limitLabel.text = ""
        }

        //元宝加速
        // let gFasterCfg = ConfigManager.cfg_misc_configCache.get("master_card_quicken_video").value.split("|");
        // if (MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT)?.val >= parseInt(miscCfg[1])) {
        //     this.btnVdieo.gray = true
        //     this.limitLabel.text = window.iLang.L2_MING_TIAN_ZAI_LAI.il()
        // } else if (MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT)?.val < parseInt(miscCfg[1]) && MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT)?.three > 0 && DataCenter.serverTimeSeconds <= MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT)?.three - 2) {
        //     this.btnVdieo.gray = true;
        //     let restime = MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT).three;
        //     let getTimeStr: number = DataCenter.serverTimeSeconds;
        //     if (restime > 0 && getTimeStr <= restime) {
        //         this.autoTimeCutDown2();
        //         this.timerLoop(1000, this, this.autoTimeCutDown2);
        //     } else if (restime == 0 || getTimeStr > restime) {
        //         this.limitLabel.text = "";
        //     } else {
        //         this.limitLabel.text = "";
        //     }
        // } else if (MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT)?.val < parseInt(miscCfg[1]) && MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT)?.three < DataCenter.serverTimeSeconds + 2) {
        //     this.btnVdieo.gray = false;
        //     this.limitLabel.text = ""
        // }


        this.SetRedPoint(this.btnDo, MasterCardDataCenter.instance.isMAasterCardLevelCanUpdate());
        this.SetRedPoint(this.btnFaster, MasterCardDataCenter.instance.checkFasterToolRedPoint());
        if (MasterCardDataCenter.instance.isMasterCartMaxLevel()) {
            this.nowlevel.text = `${level}`;
            this.maxText.visible = true;
            this.nextLevel.text = `${level + 1}`;
            this.nextlevelBox.visible = false;
            this.labTimeBox.visible = false;
        } else {
            this.maxText.visible = false;
            this.nowlevel.text = `${level}`;
            this.nextLevel.text = `${level + 1}`;
            this.labTimeBox.visible = true;
        }

        let restime = MasterCardDataCenter.instance.guUpLevelTime;
        this.nowCfg = ConfigManager.cfg_master_card_drum_levelCache.get(level);
        let getTimeStr: number = DataCenter.serverTimeSeconds;
        if (MasterCardDataCenter.instance.isMasterCartMaxLevel()) {
            this.nextCfg = ConfigManager.cfg_master_card_drum_levelCache.get(level);
            this.labTimeBox.visible = this.upBox.visible = this.costBox.visible = false;
            this.oprateBox.visible = true;

        } else {
            this.nextCfg = ConfigManager.cfg_master_card_drum_levelCache.get(level + 1);
            this.costBox.visible = this.labTimeBox.visible = true;
            this.oprateBox.visible = false;
            this.upBox.visible = (MasterCardDataCenter.instance.drum.val == 0 || getTimeStr > restime);
        }

        let colorArr = []
        for (let i: number = 1; i <= ConfigManager.cfg_master_card_colorCache.get_all()?.length; i++) {
            let nowCfg = ConfigManager.cfg_master_card_drum_levelCache.get(level);
            let nextCfg = ConfigManager.cfg_master_card_drum_levelCache.get(level + 1);
            let nowMap: Map<number, number> = new Map();
            let nextMap: Map<number, number> = new Map();

            nowCfg?.lottery_color?.split("|")?.forEach((item, index) => {
                nowMap.set(parseInt(item), parseInt(nowCfg.color_weight.split("|")[index]));

            })

            nextCfg?.lottery_color?.split("|")?.forEach((item, index) => {
                nextMap.set(parseInt(item), parseInt(nextCfg.color_weight.split("|")[index]));
            })

            if ((nowMap.get(i) ? nowMap.get(i) : 0) == 0 && (nextMap.get(i) ? nextMap.get(i) : 0) == 0) {
            }
            else {
                colorArr.push({ nowlevel: level, nextLevel: level + 1, i: i })
            }

        }

        this.levelShowLIst.height = new MasterCardLevelItem().height * colorArr.length + 55;
        this.levelList_2.array = colorArr;
        this.dataCompareBox.height = this.levelShowLIst.height
        let costList = GameUtil.parseRewards(this.nextCfg, "cost_");
        this.goodList.array = costList.filter(item => {
            return item.item.type_id != ItemConst.COST_GOLD;
        });

        let costListArr = costList.filter(item => {
            return item.item.type_id != ItemConst.COST_GOLD;
        });

        if (costListArr.length == 0) {
            this.costBox.visible = false;
        }

        let costId = parseInt(this.nextCfg.cost_1.split("|")[0]);
        let costlarge = parseInt(this.nextCfg.cost_1.split("|")[1])
        let costNum = GoodsManager.instance.GetGoodsNumByTypeId(costId);
        let nendNum = GameUtil.gold(costlarge);
        this.neednum.text = `/${MoneyTransformUtil.MoneyFormat(nendNum, true, 9999)}`;
        this.tiaoZhanCountTxt.text = MoneyTransformUtil.MoneyFormat(costNum, true, 9999) + '';
        this.tiaoZhanCountTxt.color = costNum >= nendNum ? "#fff" : "#FB342B";
        this.neednum.color = costNum >= nendNum ? "#fff" : "#FB342B";
        this.costImg2.skin = GameUtil.getItemIconUrl(costId);
        this.timeLab.color = restime > 0 ? "#5D8E17" : "#C93D00";
        if (restime > 0 && getTimeStr <= restime) {
            this.labTime.visible = true;
            this.costBox.visible = false;
            this.fasterBox.visible = true;
            this.adapterView();
            this.clearTimer(this, this.autoTimeCutDown);
            this.autoTimeCutDown();
            this.timerLoop(100, this, this.autoTimeCutDown);
        } else if (restime == 0 || getTimeStr > restime) {
            this.labTime.visible = true;
            this.fasterBox.visible = false;
            let getTimeStr: number = this.nextCfg?.need_time;
            let count = getTimeStr;
            this.timeLab.text = this.timeLab.text = MasterCardDataCenter.instance.getTimeDec(count);
        } else {
            this.labTime.visible = false;
        }
        let goodList = [];
        for (let i = 1; i <= ConfigManager.cfg_master_card_drum_levelCache.get_all()?.length; i++) {
            let drumCfg = ConfigManager.cfg_master_card_drum_levelCache.get(i);
            if (drumCfg?.extra_reward_weight_2) {
                let goodsTypeId: number = parseInt(drumCfg?.extra_reward_weight_2.split("|")[0]);
                let goodsVo = GoodsVO.GetVoByTypeId(goodsTypeId);
                if (!goodsVo) {
                    TipsUtil.showTips(window.iLang.L2_CFG_CH81_MASTER_CH81_CARD_CH81_DRUM_CH81_LEVEL_ch03_EXTRA_CH81_REWARD_CH81_WEIGHT_CH81_2_BU.il([goodsTypeId]));
                    continue;
                }
                goodList.push({ goodsVo, opneLevel: drumCfg?.level });

            }

        }
        this.goodList2.array = goodList;
        this.rewardBox.visible = !(goodList?.length <= 0);

        if (this._fasterCostItem) {
            let needFasterCostNum = Math.ceil((restime - getTimeStr) / (MiscConst.master_card_quick_item_time));
            this._fasterCostItem.setDataByTypeId(MasterCardDataCenter.COMMON_FASTER_TOOL, needFasterCostNum);
        }

        this.btnYueKa.visible = DataCenter.myLevel >= CfgCacheMapMgr.cfg_activity_iconCache.get(PanelEventConstants.WELFARE).openlv;

        this.onMatercardUpLevel();
        this.adapterView();
    }

    /* 页面自适应 */
    private adapterView() {
        let newBoxList = this.boxList.filter(item => {
            return item.visible == true;
        })
        for (let i = 0; i < newBoxList.length; i++) {
            if (newBoxList[i].visible) {
                if (i >= 1) {
                    newBoxList[i].y = newBoxList[i - 1].y + newBoxList[i - 1].height;
                    this.topPanelUI.height = newBoxList[i].y + newBoxList[i].height;
                }
                else if (i == 0) {
                    newBoxList[i].y = 45;
                    this.topPanelUI.height = newBoxList[i].y + newBoxList[i].height;
                }
            }
        }
        this.topPanelUI.y = 1280 / 2 - this.topPanelUI.height / 2;


    }

    private autoTimeCutDown() {
        let laveTime = MasterCardDataCenter.instance.cardInfo.gUpLevelLaveTime;
        if (laveTime <= 0) return;

        this.labTimeBox.visible = true;
        this.timeLab.text = MasterCardDataCenter.instance.getTimeDec(laveTime);
    }

    private autoTimeCutDown2() {
        let restime = MasterCardDataCenter.instance.getBaseInfo(MASTER_CONST.KEY_VIDO_CUT).three;
        if (restime == 0) {
            return;
        } else if (restime > 0) {
            let miscCfg = ConfigManager.cfg_misc_configCache.get("master_card_quicken_video").value.split("|");
            let getTimeStr: number = DataCenter.serverTimeSeconds;
            let count = ((restime - getTimeStr) > parseInt(miscCfg[2])) ? parseInt(miscCfg[2]) : (restime - getTimeStr);
            this.limitLabel.text = DateUtil.GetHMS(count);
            if (count <= 0) {
                this.refreshLevelDataInfo();
                this.clearTimer(this, this.autoTimeCutDown2);
                this.limitLabel.text = "";
            }
        } else {
            this.refreshLevelDataInfo();
            this.clearTimer(this, this.autoTimeCutDown2);
            this.limitLabel.text = "";
        }

    }

    onClose(): void {
        this.clearTimer(this, this.autoTimeCutDown);
        this.clearTimer(this, this.autoTimeCutDown2);
    }


}

export class MasterCardLevelCostItem extends WingStrengthCostItem {

    constructor() {
        super();
    }
    initUI(): void {
        this.scaleX = this.scaleY = 1.2;
        this.goodsItem.numBg.visible = false;
        this.goodsItem.numTxt.visible = false;
        this.goodsItem.numTxt.fontSize = 8;
        this.txtCostNum.fontSize = 16;

    }

}

