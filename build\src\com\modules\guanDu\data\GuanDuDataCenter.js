//数据不要用静态类型的
//可以在本模块引用，不可跨模块引用
import { ConfigManager } from "../../../managers/ConfigManager";
import { DispatchManager } from "../../../managers/DispatchManager";
import { Connection } from "../../../net/Connection";
import { m_guandu_info_tos } from "../../../proto/line/m_guandu_info_tos";
import { m_guandu_lineup_tos } from "../../../proto/line/m_guandu_lineup_tos";
import { m_guandu_next_tos } from "../../../proto/line/m_guandu_next_tos";
import { m_guandu_op_tos } from "../../../proto/line/m_guandu_op_tos";
import { LocalStorageUtil } from "../../../util/LocalStorageUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { PanelEventConstants } from "../../PanelEventConstants";
import { RedPointMgr } from "../../redPoint/RedPointMgr";
import { SettingDataCenter } from "../../setting/data/SettingDataCenter";
import { YueKaDataCenter } from "../../welfare/data/YueKaDataCenter";
import { GuanDuConst } from "../GuanDuConst";
import { EYueKaType } from "../../../auto/ConstAuto";
//本模块引用的时候不要缓存instance，每次通过instance获取即可
export class GuanDuDataCenter {
    constructor() {
        /**是否需要布阵 */
        this.is_need_line_up = true;
        /**当前层数 */
        this.floor = 1;
        /**历史最高层数 */
        this.history_max_floor = 1;
        /**当前层 状态  0正常  1可进入下一层 2通关了*/
        this.status = 0;
        //------------ 协议发送 end --------------
    }
    static get instance() {
        if (GuanDuDataCenter._instance == null) {
            GuanDuDataCenter._instance = new GuanDuDataCenter();
        }
        return GuanDuDataCenter._instance;
    }
    reset() {
        GuanDuDataCenter._instance = null;
    }
    updateAttrList(list) {
        this.attr_list = list;
    }
    getAttrList() {
        return this.attr_list;
    }
    updateGoodsItemList(list) {
        this.goodsItemList = list;
        DispatchManager.dispatchEvent("UPDATE_GUAN_DU_GOODS_NUM_LIST" /* UPDATE_GUAN_DU_GOODS_NUM_LIST */);
    }
    /**获取道具拥有数 */
    getOwnGoodsNumByType(gType) {
        let info = this.getGoodsInfoByType(gType);
        return info ? info.item_num : 0;
    }
    /**获取道具可使用数 */
    getGoodsCanUseNumByType(gType) {
        let info = this.getGoodsInfoByType(gType);
        return info ? info.round_max_times - info.use_times : 0;
    }
    /**获取道具可购买次数 */
    getGoodsCanBuyTimesByType(gType) {
        let info = this.getGoodsInfoByType(gType);
        return info ? info.max_buy_times - info.buy_times : -1;
    }
    /**
     * 获取官渡之战道具info
     * @param gType 道具类型
     * @returns
     */
    getGoodsInfoByType(gType) {
        if (!this.goodsItemList)
            return null;
        for (let g of this.goodsItemList) {
            if (g.item_type == gType) {
                return g;
            }
        }
        return null;
    }
    /**
     * 更新重置红点
     * @param time
     */
    updateResetRedPoint(time) {
        let saveTime = LocalStorageUtil.getUserVal("guandu_reset_time");
        let redNum = 0;
        if (time + "" != saveTime) {
            redNum = 1;
        }
        RedPointMgr.ins.SetRedData(PanelEventConstants.GUAN_DU, GuanDuConst.RESET_RED_ID, redNum);
    }
    removeResetTimeRedPoint() {
        let time = this.reset_time;
        LocalStorageUtil.setUserLocalStorage("guandu_reset_time", time + "");
        this.updateResetRedPoint(time);
    }
    /**是否勾选自动答题 */
    get isCheckedAutoAnswer() {
        let isOpen = this.isOpenAutoAnswer();
        if (!isOpen)
            return false;
        let key = "guandu_checked_auto_answer";
        return !!SettingDataCenter.instance.getVal(key);
    }
    set isCheckedAutoAnswer(val) {
        let key = "guandu_checked_auto_answer";
        SettingDataCenter.instance.updateSetting(key, val ? 1 : 0);
        SettingDataCenter.instance.m_role_setting_tos(key, val ? 1 : 0);
    }
    /**是否开启自动答题 */
    isOpenAutoAnswer(isTip = false) {
        let yueKaType = EYueKaType.TYPE_4_KING;
        if (YueKaDataCenter.instance.isOpenFuliYueKa(yueKaType)) {
            return true;
        }
        if (isTip && ConfigManager.cfg_fuli_yuekaCache.has(yueKaType)) {
            let cfgYueka = ConfigManager.cfg_fuli_yuekaCache.get(yueKaType);
            let tip = window.iLang.L2_JI_HUO_P0_JIE_SUO.il([cfgYueka.yueka_name]);
            TipsUtil.showTips(tip);
        }
        return false;
    }
    /**是否开启扫荡功能 */
    isOpenSweep(isTip = false) {
        // let limit = MiscConst.guandu_sweep_limit;
        // // if(this.history_max_floor >= limit[0] || DataCenter.vipLevel >= limit[1]){
        // //     return true;
        // // }
        // //2022/10/10 只保留VIP限制
        // if(DataCenter.vipLevel >= limit[1]){
        //     return true;
        // }
        // if(isTip){
        //     // let tip = window.i18n(`历史通关达到{0}关或VIP等级{1}解锁`,[limit[0],limit[1]]);
        //     let tip = window.i18n(`VIP{0}解锁`,[limit[1]]);
        //     TipsUtil.showTips(tip);
        // }
        let yueKaType = EYueKaType.TYPE_4_KING; //2022/10/17 改为月卡激活
        if (YueKaDataCenter.instance.isOpenFuliYueKa(yueKaType)) {
            return true;
        }
        if (isTip && ConfigManager.cfg_fuli_yuekaCache.has(yueKaType)) {
            let cfgYueka = ConfigManager.cfg_fuli_yuekaCache.get(yueKaType);
            let tip = window.iLang.L2_JI_HUO_P0_JIE_SUO.il([cfgYueka.yueka_name]);
            TipsUtil.showTips(tip);
        }
        return false;
    }
    //------------ 协议发送 start ------------
    static m_guandu_info_tos() {
        let tos = new m_guandu_info_tos();
        Connection.instance.sendMessage(tos);
    }
    static m_guandu_op_tos(op_type, id, sec_id = 0) {
        let tos = new m_guandu_op_tos();
        tos.op_type = op_type;
        tos.id = id;
        tos.sec_id = sec_id;
        Connection.instance.sendMessage(tos);
    }
    static m_guandu_lineup_tos(hero_list) {
        let tos = new m_guandu_lineup_tos();
        tos.hero_id_list = hero_list;
        Connection.instance.sendMessage(tos);
    }
    static m_guandu_next_tos() {
        let tos = new m_guandu_next_tos();
        Connection.instance.sendMessage(tos);
    }
}
GuanDuDataCenter._instance = null;
