import { UrlConfig } from "../../../../game/UrlConfig";
import { com } from "../../../ui/layaMaxUI";
import { DateUtil } from "../../../util/DateUtil";
import { GameUtil } from "../../../util/GameUtil";
import { HtmlUtil } from "../../../util/HtmlUtil";
import UIButton from "../../baseModules/UIButton";
import { UIHTMLDiv } from "../../baseModules/UIHTMLDiv";
import { UIProgressBar } from "../../baseModules/UIProgressBar";
import { DataCenter } from "../../DataCenter";
import { PanelEventConstants } from "../../PanelEventConstants";
import { PaymentLinkUtil } from "../../payment/PaymentLinkUtil";
import { PaymentVO } from "../../payment/vo/PaymentVO";
import { RedPointMgr } from "../../redPoint/RedPointMgr";
import { WelfareDataCenter } from "../data/WelfareDataCenter";
import { YueKaDataCenter } from "../data/YueKaDataCenter";
import { StringUtil } from "../../../util/StringUtil";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { MenuDataCenter } from "../../menu/data/MenuDataCenter";
import { CommonButton } from "../../BaseDialog";
import { EYueKaType } from "../../../auto/ConstAuto";
export class WelfareVipCardItem extends com.ui.res.welfare.WelfareVipCardItemUI {
    constructor() {
        super(...arguments);
        this.state = 0;
        this.yueka_type = 0;
        //private effBg: CCAnimation;
        this.sumDay = 0;
    }
    initUI() {
        UIHTMLDiv.SetUIHTMLDiv(this.labTime, 20, "#ffffff");
        this.labTime.style.stroke = 2;
        UIHTMLDiv.SetUIHTMLDiv(this.rechargeProgressTxt, 20, "#ffffff", 6, "center");
        this.rechargeProgressTxt.style.stroke = 2;
        // this.rechargeProgressTxt.style.wordWrap = false;
        this.expBar = UIProgressBar.SetUIProgressBar(this, this.progress);
    }
    setDataSource(data, index, parameter) {
        this.UpdateItem(data);
    }
    UpdateItem(itemData) {
        if (!itemData)
            return;
        let cfg = itemData;
        let kv = YueKaDataCenter.instance.getFuliYuekaInfo(cfg.yueka_type);
        this.cfgYueKa = cfg;
        if (!kv)
            return;
        this.sumDay = cfg.duration;
        this.kv = kv;
        this.yueka_type = this.kv.type;
        this.imgBg.skin = UrlConfig.BASE_RES_UI_URL + "welfare/card_" + this.yueka_type + ".png";
        this.txtGold.text = GameUtil.gold(cfg.gold).toString();
        let boatCardData = StringUtil.parseBigBracketData(CfgCacheMapMgr.cfg_boat_race_miscCache.get("auction_gold_fetch_yueka_type").value);
        this.txtDesc1.text = window.iLang.L_MONTH_CARD_TEQUAN_DESC.il() + StringUtil.fixedNum(boatCardData.get(cfg.yueka_type)[1] / 100, 2) + "%";
        this.bgDesc1.width = this.txtDesc1.textField.textWidth + 100;
        let descList = this.getCardDesc(cfg);
        this.bgDesc2.visible = this.bgDesc3.visible = false;
        if (descList.length > 0) {
            this.txtDesc2.text = descList[0];
            this.bgDesc2.visible = true;
            this.bgDesc2.width = this.txtDesc2.textField.textWidth + 100;
            if (descList.length > 1) {
                this.bgDesc3.visible = true;
                this.txtDesc3.text = descList[1];
                this.bgDesc3.width = this.txtDesc3.textField.textWidth + 100;
            }
        }
        //充值 领取 已领取
        this.state = YueKaDataCenter.instance.getFuliYuekaState(this.yueka_type);
        RedPointMgr.ins.SetRedPoint(this.btnGet, false);
        let btnSkin = CommonButton.BtnYellow;
        if (this.state == 0) {
            btnSkin = CommonButton.BtnGreen;
            this.btnRecharge.label = window.iLang.L2_TUI_JIAN_CHONG_ZHI.il();
            this.rechargeBox.visible = true;
            this.timeBox.visible = false;
            this.expBar.SetValue(this.kv.price, cfg.need_recharge);
            this.expBar.SetValueTxt(window.iLang.L2_DANG_QIAN_CHONG_ZHI_ch05.il() + this.kv.price + "/" + cfg.need_recharge);
            this.rechargeProgressTxt.innerHTML = window.iLang.L2_ZAI_CHONG_ZHI_P0_YUAN_JI_KE_JIE_SUO.il([HtmlUtil.font((cfg.need_recharge - this.kv.price) + "", "#ffd74d")]);
        }
        else if (this.state == 1) {
            this.btnGet.label = window.iLang.L2_LING_ch11_QU.il();
            this.btnGet.disabled = false;
            RedPointMgr.ins.SetRedPoint(this.btnGet, true);
            this.rechargeBox.visible = false;
            this.timeBox.visible = true;
        }
        else {
            this.btnGet.label = window.iLang.L2_YI_RECEIVE.il();
            this.btnGet.disabled = true;
            this.rechargeBox.visible = false;
            this.timeBox.visible = true;
        }
        this.btnRecharge.skin = btnSkin;
        UIButton.ResetButtonLabel(this.btnRecharge);
        // if (this.state == 0) {
        //     // this.effBg = this.ShowEffect("yueka", this.imgBg, true, 308, 200);
        //     // this.effBg.scale(2.45, 2.45);
        // } else {
        //     if (this.effBg)
        //         this.effBg.visible = false;
        // }
        this.boxTime.visible = false;
        if (this.kv) {
            if (this.kv.end_time > DataCenter.serverTimeSeconds) {
                this.boxTime.visible = true;
                this.updateTime();
                this.timerLoop(1000, this, this.updateTime);
            }
            else {
                this.clearTimer(this, this.updateTime);
            }
        }
        else {
            this.clearTimer(this, this.updateTime);
        }
    }
    getCardDesc(cfg) {
        let descList = [];
        if (cfg.hero_bag_num > 0) {
            descList.push(window.iLang.L2_HERO_BEI_BAO_ch17.il() + cfg.hero_bag_num);
        }
        if (cfg.quick_hanging_times > 0) {
            if (cfg.yueka_type == EYueKaType.TYPE_4_KING) {
                descList.push(window.iLang.L2_MEI_TIAN_E_WAI_KUAI_SU_GUA_JI_CI_SHU_ch17.il() + cfg.quick_hanging_times);
            }
            else {
                descList.push(window.iLang.L2_MEI_TIAN_MIAN_FEI_E_WAI_KUAI_SU_GUA_JI_CI_SHU_ch17.il() + cfg.quick_hanging_times);
            }
        }
        else {
            descList.push(window.iLang.L2_MIAN_FEI_KUAI_SU_GUA_JI_CI_SHU_ch17_2.il());
        }
        return descList;
    }
    addClick() {
        this.addOnClick(this, this.btnRecharge, this.onClick);
        this.addOnClick(this, this.btnGet, this.onClick);
        //this.addOnClick(this, this.linkImg, this.OnClikImg);
    }
    updateTime() {
        let date = DateUtil.GetDate(DataCenter.serverTimeSeconds * 1000);
        let targetDate = DateUtil.GetDate(this.kv.end_time * 1000);
        let count = DateUtil.getSubTimeToSeconds(targetDate, date);
        if (count <= 0) {
            this.clearTimer(this, this.updateTime);
        }
        let d = Math.floor(count / 86400);
        this.labTime.innerHTML = window.iLang.L2_DANG_QIAN_YI_RECEIVE_ch10_ch11_P0_TIAN.il([HtmlUtil.font((this.sumDay - d).toString(), "#7eea4e")]);
    }
    OnClikImg() {
        GameUtil.gotoSystemPanelById(PanelEventConstants.PAYMENT, PaymentVO.KEY_SVIP_SHOP);
    }
    onClick() {
        if (this.state == 0) {
            // DispatchManager.dispatchEvent(ModuleCommand.OPEN_PAYMENT_DIALOG);
            // GameUtil.gotoSystemPanelById(PanelEventConstants.PAYMENT, PaymentVO.KEY_EVERYDAY_SHOP);
            if (this.cfgYueKa.yueka_type === EYueKaType.TYPE_4_KING) {
                PaymentLinkUtil.onYueKaRecommendPayment(this.yueka_type);
                return;
            }
            //判断新手之路是否开启并且新手之路图标显示
            if (GameUtil.isSysOpen(PanelEventConstants.WELFARE_LIMIT_HERO, 0, false) && MenuDataCenter.instance.checkIconCfgCondition(PanelEventConstants.WELFARE_LIMIT_HERO)) {
                if (WelfareDataCenter.instance.limit_sign_info.status === 1) {
                    PaymentLinkUtil.onYueKaRecommendPayment(this.yueka_type);
                }
                else {
                    this.dispatchEvent("OPEN_WELFARE_LIMIT_HERO_DIALOG" /* OPEN_WELFARE_LIMIT_HERO_DIALOG */);
                }
            }
            else {
                PaymentLinkUtil.onYueKaRecommendPayment(this.yueka_type);
            }
        }
        else if (this.state == 1) {
            // WelfareDataCenter.instance.m_fuli_yueka_fetch_tos(this.yueka_type);
            WelfareDataCenter.instance.m_fuli_yueka_fetch_tos(0);
        }
        else {
        }
    }
    Clean() {
    }
}
