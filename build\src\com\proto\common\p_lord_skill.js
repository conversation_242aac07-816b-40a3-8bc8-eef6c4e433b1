import { CCMessage } from "../CCMessage";
export class p_lord_skill extends CCMessage {
    constructor() {
        super();
        this.id = 0;
        this.level = 0;
        this.rate = [];
    }
    pack(result) {
        result.writeInt32(this.id);
        result.writeInt32(this.level);
        let ratelen = this.rate.length;
        result.writeUint16(ratelen);
        for (let rateItem of this.rate) {
            result.writeInt32(rateItem);
        }
    }
    unpack(result) {
        this.id = result.readInt32();
        this.level = result.readInt32();
        let ratelen = result.readUint16();
        for (let rateIndex = 0; rateIndex < ratelen; rateIndex++) {
            this.rate.push(result.readInt32());
        }
    }
}
