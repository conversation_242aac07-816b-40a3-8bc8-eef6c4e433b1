import { Image } from "laya/ui/Image";
import { cfg_guide_helper } from "../../../cfg/vo/cfg_guide_helper";
import { m_fight_simp_result_toc } from "../../../proto/line/m_fight_simp_result_toc";
import { ActorType, RoleCamp } from "../../../scene2d/SceneConst";
import { MathUtil2 } from "../../../util/MathUtil2";
import { XmlFormatVo } from "../../../util/XmlFormatVo";
import { GSkeleton } from "../../baseModules/skeleton/GSkeleton";
import { BaseView } from "../../BaseView";
import CommonTalkView from "../../common/CommonTalkView";
import { GuideMgr } from "../../guide/GuideMgr";
import { HeroDataCenter } from "../../hero/data/HeroDataCenter";
import { TDRoleBase } from "../../tdBase/game/actor/TDRoleBase";
import { TDRoleHero } from "../../tdBase/game/actor/TDRoleHero";
import { TDPathPointVo } from "../../tdBase/game/vo/TDPathPointVo";
import GuaJiDialog from "../dialog/GuaJiDialog";
import { GuajiBuildGuideScript_StepBase } from "./GuajiBuildGuideScript_StepBase";
import { Sprite } from "laya/display/Sprite";
import { Point } from "laya/maths/Point";
import { HitArea } from "laya/utils/HitArea";
import { Box } from "laya/ui/Box";
import { Label } from "laya/ui/Label";
import { ColorUtil } from "../../../util/ColorUtil";
import { Handler } from "laya/utils/Handler";
import { LayerManager } from "../../../managers/LayerManager";
import { Button } from "laya/ui/Button";
import { CommonButton } from "../../BaseDialog";
import { p_kv } from "../../../proto/common/p_kv";
import { Texture } from "laya/resource/Texture";
import { ILaya } from "ILaya";
import { URL } from "laya/net/URL";
import { UrlConfig } from "../../../../game/UrlConfig";
import { SkeletonManager } from "../../baseModules/skeleton/SkeletonManager";
import { ESkeletonType } from "../../baseModules/skeleton/SkeletonData";
import { Laya } from "Laya";
import { Dialog } from "laya/ui/Dialog";
import { DialogManager } from "laya/ui/DialogManager";
import { GuajiBuildGuideScript } from "./GuajiBuildGuideScript";
import { GuaJiBuildDataCenter } from "../data/GuaJiBuildDataCenter";
import { EBuildingType } from "../../../auto/ConstAuto";
import { GuajiBuildVo } from "../vo/GuajiBuildVo";
import { LordDataCenter } from "../../Lord/data/LordDataCenter";

/**
 * 苏醒引导
 *
 * 1.进游戏显示文案剧情
 * 2.睁眼动画
 */
export class GuajiBuildGuideScript_guide_wakeup_300001 extends GuajiBuildGuideScript_StepBase {

    protected _isFinish: boolean = false;
    private recordStepMap: Map<number, boolean> = new Map();

    // private build_jinkuang_type = 3;
    private wordIndex = 0;
    private boxWords: Box;

    private imgBlackBg: Image;
    private skEys:GSkeleton;

    private btnClose: Button;

    private isSkipStep2 = false;
    get isFinish() {
        return this._isFinish;
    }
    set isFinish(value: boolean) {
        this._isFinish = value;
    }

    public init(isNewLogin: boolean, dialog: GuaJiDialog): void {
        super.init(isNewLogin, dialog);

        this.clear();

        let isFinish = GuideMgr.ins.isFinishMission(GuajiBuildGuideScript.guide_wakeup_300001);
        if(isFinish){
            GuideMgr.ins.skipGuide(GuajiBuildGuideScript.guide_wakeup_300001);
            return;
        }

        // this.btnClose = new Button();
        // this.btnClose.skin = CommonButton.BtnYellow
        // this.btnClose.size(120, 60);
        // this.btnClose.pos(this.dialog.relativeWidth - 200, 800);
        // this.btnClose.zOrder = LayerManager.STAGE_ZORDE_TOPEST;
        // this.addChild(this.btnClose);
        // this.dialog.addOnClick(this, this.btnClose, () => {
        //     this.destroy();
        // })

        let width = this.dialog.relativeWidth;
        let height = this.dialog.relativeHeight;
        let maskImg = new Image();

        // maskImg.loadImage("common/guide_mask_bg.png");
        maskImg.name = "maskImg";
        maskImg.cacheAs = "bitmap";
        maskImg.width = width;
        maskImg.height = height;
        maskImg.graphics.drawRect(0, 0, width, height, "#000000");
        this.addChild(maskImg);
        this.imgBlackBg = maskImg;

        Laya.stage.addChild(this);
        this.dialog.isScriptHideGuide = true;
        this.zOrder = LayerManager.STAGE_ZORDE_TOPEST;

        if (this.checkSkipStep()){
            // this.onEyesWakeupEnd();
            this.visible = false;
            this.dialog.isScriptHideGuide = false;
            this.timer.once(1000, this, () => {
                this.dialog.isScriptHideGuide = false;
            })
        }else{
            this.initWordsList();
        }
    }

    private checkSkipStep(){
        //建造主城已完成
        let toStep = 0;
        let vo: GuajiBuildVo = GuaJiBuildDataCenter.instance.getBuildVoByType(EBuildingType.BUILD_TYPE_BASE);
        if (vo.buildLv > 0) {
            //建造金矿
            toStep = 1;
        }
        vo = GuaJiBuildDataCenter.instance.getBuildVoByType(EBuildingType.BUILD_TYPE_GOLD_MINE);
        if (vo.buildLv > 0) {
            //点击金矿
            toStep = 2;
            this.isSkipStep2 = true;
        }
        if (toStep) {
            GuideMgr.ins.curStep = toStep;
        }
        return toStep > 0;
    }

    private initWordsList() {

        if (!this.boxWords) {
            this.boxWords = new Box();
            this.addChild(this.boxWords);
        }

        this.boxWords.destroyChildren();

        // let startY = this.dialog.relativeHeight / 2 - 200;

        let textList = this.getPlotDialogue(1, this.camp).split("\\n");
        let wordList = textList;
        let lbList:Label[] = [];
        let totalHeight = wordList.length * 40;
        let startY = (this.dialog.relativeHeight - totalHeight) / 2;
        for (let i = 0; i < wordList.length; i++) {
            let text = wordList[i];
            if(!text){
                continue;
            }
            let label = new Label();
            label.text = text;
            label.fontSize = 28;
            label.color = ColorUtil.WHITE;
            label.alpha = 0;
            // label.size(400, 24);

            label.pos(100, 42 * i + startY);
            lbList.push(label);
            this.boxWords.addChild(label);
        }
        //根据最长的label设置长度,然后居中
        let maxWidth = 0;
        lbList.forEach(lb=>{
            if(lb.width > maxWidth){
                maxWidth = lb.width;
            }
        })
        lbList.forEach(lb=>{
            lb.width = maxWidth;
            lb.x = (this.dialog.relativeWidth - maxWidth) / 2;
        })

        this.timer.loop(1000, this, this.timerShowWordsList).runImmediately();

        // this.dialog.buildView.moveToBuilding(EBuildingType.BUILD_TYPE_BASE, 999);
    }

    private timerShowWordsList() {
        let label = this.boxWords.getChildAt(this.wordIndex) as Label;
        if (label) {
            label._tweenTo({ alpha: 1 }, 1200);
        } else {
            this.timer.clear(this, this.timerShowWordsList);
            this.boxWords._tweenTo({ alpha: 0 }, 1000, null, Handler.create(this, this.showEyesWakeup), 2000);
        }
        this.wordIndex++;
    }

    //睁眼动画
    public showEyesWakeup() {

        let width = this.dialog.relativeWidth;
        let height = this.dialog.relativeHeight;

        this.skEys = SkeletonManager.ins.createSkeleton("build_eye", ESkeletonType.UI_EFFECT);
        this.skEys.pos(width / 2, height / 2);
        this.skEys.playNameOrIndex(0, false, true);
        this.addChild(this.skEys);
        // this.imgBlackBg.graphics.clear();

        this.dialog.addOnClick(this, this.imgBlackBg, () => { console.log("-----onClick maskBox-----") }).setIsClickScale(false);
   
        this.imgBlackBg._tweenTo({alpha:0}, 500, null, null, 0);
        this.timer.once(2000, this, this.onEyesWakeupEnd);
    }

    private onEyesWakeupEnd() {

        this.visible = false;
        // this.isFinish = true;
       
        this.timer.once(500, this, () => {
            this.dialog.buildView.moveToBuilding(EBuildingType.BUILD_TYPE_BASE, 0.5);
        })
        this.timer.once(1200, this, () => {
            this.dialog.isScriptHideGuide = false;
        })
        console.log("-----onEyesWakeupEnd-----");
    }

    public initUI(): void {
        // this.isNewHeroGuide = TdMainDataCenter.ins.isFirstGuideAllFinish == false;
    }

    public addClick(): void {
    }

    public addEvent(): void {
        // this.addEventListener(ModuleCommand.ON_CLOSE_TD_GUIDE_NEW_HERO_DIALOG, this, this.onCloseNewHeroDialog);
    }

    protected onNewLogin() {
    }

    isOpenGuide() {
        return true;
    }


    checkGuide(currCfg: cfg_guide_helper) {
        let step = GuideMgr.ins.curStep;
        this.zOrder = LayerManager.STAGE_ZORDE_TOPEST;
        if(!this.recordStepMap.get(step)){
            this.recordStepMap.set(step, true);
            if (step == 0) {
                //建造主城
                // let vo: GuajiBuildVo = GuaJiBuildDataCenter.instance.getBuildVoByType(EBuildingType.BUILD_TYPE_BASE);
                // if(vo.buildLv > 0){
                //     GuideMgr.ins.curStep += 1;
                //     return;
                // }
            }
            else if(step == 1){
                //建造金矿
                this.timer.once(1500, this, () => {
                    this.buildView.moveToBuilding(EBuildingType.BUILD_TYPE_GOLD_MINE, 1);
                })

                // let vo: GuajiBuildVo = GuaJiBuildDataCenter.instance.getBuildVoByType(EBuildingType.BUILD_TYPE_GOLD_MINE);
                // if (vo.buildLv > 0) {
                //     GuideMgr.ins.curStep += 1;
                //     return;
                // }

                this.dialog.isScriptHideGuide = true;
               
                this.timer.once(2000, this, () => {
                    this.dialog.isScriptHideGuide = false;
                })
            }
            else if (step == 2) {
                this.buildView.moveToBuilding(EBuildingType.BUILD_TYPE_GOLD_MINE, 1);
                let vo: GuajiBuildVo = GuaJiBuildDataCenter.instance.getBuildVoByType(EBuildingType.BUILD_TYPE_GOLD_MINE);
                if (vo.buildLv > 1) {
                    GuideMgr.ins.skipGuide(GuajiBuildGuideScript.guide_wakeup_300001);
                    return;
                }
            }
            else if(step == 3){
                //帮玩家点击一次
                this.dialog.isScriptHideGuide = true;
                this.timer.once(1500, this, ()=>{

                    if (this.isSkipStep2){

                    }else{
                        GuideMgr.ins.curStep = 2;
                    }
                   
                    this.dialog.isScriptHideGuide = false;
                    // let vo = GuaJiBuildDataCenter.instance.getBuildVoByType(this.build_jinkuang_type);
                    // this.buildView.OnBuildClick(vo);
                })
            }
        }
    }

    onGuideEnd(guideId) {
    }

    clear(){

        if (this.imgBlackBg) {
            this.imgBlackBg.destroy();
            this.imgBlackBg = null;
        }
        // if (this.imgEyesWakeupMask) {
        //     this.imgEyesWakeupMask.destroy();
        //     this.imgEyesWakeupMask = null;
        // }
        if (this.boxWords) {
            this.boxWords.destroy();
            this.boxWords = null;
        }
        if (this.btnClose) {
            this.btnClose.destroy();
            this.btnClose = null;
        }
    }

    onDestroy(): void {

        super.onDestroy();
        this.clear();
    }
}
