
//数据不要用静态类型的
//可以在本模块引用，不可跨模块引用
//本模块引用的时候不要缓存instance，每次通过instance获取即可

import { MatchConst } from "../../../auto/ConstAuto";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { cfg_lord_treasure_level } from "../../../cfg/vo/cfg_lord_treasure_level";
import { Connection } from "../../../net/Connection";
import { p_lord_treasure } from "../../../proto/common/p_lord_treasure";
import { m_lord_treasure_op_tos } from "../../../proto/line/m_lord_treasure_op_tos";
import { GameUtil } from "../../../util/GameUtil";
import { ELordGroupType, LordDataCenter } from "../../Lord/data/LordDataCenter";

/**领主装备菜单 */
export enum ELordTreasureMenuType {
    /**宝物背包 */
    MENU_BAG = 1,
    /**宝物打造 */
    MENU_WORK = 2,
}


export enum ELordTreasureOpType {
    /**打造宝物 */
    OP_WORK = 1,
    /**强化宝物 */
    OP_STREANGTH = 2,
    /**锻造宝物 */
    OP_FORGE = 3,
    /**重置宝物 */
    OP_REMAKE = 4,
    /**装备宝物 */
    OP_LOAD = 5,
    /**卸下宝物 */
    OP_UNLOAD = 6,
    /**分享宝物 */
    OP_SHARE = 7
}


export class LordTreasureDataCenter {
    private static _instance: LordTreasureDataCenter = null;
    static get instance(): LordTreasureDataCenter {
        if (LordTreasureDataCenter._instance == null) {
            LordTreasureDataCenter._instance = new LordTreasureDataCenter();
        }
        return LordTreasureDataCenter._instance;
    }

    reset(): void {
        LordTreasureDataCenter._instance = null;
    }

    get allLordTreasureList() {
        return LordDataCenter.instance.info.lord_treasure_list;
    }

    getLordTreasureById(id: number) {
        return this.allLordTreasureList.find(v => v.id == id);
    }

    getLordTreasureByTypeId(typeId: number) {
        return this.allLordTreasureList.find(v => v.type_id == typeId);
    }

    /**
     * 宝物排列顺序：已穿戴>等级（从高到低）>可锻造次数（从少到多）>宝物ID（从大到小）
     * @param a 
     * @param b 
     * @param groupType 
     * @param matchType 
     * @returns 
     */
    sortLordTreasure(
        a: p_lord_treasure,
        b: p_lord_treasure,
        matchType: number = MatchConst.MATCH_TYPE_MAIN_BATTLE,
    ) {
        let indexA = this.getGroupTypeInfoBy(a.id, matchType)?.index
        let indexB = this.getGroupTypeInfoBy(b.id, matchType)?.index

        if (indexA != indexB) {
            return indexB - indexA;
        }

        if (a.level != b.level) {
            return b.level - a.level;
        }

        if (a.forge_times != b.forge_times) {
            return a.forge_times - b.forge_times;
        }

        return b.id - a.id;
    }



    /**通过treasureID获取主副队信息 */
    public getGroupTypeInfoBy(treasureID: number, matchType: number) {
        if (LordDataCenter.instance.lordLineUpList.has(matchType)) {
            for (const [index, item] of LordDataCenter.instance.lordLineUpList.get(matchType).entries()) {
                if (item.lord_treasure == treasureID) {
                    return item;
                }
            }
        } else {
            return null;
        }
    }



    /**获取领主宝物总强化效果 */
    public getTreasureEntry(type_id: number) {
        let treasureEntryList: cfg_lord_treasure_level[] = [];

        let newTreasureEntry = 0;
        CfgCacheMapMgr.cfg_lord_treasure_levelBytreasure_idCache.get(type_id).forEach(v => {
            let _treasure_entry = GameUtil.parseCfgByField(v, "treasure_entry_", { ingoreEmpty: true });
            if (_treasure_entry.length > newTreasureEntry) {
                treasureEntryList.push(v);
                newTreasureEntry++;
            }
        })

        return treasureEntryList;
    }



    //--------------协议发送------------------------
    /**
     * 发送领主宝物操作协议
     * @param op_type - 宝物操作类型，使用 ELordTreasureOpType 枚举值
     * @param lord_id - 领主 ID，默认为 0
     * @param treasure_id - 宝物 ID，默认为 0
     * @param match_type - 战斗类型，默认为主战斗类型（MatchConst.MATCH_TYPE_MAIN_BATTLE）
     * @param index - 领主分组类型，默认为主领主类型（ELordGroupType.TYPE_MAIN）
     */
    m_lord_treasure_op_tos(
        op_type: ELordTreasureOpType,
        lord_id: number = 0,
        treasure_id: number = 0,
        match_type: number = MatchConst.MATCH_TYPE_MAIN_BATTLE,
        index = ELordGroupType.TYPE_MAIN
    ) {
        let tos: m_lord_treasure_op_tos = new m_lord_treasure_op_tos();
        tos.op_type = op_type;
        tos.lord_id = lord_id;
        tos.treasure_id = treasure_id;
        tos.match_type = match_type;
        tos.index = index;
        Connection.instance.sendMessage(tos);
    }



}
