/**
 * 官渡之战 战斗相关 DataCenter
 */
import { ConfigManager } from "../../../managers/ConfigManager";
import { DispatchManager } from "../../../managers/DispatchManager";
import { LocalStorageUtil } from "../../../util/LocalStorageUtil";
import { FightDataCenter } from "../../fight/data/FightDataCenter";
import { LineUpDataCenter } from "../../lineUp/data/LineUpDataCenter";
import { GuanDuConst } from "../GuanDuConst";
import { GuanDuDataCenter } from "./GuanDuDataCenter";
import { MatchConst } from "../../../auto/ConstAuto";
//本模块引用的时候不要缓存instance，每次通过instance获取即可
export class GuanDuFightDataCenter {
    constructor() {
        /**当前选中的英雄id */
        this._selectHeroId = 0;
        /**自动扫荡 */
        this._isAutoFightIng = false;
        //是否显示怪物已击杀完 飘字提示
        this.isShowKillAllTip = false;
    }
    static get instance() {
        if (GuanDuFightDataCenter._instance == null) {
            GuanDuFightDataCenter._instance = new GuanDuFightDataCenter();
        }
        return GuanDuFightDataCenter._instance;
    }
    reset() {
        GuanDuFightDataCenter._instance = null;
    }
    set selectHeroId(id) {
        // if(id == this._selectHeroId) return;
        this._selectHeroId = id;
        DispatchManager.dispatchEvent("CHANGE_GUAN_DU_HERO_SELECT" /* CHANGE_GUAN_DU_HERO_SELECT */, id);
    }
    get selectHeroId() {
        return this._selectHeroId;
    }
    set isAutoFightIng(val) {
        this._isAutoFightIng = val;
    }
    get isAutoFightIng() {
        return this._isAutoFightIng;
    }
    getHeroByHeroId(hero_id) {
        if (!this.heroList)
            return null;
        for (let h of this.heroList) {
            if (h.hero_id == hero_id) {
                return h;
            }
        }
        return null;
    }
    /**获取英雄列表 */
    getHeroList() {
        return this.heroList;
    }
    getMonsterList() {
        return this.monsterList;
    }
    /**获取怪物列表 */
    getMonsterShowList() {
        if (!this.monsterList)
            return [];
        //筛选出没死亡的怪物
        let list = this.monsterList.filter(p => {
            return p.status != GuanDuConst.STATUS_ENEMY_DEATH;
        });
        //TODO 最后一个怪 先不显示，打完前4个后再显示
        if (list.length >= 2) {
            //过滤掉最后一个怪
            list = list.filter(p => p.level != 5);
        }
        return list;
    }
    updateHeroList(list) {
        this.heroList = list;
        //排序
        if (list) {
            list.sort((t1, t2) => t2.power - t1.power);
        }
        DispatchManager.dispatchEvent("UPDATE_GUAN_DU_HERO_LIST" /* UPDATE_GUAN_DU_HERO_LIST */);
    }
    updateMonsterList(list) {
        let oldList = this.monsterList;
        this.monsterList = list;
        //排序
        if (list) {
            list.sort((a, b) => a.level - b.level);
        }
        if (oldList) {
            let oldKillAll = true;
            for (let e of oldList) {
                if (e.status != GuanDuConst.STATUS_ENEMY_DEATH) {
                    oldKillAll = false;
                    break;
                }
            }
            let isKillAll = true;
            for (let e of list) {
                if (e.status != GuanDuConst.STATUS_ENEMY_DEATH) {
                    isKillAll = false;
                    break;
                }
            }
            if (oldKillAll == false && isKillAll == true) {
                //当前关卡的怪全部击杀完
                // TipsUtil.showTips("击败守关将领，可进入下一关");//不能在这飘 得重新打开界面才飘提示
                this.isShowKillAllTip = true;
            }
        }
        DispatchManager.dispatchEvent("UPDATE_GUAN_DU_MONSTER_LIST" /* UPDATE_GUAN_DU_MONSTER_LIST */);
    }
    getMonsterInfoByLevel(level) {
        if (!this.monsterList)
            return null;
        for (let m of this.monsterList) {
            if (m.level == level)
                return m;
        }
        return null;
    }
    getGuanDuCfg(order_id, floor = -1) {
        if (floor == -1) {
            floor = GuanDuDataCenter.instance.floor;
        }
        let cfgList = ConfigManager.cfg_guanduCache.get(floor);
        return cfgList[order_id - 1];
    }
    /**是否选中跳过战斗 */
    set isSelectSkipFight(val) {
        LocalStorageUtil.setUserLocalStorage("GuanDuSkipFight", val + "");
    }
    /**是否选中跳过战斗 */
    get isSelectSkipFight() {
        if (!this.isShowSkipFightCheckBox())
            return false;
        let b = LocalStorageUtil.CheckUserLocalStorage("GuanDuSkipFight");
        return b;
    }
    /**是否显示跳过战斗 勾选框 */
    isShowSkipFightCheckBox() {
        // let nums = MiscConst.guandu_pass_limit;
        // // if(GuanDuDataCenter.instance.floor >= nums[0] || DataCenter.vipLevel >= nums[1]){
        // //     return true;
        // // }
        // //2022/10/10 只保留VIP限制
        // if(DataCenter.vipLevel >= nums[1]){
        //     return true;
        // }
        // let yueKaType = EYueKaType.TYPE_4_KING;//2022/10/20 改为月卡激活
        // if(YueKaDataCenter.instance.isOpenFuliYueKa(yueKaType)){
        //     return true;
        // }
        // return false;
        return true; //2022/11/30 改为默认激活
    }
    /**战斗开始协议 */
    static guandu_fight_start_tos(boss_level, is_skin, targetname = "") {
        LineUpDataCenter.setFightSkip(MatchConst.MATCH_TYPE_GUAN_DU, is_skin);
        let lineUp = [];
        for (let i = 0; i < 9; i++) {
            lineUp.push(0);
        }
        lineUp[1] = GuanDuFightDataCenter.instance.selectHeroId;
        FightDataCenter.instance.m_fight_start_tos(MatchConst.MATCH_TYPE_GUAN_DU, boss_level, {
            is_show_skip: is_skin,
            forceSkip: is_skin,
            fighter_list: lineUp,
            targetname: targetname,
            finishevent: "GUAN_DU_FIGHT_FINISH" /* GUAN_DU_FIGHT_FINISH */
        });
    }
}
GuanDuFightDataCenter._instance = null;
