// gen by exportxlsx.py
import { Event } from "laya/events/Event";
import { CfgCacheMgr } from "./CfgCacheMgr";
import { CfgCacheMapBase } from "./CfgCacheMapBase";
import { CfgCacheMapMgr_CB } from "./CfgCacheMapMgr_CB";
export class CfgCacheMapMgr {
    static get is_complete() {
        return CfgCacheMgr.ins.is_complete;
    }
    static init() {
        var cache_mgr = CfgCacheMgr.ins;
        var cachemap_mgr = CfgCacheMapMgr;
        cachemap_mgr.cfg_acc_giftCache = new CfgCacheMapBase(cache_mgr.cfg_acc_gift_cache, 4 /* GRP */, "big_type");
        cachemap_mgr.cfg_achievementCache = new CfgCacheMapBase(cache_mgr.cfg_achievement_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_activity_clientCache = new CfgCacheMapBase(cache_mgr.cfg_activity_client_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_activity_descCache = new CfgCacheMapBase(cache_mgr.cfg_activity_desc_cache, 1 /* ID */, "type");
        cachemap_mgr.cfg_activity_iconCache = new CfgCacheMapBase(cache_mgr.cfg_activity_icon_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_activity_iconParentCache = new CfgCacheMapBase(cache_mgr.cfg_activity_icon_cache, 4 /* GRP */, "parentID");
        cachemap_mgr.cfg_activity_limit_signCache = new CfgCacheMapBase(cache_mgr.cfg_activity_limit_sign_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_activity_miscCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_activity_misc_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_activity_missionCache = new CfgCacheMapBase(cache_mgr.cfg_activity_mission_cache, 5 /* MGRP */, "rewards_id,act_type");
        cachemap_mgr.cfg_activity_missionAllCache = new CfgCacheMapBase(cache_mgr.cfg_activity_mission_cache, 3 /* MKEY */, "rewards_id,act_type,id");
        cachemap_mgr.cfg_activity_noticeCache = new CfgCacheMapBase(cache_mgr.cfg_activity_notice_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_activity_pageCache = new CfgCacheMapBase(cache_mgr.cfg_activity_page_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_activity_yuekaCache = new CfgCacheMapBase(cache_mgr.cfg_activity_yueka_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_ad_showCache = new CfgCacheMapBase(cache_mgr.cfg_ad_show_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_agent_reviewCache = new CfgCacheMapBase(cache_mgr.cfg_agent_review_cache, 2 /* KEY */, "platform");
        cachemap_mgr.cfg_all_pinyin_dictCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_all_pinyin_dict_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_arenaCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_arena_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_arena_matchCache = new CfgCacheMapBase(cache_mgr.cfg_arena_match_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_arena_match_guess_allCache = new CfgCacheMapBase(cache_mgr.cfg_arena_match_guess_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_arena_max_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_arena_max_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_arena_rankCache = new CfgCacheMapBase(cache_mgr.cfg_arena_rank_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_arena_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_arena_reward_cache, 2 /* KEY */, "min_times");
        cachemap_mgr.cfg_arena_skip_limitCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_arena_skip_limit_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_arena_weekly_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_arena_weekly_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_ares_palaceCache = new CfgCacheMapBase(cache_mgr.cfg_ares_palace_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_authorized_giftsCache = new CfgCacheMapBase(cache_mgr.cfg_authorized_gifts_cache, 2 /* KEY */, "platform");
        cachemap_mgr.cfg_bag_pageCallCache = new CfgCacheMapBase(cache_mgr.cfg_bag_page_cache, 1 /* ID */, "index");
        cachemap_mgr.cfg_bai_jiang_giftCache = new CfgCacheMapBase(cache_mgr.cfg_bai_jiang_gift_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_battle_fly_nameCache = new CfgCacheMapBase(cache_mgr.cfg_battle_fly_name_cache, 3 /* MKEY */, "id,level");
        cachemap_mgr.cfg_battle_trial_buffCache = new CfgCacheMapBase(cache_mgr.cfg_battle_trial_buff_cache, 3 /* MKEY */, "type,level");
        cachemap_mgr.cfg_battle_trial_buff_listCache = new CfgCacheMapBase(cache_mgr.cfg_battle_trial_buff_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_battle_trial_buff_resetCache = new CfgCacheMapBase(cache_mgr.cfg_battle_trial_buff_reset_cache, 2 /* KEY */, "count");
        cachemap_mgr.cfg_battle_trial_guaji_monsterCache = new CfgCacheMapBase(cache_mgr.cfg_battle_trial_guaji_monster_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_battle_trial_miscCache = new CfgCacheMapBase(cache_mgr.cfg_battle_trial_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_battle_trial_pass_guanqiaCache = new CfgCacheMapBase(cache_mgr.cfg_battle_trial_pass_guanqia_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_battle_trial_pass_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_battle_trial_pass_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_beast_platformCache = new CfgCacheMapBase(cache_mgr.cfg_beast_platform_cache, 2 /* KEY */, "platform_id");
        cachemap_mgr.cfg_bingfaCache = new CfgCacheMapBase(cache_mgr.cfg_bingfa_cache, 3 /* MKEY */, "skill_level,skill_id");
        cachemap_mgr.cfg_bingfaLevelCache = new CfgCacheMapBase(cache_mgr.cfg_bingfa_cache, 4 /* GRP */, "skill_level");
        cachemap_mgr.cfg_bingfa_extCache = new CfgCacheMapBase(cache_mgr.cfg_bingfa_ext_cache, 3 /* MKEY */, "skill_level,skill_id");
        cachemap_mgr.cfg_bingfa_extBySkillIdCache = new CfgCacheMapBase(cache_mgr.cfg_bingfa_ext_cache, 4 /* GRP */, "skill_id");
        cachemap_mgr.cfg_bingfa_extBySkillLevelListCache = new CfgCacheMapBase(cache_mgr.cfg_bingfa_ext_cache, 4 /* GRP */, "skill_level");
        cachemap_mgr.cfg_bingfuCache = new CfgCacheMapBase(cache_mgr.cfg_bingfu_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_bingfu_discompose_recastCache = new CfgCacheMapBase(cache_mgr.cfg_bingfu_discompose_recast_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_bingfu_recast_lockCache = new CfgCacheMapBase(cache_mgr.cfg_bingfu_recast_lock_cache, 1 /* ID */, "skill_level");
        cachemap_mgr.cfg_bingfu_refineCache = new CfgCacheMapBase(cache_mgr.cfg_bingfu_refine_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_bingfu_refine_TargetIdCache = new CfgCacheMapBase(cache_mgr.cfg_bingfu_refine_cache, 2 /* KEY */, "target_id");
        cachemap_mgr.cfg_bingfu_upgradeCache = new CfgCacheMapBase(cache_mgr.cfg_bingfu_upgrade_cache, 3 /* MKEY */, "color,need_num");
        cachemap_mgr.cfg_boat_peak_miscCache = new CfgCacheMapBase(cache_mgr.cfg_boat_peak_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_boat_peak_rankCache = new CfgCacheMapBase(cache_mgr.cfg_boat_peak_rank_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_boat_peak_timeCache = new CfgCacheMapBase(cache_mgr.cfg_boat_peak_time_cache, 3 /* MKEY */, "day,round,step");
        cachemap_mgr.cfg_boat_peak_time_day_roundCache = new CfgCacheMapBase(cache_mgr.cfg_boat_peak_time_cache, 5 /* MGRP */, "day,round");
        cachemap_mgr.cfg_boat_peak_time_round_stepCache = new CfgCacheMapBase(cache_mgr.cfg_boat_peak_time_cache, 3 /* MKEY */, "round,step");
        cachemap_mgr.cfg_boat_racc_rank_goldCache = new CfgCacheMapBase(cache_mgr.cfg_boat_racc_rank_gold_cache, 2 /* KEY */, "min_rank");
        cachemap_mgr.cfg_boat_race_auction_reward_typeCache = new CfgCacheMapBase(cache_mgr.cfg_boat_race_auction_reward_type_cache, 2 /* KEY */, "type_id");
        cachemap_mgr.cfg_boat_race_itemCache = new CfgCacheMapBase(cache_mgr.cfg_boat_race_item_cache, 2 /* KEY */, "type_id");
        cachemap_mgr.cfg_boat_race_miscCache = new CfgCacheMapBase(cache_mgr.cfg_boat_race_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_buffCache = new CfgCacheMapBase(cache_mgr.cfg_buff_cache, 1 /* ID */, "uid");
        cachemap_mgr.cfg_buff_typeCache = new CfgCacheMapBase(cache_mgr.cfg_buff_type_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_buildingCache = new CfgCacheMapBase(cache_mgr.cfg_building_cache, 2 /* KEY */, "type");
        cachemap_mgr.cfg_building_lvCache = new CfgCacheMapBase(cache_mgr.cfg_building_lv_cache, 3 /* MKEY */, "type,level");
        cachemap_mgr.cfg_building_missionCache = new CfgCacheMapBase(cache_mgr.cfg_building_mission_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_building_plot_dialogueCache = new CfgCacheMapBase(cache_mgr.cfg_building_plot_dialogue_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_buy_timesCache = new CfgCacheMapBase(cache_mgr.cfg_buy_times_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_buy_times_typeCache = new CfgCacheMapBase(cache_mgr.cfg_buy_times_type_cache, 2 /* KEY */, "type");
        cachemap_mgr.cfg_cast_soul_activeCache = new CfgCacheMapBase(cache_mgr.cfg_cast_soul_active_cache, 1 /* ID */, "nation");
        cachemap_mgr.cfg_casting_soulCache = new CfgCacheMapBase(cache_mgr.cfg_casting_soul_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_casting_soul_buffCache = new CfgCacheMapBase(cache_mgr.cfg_casting_soul_buff_cache, 3 /* MKEY */, "item_kind,casting_soul_level");
        cachemap_mgr.cfg_casting_soul_kind_buffCache = new CfgCacheMapBase(cache_mgr.cfg_casting_soul_buff_cache, 4 /* GRP */, "item_kind");
        cachemap_mgr.cfg_chapter_dialogCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_chapter_dialog_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_chapter_scriptCache = new CfgCacheMapBase(cache_mgr.cfg_chapter_script_cache, 4 /* GRP */, "script_id");
        cachemap_mgr.cfg_chat_bullet_msgCache = new CfgCacheMapBase(cache_mgr.cfg_chat_bullet_msg_cache, 1 /* ID */, "type");
        cachemap_mgr.cfg_chat_channelCache = new CfgCacheMapBase(cache_mgr.cfg_chat_channel_cache, 2 /* KEY */, "type");
        cachemap_mgr.cfg_chat_skinCache = new CfgCacheMapBase(cache_mgr.cfg_chat_skin_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_chat_skin_widgetCache = new CfgCacheMapBase(cache_mgr.cfg_chat_skin_widget_cache, 2 /* KEY */, "widget_id");
        cachemap_mgr.cfg_client_langCache = new CfgCacheMapBase(cache_mgr.cfg_client_lang_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_client_w3_effectCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_client_w3_effect_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_client_w3_effect_descCache = new CfgCacheMapBase(cache_mgr.cfg_client_w3_effect_desc_cache, 2 /* KEY */, "field");
        cachemap_mgr.cfg_client_w3_skillCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_client_w3_skill_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_client_w3_skill_descCache = new CfgCacheMapBase(cache_mgr.cfg_client_w3_skill_desc_cache, 2 /* KEY */, "field");
        cachemap_mgr.cfg_client_w3_skinCache = new CfgCacheMapBase(cache_mgr.cfg_client_w3_skin_cache, 2 /* KEY */, "skin_id");
        cachemap_mgr.cfg_cmdCache = new CfgCacheMapBase(cache_mgr.cfg_cmd_cache, 4 /* GRP */, "group");
        cachemap_mgr.cfg_code_cliCache = new CfgCacheMapBase(cache_mgr.cfg_code_cli_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_country_war_miscCache = new CfgCacheMapBase(cache_mgr.cfg_country_war_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_cross_team_match_typeCache = new CfgCacheMapBase(cache_mgr.cfg_cross_team_match_type_cache, 1 /* ID */, "match_type");
        cachemap_mgr.cfg_cross_team_match_type_data_listCache = new CfgCacheMapBase(cache_mgr.cfg_cross_team_match_type_cache, 4 /* GRP */, "lineup_data_type");
        cachemap_mgr.cfg_cross_team_miscCache = new CfgCacheMapBase(cache_mgr.cfg_cross_team_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_cross_test_tower_openCache = new CfgCacheMapBase(cache_mgr.cfg_cross_test_tower_open_cache, 1 /* ID */, "nation");
        cachemap_mgr.cfg_crush_fight_conditionCache = new CfgCacheMapBase(cache_mgr.cfg_crush_fight_condition_cache, 1 /* ID */, "match_type");
        cachemap_mgr.cfg_crush_fight_missionCache = new CfgCacheMapBase(cache_mgr.cfg_crush_fight_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_crush_fight_mission_typeCache = new CfgCacheMapBase(cache_mgr.cfg_crush_fight_mission_type_cache, 1 /* ID */, "type");
        cachemap_mgr.cfg_csc_fmsolo_buffCache = new CfgCacheMapBase(cache_mgr.cfg_csc_fmsolo_buff_cache, 2 /* KEY */, "buff_level");
        cachemap_mgr.cfg_csc_fmsolo_challengeCache = new CfgCacheMapBase(cache_mgr.cfg_csc_fmsolo_challenge_cache, 3 /* MKEY */, "rank_sn,star");
        cachemap_mgr.cfg_csc_fmsolo_etcCache = new CfgCacheMapBase(cache_mgr.cfg_csc_fmsolo_etc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_csc_fmsolo_logCache = new CfgCacheMapBase(cache_mgr.cfg_csc_fmsolo_log_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_csc_fmsolo_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_csc_fmsolo_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_csc_fmsolo_rewardTypeCache = new CfgCacheMapBase(cache_mgr.cfg_csc_fmsolo_reward_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_csc_fmsolo_shop_lvCache = new CfgCacheMapBase(cache_mgr.cfg_csc_fmsolo_shop_lv_cache, 2 /* KEY */, "shop_level");
        cachemap_mgr.cfg_csclanCache = new CfgCacheMapBase(cache_mgr.cfg_csclan_cache, 2 /* KEY */, "min_power");
        cachemap_mgr.cfg_csclan_etcCache = new CfgCacheMapBase(cache_mgr.cfg_csclan_etc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_csclan_solo_buffCache = new CfgCacheMapBase(cache_mgr.cfg_csclan_solo_buff_cache, 2 /* KEY */, "buff_level");
        cachemap_mgr.cfg_csclan_solo_challengeCache = new CfgCacheMapBase(cache_mgr.cfg_csclan_solo_challenge_cache, 3 /* MKEY */, "rank_sn,star");
        cachemap_mgr.cfg_csclan_solo_etcCache = new CfgCacheMapBase(cache_mgr.cfg_csclan_solo_etc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_csclan_solo_logCache = new CfgCacheMapBase(cache_mgr.cfg_csclan_solo_log_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_csclan_solo_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_csclan_solo_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_csclan_solo_rewardTypeCache = new CfgCacheMapBase(cache_mgr.cfg_csclan_solo_reward_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_csclan_solo_shop_lvCache = new CfgCacheMapBase(cache_mgr.cfg_csclan_solo_shop_lv_cache, 2 /* KEY */, "shop_level");
        cachemap_mgr.cfg_daily_copyCache = new CfgCacheMapBase(cache_mgr.cfg_daily_copy_cache, 4 /* GRP */, "copy_type");
        cachemap_mgr.cfg_daily_copy_discountCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_daily_copy_discount_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_daily_copy_monsterCache = new CfgCacheMapBase(cache_mgr.cfg_daily_copy_monster_cache, 3 /* MKEY */, "map_id,wave");
        cachemap_mgr.cfg_daily_copy_typeCache = new CfgCacheMapBase(cache_mgr.cfg_daily_copy_type_cache, 1 /* ID */, "copy_type");
        cachemap_mgr.cfg_daily_missionCache = new CfgCacheMapBase(cache_mgr.cfg_daily_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_daily_mission_giftCache = new CfgCacheMapBase(cache_mgr.cfg_daily_mission_gift_cache, 1 /* ID */, "score");
        cachemap_mgr.cfg_daily_new_discount_rebateCache = new CfgCacheMapBase(cache_mgr.cfg_daily_new_discount_rebate_cache, 1 /* ID */, "day");
        cachemap_mgr.cfg_daily_new_discount_rebateByGroupCache = new CfgCacheMapBase(cache_mgr.cfg_daily_new_discount_rebate_cache, 4 /* GRP */, "floor");
        cachemap_mgr.cfg_daily_payCache = new CfgCacheMapBase(cache_mgr.cfg_daily_pay_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_dawankaCache = new CfgCacheMapBase(cache_mgr.cfg_dawanka_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_dawanka_tequanGroupCache = new CfgCacheMapBase(cache_mgr.cfg_dawanka_tequan_cache, 4 /* GRP */, "level");
        cachemap_mgr.cfg_dawanka_tequanCache = new CfgCacheMapBase(cache_mgr.cfg_dawanka_tequan_cache, 3 /* MKEY */, "level,type");
        cachemap_mgr.cfg_day_acc_pay_giftCache = new CfgCacheMapBase(cache_mgr.cfg_day_acc_pay_gift_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_deputyCache = new CfgCacheMapBase(cache_mgr.cfg_deputy_cache, 2 /* KEY */, "hero_type_id");
        cachemap_mgr.cfg_deputy_levelCache = new CfgCacheMapBase(cache_mgr.cfg_deputy_level_cache, 2 /* KEY */, "level");
        cachemap_mgr.cfg_deputy_starCache = new CfgCacheMapBase(cache_mgr.cfg_deputy_star_cache, 2 /* KEY */, "star");
        cachemap_mgr.cfg_device_excursion_name1Cache = new CfgCacheMapBase(cache_mgr.cfg_device_excursion_cache, 2 /* KEY */, "name_1");
        cachemap_mgr.cfg_device_excursion_name2Cache = new CfgCacheMapBase(cache_mgr.cfg_device_excursion_cache, 2 /* KEY */, "name_2");
        cachemap_mgr.cfg_divineCache = new CfgCacheMapBase(cache_mgr.cfg_divine_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_divine_activateCache = new CfgCacheMapBase(cache_mgr.cfg_divine_activate_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_divine_copyCache = new CfgCacheMapBase(cache_mgr.cfg_divine_copy_cache, 1 /* ID */, "storey");
        cachemap_mgr.cfg_divine_strengthenCache = new CfgCacheMapBase(cache_mgr.cfg_divine_strengthen_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_divine_wearCache = new CfgCacheMapBase(cache_mgr.cfg_divine_wear_cache, 1 /* ID */, "career");
        cachemap_mgr.cfg_dominate_pvpCache = new CfgCacheMapBase(cache_mgr.cfg_dominate_pvp_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_dominate_pvpGroupCache = new CfgCacheMapBase(cache_mgr.cfg_dominate_pvp_cache, 4 /* GRP */, "big_level");
        cachemap_mgr.cfg_dominate_pvp_limitCache = new CfgCacheMapBase(cache_mgr.cfg_dominate_pvp_limit_cache, 1 /* ID */, "type");
        cachemap_mgr.cfg_dominate_pvp_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_dominate_pvp_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_dominate_pvp_taskCache = new CfgCacheMapBase(cache_mgr.cfg_dominate_pvp_task_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_dominate_pvp_task_rewardsCache = new CfgCacheMapBase(cache_mgr.cfg_dominate_pvp_task_rewards_cache, 1 /* ID */, "grade");
        cachemap_mgr.cfg_drop_groupCache = new CfgCacheMapBase(cache_mgr.cfg_drop_group_cache, 4 /* GRP */, "group_id");
        cachemap_mgr.cfg_eight_loginCache = new CfgCacheMapBase(cache_mgr.cfg_eight_login_cache, 1 /* ID */, "day");
        cachemap_mgr.cfg_epic_battle_tipsCache = new CfgCacheMapBase(cache_mgr.cfg_epic_battle_tips_cache, 1 /* ID */, "sys_id");
        cachemap_mgr.cfg_equipCache = new CfgCacheMapBase(cache_mgr.cfg_equip_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_equipbySuitIdCache = new CfgCacheMapBase(cache_mgr.cfg_equip_cache, 4 /* GRP */, "suit_id");
        cachemap_mgr.cfg_equip_composeCache = new CfgCacheMapBase(cache_mgr.cfg_equip_compose_cache, 2 /* KEY */, "item_id");
        cachemap_mgr.cfg_equip_compose_target_idCache = new CfgCacheMapBase(cache_mgr.cfg_equip_compose_cache, 2 /* KEY */, "target_item_id");
        cachemap_mgr.cfg_equip_suitCache = new CfgCacheMapBase(cache_mgr.cfg_equip_suit_cache, 4 /* GRP */, "id");
        cachemap_mgr.cfg_equip_suit_extCache = new CfgCacheMapBase(cache_mgr.cfg_equip_suit_ext_cache, 1 /* ID */, "suit_id");
        cachemap_mgr.cfg_fail_tipsCache = new CfgCacheMapBase(cache_mgr.cfg_fail_tips_cache, 1 /* ID */, "index");
        cachemap_mgr.cfg_familyCache = new CfgCacheMapBase(cache_mgr.cfg_family_cache, 2 /* KEY */, "level");
        cachemap_mgr.cfg_family_active_attrCache = new CfgCacheMapBase(cache_mgr.cfg_family_active_attr_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_family_active_missionCache = new CfgCacheMapBase(cache_mgr.cfg_family_active_mission_cache, 1 /* ID */, "type");
        cachemap_mgr.cfg_family_bossCache = new CfgCacheMapBase(cache_mgr.cfg_family_boss_cache, 2 /* KEY */, "pass");
        cachemap_mgr.cfg_family_boss_attrCache = new CfgCacheMapBase(cache_mgr.cfg_family_boss_attr_cache, 2 /* KEY */, "times");
        cachemap_mgr.cfg_family_boss_miscCache = new CfgCacheMapBase(cache_mgr.cfg_family_boss_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_family_boss_rankCache = new CfgCacheMapBase(cache_mgr.cfg_family_boss_rank_cache, 4 /* GRP */, "pass");
        cachemap_mgr.cfg_family_etcCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_family_etc_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_family_hongbaoCache = new CfgCacheMapBase(cache_mgr.cfg_family_hongbao_cache, 1 /* ID */, "type");
        cachemap_mgr.cfg_family_hongbaoItemCache = new CfgCacheMapBase(cache_mgr.cfg_family_hongbao_cache, 2 /* KEY */, "item_type_id");
        cachemap_mgr.cfg_family_hongbao_blessingCache = new CfgCacheMapBase(cache_mgr.cfg_family_hongbao_blessing_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_family_hongbao_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_family_hongbao_reward_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_family_logCache = new CfgCacheMapBase(cache_mgr.cfg_family_log_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_family_random_nameCache = new CfgCacheMapBase(cache_mgr.cfg_family_random_name_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_family_scienceCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_family_science_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_family_science_timesCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_family_science_times_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_family_signCache = new CfgCacheMapBase(cache_mgr.cfg_family_sign_cache, 1 /* ID */, "type");
        cachemap_mgr.cfg_family_sign_activeCache = new CfgCacheMapBase(cache_mgr.cfg_family_sign_active_cache, 1 /* ID */, "need_score");
        cachemap_mgr.cfg_fight_showCache = new CfgCacheMapBase(cache_mgr.cfg_fight_show_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_first_payCache = new CfgCacheMapBase(cache_mgr.cfg_first_pay_cache, 1 /* ID */, "gift_id");
        cachemap_mgr.cfg_fishCache = new CfgCacheMapBase(cache_mgr.cfg_fish_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_fishByGroupCache = new CfgCacheMapBase(cache_mgr.cfg_fish_cache, 4 /* GRP */, "slot");
        cachemap_mgr.cfg_fish_attrCache = new CfgCacheMapBase(cache_mgr.cfg_fish_attr_cache, 1 /* ID */, "attr_key");
        cachemap_mgr.cfg_fish_colorCache = new CfgCacheMapBase(cache_mgr.cfg_fish_color_cache, 1 /* ID */, "color");
        cachemap_mgr.cfg_fish_fishbowlCache = new CfgCacheMapBase(cache_mgr.cfg_fish_fishbowl_cache, 1 /* ID */, "fishbowl_id");
        cachemap_mgr.cfg_fish_handbookCache = new CfgCacheMapBase(cache_mgr.cfg_fish_handbook_cache, 4 /* GRP */, "map_id");
        cachemap_mgr.cfg_fish_handbookByIdCache = new CfgCacheMapBase(cache_mgr.cfg_fish_handbook_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_fish_help_giftCache = new CfgCacheMapBase(cache_mgr.cfg_fish_help_gift_cache, 1 /* ID */, "gift_id");
        cachemap_mgr.cfg_fish_levelCache = new CfgCacheMapBase(cache_mgr.cfg_fish_level_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_fish_mapCache = new CfgCacheMapBase(cache_mgr.cfg_fish_map_cache, 1 /* ID */, "map_id");
        cachemap_mgr.cfg_fish_miscCache = new CfgCacheMapBase(cache_mgr.cfg_fish_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_fish_missionCache = new CfgCacheMapBase(cache_mgr.cfg_fish_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_fish_mission_guide_idCache = new CfgCacheMapBase(cache_mgr.cfg_fish_mission_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_fish_official_positionCache = new CfgCacheMapBase(cache_mgr.cfg_fish_official_position_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_fish_reshapeCache = new CfgCacheMapBase(cache_mgr.cfg_fish_reshape_cache, 3 /* MKEY */, "slot,color");
        cachemap_mgr.cfg_fish_resourcesCache = new CfgCacheMapBase(cache_mgr.cfg_fish_resources_cache, 3 /* MKEY */, "map_id,type_id");
        cachemap_mgr.cfg_fish_resourcesByHandbookTypeCache = new CfgCacheMapBase(cache_mgr.cfg_fish_resources_cache, 4 /* GRP */, "handbook_type");
        cachemap_mgr.cfg_fish_rod_levelCache = new CfgCacheMapBase(cache_mgr.cfg_fish_rod_level_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_fish_slotCache = new CfgCacheMapBase(cache_mgr.cfg_fish_slot_cache, 1 /* ID */, "slot");
        cachemap_mgr.cfg_fish_stageCache = new CfgCacheMapBase(cache_mgr.cfg_fish_stage_cache, 1 /* ID */, "stage");
        cachemap_mgr.cfg_fly_font_typeCache = new CfgCacheMapBase(cache_mgr.cfg_fly_font_type_cache, 2 /* KEY */, "from_type");
        cachemap_mgr.cfg_fuli_signCache = new CfgCacheMapBase(cache_mgr.cfg_fuli_sign_cache, 4 /* GRP */, "id");
        cachemap_mgr.cfg_fuli_sign_accCache = new CfgCacheMapBase(cache_mgr.cfg_fuli_sign_acc_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_fuli_sign_dayCache = new CfgCacheMapBase(cache_mgr.cfg_fuli_sign_day_cache, 1 /* ID */, "days_num");
        cachemap_mgr.cfg_fuli_tokenCache = new CfgCacheMapBase(cache_mgr.cfg_fuli_token_cache, 3 /* MKEY */, "type,level,rewards_id");
        cachemap_mgr.cfg_fuli_tokenByLevelCache = new CfgCacheMapBase(cache_mgr.cfg_fuli_token_cache, 5 /* MGRP */, "type,level");
        cachemap_mgr.cfg_fuli_token_typeCache = new CfgCacheMapBase(cache_mgr.cfg_fuli_token_type_cache, 3 /* MKEY */, "type,level");
        cachemap_mgr.cfg_fuli_yuekaCache = new CfgCacheMapBase(cache_mgr.cfg_fuli_yueka_cache, 1 /* ID */, "yueka_type");
        cachemap_mgr.cfg_game_descCache = new CfgCacheMapBase(cache_mgr.cfg_game_desc_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_game_desc_2CacheTemp = new CfgCacheMapBase(cache_mgr.cfg_game_desc_2_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_game_desc_3Cache = new CfgCacheMapBase(cache_mgr.cfg_game_desc_3_cache, 4 /* GRP */, "group_name");
        cachemap_mgr.cfg_general_pass_missionCache = new CfgCacheMapBase(cache_mgr.cfg_general_pass_mission_cache, 3 /* MKEY */, "task_id,pass_type,turn");
        cachemap_mgr.cfg_general_pass_missionBypass_typeandturnCache = new CfgCacheMapBase(cache_mgr.cfg_general_pass_mission_cache, 5 /* MGRP */, "pass_type,turn");
        cachemap_mgr.cfg_general_pass_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_general_pass_reward_cache, 3 /* MKEY */, "turn,pass_type,sort_id");
        cachemap_mgr.cfg_general_pass_rewardByTurnCache = new CfgCacheMapBase(cache_mgr.cfg_general_pass_reward_cache, 5 /* MGRP */, "turn,pass_type");
        cachemap_mgr.cfg_general_pass_rewardIdCache = new CfgCacheMapBase(cache_mgr.cfg_general_pass_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_general_pass_typeCache = new CfgCacheMapBase(cache_mgr.cfg_general_pass_type_cache, 3 /* MKEY */, "turn,pass_type");
        cachemap_mgr.cfg_general_pass_typeByprocess_typeCache = new CfgCacheMapBase(cache_mgr.cfg_general_pass_type_cache, 4 /* GRP */, "pass_type");
        cachemap_mgr.cfg_giftCache = new CfgCacheMapBase(cache_mgr.cfg_gift_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_god_equipCache = new CfgCacheMapBase(cache_mgr.cfg_god_equip_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_god_equip_starCache = new CfgCacheMapBase(cache_mgr.cfg_god_equip_cache, 4 /* GRP */, "star");
        cachemap_mgr.cfg_god_equip_composeCache = new CfgCacheMapBase(cache_mgr.cfg_god_equip_compose_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_god_equip_compose_itemCache = new CfgCacheMapBase(cache_mgr.cfg_god_equip_compose_cache, 2 /* KEY */, "item_id");
        cachemap_mgr.cfg_god_equip_convertCache = new CfgCacheMapBase(cache_mgr.cfg_god_equip_convert_cache, 2 /* KEY */, "star");
        cachemap_mgr.cfg_god_equip_enchantCache = new CfgCacheMapBase(cache_mgr.cfg_god_equip_enchant_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_god_equip_enchant_costCache = new CfgCacheMapBase(cache_mgr.cfg_god_equip_enchant_cost_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_god_equip_suitCache = new CfgCacheMapBase(cache_mgr.cfg_god_equip_suit_cache, 3 /* MKEY */, "star,suit_id");
        cachemap_mgr.cfg_god_equip_suitByStarCache = new CfgCacheMapBase(cache_mgr.cfg_god_equip_suit_cache, 4 /* GRP */, "star");
        cachemap_mgr.cfg_god_equip_typeCache = new CfgCacheMapBase(cache_mgr.cfg_god_equip_type_cache, 3 /* MKEY */, "type,color,star");
        cachemap_mgr.cfg_god_trialCache = new CfgCacheMapBase(cache_mgr.cfg_god_trial_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_god_trial_buffCache = new CfgCacheMapBase(cache_mgr.cfg_god_trial_buff_cache, 1 /* ID */, "buff_id");
        cachemap_mgr.cfg_god_weaponCache = new CfgCacheMapBase(cache_mgr.cfg_god_weapon_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_god_weapon_levelCache = new CfgCacheMapBase(cache_mgr.cfg_god_weapon_level_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_god_weapon_missionCache = new CfgCacheMapBase(cache_mgr.cfg_god_weapon_mission_cache, 1 /* ID */, "task_id");
        cachemap_mgr.cfg_god_weapon_missionTypeCache = new CfgCacheMapBase(cache_mgr.cfg_god_weapon_mission_cache, 4 /* GRP */, "type_id");
        cachemap_mgr.cfg_god_weapon_refineCache = new CfgCacheMapBase(cache_mgr.cfg_god_weapon_refine_cache, 3 /* MKEY */, "level,plan_id");
        cachemap_mgr.cfg_god_weapon_plan_refineCache = new CfgCacheMapBase(cache_mgr.cfg_god_weapon_refine_cache, 3 /* MKEY */, "level,plan_id");
        cachemap_mgr.cfg_god_weapon_skillCache = new CfgCacheMapBase(cache_mgr.cfg_god_weapon_skill_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_god_weapon_skill_attrTypeCache = new CfgCacheMapBase(cache_mgr.cfg_god_weapon_skill_attr_cache, 4 /* GRP */, "id");
        cachemap_mgr.cfg_god_weapon_soulCache = new CfgCacheMapBase(cache_mgr.cfg_god_weapon_soul_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_god_weapon_starCache = new CfgCacheMapBase(cache_mgr.cfg_god_weapon_star_cache, 3 /* MKEY */, "god_weapon_id,star");
        cachemap_mgr.cfg_gray_pinyinCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_gray_pinyin_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_grow_tipsCache = new CfgCacheMapBase(cache_mgr.cfg_grow_tips_cache, 2 /* KEY */, "sys_id");
        cachemap_mgr.cfg_grow_tipsTagMap = new CfgCacheMapBase(cache_mgr.cfg_grow_tips_cache, 4 /* GRP */, "show_tag");
        cachemap_mgr.cfg_guaji_box_timeCache = new CfgCacheMapBase(cache_mgr.cfg_guaji_box_time_cache, 1 /* ID */, "min_time");
        cachemap_mgr.cfg_guaji_monsterCache = new CfgCacheMapBase(cache_mgr.cfg_guaji_monster_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_guaji_quick_navigationCache = new CfgCacheMapBase(cache_mgr.cfg_guaji_quick_navigation_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_guanduCache = new CfgCacheMapBase(cache_mgr.cfg_guandu_cache, 4 /* GRP */, "floor");
        cachemap_mgr.cfg_guandu_answerCache = new CfgCacheMapBase(cache_mgr.cfg_guandu_answer_cache, 1 /* ID */, "title_id");
        cachemap_mgr.cfg_guandu_choseCache = new CfgCacheMapBase(cache_mgr.cfg_guandu_chose_cache, 1 /* ID */, "title_id");
        cachemap_mgr.cfg_guandu_floorCache = new CfgCacheMapBase(cache_mgr.cfg_guandu_floor_cache, 1 /* ID */, "floor");
        cachemap_mgr.cfg_guandu_missionCache = new CfgCacheMapBase(cache_mgr.cfg_guandu_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_guandu_shopCache = new CfgCacheMapBase(cache_mgr.cfg_guandu_shop_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_guide_helperCache = new CfgCacheMapBase(cache_mgr.cfg_guide_helper_cache, 4 /* GRP */, "guide_id");
        cachemap_mgr.cfg_guide_helper_1Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_helper_1_cache, 4 /* GRP */, "guide_id");
        cachemap_mgr.cfg_guide_helper_2Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_helper_2_cache, 4 /* GRP */, "guide_id");
        cachemap_mgr.cfg_guide_helper_3Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_helper_3_cache, 4 /* GRP */, "guide_id");
        cachemap_mgr.cfg_guide_helper_4Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_helper_4_cache, 4 /* GRP */, "guide_id");
        cachemap_mgr.cfg_guide_helper_game_1Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_helper_game_1_cache, 4 /* GRP */, "guide_id");
        cachemap_mgr.cfg_guide_helper_m2Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_helper_m2_cache, 4 /* GRP */, "guide_id");
        cachemap_mgr.cfg_guide_helper_m3Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_helper_m3_cache, 4 /* GRP */, "guide_id");
        cachemap_mgr.cfg_guide_missionCache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_guide_uinameCache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_cache, 2 /* KEY */, "ui_name");
        cachemap_mgr.guideRearIdsCache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_cache, 2 /* KEY */, "pre_id");
        cachemap_mgr.cfg_guide_mission_1Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_1_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_guide_uiname_1Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_1_cache, 2 /* KEY */, "ui_name");
        cachemap_mgr.guideRearIds_1Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_1_cache, 2 /* KEY */, "pre_id");
        cachemap_mgr.cfg_guide_mission_2Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_2_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_guide_uiname_2Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_2_cache, 2 /* KEY */, "ui_name");
        cachemap_mgr.guideRearIds_2Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_2_cache, 2 /* KEY */, "pre_id");
        cachemap_mgr.cfg_guide_mission_3Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_3_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_guide_uiname_3Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_3_cache, 2 /* KEY */, "ui_name");
        cachemap_mgr.guideRearIds_3Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_3_cache, 2 /* KEY */, "pre_id");
        cachemap_mgr.cfg_guide_mission_4Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_4_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_guide_uiname_4Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_4_cache, 2 /* KEY */, "ui_name");
        cachemap_mgr.guideRearIds_4Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_4_cache, 2 /* KEY */, "pre_id");
        cachemap_mgr.cfg_guide_mission_game_1Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_game_1_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_guide_uiname_game_1Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_game_1_cache, 2 /* KEY */, "ui_name");
        cachemap_mgr.guideRearIds_game_1Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_game_1_cache, 2 /* KEY */, "pre_id");
        cachemap_mgr.cfg_guide_mission_m2Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_m2_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_guide_uiname_m2Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_m2_cache, 2 /* KEY */, "ui_name");
        cachemap_mgr.guideRearIds_m2Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_m2_cache, 2 /* KEY */, "pre_id");
        cachemap_mgr.cfg_guide_mission_m3Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_m3_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_guide_uiname_m3Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_m3_cache, 2 /* KEY */, "ui_name");
        cachemap_mgr.guideRearIds_m3Cache = new CfgCacheMapBase(cache_mgr.cfg_guide_mission_m3_cache, 2 /* KEY */, "pre_id");
        cachemap_mgr.cfg_guide_reviewCache = new CfgCacheMapBase(cache_mgr.cfg_guide_review_cache, 2 /* KEY */, "platform_id");
        cachemap_mgr.cfg_guide_storyCache = new CfgCacheMapBase(cache_mgr.cfg_guide_story_cache, 2 /* KEY */, "platform_id");
        cachemap_mgr.cfg_hero_attr_addition_careerCache = new CfgCacheMapBase(cache_mgr.cfg_hero_attr_addition_cache, 4 /* GRP */, "career");
        cachemap_mgr.cfg_hero_attr_addition_nationCache = new CfgCacheMapBase(cache_mgr.cfg_hero_attr_addition_cache, 4 /* GRP */, "nation");
        cachemap_mgr.cfg_hero_attr_sourceCache = new CfgCacheMapBase(cache_mgr.cfg_hero_attr_source_cache, 2 /* KEY */, "type");
        cachemap_mgr.cfg_hero_bagCache = new CfgCacheMapBase(cache_mgr.cfg_hero_bag_cache, 1 /* ID */, "count");
        cachemap_mgr.cfg_hero_baseCache = new CfgCacheMapBase(cache_mgr.cfg_hero_base_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_hero_base_chipIdCache = new CfgCacheMapBase(cache_mgr.cfg_hero_base_cache, 2 /* KEY */, "hero_chip_id");
        cachemap_mgr.cfg_hero_base_completeIdCache = new CfgCacheMapBase(cache_mgr.cfg_hero_base_cache, 2 /* KEY */, "hero_complete_id");
        cachemap_mgr.cfg_hero_cheerCache = new CfgCacheMapBase(cache_mgr.cfg_hero_cheer_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_hero_cheerByNationCache = new CfgCacheMapBase(cache_mgr.cfg_hero_cheer_cache, 4 /* GRP */, "nation");
        cachemap_mgr.cfg_hero_cheer_bonusCache = new CfgCacheMapBase(cache_mgr.cfg_hero_cheer_bonus_cache, 1 /* ID */, "nation");
        cachemap_mgr.cfg_hero_cheer_levelCache = new CfgCacheMapBase(cache_mgr.cfg_hero_cheer_level_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_hero_cheer_unlockCache = new CfgCacheMapBase(cache_mgr.cfg_hero_cheer_unlock_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_hero_chip_starCache = new CfgCacheMapBase(cache_mgr.cfg_hero_chip_star_cache, 3 /* MKEY */, "source_star,target_star");
        cachemap_mgr.cfg_hero_come_missionCache = new CfgCacheMapBase(cache_mgr.cfg_hero_come_mission_cache, 1 /* ID */, "event_id");
        cachemap_mgr.cfg_hero_come_missionTypeCache = new CfgCacheMapBase(cache_mgr.cfg_hero_come_mission_cache, 5 /* MGRP */, "plan_id,type_id");
        cachemap_mgr.cfg_hero_convertCache = new CfgCacheMapBase(cache_mgr.cfg_hero_convert_cache, 3 /* MKEY */, "nation,star");
        cachemap_mgr.cfg_hero_convert_weightCache = new CfgCacheMapBase(cache_mgr.cfg_hero_convert_weight_cache, 4 /* GRP */, "hero_type_id");
        cachemap_mgr.cfg_hero_cost_planCache = new CfgCacheMapBase(cache_mgr.cfg_hero_cost_plan_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_hero_evolve_skillCache = new CfgCacheMapBase(cache_mgr.cfg_hero_evolve_skill_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_hero_handbook_descCache = new CfgCacheMapBase(cache_mgr.cfg_hero_handbook_desc_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_hero_levelCache = new CfgCacheMapBase(cache_mgr.cfg_hero_level_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_hero_level_limitCache = new CfgCacheMapBase(cache_mgr.cfg_hero_level_limit_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_hero_level_limit_typeCache = new CfgCacheMapBase(cache_mgr.cfg_hero_level_limit_cache, 3 /* MKEY */, "type,value");
        cachemap_mgr.cfg_hero_nationCache = new CfgCacheMapBase(cache_mgr.cfg_hero_nation_cache, 1 /* ID */, "nation");
        cachemap_mgr.cfg_hero_pass_missionCache = new CfgCacheMapBase(cache_mgr.cfg_hero_pass_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_hero_pass_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_hero_pass_reward_cache, 3 /* MKEY */, "level,sort_id");
        cachemap_mgr.cfg_hero_pass_rewardByTurnCache = new CfgCacheMapBase(cache_mgr.cfg_hero_pass_reward_cache, 4 /* GRP */, "level");
        cachemap_mgr.cfg_hero_recommend_preCache = new CfgCacheMapBase(cache_mgr.cfg_hero_recommend_pre_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_hero_recycleCache = new CfgCacheMapBase(cache_mgr.cfg_hero_recycle_cache, 1 /* ID */, "count");
        cachemap_mgr.cfg_hero_recycle_changeCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_hero_recycle_change_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_hero_recycle_change_star_stageCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_hero_recycle_change_star_stage_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_hero_recycleCaches = new CfgCacheMapBase(cache_mgr.cfg_hero_recycle_res_cache, 3 /* MKEY */, "type,value");
        cachemap_mgr.cfg_hero_recycle_special_switchCache = new CfgCacheMapBase(cache_mgr.cfg_hero_recycle_special_switch_cache, 2 /* KEY */, "type");
        cachemap_mgr.cfg_hero_resonate_dhyanaCache = new CfgCacheMapBase(cache_mgr.cfg_hero_resonate_dhyana_cache, 1 /* ID */, "add_level");
        cachemap_mgr.cfg_hero_resonate_duduCache = new CfgCacheMapBase(cache_mgr.cfg_hero_resonate_dudu_cache, 1 /* ID */, "slot_id");
        cachemap_mgr.cfg_hero_resonate_dudu_levelCache = new CfgCacheMapBase(cache_mgr.cfg_hero_resonate_dudu_level_cache, 1 /* ID */, "star");
        cachemap_mgr.cfg_hero_resonate_fiveCache = new CfgCacheMapBase(cache_mgr.cfg_hero_resonate_five_cache, 1 /* ID */, "slot_id");
        cachemap_mgr.cfg_hero_skinCache = new CfgCacheMapBase(cache_mgr.cfg_hero_skin_cache, 1 /* ID */, "skin_id");
        cachemap_mgr.cfg_hero_skin_listCache = new CfgCacheMapBase(cache_mgr.cfg_hero_skin_cache, 4 /* GRP */, "hero_type_id");
        cachemap_mgr.cfg_hero_skinByIconCaChe = new CfgCacheMapBase(cache_mgr.cfg_hero_skin_cache, 2 /* KEY */, "item_id");
        cachemap_mgr.cfg_hero_skin_levelCache = new CfgCacheMapBase(cache_mgr.cfg_hero_skin_level_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_hero_skin_level_listCache = new CfgCacheMapBase(cache_mgr.cfg_hero_skin_level_cache, 4 /* GRP */, "skin_id");
        cachemap_mgr.cfg_hero_skin_attriCache = new CfgCacheMapBase(cache_mgr.cfg_hero_skin_level_cache, 3 /* MKEY */, "skin_id,level");
        cachemap_mgr.cfg_hero_stageCache = new CfgCacheMapBase(cache_mgr.cfg_hero_stage_cache, 1 /* ID */, "stage");
        cachemap_mgr.cfg_hero_stage_limitCache = new CfgCacheMapBase(cache_mgr.cfg_hero_stage_limit_cache, 1 /* ID */, "star");
        cachemap_mgr.cfg_hero_starCache = new CfgCacheMapBase(cache_mgr.cfg_hero_star_cache, 4 /* GRP */, "type_id");
        cachemap_mgr.cfg_hero_starCache2 = new CfgCacheMapBase(cache_mgr.cfg_hero_star_cache, 3 /* MKEY */, "type_id,star");
        cachemap_mgr.cfg_hero_star_attrCache = new CfgCacheMapBase(cache_mgr.cfg_hero_star_attr_cache, 1 /* ID */, "star");
        cachemap_mgr.cfg_hero_star_limitCache = new CfgCacheMapBase(cache_mgr.cfg_hero_star_limit_cache, 3 /* MKEY */, "star,star_stage");
        cachemap_mgr.cfg_hero_star_limit_listCache = new CfgCacheMapBase(cache_mgr.cfg_hero_star_limit_cache, 4 /* GRP */, "star");
        cachemap_mgr.cfg_hero_star_stageCache = new CfgCacheMapBase(cache_mgr.cfg_hero_star_stage_cache, 5 /* MGRP */, "type_id,star");
        cachemap_mgr.cfg_hero_star_stageCache2 = new CfgCacheMapBase(cache_mgr.cfg_hero_star_stage_cache, 3 /* MKEY */, "type_id,star,star_stage");
        cachemap_mgr.cfg_hero_star_stage_attrCache = new CfgCacheMapBase(cache_mgr.cfg_hero_star_stage_attr_cache, 3 /* MKEY */, "star,star_stage");
        cachemap_mgr.cfg_hero_star_stage_attr_listCache = new CfgCacheMapBase(cache_mgr.cfg_hero_star_stage_attr_cache, 4 /* GRP */, "star");
        cachemap_mgr.cfg_hero_strengthenCache = new CfgCacheMapBase(cache_mgr.cfg_hero_strengthen_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_hero_upgrade_tipsCache = new CfgCacheMapBase(cache_mgr.cfg_hero_upgrade_tips_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_hero_zhouyinCache = new CfgCacheMapBase(cache_mgr.cfg_hero_zhouyin_cache, 2 /* KEY */, "type_id");
        cachemap_mgr.cfg_hunt_buyCache = new CfgCacheMapBase(cache_mgr.cfg_hunt_buy_cache, 3 /* MKEY */, "big_type,num_type");
        cachemap_mgr.cfg_hunt_costCache = new CfgCacheMapBase(cache_mgr.cfg_hunt_cost_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_hunt_descCache = new CfgCacheMapBase(cache_mgr.cfg_hunt_desc_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_hunt_giftCache = new CfgCacheMapBase(cache_mgr.cfg_hunt_gift_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_hunt_rewards_showCache = new CfgCacheMapBase(cache_mgr.cfg_hunt_rewards_show_cache, 1 /* ID */, "big_type");
        cachemap_mgr.cfg_huoqutujingCache = new CfgCacheMapBase(cache_mgr.cfg_huoqutujing_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_hzzd_achievementCache = new CfgCacheMapBase(cache_mgr.cfg_hzzd_achievement_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_hzzd_eventCache = new CfgCacheMapBase(cache_mgr.cfg_hzzd_event_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_hzzd_kills_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_hzzd_kills_reward_cache, 1 /* ID */, "kills_num");
        cachemap_mgr.cfg_hzzd_miscCache = new CfgCacheMapBase(cache_mgr.cfg_hzzd_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_i18n_langCache = new CfgCacheMapBase(cache_mgr.cfg_i18n_lang_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_i18n_tsCache = new CfgCacheMapBase(cache_mgr.cfg_i18n_ts_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_i18n_uiCache = new CfgCacheMapBase(cache_mgr.cfg_i18n_ui_cache, 2 /* KEY */, "lang_tag");
        cachemap_mgr.cfg_ingenious_planCache = new CfgCacheMapBase(cache_mgr.cfg_ingenious_plan_cache, 3 /* MKEY */, "id,star");
        cachemap_mgr.cfg_ingenious_planTypeIdCache = new CfgCacheMapBase(cache_mgr.cfg_ingenious_plan_cache, 2 /* KEY */, "type_id");
        cachemap_mgr.cfg_ingenious_planGroup = new CfgCacheMapBase(cache_mgr.cfg_ingenious_plan_cache, 4 /* GRP */, "star");
        cachemap_mgr.cfg_ingenious_planIdGroup = new CfgCacheMapBase(cache_mgr.cfg_ingenious_plan_cache, 4 /* GRP */, "id");
        cachemap_mgr.cfg_ingenious_plan_composeCache = new CfgCacheMapBase(cache_mgr.cfg_ingenious_plan_compose_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_ingenious_plan_convertCache = new CfgCacheMapBase(cache_mgr.cfg_ingenious_plan_convert_cache, 2 /* KEY */, "type_id");
        cachemap_mgr.cfg_ingenious_plan_levelCache = new CfgCacheMapBase(cache_mgr.cfg_ingenious_plan_level_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_ingenious_plan_stageCache = new CfgCacheMapBase(cache_mgr.cfg_ingenious_plan_stage_cache, 2 /* KEY */, "stage");
        cachemap_mgr.cfg_ingenious_plan_starCache = new CfgCacheMapBase(cache_mgr.cfg_ingenious_plan_star_cache, 2 /* KEY */, "star");
        cachemap_mgr.cfg_ip_setCache = new CfgCacheMapBase(cache_mgr.cfg_ip_set_cache, 2 /* KEY */, "name");
        cachemap_mgr.cfg_itemCache = new CfgCacheMapBase(cache_mgr.cfg_item_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_itemKindCache = new CfgCacheMapBase(cache_mgr.cfg_item_cache, 4 /* GRP */, "kind");
        cachemap_mgr.cfg_itemAttrCache = new CfgCacheMapBase(cache_mgr.cfg_item_cache, 2 /* KEY */, "trans_attr");
        cachemap_mgr.cfg_item_composeCache = new CfgCacheMapBase(cache_mgr.cfg_item_compose_cache, 1 /* ID */, "item_id");
        cachemap_mgr.cfg_item_compose_target_idCache = new CfgCacheMapBase(cache_mgr.cfg_item_compose_cache, 2 /* KEY */, "target_item_id");
        cachemap_mgr.cfg_item_time_clientCache = new CfgCacheMapBase(cache_mgr.cfg_item_time_client_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_large_peak_agentCache = new CfgCacheMapBase(cache_mgr.cfg_large_peak_agent_cache, 3 /* MKEY */, "agent,season");
        cachemap_mgr.cfg_large_peak_battle_table_allCache = new CfgCacheMapBase(cache_mgr.cfg_large_peak_battle_table_cache, 3 /* MKEY */, "model,round");
        cachemap_mgr.cfg_large_peak_battle_tableCache = new CfgCacheMapBase(cache_mgr.cfg_large_peak_battle_table_cache, 3 /* MKEY */, "key_group,key_round");
        cachemap_mgr.cfg_large_peak_battle_table_groupCache = new CfgCacheMapBase(cache_mgr.cfg_large_peak_battle_table_cache, 4 /* GRP */, "big_group");
        cachemap_mgr.cfg_large_peak_battle_table_key_groupCache = new CfgCacheMapBase(cache_mgr.cfg_large_peak_battle_table_cache, 4 /* GRP */, "key_group");
        cachemap_mgr.cfg_large_peak_miscCache = new CfgCacheMapBase(cache_mgr.cfg_large_peak_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_large_peak_rankCache = new CfgCacheMapBase(cache_mgr.cfg_large_peak_rank_cache, 4 /* GRP */, "season");
        cachemap_mgr.cfg_large_peak_seasonCache = new CfgCacheMapBase(cache_mgr.cfg_large_peak_season_cache, 1 /* ID */, "season");
        cachemap_mgr.cfg_large_peak_timeCache = new CfgCacheMapBase(cache_mgr.cfg_large_peak_time_cache, 3 /* MKEY */, "model,round");
        cachemap_mgr.cfg_large_peak_time_listCache = new CfgCacheMapBase(cache_mgr.cfg_large_peak_time_cache, 4 /* GRP */, "day");
        cachemap_mgr.cfg_lazy_loadCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_lazy_load_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_lcqs_acc_star_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_lcqs_acc_star_reward_cache, 4 /* GRP */, "chapter");
        cachemap_mgr.cfg_lcqs_chapter_openCache = new CfgCacheMapBase(cache_mgr.cfg_lcqs_chapter_open_cache, 1 /* ID */, "chapter");
        cachemap_mgr.cfg_lcqs_floor_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_lcqs_floor_reward_cache, 4 /* GRP */, "chapter");
        cachemap_mgr.cfg_lcqs_missionCache = new CfgCacheMapBase(cache_mgr.cfg_lcqs_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_levelCache = new CfgCacheMapBase(cache_mgr.cfg_level_cache, 2 /* KEY */, "level");
        cachemap_mgr.cfg_level_giftCache = new CfgCacheMapBase(cache_mgr.cfg_level_gift_cache, 1 /* ID */, "gift_id");
        cachemap_mgr.cfg_limit_hero_skin_chip_exchangeCaches = new CfgCacheMapBase(cache_mgr.cfg_limit_hero_skin_chip_exchange_cache, 3 /* MKEY */, "type,value");
        cachemap_mgr.cfg_lineup_buffCache = new CfgCacheMapBase(cache_mgr.cfg_lineup_buff_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_lineup_Buff_iconCache = new CfgCacheMapBase(cache_mgr.cfg_lineup_buff_icon_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_lineup_career_ruleCache = new CfgCacheMapBase(cache_mgr.cfg_lineup_career_rule_cache, 1 /* ID */, "career");
        cachemap_mgr.cfg_lineup_numCache = new CfgCacheMapBase(cache_mgr.cfg_lineup_num_cache, 1 /* ID */, "match_type");
        cachemap_mgr.cfg_lineup_recommendCache = new CfgCacheMapBase(cache_mgr.cfg_lineup_recommend_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_lineup_styleCache = new CfgCacheMapBase(cache_mgr.cfg_lineup_style_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_load_tipsCache = new CfgCacheMapBase(cache_mgr.cfg_load_tips_cache, 1 /* ID */, "index");
        cachemap_mgr.cfg_login_activityCache = new CfgCacheMapBase(cache_mgr.cfg_login_activity_cache, 3 /* MKEY */, "round,day");
        cachemap_mgr.cfg_login_activity_roundCache = new CfgCacheMapBase(cache_mgr.cfg_login_activity_round_cache, 1 /* ID */, "round");
        cachemap_mgr.cfg_lord_activationCache = new CfgCacheMapBase(cache_mgr.cfg_lord_activation_cache, 1 /* ID */, "lord_id");
        cachemap_mgr.cfg_lord_baseCache = new CfgCacheMapBase(cache_mgr.cfg_lord_base_cache, 1 /* ID */, "lord_id");
        cachemap_mgr.cfg_lord_campCache = new CfgCacheMapBase(cache_mgr.cfg_lord_camp_cache, 1 /* ID */, "lord_id");
        cachemap_mgr.cfg_lord_campByGradeCache = new CfgCacheMapBase(cache_mgr.cfg_lord_camp_cache, 4 /* GRP */, "lord_grade");
        cachemap_mgr.cfg_lord_campByNeedCardStageCache = new CfgCacheMapBase(cache_mgr.cfg_lord_camp_cache, 4 /* GRP */, "need_card_stage");
        cachemap_mgr.cfg_lord_campByCamp = new CfgCacheMapBase(cache_mgr.cfg_lord_camp_cache, 4 /* GRP */, "camp");
        cachemap_mgr.cfg_lord_campBySkill_id = new CfgCacheMapBase(cache_mgr.cfg_lord_camp_cache, 2 /* KEY */, "skill_1");
        cachemap_mgr.cfg_lord_campByStageAndCamp = new CfgCacheMapBase(cache_mgr.cfg_lord_camp_cache, 3 /* MKEY */, "need_card_stage,camp");
        cachemap_mgr.cfg_lord_exchangeCache = new CfgCacheMapBase(cache_mgr.cfg_lord_exchange_cache, 1 /* ID */, "lord_id");
        cachemap_mgr.cfg_lord_skillCache = new CfgCacheMapBase(cache_mgr.cfg_lord_skill_cache, 4 /* GRP */, "skill_id");
        cachemap_mgr.cfg_lord_skillByType = new CfgCacheMapBase(cache_mgr.cfg_lord_skill_cache, 4 /* GRP */, "skill_type");
        cachemap_mgr.cfg_lord_skill_enhanceCache = new CfgCacheMapBase(cache_mgr.cfg_lord_skill_enhance_cache, 1 /* ID */, "skill_id");
        cachemap_mgr.cfg_lord_starCache = new CfgCacheMapBase(cache_mgr.cfg_lord_star_cache, 1 /* ID */, "lord_id");
        cachemap_mgr.cfg_lord_suit_composeCache = new CfgCacheMapBase(cache_mgr.cfg_lord_suit_compose_cache, 1 /* ID */, "select_id");
        cachemap_mgr.cfg_lord_suit_selectCache = new CfgCacheMapBase(cache_mgr.cfg_lord_suit_select_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_lord_treasureCache = new CfgCacheMapBase(cache_mgr.cfg_lord_treasure_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_lord_treasure_entryCache = new CfgCacheMapBase(cache_mgr.cfg_lord_treasure_entry_cache, 3 /* MKEY */, "entry_id,entry_level");
        cachemap_mgr.cfg_lord_treasure_entryByEntry_idCache = new CfgCacheMapBase(cache_mgr.cfg_lord_treasure_entry_cache, 4 /* GRP */, "entry_id");
        cachemap_mgr.cfg_lord_treasure_forgeCache = new CfgCacheMapBase(cache_mgr.cfg_lord_treasure_forge_cache, 3 /* MKEY */, "entry_id,forge_time");
        cachemap_mgr.cfg_lord_treasure_forgeByEntry_idCache = new CfgCacheMapBase(cache_mgr.cfg_lord_treasure_forge_cache, 4 /* GRP */, "entry_id");
        cachemap_mgr.cfg_lord_treasure_levelCache = new CfgCacheMapBase(cache_mgr.cfg_lord_treasure_level_cache, 3 /* MKEY */, "type_id,level");
        cachemap_mgr.cfg_lord_treasure_levelBytreasure_idCache = new CfgCacheMapBase(cache_mgr.cfg_lord_treasure_level_cache, 4 /* GRP */, "type_id");
        cachemap_mgr.cfg_lottery_day_limitCache = new CfgCacheMapBase(cache_mgr.cfg_lottery_day_limit_cache, 2 /* KEY */, "min_day");
        cachemap_mgr.cfg_lottery_extCache = new CfgCacheMapBase(cache_mgr.cfg_lottery_ext_cache, 2 /* KEY */, "lottery_type");
        cachemap_mgr.cfg_lottery_nationCache = new CfgCacheMapBase(cache_mgr.cfg_lottery_nation_cache, 4 /* GRP */, "nation");
        cachemap_mgr.cfg_lottery_nation_timesCache = new CfgCacheMapBase(cache_mgr.cfg_lottery_nation_times_cache, 1 /* ID */, "nation");
        cachemap_mgr.cfg_lottery_scoreCache = new CfgCacheMapBase(cache_mgr.cfg_lottery_score_cache, 2 /* KEY */, "score");
        cachemap_mgr.cfg_lottery_showCache = new CfgCacheMapBase(cache_mgr.cfg_lottery_show_cache, 4 /* GRP */, "lottery_type");
        cachemap_mgr.cfg_lottery_timesCache = new CfgCacheMapBase(cache_mgr.cfg_lottery_times_cache, 2 /* KEY */, "group_id");
        cachemap_mgr.cfg_lottery_weightCache = new CfgCacheMapBase(cache_mgr.cfg_lottery_weight_cache, 4 /* GRP */, "group_id");
        cachemap_mgr.cfg_main_battleCache = new CfgCacheMapBase(cache_mgr.cfg_main_battle_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_main_battle_groupCache = new CfgCacheMapBase(cache_mgr.cfg_main_battle_cache, 4 /* GRP */, "group");
        cachemap_mgr.cfg_main_battle_boxCache = new CfgCacheMapBase(cache_mgr.cfg_main_battle_box_cache, 1 /* ID */, "box");
        cachemap_mgr.cfg_main_battle_boxByItemIdCache = new CfgCacheMapBase(cache_mgr.cfg_main_battle_box_cache, 2 /* KEY */, "item_id");
        cachemap_mgr.cfg_main_battle_box_levelCache = new CfgCacheMapBase(cache_mgr.cfg_main_battle_box_level_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_main_battle_box_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_main_battle_box_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_main_battle_box_rewardMapCache = new CfgCacheMapBase(cache_mgr.cfg_main_battle_box_reward_cache, 3 /* MKEY */, "box,item_id");
        cachemap_mgr.cfg_main_battle_box_rewardGrpCache = new CfgCacheMapBase(cache_mgr.cfg_main_battle_box_reward_cache, 4 /* GRP */, "box");
        cachemap_mgr.cfg_main_battle_box_tequanCache = new CfgCacheMapBase(cache_mgr.cfg_main_battle_box_tequan_cache, 2 /* KEY */, "goods_id");
        cachemap_mgr.cfg_main_battle_fetchCache = new CfgCacheMapBase(cache_mgr.cfg_main_battle_fetch_cache, 1 /* ID */, "pass_id");
        cachemap_mgr.cfg_main_battle_hangingCache = new CfgCacheMapBase(cache_mgr.cfg_main_battle_hanging_cache, 1 /* ID */, "min_buy_times");
        cachemap_mgr.cfg_main_battle_missionCache = new CfgCacheMapBase(cache_mgr.cfg_main_battle_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_mapCache = new CfgCacheMapBase(cache_mgr.cfg_map_cache, 2 /* KEY */, "map_id");
        cachemap_mgr.cfg_map_itemCache = new CfgCacheMapBase(cache_mgr.cfg_map_item_cache, 2 /* KEY */, "item_id");
        cachemap_mgr.cfg_map_itemGrpCache = new CfgCacheMapBase(cache_mgr.cfg_map_item_cache, 4 /* GRP */, "map_id");
        cachemap_mgr.cfg_map_item_typeCache = new CfgCacheMapBase(cache_mgr.cfg_map_item_type_cache, 2 /* KEY */, "item_type");
        cachemap_mgr.cfg_map_typeCache = new CfgCacheMapBase(cache_mgr.cfg_map_type_cache, 2 /* KEY */, "map_type");
        cachemap_mgr.cfg_master_cardCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_master_cardByGroupCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_cache, 4 /* GRP */, "slot");
        cachemap_mgr.cfg_master_card_attrCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_attr_cache, 1 /* ID */, "attr_key");
        cachemap_mgr.cfg_master_card_colorCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_color_cache, 1 /* ID */, "color");
        cachemap_mgr.cfg_master_card_drum_levelCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_drum_level_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_master_card_first_payCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_first_pay_cache, 1 /* ID */, "gift_id");
        cachemap_mgr.cfg_master_card_help_giftCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_help_gift_cache, 1 /* ID */, "gift_id");
        cachemap_mgr.cfg_master_card_miscCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_master_card_missionCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_master_card_mission_guide_idCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_mission_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_master_card_officialCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_official_cache, 1 /* ID */, "stage");
        cachemap_mgr.cfg_master_card_official_positionCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_official_position_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_master_card_reshapeCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_reshape_cache, 3 /* MKEY */, "slot,color");
        cachemap_mgr.cfg_master_card_slotCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_slot_cache, 1 /* ID */, "slot");
        cachemap_mgr.cfg_master_card_stageCache = new CfgCacheMapBase(cache_mgr.cfg_master_card_stage_cache, 1 /* ID */, "stage");
        cachemap_mgr.cfg_master_talent_scienceCache = new CfgCacheMapBase(cache_mgr.cfg_master_talent_science_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_master_talent_science_iconCache = new CfgCacheMapBase(cache_mgr.cfg_master_talent_science_icon_cache, 2 /* KEY */, "type");
        cachemap_mgr.cfg_match_typeCache = new CfgCacheMapBase(cache_mgr.cfg_match_type_cache, 1 /* ID */, "match_type");
        cachemap_mgr.cfg_match_type_teamCache = new CfgCacheMapBase(cache_mgr.cfg_match_type_team_cache, 3 /* MKEY */, "plan_id,team_id");
        cachemap_mgr.cfg_match_type_team_listCache = new CfgCacheMapBase(cache_mgr.cfg_match_type_team_cache, 4 /* GRP */, "plan_id");
        cachemap_mgr.cfg_mazeCache = new CfgCacheMapBase(cache_mgr.cfg_maze_cache, 3 /* MKEY */, "floor,pos");
        cachemap_mgr.cfg_maze_diff_rewardsCache = new CfgCacheMapBase(cache_mgr.cfg_maze_diff_rewards_cache, 3 /* MKEY */, "rewards_id,floor,diff_lv");
        cachemap_mgr.cfg_maze_mission_spoilsCache = new CfgCacheMapBase(cache_mgr.cfg_maze_mission_spoils_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_maze_monsterCache = new CfgCacheMapBase(cache_mgr.cfg_maze_monster_cache, 4 /* GRP */, "rewards_id");
        cachemap_mgr.cfg_maze_resetCache = new CfgCacheMapBase(cache_mgr.cfg_maze_reset_cache, 1 /* ID */, "count");
        cachemap_mgr.cfg_maze_reviveCache = new CfgCacheMapBase(cache_mgr.cfg_maze_revive_cache, 1 /* ID */, "count");
        cachemap_mgr.cfg_maze_shopCache = new CfgCacheMapBase(cache_mgr.cfg_maze_shop_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_maze_themeCache = new CfgCacheMapBase(cache_mgr.cfg_maze_theme_cache, 1 /* ID */, "rewards_id");
        cachemap_mgr.cfg_medalCache = new CfgCacheMapBase(cache_mgr.cfg_medal_cache, 1 /* ID */, "medal_id");
        cachemap_mgr.cfg_medalBytype = new CfgCacheMapBase(cache_mgr.cfg_medal_cache, 4 /* GRP */, "medal_type");
        cachemap_mgr.cfg_medalByidex = new CfgCacheMapBase(cache_mgr.cfg_medal_cache, 4 /* GRP */, "idex");
        cachemap_mgr.cfg_medalBySpmedal = new CfgCacheMapBase(cache_mgr.cfg_medal_cache, 4 /* GRP */, "is_sp_medal");
        cachemap_mgr.cfg_medalByclassification = new CfgCacheMapBase(cache_mgr.cfg_medal_cache, 4 /* GRP */, "classification");
        cachemap_mgr.cfg_microterminal_openCache = new CfgCacheMapBase(cache_mgr.cfg_microterminal_open_cache, 2 /* KEY */, "open_platform");
        cachemap_mgr.cfg_microterminal_signCache = new CfgCacheMapBase(cache_mgr.cfg_microterminal_sign_cache, 4 /* GRP */, "last_day");
        cachemap_mgr.cfg_misc_configCache = new CfgCacheMapBase(cache_mgr.cfg_misc_config_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_mission_shopCache = new CfgCacheMapBase(cache_mgr.cfg_mission_shop_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_mission_shop_clientCache = new CfgCacheMapBase(cache_mgr.cfg_mission_shop_client_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_mock_pvp_databaseCache = new CfgCacheMapBase(cache_mgr.cfg_mock_pvp_database_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_mock_pvp_hero_baseCache = new CfgCacheMapBase(cache_mgr.cfg_mock_pvp_hero_base_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_mock_pvp_hero_baseByNationCache = new CfgCacheMapBase(cache_mgr.cfg_mock_pvp_hero_base_cache, 4 /* GRP */, "nation");
        cachemap_mgr.cfg_mock_pvp_hero_baseByTypeIdCache = new CfgCacheMapBase(cache_mgr.cfg_mock_pvp_hero_base_cache, 3 /* MKEY */, "type_id,nation");
        cachemap_mgr.cfg_mock_pvp_limitCache = new CfgCacheMapBase(cache_mgr.cfg_mock_pvp_limit_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_mock_pvp_miscCache = new CfgCacheMapBase(cache_mgr.cfg_mock_pvp_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_mock_pvp_missionCache = new CfgCacheMapBase(cache_mgr.cfg_mock_pvp_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_modular_activity_blessCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_bless_cache, 1 /* ID */, "plan_id");
        cachemap_mgr.cfg_modular_activity_brickCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_brick_cache, 3 /* MKEY */, "plan_id,round");
        cachemap_mgr.cfg_modular_activity_carnival_linkCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_carnival_link_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_modular_activity_client_settingCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_client_setting_cache, 2 /* KEY */, "client_key");
        cachemap_mgr.cfg_modular_activity_client_settingGroupCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_client_setting_cache, 5 /* MGRP */, "sub_type,plan_id");
        cachemap_mgr.cfg_modular_activity_compose_listCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_compose_list_cache, 4 /* GRP */, "act_key");
        cachemap_mgr.cfg_modular_activity_compose_listClientKeyCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_compose_list_cache, 4 /* GRP */, "client_key");
        cachemap_mgr.cfg_modular_activity_customized_giftCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_customized_gift_cache, 1 /* ID */, "item_id");
        cachemap_mgr.cfg_modular_activity_customized_giftByplayidCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_customized_gift_cache, 4 /* GRP */, "plan_id");
        cachemap_mgr.cfg_modular_activity_diceCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_dice_cache, 5 /* MGRP */, "plan_id,floor");
        cachemap_mgr.cfg_modular_activity_dice_bossCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_dice_boss_cache, 3 /* MKEY */, "plan_id,floor");
        cachemap_mgr.cfg_modular_activity_dice_bossGroupCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_dice_boss_cache, 4 /* GRP */, "plan_id");
        cachemap_mgr.cfg_modular_activity_dice_client_diffCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_dice_client_diff_cache, 1 /* ID */, "plan_id");
        cachemap_mgr.cfg_modular_activity_dice_miscCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_dice_misc_cache, 3 /* MKEY */, "plan_id,dice_type");
        cachemap_mgr.cfg_modular_activity_dropCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_drop_cache, 2 /* KEY */, "plan_id");
        cachemap_mgr.cfg_modular_activity_drop_showCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_drop_show_cache, 3 /* MKEY */, "type,plan_id");
        cachemap_mgr.cfg_modular_activity_exchangeCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_exchange_cache, 3 /* MKEY */, "plan_id,id");
        cachemap_mgr.cfg_modular_activity_exchangeGrpCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_exchange_cache, 4 /* GRP */, "plan_id");
        cachemap_mgr.cfg_modular_activity_festival_wishCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_festival_wish_cache, 5 /* MGRP */, "plan_id,round");
        cachemap_mgr.cfg_modular_activity_festival_wish_floorCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_festival_wish_cache, 5 /* MGRP */, "plan_id,round,floor");
        cachemap_mgr.cfg_modular_activity_festival_wish_chooseCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_festival_wish_choose_cache, 5 /* MGRP */, "plan_id,round");
        cachemap_mgr.cfg_modular_activity_festival_wish_chooseListCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_festival_wish_choose_cache, 4 /* GRP */, "plan_id");
        cachemap_mgr.cfg_modular_activity_festival_wish_costCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_festival_wish_cost_cache, 3 /* MKEY */, "plan_id,floor");
        cachemap_mgr.cfg_modular_activity_free_switchCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_free_switch_cache, 2 /* KEY */, "plan_id");
        cachemap_mgr.cfg_modular_activity_general_pass_vipCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_general_pass_vip_cache, 3 /* MKEY */, "pass_type,turn");
        cachemap_mgr.cfg_modular_activity_general_pass_vipByPlanidCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_general_pass_vip_cache, 5 /* MGRP */, "pass_type,turn");
        cachemap_mgr.cfg_modular_activity_hero_challengeCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_hero_challenge_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_modular_activity_hero_challengeByPlanidCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_hero_challenge_cache, 4 /* GRP */, "plan_id");
        cachemap_mgr.cfg_modular_activity_holiday_welfare_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_holiday_welfare_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_modular_activity_hunt_costCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_hunt_cost_cache, 3 /* MKEY */, "plan_id,num_type");
        cachemap_mgr.cfg_modular_activity_hunt_cost_listCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_hunt_cost_cache, 4 /* GRP */, "plan_id");
        cachemap_mgr.cfg_modular_activity_hunt_descCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_hunt_desc_cache, 5 /* MGRP */, "plan_id,type");
        cachemap_mgr.cfg_modular_activity_hunt_miscCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_hunt_misc_cache, 1 /* ID */, "plan_id");
        cachemap_mgr.cfg_modular_activity_huoqutujingCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_huoqutujing_cache, 4 /* GRP */, "client_key");
        cachemap_mgr.cfg_modular_activity_iconCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_icon_cache, 2 /* KEY */, "act_key");
        cachemap_mgr.cfg_modular_activity_loginCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_login_cache, 3 /* MKEY */, "plan_id,day");
        cachemap_mgr.cfg_modular_activity_lottery_targetCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_lottery_target_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_modular_activity_lottery_timesCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_lottery_times_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_modular_activity_lucky_bagCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_lucky_bag_cache, 3 /* MKEY */, "plan_id,id");
        cachemap_mgr.cfg_modular_activity_lucky_bagByPlan_id = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_lucky_bag_cache, 4 /* GRP */, "plan_id");
        cachemap_mgr.cfg_modular_activity_missionCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_mission_cache, 3 /* MKEY */, "sub_type,plan_id,id");
        cachemap_mgr.cfg_modular_activity_missionListCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_mission_cache, 5 /* MGRP */, "sub_type,plan_id");
        cachemap_mgr.cfg_modular_activity_openCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_open_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_modular_activity_open_previewCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_open_preview_cache, 2 /* KEY */, "act_key");
        cachemap_mgr.cfg_modular_activity_pay_welfareCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_pay_welfare_cache, 4 /* GRP */, "plan_id");
        cachemap_mgr.cfg_modular_activity_payment_shop_itemCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_payment_shop_item_cache, 3 /* MKEY */, "item_id,sub_type,plan_id");
        cachemap_mgr.cfg_modular_activity_payment_shop_item_showCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_payment_shop_item_show_cache, 3 /* MKEY */, "item_id,sub_type,plan_id");
        cachemap_mgr.cfg_modular_activity_previewCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_preview_cache, 4 /* GRP */, "plan_id");
        cachemap_mgr.cfg_modular_activity_preview_rewardsCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_preview_rewards_cache, 2 /* KEY */, "client_key");
        cachemap_mgr.cfg_modular_activity_rankCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_rank_cache, 1 /* ID */, "plan_id");
        cachemap_mgr.cfg_modular_activity_rank_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_rank_reward_cache, 4 /* GRP */, "rank_key");
        cachemap_mgr.cfg_modular_activity_round_missionCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_round_mission_cache, 4 /* GRP */, "plan_id");
        cachemap_mgr.cfg_modular_activity_round_missionAllCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_round_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_modular_activity_round_mission_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_round_mission_reward_cache, 4 /* GRP */, "plan_id");
        cachemap_mgr.cfg_modular_activity_shop_clientCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_shop_client_cache, 3 /* MKEY */, "sub_type,plan_id");
        cachemap_mgr.cfg_modular_activity_signCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_sign_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_modular_activity_signbyplanIdCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_sign_cache, 5 /* MGRP */, "plan_id,repeat_x");
        cachemap_mgr.cfg_modular_activity_six_blessCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_six_bless_cache, 2 /* KEY */, "grade");
        cachemap_mgr.cfg_modular_activity_star_plan_heroCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_star_plan_hero_cache, 3 /* MKEY */, "sub_type,plan_id");
        cachemap_mgr.cfg_modular_activity_star_plan_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_star_plan_reward_cache, 3 /* MKEY */, "sub_type,plan_id,type_id,need_star");
        cachemap_mgr.cfg_modular_activity_star_plan_rewardByplan_idCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_star_plan_reward_cache, 5 /* MGRP */, "sub_type,plan_id,type_id");
        cachemap_mgr.cfg_modular_activity_star_plan_rewardByplan_idAndMain_pushCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_star_plan_reward_cache, 5 /* MGRP */, "sub_type,plan_id,main_push");
        cachemap_mgr.cfg_modular_activity_storyCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_story_cache, 1 /* ID */, "plan_id");
        cachemap_mgr.cfg_modular_activity_story_chapterCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_story_chapter_cache, 4 /* GRP */, "plan_id");
        cachemap_mgr.cfg_modular_activity_story_chapter_mapCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_story_chapter_map_cache, 3 /* MKEY */, "plan_id,start_id,end_id");
        cachemap_mgr.cfg_modular_activity_story_chapter_map_listCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_story_chapter_map_cache, 4 /* GRP */, "plan_id");
        cachemap_mgr.cfg_modular_activity_story_dialogueCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_story_dialogue_cache, 5 /* MGRP */, "plan_id,chapter_id");
        cachemap_mgr.cfg_modular_activity_sub_typeCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_sub_type_cache, 1 /* ID */, "sub_type");
        cachemap_mgr.cfg_modular_activity_targetCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_target_cache, 3 /* MKEY */, "plan_id,round,mission_id");
        cachemap_mgr.cfg_modular_activity_time_itemCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_time_item_cache, 2 /* KEY */, "client_key");
        cachemap_mgr.cfg_modular_activity_wallCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_wall_cache, 3 /* MKEY */, "plan_id,grid_id");
        cachemap_mgr.cfg_modular_activity_war_log_acc_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_war_log_acc_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_modular_activity_war_log_missionCache = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_war_log_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_modular_activity_weekly_card_rewardCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_modular_activity_weekly_card_reward_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_moneyCache = new CfgCacheMapBase(cache_mgr.cfg_money_cache, 2 /* KEY */, "platform");
        cachemap_mgr.cfg_monsterCache = new CfgCacheMapBase(cache_mgr.cfg_monster_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_monster_groupCache = new CfgCacheMapBase(cache_mgr.cfg_monster_group_cache, 1 /* ID */, "group_id");
        cachemap_mgr.cfg_monster_skill_tierCache = new CfgCacheMapBase(cache_mgr.cfg_monster_skill_tier_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_monster_tipsCache = new CfgCacheMapBase(cache_mgr.cfg_monster_tips_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_month_fundCache = new CfgCacheMapBase(cache_mgr.cfg_month_fund_cache, 5 /* MGRP */, "fund_type,turn");
        cachemap_mgr.cfg_month_fundCache2 = new CfgCacheMapBase(cache_mgr.cfg_month_fund_cache, 4 /* GRP */, "fund_type");
        cachemap_mgr.cfg_month_fund_typeCache = new CfgCacheMapBase(cache_mgr.cfg_month_fund_type_cache, 1 /* ID */, "fund_type");
        cachemap_mgr.cfg_musicCache = new CfgCacheMapBase(cache_mgr.cfg_music_cache, 1 /* ID */, "music_id");
        cachemap_mgr.cfg_musicUICache = new CfgCacheMapBase(cache_mgr.cfg_music_cache, 4 /* GRP */, "ui_name");
        cachemap_mgr.cfg_nation_tower_lineupCache = new CfgCacheMapBase(cache_mgr.cfg_nation_tower_lineup_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_nation_tower_openCache = new CfgCacheMapBase(cache_mgr.cfg_nation_tower_open_cache, 1 /* ID */, "type");
        cachemap_mgr.cfg_noob_payCache = new CfgCacheMapBase(cache_mgr.cfg_noob_pay_cache, 1 /* ID */, "gift_id");
        cachemap_mgr.cfg_online_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_online_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_pass_beheadCache = new CfgCacheMapBase(cache_mgr.cfg_pass_behead_cache, 1 /* ID */, "type");
        cachemap_mgr.cfg_pass_behead_boxCache = new CfgCacheMapBase(cache_mgr.cfg_pass_behead_box_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_pass_behead_guanqiaCache = new CfgCacheMapBase(cache_mgr.cfg_pass_behead_guanqia_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_pass_behead_reviveCache = new CfgCacheMapBase(cache_mgr.cfg_pass_behead_revive_cache, 1 /* ID */, "count");
        cachemap_mgr.cfg_pass_check_missionCache = new CfgCacheMapBase(cache_mgr.cfg_pass_check_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_pass_check_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_pass_check_reward_cache, 3 /* MKEY */, "turn,level,sort_id");
        cachemap_mgr.cfg_pass_check_rewardByTurnCache = new CfgCacheMapBase(cache_mgr.cfg_pass_check_reward_cache, 5 /* MGRP */, "turn,level");
        cachemap_mgr.cfg_pass_check_vipCache = new CfgCacheMapBase(cache_mgr.cfg_pass_check_vip_cache, 3 /* MKEY */, "turn,pass_type");
        cachemap_mgr.cfg_pay_vipCache = new CfgCacheMapBase(cache_mgr.cfg_pay_vip_cache, 2 /* KEY */, "level");
        cachemap_mgr.cfg_pay_vip_privilegeCache = new CfgCacheMapBase(cache_mgr.cfg_pay_vip_privilege_cache, 4 /* GRP */, "level");
        cachemap_mgr.cfg_pay_vip_privilege_typeCache = new CfgCacheMapBase(cache_mgr.cfg_pay_vip_privilege_cache, 4 /* GRP */, "priv_type");
        cachemap_mgr.cfg_pay_vip_privilege_function_typeCache = new CfgCacheMapBase(cache_mgr.cfg_pay_vip_privilege_function_cache, 2 /* KEY */, "priv_type");
        cachemap_mgr.cfg_payment_shop_itemCache = new CfgCacheMapBase(cache_mgr.cfg_payment_shop_item_cache, 2 /* KEY */, "item_id");
        cachemap_mgr.cfg_payment_shop_linkCache = new CfgCacheMapBase(cache_mgr.cfg_payment_shop_link_cache, 2 /* KEY */, "link_id");
        cachemap_mgr.cfg_payment_time_giftCache = new CfgCacheMapBase(cache_mgr.cfg_payment_time_gift_cache, 4 /* GRP */, "item_tab");
        cachemap_mgr.cfg_peak_miscCache = new CfgCacheMapBase(cache_mgr.cfg_peak_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_peak_timeCache = new CfgCacheMapBase(cache_mgr.cfg_peak_time_cache, 3 /* MKEY */, "model,round,step");
        cachemap_mgr.cfg_peak_time_model_roundCache = new CfgCacheMapBase(cache_mgr.cfg_peak_time_cache, 5 /* MGRP */, "model,round");
        cachemap_mgr.cfg_peerless_act_hero_giftCache = new CfgCacheMapBase(cache_mgr.cfg_peerless_act_hero_gift_cache, 1 /* ID */, "reward_id");
        cachemap_mgr.cfg_platform_ad_id_miscCache = new CfgCacheMapBase(cache_mgr.cfg_platform_ad_id_misc_cache, 2 /* KEY */, "platform");
        cachemap_mgr.cfg_player_strategyCache = new CfgCacheMapBase(cache_mgr.cfg_player_strategy_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_playing_preview_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_playing_preview_reward_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_progress_giftCache = new CfgCacheMapBase(cache_mgr.cfg_progress_gift_cache, 3 /* MKEY */, "type,buy_times");
        cachemap_mgr.cfg_progress_giftByTypeCache = new CfgCacheMapBase(cache_mgr.cfg_progress_gift_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_pull_wordsCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_pull_words_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_pvp_mapCache = new CfgCacheMapBase(cache_mgr.cfg_pvp_map_cache, 1 /* ID */, "mapId");
        cachemap_mgr.cfg_qq_groupCache = new CfgCacheMapBase(cache_mgr.cfg_qq_group_cache, 2 /* KEY */, "platform");
        cachemap_mgr.cfg_qq_vipCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_qq_vip_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_qxzl_miscCache = new CfgCacheMapBase(cache_mgr.cfg_qxzl_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_random_boxCache = new CfgCacheMapBase(cache_mgr.cfg_random_box_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_random_pvpAllCache = new CfgCacheMapBase(cache_mgr.cfg_random_pvp_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_random_pvpGroupCache = new CfgCacheMapBase(cache_mgr.cfg_random_pvp_cache, 4 /* GRP */, "big_level");
        cachemap_mgr.cfg_random_pvp_head_frameCache = new CfgCacheMapBase(cache_mgr.cfg_random_pvp_head_frame_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_random_pvp_limitCache = new CfgCacheMapBase(cache_mgr.cfg_random_pvp_limit_cache, 1 /* ID */, "type");
        cachemap_mgr.cfg_random_pvp_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_random_pvp_reward_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_random_pvp_taskCache = new CfgCacheMapBase(cache_mgr.cfg_random_pvp_task_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_random_pvp_task_rewardsCache = new CfgCacheMapBase(cache_mgr.cfg_random_pvp_task_rewards_cache, 1 /* ID */, "grade");
        cachemap_mgr.cfg_rank_descCache = new CfgCacheMapBase(cache_mgr.cfg_rank_desc_cache, 2 /* KEY */, "rank_key");
        cachemap_mgr.cfg_rank_missionCache = new CfgCacheMapBase(cache_mgr.cfg_rank_mission_cache, 4 /* GRP */, "rank_key");
        cachemap_mgr.cfg_rank_missionCacheAll = new CfgCacheMapBase(cache_mgr.cfg_rank_mission_cache, 3 /* MKEY */, "rank_key,id");
        cachemap_mgr.cfg_rank_rewardsCache = new CfgCacheMapBase(cache_mgr.cfg_rank_rewards_cache, 4 /* GRP */, "rank_key");
        cachemap_mgr.cfg_rank_worshipCache = new CfgCacheMapBase(cache_mgr.cfg_rank_worship_cache, 1 /* ID */, "rank_key");
        cachemap_mgr.cfg_red_cliffCache = new CfgCacheMapBase(cache_mgr.cfg_red_cliff_cache, 5 /* MGRP */, "nation,leader_type_id");
        cachemap_mgr.cfg_red_cliff_bossCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_red_cliff_boss_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_red_cliff_openCache = new CfgCacheMapBase(cache_mgr.cfg_red_cliff_open_cache, 1 /* ID */, "nation");
        cachemap_mgr.cfg_red_cliff_refreshCache = new CfgCacheMapBase(cache_mgr.cfg_red_cliff_refresh_cache, 3 /* MKEY */, "nation,leader_type_id");
        cachemap_mgr.cfg_red_cliff_reviewCache = new CfgCacheMapBase(cache_mgr.cfg_red_cliff_review_cache, 2 /* KEY */, "platform_id");
        cachemap_mgr.cfg_retrievalCache = new CfgCacheMapBase(cache_mgr.cfg_retrieval_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_river_text_constCache = new CfgCacheMapBase(cache_mgr.cfg_river_text_const_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_profilesByTypeCache = new CfgCacheMapBase(cache_mgr.cfg_role_profile_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_profileCache = new CfgCacheMapBase(cache_mgr.cfg_role_profile_cache, 2 /* KEY */, "fashion_id");
        cachemap_mgr.cfg_san_xiao_actorCache = new CfgCacheMapBase(cache_mgr.cfg_san_xiao_actor_cache, 1 /* ID */, "actor_id");
        cachemap_mgr.cfg_san_xiao_guanqiaCache = new CfgCacheMapBase(cache_mgr.cfg_san_xiao_guanqia_cache, 1 /* ID */, "guanqia_id");
        cachemap_mgr.cfg_san_xiao_itemCache = new CfgCacheMapBase(cache_mgr.cfg_san_xiao_item_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_san_xiao_item_typeCache = new CfgCacheMapBase(cache_mgr.cfg_san_xiao_item_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_san_xiao_levelCache = new CfgCacheMapBase(cache_mgr.cfg_san_xiao_level_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_san_xiao_mapCache = new CfgCacheMapBase(cache_mgr.cfg_san_xiao_map_cache, 1 /* ID */, "map_id");
        cachemap_mgr.cfg_san_xiao_miscCache = new CfgCacheMapBase(cache_mgr.cfg_san_xiao_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_sceneCache = new CfgCacheMapBase(cache_mgr.cfg_scene_cache, 2 /* KEY */, "scene_name");
        cachemap_mgr.cfg_sdk_concern_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_sdk_concern_reward_cache, 2 /* KEY */, "platform_tag");
        cachemap_mgr.cfg_sdk_platform_descCanhe = new CfgCacheMapBase(cache_mgr.cfg_sdk_platform_desc_cache, 2 /* KEY */, "platform_tag");
        cachemap_mgr.cfg_sdk_rewardsCache = new CfgCacheMapBase(cache_mgr.cfg_sdk_rewards_cache, 2 /* KEY */, "type");
        cachemap_mgr.cfg_select_boxCache = new CfgCacheMapBase(cache_mgr.cfg_select_box_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_seven_goal_singleCache = new CfgCacheMapBase(cache_mgr.cfg_seven_goal_cache, 3 /* MKEY */, "rewards_id,tag_id");
        cachemap_mgr.cfg_seven_goalCache = new CfgCacheMapBase(cache_mgr.cfg_seven_goal_cache, 5 /* MGRP */, "rewards_id,day");
        cachemap_mgr.cfg_seven_goal_giftCache = new CfgCacheMapBase(cache_mgr.cfg_seven_goal_gift_cache, 4 /* GRP */, "rewards_id");
        cachemap_mgr.cfg_seven_goal_missionCache = new CfgCacheMapBase(cache_mgr.cfg_seven_goal_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_seven_goal_missionListCache = new CfgCacheMapBase(cache_mgr.cfg_seven_goal_mission_cache, 5 /* MGRP */, "rewards_id,tag_id");
        cachemap_mgr.cfg_share_cycle_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_share_cycle_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_share_daily_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_share_daily_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_share_level_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_share_level_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_shili_previewCache = new CfgCacheMapBase(cache_mgr.cfg_shili_preview_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_shopCache = new CfgCacheMapBase(cache_mgr.cfg_shop_cache, 1 /* ID */, "shop_id");
        cachemap_mgr.cfg_shopGroupCache = new CfgCacheMapBase(cache_mgr.cfg_shop_cache, 4 /* GRP */, "group");
        cachemap_mgr.cfg_shop_itemCache = new CfgCacheMapBase(cache_mgr.cfg_shop_item_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_shop_itemByshop_idAndgoods_idCache = new CfgCacheMapBase(cache_mgr.cfg_shop_item_cache, 3 /* MKEY */, "shop_id,goods_id");
        cachemap_mgr.cfg_shop_item_tipsCache = new CfgCacheMapBase(cache_mgr.cfg_shop_item_tips_cache, 3 /* MKEY */, "shop_id,goods_id");
        cachemap_mgr.cfg_shop_reset_timesCache = new CfgCacheMapBase(cache_mgr.cfg_shop_reset_times_cache, 1 /* ID */, "shop_id");
        cachemap_mgr.cfg_shop_shortcutCache = new CfgCacheMapBase(cache_mgr.cfg_shop_shortcut_cache, 2 /* KEY */, "type_id");
        cachemap_mgr.cfg_shop_tab_limitCache = new CfgCacheMapBase(cache_mgr.cfg_shop_tab_limit_cache, 4 /* GRP */, "shop_id");
        cachemap_mgr.cfg_shop_tab_limit_mapCache = new CfgCacheMapBase(cache_mgr.cfg_shop_tab_limit_cache, 3 /* MKEY */, "shop_id,tab_id");
        cachemap_mgr.cfg_show_offCache = new CfgCacheMapBase(cache_mgr.cfg_show_off_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_skeleton_adaptiveCache = new CfgCacheMapBase(cache_mgr.cfg_skeleton_adaptive_cache, 3 /* MKEY */, "anim_name,anim_dir");
        cachemap_mgr.cfg_skillCache = new CfgCacheMapBase(cache_mgr.cfg_skill_cache, 1 /* ID */, "skill_id");
        cachemap_mgr.cfg_skill_effectCache = new CfgCacheMapBase(cache_mgr.cfg_skill_effect_cache, 1 /* ID */, "effect_id");
        cachemap_mgr.cfg_skill_eventCache = new CfgCacheMapBase(cache_mgr.cfg_skill_event_cache, 4 /* GRP */, "skill_id");
        cachemap_mgr.cfg_skill_levelCache = new CfgCacheMapBase(cache_mgr.cfg_skill_level_cache, 4 /* GRP */, "skill_id");
        cachemap_mgr.cfg_skill_summonCache = new CfgCacheMapBase(cache_mgr.cfg_skill_summon_cache, 1 /* ID */, "summon_id");
        cachemap_mgr.cfg_small_gameCache = new CfgCacheMapBase(cache_mgr.cfg_small_game_cache, 2 /* KEY */, "game_id");
        cachemap_mgr.cfg_soldier_gameCache = new CfgCacheMapBase(cache_mgr.cfg_soldier_game_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_soldier_game_rewardsCache = new CfgCacheMapBase(cache_mgr.cfg_soldier_game_rewards_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_soul_hero_link_levelCache = new CfgCacheMapBase(cache_mgr.cfg_soul_hero_link_level_cache, 1 /* ID */, "level");
        cachemap_mgr.cfg_soul_hero_link_limit_unlockCache = new CfgCacheMapBase(cache_mgr.cfg_soul_hero_link_limit_unlock_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_soul_hero_link_nationCache = new CfgCacheMapBase(cache_mgr.cfg_soul_hero_link_nation_cache, 3 /* MKEY */, "type_id,nation");
        cachemap_mgr.cfg_stage_breedCache = new CfgCacheMapBase(cache_mgr.cfg_stage_breed_cache, 2 /* KEY */, "big_stage");
        cachemap_mgr.cfg_stage_breed_attrCache = new CfgCacheMapBase(cache_mgr.cfg_stage_breed_attr_cache, 2 /* KEY */, "big_stage");
        cachemap_mgr.cfg_stage_copyCache = new CfgCacheMapBase(cache_mgr.cfg_stage_copy_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_stage_copy_bossCache = new CfgCacheMapBase(cache_mgr.cfg_stage_copy_boss_cache, 2 /* KEY */, "pass");
        cachemap_mgr.cfg_stage_copy_bossChapterCache = new CfgCacheMapBase(cache_mgr.cfg_stage_copy_boss_cache, 4 /* GRP */, "chapter_id");
        cachemap_mgr.cfg_stage_copy_boss_mapCache = new CfgCacheMapBase(cache_mgr.cfg_stage_copy_boss_cache, 2 /* KEY */, "map_item_id");
        cachemap_mgr.cfg_stage_copy_daily_missionCache = new CfgCacheMapBase(cache_mgr.cfg_stage_copy_daily_mission_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_stage_copy_miscCache = new CfgCacheMapBase(cache_mgr.cfg_stage_copy_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_stage_copy_storyCache = new CfgCacheMapBase(cache_mgr.cfg_stage_copy_story_cache, 2 /* KEY */, "pass");
        cachemap_mgr.cfg_stage_missionCache = new CfgCacheMapBase(cache_mgr.cfg_stage_mission_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_stage_skill_attrCache = new CfgCacheMapBase(cache_mgr.cfg_stage_skill_attr_cache, 3 /* MKEY */, "type_id,skill_level");
        cachemap_mgr.cfg_stage_skill_attrByTypeCache = new CfgCacheMapBase(cache_mgr.cfg_stage_skill_attr_cache, 4 /* GRP */, "type_id");
        cachemap_mgr.cfg_stage_skill_typeCache = new CfgCacheMapBase(cache_mgr.cfg_stage_skill_type_cache, 1 /* ID */, "type_id");
        cachemap_mgr.cfg_star_plan_giftCache = new CfgCacheMapBase(cache_mgr.cfg_star_plan_gift_cache, 3 /* MKEY */, "act_type,star");
        cachemap_mgr.cfg_star_plan_giftByreact_typeCache = new CfgCacheMapBase(cache_mgr.cfg_star_plan_gift_cache, 4 /* GRP */, "act_type");
        cachemap_mgr.cfg_star_plan_heroCache = new CfgCacheMapBase(cache_mgr.cfg_star_plan_hero_cache, 1 /* ID */, "rewards_id");
        cachemap_mgr.cfg_star_plan_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_star_plan_reward_cache, 3 /* MKEY */, "rewards_id,type_id,need_star");
        cachemap_mgr.cfg_star_plan_rewardByrewards_idCache = new CfgCacheMapBase(cache_mgr.cfg_star_plan_reward_cache, 5 /* MGRP */, "rewards_id,type_id");
        cachemap_mgr.cfg_star_plan_rewardByrewards_idAndMain_pushCache = new CfgCacheMapBase(cache_mgr.cfg_star_plan_reward_cache, 5 /* MGRP */, "rewards_id,main_push");
        cachemap_mgr.cfg_storyCache = new CfgCacheMapBase(cache_mgr.cfg_story_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_story_match_typeCache = new CfgCacheMapBase(cache_mgr.cfg_story_cache, 4 /* GRP */, "match_type");
        cachemap_mgr.cfg_story_actionCache = new CfgCacheMapBase(cache_mgr.cfg_story_action_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_story_actorCache = new CfgCacheMapBase(cache_mgr.cfg_story_actor_cache, 1 /* ID */, "actor_id");
        cachemap_mgr.cfg_story_bubbleCache = new CfgCacheMapBase(cache_mgr.cfg_story_bubble_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_story_mazeCache = new CfgCacheMapBase(cache_mgr.cfg_story_maze_cache, 3 /* MKEY */, "floor,pos");
        cachemap_mgr.cfg_story_maze_mission_spoilsCache = new CfgCacheMapBase(cache_mgr.cfg_story_maze_mission_spoils_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_story_maze_monsterCache = new CfgCacheMapBase(cache_mgr.cfg_story_maze_monster_cache, 4 /* GRP */, "rewards_id");
        cachemap_mgr.cfg_story_maze_resetCache = new CfgCacheMapBase(cache_mgr.cfg_story_maze_reset_cache, 1 /* ID */, "count");
        cachemap_mgr.cfg_story_maze_reviveCache = new CfgCacheMapBase(cache_mgr.cfg_story_maze_revive_cache, 1 /* ID */, "count");
        cachemap_mgr.cfg_story_maze_rewardsCache = new CfgCacheMapBase(cache_mgr.cfg_story_maze_rewards_cache, 1 /* ID */, "floor");
        cachemap_mgr.cfg_story_maze_shopCache = new CfgCacheMapBase(cache_mgr.cfg_story_maze_shop_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_story_maze_themeCache = new CfgCacheMapBase(cache_mgr.cfg_story_maze_theme_cache, 1 /* ID */, "rewards_id");
        cachemap_mgr.cfg_story_siegelord_city_typeCache = new CfgCacheMapBase(cache_mgr.cfg_story_siegelord_city_type_cache, 1 /* ID */, "type");
        cachemap_mgr.cfg_story_siegelord_levelCache = new CfgCacheMapBase(cache_mgr.cfg_story_siegelord_level_cache, 4 /* GRP */, "id");
        cachemap_mgr.cfg_story_siegelord_level_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_story_siegelord_level_reward_cache, 4 /* GRP */, "id");
        cachemap_mgr.cfg_story_siegelord_miscCache = new CfgCacheMapBase(cache_mgr.cfg_story_siegelord_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_story_siegelord_pass_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_story_siegelord_pass_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_story_siegelord_pass_reward_bystarCache = new CfgCacheMapBase(cache_mgr.cfg_story_siegelord_pass_reward_cache, 1 /* ID */, "star");
        cachemap_mgr.cfg_story_tower_battleCache = new CfgCacheMapBase(cache_mgr.cfg_story_tower_battle_cache, 5 /* MGRP */, "game_level,house,floor");
        cachemap_mgr.cfg_story_tower_battle_Game_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_story_tower_battle_cache, 4 /* GRP */, "game_level");
        cachemap_mgr.cfg_story_tower_battle_monsterCache = new CfgCacheMapBase(cache_mgr.cfg_story_tower_battle_monster_cache, 2 /* KEY */, "monster_id");
        cachemap_mgr.cfg_story_tower_battle_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_story_tower_battle_reward_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_suit_attr_Cache = new CfgCacheMapBase(cache_mgr.cfg_suit_attr_cache, 4 /* GRP */, "suit_id");
        cachemap_mgr.cfg_supreme_lotteryCache = new CfgCacheMapBase(cache_mgr.cfg_supreme_lottery_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_svip_pay_giftCache = new CfgCacheMapBase(cache_mgr.cfg_svip_pay_gift_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_sys_open_noticeCache = new CfgCacheMapBase(cache_mgr.cfg_sys_open_notice_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_sys_openlvCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_sys_openlv_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_sys_use_timesCache = new CfgCacheMapBase(cache_mgr.cfg_sys_use_times_cache, 2 /* KEY */, "type");
        cachemap_mgr.cfg_taxCache = new CfgCacheMapBase(cache_mgr.cfg_tax_cache, 2 /* KEY */, "type");
        cachemap_mgr.cfg_tax_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_tax_reward_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_td_mainCache = new CfgCacheMapBase(cache_mgr.cfg_td_main_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_td_main_missionCache = new CfgCacheMapBase(cache_mgr.cfg_td_main_mission_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_td_main_monsterCache = new CfgCacheMapBase(cache_mgr.cfg_td_main_monster_cache, 3 /* MKEY */, "id,wave");
        cachemap_mgr.cfg_td_main_monsterByPassCache = new CfgCacheMapBase(cache_mgr.cfg_td_main_monster_cache, 4 /* GRP */, "id");
        cachemap_mgr.cfg_td_main_pass_missionCache = new CfgCacheMapBase(cache_mgr.cfg_td_main_pass_mission_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_td_mapCache = new CfgCacheMapBase(cache_mgr.cfg_td_map_cache, 2 /* KEY */, "map_id");
        cachemap_mgr.cfg_td_monster_talkGroupCache = new CfgCacheMapBase(cache_mgr.cfg_td_monster_talk_cache, 4 /* GRP */, "pass_id");
        cachemap_mgr.cfg_td_trialCache = new CfgCacheMapBase(cache_mgr.cfg_td_trial_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_td_trial_monsterCache = new CfgCacheMapBase(cache_mgr.cfg_td_trial_monster_cache, 3 /* MKEY */, "id,wave");
        cachemap_mgr.cfg_team_bossCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_team_boss_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_team_xswh_bossCache = new CfgCacheMapBase(cache_mgr.cfg_team_xswh_boss_cache, 5 /* MGRP */, "day,season_type");
        cachemap_mgr.cfg_team_xswh_giftCache = new CfgCacheMapBase(cache_mgr.cfg_team_xswh_gift_cache, 3 /* MKEY */, "times,season_type");
        cachemap_mgr.cfg_team_xswh_giftbyseasontypeCache = new CfgCacheMapBase(cache_mgr.cfg_team_xswh_gift_cache, 4 /* GRP */, "season_type");
        cachemap_mgr.cfg_team_xswh_hurt_rewardsCache = new CfgCacheMapBase(cache_mgr.cfg_team_xswh_hurt_rewards_cache, 5 /* MGRP */, "day,season_type");
        cachemap_mgr.cfg_team_xswh_rank_rewardsCache = new CfgCacheMapBase(cache_mgr.cfg_team_xswh_rank_rewards_cache, 5 /* MGRP */, "day,season_type");
        cachemap_mgr.cfg_tequanCache = new CfgCacheMapBase(cache_mgr.cfg_tequan_cache, 1 /* ID */, "tequan_id");
        cachemap_mgr.cfg_test_towerCache = new CfgCacheMapBase(cache_mgr.cfg_test_tower_cache, 3 /* MKEY */, "get_arr,floor");
        cachemap_mgr.cfg_test_towerByTypeCache = new CfgCacheMapBase(cache_mgr.cfg_test_tower_cache, 4 /* GRP */, "fb_type");
        cachemap_mgr.cfg_test_towerByGetArr = new CfgCacheMapBase(cache_mgr.cfg_test_tower_cache, 4 /* GRP */, "get_arr");
        cachemap_mgr.cfg_test_tower_extra_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_test_tower_extra_reward_cache, 5 /* MGRP */, "get_arr,type");
        cachemap_mgr.cfg_test_tower_extra_reward_keyCache = new CfgCacheMapBase(cache_mgr.cfg_test_tower_extra_reward_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_test_tower_skinCache = new CfgCacheMapBase(cache_mgr.cfg_test_tower_skin_cache, 2 /* KEY */, "skin_id");
        cachemap_mgr.cfg_test_tower_skin_resCache = new CfgCacheMapBase(cache_mgr.cfg_test_tower_skin_cache, 2 /* KEY */, "skin_res");
        cachemap_mgr.cfg_theme_act_famous_lottery_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_theme_act_famous_lottery_reward_cache, 1 /* ID */, "reward_id");
        cachemap_mgr.cfg_theme_act_hero_lottery_showCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_theme_act_hero_lottery_show_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_theme_act_itemCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_theme_act_item_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_theme_act_rare_lottery_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_theme_act_rare_lottery_reward_cache, 1 /* ID */, "reward_id");
        cachemap_mgr.cfg_theme_act_skin_lotteryAllCache = new CfgCacheMapBase(cache_mgr.cfg_theme_act_skin_lottery_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_theme_act_skin_lottery_costCache = new CfgCacheMapBase(cache_mgr.cfg_theme_act_skin_lottery_cost_cache, 3 /* MKEY */, "plan_id,floor");
        cachemap_mgr.cfg_theme_act_wish_lotteryCache = new CfgCacheMapBase(cache_mgr.cfg_theme_act_wish_lottery_cache, 1 /* ID */, "reward_id");
        cachemap_mgr.cfg_theme_act_wish_lottery_itemCache = new CfgCacheMapBase(cache_mgr.cfg_theme_act_wish_lottery_item_cache, 5 /* MGRP */, "reward_id,hero_type_id");
        cachemap_mgr.cfg_theme_act_wish_lottery_showCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_theme_act_wish_lottery_show_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_tiled_effectCache = new CfgCacheMapBase(cache_mgr.cfg_tiled_effect_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_tiled_mapCache = new CfgCacheMapBase(cache_mgr.cfg_tiled_map_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_time_achievementByIdCache = new CfgCacheMapBase(cache_mgr.cfg_time_achievement_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_time_achievementCache = new CfgCacheMapBase(cache_mgr.cfg_time_achievement_cache, 4 /* GRP */, "page");
        cachemap_mgr.cfg_time_activity_dropCache = new CfgCacheMapBase(cache_mgr.cfg_time_activity_drop_cache, 3 /* MKEY */, "rewards_id,type");
        cachemap_mgr.cfg_time_activity_shopCache = new CfgCacheMapBase(cache_mgr.cfg_time_activity_shop_cache, 5 /* MGRP */, "shop_id,rewards_id");
        cachemap_mgr.cfg_time_activity_shopAllCache = new CfgCacheMapBase(cache_mgr.cfg_time_activity_shop_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_time_activity_weekCache = new CfgCacheMapBase(cache_mgr.cfg_time_activity_week_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_tipsCache = new CfgCacheMapBase(cache_mgr.cfg_tips_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_titleCache = new CfgCacheMapBase(cache_mgr.cfg_title_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_travelCache = new CfgCacheMapBase(cache_mgr.cfg_travel_cache, 2 /* KEY */, "mission_type");
        cachemap_mgr.cfg_travel_extCache = new CfgCacheMapBase(cache_mgr.cfg_travel_ext_cache, 2 /* KEY */, "index");
        cachemap_mgr.cfg_treasure_boxCache = new CfgCacheMapBase(cache_mgr.cfg_treasure_box_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_treasure_box_typeCache = new CfgCacheMapBase(cache_mgr.cfg_treasure_box_type_cache, 3 /* MKEY */, "type,box_id");
        cachemap_mgr.cfg_treasure_box_typeGrpCache = new CfgCacheMapBase(cache_mgr.cfg_treasure_box_type_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_treasure_energyCache = new CfgCacheMapBase(cache_mgr.cfg_treasure_energy_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_treasure_giftCache = new CfgCacheMapBase(cache_mgr.cfg_treasure_gift_cache, 2 /* KEY */, "gift_id");
        cachemap_mgr.cfg_treasure_miscCache = new CfgCacheMapBase(cache_mgr.cfg_treasure_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_treasure_refresh_costCache = new CfgCacheMapBase(cache_mgr.cfg_treasure_refresh_cost_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_treasure_workerCache = new CfgCacheMapBase(cache_mgr.cfg_treasure_worker_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_trig_skillCache = new CfgCacheMapBase(cache_mgr.cfg_trig_skill_cache, 4 /* GRP */, "skill_id");
        cachemap_mgr.cfg_ui_button_styleCache = new CfgCacheMapBase(cache_mgr.cfg_ui_button_style_cache, 2 /* KEY */, "btn_res");
        cachemap_mgr.cfg_ui_preloadCache = new CfgCacheMapBase(cache_mgr.cfg_ui_preload_cache, 2 /* KEY */, "ui");
        cachemap_mgr.cfg_ui_residentCache = new CfgCacheMapBase(cache_mgr.cfg_ui_resident_cache, 2 /* KEY */, "res");
        cachemap_mgr.cfg_up_star_giftCache = new CfgCacheMapBase(cache_mgr.cfg_up_star_gift_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_up_star_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_up_star_reward_cache, 3 /* MKEY */, "reward_id,id");
        cachemap_mgr.cfg_up_star_rewardByroundCache = new CfgCacheMapBase(cache_mgr.cfg_up_star_reward_cache, 5 /* MGRP */, "reward_id,round");
        cachemap_mgr.cfg_up_star_rewardByreward_idCache = new CfgCacheMapBase(cache_mgr.cfg_up_star_reward_cache, 4 /* GRP */, "reward_id");
        cachemap_mgr.cfg_vip_daily_missionCache = new CfgCacheMapBase(cache_mgr.cfg_vip_daily_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_vip_daily_mission_giftCacheTemp = new CfgCacheMapBase(cache_mgr.cfg_vip_daily_mission_gift_cache, 0 /* NULL */, "null");
        cachemap_mgr.cfg_vip_kefuCache = new CfgCacheMapBase(cache_mgr.cfg_vip_kefu_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_vip_kefuBysdknameCache = new CfgCacheMapBase(cache_mgr.cfg_vip_kefu_cache, 2 /* KEY */, "checkSdk");
        cachemap_mgr.cfg_vip_kefu_reviewCache = new CfgCacheMapBase(cache_mgr.cfg_vip_kefu_review_cache, 2 /* KEY */, "platform_id");
        cachemap_mgr.cfg_war_flagCache = new CfgCacheMapBase(cache_mgr.cfg_war_flag_cache, 1 /* ID */, "nation");
        cachemap_mgr.cfg_war_flag_facadeCache = new CfgCacheMapBase(cache_mgr.cfg_war_flag_facade_cache, 4 /* GRP */, "nation");
        cachemap_mgr.cfg_war_flag_levelCache = new CfgCacheMapBase(cache_mgr.cfg_war_flag_level_cache, 3 /* MKEY */, "nation,level");
        cachemap_mgr.cfg_war_flag_linkCache = new CfgCacheMapBase(cache_mgr.cfg_war_flag_link_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_war_flag_recycleCache = new CfgCacheMapBase(cache_mgr.cfg_war_flag_recycle_cache, 2 /* KEY */, "time");
        cachemap_mgr.cfg_war_flag_stageCache = new CfgCacheMapBase(cache_mgr.cfg_war_flag_stage_cache, 3 /* MKEY */, "nation,stage");
        cachemap_mgr.cfg_war_flag_stageCacheByNationCache = new CfgCacheMapBase(cache_mgr.cfg_war_flag_stage_cache, 4 /* GRP */, "nation");
        cachemap_mgr.cfg_war_log_missionCache = new CfgCacheMapBase(cache_mgr.cfg_war_log_mission_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_war_log_mission_pay_rewardTypeCache = new CfgCacheMapBase(cache_mgr.cfg_war_log_mission_pay_reward_cache, 4 /* GRP */, "reward_id");
        cachemap_mgr.cfg_war_log_mission_score_rewardTypeCache = new CfgCacheMapBase(cache_mgr.cfg_war_log_mission_score_reward_cache, 4 /* GRP */, "reward_id");
        cachemap_mgr.cfg_wars_honorCache = new CfgCacheMapBase(cache_mgr.cfg_wars_honor_cache, 2 /* KEY */, "type");
        cachemap_mgr.cfg_wars_map_campCache = new CfgCacheMapBase(cache_mgr.cfg_wars_map_camp_cache, 3 /* MKEY */, "map_type,camp");
        cachemap_mgr.cfg_wars_map_camp_listCache = new CfgCacheMapBase(cache_mgr.cfg_wars_map_camp_cache, 4 /* GRP */, "map_type");
        cachemap_mgr.cfg_wars_map_city_allCache = new CfgCacheMapBase(cache_mgr.cfg_wars_map_city_cache, 4 /* GRP */, "map_type");
        cachemap_mgr.cfg_wars_map_cityCache = new CfgCacheMapBase(cache_mgr.cfg_wars_map_city_cache, 3 /* MKEY */, "map_type,city_id");
        cachemap_mgr.cfg_wars_map_city_posCache = new CfgCacheMapBase(cache_mgr.cfg_wars_map_city_cache, 3 /* MKEY */, "map_type,pos");
        cachemap_mgr.cfg_wars_map_near_city2Cache = new CfgCacheMapBase(cache_mgr.cfg_wars_map_near_city2_cache, 3 /* MKEY */, "map_type,start_pos,end_pos");
        cachemap_mgr.cfg_wars_map_near_city_list2Cache = new CfgCacheMapBase(cache_mgr.cfg_wars_map_near_city2_cache, 4 /* GRP */, "map_type");
        cachemap_mgr.cfg_wars_map_typeCache = new CfgCacheMapBase(cache_mgr.cfg_wars_map_type_cache, 1 /* ID */, "map_type");
        cachemap_mgr.cfg_wars_map_type_sys_idCache = new CfgCacheMapBase(cache_mgr.cfg_wars_map_type_cache, 2 /* KEY */, "sys_id");
        cachemap_mgr.cfg_wars_map_type_matchCache = new CfgCacheMapBase(cache_mgr.cfg_wars_map_type_cache, 4 /* GRP */, "match_type");
        cachemap_mgr.cfg_wars_miscCache = new CfgCacheMapBase(cache_mgr.cfg_wars_misc_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_wars_missionCache = new CfgCacheMapBase(cache_mgr.cfg_wars_mission_cache, 2 /* KEY */, "id");
        cachemap_mgr.cfg_wars_stateCache = new CfgCacheMapBase(cache_mgr.cfg_wars_state_cache, 3 /* MKEY */, "map_type,plan_id,state");
        cachemap_mgr.cfg_wars_state_modelCache = new CfgCacheMapBase(cache_mgr.cfg_wars_state_cache, 5 /* MGRP */, "map_type,plan_id,model");
        cachemap_mgr.cfg_wars_text_constCache = new CfgCacheMapBase(cache_mgr.cfg_wars_text_const_cache, 2 /* KEY */, "key");
        cachemap_mgr.cfg_week_targetBytypeCache = new CfgCacheMapBase(cache_mgr.cfg_week_target_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_week_targetCache = new CfgCacheMapBase(cache_mgr.cfg_week_target_cache, 3 /* MKEY */, "type,id");
        cachemap_mgr.cfg_week_target_levelBytypeCache = new CfgCacheMapBase(cache_mgr.cfg_week_target_level_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_week_target_levelCache = new CfgCacheMapBase(cache_mgr.cfg_week_target_level_cache, 3 /* MKEY */, "type,assist_level");
        cachemap_mgr.cfg_wing_buffAllCache = new CfgCacheMapBase(cache_mgr.cfg_wing_buff_cache, 2 /* KEY */, "level");
        cachemap_mgr.cfg_wing_heroAllCache = new CfgCacheMapBase(cache_mgr.cfg_wing_hero_cache, 3 /* MKEY */, "type_id,level");
        cachemap_mgr.cfg_wing_heroCache = new CfgCacheMapBase(cache_mgr.cfg_wing_hero_cache, 4 /* GRP */, "type_id");
        cachemap_mgr.cfg_wing_hero_skinCache = new CfgCacheMapBase(cache_mgr.cfg_wing_hero_skin_cache, 2 /* KEY */, "model");
        cachemap_mgr.cfg_wing_levelAllCache = new CfgCacheMapBase(cache_mgr.cfg_wing_level_cache, 3 /* MKEY */, "wing_id,level");
        cachemap_mgr.cfg_wing_levelCache = new CfgCacheMapBase(cache_mgr.cfg_wing_level_cache, 4 /* GRP */, "wing_id");
        cachemap_mgr.cfg_world_boss_hurt_rewardsTypeCache = new CfgCacheMapBase(cache_mgr.cfg_world_boss_hurt_rewards_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_world_boss_hurt_rewardsCache = new CfgCacheMapBase(cache_mgr.cfg_world_boss_hurt_rewards_cache, 4 /* GRP */, "type");
        cachemap_mgr.cfg_world_boss_levelCache = new CfgCacheMapBase(cache_mgr.cfg_world_boss_level_cache, 4 /* GRP */, "boss_level");
        cachemap_mgr.cfg_world_boss_levelbytypeIdCache = new CfgCacheMapBase(cache_mgr.cfg_world_boss_level_cache, 4 /* GRP */, "skin_half");
        cachemap_mgr.cfg_world_mapCache = new CfgCacheMapBase(cache_mgr.cfg_world_map_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_world_map_2Cache = new CfgCacheMapBase(cache_mgr.cfg_world_map_2_cache, 1 /* ID */, "id");
        cachemap_mgr.cfg_wxShareCache = new CfgCacheMapBase(cache_mgr.cfg_wxShare_cache, 4 /* GRP */, "pf");
        cachemap_mgr.cfg_wxTurn_giftCache = new CfgCacheMapBase(cache_mgr.cfg_wxTurn_gift_cache, 2 /* KEY */, "platform");
        cachemap_mgr.cfg_wx_game_clubCache = new CfgCacheMapBase(cache_mgr.cfg_wx_game_club_cache, 4 /* GRP */, "plat_id");
        cachemap_mgr.cfg_xswh_bossCache = new CfgCacheMapBase(cache_mgr.cfg_xswh_boss_cache, 4 /* GRP */, "week");
        cachemap_mgr.cfg_xswh_giftCache = new CfgCacheMapBase(cache_mgr.cfg_xswh_gift_cache, 1 /* ID */, "times");
        cachemap_mgr.cfg_xswh_hurt_rewardsCache = new CfgCacheMapBase(cache_mgr.cfg_xswh_hurt_rewards_cache, 4 /* GRP */, "week");
        cachemap_mgr.cfg_xswh_rank_rewardsCache = new CfgCacheMapBase(cache_mgr.cfg_xswh_rank_rewards_cache, 4 /* GRP */, "week");
        cachemap_mgr.cfg_ybzk_rewardCache = new CfgCacheMapBase(cache_mgr.cfg_ybzk_reward_cache, 5 /* MGRP */, "rewards_id,type");
        cachemap_mgr.cfg_zero_buyCache = new CfgCacheMapBase(cache_mgr.cfg_zero_buy_cache, 1 /* ID */, "day");
        cachemap_mgr.cfg_errorCodeCache = new CfgCacheMapBase(cache_mgr.errorCode_cache, 1 /* ID */, "codeId");
        cachemap_mgr.cfg_fightAttrByIdCache = new CfgCacheMapBase(cache_mgr.fightAttr_cache, 1 /* ID */, "attrID");
        cachemap_mgr.cfg_fightAttrByKeyCache = new CfgCacheMapBase(cache_mgr.fightAttr_cache, 2 /* KEY */, "attrKey");
        cachemap_mgr.cfg_fightAttrByTypeCache = new CfgCacheMapBase(cache_mgr.fightAttr_cache, 4 /* GRP */, "show_type");
        cachemap_mgr.victoryMacroCache = new CfgCacheMapBase(cache_mgr.victoryMacro_cache, 1 /* ID */, "macroID");
        for (var k in cache_mgr.caches) {
            var cache = cache_mgr.caches[k];
            cache.once(Event.COMPLETE, null, (cache) => {
                CfgCacheMapMgr_CB.onComplete(cache);
            }, [cache]);
        }
    }
}
