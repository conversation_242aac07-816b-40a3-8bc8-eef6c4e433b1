import {CCMessage} from "../CCMessage";
import { Byte } from "laya/utils/Byte";
import {p_kv} from "../common/p_kv";


export class m_zero_buy_op_toc extends CCMessage
    {
        op_type:number = 0;
        login_days:number = 0;
        up_stage_status:number = 0;
        end_time:number = 0;
        fetch_list:p_kv[] = [];

       constructor()
       {
         super();
         
       }

       unpack(result:Byte):void 
       {
         this.op_type = result.readInt32();
         this.login_days = result.readInt16();
         this.up_stage_status = result.readInt32();
         this.end_time = result.readInt32();
         let fetch_listlen:number = result.readUint16();
         let fetch_listTemp:p_kv;
         for (let fetch_listIndex:number = 0; fetch_listIndex<fetch_listlen; fetch_listIndex++)
         {
            fetch_listTemp = new p_kv();
            fetch_listTemp.unpack(result);
            this.fetch_list.push(fetch_listTemp);
         }
       }
 
       protoId():number 
       {
           return 181;
       }

        childProtoId():number 
       {
           return 23168;
       }
}
