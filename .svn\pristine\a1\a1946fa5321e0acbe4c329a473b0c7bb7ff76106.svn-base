import { TDRoleStateRun } from "../../../../scene2d/role/fsm/state/TDRoleStateRun";
import { ESkeletonAction } from "../../../baseModules/skeleton/SkeletonData";
import { TDRolePathPointScript } from "../../../tdBase/game/script/TDRolePathPointScript";
import { GuajiBuildWorker } from "../GuajiBuildWorker";

export class GuajiBuildWorkerStateRun extends TDRoleStateRun {
    public playAni() {
        let worker = this.roleBase as GuajiBuildWorker;
        worker.setRunAction();
    }


    /**玩家拥有战斗待机动画**/
    ToStand() {
        // this.currRoleFSMMgr.ChangeState(ESkeletonAction.STAND);
        // this.roleBase.ToStand();
        //强制跑动状态.
        let script = this.roleBase.getComponent(TDRolePathPointScript);
        if (script && script.isEnd == false) {
            let worker = this.roleBase as GuajiBuildWorker;
            worker.setRunAction();
        }else{
            this.roleBase.ToStand();
        }
    }
}