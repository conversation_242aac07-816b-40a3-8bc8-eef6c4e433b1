import { Point } from "laya/maths/Point";
import { UrlConfig } from "../../../../game/UrlConfig";
import { cfg_hero_base } from "../../../cfg/vo/cfg_hero_base";
import { cfg_skill } from "../../../cfg/vo/cfg_skill";
import { AnimationManager } from "../../../managers/AnimationManager";
import { ConfigManager } from "../../../managers/ConfigManager";
import { p_fight_wpn } from "../../../proto/common/p_fight_wpn";
import { m_fight_start_toc } from "../../../proto/line/m_fight_start_toc";
import { CCAnimation } from "../../../scene2d/d2/CCAnimation";
import { RoleMgr } from "../../../scene2d/RoleMgr";
import { com } from "../../../ui/layaMaxUI";
import { GSkeleton } from "../../baseModules/skeleton/GSkeleton";
import { ESkeletonAction, ESkeletonType } from "../../baseModules/skeleton/SkeletonData";
import { SkeletonManager } from "../../baseModules/skeleton/SkeletonManager";
import { FightReplay } from "../../fight/data/FightConst";
import { FightDataCenter } from "../../fight/data/FightDataCenter";
import FightGodWeaponItem from "../../fight/view/FightGodWeaponItem";
import { FightQiMouItem } from "../../fight/view/FightQiMouItem";
import { FightStartAnimation } from "../../fight/view/FightStartAnimation";
import { GameConst } from "../../GameConst";
import { MiscConst } from "../../misc_config/MiscConst";
import { ModuleCommand } from "../../ModuleCommand";
import VipTeQuanUtil from "../../payment/VipTeQuanUtil";
import { GuaJiDataCenter } from "../data/GuaJiDataCenter";
import { FightUtil } from "../../fight/util/FightUtil";
import { MatchConst } from "../../../auto/ConstAuto";
import { UIList } from "../../baseModules/UIList";
import { UIProgressBar } from "../../baseModules/UIProgressBar";
import { FightBuffIcon } from "../../fight/view/FightBuffIcon";
import LineUpBuffItem from "../../lineUp/view/LineUpBuffItem";
import { FightDuoPingVo } from "../../fight/vo/FightDuoPingVo";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { FightQiMouEffectView } from "../../fight/view/FightQiMouEffectView";
import { FightSceneEffectView } from "../../fight/view/FightSceneEffectView";
import { p_fight_turn } from "../../../proto/common/p_fight_turn";
import { ActorType, FightTurnType, HurtType } from "../../../scene2d/SceneConst";
import { TipsUtil } from "../../../util/TipsUtil";
import { LineUpUtil } from "../../lineUp/util/LineUpUtil";
import { cfg_match_type } from "../../../cfg/vo/cfg_match_type";
import { Ease } from "laya/utils/Ease";
import { Handler } from "laya/utils/Handler";
import { Tween } from "laya/utils/Tween";
import { p_fight_actor } from "../../../proto/common/p_fight_actor";
import { DataCenter } from "../../DataCenter";
import { HeroExtKey } from "../../../scene2d/BattleConst";
import { Image } from "laya/ui/Image";
import { p_fight_hurt } from "../../../proto/common/p_fight_hurt";
import { Box } from "laya/ui/Box";

export class MainBattleView extends com.ui.res.guaJi.MainBattleViewUI {

    private _boxSk: GSkeleton;
    private _fightItemMap: Map<number, FightGodWeaponItem | FightQiMouItem>;
    private _leftGWItem: FightGodWeaponItem;
    private _leftQiMouItems: FightQiMouItem[];

    private _leftBuffItem: LineUpBuffItem;
    private _rightBuffItem: LineUpBuffItem;
    private _leftUIList: UIList;
    private _rightUIList: UIList;
    private _leftBloodBar: UIProgressBar;
    private _rightBloodBar: UIProgressBar;

    private turnInfo: p_fight_turn;
    private maxHpMap: Map<number, number> = new Map();
    private curHpMap: Map<number, number> = new Map();
    private turnSkipCache: { [turnIdx: number]: boolean } = {};

    private _match_type: number;
    private _match_cfg: cfg_match_type;

    
    private jinenghong: CCAnimation;
    private jinenglan: CCAnimation;

    private _fight_data: m_fight_start_toc;
    private _turnIdx: number = 0;
    private _roundIdx: number = 1;
    private _stepIdx: number = 0;
    private left_wpns: p_fight_wpn[] = [];
    private _rate: number = 1;
    private _decidingTxt: string;

    constructor() {
        super();

        this._match_type = MatchConst.MATCH_TYPE_MAIN_BATTLE;
        this._match_cfg = CfgCacheMapMgr.cfg_match_typeCache.get(this._match_type);
    }

    initUI() {
        this.height = this.relativeHeight;
        this.skillBgImg.y -= this.offsetY;
        this.effBox.y -= this.offsetY * 2;
        this.ratebtn.visible = false;

        this._fightItemMap = new Map();

        this._leftGWItem = new FightGodWeaponItem();
        this.leftGWBox.addChild(this._leftGWItem);
        this.godWeaponBox.y -= this.offsetY;
        
        let space = 3;
        this._leftQiMouItems = [];
        for (let i = 0; i < 2; ++i) {
            let item = new FightQiMouItem(i + 1, 1);
            item.pos(i * 50 + i * space, 0);
            this.leftQiMouBox.addChild(item);
            this._leftQiMouItems[i] = item
        }

        this._leftBuffItem = this.leftBuffItem;
        this._rightBuffItem = this.rightBuffItem;

        this._leftUIList = UIList.SetUIList(this, this.leftBuffList, FightBuffIcon);
        this._leftUIList.isBoxRight = true;
        this._rightUIList = UIList.SetUIList(this, this.rightBuffList, FightBuffIcon);

        this._decidingTxt = this.txtDeciding.text;

        this._leftBloodBar = UIProgressBar.SetUIProgressBar(this, this.leftBar);
        this._rightBloodBar = UIProgressBar.SetUIProgressBar(this, this.rightBar);
        this.timer.loop(33, this, this.updateFrame);
    }

    addEvent() {
        this.addEventListener(ModuleCommand.DROP_BOX_GUAJI_MAIN_BATTLE, this, this.onDropBoxEff);
        this.addEventListener(ModuleCommand.NEW_TURN_INDEX, this, this.updateTurnInfo);
        this.addEventListener(ModuleCommand.UPDATE_FIGHT_ROUND, this, this.updateRound);
        this.addEventListener(ModuleCommand.UPDATE_RATE, this, this.updateRate);
        this.addEventListener(ModuleCommand.SHOW_SKILL_NAME, this, this.showSkillName);
        this.addEventListener(ModuleCommand.HIDE_SKILL_NAME, this, this.hideSkillName);
        this.addEventListener(ModuleCommand.ADD_START_ANI, this, this.addStartAnimation);
        this.addEventListener(ModuleCommand.SHOW_QIMOU_EFF, this, this.showQiMouEff);
        this.addEventListener(ModuleCommand.SHOW_SCENE_EFF, this, this.showSceneEff);
        this.addEventListener(ModuleCommand.FIGHT_TRIG_HURT, this, this.fightTrigHurt);
        
    }

    private isResetBloodL: boolean = false;
    private isResetBloodR: boolean = false;
    private updateFrame(): void {
        if (this.isResetBloodL) {
            this.isResetBloodL = this.updateBloodBar(33, this._leftBloodBar);
        }
        if (this.isResetBloodR) {
            this.isResetBloodR = this.updateBloodBar(33, this._rightBloodBar);
        }
    }

    private get curRoundNum(): number {
        let fightTurn = this._fight_data.turns[this._turnIdx];
        let roundInfo = fightTurn ? fightTurn.rounds[this._roundIdx] : null;
        let ret = roundInfo ? roundInfo.round : this._roundIdx + 1;
        return ret;
    }

    private updateTurnInfo(turnIdx: number): void {
        if (!this._fight_data) {
            return;
        }
        
        //隐藏神器和龙魂Item，并且清除item的数据 start
        this._fightItemMap.clear();
        (this._leftGWItem.visible = false) || this._leftGWItem.clear();
        this._leftQiMouItems.forEach(item => item.reset());
        //隐藏神器和龙魂Item，并且清除item的数据 end

        this.maxHpMap.clear();
        this.curHpMap.clear();

        if (turnIdx >= this._fight_data.turns.length) return;
        let lastTurnIdx = this._turnIdx;
        this._turnIdx = turnIdx;
        var turnInfo = this._fight_data.turns[this._turnIdx];
        if (turnInfo) {
            let hasAssistant = this._match_cfg.lineup_num > 1 && this._match_cfg.enable_assistant == 1;

            this.turnInfo = turnInfo;

            this.imgTeamL.visible = false;
            this.imgTeamR.visible = false;

            this.isResetBloodL = false;
            this.isResetBloodR = false;
            let lastTurn = this._fight_data.turns[turnIdx - 1];
            if (hasAssistant && lastTurn) {
                let team1 = 1, team2 = 1;
                for (let i = 0; i < turnIdx; ++i) {
                    let turn = this._fight_data.turns[i];
                    if (turn.turn_result == 1) {
                        team2 += 1;
                    }
                    else {
                        team1 += 1;
                    }
                }
                this.labTeamL.text = LineUpUtil.getLineUpIndexName(this._match_type, team1);
                this.labTeamR.text = LineUpUtil.getLineUpIndexName(this._match_type, team2);
                this.isResetBloodL = lastTurn.turn_result == 2;
                this.isResetBloodR = lastTurn.turn_result == 1;
            }

            this.refreshHpMap(turnInfo.left_actors);
            this.refreshHpMap(turnInfo.right_actors);
            this.refreshBloodBar(this.isResetBloodL, this.isResetBloodR);
            if (this._leftBuffItem) {
                this._leftBuffItem.setData(turnInfo.left_actors.filter(actor => {
                    return actor.actor_type != ActorType.Summoned && actor.actor_type != ActorType.Lord;
                }));
            }
            if (this._rightBuffItem) {
                this._rightBuffItem.setData(turnInfo.right_actors.filter(actor => {
                    return actor.actor_type != ActorType.Summoned && actor.actor_type != ActorType.Lord;
                }));
            }
            this.refreshStyle(this.leftStyleImg, turnInfo.left_lineup);
            this.refreshStyle(this.rightStyleImg, turnInfo.right_lineup);
            this.refreshName(turnInfo);
            this.setWeaponData(turnInfo.left_wpns,this.leftQiMouBox);
            this.setWeaponData(turnInfo.right_wpns);
            this.countDownRound(this.curRoundNum);
            this.showAssistantTip(turnIdx);
            this.refreshBuffList(turnInfo, this._leftUIList, true);
            this.refreshBuffList(turnInfo, this._rightUIList, false);

            if (this._turnIdx != lastTurnIdx) {
                let lastTurn = this._fight_data.turns[this._turnIdx - 1];
                if (lastTurn) {
                    let finalRound = lastTurn.start_round - 1 + lastTurn.rounds.length;
                    if (turnInfo.start_round > finalRound + 1) {
                        TipsUtil.showTips(window.iLang.L2_HUI_HE_ZENG_JIA.il() + (turnInfo.start_round - finalRound));
                    }
                }
            }
        }
    }


    private refreshHpMap(actors: p_fight_actor[]): void {
        for (let actor of actors) {
            if (ActorType.Summoned == actor.actor_type) continue;
            this.maxHpMap.set(actor.actor_sn, actor.max_hp);
            this.curHpMap.set(actor.actor_sn, actor.hp);
        }
    }

    private refreshBloodBar(isResetL: boolean = false, isResetR: boolean = false): void {
        let maxHpL = 0, maxHpR = 0;
        let curHpL = 0, curHpR = 0;

        let isLeftExchange = false;
        //角色数据进行过交换 交换接口：FightDataCenter.revertFightStart
        if (this._fight_data && this._fight_data["_isExchange_"]) {
            isLeftExchange = true;
        }

        for (let [sn, hp] of this.maxHpMap.entries()) {
            let curHp = this.curHpMap.get(sn) || 0;
            if (1 <= sn && sn <= 100) {
                maxHpL += hp;
                curHpL += curHp;
            }
            else if (101 <= sn && sn <= 200) {
                maxHpR += hp;
                curHpR += curHp;
            }
        }

        if (isLeftExchange) {
            let tempMax = maxHpL;
            let tempCur = curHpL;
            maxHpL = maxHpR;
            curHpL = curHpR;
            maxHpR = tempMax;
            curHpR = tempCur;
        }

        if (isResetL) {
            let lastRateL = this._leftBloodBar.showValue / this._leftBloodBar.maxValue;
            this._leftBloodBar.SetValue(lastRateL * maxHpL, maxHpL);
        }
        else {
            this._leftBloodBar.SetValue(curHpL, maxHpL);
        }

        if (isResetR) {
            let lastRateR = this._rightBloodBar.showValue / this._rightBloodBar.maxValue;
            this._rightBloodBar.SetValue(lastRateR * maxHpR, maxHpR);
        }
        else {
            this._rightBloodBar.SetValue(curHpR, maxHpR);
        }
    }

    private updateBloodBar(interval: number, bloodBar: UIProgressBar): boolean {
        if (bloodBar && bloodBar.showValue < bloodBar.maxValue) {
            let val = Math.floor(bloodBar.maxValue / 1000 * interval);
            bloodBar.SetValue(bloodBar.showValue + val, bloodBar.maxValue);
            return true;
        }
        return false;
    }

    private refreshBuffList(fightTurn: p_fight_turn, list: UIList, isLeft: boolean): void {
        let buffDatas = [];
        if (list && fightTurn) {
            let actor = isLeft ? fightTurn.left_actors[0] : fightTurn.right_actors[0];
            if (actor) {
                actor.hero_ext.forEach((pkv) => {
                    if (pkv.key == HeroExtKey.WIN_BUFF && pkv.val > 0) {//战后疲劳
                        buffDatas.push({
                            order: isLeft ? 1 : 2,
                            buffId: 8000330,
                            buffLayer: pkv.val,
                            //战后疲劳等待特效动画播放完成才显示
                            isHide: pkv.val <= 1 && this._turnIdx < 2 && this._roundIdx < 1 && this._stepIdx < 1,
                        });
                    }
                    else if (pkv.key == HeroExtKey.FAIL_BUFF && pkv.val > 0) {//哀兵必胜
                        buffDatas.push({
                            order: isLeft ? 2 : 1,
                            buffId: 8000333,
                            buffLayer: pkv.val,
                            preDesc: window.iLang.L2_KUA_FU_DUAN_WEI_SAI_ZENG_YI_XIAO_GUO_ch31_MEI_YONG_YOU_YI_CENG_ch31_ZAI_XIA.il(),
                        });
                    }
                });
            }
        }
        list.array = buffDatas.sort((data1, data2) => data1.order - data2.order);
    }

    private refreshStyle(styleImg: Image, styleId: number): void {
        let lineup_style_cfg = ConfigManager.cfg_lineup_styleCache.get(styleId);
        if (lineup_style_cfg) {
            styleImg.skin = UrlConfig.BASE_RES_UI_URL + "lineup/" + lineup_style_cfg.icon + ".png";
        }
        else {
            styleImg.skin = "";
        }
    }

    private refreshName(trunInfo: p_fight_turn): void {
        if (this._fight_data.common_info.leftname != DataCenter.myRoleName && this._fight_data.common_info.targetname == DataCenter.myRoleName) {
            this.rightNameLabel.text = trunInfo.left_name;
            this.leftNameLabel.text = trunInfo.right_name;
        } else {
            this.leftNameLabel.text = trunInfo.left_name ? trunInfo.left_name : (this._fight_data.common_info.leftname.length > 0 ? this._fight_data.common_info.leftname : DataCenter.myRoleName);
            this.rightNameLabel.text = trunInfo.right_name ? trunInfo.right_name : this._fight_data.common_info.targetname;
            if (!this.rightNameLabel.text && trunInfo.right_actors[0]) {//没有名称，默认显示第一个敌人的配置名称
                let right_actor = trunInfo.right_actors[0];
                if (right_actor.actor_type == ActorType.Hero) {
                    let cfg = ConfigManager.cfg_hero_baseCache.get(right_actor.hero_type_id);
                    this.rightNameLabel.text = cfg ? cfg.name : "";
                }
                else if (right_actor.actor_type == ActorType.Monster) {
                    let cfg = ConfigManager.cfg_monsterCache.get(right_actor.type_id);
                    this.rightNameLabel.text = cfg ? cfg.name : "";
                }
            }
        }
    }

    private showAssistantTip(turnIdx: number): void {
        let matchType = this._fight_data.match_type;
        if (matchType == MatchConst.MATCH_TYPE_GOD_TRIAL) {
            return;
        }
        let cfgMatch = CfgCacheMapMgr.cfg_match_typeCache.get(matchType);
        let isAttritionWar = cfgMatch.turn_type == FightTurnType.ATTRITION_WAR;
        let leftIdx = turnIdx, rightIdx = turnIdx;
        if (isAttritionWar) {
            leftIdx = rightIdx = 0;
            for (let i = 0; i < turnIdx; ++i) {
                let trunInfo = this._fight_data.turns[i];
                if (trunInfo.turn_result == 1) {
                    rightIdx += 1;
                }
                else if (trunInfo.turn_result == 2) {
                    leftIdx += 1;
                }
            }
        }
        let trunInfo = this._fight_data.turns[turnIdx - 1];
        if (trunInfo) {
            let isLeft = trunInfo.turn_result == 2;
            let isShenMoTa = matchType == MatchConst.MATCH_TYPE_GHOST_TOWER;
            // let effName = isShenMoTa ? "2nd_team_tips_shenmota" : isLeft ? "2nd_team_tips_ownside" : "2nd_team_tips_enemy";
            let effName = isLeft ? LineUpUtil.getTeamLeftEff(matchType, leftIdx + 1) : LineUpUtil.getTeamRightEff(matchType, rightIdx + 1);
            this.ShowEffectCenter(effName, this.boxSecond, true);
            this.boxSecond.visible = true;
            this.boxSecond.alpha = 1;
            if (isShenMoTa) {
                Tween.to(this.boxSecond, { alpha: 0 }, 500, null, Handler.create(this, function () {
                    this.boxSecond.visible = false;
                }), 1000);
            }
            else {
                Tween.from(this.boxSecond, { x: isLeft ? -this.boxSecond.width : this.width }, 500, Ease.backOut, Handler.create(this, function () {
                    Tween.to(this.boxSecond, { alpha: 0 }, 500, null, Handler.create(this, function () {
                        this.boxSecond.visible = false;
                    }), 500);
                }));
            }
        }
    }


    private updateRound(roundIdx: number): void {
        if (!this._fight_data) {
            return;
        }
        this._roundIdx = roundIdx;
        let turnInfo = this._fight_data.turns[this._turnIdx];
        let roundInfo = turnInfo && turnInfo.rounds[this._roundIdx] || null;
        let round = roundInfo && roundInfo.round || this._roundIdx + 1;

        let fightTurn = this._fight_data.turns[this._turnIdx];
        this.roundLabel.text = round > 0 ? window.iLang.L2_DI_P0_ch04_P1_HUI_HE.il([round, fightTurn.max_round]) : window.iLang.L2_ZHAN_QIAN_ZHUN_BEI.il();

        let matchTypeCfg = ConfigManager.cfg_match_typeCache.get(this._fight_data.match_type);
        let decideRnd = fightTurn.hurt_round;
        let isDecideRnd = !!matchTypeCfg && decideRnd > 0 && round >= decideRnd;
        if (isDecideRnd) {
            let decideHarm = matchTypeCfg.decide_harm / 100;
            let decideAdd = matchTypeCfg.decide_add / 100;
            this.txtDeciding.text = this._decidingTxt.replace("{0}", (decideHarm + (round - decideRnd) * decideAdd).toString());
        }
        this.imgDeciding.visible = isDecideRnd;
      
        this.countDownRound(round);
        if (round == 1) {
            this.updateFightItem();
        }
    }


    private countDownRound(rnd: number) {
        if (rnd < 0) return;

        if (this._leftGWItem.visible) this._leftGWItem.setRound(rnd);
        this._leftQiMouItems.forEach(item => item.updateRound(rnd));
    }


    private checkIsHasQiMou(wpns: p_fight_wpn[]): boolean {
        return !!wpns.find(wpn => 1101 <= wpn.wpn_sn && wpn.wpn_sn <= 1200 || 2101 <= wpn.wpn_sn && wpn.wpn_sn <= 2200);
    }

    private setWeaponData(wpns: p_fight_wpn[], qiMouBox: Box = null): void {
        if (qiMouBox) {
            qiMouBox.visible = this.checkIsHasQiMou(wpns)
        }
        for (let i = 0; i < wpns.length; ++i) {
            let wpnData = wpns[i];
            if (!wpnData) {
                continue;
            }

            let sn = wpnData.wpn_sn % 1000;
            if (sn < 100) {//神器 1 ~ 100
                if (this._leftGWItem) {
                    this._leftGWItem.visible = true;
                    this._leftGWItem.setWeaponData(wpnData);
                    this._leftGWItem.setRound(this._roundIdx + 1);
                    this._fightItemMap.set(wpnData.wpn_sn, this._leftGWItem);
                }
            }
            else {//龙魂 101 ~ 200
                let index = sn % 100 - 1;
                let qimouItem = this._leftQiMouItems[index];
                if (qimouItem) {
                    qimouItem.visible = true;
                    qimouItem.setData(wpnData);
                    qimouItem.updateRound(this._roundIdx + 1);
                    this._fightItemMap.set(wpnData.wpn_sn, qimouItem);
                }
            }
        }
        
    }

    private updateFightItem(): void {
        if (!this._fight_data) {
            return;
        }
        let turnInfo = this._fight_data.turns[this._turnIdx];
        let roundInfo = turnInfo && turnInfo.rounds[this._roundIdx] || null;
        let round = roundInfo && roundInfo.round || this._roundIdx + 1;
        
        let stepInfos = roundInfo && roundInfo.steps || [];
        for (let i = 0; i < this._stepIdx; ++i) {
            let stepData = stepInfos[i];
            let item = this._fightItemMap.get(stepData.src_actor_sn);
            if (item) {
                if (item instanceof FightGodWeaponItem) {
                    item.setRound(round);
                }
                else {
                    item.updateRound(round, true);
                }
            }
        }
    }

    private updateRate(rate: number): void {
        if (rate == 0) {
            rate = MiscConst.fight_default_play_speed;
        }
        this._rate = rate > 0 ? rate : 1;
        this.timer.callLater(this, () => {
            // this.ratebtn.skin = "fight/rate" + this._rate + ".png";
            this.ratebtn.label = "X" + this._rate;
        });

        if (this.skillNameEff) {
            this.skillNameEff.timeScale = this.playSpeed;
        }

        if (this._qiMouEffView) {
            this._qiMouEffView.playBackRate(this.playSpeed);
        }
        if (this._sceneEffView) {
            this._sceneEffView.playBackRate(this.playSpeed);
        }

        this.dispatchEvent(ModuleCommand.FIGHT_RATE, this._rate);
    }

    private skillNameEff: CCAnimation;
    private showSkillName(actor_sn: number, actor_type: number, type_id: number, skill_id: number, icon: string = ""): void {
        this.skillNameEff = FightUtil.showSkillName(this.skillBgImg, this.iconImg, this.skillNameLabel, this.skillNameEff, {
            actor_type: actor_type,
            type_id: type_id,
            skill_id: skill_id,
            icon: icon,
            isMyTeam: RoleMgr.isMyTeam(actor_sn),
            playSpeed: this.playSpeed,
        });
    }

    private hideSkillName(): void {
        this.skillBgImg.visible = false;
        if (this.skillNameEff) {
            this.skillNameEff.stop();
        }
    }

    addClick() {
        this.addOnClick(this, this.ratebtn, this.onClickRateBtn);
    }

    private onClickRateBtn() {
        let rate = (this._rate % MiscConst.fight_play_speed_max_rate) + 1;
        let fight_toc = FightDataCenter.FIGHT_INFO_CACHE.get(MatchConst.MATCH_TYPE_MAIN_BATTLE);
        if (fight_toc.replay_type != FightReplay.REVIEW) {
            if (!VipTeQuanUtil.checkFightRate(rate, true)) {
                rate = 1;
            }
        }

        this._rate = rate;
        FightDataCenter.instance.m_fight_set_speed_tos(this._rate);
    }

    private get playSpeed(): number {
        return MiscConst.getNumberValue("fight_play_speed_rate_" + this._rate, 1);
    }

    onOpen(param: any = null) {
        this._fight_data = FightDataCenter.FIGHT_INFO_CACHE.get(MatchConst.MATCH_TYPE_MAIN_BATTLE);

        if (param) {
            this.updateTurnInfo(param.trunIndex);
            this.updateRound(Math.max(0, param.roundIndex));
        }
        else {
            this.updateTurnInfo(0);
            this.updateRound(0);
        }
        this.updateFightItem();

        if (this._fight_data) {
            this.updateRate(this._fight_data.play_speed);
        }
    }

    private onDropBoxEff(): void {
        if (GuaJiDataCenter.instance.fight_finish_data && GuaJiDataCenter.instance.fight_finish_data.is_success) {
            let dropBoxList = [];
            for (let pitem of GuaJiDataCenter.instance.fight_finish_data.gains) {
                if(ConfigManager.cfg_main_battle_boxByItemIdCache.get(pitem.type_id) != undefined){
                    dropBoxList.push(ConfigManager.cfg_main_battle_boxByItemIdCache.get(pitem.type_id));
                }
            }
            if(dropBoxList.length > 0){
                dropBoxList.sort((cfg1, cfg2) => {
                    return cfg2.box - cfg1.box
                });
                let boxCfg = dropBoxList[0];
                this._boxSk = SkeletonManager.ins.createSkeleton(boxCfg.effName, ESkeletonType.UI_EFFECT, {isLoop: false});
                this._boxSk.playAction("show up", false);
                this._boxSk.pos(0, 0);
                this._boxSk.scaleActor(0.5);
                this.effBox.addChild(this._boxSk);
                let _this = this;
                // this._boxSk.on(Event.STOPPED, this, this.OnCompleteHandler);
                this.timer.once(1200, this, function () {
                    let from = Point.TEMP.setTo(50, 50);
                    from = _this.effBox.localToGlobal(from);
                    _this.dispatchEvent(ModuleCommand.FLY_MAIN_BATTLE_BOX, [boxCfg.box, from.x, from.y]);
                });
                this.timer.once(1200, this, ()=> {

                    if (this._boxSk){
                        // this.effBox.removeChild(this._boxSk);
                        if (this._boxSk) {
                            this._boxSk.destroy();
                            this._boxSk = null;
                        }
                    }
                 
                });
            }
        }
    }

    /**总血量显示 */
    private fightTrigHurt(hurts: p_fight_hurt[], matchType: number): void {
        if (this._fight_data && matchType != this._fight_data.match_type) {
            return;
        }
        for (let hurtInfo of hurts) {
            if (!this.curHpMap.has(hurtInfo.dest_actor_sn)) continue;
            switch (hurtInfo.hurt_type) {
                case HurtType.HP:
                case HurtType.REVIVE:
                    this.curHpMap.set(hurtInfo.dest_actor_sn, hurtInfo.dest_val);
                    break;

                case HurtType.HP_LIMIT:
                    this.curHpMap.set(hurtInfo.dest_actor_sn, hurtInfo.hurt_val);
                    this.maxHpMap.set(hurtInfo.dest_actor_sn, hurtInfo.dest_val);
                    break;
            }
        }
        this.refreshBloodBar();
    }

    
    private addStartAnimation(): void {
        FightStartAnimation.instance.pos(0, this.y + 120 - this.offsetY);
        
        FightStartAnimation.instance.Show();
        FightStartAnimation.instance.isRun = false;
        this.addChildAt(FightStartAnimation.instance, 0);
    }

    /**龙魂特效 start */
    private _qiMouEffView: FightQiMouEffectView;
    public get qiMouEffView(): FightQiMouEffectView {
        if (this._qiMouEffView == null) {
            this._qiMouEffView = new FightQiMouEffectView();
            this._qiMouEffView.zOrder = 9999;
            this._qiMouEffView.pos(0, 0);
            // this._qiMouEffView.playBackRate(this.playSpeed);
            this._qiMouEffView.visible = false;
            this.addChild(this._qiMouEffView);
        }
        return this._qiMouEffView;
    }

    private showQiMouEff(effectName: string, typeId: number, skillId: number, isLeft: boolean): void {
        let cfg = CfgCacheMapMgr.cfg_ingenious_planTypeIdCache.get(typeId);
        if (cfg) {
            let vo = new FightDuoPingVo();
            vo.effName = effectName;
            vo.qiMouName = skillId.toString();
            vo.star = cfg.star;
            vo.isLeft = isLeft;
            this.qiMouEffView.setData(vo);
            this.qiMouEffView.play();
        }
    }
    /**龙魂特效 end */

    /**场景装扮特效 start */
    private _sceneEffView: FightSceneEffectView;
    public get sceneEffView(): FightSceneEffectView {
        if (this._sceneEffView == null) {
            this._sceneEffView = new FightSceneEffectView();
            this._sceneEffView.zOrder = 9999;
            this._sceneEffView.pos(0, 0);
            // this._sceneEffView.playBackRate(this.playSpeed);
            this._sceneEffView.visible = false;
            this.addChild(this._sceneEffView);
        }
        return this._sceneEffView;
    }

    private showSceneEff(vo: FightDuoPingVo): void {
        if (vo.matchType == MatchConst.MATCH_TYPE_MAIN_BATTLE) {
            this.sceneEffView.addData(vo);
        }
        // this.sceneEffView.play();
    }
}