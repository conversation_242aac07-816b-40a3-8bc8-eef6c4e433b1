{"skeleton": {"hash": "1/9925+UxnuDjFhUZM2yHoKuGmo", "spine": "3.8.75", "x": -364.73, "y": -10.48, "width": 572, "height": 871, "images": "./imgae/", "audio": "G:/拟实网络/切片/立绘/N021/imgae"}, "bones": [{"name": "root"}, {"name": "qugan", "parent": "root", "x": 61.93, "y": 404.21}, {"name": "qugan3", "parent": "qugan", "length": 39.21, "rotation": 94.35, "x": -0.42, "y": 15.3}, {"name": "qugan2", "parent": "qugan3", "length": 78.14, "rotation": 8.85, "x": 38.85, "y": 0.88}, {"name": "qun4", "parent": "qugan", "length": 27.66, "rotation": -78.79, "x": 20.8, "y": -13.1}, {"name": "qun5", "parent": "qun4", "length": 30.83, "rotation": -6.44, "x": 27.66}, {"name": "qun6", "parent": "qun5", "length": 37.8, "rotation": 0.68, "x": 30.83}, {"name": "qun7", "parent": "qun6", "length": 39.97, "rotation": -3.24, "x": 37.8}, {"name": "qun8", "parent": "qugan", "length": 23.3, "rotation": -112.62, "x": -7.19, "y": -7.87}, {"name": "qun9", "parent": "qun8", "length": 33.62, "rotation": -0.23, "x": 23.3}, {"name": "qun10", "parent": "qun9", "length": 31.91, "rotation": 2.68, "x": 33.62}, {"name": "qun11", "parent": "qun10", "length": 38.75, "rotation": 4.86, "x": 31.91}, {"name": "qun3", "parent": "qugan", "length": 22.46, "rotation": -65.77, "x": 45.11, "y": -6.02}, {"name": "qun12", "parent": "qun3", "length": 29.72, "rotation": -1.42, "x": 22.46}, {"name": "qun13", "parent": "qun12", "length": 30.68, "rotation": 3.97, "x": 29.72}, {"name": "qun14", "parent": "qun13", "length": 38.51, "rotation": 4.9, "x": 30.68}, {"name": "zuojiao1", "parent": "qugan", "length": 185.21, "rotation": -106.3, "x": -42.36, "y": -16.94}, {"name": "zuojiao2", "parent": "zuojiao1", "length": 168.99, "rotation": -2.01, "x": 184.15, "y": -1.48}, {"name": "youjiao1", "parent": "qugan", "length": 187.29, "rotation": -89.14, "x": 8.62, "y": -15.38}, {"name": "youjiao2", "parent": "youjiao1", "length": 164.94, "rotation": -2.02, "x": 192.68, "y": -0.08}, {"name": "qun1", "parent": "qugan", "length": 77.43, "rotation": -136.91, "x": -87.98, "y": -22.81}, {"name": "qun2", "parent": "qun1", "length": 79.8, "rotation": 11.52, "x": 77.43}, {"name": "qun15", "parent": "qun2", "length": 75.65, "rotation": 6.55, "x": 79.37, "y": -1.63}, {"name": "qun16", "parent": "qun15", "length": 115.42, "rotation": 8.49, "x": 75.65}, {"name": "qun17", "parent": "qugan", "length": 73.87, "rotation": -69.78, "x": 58.17, "y": -44.26}, {"name": "qun18", "parent": "qun17", "length": 73.69, "rotation": -9.29, "x": 73.87}, {"name": "qun19", "parent": "qun18", "length": 69.39, "rotation": -2.88, "x": 73.69}, {"name": "qun20", "parent": "qun19", "length": 76.41, "rotation": -2.12, "x": 69.39}, {"name": "qun21", "parent": "qugan", "length": 57.84, "rotation": -102.14, "x": -3.85, "y": -27.84}, {"name": "qun22", "parent": "qun21", "length": 59.98, "rotation": 1.62, "x": 57.84}, {"name": "qun23", "parent": "qun22", "length": 62, "rotation": 2.62, "x": 59.98}, {"name": "qun24", "parent": "qun23", "length": 60.71, "rotation": 0.41, "x": 62}, {"name": "hdj3", "parent": "qugan2", "x": 66.43, "y": -10.24}, {"name": "hdj", "parent": "hdj3", "length": 17.14, "rotation": 67.44, "x": 8.18, "y": 14.84}, {"name": "hdj2", "parent": "hdj", "length": 31.76, "rotation": 14.39, "x": 17.14}, {"name": "hdj5", "parent": "hdj3", "length": 17.93, "rotation": -100.66, "x": -0.87, "y": -13.29}, {"name": "hdj6", "parent": "hdj5", "length": 20.16, "rotation": 1.98, "x": 17.93}, {"name": "hdj1", "parent": "hdj3", "length": 21.36, "rotation": 145.49, "x": 0.65, "y": 14.97}, {"name": "hdj4", "parent": "hdj1", "length": 14.68, "rotation": 8.78, "x": 21.36}, {"name": "hdj7", "parent": "hdj4", "length": 18.32, "rotation": 10.66, "x": 14.68}, {"name": "hdj9", "parent": "hdj3", "length": 16.2, "rotation": -131.03, "x": -5.46, "y": -3.22}, {"name": "hdj10", "parent": "hdj9", "length": 18.01, "rotation": -17.18, "x": 16.2}, {"name": "hdj11", "parent": "hdj10", "length": 25.46, "rotation": -20.04, "x": 17.59, "y": 0.14}, {"name": "zuoshou3", "parent": "qugan2", "length": 101.46, "rotation": 144.94, "x": 99.39, "y": 66.95}, {"name": "<PERSON><PERSON><PERSON>", "parent": "zuoshou3", "length": 102.38, "rotation": 24.26, "x": 106.87, "y": -7.61}, {"name": "zuoshou1", "parent": "<PERSON><PERSON><PERSON>", "length": 43.61, "rotation": 23.6, "x": 99.6, "y": 2.03}, {"name": "youshopu1", "parent": "qugan2", "length": 68.37, "rotation": -162.52, "x": 41.33, "y": -24.88}, {"name": "yopushou2", "parent": "youshopu1", "length": 114.59, "rotation": -4.98, "x": 72.47, "y": 0.35}, {"name": "yopushou3", "parent": "yopushou2", "length": 43.58, "rotation": -46.93, "x": 114.59}, {"name": "tou", "parent": "qugan2", "length": 87.77, "rotation": -14.35, "x": 107.78, "y": 14.83}, {"name": "eye", "parent": "tou", "x": 50.68, "y": -33.61, "color": "abe323ff"}, {"name": "fa1", "parent": "tou", "x": 153.07, "y": -52.44}, {"name": "fa3", "parent": "fa1", "length": 30.83, "rotation": 125.41, "x": 33.51, "y": 18.87}, {"name": "fa4", "parent": "fa3", "length": 36.3, "rotation": 7.29, "x": 30.83}, {"name": "fa5", "parent": "fa4", "length": 37.8, "rotation": 11.57, "x": 36.3}, {"name": "fa6", "parent": "fa5", "length": 38.76, "rotation": 6.97, "x": 37.8}, {"name": "fa7", "parent": "tou", "length": 17.39, "rotation": -24.62, "x": 168.31, "y": -84.91}, {"name": "fa8", "parent": "fa7", "length": 20.63, "rotation": 11.37, "x": 17.39}, {"name": "fa9", "parent": "fa8", "length": 22.74, "rotation": 44.31, "x": 20.63}, {"name": "fa2", "parent": "tou", "length": 26.24, "rotation": -140.83, "x": 134.94, "y": -93.53}, {"name": "fa10", "parent": "fa2", "length": 31.75, "rotation": -23.53, "x": 26.24}, {"name": "fa11", "parent": "fa10", "length": 25.75, "rotation": -23.38, "x": 30.92, "y": -0.06}, {"name": "fa12", "parent": "fa11", "length": 27.14, "rotation": -26.98, "x": 25.75}, {"name": "fa13", "parent": "tou", "length": 32.32, "rotation": -167.43, "x": 18.8, "y": -69.54}, {"name": "fa14", "parent": "fa13", "length": 25.42, "rotation": -0.54, "x": 32.32}, {"name": "fa15", "parent": "fa14", "length": 26.36, "rotation": -5.31, "x": 25.42}, {"name": "fa16", "parent": "tou", "length": 30.47, "rotation": -178.34, "x": 74.27, "y": 57.02}, {"name": "fa17", "parent": "fa16", "length": 23.84, "rotation": 25.18, "x": 30.47}, {"name": "fa18", "parent": "fa17", "length": 22.36, "rotation": 15.37, "x": 23.84}, {"name": "fa19", "parent": "fa18", "length": 24.09, "rotation": -14.2, "x": 21.95, "y": 0.36}, {"name": "tou3", "parent": "tou", "length": 21.08, "rotation": -55.88, "x": 230.73, "y": 3.19}, {"name": "tou4", "parent": "tou3", "length": 18.76, "rotation": 1.13, "x": 21.08}, {"name": "tou5", "parent": "tou4", "length": 14.45, "rotation": 0.12, "x": 18.76}, {"name": "tou6", "parent": "tou", "length": 24.17, "rotation": 67.55, "x": 230.9, "y": 28.27}, {"name": "tou7", "parent": "tou6", "length": 18.55, "rotation": -20.46, "x": 24.17}, {"name": "tou8", "parent": "tou7", "length": 23.17, "rotation": -18.9, "x": 18.55}, {"name": "fa20", "parent": "tou", "x": 237.19, "y": 84.01}, {"name": "fa22", "parent": "fa20", "length": 94.45, "rotation": 162.15, "x": -5.5, "y": 34.17}, {"name": "fa23", "parent": "fa22", "length": 104, "rotation": -3.78, "x": 94.45}, {"name": "fa24", "parent": "fa23", "length": 102.8, "rotation": -4.34, "x": 104}, {"name": "fa25", "parent": "fa24", "length": 81.19, "rotation": 0.32, "x": 102.8}, {"name": "fa26", "parent": "fa25", "length": 100.18, "rotation": 0.79, "x": 81.19}, {"name": "target2", "parent": "root", "x": -89.27, "y": 46.19, "color": "ff3f00ff"}, {"name": "target1", "parent": "root", "x": 68.62, "y": 31.85, "color": "ff3f00ff"}], "slots": [{"name": "fa11", "bone": "fa22", "attachment": "fa11"}, {"name": "fa10", "bone": "fa13", "attachment": "fa10"}, {"name": "fa9", "bone": "tou", "attachment": "fa9"}, {"name": "yopushou2", "bone": "yopushou2", "attachment": "yopushou2"}, {"name": "qun8", "bone": "qun21", "attachment": "qun8"}, {"name": "qun7", "bone": "qun17", "attachment": "qun7"}, {"name": "youshopu1", "bone": "youshopu1", "attachment": "youshopu1"}, {"name": "zuojiao1", "bone": "zuojiao1", "attachment": "zuojiao1"}, {"name": "youjiao1", "bone": "youjiao1", "attachment": "youjiao1"}, {"name": "qundi", "bone": "root", "attachment": "qundi"}, {"name": "qun5", "bone": "qun8", "attachment": "qun5"}, {"name": "qun4", "bone": "qun4", "attachment": "qun4"}, {"name": "qun3", "bone": "qun3", "attachment": "qun3"}, {"name": "qugan", "bone": "qugan2", "attachment": "qugan"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "root", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "root", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "tou", "bone": "tou6", "attachment": "tou"}, {"name": "eye", "bone": "eye", "attachment": "eye"}, {"name": "fa5", "bone": "fa7", "attachment": "fa5"}, {"name": "zuoshou3", "bone": "zuoshou3", "attachment": "zuoshou3"}, {"name": "hdj2", "bone": "hdj9", "attachment": "hdj2"}, {"name": "hdj1", "bone": "hdj1", "attachment": "hdj1"}, {"name": "hdj", "bone": "hdj5", "attachment": "hdj"}, {"name": "fa3", "bone": "fa16", "attachment": "fa3"}, {"name": "qun1", "bone": "qun1", "attachment": "qun1"}, {"name": "qunjia", "bone": "qugan", "attachment": "qunjia"}, {"name": "zuoshou1", "bone": "zuoshou1", "attachment": "zuoshou1"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "fa2", "bone": "fa2", "attachment": "fa2"}, {"name": "fa1", "bone": "fa3", "attachment": "fa1"}], "ik": [{"name": "target", "bones": ["zuojiao1", "zuojiao2"], "target": "target2", "stretch": true}, {"name": "target1", "order": 1, "bones": ["youjiao1", "youjiao2"], "target": "target1", "bendPositive": false}], "transform": [{"name": "eye", "order": 2, "bones": ["fa20"], "target": "eye", "x": 186.51, "y": 117.61, "rotateMix": 0, "translateMix": -2, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"zuojiao": {"zuojiao": {"type": "mesh", "uvs": [0.17718, 0.27403, 0.24141, 0.17319, 0.32788, 0.07965, 0.35259, 0.0189, 0.40941, 0.03105, 0.54035, 0.00068, 0.68612, 0, 0.90106, 0.01404, 0.98506, 0.07114, 1, 0.14646, 0.98753, 0.22786, 0.87141, 0.30048, 0.80965, 0.37701, 0.75529, 0.44262, 0.63918, 0.51551, 0.54407, 0.58425, 0.50454, 0.64013, 0.48725, 0.70695, 0.4749, 0.77863, 0.49466, 0.81872, 0.45075, 0.87223, 0.43784, 0.88797, 0.40325, 0.89769, 0.41313, 0.99609, 0, 1, 0, 0.91834, 0.02772, 0.8746, 0.02338, 0.85205, 0.01043, 0.7847, 0.06478, 0.73854, 0.11419, 0.6763, 0.14631, 0.59004, 0.1636, 0.50865, 0.17102, 0.42482, 0.16855, 0.34099, 0.14038, 0.8128, 0.26391, 0.80296, 0.13332, 0.77924, 0.25568, 0.76652, 0.38274, 0.80123], "triangles": [36, 39, 21, 35, 37, 36, 26, 27, 35, 35, 36, 26, 22, 36, 21, 22, 25, 36, 26, 36, 25, 23, 25, 22, 23, 24, 25, 21, 39, 20, 20, 39, 19, 37, 27, 28, 37, 35, 27, 39, 18, 19, 37, 38, 36, 36, 38, 39, 18, 39, 17, 28, 29, 37, 37, 29, 38, 17, 39, 38, 29, 30, 38, 38, 30, 17, 17, 30, 16, 30, 31, 16, 16, 31, 15, 31, 32, 15, 15, 32, 14, 13, 14, 33, 14, 32, 33, 12, 13, 34, 12, 34, 0, 11, 12, 0, 11, 0, 1, 11, 1, 2, 11, 2, 4, 6, 11, 4, 13, 33, 34, 6, 4, 5, 10, 11, 9, 8, 6, 7, 6, 9, 11, 8, 9, 6, 2, 3, 4], "vertices": [1, 17, 63.34, -45.11, 1, 1, 17, 37.77, -45.25, 1, 1, 17, 13.08, -42.33, 1, 1, 17, -1.82, -43.99, 1, 1, 17, -1.08, -36.66, 1, 1, 17, -12.82, -24.05, 1, 1, 17, -18.25, -7.57, 1, 1, 17, -22.79, 17.83, 1, 1, 17, -12.66, 31.55, 1, 1, 17, 4.16, 38.79, 1, 1, 17, 23.38, 43.36, 1, 1, 17, 44.32, 35.54, 1, 1, 17, 64.2, 34.17, 1, 1, 17, 81.29, 32.83, 1, 1, 17, 102.3, 25.03, 1, 1, 17, 121.59, 19.31, 1, 1, 17, 135.9, 18.94, 1, 1, 17, 151.93, 21.89, 1, 1, 17, 168.9, 25.77, 1, 1, 17, 177.43, 30.96, 1, 2, 17, 191.36, 29.92, 0.75, 82, 23.18, -25.75, 0.25, 2, 17, 195.45, 29.61, 0.25, 82, 21.65, -29.56, 0.75, 1, 82, 17.53, -31.91, 1, 1, 82, 18.71, -55.73, 1, 1, 82, -30.46, -56.67, 1, 1, 82, -30.46, -36.91, 1, 2, 17, 207.21, -17.87, 0.25, 82, -27.16, -26.33, 0.75, 2, 17, 202.17, -20.02, 0.75, 82, -27.67, -20.87, 0.25, 1, 17, 187.11, -26.44, 1, 1, 17, 174.5, -23.68, 1, 1, 17, 158.36, -22.65, 1, 1, 17, 137.31, -25.36, 1, 1, 17, 117.92, -29.39, 1, 1, 17, 98.33, -34.71, 1, 1, 17, 79.09, -41.16, 1, 2, 17, 188.88, -9.64, 0.25, 82, -13.75, -11.37, 0.75, 2, 17, 182.15, 3.64, 0.25, 82, 0.95, -8.99, 0.75, 2, 17, 181.4, -12.91, 0.75, 82, -14.59, -3.25, 0.25, 2, 17, 174.04, 0.02, 0.75, 82, -0.03, -0.17, 0.25, 2, 17, 177.45, 16.98, 0.75, 82, 15.09, -8.57, 0.25], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68, 52, 70, 70, 72, 72, 42, 52, 54, 54, 56, 54, 74, 74, 76, 76, 78, 38, 40, 40, 42, 78, 40], "width": 119, "height": 242}}, "tou": {"tou": {"type": "mesh", "uvs": [0.28285, 0.81876, 0.32672, 0.86059, 0.34823, 0.87678, 0.36576, 0.91463, 0.371, 0.94031, 0.37296, 0.98603, 0.40571, 0.98346, 0.45222, 0.98346, 0.52034, 0.99322, 0.59305, 1, 0.58322, 0.97524, 0.57471, 0.95521, 0.63694, 0.95059, 0.72544, 0.92513, 0.7916, 0.9015, 0.8047, 0.85886, 0.81857, 0.80988, 0.84419, 0.76551, 0.86335, 0.73399, 0.89908, 0.70234, 0.91862, 0.65493, 0.91927, 0.59625, 0.91667, 0.55237, 0.8991, 0.52379, 0.86332, 0.52379, 0.82741, 0.53114, 0.80031, 0.5259, 0.83223, 0.50698, 0.87837, 0.47148, 0.92032, 0.43655, 0.94741, 0.42404, 0.99047, 0.44412, 0.99938, 0.41938, 0.99893, 0.34311, 0.98004, 0.31559, 0.96385, 0.30289, 0.93162, 0.29097, 0.89473, 0.27757, 0.85245, 0.27228, 0.82186, 0.24114, 0.79172, 0.21363, 0.75574, 0.1914, 0.70854, 0.17843, 0.65764, 0.16548, 0.63815, 0.16505, 0.67754, 0.1415, 0.70629, 0.11462, 0.72064, 0.09275, 0.73418, 0.06269, 0.73634, 0.03339, 0.73039, 0.006, 0.70683, 0.01704, 0.67933, 0.02884, 0.63304, 0.04031, 0.59594, 0.0471, 0.56121, 0.05318, 0.53413, 0.06741, 0.52059, 0.07909, 0.50682, 0.10152, 0.49215, 0.12166, 0.4894, 0.12972, 0.47533, 0.11429, 0.44951, 0.0896, 0.41626, 0.06907, 0.37314, 0.04879, 0.32539, 0.02799, 0.28117, 0.01134, 0.24156, 0, 0.21715, 0, 0.21224, 0.02063, 0.21292, 0.04595, 0.21894, 0.08063, 0.23379, 0.10754, 0.25431, 0.13694, 0.26775, 0.14804, 0.25157, 0.15645, 0.22328, 0.17087, 0.20135, 0.18613, 0.17588, 0.20555, 0.16726, 0.22922, 0.12049, 0.26046, 0.07442, 0.31755, 0.0389, 0.40063, 0.01126, 0.44615, 0.00928, 0.54705, 0.00784, 0.59198, 0.02933, 0.64366, 0.07321, 0.70927, 0.12621, 0.76262, 0.1792, 0.79576, 0.2458, 0.81317, 0.31097, 0.78065, 0.3145, 0.67481, 0.34978, 0.5745, 0.42564, 0.50601, 0.49533, 0.45966, 0.56678, 0.44652, 0.66998, 0.4652, 0.75555, 0.51708, 0.44389, 0.59498, 0.45945, 0.57852, 0.48721, 0.58649, 0.55015, 0.62205, 0.61919, 0.65125, 0.68282, 0.6815, 0.67552, 0.69537, 0.6598, 0.70123, 0.63386, 0.68921, 0.59377, 0.67472, 0.55879, 0.65531, 0.525, 0.63897, 0.49631, 0.62233, 0.47115, 0.60476, 0.77325, 0.71769, 0.80543, 0.69865, 0.84522, 0.6858, 0.89174, 0.66721, 0.89818, 0.67731, 0.8894, 0.69429, 0.85985, 0.69956, 0.8227, 0.70828, 0.7674, 0.72458, 0.36966, 0.62867, 0.44334, 0.62766, 0.50215, 0.64844, 0.57712, 0.67631, 0.59651, 0.70673, 0.5991, 0.74626, 0.58746, 0.77262, 0.54222, 0.78884, 0.47178, 0.78884, 0.41426, 0.77211, 0.37354, 0.72497, 0.35868, 0.68544, 0.3548, 0.65604, 0.70829, 0.80026, 0.71013, 0.78559, 0.73727, 0.75222, 0.78019, 0.72891, 0.83448, 0.71885, 0.86382, 0.71827, 0.84823, 0.73431, 0.83496, 0.76045, 0.81575, 0.79087, 0.80488, 0.81531, 0.78313, 0.83322, 0.75776, 0.82611, 0.71717, 0.81503, 0.60281, 0.84885, 0.6352, 0.8545, 0.68872, 0.86402, 0.71481, 0.84744, 0.70131, 0.82028, 0.65859, 0.80723, 0.61586, 0.82451, 0.55828, 0.87037, 0.59067, 0.87037, 0.64464, 0.87002, 0.68287, 0.88095, 0.67118, 0.89682, 0.65184, 0.90917, 0.59697, 0.90317, 0.56143, 0.89153, 0.35784, 0.78019, 0.42135, 0.81619, 0.51567, 0.82704, 0.74823, 0.85097, 0.78093, 0.8564, 0.37841, 0.83568, 0.47776, 0.86971, 0.4262, 0.88253, 0.72694, 0.87295, 0.75461, 0.87985, 0.72002, 0.90352, 0.58075, 0.93014], "triangles": [167, 145, 15, 174, 162, 161, 159, 158, 173, 171, 151, 166, 167, 166, 145, 158, 150, 171, 155, 165, 148, 150, 151, 171, 158, 157, 150, 156, 149, 157, 156, 155, 148, 151, 150, 152, 148, 154, 149, 150, 149, 152, 149, 153, 152, 149, 154, 153, 166, 151, 146, 148, 165, 129, 137, 106, 105, 137, 105, 121, 106, 126, 107, 138, 121, 120, 139, 120, 119, 140, 119, 118, 126, 108, 107, 124, 110, 109, 125, 109, 108, 123, 112, 111, 124, 111, 110, 123, 99, 112, 143, 144, 146, 135, 136, 147, 146, 137, 143, 137, 146, 136, 147, 136, 146, 127, 136, 153, 137, 138, 143, 143, 138, 142, 127, 106, 136, 138, 139, 142, 142, 139, 141, 140, 141, 139, 165, 130, 129, 129, 128, 154, 164, 131, 130, 153, 128, 127, 163, 132, 131, 132, 92, 133, 106, 127, 126, 125, 108, 126, 92, 134, 133, 125, 124, 109, 122, 134, 92, 124, 123, 111, 123, 122, 99, 128, 129, 127, 129, 126, 127, 130, 124, 125, 130, 131, 124, 126, 129, 130, 126, 130, 125, 124, 132, 123, 124, 131, 132, 132, 133, 123, 122, 123, 134, 123, 133, 134, 151, 147, 146, 145, 146, 144, 152, 135, 147, 16, 144, 143, 135, 153, 136, 143, 142, 17, 136, 106, 137, 17, 142, 141, 137, 121, 138, 18, 141, 140, 138, 120, 139, 140, 139, 119, 120, 113, 114, 120, 121, 113, 113, 121, 104, 140, 118, 19, 121, 105, 104, 113, 104, 114, 120, 115, 119, 120, 114, 115, 105, 106, 104, 119, 115, 118, 104, 106, 107, 117, 118, 116, 108, 103, 107, 107, 103, 104, 118, 115, 116, 108, 109, 103, 110, 102, 109, 109, 102, 103, 110, 111, 102, 99, 122, 93, 102, 111, 101, 111, 112, 101, 99, 100, 112, 112, 100, 101, 118, 117, 19, 26, 115, 114, 114, 104, 26, 116, 115, 21, 104, 103, 98, 117, 116, 20, 102, 101, 96, 103, 102, 97, 99, 93, 100, 101, 100, 94, 154, 148, 129, 151, 152, 147, 154, 128, 153, 152, 153, 135, 156, 148, 149, 157, 149, 150, 159, 160, 157, 162, 156, 161, 160, 161, 157, 161, 156, 157, 159, 157, 158, 162, 155, 156, 15, 145, 144, 166, 146, 145, 173, 158, 171, 165, 164, 130, 163, 131, 164, 132, 163, 91, 8, 10, 9, 8, 11, 10, 8, 162, 11, 8, 7, 162, 5, 4, 6, 162, 169, 155, 162, 7, 169, 7, 4, 170, 7, 170, 169, 7, 6, 4, 11, 174, 12, 11, 162, 174, 173, 13, 159, 159, 13, 160, 160, 13, 12, 174, 161, 12, 12, 161, 160, 4, 3, 170, 14, 173, 172, 14, 13, 173, 3, 2, 170, 172, 167, 14, 14, 167, 15, 2, 168, 170, 168, 164, 170, 170, 164, 169, 2, 1, 168, 169, 165, 155, 169, 164, 165, 168, 1, 91, 168, 91, 163, 0, 91, 1, 15, 144, 16, 168, 163, 164, 0, 90, 91, 90, 89, 91, 16, 143, 17, 91, 89, 92, 92, 89, 88, 92, 132, 91, 17, 141, 18, 88, 87, 92, 18, 140, 19, 87, 86, 92, 19, 117, 20, 115, 26, 25, 104, 98, 26, 21, 115, 25, 20, 116, 21, 22, 24, 23, 21, 25, 22, 92, 86, 93, 122, 92, 93, 24, 22, 25, 98, 103, 97, 86, 85, 93, 102, 96, 97, 96, 101, 95, 85, 84, 93, 95, 101, 94, 93, 94, 100, 84, 83, 93, 83, 82, 93, 93, 82, 94, 94, 82, 81, 94, 81, 80, 94, 80, 79, 77, 94, 79, 77, 79, 78, 75, 94, 77, 75, 77, 76, 95, 94, 75, 74, 95, 75, 74, 64, 63, 26, 98, 27, 98, 97, 27, 27, 97, 28, 95, 74, 60, 62, 61, 74, 62, 74, 63, 60, 74, 61, 97, 38, 28, 29, 38, 37, 29, 28, 38, 97, 39, 38, 97, 40, 39, 40, 42, 41, 40, 97, 42, 97, 96, 42, 95, 60, 96, 42, 44, 43, 42, 96, 44, 96, 60, 44, 44, 60, 59, 44, 59, 58, 44, 58, 57, 44, 57, 56, 44, 55, 54, 55, 44, 56, 31, 30, 32, 29, 36, 30, 29, 37, 36, 30, 33, 32, 30, 34, 33, 34, 36, 35, 34, 30, 36, 44, 54, 45, 45, 54, 53, 74, 73, 64, 45, 53, 46, 64, 72, 65, 64, 73, 72, 46, 53, 47, 47, 53, 52, 65, 71, 66, 65, 72, 71, 47, 52, 48, 66, 71, 70, 66, 70, 67, 68, 67, 69, 48, 52, 49, 49, 52, 51, 67, 70, 69, 51, 50, 49, 173, 171, 172, 171, 166, 172, 172, 166, 167], "vertices": [1, 49, 31.6, 56.22, 1, 1, 49, 19.59, 45.93, 1, 1, 49, 14.96, 40.92, 1, 1, 49, 3.99, 36.68, 1, 1, 49, -3.49, 35.33, 1, 1, 49, -16.82, 34.62, 1, 1, 49, -15.92, 27.13, 1, 1, 49, -15.71, 16.48, 1, 1, 49, -18.25, 0.83, 1, 1, 49, -19.89, -15.86, 1, 1, 49, -12.71, -13.46, 1, 1, 49, -6.9, -11.4, 1, 1, 49, -5.27, -25.62, 1, 2, 49, 2.57, -45.73, 0.8, 50, -48.11, -12.13, 0.2, 2, 49, 9.77, -60.74, 0.8, 50, -40.91, -27.13, 0.2, 2, 49, 22.28, -63.49, 0.8, 50, -28.4, -29.88, 0.2, 2, 49, 36.64, -66.38, 0.8, 50, -14.04, -32.77, 0.2, 2, 49, 49.71, -71.99, 0.8, 50, -0.96, -38.38, 0.2, 2, 49, 59, -76.19, 0.8, 50, 8.32, -42.58, 0.2, 2, 49, 68.41, -84.19, 0.8, 50, 17.73, -50.58, 0.2, 2, 49, 82.34, -88.38, 0.8, 50, 31.66, -54.78, 0.2, 2, 49, 99.47, -88.19, 0.8, 50, 48.79, -54.58, 0.2, 1, 49, 112.27, -87.34, 1, 1, 49, 120.53, -83.15, 1, 1, 49, 120.37, -74.95, 1, 1, 49, 118.06, -66.78, 1, 1, 49, 119.46, -60.54, 1, 1, 49, 125.13, -67.74, 1, 1, 49, 135.71, -78.1, 1, 1, 49, 146.1, -87.5, 1, 1, 49, 149.88, -93.63, 1, 1, 49, 144.21, -103.6, 1, 1, 49, 151.47, -105.5, 1, 1, 49, 173.74, -104.95, 1, 1, 49, 181.68, -100.46, 1, 1, 49, 185.32, -96.68, 1, 1, 49, 188.65, -89.23, 1, 1, 49, 192.39, -80.71, 1, 1, 49, 193.75, -71, 1, 1, 49, 202.7, -63.81, 1, 1, 49, 210.59, -56.75, 1, 1, 49, 216.91, -48.38, 1, 1, 49, 220.48, -37.5, 1, 1, 49, 224.03, -25.77, 1, 1, 49, 224.07, -21.31, 1, 1, 49, 231.12, -30.19, 1, 1, 49, 239.1, -36.61, 1, 1, 49, 245.55, -39.77, 1, 1, 49, 254.39, -42.7, 1, 1, 49, 262.96, -43.02, 1, 1, 49, 270.92, -41.5, 1, 1, 49, 267.59, -36.17, 1, 1, 49, 264.02, -29.94, 1, 1, 49, 260.46, -19.41, 1, 1, 49, 258.31, -10.96, 1, 1, 49, 256.37, -3.04, 1, 1, 49, 252.1, 3.08, 1, 1, 49, 248.63, 6.11, 1, 1, 49, 242.01, 9.13, 1, 1, 49, 236.07, 12.37, 1, 1, 49, 233.7, 12.96, 1, 1, 49, 238.14, 16.27, 1, 1, 49, 245.23, 22.32, 1, 1, 49, 251.07, 30.06, 1, 1, 49, 256.8, 40.05, 1, 1, 49, 262.65, 51.1, 1, 1, 49, 267.31, 61.32, 1, 1, 49, 270.44, 70.46, 1, 1, 49, 270.33, 76.05, 1, 1, 49, 264.28, 77.05, 1, 1, 49, 256.89, 76.75, 1, 1, 49, 246.79, 75.17, 1, 1, 49, 239.01, 71.61, 1, 1, 49, 230.52, 66.74, 1, 1, 49, 227.34, 63.6, 1, 1, 49, 224.81, 67.25, 1, 1, 49, 220.47, 73.65, 1, 1, 49, 215.91, 78.58, 1, 1, 49, 210.13, 84.3, 1, 1, 49, 203.18, 86.13, 1, 1, 49, 193.84, 96.66, 1, 1, 49, 176.97, 106.87, 1, 1, 49, 152.55, 114.52, 1, 1, 49, 139.13, 120.58, 1, 1, 49, 109.67, 120.45, 1, 1, 49, 96.54, 120.51, 1, 1, 49, 81.56, 115.29, 1, 1, 49, 62.6, 104.86, 1, 1, 49, 47.27, 92.42, 1, 1, 49, 37.84, 80.09, 1, 1, 49, 33.06, 64.74, 1, 2, 49, 42.85, 50.01, 0.65, 50, -7.83, 83.61, 0.35, 1, 49, 73.77, 49.82, 1, 1, 49, 103.21, 42.33, 1, 1, 49, 123.55, 25.36, 1, 1, 49, 137.4, 9.67, 1, 1, 49, 141.57, -6.61, 1, 1, 49, 136.59, -30.35, 1, 1, 49, 121.83, -50.24, 1, 2, 49, 97.66, 20.66, 0.1, 50, 46.99, 54.27, 0.9, 2, 49, 102.54, 17.19, 0.1, 50, 51.86, 50.8, 0.9, 2, 49, 100.34, 10.79, 0.1, 50, 49.66, 44.4, 0.9, 2, 49, 90.25, -3.83, 0.1, 50, 39.57, 29.78, 0.9, 2, 49, 82.04, -19.81, 0.1, 50, 31.36, 13.8, 0.9, 2, 49, 73.5, -34.55, 0.1, 50, 22.82, -0.94, 0.9, 2, 49, 69.42, -32.96, 0.1, 50, 18.74, 0.65, 0.9, 2, 49, 67.63, -29.39, 0.1, 50, 16.96, 4.21, 0.9, 2, 49, 71.03, -23.39, 0.1, 50, 20.35, 10.22, 0.9, 2, 49, 75.07, -14.12, 0.1, 50, 24.39, 19.48, 0.9, 2, 49, 80.58, -6, 0.1, 50, 29.9, 27.61, 0.9, 2, 49, 85.19, 1.83, 0.1, 50, 34.52, 35.44, 0.9, 2, 49, 89.92, 8.5, 0.1, 50, 39.24, 42.1, 0.9, 2, 49, 94.94, 14.36, 0.1, 50, 44.26, 47.97, 0.9, 2, 49, 63.35, -55.47, 0.1, 50, 12.67, -21.86, 0.9, 2, 49, 69.06, -62.72, 0.1, 50, 18.38, -29.12, 0.9, 2, 49, 72.99, -71.76, 0.1, 50, 22.31, -38.15, 0.9, 2, 49, 78.63, -82.3, 0.1, 50, 27.95, -48.69, 0.9, 2, 49, 75.71, -83.83, 0.1, 50, 25.03, -50.23, 0.9, 2, 49, 70.71, -81.92, 0.1, 50, 20.03, -48.32, 0.9, 2, 49, 69.04, -75.19, 0.1, 50, 18.36, -41.58, 0.9, 2, 49, 66.32, -66.73, 0.1, 50, 15.64, -33.13, 0.9, 2, 49, 61.31, -54.17, 0.1, 50, 10.63, -20.56, 0.9, 1, 50, 36.81, 71.06, 1, 1, 50, 37.44, 54.2, 1, 1, 50, 31.65, 40.61, 1, 1, 50, 23.85, 23.29, 1, 1, 50, 15.06, 18.67, 1, 1, 50, 3.53, 17.85, 1, 1, 50, -4.21, 20.36, 1, 1, 50, -9.16, 30.62, 1, 1, 50, -9.48, 46.75, 1, 1, 50, -4.86, 60.02, 1, 1, 50, 8.71, 69.61, 1, 1, 50, 20.19, 73.25, 1, 1, 50, 28.75, 74.31, 1, 1, 50, -11.73, -7.47, 1, 1, 50, -7.44, -7.8, 1, 1, 50, 2.43, -13.82, 1, 1, 50, 9.43, -23.51, 1, 1, 50, 12.61, -35.88, 1, 1, 50, 12.92, -42.6, 1, 1, 50, 8.16, -39.12, 1, 1, 50, 0.47, -36.24, 1, 1, 50, -8.5, -32.02, 1, 1, 50, -15.68, -29.67, 1, 1, 50, -21.01, -24.8, 1, 1, 50, -19.05, -18.95, 1, 1, 50, -16, -9.59, 1, 1, 50, -26.4, 16.4, 1, 1, 50, -27.9, 8.95, 1, 1, 50, -30.44, -3.36, 1, 1, 50, -25.48, -9.24, 1, 1, 50, -17.61, -5.99, 1, 1, 50, -13.99, 3.87, 1, 1, 50, -19.24, 13.55, 1, 2, 49, 17.79, -7.14, 0.48, 50, -32.89, 26.47, 0.52, 2, 49, 17.94, -14.55, 0.48, 50, -32.74, 19.05, 0.52, 2, 49, 18.29, -26.91, 0.48, 50, -32.39, 6.7, 0.52, 2, 49, 15.27, -35.73, 0.48, 50, -35.41, -2.12, 0.52, 2, 49, 10.58, -33.14, 0.48, 50, -40.09, 0.46, 0.52, 2, 49, 6.89, -28.79, 0.48, 50, -43.79, 4.82, 0.52, 2, 49, 8.39, -16.19, 0.48, 50, -42.29, 17.42, 0.52, 2, 49, 11.63, -7.98, 0.48, 50, -39.05, 25.62, 0.52, 2, 49, 43.2, 39.28, 0.65, 50, -7.48, 72.89, 0.35, 2, 49, 32.98, 24.53, 0.65, 50, -17.7, 58.13, 0.35, 2, 49, 30.24, 2.87, 0.65, 50, -20.43, 36.47, 0.35, 2, 49, 24.32, -50.51, 0.5, 50, -26.35, -16.91, 0.5, 2, 49, 22.89, -58.03, 0.5, 50, -27.79, -24.43, 0.5, 2, 49, 27.09, 34.25, 0.65, 50, -23.58, 67.85, 0.35, 2, 49, 17.61, 11.3, 0.65, 50, -33.06, 44.91, 0.35, 2, 49, 13.64, 23.03, 0.65, 50, -37.04, 56.64, 0.35, 2, 49, 17.81, -45.77, 0.5, 50, -32.87, -12.16, 0.5, 2, 49, 15.92, -52.14, 0.5, 50, -34.76, -18.54, 0.5, 2, 49, 8.85, -44.36, 0.5, 50, -41.83, -10.76, 0.5, 2, 49, 0.45, -12.63, 0.45, 50, -50.23, 20.97, 0.55], "hull": 91, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 0, 180, 2, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 52, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 198, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 226, 242, 242, 240, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 244, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 294, 270, 296, 298, 298, 300, 300, 302, 302, 304, 304, 306, 306, 308, 308, 296, 310, 312, 312, 314, 314, 316, 316, 318, 318, 320, 320, 322, 322, 324, 324, 310], "width": 229, "height": 292}}, "hdj1": {"hdj1": {"type": "mesh", "uvs": [0.01445, 0.70706, 0.11974, 0.55145, 0.24416, 0.43258, 0.42283, 0.24239, 0.59512, 0.09974, 0.76421, 0.01545, 0.93012, 0.01977, 1, 0.11487, 0.95245, 0.2921, 0.97478, 0.45203, 0.97478, 0.60116, 0.98435, 0.78703, 1, 0.91671, 0.78335, 0.99019, 0.47707, 1, 0.22502, 0.94481, 0.05912, 0.867, 0, 0.78919, 0.56322, 0.64222, 0.64617, 0.39367], "triangles": [18, 2, 19, 18, 19, 10, 1, 2, 18, 18, 10, 11, 0, 15, 16, 18, 0, 1, 17, 0, 16, 18, 15, 0, 13, 18, 11, 13, 11, 12, 14, 15, 18, 13, 14, 18, 7, 5, 6, 4, 8, 19, 3, 4, 19, 5, 8, 4, 5, 7, 8, 2, 3, 19, 19, 8, 9, 19, 9, 10], "vertices": [2, 38, 23.06, -21.62, 0.30332, 39, 4.24, -22.79, 0.69668, 3, 37, 36.86, -17.23, 0.02486, 38, 12.69, -19.39, 0.58896, 39, -5.55, -18.69, 0.38618, 3, 37, 28.09, -15.04, 0.21365, 38, 4.36, -15.89, 0.68477, 39, -13.08, -13.71, 0.10158, 2, 37, 14.38, -12.33, 0.93377, 38, -8.78, -11.12, 0.06623, 1, 37, 3.51, -8.8, 1, 1, 37, -3.94, -4.09, 1, 1, 37, -6.22, 2.5, 1, 1, 37, -1.8, 7.38, 1, 2, 37, 9.17, 9.51, 0.97417, 38, -10.6, 11.26, 0.02583, 3, 37, 18.07, 13.99, 0.57818, 38, -1.12, 14.33, 0.39587, 39, -12.88, 17, 0.02595, 3, 37, 26.68, 17.35, 0.13903, 38, 7.9, 16.33, 0.60675, 39, -3.63, 17.3, 0.25423, 3, 37, 37.27, 21.91, 0.00372, 38, 19.07, 19.22, 0.22264, 39, 7.87, 18.08, 0.77365, 2, 38, 26.77, 21.61, 0.08161, 39, 15.88, 19, 0.91839, 2, 38, 33.19, 13.72, 0.01451, 39, 20.73, 10.05, 0.98549, 2, 38, 36.58, 1.29, 0.00059, 39, 21.76, -2.78, 0.99941, 2, 38, 35.53, -9.79, 0.06125, 39, 18.69, -13.48, 0.93875, 2, 38, 32.34, -17.63, 0.15968, 39, 14.09, -20.6, 0.84032, 2, 38, 28.17, -21.1, 0.22439, 39, 9.35, -23.24, 0.77561, 3, 37, 35.33, 2.17, 0, 38, 14.14, 0.01, 0.86603, 39, -0.53, 0.11, 0.13397, 1, 37, 19.71, -0.19, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 42, "height": 62}}, "eye": {"eye": {"type": "mesh", "uvs": [0.03794, 0.13138, 0.09472, 0.06245, 0.17523, 0.05655, 0.28775, 0.0979, 0.38168, 0.15304, 0.44775, 0.23772, 0.478, 0.34166, 0.496, 0.42547, 0.50381, 0.46178, 0.51516, 0.55827, 0.51103, 0.68824, 0.66587, 0.79064, 0.67715, 0.70507, 0.71322, 0.60981, 0.76523, 0.53439, 0.79951, 0.50339, 0.84568, 0.46162, 0.8694, 0.45217, 0.8951, 0.44194, 0.90585, 0.43766, 0.92967, 0.43766, 0.94469, 0.43766, 0.99393, 0.39267, 0.98561, 0.45089, 0.95509, 0.49719, 0.94053, 0.58015, 0.91903, 0.65292, 0.90654, 0.73098, 0.88574, 0.81698, 0.86077, 0.90298, 0.82401, 0.96913, 0.79142, 0.99692, 0.73663, 0.9731, 0.70472, 0.89239, 0.65687, 0.80772, 0.50451, 0.7151, 0.45023, 0.73302, 0.41694, 0.78727, 0.37259, 0.83529, 0.32458, 0.83061, 0.27758, 0.82602, 0.20268, 0.79162, 0.10697, 0.69769, 0.05353, 0.56325, 0.01954, 0.39522, 0.00983, 0.21925, 0.01607, 0.15045, 0.0208, 0.18495, 0.06779, 0.18166, 0.15311, 0.14866, 0.2594, 0.19194, 0.3482, 0.2725, 0.40417, 0.38837, 0.42921, 0.49713, 0.44738, 0.58694, 0.43813, 0.70784, 0.69315, 0.81379, 0.71837, 0.68978, 0.76892, 0.59953, 0.80287, 0.56723, 0.84848, 0.54339, 0.87293, 0.53084, 0.89862, 0.53085, 0.91278, 0.55093], "triangles": [49, 1, 2, 48, 0, 1, 48, 1, 49, 47, 46, 0, 47, 0, 48, 50, 2, 3, 49, 2, 50, 45, 46, 47, 51, 3, 4, 51, 4, 5, 50, 3, 51, 52, 51, 5, 52, 5, 6, 48, 44, 45, 48, 45, 47, 23, 21, 22, 6, 7, 53, 6, 53, 52, 54, 53, 7, 24, 21, 23, 62, 61, 17, 16, 17, 61, 62, 18, 19, 62, 17, 18, 60, 16, 61, 15, 16, 60, 19, 63, 62, 20, 63, 19, 20, 21, 24, 25, 63, 20, 8, 54, 7, 49, 43, 44, 49, 44, 48, 59, 15, 60, 14, 15, 59, 24, 25, 20, 8, 55, 54, 58, 14, 59, 13, 14, 58, 26, 63, 25, 8, 9, 55, 57, 13, 58, 50, 42, 43, 50, 43, 49, 57, 12, 13, 54, 39, 53, 9, 36, 55, 9, 10, 36, 26, 27, 62, 26, 62, 63, 35, 36, 10, 52, 40, 41, 52, 53, 40, 37, 55, 36, 41, 42, 50, 41, 50, 51, 41, 51, 52, 39, 40, 53, 34, 10, 11, 35, 10, 34, 56, 12, 57, 11, 12, 56, 60, 61, 28, 27, 61, 62, 59, 60, 29, 61, 27, 28, 38, 39, 54, 54, 55, 38, 37, 38, 55, 33, 56, 57, 34, 11, 56, 33, 34, 56, 58, 59, 30, 31, 57, 58, 29, 60, 28, 57, 32, 33, 30, 59, 29, 57, 31, 32, 58, 30, 31], "vertices": [1, 50, 31.8, 72.91, 1, 1, 50, 36.42, 65.96, 1, 1, 50, 37.01, 55.99, 1, 1, 50, 34.6, 41.98, 1, 1, 50, 31.25, 30.27, 1, 1, 50, 25.91, 21.97, 1, 1, 50, 19.23, 18.08, 1, 1, 50, 13.83, 15.74, 1, 1, 50, 11.49, 14.73, 1, 1, 50, 5.25, 13.19, 1, 1, 50, -3.21, 13.54, 1, 1, 50, -9.48, -5.79, 1, 1, 50, -3.89, -7.08, 1, 1, 50, 2.39, -11.43, 1, 1, 50, 7.42, -17.78, 1, 1, 50, 9.52, -21.99, 1, 1, 50, 12.35, -27.66, 1, 1, 50, 13.02, -30.59, 1, 1, 50, 13.75, -33.76, 1, 1, 50, 14.05, -35.09, 1, 1, 50, 14.11, -38.04, 1, 1, 50, 14.15, -39.9, 1, 1, 50, 17.19, -45.95, 1, 1, 50, 13.39, -44.99, 1, 1, 50, 10.31, -41.27, 1, 1, 50, 4.88, -39.57, 1, 1, 50, 0.1, -37, 1, 1, 50, -5.01, -35.55, 1, 1, 50, -10.65, -33.09, 1, 1, 50, -16.3, -30.1, 1, 1, 50, -20.69, -25.63, 1, 1, 50, -22.58, -21.63, 1, 1, 50, -21.16, -14.8, 1, 1, 50, -16, -10.74, 1, 1, 50, -10.61, -4.7, 1, 1, 50, -4.97, 14.31, 1, 1, 50, -6.27, 21.02, 1, 1, 50, -9.88, 25.07, 1, 1, 50, -13.11, 30.51, 1, 1, 50, -12.93, 36.47, 1, 1, 50, -12.74, 42.3, 1, 1, 50, -10.69, 51.63, 1, 1, 50, -4.83, 63.62, 1, 1, 50, 3.78, 70.42, 1, 1, 50, 14.61, 74.85, 1, 1, 50, 26.02, 76.28, 1, 1, 50, 30.51, 75.6, 1, 1, 50, 28.28, 74.97, 1, 1, 50, 28.61, 69.15, 1, 1, 50, 30.97, 58.61, 1, 1, 50, 28.42, 45.38, 1, 1, 50, 23.4, 34.26, 1, 1, 50, 16.01, 27.17, 1, 1, 50, 9.01, 23.93, 1, 1, 50, 3.21, 21.56, 1, 1, 50, -4.67, 22.55, 1, 1, 50, -10.92, -9.21, 1, 1, 50, -2.8, -12.17, 1, 1, 50, 3.19, -18.32, 1, 1, 50, 5.38, -22.49, 1, 1, 50, 7.04, -28.11, 1, 1, 50, 7.92, -31.13, 1, 1, 50, 7.98, -34.31, 1, 1, 50, 6.71, -36.09, 1], "hull": 47, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 0, 92, 0, 2, 76, 78, 78, 80, 12, 14, 14, 16, 72, 18, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 50, 40, 48, 42, 0, 94, 94, 90, 2, 96, 96, 88, 4, 98, 98, 86, 96, 98, 6, 100, 100, 84, 98, 100, 8, 102, 102, 82, 100, 102, 10, 104, 104, 80, 102, 104, 12, 106, 106, 78, 104, 106, 14, 108, 108, 76, 106, 108, 16, 110, 110, 74, 108, 110, 24, 112, 112, 66, 26, 114, 114, 64, 112, 114, 28, 116, 116, 62, 114, 116, 30, 118, 118, 60, 116, 118, 32, 120, 120, 58, 118, 120, 34, 122, 122, 56, 120, 122, 36, 124, 124, 54, 122, 124, 38, 126, 126, 52, 124, 126], "width": 124, "height": 65}}, "youjiao": {"youjiao": {"type": "mesh", "uvs": [0.09275, 0.06058, 0.04235, 0.13397, 0, 0.25166, 0, 0.35324, 0.05013, 0.47232, 0.17613, 0.6177, 0.19053, 0.71878, 0.18384, 0.81237, 0.18384, 0.85357, 0.18384, 0.88428, 0.26548, 0.99265, 1, 1, 0.98174, 0.941, 0.92118, 0.88732, 0.74211, 0.80757, 0.7361, 0.77465, 0.73158, 0.74984, 0.64994, 0.68752, 0.62888, 0.62371, 0.65784, 0.53256, 0.68944, 0.4647, 0.80912, 0.41209, 0.89865, 0.33714, 0.98292, 0.27536, 0.98028, 0.16091, 0.96448, 0.06469, 1, 0, 0.80912, 0, 0.61425, 0.04748, 0.45099, 0.08596, 0.32722, 0.11331, 0.22979, 0.11635, 0.14815, 0.05153, 0.31869, 0.89454, 0.42053, 0.84694, 0.56936, 0.80476, 0.28579, 0.86441, 0.38606, 0.82043, 0.54586, 0.78307], "triangles": [9, 36, 33, 33, 36, 34, 34, 37, 35, 35, 15, 14, 12, 33, 34, 35, 14, 13, 34, 35, 13, 10, 9, 33, 12, 10, 33, 34, 13, 12, 11, 10, 12, 9, 8, 36, 36, 37, 34, 37, 36, 7, 36, 8, 7, 37, 38, 35, 38, 37, 6, 37, 7, 6, 35, 38, 15, 38, 16, 15, 38, 17, 16, 38, 6, 17, 6, 18, 17, 6, 5, 18, 18, 5, 19, 5, 4, 19, 19, 4, 20, 20, 4, 30, 30, 3, 31, 30, 4, 3, 1, 31, 2, 20, 30, 21, 21, 30, 22, 23, 22, 30, 31, 3, 2, 23, 30, 29, 29, 24, 23, 24, 29, 28, 25, 28, 27, 28, 25, 24, 1, 0, 31, 0, 32, 31, 25, 27, 26], "vertices": [1, 19, -18.13, -40.96, 1, 1, 19, -0.68, -44.28, 1, 1, 19, 27.06, -46.15, 1, 1, 19, 50.77, -44.48, 1, 1, 19, 78.25, -38.02, 1, 1, 19, 111.39, -24.32, 1, 1, 19, 134.89, -21.36, 1, 1, 19, 156.78, -20.43, 1, 2, 19, 166.4, -19.75, 0.75, 83, -19.8, -0.07, 0.25, 2, 19, 173.57, -19.24, 0.25, 83, -19.8, -7.26, 0.75, 1, 83, -12.45, -32.61, 1, 1, 83, 53.65, -34.33, 1, 1, 83, 52.01, -20.53, 1, 1, 83, 46.56, -7.97, 1, 2, 19, 152.13, 29.62, 0.25, 83, 30.44, 10.69, 0.75, 2, 19, 144.48, 28.53, 0.75, 83, 29.9, 18.4, 0.25, 1, 19, 138.72, 27.72, 1, 1, 19, 124.69, 19.37, 1, 1, 19, 109.93, 16.43, 1, 1, 19, 88.47, 17.53, 1, 1, 19, 72.43, 19.25, 1, 1, 19, 59.39, 29.13, 1, 1, 19, 41.33, 35.93, 1, 1, 19, 26.38, 42.48, 1, 1, 19, -0.32, 40.37, 1, 1, 19, -22.68, 37.37, 1, 1, 19, -38, 39.49, 1, 1, 19, -36.8, 22.35, 1, 1, 19, -24.48, 5.64, 1, 1, 19, -14.47, -8.39, 1, 1, 19, -7.3, -19.05, 1, 1, 19, -5.98, -27.74, 1, 1, 19, -20.59, -36.14, 1, 2, 19, 175.11, -6.97, 0.25, 83, -7.67, -9.66, 0.75, 2, 19, 163.35, 1.39, 0.25, 83, 1.5, 1.48, 0.75, 2, 19, 152.57, 14.06, 0.25, 83, 14.89, 11.35, 0.75, 2, 19, 168.29, -10.42, 0.75, 83, -10.63, -2.61, 0.25, 2, 19, 157.38, -2.14, 0.75, 83, -1.6, 7.69, 0.25, 2, 19, 147.65, 11.59, 0.75, 83, 12.78, 16.43, 0.25], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 18, 66, 66, 68, 68, 70, 70, 28, 14, 16, 16, 18, 16, 72, 72, 74, 74, 76, 28, 30, 30, 32, 76, 30], "width": 90, "height": 234}}, "qundi": {"qundi": {"x": 48.27, "y": 380.52, "width": 152, "height": 76}}, "zuojiao1": {"zuojiao1": {"type": "mesh", "uvs": [0, 0.8816, 0, 0.80498, 0.01527, 0.66677, 0.03195, 0.50734, 0.04586, 0.46828, 0.00971, 0.39016, 0.03195, 0.27494, 0.11815, 0.13371, 0.32115, 0.03606, 0.52692, 0, 0.75493, 0.00601, 0.91065, 0.08113, 1, 0.17278, 1, 0.27093, 0.94404, 0.3821, 0.87174, 0.51732, 0.79839, 0.6139, 0.67326, 0.72808, 0.55647, 0.84226, 0.43134, 0.95644, 0.32289, 1, 0.14493, 1, 0.06707, 0.93842], "triangles": [11, 12, 13, 13, 10, 11, 6, 15, 5, 4, 5, 15, 10, 14, 9, 13, 14, 10, 9, 14, 15, 15, 8, 9, 15, 7, 8, 7, 15, 6, 16, 4, 15, 16, 3, 4, 17, 3, 16, 2, 3, 17, 18, 1, 2, 17, 18, 2, 22, 0, 1, 1, 19, 22, 18, 19, 1, 20, 21, 22, 19, 20, 22], "vertices": [1, 16, 193.16, -27.18, 1, 1, 16, 177.64, -31.71, 1, 1, 16, 149.16, -38.23, 1, 1, 16, 116.34, -45.85, 1, 1, 16, 107.99, -46.64, 1, 1, 16, 93.32, -55.22, 1, 1, 16, 69.28, -59.61, 1, 1, 16, 37.92, -58.54, 1, 1, 16, 11.65, -42.12, 1, 1, 16, -2.24, -21.74, 1, 1, 16, -8.32, 3.57, 1, 1, 16, 1.91, 25.06, 1, 1, 16, 17.61, 40.26, 1, 1, 16, 37.49, 46.07, 1, 1, 16, 61.8, 46.53, 1, 1, 16, 91.49, 46.63, 1, 1, 16, 113.4, 44.33, 1, 1, 16, 140.53, 37.4, 1, 1, 16, 167.39, 31.38, 1, 1, 16, 194.52, 24.45, 1, 1, 16, 206.81, 15.17, 1, 1, 16, 212.5, -4.31, 1, 1, 16, 202.52, -16.47, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 114, "height": 211}}, "fa1": {"fa1": {"type": "mesh", "uvs": [0.69664, 0.46669, 0.74973, 0.52485, 0.80645, 0.62288, 0.88006, 0.72091, 0.94039, 0.62454, 1, 0.48165, 1, 0.33876, 0.97176, 0.13439, 0.94039, 0.07291, 0.8873, 0, 0.79317, 0, 0.70267, 0.02472, 0.56752, 0.12109, 0.43358, 0.25568, 0.3235, 0.39584, 0.23662, 0.49221, 0.16542, 0.54704, 0.08095, 0.55368, 0, 0.4839, 0, 0.58525, 0.04354, 0.66168, 0.08826, 0.70798, 0.18158, 0.70354, 0.13734, 0.7545, 0.08183, 0.80102, 0.01988, 0.85641, 0, 0.90957, 0.0416, 0.89628, 0.1132, 0.91179, 0.20169, 0.92287, 0.25801, 0.84754, 0.24223, 0.89164, 0.22681, 0.92487, 0.19598, 0.95625, 0.16145, 0.97748, 0.18554, 0.99514, 0.23317, 0.99514, 0.30544, 0.9876, 0.38058, 0.93796, 0.43642, 0.84825, 0.50909, 0.74015, 0.55607, 0.63085, 0.57604, 0.56696, 0.5923, 0.51398, 0.65137, 0.46399, 0.68767, 0.45355, 0.75971, 0.16281, 0.60739, 0.28423, 0.44934, 0.47188, 0.32795, 0.70369], "triangles": [46, 11, 10, 46, 47, 12, 46, 12, 11, 13, 12, 47, 45, 47, 46, 46, 10, 9, 8, 46, 9, 44, 47, 45, 0, 45, 46, 48, 13, 47, 43, 48, 47, 14, 13, 48, 6, 46, 7, 44, 43, 47, 1, 0, 46, 42, 48, 43, 19, 18, 17, 1, 46, 6, 7, 46, 8, 5, 2, 6, 2, 1, 6, 4, 2, 5, 41, 48, 42, 20, 19, 17, 22, 16, 15, 49, 14, 48, 40, 49, 48, 15, 14, 49, 22, 15, 49, 21, 17, 16, 21, 16, 22, 20, 17, 21, 3, 2, 4, 41, 40, 48, 30, 22, 49, 23, 22, 30, 39, 49, 40, 38, 30, 49, 30, 29, 23, 27, 25, 24, 26, 25, 27, 28, 24, 23, 29, 28, 23, 27, 24, 28, 39, 38, 49, 37, 31, 30, 38, 37, 30, 32, 31, 37, 35, 34, 33, 36, 32, 37, 33, 32, 36, 35, 33, 36], "vertices": [3, 51, -7.41, 27.01, 0.48072, 52, 30.34, 28.64, 0.24499, 53, 3.15, 28.47, 0.27429, 3, 51, -14.78, 17.35, 0.84117, 52, 26.74, 40.24, 0.0858, 53, 1.05, 40.43, 0.07303, 3, 51, -27.32, 6.95, 0.99213, 52, 25.53, 56.49, 0.00119, 53, 1.91, 56.7, 0.00667, 1, 51, -39.8, -6.48, 1, 1, 51, -27.06, -17.03, 1, 2, 51, -8.27, -27.32, 0.99781, 52, -13.44, 60.82, 0.00219, 2, 51, 10.3, -26.95, 0.89176, 52, -23.9, 45.47, 0.10824, 2, 51, 36.76, -21.37, 0.45611, 52, -34.68, 20.67, 0.54389, 2, 51, 44.64, -15.59, 0.32127, 52, -34.54, 10.9, 0.67873, 2, 51, 53.93, -5.9, 0.17486, 52, -32.02, -2.28, 0.82514, 2, 51, 53.59, 10.94, 0.04546, 52, -18.1, -11.77, 0.95454, 1, 52, -2.9, -18.24, 1, 2, 52, 24.14, -21.5, 0.75697, 53, -9.36, -20.48, 0.24303, 3, 52, 53.81, -20.54, 0.00805, 53, 20.18, -23.3, 0.94516, 54, -20.46, -19.59, 0.04679, 3, 53, 47.01, -22.73, 0.19675, 54, 5.94, -24.42, 0.79679, 55, -34.59, -20.37, 0.00647, 3, 53, 66.96, -23.68, 0.00134, 54, 25.29, -29.35, 0.76642, 55, -15.98, -27.61, 0.23224, 2, 54, 38.64, -35.26, 0.39663, 55, -3.44, -35.11, 0.60337, 2, 54, 48.41, -46.84, 0.21202, 55, 4.84, -47.78, 0.78798, 2, 54, 49.84, -63.88, 0.17498, 55, 4.2, -64.87, 0.82502, 2, 54, 60.38, -55.97, 0.18119, 55, 15.62, -58.3, 0.81881, 2, 54, 63.65, -43.77, 0.19699, 55, 20.35, -46.59, 0.80301, 2, 54, 63.67, -33.76, 0.20854, 55, 21.58, -36.65, 0.79146, 2, 54, 53.18, -20.74, 0.15651, 55, 12.75, -22.46, 0.84349, 2, 54, 63.23, -23.1, 0.01423, 55, 22.44, -26.02, 0.98577, 1, 55, 32.64, -31.62, 1, 1, 55, 44.41, -37.64, 1, 1, 55, 52.17, -37.28, 1, 1, 55, 46.96, -31.69, 1, 1, 55, 42.32, -19.57, 1, 1, 55, 35.67, -5.12, 1, 1, 55, 22.16, -1.26, 1, 1, 55, 28.54, -0.86, 1, 1, 55, 33.66, -1.09, 1, 1, 55, 39.95, -3.85, 1, 1, 55, 45.42, -7.83, 1, 1, 55, 45.26, -2.95, 1, 1, 55, 41.01, 4.45, 1, 2, 54, 69.42, 19.15, 0.00029, 55, 33.71, 15.17, 0.99971, 2, 54, 56.19, 26.04, 0.05985, 55, 21.41, 23.61, 0.94015, 3, 53, 70.91, 34.68, 0.00051, 54, 40.86, 27.04, 0.37655, 55, 6.32, 26.47, 0.62294, 3, 53, 51.85, 32.8, 0.06617, 54, 21.82, 29.01, 0.80329, 55, -12.35, 30.74, 0.13054, 4, 51, -29.25, 51.74, 0.0002, 53, 36.13, 27.75, 0.35539, 54, 5.4, 27.21, 0.63566, 55, -28.85, 30.95, 0.00874, 5, 51, -20.88, 48.33, 0.00457, 52, 55.52, 27.26, 0.00019, 53, 27.95, 23.9, 0.62738, 54, -3.39, 25.09, 0.36767, 55, -37.84, 29.9, 0.00019, 4, 51, -13.93, 45.56, 0.0238, 52, 49.24, 23.2, 0.01253, 53, 21.2, 20.68, 0.82884, 54, -10.64, 23.29, 0.13483, 4, 51, -7.22, 35.11, 0.16587, 52, 36.84, 23.79, 0.16818, 53, 8.98, 22.83, 0.65994, 54, -22.19, 27.85, 0.00601, 4, 51, -5.74, 28.64, 0.37955, 52, 30.7, 26.32, 0.26136, 53, 3.21, 26.13, 0.35897, 54, -27.17, 32.23, 0.00011, 2, 51, 32.31, 16.51, 0.03321, 52, -1.23, 2.35, 0.96679, 3, 51, 15.98, 43.45, 0.00012, 52, 30.19, 0.04, 0.61576, 53, -0.63, 0.12, 0.38412, 2, 53, 36.72, -0.39, 0.43169, 54, 0.33, -0.47, 0.56831, 2, 54, 37.48, 0.23, 0.55311, 55, -0.29, 0.27, 0.44689], "hull": 46, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 0, 90], "width": 179, "height": 130}}, "fa2": {"fa2": {"type": "mesh", "uvs": [0.00411, 0.25557, 0.09808, 0.23359, 0.23041, 0.16185, 0.33205, 0.08433, 0.40684, 0.02301, 0.52958, 0.00218, 0.68301, 0.03574, 0.71561, 0.0716, 0.85876, 0.12026, 0.94698, 0.20357, 0.98342, 0.30886, 0.99954, 0.41847, 0.98803, 0.53417, 0.96694, 0.6221, 0.91668, 0.75579, 0.80545, 0.87381, 0.68846, 0.94786, 0.58682, 1, 0.49285, 0.99298, 0.57148, 0.93051, 0.65011, 0.80783, 0.67696, 0.71642, 0.68271, 0.61846, 0.64052, 0.49235, 0.55805, 0.40441, 0.44682, 0.32574, 0.30107, 0.29913, 0.08627, 0.30838, 0, 0.28177, 0.75091, 0.31123, 0.85369, 0.56531, 0.80857, 0.77402, 0.52529, 0.14335], "triangles": [27, 0, 1, 28, 0, 27, 32, 4, 5, 32, 5, 6, 3, 4, 32, 32, 2, 3, 1, 2, 26, 27, 1, 26, 15, 20, 31, 16, 19, 20, 15, 16, 20, 17, 19, 16, 18, 19, 17, 22, 23, 30, 13, 22, 30, 13, 21, 22, 14, 21, 13, 31, 21, 14, 20, 21, 31, 15, 31, 14, 23, 24, 29, 11, 29, 10, 11, 23, 29, 11, 30, 23, 12, 30, 11, 13, 30, 12, 32, 6, 7, 32, 26, 2, 8, 32, 7, 29, 8, 9, 29, 9, 10, 29, 32, 8, 25, 26, 32, 29, 25, 32, 24, 25, 29], "vertices": [1, 49, 121, -55.54, 1, 1, 49, 123.8, -62.34, 1, 1, 49, 132.67, -71.83, 1, 1, 49, 142.2, -79.06, 1, 1, 49, 149.72, -84.37, 1, 1, 49, 152.42, -93.28, 1, 1, 49, 148.59, -104.56, 1, 1, 59, 1.27, 16.37, 1, 2, 59, 12.34, 20.98, 0.98534, 60, -21.12, 13.69, 0.01466, 2, 59, 24.25, 19.84, 0.79675, 60, -9.74, 17.4, 0.20325, 2, 59, 35.92, 14.09, 0.24635, 60, 3.26, 16.78, 0.75365, 2, 59, 47.1, 6.85, 0.00138, 60, 16.39, 14.6, 0.99862, 1, 60, 29.73, 10.28, 1, 2, 60, 39.65, 6.13, 0.7047, 61, 5.56, 9.14, 0.2953, 3, 60, 54.39, -1.47, 0.00476, 61, 22.11, 8.02, 0.95197, 62, -6.88, 5.49, 0.04327, 1, 62, 9.45, 7.28, 1, 1, 62, 21.71, 5.61, 1, 1, 62, 31.17, 3.29, 1, 1, 62, 34.51, -2.77, 1, 1, 62, 25.02, -2.54, 1, 2, 61, 31.34, -10.24, 0.06627, 62, 9.62, -6.59, 0.93373, 2, 61, 20.11, -10.01, 0.82263, 62, -0.49, -11.48, 0.17737, 2, 60, 34.03, -13.85, 0.10429, 61, 8.33, -11.42, 0.89571, 3, 59, 37.99, -19.31, 0.02399, 60, 18.48, -13.01, 0.88644, 61, -6.27, -16.82, 0.08957, 3, 59, 25.9, -17.49, 0.38129, 60, 6.67, -16.17, 0.61858, 61, -15.85, -24.41, 0.00013, 2, 59, 13.4, -18.03, 0.89896, 60, -4.58, -21.65, 0.10104, 2, 59, 4.31, -24.42, 0.99581, 60, -10.36, -31.15, 0.00419, 1, 49, 114.73, -61.66, 1, 1, 49, 117.83, -55.3, 1, 2, 59, 25.69, 0.54, 0.72167, 60, -0.72, 0.28, 0.27833, 2, 60, 30.93, -0.15, 0.00282, 61, 0.05, -0.08, 0.99718, 2, 61, 25.51, 0.56, 0.63999, 62, -0.47, 0.39, 0.36001, 2, 59, -0.45, 0.08, 0.97304, 60, -24.51, -10.58, 0.02696], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 52, 64, 64, 14], "width": 73, "height": 121}}, "fa3": {"fa3": {"type": "mesh", "uvs": [0.02336, 0.18092, 0.11076, 0.14192, 0.24876, 0.07692, 0.39826, 0.07792, 0.39915, 0.15368, 0.38555, 0.23292, 0.37467, 0.34358, 0.40731, 0.44765, 0.46715, 0.54462, 0.56507, 0.61676, 0.69835, 0.6889, 0.81259, 0.75039, 0.95131, 0.81898, 1, 0.91004, 1, 1, 0.89147, 1, 0.82075, 0.92935, 0.72807, 0.85787, 0.58391, 0.81293, 0.40711, 0.74079, 0.26296, 0.66983, 0.1324, 0.59533, 0.01816, 0.49362, 0, 0.3458, 0, 0.27129, 0.19497, 0.44278, 0.41258, 0.63436, 0.70634, 0.77154], "triangles": [2, 4, 1, 4, 2, 3, 5, 0, 1, 27, 10, 11, 17, 27, 11, 17, 11, 12, 18, 27, 17, 16, 17, 12, 13, 16, 12, 15, 16, 13, 15, 13, 14, 26, 8, 9, 19, 20, 26, 9, 19, 26, 27, 19, 10, 9, 10, 19, 27, 18, 19, 21, 22, 25, 8, 21, 25, 8, 25, 7, 26, 21, 8, 20, 21, 26, 5, 1, 4, 5, 24, 0, 6, 24, 5, 23, 24, 6, 25, 23, 6, 25, 6, 7, 22, 23, 25], "vertices": [1, 49, 73.69, 65.67, 1, 1, 49, 78.26, 61.39, 1, 1, 49, 85.87, 54.64, 1, 1, 49, 85.91, 47.17, 1, 1, 66, -2.64, 10.15, 1, 1, 66, 6.47, 9.39, 1, 2, 66, 19.19, 8.73, 0.98322, 67, -6.49, 12.7, 0.01678, 2, 66, 31.17, 10.25, 0.28918, 67, 5, 8.98, 0.71082, 2, 67, 16.35, 6.84, 0.98301, 68, -5.42, 8.58, 0.01699, 3, 67, 25.95, 7.66, 0.1891, 68, 4.06, 6.83, 0.81062, 69, -18.94, 1.88, 0.00029, 2, 68, 14.69, 6.4, 0.75474, 69, -8.53, 4.08, 0.24526, 1, 69, 0.36, 5.98, 1, 1, 69, 10.53, 8.61, 1, 1, 69, 20.98, 6.05, 1, 1, 69, 30.21, 1.37, 1, 1, 69, 27.75, -3.47, 1, 1, 69, 18.91, -2.95, 1, 2, 68, 30.32, -5.24, 0.05118, 69, 9.48, -3.37, 0.94882, 2, 68, 21.69, -7.28, 0.97194, 69, 1.61, -7.47, 0.02806, 2, 67, 35.38, -5.64, 0.00067, 68, 9.62, -8.5, 0.99933, 2, 67, 24.9, -8.6, 0.5504, 68, -1.26, -8.57, 0.4496, 2, 67, 14.35, -10.77, 0.99918, 68, -12.01, -7.87, 0.00082, 2, 66, 36.28, -9.25, 0.2153, 67, 1.33, -10.85, 0.7847, 1, 66, 19.28, -10.01, 1, 1, 66, 10.71, -9.93, 1, 2, 66, 30.52, -0.36, 0.51656, 67, -0.1, -0.35, 0.48344, 2, 67, 24.47, -0.09, 0.23587, 68, 0.57, -0.25, 0.76413, 1, 69, 0.13, 0.14, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "width": 50, "height": 115}}, "qun1": {"qun1": {"type": "mesh", "uvs": [0.95186, 0.0106, 0.99933, 0.04625, 1, 0.1139, 0.97559, 0.17206, 0.91625, 0.22508, 0.86729, 0.26073, 0.7575, 0.30096, 0.6605, 0.35347, 0.60858, 0.41198, 0.55678, 0.47312, 0.50782, 0.53619, 0.45144, 0.61846, 0.39952, 0.71536, 0.35056, 0.7977, 0.30902, 0.88181, 0.27638, 0.97596, 0.26748, 1, 0.20368, 0.98053, 0.11763, 0.92843, 0.04494, 0.86535, 0, 0.81142, 0, 0.74834, 0.0433, 0.6798, 0.11647, 0.58251, 0.20739, 0.4746, 0.3246, 0.36948, 0.42251, 0.28355, 0.53938, 0.18955, 0.65214, 0.11733, 0.77676, 0.04694, 0.89248, 0, 0.93996, 0, 0.59577, 0.26172, 0.82888, 0.13005, 0.40733, 0.4257, 0.25581, 0.59328], "triangles": [4, 33, 3, 2, 3, 33, 28, 29, 33, 2, 33, 1, 33, 0, 1, 33, 30, 0, 30, 31, 0, 33, 29, 30, 7, 32, 6, 6, 32, 33, 6, 33, 5, 33, 32, 28, 32, 27, 28, 5, 33, 4, 10, 34, 9, 9, 34, 8, 8, 34, 26, 8, 26, 7, 7, 26, 32, 26, 27, 32, 11, 35, 10, 23, 24, 35, 35, 34, 10, 35, 24, 34, 24, 25, 34, 34, 25, 26, 16, 17, 15, 15, 17, 14, 14, 17, 18, 18, 19, 14, 14, 19, 13, 19, 20, 13, 22, 13, 21, 13, 22, 12, 22, 23, 35, 13, 20, 21, 12, 22, 35, 12, 35, 11], "vertices": [1, 1, -58.4, 25.11, 1, 1, 1, -46.82, 11, 1, 1, 1, -46.66, -15.79, 1, 1, 1, -52.61, -38.83, 1, 1, 20, 10.03, 41.3, 1, 2, 21, -39.36, 52.37, 0.00128, 20, 28.4, 43.45, 0.99872, 2, 21, -10.86, 39.75, 0.15996, 20, 58.84, 36.78, 0.84004, 2, 21, 19.8, 32.5, 0.83049, 20, 90.34, 35.8, 0.16951, 2, 21, 46.02, 35.58, 0.99769, 20, 115.41, 44.06, 0.00231, 2, 22, -1.58, 41.38, 0.04749, 21, 73.08, 39.3, 0.95251, 3, 23, -42.7, 49.81, 0.01715, 22, 26.06, 42.96, 0.47405, 21, 100.36, 44.02, 0.5088, 3, 23, -7.37, 48.24, 0.34055, 22, 61.24, 46.62, 0.57734, 21, 134.89, 51.67, 0.08211, 3, 23, 33.01, 49.7, 0.91802, 22, 100.96, 54.02, 0.08034, 21, 173.51, 63.56, 0.00164, 2, 23, 67.73, 49.83, 0.99959, 22, 135.29, 59.28, 0.00041, 1, 23, 102.48, 51.91, 1, 1, 23, 140.21, 57.4, 1, 1, 23, 149.89, 58.67, 1, 1, 23, 148.08, 41.4, 1, 1, 23, 136.03, 14.54, 1, 1, 23, 118.78, -10.78, 1, 1, 23, 102.56, -28.48, 1, 1, 23, 79.14, -37.17, 1, 1, 23, 50.02, -36.69, 1, 2, 23, 7.69, -33.35, 0.79112, 22, 88.18, -31.85, 0.20888, 2, 23, -40.09, -27.4, 0.00248, 22, 40.04, -33.02, 0.99752, 2, 22, -10.22, -28.04, 0.86201, 21, 72.42, -30.66, 0.13799, 3, 22, -51.55, -23.52, 0.06961, 21, 30.85, -30.88, 0.92492, 20, 113.82, -24.1, 0.00548, 2, 21, -16.01, -29.19, 0.16685, 20, 67.57, -31.8, 0.83315, 1, 20, 27.94, -33.89, 1, 1, 1, -101.13, 10.72, 1, 1, 1, -72.89, 29.31, 1, 1, 1, -61.31, 29.31, 1, 2, 21, -0.68, -1.42, 0.42902, 20, 77.05, -1.53, 0.57098, 1, 1, -88.41, -22.19, 1, 2, 22, -0.45, 0.38, 0.84275, 21, 78.89, -1.3, 0.15725, 3, 23, -0.13, 0.01, 0.48587, 22, 75.52, -0.01, 0.51413, 21, 154.4, 6.98, 0], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62], "width": 244, "height": 396}}, "fa10": {"fa10": {"type": "mesh", "uvs": [0.28038, 0.51555, 0.19638, 0.5627, 0.11038, 0.63498, 0, 0.71879, 0, 0.77431, 0.09238, 0.79527, 0.25438, 0.80469, 0.40438, 0.80155, 0.4945, 0.77274, 0.59237, 0.70328, 0.59223, 0.74959, 0.61246, 0.79135, 0.65411, 0.8487, 0.69934, 0.88423, 0.72693, 0.92664, 0.72693, 0.96779, 0.72574, 1, 0.76382, 0.98025, 0.79476, 0.93911, 0.82148, 0.89963, 0.84237, 0.86876, 0.83166, 0.81702, 0.80667, 0.75131, 0.78048, 0.68461, 0.76977, 0.62678, 0.76858, 0.54449, 0.86055, 0.51533, 0.94329, 0.45723, 0.9961, 0.38623, 1, 0.32611, 0.9992, 0.2309, 0.95966, 0.12958, 0.9542, 0.0803, 0.95556, 0.01815, 0.87784, 0.00387, 0.83147, 0.00315, 0.72375, 0.03958, 0.5996, 0.10477, 0.54097, 0.14406, 0.54233, 0.19549, 0.58324, 0.24977, 0.62843, 0.29615, 0.64033, 0.34342, 0.61297, 0.41501, 0.58322, 0.44514, 0.50686, 0.45865, 0.40371, 0.45917, 0.46818, 0.49242, 0.61866, 0.60754, 0.62602, 0.56285, 0.71382, 0.72881, 0.74911, 0.84368], "triangles": [32, 34, 33, 39, 38, 37, 40, 39, 37, 32, 36, 35, 32, 35, 34, 40, 37, 31, 36, 31, 37, 32, 31, 36, 30, 40, 31, 30, 41, 40, 41, 30, 29, 42, 41, 29, 28, 42, 29, 27, 43, 42, 28, 27, 42, 47, 46, 45, 26, 43, 27, 25, 44, 43, 26, 25, 43, 49, 44, 25, 47, 45, 44, 49, 47, 44, 48, 47, 49, 49, 25, 24, 48, 49, 24, 48, 0, 47, 9, 48, 24, 9, 24, 23, 0, 48, 9, 9, 1, 0, 9, 2, 1, 8, 2, 9, 3, 5, 4, 2, 5, 3, 6, 5, 2, 8, 7, 2, 7, 6, 2, 50, 9, 23, 51, 21, 20, 13, 12, 51, 19, 51, 20, 13, 51, 19, 14, 13, 19, 18, 14, 19, 15, 14, 18, 17, 15, 18, 16, 15, 17, 11, 10, 50, 22, 11, 50, 51, 22, 21, 51, 11, 22, 12, 11, 51, 50, 23, 22, 10, 9, 50], "vertices": [1, 49, 36.18, -23.85, 1, 1, 49, 25.09, -13.91, 1, 1, 49, 8.19, -3.84, 1, 1, 49, -11.43, 9.13, 1, 1, 49, -24.26, 8.87, 1, 1, 49, -28.87, -2.4, 1, 1, 49, -30.66, -22.04, 1, 1, 49, -29.57, -40.17, 1, 1, 49, -22.7, -50.94, 1, 1, 49, -6.42, -62.46, 1, 2, 63, 33.55, -14.53, 0.39463, 64, 1.37, -14.51, 0.60537, 3, 63, 43.5, -14.04, 0.04807, 64, 11.31, -13.93, 0.94706, 65, -12.76, -15.18, 0.00487, 2, 64, 25.27, -11.48, 0.55596, 65, 0.92, -11.45, 0.44404, 2, 64, 34.36, -7.66, 0.01576, 65, 9.62, -6.8, 0.98424, 1, 65, 19.69, -4.43, 1, 1, 65, 29.15, -5.35, 1, 1, 65, 36.54, -6.22, 1, 1, 65, 32.45, -1.19, 1, 1, 65, 23.35, 3.46, 1, 1, 65, 14.59, 7.56, 1, 2, 64, 34.12, 10.01, 0.04693, 65, 7.74, 10.77, 0.95307, 2, 64, 22.14, 10.99, 0.74751, 65, -4.28, 10.64, 0.25249, 2, 63, 39.08, 10.83, 0.08417, 64, 6.66, 10.89, 0.91583, 1, 49, -1.65, -85.13, 1, 1, 49, 11.68, -83.57, 1, 1, 49, 30.68, -83.05, 1, 1, 49, 37.64, -94.04, 1, 1, 49, 51.26, -103.78, 1, 1, 49, 67.78, -109.84, 1, 1, 49, 81.68, -110.03, 1, 1, 49, 103.66, -109.5, 1, 1, 49, 126.97, -104.24, 1, 1, 49, 138.34, -103.36, 1, 1, 49, 152.69, -103.24, 1, 1, 49, 155.8, -93.77, 1, 1, 49, 155.86, -88.15, 1, 1, 49, 147.18, -75.29, 1, 1, 49, 131.83, -60.57, 1, 1, 49, 122.61, -53.66, 1, 1, 49, 110.74, -54.06, 1, 1, 49, 98.3, -59.26, 1, 1, 49, 87.7, -64.94, 1, 1, 49, 76.81, -66.6, 1, 1, 49, 60.21, -63.62, 1, 1, 49, 53.18, -60.16, 1, 1, 49, 49.87, -50.99, 1, 1, 49, 49.5, -38.51, 1, 1, 49, 41.98, -46.46, 1, 1, 49, 15.76, -65.2, 1, 1, 49, 26.1, -65.88, 1, 2, 63, 31.76, 0.84, 0.62375, 64, -0.56, 0.84, 0.37625, 2, 64, 26.3, 0.02, 0.33757, 65, 0.88, 0.1, 0.66243], "hull": 48, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 40, 42, 36, 38, 38, 40, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 0, 94, 18, 96, 96, 98, 98, 50], "width": 121, "height": 231}}, "fa11": {"fa11": {"type": "mesh", "uvs": [0.83127, 0.18296, 0.91113, 0.16544, 0.96304, 0.1358, 1, 0.10077, 1, 0.06776, 0.95346, 0.03138, 0.89276, 0.0462, 0.84783, 0.03801, 0.81429, 0.02992, 0.78794, 0.00837, 0.73602, 0, 0.67693, 0, 0.63673, 0.01982, 0.58642, 0.02184, 0.5385, 0.0434, 0.50176, 0.05889, 0.467, 0.06091, 0.41509, 0.04676, 0.41669, 0.08112, 0.44624, 0.10268, 0.46301, 0.11144, 0.42387, 0.12491, 0.37436, 0.12828, 0.32724, 0.11885, 0.28251, 0.09998, 0.26894, 0.12262, 0.2913, 0.14754, 0.32804, 0.17449, 0.36637, 0.18998, 0.4063, 0.19537, 0.43745, 0.19537, 0.42356, 0.2191, 0.42421, 0.23669, 0.43333, 0.26087, 0.41053, 0.2834, 0.3799, 0.30099, 0.34276, 0.33122, 0.38381, 0.33342, 0.41834, 0.32572, 0.39489, 0.35925, 0.37609, 0.40232, 0.35654, 0.4364, 0.31001, 0.48009, 0.26179, 0.50812, 0.21227, 0.53176, 0.1647, 0.54824, 0.11193, 0.55429, 0.05459, 0.55539, 0, 0.54605, 0.00171, 0.55759, 0.02778, 0.58067, 0.06557, 0.59551, 0.10206, 0.59881, 0.1418, 0.59826, 0.12226, 0.61132, 0.08446, 0.63495, 0.04602, 0.67068, 0.02322, 0.70256, 0, 0.74433, 0.00432, 0.80204, 0.02322, 0.83227, 0.05175, 0.84838, 0.0564, 0.83557, 0.04896, 0.82668, 0.04338, 0.80655, 0.05082, 0.78799, 0.06787, 0.7655, 0.07872, 0.7506, 0.07655, 0.77466, 0.07717, 0.80776, 0.08782, 0.84195, 0.10584, 0.88617, 0.13122, 0.92536, 0.17422, 0.95334, 0.2395, 0.9875, 0.29273, 1, 0.35388, 1, 0.41121, 0.99407, 0.46076, 0.98025, 0.44807, 0.97127, 0.428, 0.96816, 0.39647, 0.96729, 0.36903, 0.95278, 0.33586, 0.92895, 0.31334, 0.90166, 0.29327, 0.8647, 0.2945, 0.83465, 0.30024, 0.79838, 0.31621, 0.83016, 0.34241, 0.85745, 0.3678, 0.8761, 0.39319, 0.89061, 0.42964, 0.90442, 0.48001, 0.91168, 0.51563, 0.91168, 0.48598, 0.9016, 0.45555, 0.88659, 0.43749, 0.8718, 0.41841, 0.852, 0.44342, 0.86505, 0.46741, 0.87528, 0.503, 0.8768, 0.52312, 0.87114, 0.50542, 0.86332, 0.48313, 0.84863, 0.47232, 0.83863, 0.45472, 0.82235, 0.44159, 0.79556, 0.44006, 0.76617, 0.44587, 0.72856, 0.45106, 0.70047, 0.46633, 0.71851, 0.48313, 0.73371, 0.51306, 0.74814, 0.53841, 0.75767, 0.53902, 0.7417, 0.51853, 0.71937, 0.50888, 0.68583, 0.5049, 0.64416, 0.51512, 0.59626, 0.53159, 0.54824, 0.55771, 0.5037, 0.5861, 0.44468, 0.61366, 0.37728, 0.62899, 0.33033, 0.63807, 0.28722, 0.66136, 0.24794, 0.70338, 0.21681, 0.77209, 0.18855, 0.26037, 0.79368, 0.34233, 0.65266, 0.45051, 0.47937, 0.54012, 0.29594, 0.60994, 0.12612], "triangles": [76, 81, 77, 76, 82, 81, 82, 76, 83, 76, 75, 83, 75, 74, 83, 77, 79, 78, 77, 80, 79, 77, 81, 80, 74, 84, 83, 74, 73, 84, 73, 85, 84, 73, 72, 85, 72, 71, 85, 93, 95, 94, 92, 96, 93, 93, 96, 95, 92, 97, 96, 92, 91, 97, 91, 98, 97, 91, 90, 98, 71, 129, 85, 71, 70, 129, 101, 103, 102, 101, 100, 103, 90, 89, 98, 100, 104, 103, 100, 99, 104, 99, 105, 104, 105, 99, 106, 85, 129, 86, 89, 88, 98, 99, 98, 106, 98, 107, 106, 98, 88, 107, 60, 63, 61, 61, 63, 62, 70, 69, 129, 86, 129, 87, 60, 64, 63, 60, 59, 64, 88, 87, 107, 69, 68, 129, 64, 59, 65, 59, 58, 65, 107, 87, 108, 109, 108, 87, 68, 67, 129, 129, 67, 54, 67, 55, 54, 54, 53, 129, 129, 53, 130, 44, 130, 53, 87, 129, 130, 44, 53, 45, 65, 58, 66, 109, 87, 130, 66, 58, 67, 114, 113, 115, 58, 57, 67, 57, 56, 67, 67, 56, 55, 113, 116, 115, 113, 112, 116, 112, 111, 116, 109, 130, 110, 111, 117, 116, 111, 110, 117, 110, 118, 117, 110, 130, 118, 44, 43, 130, 43, 42, 130, 118, 130, 119, 131, 42, 41, 131, 130, 42, 119, 130, 131, 53, 52, 46, 52, 51, 46, 53, 46, 45, 119, 131, 120, 50, 47, 51, 51, 47, 46, 50, 49, 47, 49, 48, 47, 120, 131, 121, 121, 131, 122, 41, 40, 131, 122, 131, 39, 131, 40, 39, 39, 38, 122, 38, 132, 122, 122, 132, 123, 123, 132, 124, 36, 35, 37, 37, 35, 38, 124, 132, 125, 35, 34, 38, 38, 33, 132, 38, 34, 33, 33, 30, 132, 132, 30, 133, 125, 132, 126, 133, 30, 20, 126, 132, 133, 133, 20, 15, 20, 16, 15, 15, 14, 133, 33, 32, 30, 126, 133, 127, 32, 31, 30, 127, 133, 128, 128, 133, 12, 10, 12, 11, 12, 10, 128, 29, 21, 30, 30, 21, 20, 28, 22, 29, 29, 22, 21, 28, 27, 22, 128, 8, 0, 8, 10, 9, 8, 128, 10, 1, 7, 6, 1, 0, 7, 0, 8, 7, 26, 23, 27, 27, 23, 22, 1, 6, 2, 26, 25, 23, 23, 25, 24, 2, 6, 3, 4, 3, 6, 14, 13, 133, 133, 13, 12, 20, 19, 16, 19, 18, 16, 6, 5, 4, 18, 17, 16], "vertices": [2, 76, -32.63, -63.98, 0.99901, 77, -4.27, 101.74, 0.00099, 1, 76, -22.75, -99.09, 1, 1, 76, -6.76, -121.72, 1, 1, 76, 11.92, -137.68, 1, 1, 76, 29.21, -137.34, 1, 1, 76, 47.86, -116.39, 1, 1, 76, 39.56, -89.72, 1, 1, 76, 43.46, -69.78, 1, 1, 76, 47.4, -54.87, 1, 2, 76, 58.46, -43, 0.99715, 77, -84.53, 53.85, 0.00285, 2, 76, 62.38, -19.97, 0.95671, 77, -81.21, 30.73, 0.04329, 2, 76, 61.86, 6.14, 0.81803, 77, -72.71, 6.03, 0.18197, 2, 76, 51.12, 23.7, 0.5741, 77, -57.11, -7.39, 0.4259, 2, 76, 49.62, 45.91, 0.26424, 77, -48.87, -28.07, 0.73576, 2, 76, 37.9, 66.86, 0.07474, 77, -31.29, -44.42, 0.92526, 2, 76, 29.46, 82.93, 0.01022, 77, -18.33, -57.13, 0.98978, 1, 77, -12.32, -71.32, 1, 1, 77, -11.87, -95.43, 1, 1, 77, 4.93, -88.9, 1, 1, 77, 11.36, -72.87, 1, 1, 77, 13.28, -64.37, 1, 2, 77, 25.59, -78.42, 0.99933, 78, -63.54, -82.79, 0.00067, 1, 77, 34.38, -98.54, 1, 1, 77, 36.49, -119.85, 1, 1, 77, 33.58, -141.75, 1, 1, 77, 46.74, -143.57, 1, 1, 77, 55.88, -129.97, 1, 1, 77, 63.94, -110.02, 1, 2, 77, 66.1, -91.36, 0.9981, 78, -22.26, -93.03, 0.0019, 2, 77, 63.03, -73.75, 0.98453, 78, -26.49, -75.66, 0.01547, 2, 77, 58.55, -60.73, 0.93697, 78, -31.82, -62.97, 0.06303, 2, 77, 72.3, -62.49, 0.81924, 78, -17.98, -63.82, 0.18076, 2, 77, 80.92, -59.22, 0.73417, 78, -9.6, -59.98, 0.26583, 2, 77, 91.59, -51.28, 0.52289, 78, 0.53, -51.36, 0.47711, 2, 77, 106.04, -56.97, 0.22968, 78, 15.32, -56.08, 0.77032, 2, 77, 119.16, -66.77, 0.10331, 78, 29.05, -65, 0.89669, 2, 77, 139.48, -77.13, 0.06907, 78, 50.01, -74, 0.93093, 2, 77, 134.66, -59.6, 0.08283, 78, 44.05, -56.82, 0.91717, 3, 77, 125.88, -46.48, 0.08487, 78, 34.43, -44.31, 0.9138, 79, -66.02, -49.45, 0.00134, 3, 77, 145.87, -50.57, 0.00736, 78, 54.64, -47.07, 0.96165, 79, -45.66, -50.67, 0.03099, 2, 78, 78.66, -45.99, 0.80505, 79, -21.79, -47.78, 0.19495, 3, 78, 98.47, -47.05, 0.47574, 79, -1.95, -47.33, 0.52399, 80, -105.01, -46.75, 0.00027, 3, 78, 127.55, -57.15, 0.0751, 79, 27.8, -55.21, 0.89764, 80, -75.3, -54.79, 0.02726, 4, 78, 149.34, -71.11, 0.00482, 79, 50.59, -67.48, 0.85983, 80, -52.58, -67.19, 0.13508, 81, -134.68, -65.33, 0.00027, 3, 79, 71.58, -81.32, 0.67506, 80, -31.66, -81.15, 0.31789, 81, -113.96, -79.58, 0.00705, 3, 79, 88.86, -96.1, 0.50966, 80, -14.47, -96.02, 0.4696, 81, -96.98, -94.69, 0.02074, 3, 79, 102.31, -115.42, 0.44493, 80, -1.13, -115.41, 0.53755, 81, -83.9, -114.26, 0.01752, 3, 79, 114.37, -137.71, 0.44377, 80, 10.81, -137.78, 0.55297, 81, -72.27, -136.79, 0.00327, 2, 79, 121.01, -161.42, 0.4445, 80, 17.32, -161.52, 0.5555, 3, 79, 126.05, -157.99, 0.4445, 80, 22.38, -158.12, 0.55541, 81, -60.99, -157.29, 9e-05, 3, 79, 131.56, -142.22, 0.44415, 80, 27.98, -142.38, 0.55441, 81, -55.17, -141.63, 0.00144, 3, 79, 130.87, -123.81, 0.44201, 80, 27.39, -123.97, 0.55123, 81, -55.5, -123.21, 0.00676, 3, 79, 125.06, -108.67, 0.43436, 80, 21.66, -108.79, 0.54731, 81, -61.02, -107.96, 0.01833, 3, 79, 116.8, -93.16, 0.37777, 80, 13.49, -93.24, 0.5608, 81, -68.98, -92.29, 0.06143, 3, 79, 126.83, -97.73, 0.26623, 80, 23.49, -97.87, 0.60756, 81, -59.04, -97.06, 0.12621, 3, 79, 145.46, -106.96, 0.14315, 80, 42.08, -107.19, 0.62732, 81, -40.59, -106.64, 0.22953, 3, 79, 169.87, -113.55, 0.06453, 80, 66.45, -113.92, 0.59678, 81, -16.31, -113.71, 0.33869, 3, 79, 189.33, -114.91, 0.03065, 80, 85.9, -115.39, 0.56376, 81, 3.12, -115.44, 0.40558, 3, 79, 213.49, -114.07, 0.00974, 80, 110.06, -114.68, 0.53931, 81, 27.29, -115.07, 0.45094, 3, 79, 239.54, -98.59, 0.00082, 80, 136.2, -99.34, 0.53052, 81, 53.63, -100.09, 0.46867, 2, 80, 146.57, -84.75, 0.52919, 81, 64.2, -85.64, 0.47081, 2, 80, 148.42, -69.68, 0.52881, 81, 66.27, -70.61, 0.47119, 2, 80, 141.5, -70.87, 0.52886, 81, 59.33, -71.7, 0.47114, 2, 80, 138.83, -75.91, 0.52903, 81, 56.59, -76.7, 0.47097, 3, 79, 233.78, -82.14, 0.00071, 80, 130.52, -82.87, 0.52937, 81, 48.19, -83.54, 0.46993, 3, 79, 223.62, -83.65, 0.00324, 80, 120.36, -84.32, 0.52768, 81, 38, -84.85, 0.46908, 3, 79, 209.7, -82.31, 0.00986, 80, 106.45, -82.9, 0.51561, 81, 24.11, -83.24, 0.47453, 3, 79, 200.56, -81.6, 0.01572, 80, 97.31, -82.14, 0.48455, 81, 14.99, -82.36, 0.49973, 3, 79, 212.22, -76.71, 0.00664, 80, 109, -77.32, 0.35068, 81, 26.74, -77.69, 0.64268, 3, 79, 227.53, -68.56, 0.001, 80, 124.35, -69.25, 0.21223, 81, 42.21, -69.84, 0.78677, 2, 80, 138.23, -56.97, 0.10149, 81, 56.25, -57.75, 0.89851, 2, 80, 155.32, -39.42, 0.01926, 81, 73.58, -40.44, 0.98074, 1, 81, 87.12, -21.35, 1, 1, 81, 91.96, 2.16, 1, 1, 81, 95.4, 35.94, 1, 1, 81, 90.97, 59.96, 1, 1, 81, 79.12, 84.25, 1, 1, 81, 65.22, 105.66, 1, 1, 81, 49.11, 122.17, 1, 1, 81, 47.34, 115.06, 1, 1, 81, 49.76, 106.38, 1, 1, 81, 55.46, 93.65, 1, 1, 81, 53.95, 79.42, 1, 1, 81, 49.15, 60.77, 1, 1, 81, 40.66, 45.55, 1, 1, 81, 27.15, 29.09, 1, 2, 80, 93.63, 22.85, 0.06165, 81, 12.76, 22.67, 0.93835, 2, 80, 75.52, 16.54, 0.79742, 81, -5.44, 16.62, 0.20258, 2, 80, 87.2, 30.35, 0.98536, 81, 6.43, 30.26, 0.01464, 1, 80, 94.74, 47.14, 1, 1, 80, 98.41, 61.56, 1, 1, 80, 100.13, 75, 1, 1, 80, 99.34, 92.65, 1, 1, 80, 92.69, 114.23, 1, 1, 80, 85.59, 128.29, 1, 1, 80, 86.79, 114.21, 1, 1, 80, 85.83, 98.66, 1, 1, 80, 82.51, 88.04, 1, 1, 80, 77.05, 75.83, 1, 1, 80, 78.17, 88.79, 1, 1, 80, 78.18, 100.66, 1, 2, 79, 173.96, 115.46, 0, 80, 71.8, 115.07, 1, 2, 79, 167.27, 122.03, 0, 80, 65.14, 121.67, 1, 2, 79, 167.18, 113.19, 0, 80, 65.01, 112.84, 1, 1, 80, 62.58, 100.57, 1, 2, 79, 162.33, 94.27, 8e-05, 80, 60.06, 93.94, 0.99992, 2, 79, 158.29, 83.47, 0.00163, 80, 55.95, 83.16, 0.99837, 2, 79, 148.44, 71.9, 0.0152, 80, 46.04, 71.65, 0.9848, 2, 79, 135.03, 64.28, 0.06796, 80, 32.59, 64.1, 0.93204, 2, 79, 116.32, 57.58, 0.25588, 80, 13.84, 57.51, 0.74412, 2, 79, 102.18, 52.92, 0.57841, 80, -0.33, 52.92, 0.42159, 2, 79, 107.51, 63.23, 0.70686, 80, 5.07, 63.21, 0.29314, 2, 79, 111.22, 73.47, 0.74119, 80, 8.83, 73.42, 0.25881, 2, 79, 111.92, 88.69, 0.75919, 80, 9.61, 88.64, 0.24081, 2, 79, 111.26, 100.94, 0.76303, 80, 9.02, 100.89, 0.23697, 2, 79, 103.69, 97.37, 0.76173, 80, 1.43, 97.36, 0.23827, 2, 79, 97.4, 83.97, 0.75421, 80, -4.93, 84, 0.24579, 2, 79, 83.7, 72.17, 0.77235, 80, -18.69, 72.27, 0.22765, 2, 79, 65.07, 60.65, 0.88933, 80, -37.39, 60.86, 0.11067, 3, 78, 148.58, 50.01, 0.0178, 79, 40.67, 53.24, 0.96808, 80, -61.84, 53.58, 0.01412, 2, 78, 122.56, 46.98, 0.20031, 79, 14.95, 48.25, 0.79969, 2, 78, 96.57, 48.59, 0.64339, 79, -11.09, 47.89, 0.35661, 2, 78, 63.2, 48.18, 0.97053, 79, -44.33, 44.96, 0.02947, 2, 77, 123.32, 43.94, 0.06133, 78, 25.91, 45.74, 0.93867, 3, 76, -111.63, 23.87, 0.00158, 77, 97.86, 42.34, 0.38679, 78, 0.61, 42.47, 0.61164, 3, 76, -88.96, 20.3, 0.0255, 77, 75.19, 38.78, 0.82183, 78, -21.77, 37.43, 0.15267, 3, 76, -68.18, 10.43, 0.15817, 77, 52.38, 41.81, 0.83787, 78, -44.73, 38.95, 0.00397, 2, 76, -51.49, -7.82, 0.51867, 77, 30.91, 54.06, 0.48133, 2, 76, -36.08, -37.89, 0.93833, 77, 7.02, 77.96, 0.06167, 3, 79, 184.06, 0.15, 0, 80, 81.27, -0.29, 0.49528, 81, 0.08, -0.3, 0.50472, 3, 79, 101.78, -1.28, 0.56064, 80, -1.03, -1.27, 0.43935, 81, -82.22, -0.14, 0, 2, 78, 103.15, -0.04, 0.54948, 79, -0.84, -0.1, 0.45052, 2, 77, 93.6, -0.67, 0.54901, 78, -0.8, -0.73, 0.45099, 2, 76, -4.81, 34.42, 0.00241, 77, -0.58, -0.46, 0.99759], "hull": 129, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 0, 256], "width": 442, "height": 524}}, "yopushou2": {"yopushou2": {"type": "mesh", "uvs": [0.0213, 0.1308, 0.17412, 0.06873, 0.2423, 0.04638, 0.30343, 0.00292, 0.42568, 0, 0.51267, 0.01782, 0.56675, 0.07866, 0.66314, 0.15688, 0.70546, 0.26489, 0.69606, 0.33815, 0.689, 0.41264, 0.72192, 0.47348, 0.83007, 0.52314, 0.86497, 0.57815, 0.93315, 0.64271, 1, 0.73707, 1, 0.81033, 0.96842, 0.82274, 0.87202, 0.82274, 0.85792, 0.88358, 0.80384, 0.97928, 0.62751, 1, 0.4841, 0.95197, 0.43002, 0.89361, 0.43708, 0.84519, 0.57814, 0.79429, 0.55933, 0.72228, 0.54287, 0.64571, 0.46059, 0.57991, 0.36655, 0.52404, 0.18316, 0.42223, 0.07121, 0.3226, 0, 0.22576], "triangles": [20, 21, 19, 25, 19, 21, 22, 23, 25, 21, 22, 25, 25, 23, 24, 19, 25, 18, 16, 17, 15, 15, 17, 18, 14, 15, 18, 18, 25, 26, 18, 26, 14, 14, 27, 13, 14, 26, 27, 27, 12, 13, 27, 11, 12, 27, 28, 11, 28, 29, 11, 29, 10, 11, 10, 29, 9, 9, 29, 30, 7, 31, 6, 2, 6, 1, 6, 4, 5, 4, 6, 2, 2, 3, 4, 30, 31, 9, 1, 6, 31, 9, 31, 8, 8, 31, 7, 31, 32, 1, 1, 32, 0], "vertices": [1, 47, -13.2, -15.26, 1, 1, 47, -16.92, 2.48, 1, 1, 47, -17.72, 9.98, 1, 1, 47, -22.2, 18.51, 1, 1, 47, -17.68, 29.09, 1, 1, 47, -11.28, 35.08, 1, 1, 47, 0.68, 34.96, 1, 1, 47, 17.16, 37.09, 1, 1, 47, 36.21, 32.33, 1, 1, 47, 47.57, 25.88, 1, 1, 47, 59.23, 19.53, 1, 1, 47, 70.33, 17.62, 1, 1, 47, 82.71, 22.94, 1, 1, 47, 92.95, 21.65, 1, 2, 47, 106.09, 22.44, 0.92915, 48, -22.2, 9.11, 0.07085, 2, 47, 123.95, 20.81, 0.39746, 48, -8.82, 21.05, 0.60254, 2, 47, 135.69, 15.16, 0.12237, 48, 3.34, 25.77, 0.87763, 2, 47, 136.4, 11.52, 0.10305, 48, 6.47, 23.8, 0.89695, 2, 47, 132.47, 3.36, 0.02652, 48, 9.75, 15.35, 0.97348, 1, 48, 20.33, 18.04, 1, 1, 48, 38.05, 19.46, 1, 1, 48, 47.48, 5.35, 1, 1, 48, 44.39, -10.32, 1, 1, 48, 36.55, -18.81, 1, 1, 48, 28.27, -21.31, 1, 2, 47, 115.92, -19.33, 0.10378, 48, 15.03, -12.23, 0.89622, 2, 47, 103.6, -15.36, 0.70665, 48, 3.72, -18.52, 0.29335, 2, 47, 90.65, -10.85, 0.9895, 48, -8.43, -24.89, 0.0105, 1, 47, 76.74, -12.73, 1, 1, 47, 63.95, -16.38, 1, 1, 47, 40.14, -24.05, 1, 1, 47, 19.6, -25.84, 1, 1, 47, 1.16, -24.4, 1], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64], "width": 94, "height": 178}}, "fa9": {"fa9": {"x": -12.34, "y": -43.4, "rotation": -88.85, "width": 121, "height": 230}}, "zuoshou3": {"zuoshou3": {"x": 37.66, "y": -15.63, "rotation": 111.85, "width": 106, "height": 154}}, "hdj2": {"hdj2": {"type": "mesh", "uvs": [0, 0.07935, 0.08867, 0.19016, 0.20206, 0.32931, 0.34121, 0.52258, 0.3876, 0.66947, 0.3979, 0.94262, 0.51902, 1, 0.65044, 1, 0.84113, 0.86016, 1, 0.71843, 1, 0.57412, 0.84887, 0.37662, 0.71487, 0.21943, 0.54221, 0.13181, 0.35925, 0.06997, 0.07579, 0, 0, 0.00297, 0.29998, 0.17562, 0.55252, 0.41012], "triangles": [0, 16, 15, 17, 15, 14, 1, 15, 17, 0, 15, 1, 2, 1, 17, 18, 13, 12, 18, 12, 11, 2, 17, 18, 13, 17, 14, 18, 17, 13, 3, 2, 18, 4, 3, 18, 8, 11, 10, 8, 18, 11, 9, 8, 10, 4, 18, 8, 6, 5, 4, 4, 7, 6, 8, 7, 4], "vertices": [1, 40, -0.36, -2.7, 1, 2, 40, 6.41, -5.64, 0.9609, 41, -7.69, -8.28, 0.0391, 2, 40, 15, -9.29, 0.37872, 41, 1.6, -9.23, 0.62128, 3, 40, 26.09, -14.8, 0.00256, 41, 13.82, -11.22, 0.76481, 42, 0.35, -11.97, 0.23263, 2, 41, 20.93, -14.92, 0.21723, 42, 8.3, -13, 0.78277, 2, 41, 31.35, -24.58, 0.00294, 42, 21.4, -18.51, 0.99706, 1, 42, 26.76, -14.06, 1, 1, 42, 29.65, -7.86, 1, 1, 42, 27.24, 4.19, 1, 1, 42, 24.04, 14.79, 1, 1, 42, 17.24, 17.96, 1, 1, 42, 4.61, 15.17, 1, 2, 41, 16.41, 13.66, 0.16099, 42, -5.74, 12.3, 0.83901, 3, 40, 25.85, 8.05, 0.00033, 41, 6.84, 10.54, 0.7481, 42, -13.66, 6.09, 0.25157, 3, 40, 15.94, 6.45, 0.50264, 41, -2.16, 6.08, 0.48953, 42, -20.59, -1.18, 0.00783, 1, 40, 1.2, 2.79, 1, 1, 40, -2.21, 0.81, 1, 3, 40, 15.77, 0.15, 0.85625, 41, -0.45, 0.02, 0.14373, 42, -16.91, -6.29, 2e-05, 1, 42, -0.31, 0.46, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 52, "height": 52}}, "hdj": {"hdj": {"type": "mesh", "uvs": [0, 0.2028, 0.00441, 0.36096, 0.0247, 0.53786, 0.07246, 0.71267, 0.13692, 0.87499, 0.22049, 0.94158, 0.34106, 0.92494, 0.39836, 0.84169, 0.46163, 0.73972, 0.49864, 0.67313, 0.56191, 0.76469, 0.61921, 0.73764, 0.66283, 0.8438, 0.74879, 0.94994, 0.84071, 1, 0.91234, 0.9874, 0.95173, 0.84588, 0.978, 0.72518, 0.99471, 0.53372, 0.98635, 0.30064, 0.97083, 0.18618, 0.88846, 0.10086, 0.79535, 0.15913, 0.71297, 0.27983, 0.64493, 0.38597, 0.56256, 0.34435, 0.49093, 0.34435, 0.46825, 0.38597, 0.40815, 0.30016, 0.33533, 0.20027, 0.25296, 0.15033, 0.16939, 0.12535, 0.06314, 0.12535, 0.44984, 0.5105, 0.46609, 0.62556, 0.67325, 0.53351, 0.66919, 0.65035], "triangles": [1, 0, 32, 33, 28, 27, 35, 24, 23, 31, 2, 1, 25, 34, 33, 25, 24, 35, 25, 27, 26, 27, 25, 33, 9, 34, 25, 35, 9, 25, 32, 31, 1, 30, 3, 2, 21, 20, 19, 18, 23, 19, 35, 11, 9, 36, 11, 35, 19, 22, 21, 19, 23, 22, 23, 18, 35, 34, 7, 33, 8, 34, 9, 10, 9, 11, 3, 29, 33, 33, 29, 28, 8, 7, 34, 12, 11, 36, 17, 35, 18, 17, 36, 35, 16, 36, 17, 13, 12, 36, 4, 3, 33, 31, 30, 2, 29, 3, 30, 7, 4, 33, 7, 5, 4, 6, 5, 7, 16, 13, 36, 16, 14, 13, 15, 14, 16], "vertices": [1, 34, 39.15, -22.48, 1, 1, 34, 39.61, -10.77, 1, 2, 34, 38.15, 2.5, 0.9999, 33, 53.47, 11.9, 0.0001, 2, 34, 33.14, 15.93, 0.96501, 33, 45.29, 23.66, 0.03499, 2, 34, 25.91, 28.62, 0.84841, 33, 35.13, 34.16, 0.15159, 2, 34, 15.61, 34.48, 0.72078, 33, 23.7, 37.27, 0.27922, 3, 32, -19.5, 35.6, 0.00569, 34, 0.01, 34.61, 0.49204, 33, 8.55, 33.53, 0.50227, 3, 32, -15.19, 26.99, 0.03793, 34, -7.9, 29.13, 0.32581, 33, 2.26, 26.25, 0.63626, 3, 32, -9.71, 17.32, 0.1996, 34, -16.69, 22.32, 0.10705, 33, -4.57, 17.48, 0.69335, 4, 32, -6, 11.55, 0.54208, 34, -21.88, 17.83, 0.01978, 33, -8.48, 11.84, 0.43498, 35, -23.46, -9.64, 0.00316, 4, 32, -14.47, 5.15, 0.82808, 34, -29.42, 25.3, 1e-05, 33, -17.64, 17.2, 0.04246, 35, -15.61, -16.77, 0.12945, 4, 32, -14.2, -2.5, 0.56829, 33, -24.6, 14.02, 0.00158, 35, -8.13, -15.1, 0.4236, 36, -26.57, -14.19, 0.00653, 3, 32, -23.14, -6.18, 0.22642, 35, -2.86, -23.2, 0.70781, 36, -21.58, -22.47, 0.06577, 3, 32, -33.32, -15.19, 0.06717, 35, 7.87, -31.54, 0.68634, 36, -11.14, -31.17, 0.24649, 3, 32, -39.63, -25.88, 0.01479, 35, 19.55, -35.76, 0.54854, 36, 0.38, -35.8, 0.43667, 3, 32, -40.84, -35.09, 0.00307, 35, 28.82, -35.24, 0.46715, 36, 9.67, -35.6, 0.52977, 3, 32, -31.8, -42.43, 1e-05, 35, 34.36, -25.01, 0.32375, 36, 15.56, -25.56, 0.67624, 2, 35, 38.15, -16.23, 0.14784, 36, 19.64, -16.92, 0.85216, 2, 35, 40.93, -2.18, 0.00233, 36, 22.91, -2.97, 0.99767, 2, 35, 40.62, 15.1, 0.02774, 36, 23.2, 14.31, 0.97226, 2, 35, 38.99, 23.65, 0.07542, 36, 21.87, 22.91, 0.92458, 2, 35, 28.66, 30.43, 0.18613, 36, 11.78, 30.04, 0.81387, 2, 35, 16.47, 26.66, 0.40887, 36, -0.54, 26.69, 0.59113, 3, 32, 16.01, -22.02, 0.00139, 35, 5.45, 18.21, 0.8073, 36, -11.84, 18.63, 0.19132, 4, 32, 10.37, -11.68, 0.2303, 33, -23.65, -12.19, 0.02753, 35, -3.66, 10.75, 0.73424, 36, -21.21, 11.49, 0.00792, 3, 32, 15.8, -2.03, 0.53537, 33, -12.66, -13.51, 0.33156, 35, -14.14, 14.3, 0.13307, 3, 32, 17.91, 6.96, 0.30316, 33, -3.55, -12, 0.68658, 35, -23.37, 14.71, 0.01026, 3, 32, 15.58, 10.51, 0.13735, 33, -1.16, -8.49, 0.86187, 35, -26.43, 11.76, 0.00078, 2, 34, -12.67, -10.68, 0.11953, 33, 7.52, -13.5, 0.88047, 2, 34, -3.96, -18.87, 0.59894, 33, 17.99, -19.26, 0.40106, 2, 34, 6.3, -23.48, 0.90357, 33, 29.08, -21.18, 0.09643, 2, 34, 16.88, -26.27, 0.99327, 33, 40.02, -21.25, 0.00673, 1, 34, 30.53, -27.47, 1, 3, 32, 7.15, 14.93, 0.01842, 34, -16.66, 5.29, 0.00128, 33, -0.31, 0.99, 0.9803, 3, 32, -1.62, 14.83, 0.30186, 34, -18.01, 13.96, 0.02999, 33, -3.77, 9.05, 0.66815, 2, 32, -1.09, -12.74, 0.0183, 35, -0.5, -0.32, 0.9817, 3, 32, -9.39, -10.25, 0.2809, 35, -1.4, -8.93, 0.71438, 36, -19.63, -8.26, 0.00472], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 54, 66, 66, 68, 68, 18, 48, 70, 70, 72, 72, 22], "width": 129, "height": 74}}, "youjiao1": {"youjiao1": {"type": "mesh", "uvs": [0, 0.17354, 0.11057, 0.06426, 0.33431, 0, 0.62057, 0.0016, 0.79495, 0.08029, 0.90682, 0.22163, 0.97592, 0.42126, 0.93973, 0.56527, 0.86405, 0.73138, 0.78179, 0.93684, 0.59424, 0.9995, 0.32773, 1, 0.0645, 0.96307, 0.04805, 0.8465, 0.01515, 0.67018, 0, 0.45307, 0, 0.29861], "triangles": [4, 16, 0, 4, 15, 16, 4, 1, 2, 4, 2, 3, 5, 6, 15, 14, 15, 6, 0, 1, 4, 5, 15, 4, 7, 14, 6, 8, 14, 7, 13, 14, 8, 9, 13, 8, 13, 11, 12, 13, 9, 11, 9, 10, 11], "vertices": [1, 18, 29.22, -35.72, 1, 1, 18, 6.43, -25.09, 1, 1, 18, -6.76, -4.09, 1, 1, 18, -6.02, 22.53, 1, 1, 18, 10.74, 38.5, 1, 1, 18, 40.57, 48.46, 1, 1, 18, 82.59, 54.26, 1, 1, 18, 112.78, 50.44, 1, 1, 18, 147.55, 42.88, 1, 1, 18, 190.58, 34.59, 1, 1, 18, 203.48, 16.95, 1, 1, 18, 203.21, -7.83, 1, 1, 18, 195.09, -32.19, 1, 1, 18, 170.59, -33.36, 1, 1, 18, 133.52, -35.87, 1, 1, 18, 87.91, -36.59, 1, 1, 18, 55.48, -36.11, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 93, "height": 210}}, "fa5": {"fa5": {"type": "mesh", "uvs": [0.11601, 0.97275, 0.18651, 0.86033, 0.34514, 0.76887, 0.53461, 0.67169, 0.78136, 0.55165, 0.97964, 0.51354, 1, 0.33825, 0.93557, 0.15724, 0.78136, 0.05054, 0.56104, 0.00862, 0.24379, 0, 0, 0.0372, 0.03229, 0.08484, 0.1777, 0.13056, 0.37157, 0.23346, 0.42886, 0.34206, 0.31909, 0.4878, 0.21775, 0.57354, 0.13844, 0.67453, 0, 0.83077, 0, 0.9813, 0.04591, 1, 0.53212, 0.51749, 0.67088, 0.26249], "triangles": [1, 19, 2, 0, 19, 1, 20, 19, 0, 21, 20, 0, 3, 22, 4, 17, 16, 22, 17, 3, 18, 3, 17, 22, 2, 18, 3, 19, 18, 2, 23, 7, 6, 15, 14, 23, 6, 15, 23, 6, 22, 15, 22, 16, 15, 22, 6, 4, 5, 4, 6, 12, 11, 10, 13, 12, 10, 9, 13, 10, 14, 9, 8, 23, 14, 8, 14, 13, 9, 7, 23, 8], "vertices": [1, 49, 150.43, -79.37, 1, 1, 49, 158.79, -81.46, 1, 1, 56, -1.79, -2.46, 1, 1, 56, 7.32, -4.79, 1, 2, 57, -0.25, -8.15, 0.71443, 56, 18.75, -8.04, 0.28557, 2, 57, 4.06, -13.59, 0.98128, 56, 24.05, -12.53, 0.01872, 2, 58, -10.43, -5.19, 0.03982, 57, 16.79, -11, 0.96018, 2, 58, 2.21, -10.08, 0.91828, 57, 29.25, -5.67, 0.08172, 1, 58, 11.51, -9.74, 1, 1, 58, 17.72, -5.18, 1, 1, 58, 23.33, 3.3, 1, 1, 58, 24.84, 11.44, 1, 1, 58, 21.27, 12.3, 1, 2, 58, 16.01, 9.96, 0.99903, 57, 25.13, 18.31, 0.00097, 2, 58, 6.32, 8.38, 0.77025, 57, 19.3, 10.41, 0.22975, 2, 58, -1.56, 10.8, 0.08533, 57, 11.97, 6.63, 0.91467, 2, 57, 0.65, 7.35, 0.4979, 56, 16.58, 7.34, 0.5021, 2, 57, -6.3, 8.92, 0.02005, 56, 9.45, 7.5, 0.97995, 1, 56, 1.62, 6.54, 1, 1, 49, 160.85, -75.45, 1, 1, 49, 149.72, -75.67, 1, 1, 49, 148.36, -77.17, 1, 2, 57, 0.22, 0.2, 0.63831, 56, 17.56, 0.24, 0.36169, 2, 58, -0.32, 1.15, 0.07793, 57, 19.6, 0.6, 0.92207], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42], "width": 32, "height": 74}}, "qun3": {"qun3": {"type": "mesh", "uvs": [0, 0.04282, 0.04595, 0.01269, 0.21364, 0, 0.26192, 0.05234, 0.35593, 0.12371, 0.45502, 0.19349, 0.52362, 0.24583, 0.58218, 0.32447, 0.62537, 0.40377, 0.70668, 0.51796, 0.7956, 0.59885, 0.88961, 0.71462, 0.97092, 0.76855, 1, 0.83357, 0.99887, 0.9097, 0.91502, 0.96204, 0.75495, 1, 0.66602, 1, 0.59996, 0.93666, 0.53898, 0.8304, 0.45259, 0.7289, 0.40178, 0.62755, 0.32555, 0.48482, 0.30523, 0.35635, 0.22138, 0.26278, 0.11721, 0.17713, 0.01558, 0.10259, 0.33845, 0.22481, 0.42417, 0.35474, 0.53927, 0.52748, 0.68866, 0.70264], "triangles": [27, 25, 4, 26, 0, 1, 1, 3, 26, 3, 1, 2, 25, 3, 4, 25, 26, 3, 19, 20, 30, 11, 12, 18, 11, 18, 19, 18, 12, 13, 14, 15, 13, 17, 18, 16, 30, 11, 19, 13, 15, 18, 15, 16, 18, 30, 29, 9, 30, 9, 10, 21, 29, 30, 30, 10, 11, 20, 21, 30, 22, 23, 28, 8, 29, 28, 8, 28, 7, 29, 8, 9, 22, 28, 29, 21, 22, 29, 27, 4, 5, 24, 25, 27, 6, 28, 27, 6, 27, 5, 28, 6, 7, 23, 24, 27, 28, 23, 27], "vertices": [1, 1, 12.34, 22.59, 1, 1, 1, 16.84, 27.32, 1, 1, 1, 33.28, 29.31, 1, 1, 1, 38.01, 21.09, 1, 1, 1, 47.22, 9.89, 1, 1, 12, 0.33, 12.81, 1, 2, 12, 10.58, 15.57, 0.95276, 13, -12.26, 15.27, 0.04724, 2, 12, 24.2, 15.74, 0.42395, 13, 1.35, 15.78, 0.57605, 3, 12, 37.29, 14.49, 0.01832, 13, 14.47, 14.85, 0.97267, 14, -14.18, 15.87, 0.00901, 2, 13, 34.08, 15.25, 0.28115, 14, 5.41, 14.91, 0.71885, 3, 13, 49.17, 18.36, 0.00198, 14, 20.68, 16.97, 0.91647, 15, -8.52, 17.76, 0.08155, 2, 14, 41.05, 17, 0.0854, 15, 11.79, 16.05, 0.9146, 2, 14, 52.2, 20.3, 0.00021, 15, 23.18, 18.39, 0.99979, 1, 15, 33.36, 15.45, 1, 1, 15, 43.47, 9.08, 1, 1, 15, 46.15, -2.23, 1, 1, 15, 42.98, -18.71, 1, 1, 15, 38.4, -26.12, 1, 2, 14, 59.39, -24.05, 0.00072, 15, 26.54, -26.41, 0.99928, 2, 14, 41.8, -21.86, 0.14811, 15, 9.21, -22.73, 0.85189, 3, 13, 54.96, -20.54, 0.00078, 14, 23.76, -22.24, 0.75474, 15, -8.8, -21.57, 0.24448, 3, 13, 38.36, -18.97, 0.17158, 14, 7.31, -19.52, 0.82139, 15, -24.96, -17.45, 0.00702, 3, 12, 36.83, -17.53, 0.00111, 13, 14.81, -17.16, 0.97185, 14, -16.06, -16.09, 0.02703, 2, 12, 17.62, -11.07, 0.76161, 13, -4.56, -11.18, 0.23839, 1, 12, 0.86, -12.53, 1, 1, 1, 23.83, 1.5, 1, 1, 1, 13.87, 13.2, 1, 1, 12, 0.13, 0.38, 1, 2, 12, 22.18, -0.33, 0.58903, 13, -0.27, -0.34, 0.41097, 2, 13, 29.1, -0.46, 0.69347, 14, -0.65, -0.41, 0.30653, 2, 14, 30.5, 0.27, 0.547, 15, -0.16, 0.28, 0.453], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52], "width": 98, "height": 157}}, "qun4": {"qun4": {"type": "mesh", "uvs": [0.03716, 0.04084, 0, 0.07833, 0.02157, 0.14394, 0.06523, 0.2283, 0.09953, 0.32282, 0.1276, 0.44199, 0.13695, 0.55179, 0.12136, 0.6723, 0.08886, 0.78025, 0.07639, 0.9262, 0.22295, 0.87264, 0.41318, 0.95432, 0.58781, 1, 0.73437, 0.94494, 0.88718, 0.85791, 0.98073, 0.87264, 1, 0.78025, 0.95003, 0.68319, 0.89702, 0.5734, 0.82529, 0.47713, 0.76916, 0.38474, 0.72239, 0.26888, 0.65066, 0.15507, 0.58518, 0.05732, 0.41678, 0, 0.17355, 0.00644, 0.41035, 0.21049, 0.4972, 0.36453, 0.52364, 0.54127, 0.56518, 0.75044], "triangles": [26, 25, 24, 26, 24, 23, 26, 2, 25, 25, 2, 1, 25, 1, 0, 14, 29, 16, 10, 8, 29, 15, 14, 16, 9, 8, 10, 13, 29, 14, 11, 10, 29, 13, 11, 29, 12, 11, 13, 28, 19, 18, 29, 28, 18, 29, 18, 17, 7, 6, 28, 29, 7, 28, 29, 17, 16, 29, 8, 7, 28, 27, 20, 28, 20, 19, 5, 27, 28, 6, 5, 28, 26, 23, 22, 3, 2, 26, 26, 22, 21, 4, 3, 26, 27, 26, 21, 4, 26, 27, 27, 21, 20, 5, 4, 27], "vertices": [1, 1, -7.83, 17.08, 1, 1, 1, -10.66, 10.45, 1, 1, 1, -9.02, -1.17, 1, 2, 4, -2.21, -26.57, 0.98746, 5, -26.7, -29.76, 0.01254, 3, 4, 14.71, -27.27, 0.8001, 5, -9.81, -28.55, 0.19961, 6, -40.97, -28.07, 0.00029, 3, 4, 35.82, -29.27, 0.20383, 5, 11.39, -28.18, 0.70486, 6, -19.77, -27.94, 0.09131, 4, 4, 55.02, -32.35, 0.00668, 5, 30.81, -29.08, 0.44691, 6, -0.36, -29.08, 0.53429, 7, -36.46, -31.19, 0.01211, 3, 5, 51.97, -32.03, 0.05341, 6, 20.77, -32.28, 0.73658, 7, -15.19, -33.19, 0.21001, 3, 5, 70.81, -36.08, 0.00029, 6, 39.55, -36.55, 0.37867, 7, 3.81, -36.39, 0.62104, 2, 6, 65.18, -39.94, 0.15424, 7, 29.59, -38.33, 0.84576, 2, 6, 56.8, -27.96, 0.14164, 7, 20.54, -26.84, 0.85836, 2, 6, 72.56, -14.93, 0.00177, 7, 35.55, -12.95, 0.99823, 1, 7, 44.14, 0, 1, 1, 7, 34.83, 11.51, 1, 2, 6, 58.99, 22.54, 0.10742, 7, 19.88, 23.71, 0.89258, 2, 6, 62.26, 29.38, 0.13789, 7, 22.76, 30.71, 0.86211, 2, 6, 46.12, 32.38, 0.31114, 7, 6.47, 32.8, 0.68886, 3, 5, 59.12, 30.57, 0.00623, 6, 28.66, 30.23, 0.71922, 7, -10.84, 29.67, 0.27455, 3, 5, 39.42, 28.17, 0.19732, 6, 8.93, 28.06, 0.78303, 7, -30.41, 26.39, 0.01965, 3, 4, 52.22, 21.53, 0.00332, 5, 21.99, 24.15, 0.72625, 6, -8.55, 24.25, 0.27044, 3, 4, 35.35, 20.52, 0.22582, 5, 5.34, 21.26, 0.76354, 6, -25.24, 21.56, 0.01064, 2, 4, 14.55, 21.02, 0.92387, 5, -15.39, 19.42, 0.07613, 1, 4, -6.27, 19.59, 1, 1, 1, 33.82, 14.16, 1, 1, 1, 21.02, 24.31, 1, 1, 1, 2.53, 23.17, 1, 1, 4, -0.2, -0.23, 1, 2, 4, 27.83, 0.94, 0.47173, 5, 0.06, 0.96, 0.52827, 2, 5, 31.4, 0.36, 0.34231, 6, 0.58, 0.35, 0.65769, 2, 6, 37.73, -0.01, 0.51754, 7, -0.07, -0.02, 0.48246], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50], "width": 76, "height": 177}}, "qun5": {"qun5": {"type": "mesh", "uvs": [0.89912, 0.00024, 0.97917, 0.0466, 1, 0.12978, 0.9835, 0.21842, 0.93807, 0.28176, 0.88182, 0.35403, 0.83638, 0.43176, 0.81475, 0.51085, 0.77148, 0.61858, 0.71956, 0.70585, 0.68927, 0.79312, 0.66114, 0.93221, 0.65249, 1, 0.48158, 0.98676, 0.28037, 0.94721, 0.0835, 0.89403, 0, 0.83812, 0.04239, 0.75221, 0.14152, 0.61344, 0.24104, 0.48799, 0.35787, 0.37889, 0.46821, 0.2848, 0.58936, 0.17162, 0.67373, 0.06662, 0.75811, 0.00389, 0.75595, 0.19617, 0.65859, 0.32571, 0.53527, 0.51526, 0.42927, 0.69389], "triangles": [1, 25, 23, 2, 25, 1, 24, 0, 1, 1, 23, 24, 8, 27, 7, 28, 19, 27, 9, 28, 27, 18, 19, 28, 8, 9, 27, 10, 28, 9, 17, 14, 15, 28, 17, 18, 16, 17, 15, 28, 13, 14, 28, 14, 17, 13, 28, 10, 11, 13, 10, 12, 13, 11, 6, 26, 5, 6, 27, 26, 26, 20, 21, 7, 27, 6, 27, 20, 26, 19, 20, 27, 22, 23, 25, 3, 25, 2, 4, 25, 3, 26, 22, 25, 5, 26, 25, 21, 22, 26, 4, 5, 25], "vertices": [1, 1, 8.85, 24.27, 1, 1, 1, 17.18, 16.62, 1, 1, 1, 19.34, 2.9, 1, 2, 8, -5.99, 24.39, 0.99568, 9, -29.38, 24.27, 0.00432, 2, 8, 5.48, 24.05, 0.93239, 9, -17.92, 23.98, 0.06761, 3, 8, 18.73, 23.24, 0.61737, 9, -4.66, 23.22, 0.38095, 10, -37.15, 24.98, 0.00167, 3, 8, 32.39, 23.81, 0.15855, 9, 9, 23.85, 0.78481, 10, -23.48, 24.97, 0.05664, 3, 8, 45.3, 26.75, 0.01068, 9, 21.89, 26.84, 0.71571, 10, -10.45, 27.36, 0.27361, 3, 9, 40.02, 29.6, 0.26837, 11, -21.56, 31.2, 0.02416, 10, 7.78, 29.27, 0.70747, 3, 9, 55.39, 30.21, 0.04859, 11, -6.25, 29.8, 0.23608, 10, 23.16, 29.17, 0.71533, 3, 9, 69.88, 32.9, 0.00139, 11, 8.47, 30.57, 0.65653, 10, 37.76, 31.18, 0.34208, 2, 11, 31.38, 33.81, 0.97622, 10, 60.31, 36.35, 0.02378, 2, 11, 42.4, 35.9, 0.99905, 10, 71.12, 39.36, 0.00095, 1, 11, 44.99, 18.18, 1, 1, 11, 44.23, -3.73, 1, 1, 11, 41.18, -25.79, 1, 2, 11, 34.57, -36.61, 0.99699, 10, 69.46, -33.55, 0.00301, 2, 11, 19.74, -36.1, 0.94403, 10, 54.63, -34.3, 0.05597, 3, 9, 64.69, -31.1, 0.00664, 11, -5.07, -32.21, 0.47274, 10, 29.59, -32.52, 0.52062, 3, 9, 41.59, -29.61, 0.24965, 11, -27.77, -27.69, 0.04675, 10, 6.59, -29.95, 0.7036, 3, 8, 43.48, -25.48, 0.01131, 9, 20.29, -25.4, 0.8317, 10, -14.5, -24.75, 0.15699, 3, 8, 24.73, -20.86, 0.34867, 9, 1.52, -20.86, 0.65038, 10, -33.03, -19.34, 0.00096, 2, 8, 2.65, -16.41, 0.97572, 9, -20.58, -16.5, 0.02428, 1, 1, -14.59, 13.32, 1, 1, 1, -5.81, 23.67, 1, 1, 8, -0.28, 1.14, 1, 2, 8, 23.35, 0.01, 0.48598, 9, 0.05, 0.01, 0.51402, 2, 9, 33.85, 0.34, 0.43309, 10, 0.25, 0.33, 0.56691, 2, 11, -0.17, 0.16, 0.44863, 10, 31.72, 0.15, 0.55137], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "width": 104, "height": 165}}, "qun7": {"qun7": {"type": "mesh", "uvs": [0, 0, 0.1108, 0.01043, 0.28361, 0.04631, 0.41285, 0.08959, 0.56284, 0.14902, 0.65739, 0.21294, 0.74542, 0.27686, 0.82042, 0.36882, 0.86932, 0.4675, 0.93127, 0.55945, 0.99322, 0.63458, 1, 0.70972, 0.96388, 0.77251, 0.91823, 0.8241, 0.80737, 0.89699, 0.68347, 0.96539, 0.57914, 1, 0.51719, 1, 0.48132, 0.89811, 0.44872, 0.76354, 0.41611, 0.62337, 0.35416, 0.50974, 0.28895, 0.40881, 0.20744, 0.30677, 0.10963, 0.18902, 0.05094, 0.08922, 0, 0.03091, 0.48516, 0.29582, 0.60898, 0.51241, 0.68681, 0.70222], "triangles": [3, 24, 25, 26, 0, 1, 25, 26, 1, 25, 1, 2, 3, 25, 2, 12, 29, 11, 13, 29, 12, 19, 29, 13, 14, 19, 13, 18, 19, 14, 15, 18, 14, 17, 18, 15, 16, 17, 15, 28, 8, 9, 20, 21, 28, 9, 20, 28, 29, 9, 10, 29, 20, 9, 29, 10, 11, 19, 20, 29, 22, 23, 27, 28, 21, 22, 27, 7, 22, 7, 27, 6, 28, 7, 8, 7, 28, 22, 24, 3, 4, 27, 24, 4, 27, 4, 5, 27, 5, 6, 23, 24, 27], "vertices": [1, 1, 26.34, -10.69, 1, 1, 1, 39.53, -14.3, 1, 1, 1, 60.09, -26.71, 1, 1, 1, 75.47, -41.69, 1, 1, 24, 29.04, 26.76, 1, 2, 24, 53.68, 29.67, 0.95539, 25, -24.71, 26.03, 0.04461, 2, 24, 78.05, 31.86, 0.46502, 25, -1.01, 32.12, 0.53498, 3, 24, 110.99, 29.23, 0.009, 25, 31.92, 34.84, 0.98608, 26, -43.46, 32.7, 0.00493, 2, 25, 66.55, 34.07, 0.62634, 26, -8.84, 33.67, 0.37366, 3, 25, 99.19, 35.28, 0.03298, 26, 23.69, 36.51, 0.9664, 27, -47.02, 34.8, 0.00062, 2, 26, 50.47, 40.17, 0.89618, 27, -20.4, 39.44, 0.10382, 2, 26, 76.32, 37.32, 0.44478, 27, 5.54, 37.55, 0.55522, 2, 26, 97.23, 30.02, 0.07146, 27, 26.71, 31.03, 0.92854, 2, 26, 114.14, 22.14, 0.00084, 27, 43.9, 23.78, 0.99916, 1, 27, 67.62, 8.05, 1, 1, 27, 89.64, -9.06, 1, 1, 27, 100.26, -22.65, 1, 1, 27, 99.5, -29.98, 1, 1, 27, 63.99, -30.58, 1, 2, 26, 85.56, -30.24, 0.08308, 27, 17.28, -29.63, 0.91692, 3, 25, 109.27, -29.11, 0.00109, 26, 37, -27.29, 0.99504, 27, -31.36, -28.47, 0.00386, 2, 25, 69.27, -28.89, 0.6406, 26, -2.97, -29.07, 0.3594, 2, 24, 102.12, -34.9, 0.01979, 25, 33.51, -29.88, 0.98021, 2, 24, 65.63, -31.79, 0.66709, 25, -3, -32.7, 0.33291, 1, 24, 23.38, -28.63, 1, 1, 1, 32.4, -41.56, 1, 1, 1, 26.34, -21.38, 1, 2, 24, 73.5, 0.53, 0.56204, 25, -0.44, 0.46, 0.43796, 2, 25, 75.93, 0.71, 0.14807, 26, 2.2, 0.82, 0.85193, 2, 26, 68.53, 0.78, 0.64741, 27, -0.89, 0.75, 0.35259], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52], "width": 119, "height": 346}}, "qun8": {"qun8": {"type": "mesh", "uvs": [0, 0.93725, 0.03472, 0.92485, 0.11934, 0.89462, 0.16033, 0.86052, 0.22459, 0.86885, 0.27544, 0.87544, 0.37478, 0.89675, 0.441, 0.87544, 0.51209, 0.89675, 0.61616, 0.91807, 0.67645, 0.92415, 0.7218, 0.92872, 0.7798, 0.93777, 0.84479, 0.94791, 0.92518, 0.97489, 1, 1, 1, 0.92446, 0.9804, 0.72411, 0.96778, 0.55574, 0.93309, 0.32981, 0.88579, 0.1657, 0.82272, 0.07405, 0.72969, 0.01864, 0.654, 0, 0.49001, 0, 0.3891, 0.06339, 0.27714, 0.18701, 0.17307, 0.349, 0.10843, 0.53016, 0.04062, 0.74543, 0, 0.87757, 0.30346, 0.66194, 0.57381, 0.6913, 0.84536, 0.70762, 0.32881, 0.35361, 0.85622, 0.43681, 0.5895, 0.4205, 0.62812, 0.17253, 0.40001, 0.1448, 0.78743, 0.20842], "triangles": [26, 25, 38, 38, 24, 37, 38, 25, 24, 0, 30, 1, 1, 30, 2, 2, 30, 29, 3, 29, 28, 28, 27, 31, 31, 34, 36, 31, 27, 34, 34, 38, 36, 27, 26, 34, 34, 26, 38, 19, 39, 20, 39, 21, 20, 39, 22, 21, 14, 16, 15, 16, 14, 17, 14, 13, 17, 13, 33, 17, 33, 18, 17, 33, 35, 18, 39, 35, 36, 35, 19, 18, 19, 35, 39, 36, 37, 39, 37, 22, 39, 37, 23, 22, 36, 38, 37, 37, 24, 23, 13, 12, 33, 12, 11, 33, 11, 10, 33, 10, 9, 33, 8, 32, 9, 9, 32, 35, 33, 9, 35, 36, 35, 32, 8, 7, 32, 7, 6, 31, 3, 2, 29, 6, 5, 31, 7, 31, 32, 5, 4, 31, 4, 3, 31, 31, 3, 28, 32, 31, 36], "vertices": [1, 22, 121.84, 38.71, 1, 4, 30, 142, -160.66, 0.07806, 31, 78.85, -161.23, 0.18837, 29, 209.18, -154, 0.02449, 22, 113.94, 47, 0.70908, 3, 30, 131.23, -134.9, 0.26833, 31, 68.26, -135.39, 0.64749, 29, 197.24, -128.76, 0.08418, 3, 30, 121.46, -123.05, 0.26678, 31, 58.58, -123.47, 0.6469, 29, 186.94, -117.37, 0.08631, 3, 30, 120.59, -102.48, 0.24373, 31, 57.86, -102.89, 0.68443, 29, 185.13, -96.85, 0.07184, 3, 30, 119.9, -86.2, 0.2255, 31, 57.29, -86.61, 0.71412, 29, 183.7, -80.62, 0.06039, 3, 30, 120.54, -54.12, 0.14666, 31, 58.15, -54.54, 0.82677, 29, 182.87, -48.55, 0.02657, 3, 30, 112.65, -33.88, 0.07472, 31, 50.41, -34.24, 0.91591, 29, 174.07, -28.7, 0.00937, 3, 30, 114.52, -10.73, 0.00333, 31, 52.45, -11.11, 0.99636, 29, 174.88, -5.48, 0.00031, 2, 30, 114.95, 22.84, 0.0613, 31, 53.11, 22.46, 0.9387, 4, 30, 113.73, 42.09, 0.17966, 31, 52.03, 41.72, 0.81783, 29, 171.67, 47.25, 0.00044, 28, 228.1, 52.1, 0.00207, 4, 30, 112.81, 56.57, 0.26868, 31, 51.22, 56.2, 0.72692, 29, 170.09, 61.67, 0.00077, 28, 226.11, 66.47, 0.00363, 4, 30, 112.39, 75.19, 0.34727, 31, 50.93, 74.83, 0.64274, 29, 168.81, 80.25, 0.00238, 28, 224.31, 85, 0.00761, 4, 30, 111.91, 96.05, 0.43533, 31, 50.6, 95.69, 0.54841, 29, 167.38, 101.07, 0.00419, 28, 222.29, 105.77, 0.01207, 5, 30, 114.69, 122.33, 0.20985, 31, 53.58, 121.95, 0.26437, 29, 168.96, 127.45, 0.00202, 28, 223.12, 132.18, 0.00582, 27, 16.04, -30.77, 0.51794, 1, 27, 24.41, -7.64, 1, 1, 27, 6.68, -5.8, 1, 1, 26, 28.15, -5.6, 1, 1, 25, 61.75, -3.42, 1, 1, 25, 7.3, -4.17, 1, 1, 24, 38.84, -6.06, 1, 1, 24, 11.59, -17.46, 1, 2, 30, -100.28, 29.57, 0.00914, 28, 15.6, 23.77, 0.99086, 2, 29, -41.46, 0.41, 0.00012, 28, 16.38, -0.76, 0.99988, 1, 20, -5.92, 34.13, 1, 1, 20, 27.81, 23.06, 1, 1, 21, 0.45, 20.29, 1, 1, 21, 50.84, 15.36, 1, 1, 22, 21, 22.68, 1, 1, 22, 75.93, 28.23, 1, 1, 22, 109.5, 31.92, 1, 4, 30, 68.77, -84.26, 0.07072, 31, 6.17, -84.31, 0.50926, 29, 132.53, -81.03, 0.01447, 21, 86.97, 92.03, 0.40555, 5, 30, 63.79, 2.11, 0.00246, 31, 1.81, 2.1, 0.73578, 29, 123.61, 5.03, 0.00025, 28, 181.26, 8.53, 0.17335, 25, 69.31, -132.89, 0.08817, 5, 30, 55.71, 88.45, 0.12046, 31, -5.65, 88.49, 0.32951, 29, 111.59, 90.9, 0.00086, 28, 166.81, 94.03, 0.02789, 25, 89.53, -48.57, 0.52128, 6, 30, -4.42, -86.24, 0.01334, 31, -67.03, -85.76, 0.20944, 29, 59.51, -86.35, 0.00269, 28, 119.77, -84.63, 0.02737, 25, -23.77, -194.5, 0.01392, 21, 22.97, 56.49, 0.73324, 5, 30, -8.07, 83.1, 0.0128, 31, -69.47, 83.61, 0.17277, 29, 48.12, 82.65, 0.00014, 28, 103.6, 83.98, 0.03561, 25, 27.44, -33.04, 0.77868, 6, 30, -0.2, -1.7, 0.00216, 31, -62.21, -1.26, 0.43831, 29, 59.86, -1.71, 0.00034, 28, 117.72, -0.01, 0.46671, 25, 7.51, -115.85, 0.0519, 21, -12.32, 133.43, 0.04057, 6, 30, -59.86, 2.47, 0.00096, 31, -121.83, 3.34, 0.1797, 29, 0.08, -0.27, 0.00021, 28, 57.92, -0.27, 0.78123, 25, -47.61, -92.64, 0.02128, 21, -67.17, 109.58, 0.01663, 7, 30, -56.35, -70.51, 0.00331, 31, -118.85, -69.66, 0.06948, 29, 6.92, -73.01, 0.00067, 28, 66.82, -72.79, 0.08966, 25, -67.84, -162.85, 0.00561, 21, -30.37, 46.47, 0.17812, 20, 38.39, 39.47, 0.65315, 7, 30, -58.45, 53.97, 0.00225, 31, -120.05, 54.83, 0.09117, 29, -0.87, 51.24, 0.0001, 28, 55.52, 51.19, 0.2902, 25, -29.65, -44.36, 0.12331, 24, 37.45, -38.99, 0.48691, 21, -89.69, 155.92, 0.00607], "hull": 31, "edges": [4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 6, 8, 8, 10, 0, 2, 2, 4, 22, 24, 24, 26, 26, 28, 28, 30, 18, 20, 20, 22], "width": 319, "height": 236}}, "qugan": {"qugan": {"type": "mesh", "uvs": [0.30238, 0.13546, 0.2868, 0.2127, 0.30757, 0.3006, 0.33249, 0.36187, 0.37195, 0.37918, 0.4083, 0.44977, 0.43322, 0.50038, 0.45503, 0.57496, 0.35368, 0.74702, 0.23842, 0.96575, 0.27778, 1, 0.37711, 1, 0.49611, 0.97056, 0.55421, 0.84677, 0.61231, 0.72179, 0.65542, 0.73861, 0.71278, 0.77844, 0.78118, 0.82891, 0.8018, 0.76281, 0.82054, 0.73998, 0.84396, 0.80488, 0.87208, 0.90943, 0.9864, 0.96952, 0.97234, 0.93227, 0.98734, 0.89501, 0.93767, 0.78685, 0.91237, 0.72796, 0.85708, 0.63302, 0.81862, 0.57268, 0.76801, 0.47654, 0.79238, 0.42006, 0.81018, 0.33713, 0.82892, 0.27103, 0.8308, 0.1797, 0.81112, 0.11841, 0.75302, 0.08716, 0.69305, 0.06673, 0.63027, 0.09197, 0.60028, 0.08596, 0.5628, 0.06192, 0.50751, 0.05351, 0.4035, 0.0475, 0.33322, 0.08716, 0.48099, 0.46542, 0.57409, 0.44441, 0.63292, 0.45491, 0.73123, 0.50745], "triangles": [33, 35, 34, 2, 0, 42, 1, 0, 2, 35, 32, 37, 33, 32, 35, 4, 3, 2, 41, 2, 42, 41, 4, 2, 4, 41, 40, 32, 31, 37, 38, 40, 39, 44, 38, 37, 37, 36, 35, 37, 45, 44, 37, 31, 45, 5, 4, 43, 40, 44, 4, 40, 38, 44, 30, 45, 31, 44, 43, 4, 29, 45, 30, 6, 5, 43, 46, 45, 29, 7, 6, 43, 14, 44, 45, 14, 45, 46, 15, 14, 46, 19, 28, 27, 19, 27, 26, 7, 43, 44, 14, 7, 44, 28, 46, 29, 19, 16, 28, 28, 15, 46, 28, 16, 15, 19, 18, 16, 20, 19, 26, 20, 26, 25, 17, 16, 18, 13, 7, 14, 8, 7, 13, 21, 20, 25, 21, 25, 24, 23, 21, 24, 21, 23, 22, 12, 8, 13, 10, 9, 8, 8, 11, 10, 12, 11, 8], "vertices": [1, 3, 94.94, 84.48, 1, 1, 3, 79.18, 92.75, 1, 2, 1, -106.69, 90.28, 4e-05, 3, 58.74, 91.44, 0.99996, 2, 1, -99.57, 76.61, 0.00129, 3, 43.81, 87.62, 0.99871, 3, 1, -88.28, 72.75, 0.0062, 2, 63.95, 83.24, 1e-05, 3, 37.48, 77.52, 0.99379, 3, 1, -77.88, 57.01, 0.03658, 2, 47.47, 74.07, 0.00613, 3, 19.78, 70.99, 0.95729, 3, 1, -70.76, 45.73, 0.09104, 2, 35.67, 67.82, 0.03165, 3, 7.16, 66.63, 0.8773, 3, 1, -64.52, 29.09, 0.28089, 2, 18.62, 62.86, 0.10442, 3, -10.46, 64.36, 0.61469, 3, 1, -93.51, -9.28, 0.77143, 2, -17.44, 94.68, 0.03665, 3, -41.19, 101.34, 0.19192, 2, 1, -126.47, -58.05, 0.89975, 3, -81.15, 144.58, 0.10025, 2, 1, -115.21, -65.69, 0.89897, 3, -91.15, 135.36, 0.10103, 3, 1, -86.81, -65.69, 0.8852, 2, -74.2, 92.28, 0.00269, 3, -97.64, 107.71, 0.11211, 3, 1, -52.77, -59.12, 0.84954, 2, -70.24, 57.84, 0.01913, 3, -99.02, 73.07, 0.13133, 3, 1, -36.15, -31.52, 0.75691, 2, -43.97, 39.18, 0.07592, 3, -75.95, 50.59, 0.16717, 3, 1, -19.54, -3.65, 0.6398, 2, -17.44, 20.49, 0.2349, 3, -52.61, 28.05, 0.12531, 3, 1, -7.21, -7.4, 0.95148, 2, -22.12, 8.49, 0.03111, 3, -59.08, 16.9, 0.01741, 2, 1, 9.2, -16.28, 0.9899, 2, -32.22, -7.2, 0.0101, 2, 1, 28.76, -27.54, 0.97816, 2, -44.93, -25.85, 0.02184, 2, 1, 34.66, -12.8, 0.86589, 2, -30.68, -32.85, 0.13411, 2, 1, 40.02, -7.7, 0.78709, 2, -26.01, -38.58, 0.21291, 2, 1, 46.72, -22.18, 0.88694, 2, -40.95, -44.16, 0.11306, 2, 1, 54.76, -45.49, 0.97087, 2, -64.8, -50.41, 0.02913, 1, 1, 87.45, -58.89, 1, 2, 1, 83.43, -50.58, 0.997, 2, -72.06, -78.62, 0.003, 2, 1, 87.72, -42.28, 0.99277, 2, -64.1, -83.52, 0.00723, 2, 1, 73.52, -18.16, 0.92585, 2, -38.97, -71.19, 0.07415, 2, 1, 66.28, -5.02, 0.84333, 2, -25.33, -64.97, 0.15667, 2, 1, 50.47, 16.15, 0.56709, 2, -3.02, -50.81, 0.43291, 2, 1, 39.47, 29.6, 0.27989, 2, 11.23, -40.86, 0.72011, 3, 1, 24.99, 51.04, 0.01318, 2, 33.71, -28.06, 0.9757, 3, -9.54, -27.8, 0.01112, 2, 2, 45.74, -35.96, 0.85135, 3, 1.14, -37.46, 0.14865, 2, 2, 63.79, -42.44, 0.54893, 3, 17.98, -46.64, 0.45107, 2, 2, 78.09, -48.9, 0.36278, 3, 31.1, -55.23, 0.63722, 2, 2, 98.35, -50.98, 0.22031, 3, 50.81, -60.4, 0.77969, 2, 2, 112.41, -46.41, 0.16358, 3, 65.4, -58.05, 0.83642, 2, 2, 120.62, -30.37, 0.099, 3, 75.98, -43.46, 0.901, 2, 2, 126.46, -13.61, 0.04199, 3, 84.33, -27.8, 0.95801, 2, 2, 122.21, 4.72, 0.00591, 3, 82.96, -9.04, 0.99409, 2, 2, 124.2, 13.17, 0.00015, 3, 86.22, -0.99, 0.99985, 1, 3, 93.89, 8.22, 1, 1, 3, 99.33, 23.18, 1, 1, 3, 107.43, 51.84, 1, 1, 3, 103.41, 73.43, 1, 3, 1, -57.09, 53.52, 0.06468, 2, 42.41, 53.61, 0.02362, 3, 11.63, 51.55, 0.91169, 3, 1, -30.47, 58.21, 0.0185, 2, 45.06, 26.7, 0.00869, 3, 10.11, 24.56, 0.97281, 2, 1, -13.64, 55.87, 0.0025, 3, 3.99, 8.71, 0.9975, 2, 1, 14.47, 44.15, 0.01129, 2, 27.64, -17.05, 0.98871], "hull": 43, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 0, 84, 12, 86, 86, 88, 88, 90, 90, 92, 92, 58], "width": 286, "height": 223}}, "youshopu1": {"youshopu1": {"x": 33.5, "y": 1.41, "rotation": 59.31, "width": 57, "height": 85}}, "zuoshou1": {"zuoshou1": {"x": 21.53, "y": 6.86, "rotation": 64, "width": 61, "height": 66}}, "qunjia": {"qunjia": {"x": -116.66, "y": -26.69, "width": 156, "height": 126}}, "zuoshou": {"zuoshou": {"x": 46.45, "y": -8.64, "rotation": 87.59, "width": 98, "height": 148}}}}], "animations": {"animation": {"bones": {"qugan": {"translate": [{"y": -8.42, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "y": -8.7, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "y": -2.25, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "y": -8.42, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.1, "y": -8.7, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "y": -2.25, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 4, "y": -8.42, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 4.1, "y": -8.7, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "y": -2.25, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6, "y": -8.42}]}, "qugan3": {"translate": [{"y": 0.33, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "y": 2.57, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "y": 0.33, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "y": 2.57, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4, "y": 0.33, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.2, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "y": 2.57, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6, "y": 0.33}]}, "qugan2": {"translate": [{"x": 0.93, "y": -0.07, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 3.85, "y": -0.29, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "x": 0.93, "y": -0.07, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.3, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": 3.85, "y": -0.29, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "x": 0.93, "y": -0.07, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 3.85, "y": -0.29, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6, "x": 0.93, "y": -0.07}]}, "tou": {"rotate": [{"angle": -2.59, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -3.82, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -0.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -2.59, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "angle": -3.82, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": -0.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -2.59, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "angle": -3.82, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -0.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": -2.59}]}, "zuoshou3": {"rotate": [{"angle": -1.28, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4}, {"time": 1.4, "angle": -3.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -1.28, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4}, {"time": 3.4, "angle": -3.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -1.28, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4}, {"time": 5.4, "angle": -3.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": -1.28}], "translate": [{"x": 1.07, "y": -0.25, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 2.92, "y": -0.68, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "x": 1.07, "y": -0.25, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "x": 2.92, "y": -0.68, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "x": 1.07, "y": -0.25, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "x": 2.92, "y": -0.68, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "x": 1.07, "y": -0.25}]}, "zuoshou": {"rotate": [{"angle": -5.94, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -10.92, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -5.94, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 2.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": -10.92, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "angle": -5.94, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 4.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": -10.92, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "angle": -5.94}]}, "zuoshou1": {"rotate": [{"angle": -18.48, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -24.38, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -18.48, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "angle": -24.38, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": -18.48, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.7, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "angle": -24.38, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6, "angle": -18.48}]}, "youshopu1": {"rotate": [{"angle": -2.6}, {"time": 0.4, "angle": -4.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -1.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.4}, {"time": 2, "angle": -2.6}, {"time": 2.4, "angle": -4.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3, "angle": -1.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4}, {"time": 4, "angle": -2.6}, {"time": 4.4, "angle": -4.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5, "angle": -1.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.4}, {"time": 6, "angle": -2.6}], "translate": [{"x": 1.47, "y": -0.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "x": 2.33, "y": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": 0.86, "y": -0.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "x": 1.47, "y": -0.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "x": 2.33, "y": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3, "x": 0.86, "y": -0.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "x": 1.47, "y": -0.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "x": 2.33, "y": -0.55, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5, "x": 0.86, "y": -0.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "x": 1.47, "y": -0.35}]}, "yopushou2": {"rotate": [{"angle": -2.78, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "angle": -6.1, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": -3.32, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 1.5333, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -2.78, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 2.5333, "angle": -6.1, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 3, "angle": -3.32, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 3.5333, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "angle": -2.78, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 4.5333, "angle": -6.1, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 5, "angle": -3.32, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 5.5333, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "angle": -2.78}]}, "yopushou3": {"rotate": [{"angle": -0.94, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "angle": -3.9, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1, "angle": -2.96, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -0.94, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.7, "angle": -3.9, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3, "angle": -2.96, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 3.7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": -0.94, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.7, "angle": -3.9, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5, "angle": -2.96, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 5.7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6, "angle": -0.94}]}, "hdj5": {"rotate": [{"angle": 2.52, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4}, {"time": 1.4, "angle": 6.86, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": 2.52, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4}, {"time": 3.4, "angle": 6.86, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 2.52, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4}, {"time": 5.4, "angle": 6.86, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": 2.52}]}, "hdj6": {"rotate": [{"angle": 7.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 15.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 7.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 15.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 7.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 15.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": 7.83}]}, "hdj1": {"rotate": [{"angle": -2.68, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4}, {"time": 1.4, "angle": -7.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -2.68, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4}, {"time": 3.4, "angle": -7.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -2.68, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4}, {"time": 5.4, "angle": -7.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": -2.68}]}, "hdj4": {"rotate": [{"angle": -8.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5}, {"time": 1.5, "angle": -16.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -8.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5}, {"time": 3.5, "angle": -16.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -8.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5}, {"time": 5.5, "angle": -16.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": -8.38}]}, "hdj7": {"rotate": [{"angle": -26.67, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6}, {"time": 1.6, "angle": -42.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -26.67, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.6}, {"time": 3.6, "angle": -42.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": -26.67, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.6}, {"time": 5.6, "angle": -42.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": -26.67}]}, "hdj": {"rotate": [{"angle": -3.36, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -9.13, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -3.36, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": -9.13, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -3.36, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -9.13, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": -3.36}]}, "hdj2": {"rotate": [{"angle": -10.01, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5}, {"time": 1.5, "angle": -20.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -10.01, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5}, {"time": 3.5, "angle": -20.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -10.01, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5}, {"time": 5.5, "angle": -20.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": -10.01}]}, "hdj11": {"rotate": [{"angle": 19.16, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 30.31, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": 19.16, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.6, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 30.31, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": 19.16, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 30.31, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": 19.16}]}, "hdj9": {"rotate": [{"angle": 2.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4}, {"time": 1.4, "angle": 7.06, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": 2.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4}, {"time": 3.4, "angle": 7.06, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 2.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4}, {"time": 5.4, "angle": 7.06, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": 2.6}]}, "hdj10": {"rotate": [{"angle": 9.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5}, {"time": 1.5, "angle": 19.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 9.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5}, {"time": 3.5, "angle": 19.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 9.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5}, {"time": 5.5, "angle": 19.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": 9.55}]}, "zuojiao1": {"scale": [{"x": 0.981}]}, "fa2": {"rotate": [{"angle": 0.21, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 0.5, "angle": -4.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.6, "angle": -4.73}, {"time": 1.6, "angle": 3.15, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": 0.21, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 2.5, "angle": -4.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.6, "angle": -4.73}, {"time": 3.6, "angle": 3.15, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": 0.21, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 4.5, "angle": -4.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 4.6, "angle": -4.73}, {"time": 5.6, "angle": 3.15, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": 0.21}]}, "fa10": {"rotate": [{"angle": 3.16, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.5, "angle": -5.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.7, "angle": -7.53}, {"time": 1.7, "angle": 6.63, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": 3.16, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 2.5, "angle": -5.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7, "angle": -7.53}, {"time": 3.7, "angle": 6.63, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": 3.16, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 4.5, "angle": -5.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.7, "angle": -7.53}, {"time": 5.7, "angle": 6.63, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6, "angle": 3.16}]}, "fa11": {"rotate": [{"angle": 10.27, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.5, "angle": -4.64, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "angle": -10.39, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 13.38, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 10.27, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 2.5, "angle": -4.64, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8, "angle": -10.39, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": 13.38, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 10.27, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 4.5, "angle": -4.64, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.8, "angle": -10.39, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": 13.38, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6, "angle": 10.27}]}, "fa12": {"rotate": [{"angle": 17.25, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.5, "angle": -2.92, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.9, "angle": -15.47}, {"time": 1.9, "angle": 18.63, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": 17.25, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": -2.92, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9, "angle": -15.47}, {"time": 3.9, "angle": 18.63, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 4, "angle": 17.25, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 4.5, "angle": -2.92, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.9, "angle": -15.47}, {"time": 5.9, "angle": 18.63, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6, "angle": 17.25}]}, "fa7": {"rotate": [{"angle": 3.63, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 0.5, "angle": -3.76, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.6, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 8.35, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": 3.63, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 2.5, "angle": -3.76, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.6, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 8.35, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": 3.63, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 4.5, "angle": -3.76, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 4.6, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 8.35, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": 3.63}]}, "fa8": {"rotate": [{"angle": 9.74, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.5, "angle": -5.56, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.7, "angle": -8.75}, {"time": 1.7, "angle": 15.75, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": 9.74, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 2.5, "angle": -5.56, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7, "angle": -8.75}, {"time": 3.7, "angle": 15.75, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": 9.74, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 4.5, "angle": -5.56, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.7, "angle": -8.75}, {"time": 5.7, "angle": 15.75, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6, "angle": 9.74}]}, "fa9": {"rotate": [{"angle": 11.3, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.5, "angle": -13.62, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "angle": -23.24}, {"time": 1.8, "angle": 16.5, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 11.3, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 2.5, "angle": -13.62, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8, "angle": -23.24}, {"time": 3.8, "angle": 16.5, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 11.3, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 4.5, "angle": -13.62, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.8, "angle": -23.24}, {"time": 5.8, "angle": 16.5, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6, "angle": 11.3}]}, "fa3": {"rotate": [{"angle": -1.02, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 0.5, "angle": 1.5, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.6, "angle": 1.68}, {"time": 1.6, "angle": -2.63, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -1.02, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 2.5, "angle": 1.5, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.6, "angle": 1.68}, {"time": 3.6, "angle": -2.63, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": -1.02, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 4.5, "angle": 1.5, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 4.6, "angle": 1.68}, {"time": 5.6, "angle": -2.63, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": -1.02}]}, "fa4": {"rotate": [{"angle": -2.38, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.5, "angle": 2.95, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.7, "angle": 4.06, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -4.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -2.38, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 2.5, "angle": 2.95, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7, "angle": 4.06, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "angle": -4.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": -2.38, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 4.5, "angle": 2.95, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.7, "angle": 4.06, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "angle": -4.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6, "angle": -2.38}]}, "fa5": {"rotate": [{"angle": -13.83, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.5, "angle": 3.48, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "angle": 10.17}, {"time": 1.8, "angle": -17.44, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": -13.83, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 2.5, "angle": 3.48, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8, "angle": 10.17}, {"time": 3.8, "angle": -17.44, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": -13.83, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 4.5, "angle": 3.48, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.8, "angle": 10.17}, {"time": 5.8, "angle": -17.44, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6, "angle": -13.83}]}, "fa6": {"rotate": [{"angle": -17.59, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.5, "angle": 2.89, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.9, "angle": 15.61, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -18.98, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": -17.59, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": 2.89, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9, "angle": 15.61, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "angle": -18.98, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 4, "angle": -17.59, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 4.5, "angle": 2.89, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.9, "angle": 15.61, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": -18.98, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6, "angle": -17.59}]}, "fa16": {"rotate": [{"angle": -0.74, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 0.5, "angle": -3.22, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.6, "angle": -3.4, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 0.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -0.74, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 2.5, "angle": -3.22, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.6, "angle": -3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 0.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": -0.74, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 4.5, "angle": -3.22, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 4.6, "angle": -3.4, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 0.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": -0.74}]}, "fa17": {"rotate": [{"angle": 5.97, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.5, "angle": -3.98, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.7, "angle": -6.05, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 9.87, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": 5.97, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 2.5, "angle": -3.98, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7, "angle": -6.05, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "angle": 9.87, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": 5.97, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 4.5, "angle": -3.98, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.7, "angle": -6.05, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "angle": 9.87, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6, "angle": 5.97}]}, "fa18": {"rotate": [{"angle": 7.17, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.5, "angle": -5.89, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "angle": -10.93, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 9.89, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 7.17, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 2.5, "angle": -5.89, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8, "angle": -10.93, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": 9.89, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 7.17, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 4.5, "angle": -5.89, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.8, "angle": -10.93, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": 9.89, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6, "angle": 7.17}]}, "fa19": {"rotate": [{"angle": 11.67, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.5, "angle": -6.02, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.9, "angle": -17.02, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 12.88, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": 11.67, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": -6.02, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9, "angle": -17.02, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "angle": 12.88, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 4, "angle": 11.67, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 4.5, "angle": -6.02, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.9, "angle": -17.02, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": 12.88, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6, "angle": 11.67}]}, "qun8": {"rotate": [{"angle": -0.35, "curve": 0.355, "c2": 0.43, "c3": 0.691, "c4": 0.78}, {"time": 0.1333, "angle": -1.78, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.2333, "angle": -2.29}, {"time": 1.2333, "angle": 9.73, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": -0.35, "curve": 0.355, "c2": 0.43, "c3": 0.691, "c4": 0.78}, {"time": 2.1333, "angle": -1.78, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.2333, "angle": -2.29}, {"time": 3.2333, "angle": 9.73, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 4, "angle": -0.35, "curve": 0.355, "c2": 0.43, "c3": 0.691, "c4": 0.78}, {"time": 4.1333, "angle": -1.78, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 4.2333, "angle": -2.29}, {"time": 5.2333, "angle": 9.73, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 6, "angle": -0.35}]}, "qun9": {"rotate": [{"angle": -1.57, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.1333, "angle": -4.39, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.3667, "angle": -7.38, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 10.61, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2, "angle": -1.57, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 2.1333, "angle": -4.39, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 2.3667, "angle": -7.38, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": 10.61, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "angle": -1.57, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 4.1333, "angle": -4.39, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 4.3667, "angle": -7.38, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "angle": 10.61, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 6, "angle": -1.57}]}, "qun10": {"rotate": [{"angle": -0.56, "curve": 0.334, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.1333, "angle": -4.75, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 10.32, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -0.56, "curve": 0.334, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 2.1333, "angle": -4.75, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.5333, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": 10.32, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "angle": -0.56, "curve": 0.334, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 4.1333, "angle": -4.75, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.5333, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": 10.32, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "angle": -0.56}]}, "qun11": {"rotate": [{"angle": 6.55, "curve": 0.322, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.1333, "angle": -0.48, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.7333, "angle": -27.12}, {"time": 1.7333, "angle": 15.02, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 6.55, "curve": 0.322, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 2.1333, "angle": -0.48, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.7333, "angle": -27.12}, {"time": 3.7333, "angle": 15.02, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 4, "angle": 6.55, "curve": 0.322, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 4.1333, "angle": -0.48, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.7333, "angle": -27.12}, {"time": 5.7333, "angle": 15.02, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 6, "angle": 6.55}]}, "qun4": {"rotate": [{"angle": -3.93, "curve": 0.346, "c2": 0.4, "c3": 0.68, "c4": 0.74}, {"time": 0.0667, "angle": -4.38, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": -4.73}, {"time": 1.1667, "angle": 3.6, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -3.93, "curve": 0.346, "c2": 0.4, "c3": 0.68, "c4": 0.74}, {"time": 2.0667, "angle": -4.38, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.1667, "angle": -4.73}, {"time": 3.1667, "angle": 3.6, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -3.93, "curve": 0.346, "c2": 0.4, "c3": 0.68, "c4": 0.74}, {"time": 4.0667, "angle": -4.38, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 4.1667, "angle": -4.73}, {"time": 5.1667, "angle": 3.6, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": -3.93}]}, "qun5": {"rotate": [{"angle": -5.33, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 0.0667, "angle": -7.02, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.3, "angle": -10.75}, {"time": 1.3, "angle": 11.67, "curve": 0.243, "c3": 0.655, "c4": 0.62}, {"time": 2, "angle": -5.33, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 2.0667, "angle": -7.02, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 2.3, "angle": -10.75}, {"time": 3.3, "angle": 11.67, "curve": 0.243, "c3": 0.655, "c4": 0.62}, {"time": 4, "angle": -5.33, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 4.0667, "angle": -7.02, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 4.3, "angle": -10.75}, {"time": 5.3, "angle": 11.67, "curve": 0.243, "c3": 0.655, "c4": 0.62}, {"time": 6, "angle": -5.33}]}, "qun6": {"rotate": [{"angle": -5.43, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.0667, "angle": -8.03, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4667, "angle": -18.98}, {"time": 1.4667, "angle": 10.8, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": -5.43, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 2.0667, "angle": -8.03, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4667, "angle": -18.98}, {"time": 3.4667, "angle": 10.8, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "angle": -5.43, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 4.0667, "angle": -8.03, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4667, "angle": -18.98}, {"time": 5.4667, "angle": 10.8, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 6, "angle": -5.43}]}, "qun7": {"rotate": [{"angle": 8.68, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 0.0667, "angle": 4.71, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6667, "angle": -24.63, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 21.78, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 8.68, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 2.0667, "angle": 4.71, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.6667, "angle": -24.63, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 21.78, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 8.68, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 4.0667, "angle": 4.71, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.6667, "angle": -24.63, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 21.78, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": 8.68}]}, "qun3": {"rotate": [{"angle": -6.59, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": -6.85, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -0.85, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": -6.59, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.1, "angle": -6.85, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -0.85, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 4, "angle": -6.59, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 4.1, "angle": -6.85, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": -0.85, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6, "angle": -6.59}]}, "qun12": {"rotate": [{"angle": -7.62, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "angle": -9.74}, {"time": 1.2333, "angle": 2.99, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": -7.62, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 2.2333, "angle": -9.74}, {"time": 3.2333, "angle": 2.99, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 4, "angle": -7.62, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 4.2333, "angle": -9.74}, {"time": 5.2333, "angle": 2.99, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 6, "angle": -7.62}]}, "qun13": {"rotate": [{"angle": -7.18, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -15.05}, {"time": 1.4, "angle": 6.33, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -7.18, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "angle": -15.05}, {"time": 3.4, "angle": 6.33, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -7.18, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "angle": -15.05}, {"time": 5.4, "angle": 6.33, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": -7.18}]}, "qun14": {"rotate": [{"angle": 0.42, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "angle": -22.65}, {"time": 1.6, "angle": 13.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": 0.42, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.6, "angle": -22.65}, {"time": 3.6, "angle": 13.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": 0.42, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.6, "angle": -22.65}, {"time": 5.6, "angle": 13.84, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": 0.42}]}, "qun17": {"rotate": [{"angle": -2.56, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -3.09}, {"time": 1.1333, "angle": 4.45, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": -2.56, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.1333, "angle": -3.09}, {"time": 3.1333, "angle": 4.45, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "angle": -2.56, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.1333, "angle": -3.09}, {"time": 5.1333, "angle": 4.45, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 6, "angle": -2.56}]}, "qun18": {"rotate": [{"angle": -2.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -6.18}, {"time": 1.3333, "angle": 7.64, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -6.18}, {"time": 3.3333, "angle": 7.64, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -2.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": -6.18}, {"time": 5.3333, "angle": 7.64, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -2.26}]}, "qun19": {"rotate": [{"angle": 3.78, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "angle": -9.5}, {"time": 1.6, "angle": 11.51, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": 3.78, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.6, "angle": -9.5}, {"time": 3.6, "angle": 11.51, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": 3.78, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.6, "angle": -9.5}, {"time": 5.6, "angle": 11.51, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": 3.78}]}, "qun20": {"rotate": [{"angle": 12.5, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.9333, "angle": -13.71, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 13.11, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": 12.5, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 2.9333, "angle": -13.71, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 13.11, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 4, "angle": 12.5, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 4.9333, "angle": -13.71, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": 13.11, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 6, "angle": 12.5}]}, "qun21": {"rotate": [{"angle": -1.66, "curve": 0.349, "c2": 0.4, "c3": 0.684, "c4": 0.74}, {"time": 0.1, "angle": -2.42, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.2333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 5.15, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": -1.66, "curve": 0.349, "c2": 0.4, "c3": 0.684, "c4": 0.74}, {"time": 2.1, "angle": -2.42, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.2333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": 5.15, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 4, "angle": -1.66, "curve": 0.349, "c2": 0.4, "c3": 0.684, "c4": 0.74}, {"time": 4.1, "angle": -2.42, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.2333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "angle": 5.15, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 6, "angle": -1.66}]}, "qun22": {"rotate": [{"angle": -0.47, "curve": 0.338, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.1, "angle": -2.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4333, "angle": -5.78}, {"time": 1.4333, "angle": 7.15, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -0.47, "curve": 0.338, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 2.1, "angle": -2.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.4333, "angle": -5.78}, {"time": 3.4333, "angle": 7.15, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 4, "angle": -0.47, "curve": 0.338, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 4.1, "angle": -2.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.4333, "angle": -5.78}, {"time": 5.4333, "angle": 7.15, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 6, "angle": -0.47}]}, "qun23": {"rotate": [{"angle": 4.2, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 0.1, "angle": 1.81, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.7, "angle": -10.08, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 8.73, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": 4.2, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 2.1, "angle": 1.81, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.7, "angle": -10.08, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "angle": 8.73, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": 4.2, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 4.1, "angle": 1.81, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.7, "angle": -10.08, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "angle": 8.73, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6, "angle": 4.2}]}, "qun24": {"rotate": [{"angle": 7.14}, {"time": 0.0333, "angle": 7.87, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 0.1, "angle": 7.37, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 1.0333, "angle": -14.15}, {"time": 2, "angle": 7.14}, {"time": 2.0333, "angle": 7.87, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2.1, "angle": 7.37, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 3.0333, "angle": -14.15}, {"time": 4, "angle": 7.14}, {"time": 4.0333, "angle": 7.87, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 4.1, "angle": 7.37, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 5.0333, "angle": -14.15}, {"time": 6, "angle": 7.14}]}, "qun1": {"rotate": [{"angle": -2.18, "curve": 0.356, "c2": 0.42, "c3": 0.696, "c4": 0.78}, {"time": 0.2, "angle": -3.49, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.3333, "angle": -3.92}, {"time": 1.3333, "angle": 2.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.18, "curve": 0.356, "c2": 0.42, "c3": 0.696, "c4": 0.78}, {"time": 2.2, "angle": -3.49, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.3333, "angle": -3.92}, {"time": 3.3333, "angle": 2.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -2.18, "curve": 0.356, "c2": 0.42, "c3": 0.696, "c4": 0.78}, {"time": 4.2, "angle": -3.49, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.3333, "angle": -3.92}, {"time": 5.3333, "angle": 2.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -2.18}]}, "qun2": {"rotate": [{"angle": 2.66, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.2, "angle": -1.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5333, "angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 9.74, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": 2.66, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 2.2, "angle": -1.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5333, "angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": 9.74, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "angle": 2.66, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 4.2, "angle": -1.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.5333, "angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "angle": 9.74, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "angle": 2.66}]}, "qun15": {"rotate": [{"angle": 7, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.2, "angle": 1.66, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "angle": -12.42}, {"time": 1.8, "angle": 9.85, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 7, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 2.2, "angle": 1.66, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.8, "angle": -12.42}, {"time": 3.8, "angle": 9.85, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4, "angle": 7, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 4.2, "angle": 1.66, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.8, "angle": -12.42}, {"time": 5.8, "angle": 9.85, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6, "angle": 7}]}, "qun16": {"rotate": [{"angle": 9.43}, {"time": 0.1333, "angle": 13.47, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 0.2, "angle": 12.78, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 1.1333, "angle": -16.86}, {"time": 2, "angle": 9.43}, {"time": 2.1333, "angle": 13.47, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2.2, "angle": 12.78, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 3.1333, "angle": -16.86}, {"time": 4, "angle": 9.43}, {"time": 4.1333, "angle": 13.47, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 4.2, "angle": 12.78, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 5.1333, "angle": -16.86}, {"time": 6, "angle": 9.43}]}, "fa22": {"rotate": [{"angle": 1.29, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -1.35, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": 1.29, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.1333, "angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": -1.35, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "angle": 1.29, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.1333, "angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": -1.35, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 6, "angle": 1.29}]}, "fa23": {"rotate": [{"angle": 2.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 4.93, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.01, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 4.93, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -5.01, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 2.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": 4.93, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -5.01, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 2.11}]}, "fa24": {"rotate": [{"angle": -1.76, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "angle": 10.47, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -8.88, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -1.76, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.6, "angle": 10.47, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -8.88, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": -1.76, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.6, "angle": 10.47, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": -8.88, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6, "angle": -1.76}]}, "fa25": {"rotate": [{"angle": -11.87, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "angle": 16.53, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -13.99, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -11.87, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 2.8667, "angle": 16.53, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": -13.99, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 4, "angle": -11.87, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 4.8667, "angle": 16.53, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "angle": -13.99, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 6, "angle": -11.87}]}, "fa26": {"rotate": [{"angle": -16.32, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -19.61, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 27.68, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": -16.32, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.1333, "angle": -19.61, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 27.68, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "angle": -16.32, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.1333, "angle": -19.61, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": 27.68, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 6, "angle": -16.32}]}, "eye": {"translate": [{"x": -0.13, "y": 2.61, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -0.26, "y": 5.21, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -0.13, "y": 2.61, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -0.26, "y": 5.21, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -0.13, "y": 2.61, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": -0.26, "y": 5.21, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "x": -0.13, "y": 2.61}]}}, "deform": {"default": {"tou": {"tou": [{}, {"time": 0.1333, "offset": 218, "vertices": [-11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -11.84674, 3.61917, -28.98297, 11.66056, -32.40778, 10.42963, -27.30878, 11.40306, -17.54681, 8.18208, -6.28876, 2.77992, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.03998, -0.64939, -19.289, -0.95709, -23.37958, 0.79936, -18.23639, 4.83604], "curve": "stepped"}, {"time": 0.2, "offset": 218, "vertices": [-11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -11.39862, 10.43204, -11.39856, 10.43204, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -6.659, 4.20543, -6.659, 4.20542, -11.84674, 3.61917, -28.98297, 11.66056, -32.40778, 10.42963, -27.30878, 11.40306, -17.54681, 8.18208, -6.28876, 2.77992, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.03998, -0.64939, -19.289, -0.95709, -23.37958, 0.79936, -18.23639, 4.83604]}, {"time": 0.3333}]}, "eye": {"eye": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "vertices": [-3.16882, 0.54409, -13.30713, 7.0831, -24.49457, 11.97889, -29.32056, 17.22213, -31.19281, 17.8636, -26.7998, 16.62935, -21.07288, 13.27878, -15.27802, 9.02091, -12.32855, 5.21913, -6.16327, 3.08297, 0, 0, 0, 0, -4.61548, -1.35749, -10.32373, -1.61777, -15.16315, -2.234, -16.05353, -2.72201, -15.94025, -0.86031, -13.82104, -1.03813, -12.95972, -0.72078, -12.46912, -1.86374, -10.6015, -0.53981, -10.83319, 1.15096, -15.02112, 7.02333, -12.02106, 6.59025, -10.96759, 3.87819, -7.52905, 2.86937, -4.67072, 1.28694, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.94177, 1.9404, -2.96124, 0.39124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.16852, 5.73869, -27.28149, 11.86687, -33.284, 18.31516, -34.14545, 17.23383, -28.87714, 14.96387, -21.93671, 12.9521, -16.08752, 8.84672, -7.71857, 2.89801, -5.01788, -1.53223, -18.61127, -2.59828, -25.87238, -3.29119, -26.11279, -2.66739, -23.58344, -2.15648, -18.8963, -2.18949, -13.19348, -1.16747, -11.49487, 0.30511], "curve": "stepped"}, {"time": 0.2, "vertices": [-3.16882, 0.54409, -13.30713, 7.0831, -24.49457, 11.97889, -29.32056, 17.22213, -31.19281, 17.8636, -26.7998, 16.62935, -21.07288, 13.27878, -15.27802, 9.02091, -12.32855, 5.21913, -6.16327, 3.08297, 0, 0, 0, 0, -4.61548, -1.35749, -10.32373, -1.61777, -15.16315, -2.234, -16.05353, -2.72201, -15.94025, -0.86031, -13.82104, -1.03813, -12.95972, -0.72078, -12.46912, -1.86374, -10.6015, -0.53981, -10.83319, 1.15096, -15.02112, 7.02333, -12.02106, 6.59025, -10.96759, 3.87819, -7.52905, 2.86937, -4.67072, 1.28694, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.94177, 1.9404, -2.96124, 0.39124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -14.16852, 5.73869, -27.28149, 11.86687, -33.284, 18.31516, -34.14545, 17.23383, -28.87714, 14.96387, -21.93671, 12.9521, -16.08752, 8.84672, -7.71857, 2.89801, -5.01788, -1.53223, -18.61127, -2.59828, -25.87238, -3.29119, -26.11279, -2.66739, -23.58344, -2.15648, -18.8963, -2.18949, -13.19348, -1.16747, -11.49487, 0.30511], "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}}}}}}