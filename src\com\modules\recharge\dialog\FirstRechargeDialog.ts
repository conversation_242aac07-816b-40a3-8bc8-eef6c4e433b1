import { Event } from "laya/events/Event";
import { Box } from "laya/ui/Box";
import { Button } from "laya/ui/Button";
import { Image } from "laya/ui/Image";
import { UrlConfig } from "../../../../game/UrlConfig";
import { MatchConst } from "../../../auto/ConstAuto";
import { cfg_fight_show } from "../../../cfg/vo/cfg_fight_show";
import { cfg_first_pay } from "../../../cfg/vo/cfg_first_pay";
import { ConfigManager } from "../../../managers/ConfigManager";
import { DialogEffectManager } from "../../../managers/DialogEffectManager";
import { DispatchManager } from "../../../managers/DispatchManager";
import { p_first_pay_info } from "../../../proto/common/p_first_pay_info";
import { com } from "../../../ui/layaMaxUI";
import { DateUtil } from "../../../util/DateUtil";
import { GameUtil } from "../../../util/GameUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { CommonButton } from "../../BaseDialog";
import { IUIListItem } from "../../baseModules/IUListItem";
import { GSkeleton } from "../../baseModules/skeleton/GSkeleton";
import { ESkeletonType } from "../../baseModules/skeleton/SkeletonData";
import UIButton from "../../baseModules/UIButton";
import { UIList } from "../../baseModules/UIList";
import { UIListItemData } from "../../baseModules/UIListItemData";
import UITab from "../../baseModules/UITab";
import { UITabData } from "../../baseModules/UITabData";
import { CommonTabItem } from "../../common/CommonTabItem";
import { HandEff } from "../../common/HandEff";
import { DataCenter } from "../../DataCenter";
import { FightDataCenter } from "../../fight/data/FightDataCenter";
import { GoodsVO } from "../../goods/GoodsVO";
import { GuideMgr } from "../../guide/GuideMgr";
import { MenuDataCenter } from "../../menu/data/MenuDataCenter";
import { ModuleCommand } from "../../ModuleCommand";
import { PanelEventConstants } from "../../PanelEventConstants";
import { PaymentDataCenter } from "../../payment/data/PaymentDataCenter";
import { PaymentVO } from "../../payment/vo/PaymentVO";
import { RedPointMgr } from "../../redPoint/RedPointMgr";
import RedPointVo from "../../redPoint/RedPointVo";
import { TodayTipSetting } from "../../setting/TodayTipSetting";
import { FirstPayType, RechargeDataCenter } from "../data/RechargeDataCenter";
import { FirstRechargeFightSelectView } from "../view/FirstRechargeFightSelectView";
import { FirstRechargeRewardItem } from "../view/FirstRechargeRewardItem";


//界面类都是每次打开每次新建的
export class FirstRechargeDialog extends com.ui.res.recharge.FirstRechargeDialogUI {
    private _uiTab: UITab;
    private _taskInfo: p_first_pay_info;
    private cur_cfg_first_pay: cfg_first_pay = null;
    private _list: UIList;
    private _maxRMB: number = 0;

    private boxList:Box[];
    private imgList:Image[];
    private modelList:GSkeleton[];
    private modelSk1:GSkeleton;
    private modelSk2:GSkeleton;
    private modelSk3:GSkeleton;
    constructor() {
        super();
        this.resName = UrlConfig.FIRST_RECHARGE_RES;
        this.navShow = 0;
        //设置是否显示界面弹出、关闭动画
        this.isShowEffect = true;
        this.popupEffect = null;//首充弹出动画特殊处理，因为数据还没设置完，就播放动画了
    }

    getClassName(): string {
        return "FirstRechargeDialog";
    }

    onOpen(param: any): void {

        DialogEffectManager.instance.popupEffect(this);

        GuideMgr.ins.setHideGuideUI(true, this.name);

        GuideMgr.ins.isHideGirl = true;

        let idx: number = 0;
        if (param && param.child_id) {

            idx = this._uiTab.getIdToIndex(param.child_id);

        } else {

            idx = this.getShowTabIdx();

        }
        this._uiTab.selectedIndex = idx;
    }

    private getShowTabIdx(): number {
        if (!this._last_show_cfg_list) return 0;
        let idx: number = 0;
        let cfgList = this._last_show_cfg_list;
        for (let i = 0; i < cfgList.length; i++) {
            let status = RechargeDataCenter.instance.getFirstPayTaskStateByGiftId(cfgList[i].gift_id);
            if (status != 2 && status != 3) {
                idx = i;
                break;
            }

        }
        return idx;
    }

    onClose(): void {
        if (this.selectBox.selected) {
            TodayTipSetting.setTodayTip("FirstRechargeDialog");
        } else {
            TodayTipSetting.setTodayTip("FirstRechargeDialog", true);
        }
        super.onClose();
        GuideMgr.ins.setHideGuideUI(false, this.name);
        GuideMgr.ins.isHideGirl = false;
    }

    initUI(): void {
        this.mouseEnabled = true;
        this.mouseThrough = false;

        this.txtPayNum.text = "";
        this.roleImg.skin = "";

        this.selectBox.selected = !TodayTipSetting.checkTodayTip("FirstRechargeDialog");
        this.selectBox.visible = false;

        this._list = UIList.SetUIList(this, this.rewardBox, FirstRechargeRewardItem);
        this._list.SetSpace(20, 0);
        this._list.SetRepeat(4, 1);
        this._list.SetPadding(0, -20, 0, 0);
        // this._list.isBoxCenter = true;

        this._uiTab = UITab.SetUITab(this, this.p_tab, CommonTabItem, null, this.OnSeletcMenuTab, false);
        this._uiTab.SetTabSize(183, 52);
        this._uiTab.SetSpace(-15, 0);

        ConfigManager.cfg_first_payCache.forEach(cfg => {
            this._maxRMB = Math.max(cfg.money, this._maxRMB);
        });


        this.updateTabShow();

        //倒计时
        this.timerLoop(1000, this, this.onTimeCountDown);
        this.onTimeCountDown();

        this.boxList = [this.heroBox1,this.heroBox2,this.heroBox3];
        this.imgList = [this.imgHeroDesc1,this.imgHeroDesc2,this.imgHeroDesc3];
        this.modelList = [this.modelSk1,this.modelSk2,this.modelSk3];
    }

    addClick(): void {
        this.addOnClick(this, this.btn, this.onClickBtn);
        this.addOnClick(this, this.btnPay, this.onClickBtn);
        this.addOnClick(this, this.btnFightShow, this.onClickFightShow, Event.MOUSE_DOWN);

        this.on(Event.MOUSE_DOWN, this, this.onClickSelf);

        this.addOnClick(this, this.leftArrow, this.onClickLeft);
        this.addOnClick(this, this.rightArrow, this.onClickRight);

        this.addOnClick(this, this.btnReward, this.onClickBtnRewardDetail);

        this.addOnClick(this, this.btnShow1, this.onClickShowFight, Event.CLICK, [1])

        this.addOnClick(this, this.btnShow2, this.onClickShowFight, Event.CLICK, [2])
    }

    addEvent(): void {
        this.addEventListener(ModuleCommand.UPDATE_FIRST_PAY_TASK_INFO, this, this.updateView);
        this.addEventListener(ModuleCommand.OPEN_COMMON_REWARD_TIP_DIALOG, this, this.dealRewardDialog, [false]);
        this.addEventListener(ModuleCommand.CLOSED_COMMON_REWARD_TIP_DIALOG, this, this.dealRewardDialog, [true]);
    }

    onClickShowFight(showNum: number) {

        let match_type = MatchConst.MATCH_TYPE_FIGHT_SHOW;

        let target_id = this.cur_cfg_first_pay.show_war_id;

        if (target_id && target_id != '0') {

            if (target_id.indexOf('|') != -1) {

                target_id = target_id.split("|")[showNum - 1]

            }

            FightDataCenter.instance.m_fight_start_tos(match_type, parseInt(target_id));

        }
    }

    private dealRewardDialog(visible: boolean): void {
        this.visible = visible;
        if (visible) {
            DialogEffectManager.instance.popupEffect(this);
        }
    }

    private _last_show_cfg_list: cfg_first_pay[];

    private updateTabShow(): void {
        let cfgList = this.getFirstCfgList();
        if (this._last_show_cfg_list) {
            //判断显示的数据是否一样
            if (this._last_show_cfg_list[0] == cfgList[0] && this._last_show_cfg_list[1] == cfgList[1]) {
                return;
            }
        }
        this._last_show_cfg_list = cfgList;

        let tabDatas: UITabData[] = this.getTabDatas();
        this._uiTab.array = tabDatas;
        this.p_tab.visible = tabDatas.length > 1;
    }

    private getFirstCfgList(): cfg_first_pay[] {
        let res = [null, null];//永远只显示2挡奖励
        let state: number;
        let first: cfg_first_pay;
        ConfigManager.cfg_first_payCache.forEach(cfg => {
            //判断第一档(w6d新增tab_type字段)
            state = RechargeDataCenter.instance.getFirstPayTaskStateByGiftId(cfg.gift_id);
            if ((state == 0 && (cfg.pre_pay == 0 || RechargeDataCenter.instance.getFirstPayTaskStateByGiftId(cfg.pre_pay) != 0))) {
                if (cfg.tab_type === 1) {
                    res[0] = cfg;
                }
            }
            if (!first) {
                first = cfg;
            } else {
                first = cfg.money > first.money ? cfg : first;
            }
            // }
            //判断第二档,第二档永远显示最后一档
            if (cfg.tab_type === 2) {
                res[1] = cfg;
            }
        });
        if (!res[0]) res[0] = first;
        if (!res[0]) res.shift();
        return res;
    }

    private getTabDatas(): UITabData[] {
        let tabDatas: any[] = [];
        let idx: number = 1;
        let cfgList = this.getFirstCfgList();
        cfgList.forEach(cfg => {
            tabDatas.push(UITab.GetItemData(idx, "", cfg, null, `firstRecharge/${cfg.tab_skin}`, PanelEventConstants.FIRST_RECHARG, cfg.gift_id));
            idx++;
        });

        return tabDatas;
    }

    private onClickSelf(e: Event): void {
        if (e.currentTarget instanceof FirstRechargeFightSelectView) {
            e.stopPropagation();
        }
    }

    private onClickLeft() {
        if (this._list.scrollBar) {
            this.slide(-500);
        }
    }

    private onClickRight() {
        if (this._list.scrollBar) {
            this.slide(500);
        }
    }

    /**已购详情界面 */
    private onClickBtnRewardDetail(): void {
        this.dispatchEvent(ModuleCommand.OPEN_FIRST_RECHARGE_REWARD_LOOK_DIALOG);
    }

    /**
     * 滑动列表
     * @param changeValue 小于0往左滑动,大于0往右滑动
     */
    public slide(changeValue: number) {
        this._list.slide(changeValue, 1000);
    }

    private animationFlag: number = 0;

    OnSeletcMenuTab(uitabData: UITabData): void {
        if (this.cur_cfg_first_pay == uitabData.data) {
            return;
        }
        let data: cfg_first_pay = uitabData.data;
        this.updateViewByData(data);
        this.updateHeroModel();
    }

    private updateViewByData(cfg: cfg_first_pay): void {
        if (!cfg || !this) {
            return;
        }
        this.cur_cfg_first_pay = cfg;
        this.roleImg.skin = cfg.ad_img ? UrlConfig.BASE_RES_UI_URL + `firstRecharge/${cfg.ad_img}`: "";
        this.imgText.skin = `firstRecharge/${cfg.desc_img}`;


        /* 战斗演示按钮是否展示 */
        // let isDubule=this.cur_cfg_first_pay.show_war_id.index
        if (this.cur_cfg_first_pay?.show_war_id) {

            if (this.cur_cfg_first_pay.show_war_id == "0") {

                this.btnShow1.visible = this.btnShow2.visible = false;


            } else if (this.cur_cfg_first_pay.show_war_id.indexOf("|") != -1) {

                let idArr = this.cur_cfg_first_pay.show_war_id.split("|");

                this.btnShow1.visible = this.btnShow2.visible = true


            } else {

                this.btnShow1.visible = true;

                this.btnShow2.visible = false;

            }


        } else {

            this.btnShow1.visible = this.btnShow2.visible = false;

        }


        if (cfg.yueka_type) {
            this.yueKaDesc.visible = true;
            this.yueKaDesc.skin = `firstRecharge/yueka_${cfg.yueka_type}.png`;
        } else {
            this.yueKaDesc.visible = false;
        }
        this.onTimeCountDown();

        this.btnReward.visible = RechargeDataCenter.instance.isFirstPay;

        //策划需求
        // this.bgPrice.visible = false;

        let info: p_first_pay_info = RechargeDataCenter.instance.getFirstPayTaskInfoByGiftId(cfg.gift_id);
        this._taskInfo = info;
        this.btn.visible = true;
        this.btnPay.visible = false;
        if (info) {
            this.setBtnRedState(info.status);
            this.btn.skin = CommonButton.BtnGreen;
            this.btn.stateNum = 3;
            this.btnPay.stateNum = 3;
            switch (info.status) {
                case 0:
                    this.btn.visible = false;
                    this.btnPay.visible = true;
                    this.btnPay.skin = CommonButton.BtnYellow;
                    this.btnPay.label = cfg.money + window.iRMB;
                    break;
                case 1:
                    this.btn.label = window.iLang.L2_RECEIVE_JIANG_LI.il();
                    this.btn.skin = CommonButton.BtnYellow;
                    this.btn.gray = false;
                    break;
                case 2:
                    this.btn.label = window.iLang.L2_MING_RI_ZAI_LAI.il();
                    this.btn.gray = false;
                    break;
                case 3:
                    this.btn.skin = CommonButton.BtnYellow;
                    this.btn.label = "";//"已领取";
                    this.btn.visible = false;
                    this.btnPay.visible = true;
                    this.btnPay.stateNum = 1;
                    this.btnPay.skin = `firstRecharge/btnGray.png`;
                    this.btnPay.label = "";
                    break;
            }
            UIButton.ResetButtonLabel(this.btn);
            if (info.status == 0 || info.status == 1) {
                HandEff.addEff(this.btn);
            } else {
                HandEff.removeEff(this.btn);
            }
            let listData: any[] = [];
            if (cfg.money == this._maxRMB || true) {
                // if(cfg.money == 68 || true) {//!基于以前的代码改的，临时需求先定死第二档
                //全部显示
                let voList: GoodsVO[];
                for (let day: number = 1; day <= 7; day++) {
                    voList = GameUtil.parseRewards(cfg, `day${day}_reward_`);
                    if (voList.length > 0) {
                        let title = DataCenter.serverOpenedDay >= day ? window.iLang.L2_CHONG_ZHI_KE_RECEIVE.il() : window.iLang.L2_DI_P0_TIAN_MIAN_FEI_RECEIVE.il([day]);
                        listData.push({state: day <= info.fetch_num ? 2 : 0, goods: voList, title: title});
                    }
                }
            }
            this._list.array = listData;
            this._list.selectedIndex = info.fetch_num;
            this._list.ScrollTo(this._list.selectedIndex);
            this.leftArrow.visible = this.rightArrow.visible = listData.length > 3;

        } else {
            this.setBtnRedState(0);
        }
    }

    private setBtnRedState(state: number): void {
        this.SetRedPoint(this.btn, state == 1);
    }

    private getCurrCfg(): cfg_first_pay {
        if (this._taskInfo) {
            let cfg: cfg_first_pay = ConfigManager.cfg_first_payCache.get(this._taskInfo.gift_id);
            return cfg;
        }
        return null;
    }

    private onClickBtn(): void {
        if (this._taskInfo) {
            switch (this._taskInfo.status) {
                case 0:
                    PaymentDataCenter.instance.do_coupon_buy(this.cur_cfg_first_pay.money, PaymentVO.KEY_FIRST_PAY_SHOP, this._taskInfo.gift_id);
                    break;
                case 1:
                    TipsUtil.removePopUpWindowVO(ModuleCommand.OPEN_FIRST_RECHARGE_DIALOG);
                    TipsUtil.removePopUpWindowVO(ModuleCommand.OPEN_AD_DIALOG);
                    RechargeDataCenter.instance.m_first_pay_fetch_tos(this._taskInfo.gift_id);
                    break;
                case 2:
                    TipsUtil.showTips(window.iLang.L2_MING_RI_ZAI_LAI_ch26.il());
                    this.dispatchEvent(ModuleCommand.CLOSE_FIRST_RECHARGE_DIALOG);
                    break;
                case 3:
                    // TipsUtil.showTips("奖励已领取！");
                    break;
            }
        }
    }

    private updateView(): void {
        this.updateTabShow();
        //已充值
        this.txtPayNum.visible = false;//需求隐藏
        //获取当前显示的档次
        let cfg: cfg_first_pay = this.getCurrCfg();
        if (RechargeDataCenter.instance.isBuyAndFetchedAllFirstPay) {
            this.dispatchEvent(ModuleCommand.CLOSE_FIRST_RECHARGE_DIALOG);
            //关闭图标
            MenuDataCenter.instance.updateIconState(PanelEventConstants.FIRST_RECHARG, 2);//关闭每日充值图标
            return;
        }

        if (cfg) {
            let info: p_first_pay_info = RechargeDataCenter.instance.getFirstPayTaskInfoByGiftId(cfg.gift_id);
            if (info && (info.status == 2 || info.status == 3)) {
                let isClose = true;
                for (let cfg of this._last_show_cfg_list) {//判断档位买完没有，买完了就关闭界面
                    if (RechargeDataCenter.instance.getFirstPayTaskStateByGiftId(cfg.gift_id) == 0) {
                        isClose = false;
                        break;
                    }
                }
                if (isClose) {
                    // //所有奖励都领取完了，需要关闭页面
                    this.dispatchEvent(ModuleCommand.CLOSE_FIRST_RECHARGE_DIALOG);
                    // return;
                } else {
                    //切到第二档
                    let idx: number = this.getShowTabIdx();
                    this._uiTab.selectedIndex = idx;
                }
                return;
            }
        }
    }
    private updateHeroModel(): void {
        let cfg: cfg_first_pay = this.getCurrCfg();
        if (!cfg) {
            return;
        }
        this.boxList.forEach((box, index)=>{
            this.clearArrEffect(box);
            let cfgIndex  = index + 1;
            let imgDesc = this.imgList[index];
            let model = this.modelList[index];
            const heroShow = cfg[`hero_show${cfgIndex}`];
            const heroDesc:string = cfg[`hero_des${cfgIndex}`];
            box.visible = false;
            if (heroShow) {
                box.visible = true;
                let offsetX =  cfg[`hero_show${cfgIndex}_offset2d_x`] || 0;
                let offsetY =  cfg[`hero_show${cfgIndex}_offset2d_y`] || 0;
                let scale = cfg[`hero_show${cfgIndex}_eff2d_scale`] || 1;
                model = this.showGSkeleton(box,heroShow, model,{
                    type:ESkeletonType.MODEL_LIHUI,
                    x:offsetX,
                    y:offsetY,
                    scale:scale,
                });
            }
            if (heroDesc) {
                let imgOffsetX =  cfg[`hero_des${cfgIndex}_offset_x`] || 0;
                let imgOffsetY =  cfg[`hero_des${cfgIndex}_offset_y`] || 0;
                imgDesc.skin = `firstRecharge/${heroDesc}.png`;
                imgDesc.pos(imgOffsetX,imgOffsetY);
            }
            else{
                imgDesc.skin = "";
            }
        });

    }
    private onClickFightShow(): void {
        let cfg: cfg_first_pay = this.getCurrCfg();
        if (cfg) {

            if (FightDataCenter.isInFight == true) {
                TipsUtil.showTips(window.iLang.L2_DANG_QIAN_ZHENG_ZAI_ZHAN_DOU_ZHONG.il());
                return;
            }

            let cfgshow: cfg_fight_show = ConfigManager.cfg_fight_showCache.get(Number(cfg.fight_show_id));
            if (cfgshow) {
                FightDataCenter.instance.m_fight_start_tos(MatchConst.MATCH_TYPE_FIGHT_SHOW, cfgshow.id);
                this.dispatchEvent(ModuleCommand.CLOSE_FIRST_RECHARGE_DIALOG);
            }
        }
    }
	private clearArrEffect(box:Box) {
        if (box.numChildren > 0) {
            box.destroyChildren();  
        }
	}
    destroy(destroyChild: boolean = true): void {
        super.destroy(destroyChild);
        TipsUtil.clearPopUpWindows();
    }

    private onTimeCountDown(): void {
        let end_time = RechargeDataCenter.instance.getFirstPayEndTime(FirstPayType.first_pay);
        let time = end_time - DataCenter.serverTimeSeconds;
        if (time > 0) {
            this.txtEndTime.text = window.iLang.L2_DAO_JI_SHI_ch05.il() + DateUtil.getCommonActEndTime(time);
            //倒计时位置
            if (this.yueKaDesc.visible) {
                this.txtEndTime.pos(516, 1007);
            } else {
                this.txtEndTime.pos((this.width - this.txtEndTime.textField.width) / 2, 950);
            }
        } else {
            this.txtEndTime.text = "";
        }
    }
}

export class firstRechargeTab extends Button implements IUIListItem {
    public itemData: UITabData;
    public isAddRedListener: boolean = false;

    constructor() {
        super();
        this.labelSize = 24;
        this.labelColors = "#fff1e2,#fff1e2,#fff1e2";
        this.labelPadding = "3,0,0,0";
        this.size(146, 44);
    }

    public Clean(): void {

    }

    destroy(destroyChild: boolean = true): void {
        super.destroy(destroyChild);
        DispatchManager.removeEventListener(ModuleCommand.RED_CHILD_CHANGE, this, this.showRed);
    }

    public set isSelect(value: boolean) {
        this.selected = value;
    }

    public UpdateItem(nowItemData: UIListItemData, isSelect: boolean = false): void {
        this.itemData = nowItemData as UITabData;
        this.isSelect = isSelect;
        this.skin = this.itemData.skin;
        this.label = this.itemData.name;
        this.addRedListener();
    }

    private addRedListener(): void {
        if (this.itemData.eventId >= 0) {
            this.showRed(RedPointMgr.ins.getRedVo(this.itemData.eventId, this.itemData.redId));
            if (this.isAddRedListener == false) {
                this.isAddRedListener = true;
                DispatchManager.addEventListener(ModuleCommand.RED_CHILD_CHANGE, this, this.showRed);
            }
        }
    }

    protected showRed(vo: RedPointVo): void {
        if (vo != null && this.itemData.eventId != 0 && vo.eventId == this.itemData.eventId && vo.redId == this.itemData.redId) {
            RedPointMgr.ins.SetRedPoint(this, vo.isRedState, 0, 0);
        }
    }
}