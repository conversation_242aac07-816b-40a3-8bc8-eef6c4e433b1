import { ILaya } from "ILaya";
import { Laya } from "Laya";
import { loadItem } from "laya/net/LoaderManager";
import { Render } from "laya/renders/Render";
import { Handler } from "laya/utils/Handler";

import { GlobalConfig } from "../../game/GlobalConfig";
import { DataCenter } from "../modules/DataCenter";
import { Loader } from "laya/net/Loader";
import { UrlConfig } from "../../game/UrlConfig";
import { IexManager } from "./IexManager";
import { LocalServerManager, LocalServerReturnVo } from "./LocalServerManager";
import { LocalStorage } from "laya/net/LocalStorage";
import { CmdConsts } from "../modules/cmd/data/CmdConsts";
import { TipsUtil } from "../util/TipsUtil";
import { ConsoleUtils } from "../util/ConsoleUtils";

export class ResStatisticsManager {

    //-------------------------------------------------------------以下为统计用------------------------------

    // private static overrideLoad: any = null;
    /**
     * 前15级加载的资源
     */

    private static originLoadFunc: any = null;
    /**
     * 前15级加载的资源
     */
    public static level_res_map: Map<number, string[]>;
    public static res_level_map: Map<string, number>;

    public static getJsonUrl(): string {
        if (DataCenter.isM2HuiHeGuaJi) {
            return UrlConfig.RES_LAZY_LOAD_GUA_JI_M2;
        } else if (DataCenter.isM3Building) {
            return UrlConfig.RES_LAZY_LOAD_GUA_JI_BUILDING_M3;
        } else {
            return UrlConfig.RES_LAZY_LOAD_TD_MAIN;
        }
    }

    /**
     * 统计资源加载
     * 
     * 本地测试用的,在main.ts里调用   --yanghaidong
     */
    public static init() {
        if (!ResStatisticsManager.originLoadFunc && GlobalConfig.IsDebug && !Render.isConchApp) {

            ResStatisticsManager.level_res_map = new Map();
            ResStatisticsManager.res_level_map = new Map();
            ResStatisticsManager.originLoadFunc = Loader.prototype.load;
            Loader.prototype.load = ResStatisticsManager.onOerrideLoad;

            ResStatisticsManager.setMustPreloadRes();
        }
    }

    public static onOerrideLoad(url: string, type: string | null = null, cache: boolean = true, group: string | null = null, ignoreCache: boolean = false, useWorkerLoader: boolean = ILaya.WorkerLoader.enable): void {
        ResStatisticsManager.recordUrl(url, type);
        ResStatisticsManager.originLoadFunc.apply(this, [url, type, cache, group, ignoreCache, useWorkerLoader]);
    }

    private static setMustPreloadRes(){

    }

    private static checkIsFilterUrl(url: string){

        let filterUrlList = ["/platform/"
            , UrlConfig.RES_LAZY_LOAD_GUA_JI_M2
            , UrlConfig.RES_LAZY_LOAD_GUA_JI_BUILDING_M3
            , UrlConfig.RES_LAZY_LOAD_TD_MAIN
            , UrlConfig.CONFIG_URL
            , UrlConfig.UI_URL
            , UrlConfig.UIBIN_URL
            , UrlConfig.UITHEME_URL
            , UrlConfig.FILECONFIG_URL_W7
            , UrlConfig.FILECONFIG_URL_ORIGIN
            , UrlConfig.UIBIN_URL
        ];

        let isFilter = false;
        filterUrlList.some(filterUrl=>{
            if(url.indexOf(filterUrl) >= 0){
                isFilter = true;
                return true;
            }
        })

        return isFilter;
        
    }
    public static recordUrl(realUrl: string, type: string | null = null) {

        if(!GlobalConfig.IsDebug || !this.originLoadFunc){
            return
        }

        //过滤
        if (this.checkIsFilterUrl(realUrl)) {
            return;
        }

        let level = DataCenter.myLevel;
        let recordUrlList:Set<string> = new Set();
        /**
         * 过滤类型
         * 1)res/ui开头
         * 2)res/atlas开头,顺带保存对应png
         * 
         * 3)res/2d开头,.sk结尾,骨骼动画相关,顺带保存对应png
         * 4)音效,音乐相关,要到GameSoundManager去统计
         * 5)特效相关res/effects
         */
        if (realUrl.startsWith(UrlConfig.BASE_RES_UI_URL)) {
            recordUrlList.add(realUrl);
        } 
        // else if (realUrl.startsWith(UrlConfig.BASE_RES_ATLAS_URL) || realUrl.startsWith(UrlConfig.EFFECT_PATH)) {
        //     saveList.add(realUrl);
        //     let png = realUrl.replace(".atlas", ".png");
        //     if (png != realUrl) {
        //         saveList.add(png);
        //     }
        // } 
        // else if (realUrl.endsWith(".sk")) {
        //     saveList.add(realUrl);
        //     let png = realUrl.replace(".sk", ".png");
        //     if (png != realUrl) {
        //         saveList.add(png);
        //     }
        // } 
        else if (realUrl.indexOf("music/") >= 0) {
            recordUrlList.add(realUrl);
        } 
        else if (realUrl.indexOf("/sounds_") >= 0) {
            //转成ogg, native只支持ogg
            // realUrl = realUrl.replace("sounds_mp3", "sounds_ogg");
            // realUrl = realUrl.replace(".mp3", ".ogg");
            // recordUrlList.add(realUrl);
        }
        else{
            recordUrlList.add(realUrl);
        }

        recordUrlList.forEach(url => {
            let resIndex = url.indexOf("res/");
            let resUrl = url;
            if(resIndex != 0){
                //截取
                resUrl = url.substring(resIndex);
            }
            let saveLv = this.res_level_map.get(url);
            if(!saveLv && UrlConfig.checkResExist(url)){
                this.res_level_map.set(url, level);

                let oldList = this.level_res_map.get(level) || [];
                oldList.push(url);
                this.level_res_map.set(level, oldList);
            }
        })
    }

    /**
     * 保存到bin\res\level_res_map.json
     */
    public static saveToLocalJson(){
        if(!this.level_res_map){
            return;
        }

        let json = {}
        this.level_res_map.forEach((resList, level) => {
            json[level] = resList;
        })

        let trunkDir =  LocalStorage.getItem(CmdConsts.CMD_LOCAL_TRUNK_DIR) || "";
        if (!trunkDir){
            let tips = "----请先设置本地资源目录";
            TipsUtil.showTips(tips);
            ConsoleUtils.error(tips);
            return;
        }

        let filePath = trunkDir + "/bin/" + this.getJsonUrl();

        LocalServerManager.ins.saveLocalFileContent(filePath, json, Handler.create(this, (retVo:LocalServerReturnVo)=>{
            if(retVo.error_code == 0){
                var tips = "----保存 等级,资源 预加载 数据成功";
            }else{
                var tips = "----保存 等级,资源 预加载 数据失败, error_msg = " + retVo.error_msg;
            }

            TipsUtil.showTips(tips);
            ConsoleUtils.error(tips);
        }))
    }
}