import { CfgCacheMapMgr } from "../cfg/CfgCacheMapMgr";
import { DataCenter } from "../modules/DataCenter";
import { GlobalConfig } from "../../game/GlobalConfig";
import { RoleDataCenter } from "../modules/role/data/RoleDataCenter";
import { cfg_client_w3_effect_ext } from "../cfg/vo_ext/cfg_client_w3_effect_ext";
import { cfg_client_w3_skill_ext } from "../cfg/vo_ext/cfg_client_w3_skill_ext";
import { CommonMacro } from "../auto/CommonMacro";
import { cfg_sys_openlv_ext } from "../cfg/vo_ext/cfg_sys_openlv_ext";
export class ConfigManager {
    // get success(): boolean {
    //     return this._ok;
    // }
    // set success(value: boolean) {
    //     this._ok = value;
    // }
    constructor() {
        this.referenceClass = [DataCenter]; // 强制引用类,防止被编译器忽略
        this._dicParse = {};
    }
    // ORDER: string[] = ["mapClientVO2"];
    // private _ok: boolean = false;
    static get instance() {
        if (ConfigManager._instance == null) {
            ConfigManager._instance = new ConfigManager();
            window["BundleConfigManager"] = ConfigManager;
        }
        return ConfigManager._instance;
    }
    get dic() {
        return this._dicParse;
    }
    //
    // setSingleParseHandle(namestr: string, handle: ConfigUnpackInterface): void {
    //     this._dicParse[namestr] = handle;
    // }
    //
    // getSingleParseByName(namestr: string): ConfigUnpackInterface {
    //     return this._dicParse[namestr];
    // }
    /**
     * 解析配置指定字段，生成值列表
     * @param cfg
     * @param prefixs
     * @param param2 isOneArr: 开启判断如果只有一个字段，返回一维数组；ingoreEmpty: 忽略空值，空值不会添加到列表中
     * @returns
     */
    static parseCfgByField(cfg, prefixs, { suffix = 1, count = 30, isOneArr = false, ingoreEmpty = false, } = {}) {
        if (!cfg || prefixs.length <= 0) {
            return [];
        }
        let prefixLen = prefixs.length;
        isOneArr = prefixLen == 1 && isOneArr;
        let datas = [], value = isOneArr ? null : [];
        for (; count > 0; ++suffix, --count) {
            let has_Field = true;
            for (let j = 0; j < prefixLen; ++j) {
                let prefix = prefixs[j];
                let cfgVal = cfg[prefix + suffix];
                //空值判断
                let isEmpty = !cfgVal;
                if (!ingoreEmpty || !isEmpty) {
                    if (!isOneArr) {
                        value.push(cfgVal);
                    }
                    else {
                        value = cfgVal;
                    }
                }
                //遇到未定义的值，判定为字段不存在，循环结束
                if (cfgVal === undefined) {
                    has_Field = false;
                    break;
                }
            }
            //字段不存在，循环结束
            if (!has_Field) {
                break;
            }
            if (isOneArr) {
                if (!ingoreEmpty || value) {
                    datas.push(value);
                    value = null;
                }
            }
            else if (value && value.length > 0) {
                datas.push(value);
                value = [];
            }
        }
        return datas;
    }
    setParseFuncDict(parseDict) {
        this._dicParse = parseDict;
    }
    getParseFuncByName(namestr) {
        return this._dicParse[namestr];
    }
    clear() {
        this._dicParse = null;
    }
    // init start // gen by exportxlsx.py
    static init() {
        ConfigManager.cfg_achievementCache = CfgCacheMapMgr.cfg_achievementCache;
        ConfigManager.cfg_activity_clientCache = CfgCacheMapMgr.cfg_activity_clientCache;
        ConfigManager.cfg_activity_descCache = CfgCacheMapMgr.cfg_activity_descCache;
        ConfigManager.cfg_activity_iconCache = CfgCacheMapMgr.cfg_activity_iconCache;
        ConfigManager.cfg_activity_iconParentCache = CfgCacheMapMgr.cfg_activity_iconParentCache;
        //ConfigManager.cfg_activity_icon_1Cache = CfgCacheMapMgr.cfg_activity_icon_1Cache;
        //ConfigManager.cfg_activity_iconParent_1Cache = CfgCacheMapMgr.cfg_activity_iconParent_1Cache;
        ConfigManager.cfg_activity_miscCacheTemp = CfgCacheMapMgr.cfg_activity_miscCacheTemp;
        ConfigManager.cfg_activity_missionCache = CfgCacheMapMgr.cfg_activity_missionCache;
        ConfigManager.cfg_activity_missionAllCache = CfgCacheMapMgr.cfg_activity_missionAllCache;
        ConfigManager.cfg_activity_noticeCache = CfgCacheMapMgr.cfg_activity_noticeCache;
        ConfigManager.cfg_activity_pageCache = CfgCacheMapMgr.cfg_activity_pageCache;
        ConfigManager.cfg_ad_showCache = CfgCacheMapMgr.cfg_ad_showCache;
        ConfigManager.cfg_all_pinyin_dictCacheTemp = CfgCacheMapMgr.cfg_all_pinyin_dictCacheTemp;
        ConfigManager.cfg_arenaCacheTemp = CfgCacheMapMgr.cfg_arenaCacheTemp;
        ConfigManager.cfg_arena_matchCache = CfgCacheMapMgr.cfg_arena_matchCache;
        ConfigManager.cfg_arena_match_guess_allCache = CfgCacheMapMgr.cfg_arena_match_guess_allCache;
        ConfigManager.cfg_arena_rankCache = CfgCacheMapMgr.cfg_arena_rankCache;
        ConfigManager.cfg_arena_rewardCache = CfgCacheMapMgr.cfg_arena_rewardCache;
        ConfigManager.cfg_arena_skip_limitCacheTemp = CfgCacheMapMgr.cfg_arena_skip_limitCacheTemp;
        ConfigManager.cfg_arena_weekly_rewardCache = CfgCacheMapMgr.cfg_arena_weekly_rewardCache;
        ConfigManager.cfg_ares_palaceCache = CfgCacheMapMgr.cfg_ares_palaceCache;
        ConfigManager.cfg_bag_pageCallCache = CfgCacheMapMgr.cfg_bag_pageCallCache;
        ConfigManager.cfg_bai_jiang_giftCache = CfgCacheMapMgr.cfg_bai_jiang_giftCache;
        ConfigManager.cfg_bingfaCache = CfgCacheMapMgr.cfg_bingfaCache;
        ConfigManager.cfg_bingfaLevelCache = CfgCacheMapMgr.cfg_bingfaLevelCache;
        ConfigManager.cfg_bingfa_extCache = CfgCacheMapMgr.cfg_bingfa_extCache;
        ConfigManager.cfg_bingfa_extBySkillIdCache = CfgCacheMapMgr.cfg_bingfa_extBySkillIdCache;
        ConfigManager.cfg_bingfa_extBySkillLevelListCache = CfgCacheMapMgr.cfg_bingfa_extBySkillLevelListCache;
        ConfigManager.cfg_bingfuCache = CfgCacheMapMgr.cfg_bingfuCache;
        ConfigManager.cfg_bingfu_upgradeCache = CfgCacheMapMgr.cfg_bingfu_upgradeCache;
        ConfigManager.cfg_bingfu_discompose_recastCache = CfgCacheMapMgr.cfg_bingfu_discompose_recastCache;
        ConfigManager.cfg_bingfu_refineCache = CfgCacheMapMgr.cfg_bingfu_refineCache;
        ConfigManager.cfg_buffCache = CfgCacheMapMgr.cfg_buffCache;
        ConfigManager.cfg_buff_typeCache = CfgCacheMapMgr.cfg_buff_typeCache;
        ConfigManager.cfg_buy_timesCache = CfgCacheMapMgr.cfg_buy_timesCache;
        ConfigManager.cfg_buy_times_typeCache = CfgCacheMapMgr.cfg_buy_times_typeCache;
        ConfigManager.cfg_cast_soul_activeCache = CfgCacheMapMgr.cfg_cast_soul_activeCache;
        ConfigManager.cfg_casting_soulCache = CfgCacheMapMgr.cfg_casting_soulCache;
        ConfigManager.cfg_casting_soul_buffCache = CfgCacheMapMgr.cfg_casting_soul_buffCache;
        ConfigManager.cfg_casting_soul_kind_buffCache = CfgCacheMapMgr.cfg_casting_soul_kind_buffCache;
        ConfigManager.cfg_chapter_dialogCacheTemp = CfgCacheMapMgr.cfg_chapter_dialogCacheTemp;
        ConfigManager.cfg_chapter_scriptCache = CfgCacheMapMgr.cfg_chapter_scriptCache;
        ConfigManager.cfg_chat_channelCache = CfgCacheMapMgr.cfg_chat_channelCache;
        ConfigManager.cfg_chat_skinCache = CfgCacheMapMgr.cfg_chat_skinCache;
        ConfigManager.cfg_chat_skin_widgetCache = CfgCacheMapMgr.cfg_chat_skin_widgetCache;
        ConfigManager.cfg_client_w3_effectCacheTemp = CfgCacheMapMgr.cfg_client_w3_effectCacheTemp;
        ConfigManager.cfg_client_w3_skillCacheTemp = CfgCacheMapMgr.cfg_client_w3_skillCacheTemp;
        ConfigManager.cfg_client_w3_skinCache = CfgCacheMapMgr.cfg_client_w3_skinCache;
        // ConfigManager.cfg_client_w3_skin_resCache = CfgCacheMapMgr.cfg_client_w3_skin_resCache;
        ConfigManager.cfg_cmdCache = CfgCacheMapMgr.cfg_cmdCache;
        ConfigManager.cfg_csc_fmsolo_buffCache = CfgCacheMapMgr.cfg_csc_fmsolo_buffCache;
        ConfigManager.cfg_csc_fmsolo_challengeCache = CfgCacheMapMgr.cfg_csc_fmsolo_challengeCache;
        ConfigManager.cfg_csc_fmsolo_etcCache = CfgCacheMapMgr.cfg_csc_fmsolo_etcCache;
        ConfigManager.cfg_csc_fmsolo_logCache = CfgCacheMapMgr.cfg_csc_fmsolo_logCache;
        ConfigManager.cfg_csc_fmsolo_rewardCache = CfgCacheMapMgr.cfg_csc_fmsolo_rewardCache;
        ConfigManager.cfg_csc_fmsolo_rewardTypeCache = CfgCacheMapMgr.cfg_csc_fmsolo_rewardTypeCache;
        ConfigManager.cfg_csc_fmsolo_shop_lvCache = CfgCacheMapMgr.cfg_csc_fmsolo_shop_lvCache;
        ConfigManager.cfg_daily_copyCache = CfgCacheMapMgr.cfg_daily_copyCache;
        ConfigManager.cfg_daily_copy_discountCacheTemp = CfgCacheMapMgr.cfg_daily_copy_discountCacheTemp;
        ConfigManager.cfg_daily_copy_typeCache = CfgCacheMapMgr.cfg_daily_copy_typeCache;
        ConfigManager.cfg_daily_missionCache = CfgCacheMapMgr.cfg_daily_missionCache;
        ConfigManager.cfg_daily_mission_giftCache = CfgCacheMapMgr.cfg_daily_mission_giftCache;
        ConfigManager.cfg_drop_groupCache = CfgCacheMapMgr.cfg_drop_groupCache;
        ConfigManager.cfg_eight_loginCache = CfgCacheMapMgr.cfg_eight_loginCache;
        ConfigManager.cfg_epic_battle_tipsCache = CfgCacheMapMgr.cfg_epic_battle_tipsCache;
        ConfigManager.cfg_equipCache = CfgCacheMapMgr.cfg_equipCache;
        ConfigManager.cfg_equip_composeCache = CfgCacheMapMgr.cfg_equip_composeCache;
        ConfigManager.cfg_equip_compose_target_idCache = CfgCacheMapMgr.cfg_equip_compose_target_idCache;
        ConfigManager.cfg_equip_suitCache = CfgCacheMapMgr.cfg_equip_suitCache;
        ConfigManager.cfg_equip_suit_extCache = CfgCacheMapMgr.cfg_equip_suit_extCache;
        ConfigManager.cfg_fail_tipsCache = CfgCacheMapMgr.cfg_fail_tipsCache;
        ConfigManager.cfg_familyCache = CfgCacheMapMgr.cfg_familyCache;
        ConfigManager.cfg_family_active_attrCache = CfgCacheMapMgr.cfg_family_active_attrCache;
        ConfigManager.cfg_family_active_missionCache = CfgCacheMapMgr.cfg_family_active_missionCache;
        ConfigManager.cfg_family_bossCache = CfgCacheMapMgr.cfg_family_bossCache;
        ConfigManager.cfg_family_boss_attrCache = CfgCacheMapMgr.cfg_family_boss_attrCache;
        ConfigManager.cfg_family_boss_miscCache = CfgCacheMapMgr.cfg_family_boss_miscCache;
        ConfigManager.cfg_family_boss_rankCache = CfgCacheMapMgr.cfg_family_boss_rankCache;
        ConfigManager.cfg_family_etcCacheTemp = CfgCacheMapMgr.cfg_family_etcCacheTemp;
        ConfigManager.cfg_family_hongbaoCache = CfgCacheMapMgr.cfg_family_hongbaoCache;
        ConfigManager.cfg_family_hongbaoItemCache = CfgCacheMapMgr.cfg_family_hongbaoItemCache;
        ConfigManager.cfg_family_hongbao_blessingCache = CfgCacheMapMgr.cfg_family_hongbao_blessingCache;
        ConfigManager.cfg_family_hongbao_rewardCache = CfgCacheMapMgr.cfg_family_hongbao_rewardCache;
        ConfigManager.cfg_family_logCache = CfgCacheMapMgr.cfg_family_logCache;
        ConfigManager.cfg_family_scienceCacheTemp = CfgCacheMapMgr.cfg_family_scienceCacheTemp;
        ConfigManager.cfg_family_science_timesCacheTemp = CfgCacheMapMgr.cfg_family_science_timesCacheTemp;
        ConfigManager.cfg_family_signCache = CfgCacheMapMgr.cfg_family_signCache;
        ConfigManager.cfg_family_sign_activeCache = CfgCacheMapMgr.cfg_family_sign_activeCache;
        ConfigManager.cfg_fight_showCache = CfgCacheMapMgr.cfg_fight_showCache;
        ConfigManager.cfg_first_payCache = CfgCacheMapMgr.cfg_first_payCache;
        ConfigManager.cfg_fly_font_typeCache = CfgCacheMapMgr.cfg_fly_font_typeCache;
        ConfigManager.cfg_fuli_signCache = CfgCacheMapMgr.cfg_fuli_signCache;
        ConfigManager.cfg_fuli_sign_accCache = CfgCacheMapMgr.cfg_fuli_sign_accCache;
        ConfigManager.cfg_fuli_sign_dayCache = CfgCacheMapMgr.cfg_fuli_sign_dayCache;
        ConfigManager.cfg_fuli_tokenCache = CfgCacheMapMgr.cfg_fuli_tokenCache;
        ConfigManager.cfg_fuli_tokenByLevelCache = CfgCacheMapMgr.cfg_fuli_tokenByLevelCache;
        ConfigManager.cfg_fuli_token_typeCache = CfgCacheMapMgr.cfg_fuli_token_typeCache;
        ConfigManager.cfg_fuli_yuekaCache = CfgCacheMapMgr.cfg_fuli_yuekaCache;
        ConfigManager.cfg_game_descCache = CfgCacheMapMgr.cfg_game_descCache;
        ConfigManager.cfg_game_desc_2CacheTemp = CfgCacheMapMgr.cfg_game_desc_2CacheTemp;
        ConfigManager.cfg_general_pass_rewardCache = CfgCacheMapMgr.cfg_general_pass_rewardCache;
        ConfigManager.cfg_general_pass_rewardByTurnCache = CfgCacheMapMgr.cfg_general_pass_rewardByTurnCache;
        ConfigManager.cfg_general_pass_rewardIdCache = CfgCacheMapMgr.cfg_general_pass_rewardIdCache;
        ConfigManager.cfg_general_pass_typeCache = CfgCacheMapMgr.cfg_general_pass_typeCache;
        ConfigManager.cfg_general_pass_typeByprocess_typeCache = CfgCacheMapMgr.cfg_general_pass_typeByprocess_typeCache;
        ConfigManager.cfg_giftCache = CfgCacheMapMgr.cfg_giftCache;
        ConfigManager.cfg_god_equipCache = CfgCacheMapMgr.cfg_god_equipCache;
        ConfigManager.cfg_god_equip_starCache = CfgCacheMapMgr.cfg_god_equip_starCache;
        ConfigManager.cfg_god_equip_composeCache = CfgCacheMapMgr.cfg_god_equip_composeCache;
        ConfigManager.cfg_god_equip_compose_itemCache = CfgCacheMapMgr.cfg_god_equip_compose_itemCache;
        ConfigManager.cfg_god_equip_suitCache = CfgCacheMapMgr.cfg_god_equip_suitCache;
        ConfigManager.cfg_god_equip_suitByStarCache = CfgCacheMapMgr.cfg_god_equip_suitByStarCache;
        ConfigManager.cfg_god_equip_typeCache = CfgCacheMapMgr.cfg_god_equip_typeCache;
        ConfigManager.cfg_god_trialCache = CfgCacheMapMgr.cfg_god_trialCache;
        ConfigManager.cfg_god_trial_buffCache = CfgCacheMapMgr.cfg_god_trial_buffCache;
        ConfigManager.cfg_god_weaponCache = CfgCacheMapMgr.cfg_god_weaponCache;
        ConfigManager.cfg_god_weapon_levelCache = CfgCacheMapMgr.cfg_god_weapon_levelCache;
        ConfigManager.cfg_god_weapon_starCache = CfgCacheMapMgr.cfg_god_weapon_starCache;
        ConfigManager.cfg_god_weapon_missionCache = CfgCacheMapMgr.cfg_god_weapon_missionCache;
        ConfigManager.cfg_god_weapon_missionTypeCache = CfgCacheMapMgr.cfg_god_weapon_missionTypeCache;
        ConfigManager.cfg_god_weapon_refineCache = CfgCacheMapMgr.cfg_god_weapon_refineCache;
        ConfigManager.cfg_god_weapon_plan_refineCache = CfgCacheMapMgr.cfg_god_weapon_plan_refineCache;
        ConfigManager.cfg_god_weapon_skillCache = CfgCacheMapMgr.cfg_god_weapon_skillCache;
        ConfigManager.cfg_god_weapon_skill_attrTypeCache = CfgCacheMapMgr.cfg_god_weapon_skill_attrTypeCache;
        ConfigManager.cfg_god_weapon_soulCache = CfgCacheMapMgr.cfg_god_weapon_soulCache;
        ConfigManager.cfg_gray_pinyinCacheTemp = CfgCacheMapMgr.cfg_gray_pinyinCacheTemp;
        ConfigManager.cfg_grow_tipsCache = CfgCacheMapMgr.cfg_grow_tipsCache;
        ConfigManager.cfg_grow_tipsTagMap = CfgCacheMapMgr.cfg_grow_tipsTagMap;
        ConfigManager.cfg_guaji_box_timeCache = CfgCacheMapMgr.cfg_guaji_box_timeCache;
        ConfigManager.cfg_guaji_monsterCache = CfgCacheMapMgr.cfg_guaji_monsterCache;
        ConfigManager.cfg_guanduCache = CfgCacheMapMgr.cfg_guanduCache;
        ConfigManager.cfg_guandu_answerCache = CfgCacheMapMgr.cfg_guandu_answerCache;
        ConfigManager.cfg_guandu_choseCache = CfgCacheMapMgr.cfg_guandu_choseCache;
        ConfigManager.cfg_guandu_floorCache = CfgCacheMapMgr.cfg_guandu_floorCache;
        ConfigManager.cfg_guandu_missionCache = CfgCacheMapMgr.cfg_guandu_missionCache;
        ConfigManager.cfg_guandu_shopCache = CfgCacheMapMgr.cfg_guandu_shopCache;
        ConfigManager.cfg_guide_helperCache = CfgCacheMapMgr.cfg_guide_helperCache;
        ConfigManager.cfg_guide_missionCache = CfgCacheMapMgr.cfg_guide_missionCache;
        ConfigManager.cfg_hero_attr_addition_careerCache = CfgCacheMapMgr.cfg_hero_attr_addition_careerCache;
        ConfigManager.cfg_hero_attr_addition_nationCache = CfgCacheMapMgr.cfg_hero_attr_addition_nationCache;
        ConfigManager.cfg_hero_bagCache = CfgCacheMapMgr.cfg_hero_bagCache;
        ConfigManager.cfg_hero_baseCache = CfgCacheMapMgr.cfg_hero_baseCache;
        ConfigManager.cfg_hero_base_chipIdCache = CfgCacheMapMgr.cfg_hero_base_chipIdCache;
        ConfigManager.cfg_hero_base_completeIdCache = CfgCacheMapMgr.cfg_hero_base_completeIdCache;
        ConfigManager.cfg_hero_chip_starCache = CfgCacheMapMgr.cfg_hero_chip_starCache;
        ConfigManager.cfg_hero_come_missionCache = CfgCacheMapMgr.cfg_hero_come_missionCache;
        ConfigManager.cfg_hero_come_missionTypeCache = CfgCacheMapMgr.cfg_hero_come_missionTypeCache;
        ConfigManager.cfg_hero_convertCache = CfgCacheMapMgr.cfg_hero_convertCache;
        ConfigManager.cfg_hero_convert_weightCache = CfgCacheMapMgr.cfg_hero_convert_weightCache;
        ConfigManager.cfg_hero_handbook_descCache = CfgCacheMapMgr.cfg_hero_handbook_descCache;
        ConfigManager.cfg_hero_levelCache = CfgCacheMapMgr.cfg_hero_levelCache;
        ConfigManager.cfg_hero_level_limitCache = CfgCacheMapMgr.cfg_hero_level_limitCache;
        ConfigManager.cfg_hero_level_limit_typeCache = CfgCacheMapMgr.cfg_hero_level_limit_typeCache;
        ConfigManager.cfg_hero_pass_missionCache = CfgCacheMapMgr.cfg_hero_pass_missionCache;
        ConfigManager.cfg_hero_pass_rewardCache = CfgCacheMapMgr.cfg_hero_pass_rewardCache;
        ConfigManager.cfg_hero_pass_rewardByTurnCache = CfgCacheMapMgr.cfg_hero_pass_rewardByTurnCache;
        ConfigManager.cfg_hero_recommend_preCache = CfgCacheMapMgr.cfg_hero_recommend_preCache;
        ConfigManager.cfg_hero_recycleCache = CfgCacheMapMgr.cfg_hero_recycleCache;
        ConfigManager.cfg_hero_recycle_changeCacheTemp = CfgCacheMapMgr.cfg_hero_recycle_changeCacheTemp;
        ConfigManager.cfg_hero_recycle_change_star_stageCacheTemp = CfgCacheMapMgr.cfg_hero_recycle_change_star_stageCacheTemp;
        ConfigManager.cfg_hero_recycleCaches = CfgCacheMapMgr.cfg_hero_recycleCaches;
        ConfigManager.cfg_hero_resonate_duduCache = CfgCacheMapMgr.cfg_hero_resonate_duduCache;
        ConfigManager.cfg_hero_resonate_dudu_levelCache = CfgCacheMapMgr.cfg_hero_resonate_dudu_levelCache;
        ConfigManager.cfg_hero_resonate_fiveCache = CfgCacheMapMgr.cfg_hero_resonate_fiveCache;
        ConfigManager.cfg_hero_skinCache = CfgCacheMapMgr.cfg_hero_skinCache;
        ConfigManager.cfg_hero_skinByIconCaChe = CfgCacheMapMgr.cfg_hero_skinByIconCaChe;
        ConfigManager.cfg_hero_skin_listCache = CfgCacheMapMgr.cfg_hero_skin_listCache;
        ConfigManager.cfg_hero_skin_levelCache = CfgCacheMapMgr.cfg_hero_skin_levelCache;
        ConfigManager.cfg_hero_skin_level_listCache = CfgCacheMapMgr.cfg_hero_skin_level_listCache;
        ConfigManager.cfg_hero_skin_attriCache = CfgCacheMapMgr.cfg_hero_skin_attriCache;
        ConfigManager.cfg_hero_stageCache = CfgCacheMapMgr.cfg_hero_stageCache;
        ConfigManager.cfg_hero_stage_limitCache = CfgCacheMapMgr.cfg_hero_stage_limitCache;
        ConfigManager.cfg_hero_starCache = CfgCacheMapMgr.cfg_hero_starCache;
        ConfigManager.cfg_hero_starCache2 = CfgCacheMapMgr.cfg_hero_starCache2;
        ConfigManager.cfg_hero_star_attrCache = CfgCacheMapMgr.cfg_hero_star_attrCache;
        ConfigManager.cfg_hero_star_stageCache = CfgCacheMapMgr.cfg_hero_star_stageCache;
        ConfigManager.cfg_hero_star_stageCache2 = CfgCacheMapMgr.cfg_hero_star_stageCache2;
        ConfigManager.cfg_hero_star_stage_attrCache = CfgCacheMapMgr.cfg_hero_star_stage_attrCache;
        ConfigManager.cfg_hero_star_stage_attr_listCache = CfgCacheMapMgr.cfg_hero_star_stage_attr_listCache;
        ConfigManager.cfg_hunt_buyCache = CfgCacheMapMgr.cfg_hunt_buyCache;
        ConfigManager.cfg_hunt_costCache = CfgCacheMapMgr.cfg_hunt_costCache;
        ConfigManager.cfg_hunt_descCache = CfgCacheMapMgr.cfg_hunt_descCache;
        ConfigManager.cfg_hunt_giftCache = CfgCacheMapMgr.cfg_hunt_giftCache;
        ConfigManager.cfg_hunt_rewards_showCache = CfgCacheMapMgr.cfg_hunt_rewards_showCache;
        ConfigManager.cfg_huoqutujingCache = CfgCacheMapMgr.cfg_huoqutujingCache;
        ConfigManager.cfg_hzzd_achievementCache = CfgCacheMapMgr.cfg_hzzd_achievementCache;
        ConfigManager.cfg_hzzd_eventCache = CfgCacheMapMgr.cfg_hzzd_eventCache;
        ConfigManager.cfg_hzzd_kills_rewardCache = CfgCacheMapMgr.cfg_hzzd_kills_rewardCache;
        ConfigManager.cfg_hzzd_miscCache = CfgCacheMapMgr.cfg_hzzd_miscCache;
        ConfigManager.cfg_ip_setCache = CfgCacheMapMgr.cfg_ip_setCache;
        ConfigManager.cfg_itemCache = CfgCacheMapMgr.cfg_itemCache;
        ConfigManager.cfg_itemKindCache = CfgCacheMapMgr.cfg_itemKindCache;
        ConfigManager.cfg_itemAttrCache = CfgCacheMapMgr.cfg_itemAttrCache;
        ConfigManager.cfg_item_composeCache = CfgCacheMapMgr.cfg_item_composeCache;
        ConfigManager.cfg_item_compose_target_idCache = CfgCacheMapMgr.cfg_item_compose_target_idCache;
        ConfigManager.cfg_lazy_loadCacheTemp = CfgCacheMapMgr.cfg_lazy_loadCacheTemp;
        ConfigManager.cfg_lcqs_acc_star_rewardCache = CfgCacheMapMgr.cfg_lcqs_acc_star_rewardCache;
        ConfigManager.cfg_lcqs_chapter_openCache = CfgCacheMapMgr.cfg_lcqs_chapter_openCache;
        ConfigManager.cfg_lcqs_floor_rewardCache = CfgCacheMapMgr.cfg_lcqs_floor_rewardCache;
        ConfigManager.cfg_lcqs_missionCache = CfgCacheMapMgr.cfg_lcqs_missionCache;
        ConfigManager.cfg_levelCache = CfgCacheMapMgr.cfg_levelCache;
        ConfigManager.cfg_level_giftCache = CfgCacheMapMgr.cfg_level_giftCache;
        ConfigManager.cfg_lineup_buffCache = CfgCacheMapMgr.cfg_lineup_buffCache;
        ConfigManager.cfg_lineup_Buff_iconCache = CfgCacheMapMgr.cfg_lineup_Buff_iconCache;
        ConfigManager.cfg_lineup_career_ruleCache = CfgCacheMapMgr.cfg_lineup_career_ruleCache;
        ConfigManager.cfg_lineup_numCache = CfgCacheMapMgr.cfg_lineup_numCache;
        ConfigManager.cfg_lineup_recommendCache = CfgCacheMapMgr.cfg_lineup_recommendCache;
        ConfigManager.cfg_lineup_styleCache = CfgCacheMapMgr.cfg_lineup_styleCache;
        ConfigManager.cfg_load_tipsCache = CfgCacheMapMgr.cfg_load_tipsCache;
        ConfigManager.cfg_login_activityCache = CfgCacheMapMgr.cfg_login_activityCache;
        ConfigManager.cfg_login_activity_roundCache = CfgCacheMapMgr.cfg_login_activity_roundCache;
        ConfigManager.cfg_lottery_day_limitCache = CfgCacheMapMgr.cfg_lottery_day_limitCache;
        ConfigManager.cfg_lottery_extCache = CfgCacheMapMgr.cfg_lottery_extCache;
        ConfigManager.cfg_lottery_nationCache = CfgCacheMapMgr.cfg_lottery_nationCache;
        ConfigManager.cfg_lottery_nation_timesCache = CfgCacheMapMgr.cfg_lottery_nation_timesCache;
        ConfigManager.cfg_lottery_scoreCache = CfgCacheMapMgr.cfg_lottery_scoreCache;
        ConfigManager.cfg_lottery_showCache = CfgCacheMapMgr.cfg_lottery_showCache;
        ConfigManager.cfg_lottery_timesCache = CfgCacheMapMgr.cfg_lottery_timesCache;
        ConfigManager.cfg_main_battleCache = CfgCacheMapMgr.cfg_main_battleCache;
        ConfigManager.cfg_main_battle_groupCache = CfgCacheMapMgr.cfg_main_battle_groupCache;
        ConfigManager.cfg_main_battle_fetchCache = CfgCacheMapMgr.cfg_main_battle_fetchCache;
        ConfigManager.cfg_main_battle_hangingCache = CfgCacheMapMgr.cfg_main_battle_hangingCache;
        ConfigManager.cfg_main_battle_missionCache = CfgCacheMapMgr.cfg_main_battle_missionCache;
        ConfigManager.cfg_match_typeCache = CfgCacheMapMgr.cfg_match_typeCache;
        ConfigManager.cfg_mazeCache = CfgCacheMapMgr.cfg_mazeCache;
        ConfigManager.cfg_maze_mission_spoilsCache = CfgCacheMapMgr.cfg_maze_mission_spoilsCache;
        ConfigManager.cfg_maze_monsterCache = CfgCacheMapMgr.cfg_maze_monsterCache;
        ConfigManager.cfg_maze_diff_rewardsCache = CfgCacheMapMgr.cfg_maze_diff_rewardsCache;
        ConfigManager.cfg_maze_resetCache = CfgCacheMapMgr.cfg_maze_resetCache;
        ConfigManager.cfg_maze_reviveCache = CfgCacheMapMgr.cfg_maze_reviveCache;
        ConfigManager.cfg_maze_shopCache = CfgCacheMapMgr.cfg_maze_shopCache;
        ConfigManager.cfg_maze_themeCache = CfgCacheMapMgr.cfg_maze_themeCache;
        ConfigManager.cfg_story_mazeCache = CfgCacheMapMgr.cfg_story_mazeCache;
        ConfigManager.cfg_story_maze_mission_spoilsCache = CfgCacheMapMgr.cfg_story_maze_mission_spoilsCache;
        ConfigManager.cfg_story_maze_monsterCache = CfgCacheMapMgr.cfg_story_maze_monsterCache;
        ConfigManager.cfg_story_maze_rewardsCache = CfgCacheMapMgr.cfg_story_maze_rewardsCache;
        ConfigManager.cfg_story_maze_resetCache = CfgCacheMapMgr.cfg_story_maze_resetCache;
        ConfigManager.cfg_story_maze_reviveCache = CfgCacheMapMgr.cfg_story_maze_reviveCache;
        ConfigManager.cfg_story_maze_shopCache = CfgCacheMapMgr.cfg_story_maze_shopCache;
        ConfigManager.cfg_story_maze_themeCache = CfgCacheMapMgr.cfg_story_maze_themeCache;
        ConfigManager.cfg_misc_configCache = CfgCacheMapMgr.cfg_misc_configCache;
        ConfigManager.cfg_mission_shopCache = CfgCacheMapMgr.cfg_mission_shopCache;
        ConfigManager.cfg_mission_shop_clientCache = CfgCacheMapMgr.cfg_mission_shop_clientCache;
        ConfigManager.cfg_modular_activity_compose_listCache = CfgCacheMapMgr.cfg_modular_activity_compose_listCache;
        ConfigManager.cfg_modular_activity_iconCache = CfgCacheMapMgr.cfg_modular_activity_iconCache;
        ConfigManager.cfg_modular_activity_lottery_timesCache = CfgCacheMapMgr.cfg_modular_activity_lottery_timesCache;
        ConfigManager.cfg_modular_activity_missionCache = CfgCacheMapMgr.cfg_modular_activity_missionCache;
        ConfigManager.cfg_modular_activity_missionListCache = CfgCacheMapMgr.cfg_modular_activity_missionListCache;
        ConfigManager.cfg_modular_activity_star_plan_heroCache = CfgCacheMapMgr.cfg_modular_activity_star_plan_heroCache;
        ConfigManager.cfg_modular_activity_star_plan_rewardCache = CfgCacheMapMgr.cfg_modular_activity_star_plan_rewardCache;
        ConfigManager.cfg_modular_activity_star_plan_rewardByplan_idCache = CfgCacheMapMgr.cfg_modular_activity_star_plan_rewardByplan_idCache;
        ConfigManager.cfg_modular_activity_star_plan_rewardByplan_idAndMain_pushCache = CfgCacheMapMgr.cfg_modular_activity_star_plan_rewardByplan_idAndMain_pushCache;
        ConfigManager.cfg_modular_activity_sub_typeCache = CfgCacheMapMgr.cfg_modular_activity_sub_typeCache;
        ConfigManager.cfg_modular_activity_weekly_card_rewardCacheTemp = CfgCacheMapMgr.cfg_modular_activity_weekly_card_rewardCacheTemp;
        ConfigManager.cfg_moneyCache = CfgCacheMapMgr.cfg_moneyCache;
        ConfigManager.cfg_monsterCache = CfgCacheMapMgr.cfg_monsterCache;
        ConfigManager.cfg_monster_groupCache = CfgCacheMapMgr.cfg_monster_groupCache;
        ConfigManager.cfg_monster_skill_tierCache = CfgCacheMapMgr.cfg_monster_skill_tierCache;
        ConfigManager.cfg_monster_tipsCache = CfgCacheMapMgr.cfg_monster_tipsCache;
        ConfigManager.cfg_month_fundCache = CfgCacheMapMgr.cfg_month_fundCache;
        ConfigManager.cfg_month_fundCache2 = CfgCacheMapMgr.cfg_month_fundCache2;
        ConfigManager.cfg_month_fund_typeCache = CfgCacheMapMgr.cfg_month_fund_typeCache;
        ConfigManager.cfg_musicCache = CfgCacheMapMgr.cfg_musicCache;
        ConfigManager.cfg_musicUICache = CfgCacheMapMgr.cfg_musicUICache;
        ConfigManager.cfg_online_rewardCache = CfgCacheMapMgr.cfg_online_rewardCache;
        ConfigManager.cfg_pass_beheadCache = CfgCacheMapMgr.cfg_pass_beheadCache;
        ConfigManager.cfg_pass_behead_boxCache = CfgCacheMapMgr.cfg_pass_behead_boxCache;
        ConfigManager.cfg_pass_behead_guanqiaCache = CfgCacheMapMgr.cfg_pass_behead_guanqiaCache;
        ConfigManager.cfg_pass_behead_reviveCache = CfgCacheMapMgr.cfg_pass_behead_reviveCache;
        ConfigManager.cfg_pass_check_missionCache = CfgCacheMapMgr.cfg_pass_check_missionCache;
        ConfigManager.cfg_pass_check_rewardCache = CfgCacheMapMgr.cfg_pass_check_rewardCache;
        ConfigManager.cfg_pass_check_rewardByTurnCache = CfgCacheMapMgr.cfg_pass_check_rewardByTurnCache;
        ConfigManager.cfg_pass_check_vipCache = CfgCacheMapMgr.cfg_pass_check_vipCache;
        ConfigManager.cfg_pay_vipCache = CfgCacheMapMgr.cfg_pay_vipCache;
        ConfigManager.cfg_pay_vip_privilegeCache = CfgCacheMapMgr.cfg_pay_vip_privilegeCache;
        ConfigManager.cfg_pay_vip_privilege_typeCache = CfgCacheMapMgr.cfg_pay_vip_privilege_typeCache;
        ConfigManager.cfg_pay_vip_privilege_function_typeCache = CfgCacheMapMgr.cfg_pay_vip_privilege_function_typeCache;
        ConfigManager.cfg_payment_shop_itemCache = CfgCacheMapMgr.cfg_payment_shop_itemCache;
        ConfigManager.cfg_payment_shop_linkCache = CfgCacheMapMgr.cfg_payment_shop_linkCache;
        ConfigManager.cfg_peak_miscCache = CfgCacheMapMgr.cfg_peak_miscCache;
        ConfigManager.cfg_peerless_act_hero_giftCache = CfgCacheMapMgr.cfg_peerless_act_hero_giftCache;
        ConfigManager.cfg_progress_giftCache = CfgCacheMapMgr.cfg_progress_giftCache;
        ConfigManager.cfg_progress_giftByTypeCache = CfgCacheMapMgr.cfg_progress_giftByTypeCache;
        ConfigManager.cfg_pull_wordsCacheTemp = CfgCacheMapMgr.cfg_pull_wordsCacheTemp;
        ConfigManager.cfg_pvp_mapCache = CfgCacheMapMgr.cfg_pvp_mapCache;
        ConfigManager.cfg_qq_groupCache = CfgCacheMapMgr.cfg_qq_groupCache;
        ConfigManager.cfg_qq_vipCacheTemp = CfgCacheMapMgr.cfg_qq_vipCacheTemp;
        ConfigManager.cfg_qxzl_miscCache = CfgCacheMapMgr.cfg_qxzl_miscCache;
        ConfigManager.cfg_random_boxCache = CfgCacheMapMgr.cfg_random_boxCache;
        ConfigManager.cfg_random_pvpAllCache = CfgCacheMapMgr.cfg_random_pvpAllCache;
        ConfigManager.cfg_random_pvpGroupCache = CfgCacheMapMgr.cfg_random_pvpGroupCache;
        ConfigManager.cfg_random_pvp_head_frameCache = CfgCacheMapMgr.cfg_random_pvp_head_frameCache;
        ConfigManager.cfg_random_pvp_rewardCache = CfgCacheMapMgr.cfg_random_pvp_rewardCache;
        ConfigManager.cfg_random_pvp_limitCache = CfgCacheMapMgr.cfg_random_pvp_limitCache;
        ConfigManager.cfg_random_pvp_taskCache = CfgCacheMapMgr.cfg_random_pvp_taskCache;
        ConfigManager.cfg_rank_descCache = CfgCacheMapMgr.cfg_rank_descCache;
        ConfigManager.cfg_rank_missionCache = CfgCacheMapMgr.cfg_rank_missionCache;
        ConfigManager.cfg_rank_missionCacheAll = CfgCacheMapMgr.cfg_rank_missionCacheAll;
        ConfigManager.cfg_rank_rewardsCache = CfgCacheMapMgr.cfg_rank_rewardsCache;
        ConfigManager.cfg_red_cliffCache = CfgCacheMapMgr.cfg_red_cliffCache;
        ConfigManager.cfg_red_cliff_bossCacheTemp = CfgCacheMapMgr.cfg_red_cliff_bossCacheTemp;
        ConfigManager.cfg_red_cliff_openCache = CfgCacheMapMgr.cfg_red_cliff_openCache;
        ConfigManager.cfg_profilesByTypeCache = CfgCacheMapMgr.cfg_profilesByTypeCache;
        ConfigManager.cfg_profileCache = CfgCacheMapMgr.cfg_profileCache;
        ConfigManager.cfg_sceneCache = CfgCacheMapMgr.cfg_sceneCache;
        ConfigManager.cfg_sdk_concern_rewardCache = CfgCacheMapMgr.cfg_sdk_concern_rewardCache;
        ConfigManager.cfg_sdk_platform_descCanhe = CfgCacheMapMgr.cfg_sdk_platform_descCanhe;
        ConfigManager.cfg_select_boxCache = CfgCacheMapMgr.cfg_select_boxCache;
        ConfigManager.cfg_seven_goal_singleCache = CfgCacheMapMgr.cfg_seven_goal_singleCache;
        ConfigManager.cfg_seven_goalCache = CfgCacheMapMgr.cfg_seven_goalCache;
        ConfigManager.cfg_seven_goal_giftCache = CfgCacheMapMgr.cfg_seven_goal_giftCache;
        ConfigManager.cfg_seven_goal_missionCache = CfgCacheMapMgr.cfg_seven_goal_missionCache;
        ConfigManager.cfg_seven_goal_missionListCache = CfgCacheMapMgr.cfg_seven_goal_missionListCache;
        ConfigManager.cfg_share_cycle_rewardCache = CfgCacheMapMgr.cfg_share_cycle_rewardCache;
        ConfigManager.cfg_share_daily_rewardCache = CfgCacheMapMgr.cfg_share_daily_rewardCache;
        ConfigManager.cfg_share_level_rewardCache = CfgCacheMapMgr.cfg_share_level_rewardCache;
        ConfigManager.cfg_shopCache = CfgCacheMapMgr.cfg_shopCache;
        ConfigManager.cfg_shopGroupCache = CfgCacheMapMgr.cfg_shopGroupCache;
        ConfigManager.cfg_shop_itemCache = CfgCacheMapMgr.cfg_shop_itemCache;
        ConfigManager.cfg_shop_item_tipsCache = CfgCacheMapMgr.cfg_shop_item_tipsCache;
        ConfigManager.cfg_shop_reset_timesCache = CfgCacheMapMgr.cfg_shop_reset_timesCache;
        ConfigManager.cfg_shop_shortcutCache = CfgCacheMapMgr.cfg_shop_shortcutCache;
        ConfigManager.cfg_show_offCache = CfgCacheMapMgr.cfg_show_offCache;
        ConfigManager.cfg_skeleton_adaptiveCache = CfgCacheMapMgr.cfg_skeleton_adaptiveCache;
        ConfigManager.cfg_skillCache = CfgCacheMapMgr.cfg_skillCache;
        ConfigManager.cfg_skill_effectCache = CfgCacheMapMgr.cfg_skill_effectCache;
        ConfigManager.cfg_skill_eventCache = CfgCacheMapMgr.cfg_skill_eventCache;
        ConfigManager.cfg_skill_levelCache = CfgCacheMapMgr.cfg_skill_levelCache;
        ConfigManager.cfg_star_plan_giftCache = CfgCacheMapMgr.cfg_star_plan_giftCache;
        ConfigManager.cfg_star_plan_giftByreact_typeCache = CfgCacheMapMgr.cfg_star_plan_giftByreact_typeCache;
        ConfigManager.cfg_star_plan_heroCache = CfgCacheMapMgr.cfg_star_plan_heroCache;
        ConfigManager.cfg_star_plan_rewardCache = CfgCacheMapMgr.cfg_star_plan_rewardCache;
        ConfigManager.cfg_star_plan_rewardByrewards_idCache = CfgCacheMapMgr.cfg_star_plan_rewardByrewards_idCache;
        ConfigManager.cfg_star_plan_rewardByrewards_idAndMain_pushCache = CfgCacheMapMgr.cfg_star_plan_rewardByrewards_idAndMain_pushCache;
        ConfigManager.cfg_storyCache = CfgCacheMapMgr.cfg_storyCache;
        ConfigManager.cfg_story_match_typeCache = CfgCacheMapMgr.cfg_story_match_typeCache;
        ConfigManager.cfg_story_actionCache = CfgCacheMapMgr.cfg_story_actionCache;
        ConfigManager.cfg_story_actorCache = CfgCacheMapMgr.cfg_story_actorCache;
        ConfigManager.cfg_story_bubbleCache = CfgCacheMapMgr.cfg_story_bubbleCache;
        ConfigManager.cfg_suit_attr_Cache = CfgCacheMapMgr.cfg_suit_attr_Cache;
        ConfigManager.cfg_svip_pay_giftCache = CfgCacheMapMgr.cfg_svip_pay_giftCache;
        ConfigManager.cfg_sys_open_noticeCache = CfgCacheMapMgr.cfg_sys_open_noticeCache;
        ConfigManager.cfg_team_bossCacheTemp = CfgCacheMapMgr.cfg_team_bossCacheTemp;
        ConfigManager.cfg_tequanCache = CfgCacheMapMgr.cfg_tequanCache;
        ConfigManager.cfg_test_towerCache = CfgCacheMapMgr.cfg_test_towerCache;
        ConfigManager.cfg_test_towerByTypeCache = CfgCacheMapMgr.cfg_test_towerByTypeCache;
        ConfigManager.cfg_test_towerByGetArr = CfgCacheMapMgr.cfg_test_towerByGetArr;
        ConfigManager.cfg_test_tower_extra_rewardCache = CfgCacheMapMgr.cfg_test_tower_extra_rewardCache;
        ConfigManager.cfg_theme_act_famous_lottery_rewardCache = CfgCacheMapMgr.cfg_theme_act_famous_lottery_rewardCache;
        ConfigManager.cfg_theme_act_hero_lottery_showCacheTemp = CfgCacheMapMgr.cfg_theme_act_hero_lottery_showCacheTemp;
        ConfigManager.cfg_theme_act_itemCacheTemp = CfgCacheMapMgr.cfg_theme_act_itemCacheTemp;
        ConfigManager.cfg_theme_act_skin_lotteryAllCache = CfgCacheMapMgr.cfg_theme_act_skin_lotteryAllCache;
        ConfigManager.cfg_theme_act_wish_lotteryCache = CfgCacheMapMgr.cfg_theme_act_wish_lotteryCache;
        ConfigManager.cfg_theme_act_wish_lottery_itemCache = CfgCacheMapMgr.cfg_theme_act_wish_lottery_itemCache;
        ConfigManager.cfg_theme_act_wish_lottery_showCacheTemp = CfgCacheMapMgr.cfg_theme_act_wish_lottery_showCacheTemp;
        ConfigManager.cfg_tiled_effectCache = CfgCacheMapMgr.cfg_tiled_effectCache;
        ConfigManager.cfg_tiled_mapCache = CfgCacheMapMgr.cfg_tiled_mapCache;
        ConfigManager.cfg_time_achievementByIdCache = CfgCacheMapMgr.cfg_time_achievementByIdCache;
        ConfigManager.cfg_time_achievementCache = CfgCacheMapMgr.cfg_time_achievementCache;
        ConfigManager.cfg_time_activity_dropCache = CfgCacheMapMgr.cfg_time_activity_dropCache;
        ConfigManager.cfg_time_activity_shopCache = CfgCacheMapMgr.cfg_time_activity_shopCache;
        ConfigManager.cfg_time_activity_shopAllCache = CfgCacheMapMgr.cfg_time_activity_shopAllCache;
        ConfigManager.cfg_time_activity_weekCache = CfgCacheMapMgr.cfg_time_activity_weekCache;
        ConfigManager.cfg_tipsCache = CfgCacheMapMgr.cfg_tipsCache;
        ConfigManager.cfg_titleCache = CfgCacheMapMgr.cfg_titleCache;
        ConfigManager.cfg_travelCache = CfgCacheMapMgr.cfg_travelCache;
        ConfigManager.cfg_travel_extCache = CfgCacheMapMgr.cfg_travel_extCache;
        ConfigManager.cfg_trig_skillCache = CfgCacheMapMgr.cfg_trig_skillCache;
        ConfigManager.cfg_ui_button_styleCache = CfgCacheMapMgr.cfg_ui_button_styleCache;
        ConfigManager.cfg_up_star_giftCache = CfgCacheMapMgr.cfg_up_star_giftCache;
        ConfigManager.cfg_up_star_rewardCache = CfgCacheMapMgr.cfg_up_star_rewardCache;
        ConfigManager.cfg_up_star_rewardByroundCache = CfgCacheMapMgr.cfg_up_star_rewardByroundCache;
        ConfigManager.cfg_up_star_rewardByreward_idCache = CfgCacheMapMgr.cfg_up_star_rewardByreward_idCache;
        ConfigManager.cfg_vip_daily_missionCache = CfgCacheMapMgr.cfg_vip_daily_missionCache;
        ConfigManager.cfg_vip_daily_mission_giftCacheTemp = CfgCacheMapMgr.cfg_vip_daily_mission_giftCacheTemp;
        ConfigManager.cfg_war_flagCache = CfgCacheMapMgr.cfg_war_flagCache;
        ConfigManager.cfg_war_flag_levelCache = CfgCacheMapMgr.cfg_war_flag_levelCache;
        ConfigManager.cfg_war_flag_stageCache = CfgCacheMapMgr.cfg_war_flag_stageCache;
        ConfigManager.cfg_war_flag_stageCacheByNationCache = CfgCacheMapMgr.cfg_war_flag_stageCacheByNationCache;
        ConfigManager.cfg_war_log_missionCache = CfgCacheMapMgr.cfg_war_log_missionCache;
        ConfigManager.cfg_war_log_mission_pay_rewardTypeCache = CfgCacheMapMgr.cfg_war_log_mission_pay_rewardTypeCache;
        ConfigManager.cfg_war_log_mission_score_rewardTypeCache = CfgCacheMapMgr.cfg_war_log_mission_score_rewardTypeCache;
        ConfigManager.cfg_world_boss_hurt_rewardsTypeCache = CfgCacheMapMgr.cfg_world_boss_hurt_rewardsTypeCache;
        ConfigManager.cfg_world_boss_hurt_rewardsCache = CfgCacheMapMgr.cfg_world_boss_hurt_rewardsCache;
        ConfigManager.cfg_world_boss_levelCache = CfgCacheMapMgr.cfg_world_boss_levelCache;
        ConfigManager.cfg_world_boss_levelbytypeIdCache = CfgCacheMapMgr.cfg_world_boss_levelbytypeIdCache;
        ConfigManager.cfg_world_mapCache = CfgCacheMapMgr.cfg_world_mapCache;
        ConfigManager.cfg_wxShareCache = CfgCacheMapMgr.cfg_wxShareCache;
        ConfigManager.cfg_ybzk_rewardCache = CfgCacheMapMgr.cfg_ybzk_rewardCache;
        ConfigManager.cfg_errorCodeCache = CfgCacheMapMgr.cfg_errorCodeCache;
        ConfigManager.cfg_fightAttrByIdCache = CfgCacheMapMgr.cfg_fightAttrByIdCache;
        ConfigManager.cfg_fightAttrByKeyCache = CfgCacheMapMgr.cfg_fightAttrByKeyCache;
        ConfigManager.cfg_fightAttrByTypeCache = CfgCacheMapMgr.cfg_fightAttrByTypeCache;
        ConfigManager.victoryMacroCache = CfgCacheMapMgr.victoryMacroCache;
        ConfigManager.cfg_hero_strengthenCache = CfgCacheMapMgr.cfg_hero_strengthenCache;
        ConfigManager.cfg_main_battle_boxCache = CfgCacheMapMgr.cfg_main_battle_boxCache;
        ConfigManager.cfg_main_battle_boxByItemIdCache = CfgCacheMapMgr.cfg_main_battle_boxByItemIdCache;
        ConfigManager.cfg_main_battle_box_rewardCache = CfgCacheMapMgr.cfg_main_battle_box_rewardCache;
        ConfigManager.cfg_main_battle_box_rewardMapCache = CfgCacheMapMgr.cfg_main_battle_box_rewardMapCache;
        ConfigManager.cfg_main_battle_box_rewardGrpCache = CfgCacheMapMgr.cfg_main_battle_box_rewardGrpCache;
        ConfigManager.cfg_main_battle_box_levelCache = CfgCacheMapMgr.cfg_main_battle_box_levelCache;
        ConfigManager.cfg_bingfu_recast_lockCache = CfgCacheMapMgr.cfg_bingfu_recast_lockCache;
        ConfigManager.cfg_modular_activity_general_pass_vipCache = CfgCacheMapMgr.cfg_modular_activity_general_pass_vipCache;
        ConfigManager.cfg_modular_activity_general_pass_vipByPlanidCache = CfgCacheMapMgr.cfg_modular_activity_general_pass_vipByPlanidCache;
        ConfigManager.cfg_general_pass_missionCache = CfgCacheMapMgr.cfg_general_pass_missionCache;
        ConfigManager.cfg_general_pass_missionBypass_typeandturnCache = CfgCacheMapMgr.cfg_general_pass_missionBypass_typeandturnCache;
        ConfigManager.cfg_hero_upgrade_tipsCache = CfgCacheMapMgr.cfg_hero_upgrade_tipsCache;
        ConfigManager.cfg_modular_activity_rankCache = CfgCacheMapMgr.cfg_modular_activity_rankCache;
        ConfigManager.cfg_modular_activity_rank_rewardCache = CfgCacheMapMgr.cfg_modular_activity_rank_rewardCache;
        ConfigManager.cfg_modular_activity_carnival_linkCache = CfgCacheMapMgr.cfg_modular_activity_carnival_linkCache;
        ConfigManager.cfg_modular_activity_loginCache = CfgCacheMapMgr.cfg_modular_activity_loginCache;
        ConfigManager.cfg_crush_fight_conditionCache = CfgCacheMapMgr.cfg_crush_fight_conditionCache;
        ConfigManager.cfg_hero_resonate_dhyanaCache = CfgCacheMapMgr.cfg_hero_resonate_dhyanaCache;
        ConfigManager.cfg_red_cliff_refreshCache = CfgCacheMapMgr.cfg_red_cliff_refreshCache;
        ConfigManager.cfg_rank_worshipCache = CfgCacheMapMgr.cfg_rank_worshipCache;
        ConfigManager.cfg_guide_storyCache = CfgCacheMapMgr.cfg_guide_storyCache;
        ConfigManager.cfg_guide_reviewCache = CfgCacheMapMgr.cfg_guide_reviewCache;
        ConfigManager.cfg_agent_reviewCache = CfgCacheMapMgr.cfg_agent_reviewCache;
        ConfigManager.cfg_main_battle_box_tequanCache = CfgCacheMapMgr.cfg_main_battle_box_tequanCache;
        ConfigManager.cfg_guaji_quick_navigationCache = CfgCacheMapMgr.cfg_guaji_quick_navigationCache;
        ConfigManager.cfg_modular_activity_signCache = CfgCacheMapMgr.cfg_modular_activity_signCache;
        ConfigManager.cfg_modular_activity_signbyplanIdCache = CfgCacheMapMgr.cfg_modular_activity_signbyplanIdCache;
        ConfigManager.cfg_modular_activity_dropCache = CfgCacheMapMgr.cfg_modular_activity_dropCache;
        ConfigManager.cfg_modular_activity_hero_challengeByPlanidCache = CfgCacheMapMgr.cfg_modular_activity_hero_challengeByPlanidCache;
        ConfigManager.cfg_modular_activity_hero_challengeCache = CfgCacheMapMgr.cfg_modular_activity_hero_challengeCache;
        ConfigManager.cfg_modular_activity_free_switchCache = CfgCacheMapMgr.cfg_modular_activity_free_switchCache;
        ConfigManager.cfg_modular_activity_drop_showCache = CfgCacheMapMgr.cfg_modular_activity_drop_showCache;
        ConfigManager.cfg_payment_time_giftCache = CfgCacheMapMgr.cfg_payment_time_giftCache;
        ConfigManager.cfg_test_tower_extra_reward_keyCache = CfgCacheMapMgr.cfg_test_tower_extra_reward_keyCache;
        ConfigManager.cfg_battle_fly_nameCache = CfgCacheMapMgr.cfg_battle_fly_nameCache;
        ConfigManager.cfg_god_equip_convertCache = CfgCacheMapMgr.cfg_god_equip_convertCache;
        ConfigManager.cfg_modular_activity_pay_welfareCache = CfgCacheMapMgr.cfg_modular_activity_pay_welfareCache;
        ConfigManager.cfg_soul_hero_link_levelCache = CfgCacheMapMgr.cfg_soul_hero_link_levelCache;
        ConfigManager.cfg_soul_hero_link_nationCache = CfgCacheMapMgr.cfg_soul_hero_link_nationCache;
        ConfigManager.cfg_wxTurn_giftCache = CfgCacheMapMgr.cfg_wxTurn_giftCache;
        ConfigManager.cfg_war_flag_facadeCache = CfgCacheMapMgr.cfg_war_flag_facadeCache;
        ConfigManager.cfg_war_flag_linkCache = CfgCacheMapMgr.cfg_war_flag_linkCache;
        ConfigManager.cfg_war_flag_recycleCache = CfgCacheMapMgr.cfg_war_flag_recycleCache;
        ConfigManager.cfg_device_excursion_name1Cache = CfgCacheMapMgr.cfg_device_excursion_name1Cache;
        ConfigManager.cfg_device_excursion_name2Cache = CfgCacheMapMgr.cfg_device_excursion_name2Cache;
        ConfigManager.cfg_modular_activity_storyCache = CfgCacheMapMgr.cfg_modular_activity_storyCache;
        ConfigManager.cfg_modular_activity_story_chapterCache = CfgCacheMapMgr.cfg_modular_activity_story_chapterCache;
        ConfigManager.cfg_modular_activity_story_dialogueCache = CfgCacheMapMgr.cfg_modular_activity_story_dialogueCache;
        ConfigManager.cfg_hero_recycle_special_switchCache = CfgCacheMapMgr.cfg_hero_recycle_special_switchCache;
        ConfigManager.cfg_medalCache = CfgCacheMapMgr.cfg_medalCache;
        ConfigManager.cfg_medalBytype = CfgCacheMapMgr.cfg_medalBytype;
        ConfigManager.cfg_medalByidex = CfgCacheMapMgr.cfg_medalByidex;
        ConfigManager.cfg_medalBySpmedal = CfgCacheMapMgr.cfg_medalBySpmedal;
        ConfigManager.cfg_ingenious_planTypeIdCache = CfgCacheMapMgr.cfg_ingenious_planTypeIdCache;
        ConfigManager.cfg_ingenious_planIdGroup = CfgCacheMapMgr.cfg_ingenious_planIdGroup;
        ConfigManager.cfg_random_pvp_task_rewardsCache = CfgCacheMapMgr.cfg_random_pvp_task_rewardsCache;
        ConfigManager.cfg_vip_kefuCache = CfgCacheMapMgr.cfg_vip_kefuCache;
        ConfigManager.cfg_vip_kefuBysdknameCache = CfgCacheMapMgr.cfg_vip_kefuBysdknameCache;
        ConfigManager.cfg_modular_activity_compose_listClientKeyCache = CfgCacheMapMgr.cfg_modular_activity_compose_listClientKeyCache;
        ConfigManager.cfg_modular_activity_open_previewCache = CfgCacheMapMgr.cfg_modular_activity_open_previewCache;
        ConfigManager.cfg_player_strategyCache = CfgCacheMapMgr.cfg_player_strategyCache;
        ConfigManager.cfg_sdk_rewardsCache = CfgCacheMapMgr.cfg_sdk_rewardsCache;
        ConfigManager.cfg_vip_kefu_reviewCache = CfgCacheMapMgr.cfg_vip_kefu_reviewCache;
        ConfigManager.cfg_red_cliff_reviewCache = CfgCacheMapMgr.cfg_red_cliff_reviewCache;
        ConfigManager.cfg_microterminal_openCache = CfgCacheMapMgr.cfg_microterminal_openCache;
        ConfigManager.cfg_microterminal_signCache = CfgCacheMapMgr.cfg_microterminal_signCache;
        ConfigManager.cfg_authorized_giftsCache = CfgCacheMapMgr.cfg_authorized_giftsCache;
        ConfigManager.cfg_modular_activity_brickCache = CfgCacheMapMgr.cfg_modular_activity_brickCache;
        ConfigManager.cfg_modular_activity_targetCache = CfgCacheMapMgr.cfg_modular_activity_targetCache;
        ConfigManager.cfg_modular_activity_blessCache = CfgCacheMapMgr.cfg_modular_activity_blessCache;
        ConfigManager.cfg_cross_test_tower_openCache = CfgCacheMapMgr.cfg_cross_test_tower_openCache;
        ConfigManager.cfg_nation_tower_openCache = CfgCacheMapMgr.cfg_nation_tower_openCache;
        ConfigManager.cfg_nation_tower_lineupCache = CfgCacheMapMgr.cfg_nation_tower_lineupCache;
        ConfigManager.cfg_master_cardCache = CfgCacheMapMgr.cfg_master_cardCache;
        ConfigManager.cfg_master_card_stageCache = CfgCacheMapMgr.cfg_master_card_stageCache;
        ConfigManager.cfg_master_card_colorCache = CfgCacheMapMgr.cfg_master_card_colorCache;
        ConfigManager.cfg_master_card_attrCache = CfgCacheMapMgr.cfg_master_card_attrCache;
        ConfigManager.cfg_master_card_drum_levelCache = CfgCacheMapMgr.cfg_master_card_drum_levelCache;
        ConfigManager.cfg_master_card_missionCache = CfgCacheMapMgr.cfg_master_card_missionCache;
        ConfigManager.cfg_master_card_officialCache = CfgCacheMapMgr.cfg_master_card_officialCache;
        ConfigManager.cfg_master_card_official_positionCache = CfgCacheMapMgr.cfg_master_card_official_positionCache;
        ConfigManager.cfg_master_card_reshapeCache = CfgCacheMapMgr.cfg_master_card_reshapeCache;
        ConfigManager.cfg_master_card_slotCache = CfgCacheMapMgr.cfg_master_card_slotCache;
        ConfigManager.cfg_week_targetBytypeCache = CfgCacheMapMgr.cfg_week_targetBytypeCache;
        ConfigManager.cfg_week_targetCache = CfgCacheMapMgr.cfg_week_targetCache;
        ConfigManager.cfg_week_target_levelBytypeCache = CfgCacheMapMgr.cfg_week_target_levelBytypeCache;
        ConfigManager.cfg_week_target_levelCache = CfgCacheMapMgr.cfg_week_target_levelCache;
        ConfigManager.cfg_master_card_help_giftCache = CfgCacheMapMgr.cfg_master_card_help_giftCache;
        ConfigManager.cfg_master_card_first_payCache = CfgCacheMapMgr.cfg_master_card_first_payCache;
        ConfigManager.cfg_medalByclassification = CfgCacheMapMgr.cfg_medalByclassification;
        ConfigManager.cfg_stage_breed_attrCache = CfgCacheMapMgr.cfg_stage_breed_attrCache;
        ConfigManager.cfg_hero_zhouyinCache = CfgCacheMapMgr.cfg_hero_zhouyinCache;
        ConfigManager.cfg_story_tower_battle_Game_rewardCache = CfgCacheMapMgr.cfg_story_tower_battle_Game_rewardCache;
        ConfigManager.cfg_story_siegelord_pass_reward_bystarCache = CfgCacheMapMgr.cfg_story_siegelord_pass_reward_bystarCache;
        ConfigManager.cfg_boat_peak_rankCache = CfgCacheMapMgr.cfg_boat_peak_rankCache;
        ConfigManager.cfg_mock_pvp_hero_baseCache = CfgCacheMapMgr.cfg_mock_pvp_hero_baseCache;
        ConfigManager.cfg_platform_ad_id_miscCache = CfgCacheMapMgr.cfg_platform_ad_id_miscCache;
        ConfigManager.cfg_csclan_solo_buffCache = CfgCacheMapMgr.cfg_csclan_solo_buffCache;
        ConfigManager.cfg_csclan_solo_challengeCache = CfgCacheMapMgr.cfg_csclan_solo_challengeCache;
        ConfigManager.cfg_csclan_solo_etcCache = CfgCacheMapMgr.cfg_csclan_solo_etcCache;
        ConfigManager.cfg_csclan_solo_logCache = CfgCacheMapMgr.cfg_csclan_solo_logCache;
        ConfigManager.cfg_csclan_solo_rewardCache = CfgCacheMapMgr.cfg_csclan_solo_rewardCache;
        ConfigManager.cfg_csclan_solo_rewardTypeCache = CfgCacheMapMgr.cfg_csclan_solo_rewardTypeCache;
        ConfigManager.cfg_csclan_solo_shop_lvCache = CfgCacheMapMgr.cfg_csclan_solo_shop_lvCache;
    }
    /**获取英雄技能列表(无论学会与否) */
    static getHeroActSkills(type_id) {
        let hero_cfg = ConfigManager.cfg_hero_baseCache.get(type_id);
        return hero_cfg.skill_array_.filter(function (id) {
            return id != 0;
        });
    }
    static getCfgTimeActivityShopItemList(shop_id, reward_id) {
        return ConfigManager.cfg_time_activity_shopCache.m_get(shop_id, reward_id);
    }
    static getActivityMission(rewards_id, act_type, id) {
        return ConfigManager.cfg_activity_missionAllCache.m_get(rewards_id, act_type, id);
    }
    static getActivityMissionList(rewards_id, act_type) {
        return ConfigManager.cfg_activity_missionCache.m_get(rewards_id, act_type);
    }
    cfg_vip_daily_mission_giftCallBack(vo) {
        if (ConfigManager.cfg_vip_daily_mission_giftCache.has(vo.type) == false) {
            ConfigManager.cfg_vip_daily_mission_giftCache.set(vo.type, new Map());
        }
        ConfigManager.cfg_vip_daily_mission_giftCache.get(vo.type).set(vo.score, vo);
    }
    cfg_family_boss_miscCallBack(vo) {
        ConfigManager.cfg_family_boss_miscKVCache.set(vo.key, vo.value);
    }
    cfg_csc_fmsolo_etcCallBack(vo) {
        ConfigManager.cfg_csc_fmsolo_etcKVCache.set(vo.key, vo.value);
    }
    static getCfg_csc_fmsolo_challenge(rank_sn, star) {
        return ConfigManager.cfg_csc_fmsolo_challengeCache.m_get(rank_sn, star);
    }
    cfg_family_scienceCallBack(vo) {
        if (!ConfigManager.cfg_family_scienceCache.has(vo.career)) {
            ConfigManager.cfg_family_scienceCache.set(vo.career, new Map());
        }
        let map = ConfigManager.cfg_family_scienceCache.get(vo.career);
        if (!map.has(vo.level)) {
            map.set(vo.level, []);
        }
        let list = map.get(vo.level);
        list.push(vo);
    }
    cfg_family_science_timesCallBack(vo) {
        ConfigManager.cfg_family_science_timesCache.push(vo);
    }
    cfg_game_desc_2CallBack(vo) {
        if (!ConfigManager.cfg_game_desc_2Cache.has(vo.group_name)) {
            ConfigManager.cfg_game_desc_2Cache.set(vo.group_name, new Map);
        }
        let map = ConfigManager.cfg_game_desc_2Cache.get(vo.group_name);
        if (!map.has(vo.title)) {
            map.set(vo.title, []);
        }
        let list = map.get(vo.title);
        list.push(vo);
    }
    cfg_sys_openlvCallBack(vo) {
        if (!ConfigManager.cfg_sys_openlvCache.has(vo.eventId)) {
            ConfigManager.cfg_sys_openlvCache.set(vo.eventId, new Map());
        }
        let subMap = ConfigManager.cfg_sys_openlvCache.get(vo.eventId);
        subMap.set(vo.childId, vo);
        if (vo.open_tips_index > 0) {
            if (!ConfigManager.cfg_sys_open_tipsCache.has(vo.eventId)) {
                ConfigManager.cfg_sys_open_tipsCache.set(vo.eventId, new Map());
            }
            let map = ConfigManager.cfg_sys_open_tipsCache.get(vo.eventId);
            map.set(vo.childId, vo);
        }
        if (vo.main_open_preview > 0) {
            ConfigManager.cfg_sys_mian_previewCache.push(vo);
        }
    }
    static getSysOpenLvConfig(event_id, childId = 0) {
        if (ConfigManager.extCfgSysOpenLvCache.has(event_id) == false) {
            ConfigManager.extCfgSysOpenLvCache.set(event_id, new Map());
        }
        let extSubMap = ConfigManager.extCfgSysOpenLvCache.get(event_id);
        if (extSubMap && extSubMap.has(childId)) {
            return extSubMap.get(childId);
        }
        let rawCfg = this.getSysOpenLvConfigOld(event_id, childId);
        if (rawCfg) {
            let extCfg = new cfg_sys_openlv_ext(rawCfg);
            ConfigManager.extCfgSysOpenLvCache.get(event_id).set(childId, extCfg);
            return extCfg;
        }
        return null;
    }
    //  旧版本，新版本根据不同主线模式，来读取不同字段的值
    static getSysOpenLvConfigOld(event_id, childId = 0) {
        let subMap = ConfigManager.getSysOpenVerCfg("cfg_sys_openlv").get(event_id);
        if (subMap) {
            if (subMap.has(childId)) {
                return subMap.get(childId);
            }
            return subMap.get(0);
        }
        return null;
    }
    static getSysOpenVerCfg(cfg_name) {
        let sys_open_ver = RoleDataCenter.instance.getValueByFieldName("sys_open_ver");
        if (sys_open_ver != 0) {
            return ConfigManager[cfg_name + "_" + sys_open_ver + "Cache"];
        }
        return ConfigManager[cfg_name + "Cache"];
    }
    static getCfgHeroStarArr2(typeId) {
        return ConfigManager.cfg_hero_starCache.get(typeId);
    }
    static getCfgHeroStar2(typeId, star) {
        return ConfigManager.cfg_hero_starCache2.m_get(typeId, star);
    }
    static getCfgHeroStarStageList(typeId, tarStar) {
        return ConfigManager.cfg_hero_star_stageCache.m_get(typeId, tarStar) || [];
    }
    static getCfgHeroStarStage(typeId, star, starStage) {
        return ConfigManager.cfg_hero_star_stageCache2.m_get(typeId, star, starStage);
    }
    static getCfgHeroStarStageAttrList(star) {
        return ConfigManager.cfg_hero_star_stage_attr_listCache.get(star) || [];
    }
    static getCfgHeroStarStageAttr(star, starStage) {
        return ConfigManager.cfg_hero_star_stage_attrCache.m_get(star, starStage);
    }
    /**获取当前神器可以升级的上限 */
    static getGodWeaponMaxUpgradeStage(level) {
        let max_level = 0;
        for (let cfg of ConfigManager.cfg_god_weapon_levelCache.values()) {
            if (cfg.level <= level) {
                max_level = Math.max(cfg.level, max_level);
            }
        }
        return max_level;
    }
    /**获取当前英雄可以升阶的上限 */
    static getHeroMaxUpgradeStage(star, level = 0) {
        let max_stage = 0;
        for (let cfg of ConfigManager.cfg_hero_stage_limitCache.values()) {
            if (star >= cfg.star) {
                max_stage = Math.max(max_stage, cfg.stage);
            }
        }
        if (max_stage > 0 && level > 0) {
            let max_level = ConfigManager.GetHeroMaxUpgradeLevel(star, max_stage);
            if (level >= max_level) {
                return max_stage;
            }
            max_stage = 0;
            for (let lvLimitcfg of ConfigManager.cfg_hero_level_limitCache.values()) {
                if (lvLimitcfg.type == 1) {
                    max_stage = Math.max(max_stage, lvLimitcfg.value);
                    if (level < lvLimitcfg.level) {
                        break;
                    }
                }
                else if (lvLimitcfg.type == 2 && level >= lvLimitcfg.level) {
                    for (let stageLimitCfg of ConfigManager.cfg_hero_stage_limitCache.values()) {
                        if (lvLimitcfg.value >= stageLimitCfg.star) {
                            max_stage = Math.max(max_stage, stageLimitCfg.stage);
                        }
                    }
                }
            }
        }
        return max_stage;
    }
    /**获取当前英雄可以升级的上限 */
    static GetHeroMaxUpgradeLevel(star, stage, starStageList = null) {
        let cfg = ConfigManager.getHeroLevelLimitCfg(star, stage);
        let maxLevel = cfg ? cfg.level : 0;
        if (starStageList && starStageList.length > 0) {
            let cfgList = ConfigManager.getCfgHeroStarStageAttrList(star + 1);
            if (cfgList) {
                for (let i = 0; i < cfgList.length; ++i) {
                    let cfg = cfgList[i];
                    if (starStageList.indexOf(cfg.star_stage) >= 0) {
                        maxLevel += cfg.add_max_level;
                    }
                }
            }
        }
        return maxLevel;
    }
    static getHeroLevelLimitCfg(star, stage) {
        let cfg = ConfigManager.cfg_hero_level_limit_typeCache.m_get(1, stage);
        if (!cfg) {
            cfg = ConfigManager.cfg_hero_level_limit_typeCache.m_get(2, star);
        }
        return cfg;
    }
    cfg_modular_activity_huoqutujingCallBack(vo) {
        let itemId;
        let list;
        for (let i = 1; i <= 100; i++) {
            if (!vo.hasOwnProperty("item_" + i)) {
                break;
            }
            itemId = vo["item_" + i];
            if (itemId <= 0) {
                continue;
            }
            if (!ConfigManager.cfg_modular_activity_huoqutujingCache.has(itemId)) {
                ConfigManager.cfg_modular_activity_huoqutujingCache.set(itemId, []);
            }
            list = ConfigManager.cfg_modular_activity_huoqutujingCache.get(itemId);
            list.push(vo);
        }
    }
    /**
     * 获取模块化活动获取途径
     * @param item_id 道具id
     * @returns
     */
    static getActModsHuoqutujingList(item_id) {
        return ConfigManager.cfg_modular_activity_huoqutujingCache.get(item_id) || [];
    }
    cfg_modular_activity_time_itemCallBack(vo) {
        let itemId;
        let list;
        for (let i = 1; i <= 100; i++) {
            if (!vo.hasOwnProperty("item_" + i)) {
                break;
            }
            itemId = vo["item_" + i];
            if (itemId <= 0) {
                continue;
            }
            if (!ConfigManager.cfg_modular_activity_time_itemCache.has(itemId)) {
                ConfigManager.cfg_modular_activity_time_itemCache.set(itemId, []);
            }
            list = ConfigManager.cfg_modular_activity_time_itemCache.get(itemId);
            list.push(vo);
            ConfigManager.all_modular_act_time_itemList.add(itemId);
        }
    }
    /**
     * 获取模块化活动限时道具所在的活动列表
     * @param item_id 道具id
     * @returns
     */
    static getActModsTimeGoodsActList(item_id) {
        return ConfigManager.cfg_modular_activity_time_itemCache.get(item_id) || [];
    }
    cfg_modular_activity_preview_rewardsCallBack(vo) {
        let itemId;
        let list;
        for (let i = 1; i <= 100; i++) {
            if (!vo.hasOwnProperty("act_item_" + i)) {
                break;
            }
            itemId = vo["act_item_" + i];
            if (itemId <= 0) {
                continue;
            }
            if (!ConfigManager.cfg_modular_activity_preview_rewardsTempCache.has(itemId)) {
                ConfigManager.cfg_modular_activity_preview_rewardsTempCache.set(itemId, []);
            }
            list = ConfigManager.cfg_modular_activity_preview_rewardsTempCache.get(itemId);
            list.push(vo);
        }
    }
    /**
     * 获取模块化活动预告道具所在的活动列表
     * @param item_id 道具id
     * @returns
     */
    static getActModsPreviewRewardsGoodsActList(item_id) {
        return ConfigManager.cfg_modular_activity_preview_rewardsTempCache.get(item_id) || [];
    }
    cfg_family_etcCallBack(vo) {
        ConfigManager.cfg_family_etcCache.set(vo.key, vo.value);
    }
    static getArenaRankType(type) {
        let arr = ConfigManager.cfg_arena_rankCache.get(type);
        if (arr) {
            arr.sort(function (a, b) {
                return a.from_rank - b.from_rank;
            });
        }
        return arr;
    }
    /**通过类型获得地图 */
    static getPvpMapTypeToCfg(mapType) {
        let cfgs = [];
        let map = ConfigManager.cfg_pvp_mapCache;
        map.forEach(element => {
            if (element.map_type == mapType) {
                cfgs.push(element);
            }
        });
        return cfgs;
    }
    cfg_arenaCallBack(vo) {
        ConfigManager.cfg_arenaCache.set(vo.key, vo.value);
    }
    cfg_arena_skip_limitCallBack(vo) {
        ConfigManager.cfg_arena_skip_limitCache.push(vo);
    }
    static getHuntTypeGift(big_type) {
        let arr = [];
        for (const item of ConfigManager.cfg_hunt_giftCache.values()) {
            if (item.big_type == big_type) {
                arr.push(item);
            }
        }
        return arr;
    }
    static getHuntTypeCost(big_type, num_type) {
        let arr = Array.from(ConfigManager.cfg_hunt_costCache.values());
        for (const item of arr) {
            if (item.big_type == big_type && item.num_type == num_type) {
                return item;
            }
        }
        return null;
    }
    static getHuntBuyCostByBigType(big_type, num_type) {
        let key = ConfigManager.genCfgKey(big_type, num_type);
        if (ConfigManager.cfg_hunt_buy_costCache.has(key)) {
            return ConfigManager.cfg_hunt_buy_costCache.get(key);
        }
        let cfg = ConfigManager.cfg_hunt_buyCache.m_get(big_type, num_type);
        if (!cfg)
            return null;
        let arr = cfg.cost.split("|");
        let cfgItem = ConfigManager.cfg_itemCache.get(Number(arr[0]));
        if (!cfgItem)
            return null;
        let res = { cfg: cfgItem, num: Number(arr[1]) };
        ConfigManager.cfg_hunt_buy_costCache.set(key, res);
        return res;
    }
    /**新手指引 */
    static getGuideVerCfg(cfg_name) {
        // let reviewCfg = ConfigManager.cfg_guide_reviewCache.get(GlobalConfig.PlatName);
        //w9优化
        let reviewCfg = ConfigManager.cfg_agent_reviewCache.get(GlobalConfig.PlatName);
        if (GlobalConfig.is_majia_shenhe && reviewCfg != undefined && CfgCacheMapMgr[cfg_name + "_" + reviewCfg.guide_ver + "Cache"]) {
            return CfgCacheMapMgr[cfg_name + "_" + reviewCfg.guide_ver + "Cache"];
        }
        let small_game_cfg = CfgCacheMapMgr.cfg_small_gameCache.get(RoleDataCenter.instance.getValueByFieldName("small_game_id"));
        // let small_game_cfg = CfgCacheMapMgr.cfg_small_gameCache.get(1);
        if (small_game_cfg != undefined) {
            return CfgCacheMapMgr[cfg_name + "_" + small_game_cfg.guide_ver + "Cache"];
        }
        //支持不同方案的引导配置
        let mCache = CfgCacheMapMgr[ConfigManager.getGuideCacheCfgName(cfg_name)];
        if (mCache) {
            return mCache;
        }
        return CfgCacheMapMgr[cfg_name + "Cache"];
    }
    static getGuideCacheCfgName(cfg_name) {
        switch (DataCenter.main_guide_type) {
            case CommonMacro.MAIN_GUIDE_TYPE_M2:
                return cfg_name + "_m2Cache";
            case CommonMacro.MAIN_GUIDE_TYPE_M3_BUILDING:
                return cfg_name + "_m3Cache";
        }
        return cfg_name + "Cache";
    }
    static getGuideHelper(guide_id, step = 0) {
        let arr = ConfigManager.getGuideVerCfg("cfg_guide_helper").get(guide_id);
        if (arr != null) {
            for (const iterator of arr) {
                if (iterator.step == step) {
                    return iterator;
                }
            }
        }
        return null;
    }
    cfg_gray_pinyinCallBack(vo) {
        let str = vo.name;
        ConfigManager.cfg_gray_pinyinObj[str.trim().toLocaleLowerCase()] = true;
        ConfigManager.cfg_gray_pinyinLenDic.set(str.length, true);
    }
    cfg_pull_wordsCallBack(vo) {
        let str = vo.word;
        ConfigManager.cfg_pull_wordsObj[str.trim()] = true;
        ConfigManager.cfg_pull_wordsLenDic.set(str.length, true);
    }
    cfg_all_pinyin_dictCallBack(vo) {
        ConfigManager.cfg_pinyin_dictCache.set(vo.word, vo.pinyin);
    }
    static getRandomPvPGradeName(grade_id) {
        let cfg = ConfigManager.cfg_random_pvpAllCache.get(grade_id);
        if (!cfg)
            return window.iLang.L2_ZAN_WU_DUAN_WEI.il();
        return cfg.full_name;
    }
    static getTrigSkillCfg(skill_id) {
        let cfgList = ConfigManager.cfg_trig_skillCache.get(skill_id);
        return cfgList ? cfgList[0] : null;
    }
    cfg_chapter_dialogCallBack(vo) {
        let map = ConfigManager.cfgChapterDialogCache[vo.condition];
        if (!map) {
            map = new Map();
            ConfigManager.cfgChapterDialogCache[vo.condition] = map;
        }
        map.set(vo.param, vo);
        ConfigManager.cfg_chapter_dialog_cache.set(vo.script_id, vo);
    }
    /**根据条件获取章节剧情对话配置 */
    static getCfgChapterDialog(condition, param) {
        let map = ConfigManager.cfgChapterDialogCache[condition];
        if (map) {
            return map.get(param);
        }
        return null;
    }
    /**是否是稀有天赋 */
    static is_bingfa_rare(type_id) {
        return this.cfg_bingfaTempShopCache.has("rare_" + type_id);
    }
    /**是否是专属天赋 */
    static is_bingfa_ex(type_id) {
        return this.cfg_bingfaTempShopCache.has("ex_" + type_id);
    }
    cfg_team_bossCallBack(vo) {
        var temp;
        if (vo.type == 2) {
            if (vo.need_powers) {
                temp = vo.need_powers.split("|");
                for (var i = 0; i < temp.length; i++) {
                    temp[i] = Number(temp[i]);
                }
                vo.need_powers = temp;
            }
            ConfigManager.cfg_team_bossCache.set(vo.boss_id, vo);
            if (vo.kind == 1)
                ConfigManager.cfg_team_bossnormal.push(vo);
            else if (vo.kind == 2)
                ConfigManager.cfg_team_bosselite.push(vo);
        }
        else if (vo.type == 1) {
            if (vo.recommend_powers) {
                temp = vo.recommend_powers.split("|");
                for (var i = 0; i < temp.length; i++) {
                    temp[i] = Number(temp[i]);
                }
                vo.recommend_powers = temp;
            }
            for (var i = 1; i < 4; i++) {
                temp = vo["monster_lineup_" + i].split("|");
                for (var j = 0; j < temp.length; j++) {
                    temp[j] = Number(temp[j]);
                }
                vo["monster_lineup_" + i] = temp;
            }
            ConfigManager.cfg_team_bossCache1.set(vo.boss_id, vo);
        }
    }
    static splitType1(vo, attr) {
        var temp;
        if (vo[attr]) {
            temp = vo[attr].split("|");
            for (var i = 0; i < temp.length; i++) {
                temp[i] = Number(temp[i]);
            }
            vo[attr] = temp;
        }
    }
    cfg_qq_vipCallBack(vo) {
        if (!ConfigManager.cfg_qq_vipCache.has(vo.vip_type)) {
            ConfigManager.cfg_qq_vipCache.set(vo.vip_type, new Map());
        }
        if (!ConfigManager.cfg_qq_vipCache.get(vo.vip_type).has(vo.gift_type)) {
            let qqarr = [];
            ConfigManager.cfg_qq_vipCache.get(vo.vip_type).set(vo.gift_type, qqarr);
        }
        ConfigManager.cfg_qq_vipCache.get(vo.vip_type).get(vo.gift_type).push(vo);
    }
    static get_cfg_client_w3_skill_skin_tag(skill_id, heroSkinId = 0) {
        let skill_skin = skill_id + "_" + heroSkinId;
        return skill_skin;
    }
    /**根据表唯一id获取配置 */
    static get_cfg_client_w3_skill_by_skinCfgId(skill_id, skinCfgId = 0) {
        let cfgSkin = this.cfg_hero_skin_levelCache.get(skinCfgId);
        let heroSkinId = (cfgSkin === null || cfgSkin === void 0 ? void 0 : cfgSkin.skin_id) || 0;
        return this.get_cfg_client_w3_skill(skill_id, heroSkinId);
    }
    /**根据武将皮肤,获取配置,如果获取不到就取默认配置 */
    static get_cfg_client_w3_skill(skill_id, heroSkinId = 0) {
        let cfgBase = this.cfg_client_w3_skillCache.get(skill_id);
        if (heroSkinId) {
            let skill_skin = this.get_cfg_client_w3_skill_skin_tag(skill_id, heroSkinId);
            let cfg = this.cfg_client_w3_skill_skinCache.get(skill_skin);
            if (cfg) {
                return cfg;
            }
        }
        else {
            // console.error("-----------------get_cfg_client_w3_skill heroSkinId = null");
        }
        return cfgBase;
    }
    static create_cfg_client_w3_skill(skill_id, heroSkinId = 0) {
        let cfgBase = this.cfg_client_w3_skillCache.get(skill_id);
        if (!cfgBase) {
            return null;
        }
        let skill_skin = this.get_cfg_client_w3_skill_skin_tag(skill_id, heroSkinId);
        let cfg = new cfg_client_w3_skill_ext(cfgBase);
        cfg.isHeroSkinSkill = true;
        cfg.heroSkinId = heroSkinId + "";
        ConfigManager.cfg_client_w3_skill_skinCache.set(skill_skin, cfg);
        return cfg;
    }
    cfg_client_w3_skillCallBack(vo) {
        ConfigManager.cfg_client_w3_skillCache.set(vo.skill_id, vo);
        //解析皮肤
        if (vo.ext) {
            let cfgList = cfg_client_w3_skill_ext.parseHeroSkinSkill(vo);
            cfgList.forEach(cfg => {
                let skill_skin = ConfigManager.get_cfg_client_w3_skill_skin_tag(cfg.skill_id, +cfg.heroSkinId);
                ConfigManager.cfg_client_w3_skill_skinCache.set(skill_skin, cfg);
            });
        }
        // if(vo.maxtime > ConfigManager.cfg_client_w3_skill_maxtime){
        //     ConfigManager.cfg_client_w3_skill_maxtime = vo.maxtime;
        // }
    }
    cfg_client_w3_effectCallBack(vo) {
        ConfigManager.cfg_client_w3_effectCache.set(vo.effect_id, vo);
        //解析皮肤
        if (vo.ext) {
            let cfgList = cfg_client_w3_effect_ext.parseHeroSkinSkillEffect(vo);
            cfgList.forEach(cfg => {
                let effect_skin = ConfigManager.get_cfg_client_w3_skill_skin_tag(cfg.effect_id, +cfg.heroSkinId);
                ConfigManager.cfg_client_w3_effect_skinCache.set(effect_skin, cfg);
            });
        }
    }
    /**根据武将皮肤,获取配置,如果获取不到就取默认配置 */
    static get_cfg_client_w3_effect(effect_id, heroSkinId = 0) {
        let cfgBase = this.cfg_client_w3_effectCache.get(effect_id);
        if (heroSkinId) {
            let skill_skin = this.get_cfg_client_w3_skill_skin_tag(effect_id, heroSkinId);
            let cfg = this.cfg_client_w3_effect_skinCache.get(skill_skin);
            if (cfg) {
                return cfg;
            }
        }
        else {
            // console.error("-----------------get_cfg_client_w3_effect heroSkinId = null");
        }
        return cfgBase;
    }
    static create_cfg_client_w3_effect(effect_id, heroSkinId = 0) {
        let cfgBase = this.cfg_client_w3_effectCache.get(effect_id);
        if (!cfgBase) {
            return null;
        }
        let skill_skin = this.get_cfg_client_w3_skill_skin_tag(effect_id, heroSkinId);
        let cfg = new cfg_client_w3_effect_ext(cfgBase);
        cfg.isHeroSkin = true;
        cfg.heroSkinId = heroSkinId + "";
        ConfigManager.cfg_client_w3_effect_skinCache.set(skill_skin, cfg);
        return cfg;
    }
    cfg_guaji_monsterCallBack(vo) {
        ConfigManager.cfg_guaji_monster_AllProb += vo.prob;
    }
    static getHeroChipStar(source_star, target_star) {
        return ConfigManager.cfg_hero_chip_starCache.m_get(source_star + "_" + target_star);
    }
    /**----------六国争霸-------- */
    static genCfgKey(...param) {
        return param.join("-");
    }
    cfg_activity_miscCallBack(vo) {
        ConfigManager.cfg_activity_miscCache.set(vo.key, vo.value);
    }
    static getHeroRecycleTesArray(type, value) {
        let cfg = ConfigManager.cfg_hero_recycleCaches.m_get(type, value);
        return cfg;
    }
    static getLineUpStandList(id) {
        let cfg = ConfigManager.cfg_lineup_styleCache.get(id);
        return cfg && cfg.stand_array_ ? cfg.stand_array_ : [];
    }
    static getLineUpRuleByCareer(career) {
        let cfg = ConfigManager.cfg_lineup_career_ruleCache.get(career);
        return cfg && cfg.order_array_ ? cfg.order_array_ : [];
    }
    static getLineUpBuffIconId(nation_list) {
        let key = 0;
        for (let i = 0; i < 5; ++i) {
            key = key * 2 + (nation_list[i] > 0 ? 1 : 0);
        }
        let icon_id = ConfigManager.cfg_lineup_Buff_icon_idCache.get(key);
        return icon_id ? icon_id : 0;
    }
    cfg_guandu_answerCallBack(vo) {
        if (vo.reward_1 && vo.reward_1.length > 0) {
            ConfigManager.temp_cfg_guandu_answerList.push(vo);
        }
    }
    cfg_red_cliff_bossCallBack(vo) {
        if (!ConfigManager.cfg_red_cliff_bossCache.has(vo.nation)) {
            let dict = new Map();
            ConfigManager.cfg_red_cliff_bossCache.set(vo.nation, dict);
        }
        let list = ConfigManager.cfg_red_cliff_bossCache.get(vo.nation);
        if (!list.has(vo.leader_type_id)) {
            let dict = [];
            list.set(vo.leader_type_id, dict);
        }
        let list2 = list.get(vo.leader_type_id);
        list2.push(vo);
    }
    cfg_hero_recycle_changeCallBack(vo) {
        if (!ConfigManager.cfg_hero_recycle_changeCache.has(vo.type)) {
            let dict = new Map();
            ConfigManager.cfg_hero_recycle_changeCache.set(vo.type, dict);
        }
        let list = ConfigManager.cfg_hero_recycle_changeCache.get(vo.type);
        list.set(vo.star + "_" + vo.nation, vo);
    }
    cfg_hero_recycle_change_star_stageCallBack(vo) {
        if (!ConfigManager.cfg_hero_recycle_change_star_stageCache.has(vo.type)) {
            let dict = new Map();
            ConfigManager.cfg_hero_recycle_change_star_stageCache.set(vo.type, dict);
        }
        let list = ConfigManager.cfg_hero_recycle_change_star_stageCache.get(vo.type);
        list.set(vo.star + "_" + vo.nation + "_" + vo.star_stage, vo);
    }
    cfg_master_card_mission_guide_idCacheCallBack(vo) {
        if (vo.guide_id != 0) {
            if (!ConfigManager.cfg_master_card_mission_guide_idCache.has(vo.guide_id)) {
                ConfigManager.cfg_master_card_mission_guide_idCache.set(vo.guide_id, vo);
            }
        }
    }
    static get_story_tower_battle_Game_reward(key) {
        let vo;
        ConfigManager.cfg_story_tower_battle_Game_rewardCache.get(key).forEach((v, k) => {
            if (v.rewards_1) {
                vo = v;
            }
        });
        return vo;
    }
    static get_cfg_story_tower_battle(key) {
        let cfgGroup = CfgCacheMapMgr.cfg_story_tower_battle_Game_rewardCache.get(key);
        let game_level_map = new Map();
        cfgGroup.forEach(vo => {
            let house_map = game_level_map.get(vo.house);
            if (!house_map) {
                house_map = new Map();
                game_level_map.set(vo.house, house_map);
            }
            house_map.set(vo.floor, vo);
            // if (!house_map) {
            //     house_map = new Map();
            //     game_level_map.set(vo.house, house_map);
            // }
        });
        return game_level_map;
    }
    static get_siegelord_level(key) {
        let cfgGroup = CfgCacheMapMgr.cfg_story_siegelord_levelCache.get(key);
        let siegelord_level = new Map();
        cfgGroup.forEach(vo => {
            siegelord_level.set(vo.city_id, vo);
        });
        return siegelord_level;
    }
    static getGodEquipSuitCfg(suitId, star) {
        let cfg = ConfigManager.cfg_god_equip_suitCache.m_get(star, suitId);
        return cfg;
    }
    static getGodEquipTypeCfg(kind, color, star) {
        let cfg = ConfigManager.cfg_god_equip_typeCache.m_get(kind, color, star);
        return cfg;
    }
    cfg_lazy_loadCallBack(vo) {
        ConfigManager.cfg_lazy_loadCache.push(vo);
    }
    cfg_daily_copy_discountCallBack(vo) {
        ConfigManager.cfg_daily_copy_discountCache.push(vo);
    }
    cfg_theme_act_itemCallBack(vo) {
        ConfigManager.cfg_theme_act_itemCache.push(vo);
    }
    cfg_theme_act_skin_lotteryCallBack(vo) {
        let key = ConfigManager.genCfgKey(vo.rewards_id, vo.round);
        if (!ConfigManager.cfg_theme_act_skin_lotteryCache.has(key)) {
            ConfigManager.cfg_theme_act_skin_lotteryCache.set(key, new Map());
        }
        let map = ConfigManager.cfg_theme_act_skin_lotteryCache.get(key);
        if (!map.has(vo.floor)) {
            map.set(vo.floor, []);
        }
        let list = map.get(vo.floor);
        list.push(vo);
    }
    cfg_theme_act_hero_lottery_showCallBack(vo) {
        if (!ConfigManager.cfg_theme_act_hero_lottery_showCache.has(vo.group_id)) {
            ConfigManager.cfg_theme_act_hero_lottery_showCache.set(vo.group_id, new Map());
        }
        let map = ConfigManager.cfg_theme_act_hero_lottery_showCache.get(vo.group_id);
        if (!map.has(vo.title)) {
            map.set(vo.title, []);
        }
        let list = map.get(vo.title);
        list.push(vo);
    }
    cfg_theme_act_wish_lottery_showCallBack(vo) {
        if (!ConfigManager.cfg_theme_act_wish_lottery_showCache.has(vo.plan_id)) {
            ConfigManager.cfg_theme_act_wish_lottery_showCache.set(vo.plan_id, new Map());
        }
        let map = ConfigManager.cfg_theme_act_wish_lottery_showCache.get(vo.plan_id);
        if (!map.has(vo.title)) {
            map.set(vo.title, []);
        }
        let list = map.get(vo.title);
        list.push(vo);
    }
    static getCfg_theme_act_wish_lottery_itemList(reward_id, hero_type_id) {
        return ConfigManager.cfg_theme_act_wish_lottery_itemCache.m_get(reward_id, hero_type_id);
    }
    static getHeroSkinLevelCfg(skinId, level) {
        let list = ConfigManager.cfg_hero_skin_level_listCache.get(skinId);
        return list ? list[level] : null;
    }
    cfg_modular_activity_weekly_card_rewardCallBack(vo) {
        let map = ConfigManager.cfg_modular_activity_weekly_card_rewardCache.get(vo.plan_id);
        if (map == null) {
            map = new Map();
            ConfigManager.cfg_modular_activity_weekly_card_rewardCache.set(vo.plan_id, map);
        }
        let list = ConfigManager.cfg_modular_activity_weekly_card_rewardCache.get(vo.plan_id).get(vo.sub_type);
        if (list == null) {
            list = [];
            ConfigManager.cfg_modular_activity_weekly_card_rewardCache.get(vo.plan_id).set(vo.sub_type, list);
        }
        ConfigManager.cfg_modular_activity_weekly_card_rewardCache.get(vo.plan_id).get(vo.sub_type).push(vo);
    }
    static getcfgMaze(floor, pos) {
        return ConfigManager.cfg_mazeCache.m_get(floor, pos);
    }
    static getcfgStoryMaze(floor, pos) {
        return ConfigManager.cfg_story_mazeCache.m_get(floor, pos);
    }
    static getModularActMissionList(sub_type, plan_id) {
        let key = ConfigManager.genCfgKey(sub_type, plan_id);
        return ConfigManager.cfg_modular_activity_missionListCache.m_get(sub_type, plan_id);
    }
    static getModularActMission(sub_type, plan_id, id) {
        return ConfigManager.cfg_modular_activity_missionCache.m_get(sub_type, plan_id, id);
    }
    static isBeast() {
        return CfgCacheMapMgr.cfg_beast_platformCache.get(GlobalConfig.PlatName) != undefined;
    }
    static get_story_siegelord_pass_reward_Max() {
        let max = 0;
        ConfigManager.cfg_story_siegelord_pass_reward_bystarCache.forEach((v) => {
            if (max < v.star)
                max = v.star;
        });
        return max;
    }
    static get_cfg_boat_peak_rankCache(season, type) {
        let cfgList = ConfigManager.cfg_boat_peak_rankCache.get(type);
        let arr = [];
        for (let i = 0; i < cfgList.length; i++) {
            let cfg = cfgList[i];
            if (season <= cfg.max_season && season >= cfg.min_season) {
                arr.push(cfg);
            }
        }
        return arr;
    }
    cfg_csclan_solo_etcCallBack(vo) {
        ConfigManager.cfg_csclan_solo_etcKVCache.set(vo.key, vo.value);
    }
    static getCfg_csclan_solo_challenge(rank_sn, star) {
        return ConfigManager.cfg_csclan_solo_challengeCache.m_get(rank_sn, star);
    }
}
ConfigManager._instance = null;
ConfigManager.cfg_shop_item_idsCache = new Map();
ConfigManager.cfg_shop_item_tabCache = new Map();
ConfigManager.cfg_shop_itemByShopIdTypeIdCache = new Map();
ConfigManager.time_activity_shopMap = new Map(); //节日商店id集合
ConfigManager.cfg_daily_mission_max_score = 0;
ConfigManager.cfg_vip_daily_mission_giftCache = new Map();
ConfigManager.cfg_family_boss_miscKVCache = new Map();
ConfigManager.cfg_csc_fmsolo_etcKVCache = new Map();
ConfigManager.cfg_family_scienceCache = new Map();
ConfigManager.cfg_family_science_timesCache = [];
ConfigManager.cfg_activity_iconParentMapCache = new Map();
ConfigManager.cfg_main_battle_group_chapter_dropCache = new Map();
ConfigManager.cfg_main_battle_group_on_hook_dropCache = [];
ConfigManager.cfg_game_desc_2Cache = new Map();
/**系统开放配置 */
ConfigManager.cfg_sys_openlvCache = new Map();
ConfigManager.cfg_sys_open_tipsCache = new Map();
ConfigManager.cfg_sys_mian_previewCache = [];
/**系统开放配置 */
// public static cfg_sys_openlv_1Cache: Map<number, Map<number, cfg_sys_openlv_1>> = new Map();
// public static cfg_sys_open_tips_1Cache: Map<number, Map<number, cfg_sys_openlv_1>> = new Map();
// public static cfg_sys_mian_preview_1Cache: cfg_sys_openlv_1[] = [];
// public cfg_sys_openlv_1CallBack(vo: cfg_sys_openlv_1): void {
//     if (!ConfigManager.cfg_sys_openlv_1Cache.has(vo.eventId)) {
//         ConfigManager.cfg_sys_openlv_1Cache.set(vo.eventId, new Map());
//     }
//     let subMap: Map<number, cfg_sys_openlv_1> = ConfigManager.cfg_sys_openlv_1Cache.get(vo.eventId);
//     subMap.set(vo.childId, vo);
//     if (vo.open_tips_index > 0) {
//         if (!ConfigManager.cfg_sys_open_tips_1Cache.has(vo.eventId)) {
//             ConfigManager.cfg_sys_open_tips_1Cache.set(vo.eventId, new Map());
//         }
//         let map: Map<number, cfg_sys_openlv_1> = ConfigManager.cfg_sys_open_tips_1Cache.get(vo.eventId);
//         map.set(vo.childId, vo);
//     }
//     if (vo.main_open_preview > 0) {
//         ConfigManager.cfg_sys_mian_preview_1Cache.push(vo);
//     }
// }
/**获取系统等级开放配置 */
ConfigManager.extCfgSysOpenLvCache = new Map();
/**模块化活动获取途径 */
ConfigManager.cfg_modular_activity_huoqutujingCache = new Map();
/**模块化活动限时道具 */
ConfigManager.cfg_modular_activity_time_itemCache = new Map();
ConfigManager.all_modular_act_time_itemList = new Set(); //所有限时道具
/**模块化活动预告道具 */
ConfigManager.cfg_modular_activity_preview_rewardsTempCache = new Map();
ConfigManager.cfg_family_etcCache = new Map();
ConfigManager.cfg_arenaCache = new Map();
ConfigManager.cfg_arena_skip_limitCache = [];
ConfigManager.cfg_hunt_buy_costCache = new Map();
ConfigManager.cfg_gray_pinyinObj = {}; //屏蔽字特殊处理
ConfigManager.cfg_gray_pinyinLenDic = new Map();
ConfigManager.cfg_pull_wordsObj = {}; //屏蔽字特殊处理
ConfigManager.cfg_pull_wordsLenDic = new Map();
ConfigManager.cfg_pinyin_dictCache = new Map();
ConfigManager.cfgChapterDialogCache = {};
ConfigManager.cfg_chapter_dialog_cache = new Map();
ConfigManager.cfg_bingfaTempShopCache = new Map();
ConfigManager.cfg_god_weapon_refine_attrCache = new Map();
/** 混沌之地的不同难度下的章节信息 vo.level */
ConfigManager.cfg_chaos_lvList = new Map();
/** 混沌之地的不同难度下的关卡信息 vo.level_vo.chapter */
ConfigManager.cfg_chaos_lvChapterList = new Map();
ConfigManager.cfg_team_bossCache = new Map();
ConfigManager.cfg_team_bossCache1 = new Map();
ConfigManager.cfg_team_bossnormal = [];
ConfigManager.cfg_team_bosselite = [];
ConfigManager.cfg_arena_match_guessCache = [];
/**每日冲刺 */
ConfigManager.cfg_rank_descStringCache = new Map();
ConfigManager.cfg_qq_vipCache = new Map();
ConfigManager.cfg_match_type_sysCache = new Map();
ConfigManager.cfg_client_w3_skill_maxtime = 4000;
//w10优化,这个只能读默认皮肤的配置,因为不同武将皮肤可能有不同配置,尽量用get_cfg_client_w3_skill
ConfigManager.cfg_client_w3_skillCache = new Map();
ConfigManager.cfg_client_w3_skill_skinCache = new Map();
//w10优化,这个只能读默认皮肤的配置,因为不同武将皮肤可能有不同配置,尽量用get_cfg_client_w3_effect
ConfigManager.cfg_client_w3_effectCache = new Map();
ConfigManager.cfg_client_w3_effect_skinCache = new Map();
ConfigManager.cfg_guaji_monster_AllProb = 0;
ConfigManager.cfg_client_w3_skin_resCache = new Map();
ConfigManager.cfg_activity_miscCache = new Map();
ConfigManager.cfg_lineup_buff_idCache = new Map();
ConfigManager.cfg_lineup_Buff_icon_idCache = new Map();
ConfigManager.cfg_god_trial_first_rewardCache = new Map();
ConfigManager.temp_cfg_guandu_answerList = []; //临时对象
ConfigManager.cfg_family_sign_max_score = 0;
ConfigManager.cfg_pass_behead_box_idsCache = new Map();
ConfigManager.cfg_red_cliff_bossCache = new Map();
ConfigManager.cfg_hero_recycle_changeCache = new Map();
ConfigManager.cfg_hero_recycle_change_star_stageCache = new Map();
ConfigManager.cfg_master_card_mission_guide_idCache = new Map();
/**剧情行动配置 */
ConfigManager.cfg_story_action_idsCache = new Map();
ConfigManager.cfg_lazy_loadCache = [];
ConfigManager.cfg_daily_copy_discountCache = [];
ConfigManager.cfg_theme_act_itemCache = [];
/**主题活动 - 限时皮肤 */
ConfigManager.cfg_theme_act_skin_lotteryCache = new Map();
ConfigManager.cfg_theme_act_hero_lottery_showCache = new Map();
ConfigManager.cfg_theme_act_wish_lottery_showCache = new Map();
ConfigManager.cfg_modular_activity_weekly_card_rewardCache = new Map();
ConfigManager.cfg_stage_breedCache = new Map();
ConfigManager.cfg_stage_breed_bigCache = new Map();
ConfigManager.cfg_stage_breed_big_endCache = new Map();
/**境界系统 end */
ConfigManager.MAX_STAGE = 0;
ConfigManager.MAX_BIG_STAGE = 0;
/**WS10 武将苏醒 end */
ConfigManager.MAX_LIMIT_SIGN = 0;
ConfigManager.cfg_modular_activity_festival_wish_round = new Map();
ConfigManager.WISH_MAX_ROUND = new Map();
ConfigManager.cfg_csclan_solo_etcKVCache = new Map();
