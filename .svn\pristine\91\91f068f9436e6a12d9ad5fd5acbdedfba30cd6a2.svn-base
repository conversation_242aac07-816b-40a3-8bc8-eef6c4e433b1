import {Event} from "laya/events/Event";
import {UrlConfig} from "../../../../game/UrlConfig";
import {CfgCacheMapMgr} from "../../../cfg/CfgCacheMapMgr";
import {cfg_hero_base} from "../../../cfg/vo/cfg_hero_base";
import {ConfigManager} from "../../../managers/ConfigManager";
import {GameSoundManager} from "../../../managers/GameSoundManager";
import {p_hero} from "../../../proto/common/p_hero";
import {p_kv} from "../../../proto/common/p_kv";
import {CCAnimation} from "../../../scene2d/d2/CCAnimation";
import {com} from "../../../ui/layaMaxUI";
import {GameUtil} from "../../../util/GameUtil";
import {StringUtil} from "../../../util/StringUtil";
import {TipsUtil} from "../../../util/TipsUtil";
import {TweenUtil} from "../../../util/TweenUtil";
import UIFactory from "../../../util/UIFactory";
import {ESkeletonType} from "../../baseModules/skeleton/SkeletonData";
import UITab from "../../baseModules/UITab";
import {UITabData} from "../../baseModules/UITabData";
import {W73DHeroView} from "../../baseModules/w73d/W73DHeroView";
import {DataCenter} from "../../DataCenter";
import {FightDataCenter} from "../../fight/data/FightDataCenter";
import {GameConst} from "../../GameConst";
import {HeroZhouyinDataCenter} from "../../heroZhouyin/data/HeroZhouyinDataCenter";
import {ModuleCommand} from "../../ModuleCommand";
import {SkillDataCenter} from "../../skill/data/SkillDataCenter";
import {HeroConsts, HeroNation, PillarEffType} from "../data/HeroConsts";
import {HeroDataCenter} from "../data/HeroDataCenter";
import {HeroEvolveDataCenter} from "../data/HeroEvolveDataCenter";
import {HeroTuJianDataCenter} from "../data/HeroTuJianDataCenter";
import {HeroStarScript} from "../script/HeroStarScript";
import {HeroUtil} from "../util/HeroUtil";
import {HeroTuJianListView} from "../view/list/HeroTuJianListView";
import {MatchConst} from "../../../auto/ConstAuto";
import {GSkeleton} from "../../baseModules/skeleton/GSkeleton";
import HeroInfoSumItem2 from "../view/info/HeroInfoSumItem2";
import SkillItem from "../../skill/view/SkillItem";
import {SkillItemVo} from "../../skill/vo/SkillItemVo";
import {DialogNavShow} from "../../BaseDialog";
import {MoneyTransformUtil} from "../../../util/MoneyTransformUtil";
import {GoodsManager} from "../../test_bag/GoodsManager";
import {GoodsChangeManager} from "../../test_bag/GoodsChangeManager";
import HeroStageItem from "../view/stageUpdate/HeroStageItem";
import {HeroStarStageItem} from "../view/starUpdate/HeroStarStageItem";
import {UIList} from "../../baseModules/UIList";
import {cfg_hero_star_stage} from "../../../cfg/vo/cfg_hero_star_stage";
import {fightAttr} from "../../../cfg/vo/fightAttr";
import {FightAttrMgr} from "../../role/data/FightAttrMgr";
import {FightAttrVO} from "../../role/vo/FightAttrVO";


export class HeroTuJianInfoDialog2 extends com.ui.res.heroTujian.HeroTuJianInfoDialog2UI {

    private _selTypeId: number;
    private _selIndex: number;
    private _selStarIndex: number = 1;//0：初始， 1：最高
    private _selNation: number = DataCenter.NATION_WEI;
    private _selSubType: number = 1; // 1：信息界面， 2：列传描述界面

    private _starTab: UITab;

    private _heroCfg: cfg_hero_base;
    private _heroInfo: p_hero;
    private _heroCfgList: cfg_hero_base[];
    private show3d: W73DHeroView;
    private _heroZhouyinLv: number;

    private _preSound1: string;
    private _preSound2: string;
    private _preNation: number;

    private _starBgEff: CCAnimation;
    private pillarEff:GSkeleton;
    private attrList:HeroInfoSumItem2[];
    protected _skillItemList: SkillItem[];

    private _stageList: UIList;
    private _starStageList: UIList;
    private _soulLvList: UIList;
    //顶部货币信息
    private type_id_1: number = GameConst.ITEM_TYPE_SILVER;
    private type_id_2: number = GameConst.ITEM_TYPE_HERO_EXP;
    constructor() {
        super();
        this.navShow = DialogNavShow.NONE;
        this.drawCallOptimize = false;
    }

    initUI() {
        this.height = this.relativeHeight;
        this.y = 0;
        this.callLater(this.resetHitArea, [true]);
        this.panelContext.vScrollBar.visible = false;
        this._starTab =UIFactory.createUITab_H(this, this.tabBox,{
            itemSkin:UIFactory.newCommonTabSkin1,
            onSelect:this.onSelectTab,
            isSelectOne:false,
        });
        this._starTab.SetTabSize(148,70);
        this._starTab.SetSpace(5,0);
        this._starTab.array = [];
        TweenUtil.setButtonClickScale(this.btnLeft);
        TweenUtil.setButtonClickScale(this.btnRight);

        //w10优化
        this.show3d = new W73DHeroView;
        this.show3d.skType = ESkeletonType.MODEL_LIHUI;
        this.show3d.x = this.modelBox.width / 2;
        this.show3d.y = this.modelBox.height;
        this.modelBox.addChild(this.show3d);
        this.show3d.simplified_mesh = this.show3d.useCache = false;
        this._stageList = UIList.SetUIList(this, this.stageBox, HeroStageItem);
        this._stageList.SetRepeat(10, 1);
        this._stageList.SetSpace(5, 0);
        this._stageList.scrollBarHide = true;
        
        this._starStageList = UIList.SetUIList(this, this.starStageBox, HeroStarStageItem);
        this._starStageList.SetRepeat(10, 1);
        this._starStageList.SetSpace(5, 0);
        this._starStageList.scrollBarHide = true;

        this._soulLvList = UIList.SetUIList(this, this.soulLvBox, HeroStageItem);
        this._soulLvList.SetRepeat(10, 1);
        this._soulLvList.SetSpace(5, 0);
        this._soulLvList.scrollBarHide = true;
        this._soulLvList.AddOtherParamete("iconSkin", "heroInfo/img_soul_lv.png");
        this._soulLvList.AddOtherParamete("iconBgSkin", "");
        this.attrList = [
            this.attrItem1,
            this.attrItem2,
            this.attrItem3,
            this.attrItem4,
        ];
        this._skillItemList = [
            this.skillItem1,
            this.skillItem2,
            this.skillItem3,
            this.skillItem4,
        ];

        for (const attrItem of this.attrList) {
            attrItem.setTxtStyle({
                isShowBg:true,
                size:24,
                attrNameColor:"#855033",
                attrValueColor:"#855033",
                stroke:0
            });
        }
        this.setTopItemInfo();
    }   
    setTopItemInfo(){
        this.item1Txt.text = this.myMoneyFormat(this.type_id_1);
        this.item2Txt.text = this.myMoneyFormat(this.type_id_2);
        this.imgItem1.skin = UrlConfig.getGoodsIconByTypeId(this.type_id_1);
        this.imgItem2.skin =  UrlConfig.getGoodsIconByTypeId(this.type_id_2);
    }
    private myMoneyFormat(itemID: number) {
        if (itemID == GameConst.ITEM_TYPE_HERO_EXP) {
            //武将经验暂时不做小数点的处理
            return MoneyTransformUtil.MoneyFormat(GoodsManager.instance.GetGoodsNumByTypeId(itemID), false, 9999);
        } else {
            return MoneyTransformUtil.MoneyFormat(GoodsManager.instance.GetGoodsNumByTypeId(itemID), true, 99999);
        }
    }
    addEvent() {
        this.addEventListener(ModuleCommand.UPDATE_HERO_TUJIAN_LIST, this, this.OnUpdateHeroTuJianList);
        this.addEventListener(ModuleCommand.HERO_SKIN_LOAD, this, this.OnUpdateHeroSkin);
        this.initItemChangeEvent();
    }
    private initItemChangeEvent(): void {
        GoodsChangeManager.ins.removeItemChangeAllByCaller(this);
        GoodsChangeManager.ins.addItemChangeListener(this.type_id_1, this, this.setTopItemInfo);
        GoodsChangeManager.ins.addItemChangeListener(this.type_id_2, this, this.setTopItemInfo);
    }
    private OnUpdateHeroTuJianList(): void {
        this.refreshSubView();
        this.refreshRedPoint();
    }

    private OnUpdateHeroSkin(): void {
        this.refreshInfo();
        this.refreshUI();
        this.refreshShow3D();
        this.refreshSubView();
    }

    addClick() {
        this.addOnClick(this, this.closeBtn, this.OnCLickBtnClose);
        this.addOnClick(this, this.btnLeft, this.OnClickChange, Event.CLICK, [-1]);
        this.addOnClick(this, this.btnRight, this.OnClickChange, Event.CLICK, [1]);
        this.addOnClick(this, this.btnLieZ, this.OnClickBtnLieZ);
        this.addOnClick(this, this.imgReward, this.OnClickImgReward);
        this.addOnClick(this, this.btnSound, this.OnClickBtnSound);
        this.addOnClick(this, this.imgPillar, this.onClickimgPillar);
        this.addOnClick(this, this.growth_road, this.onClickgrowth_road);
        this.addOnClick(this, this.btnSoulHelp, this.onClickBtnSoulDesc);
        this.addOnClick(this, this.btnGet, this.onClickGet);
        this.addOnClick(this,this.btnShow,this.onClickBtnWar)
        this.addOnClick(this, this.feekBack, this.onClickFeekBack);
        this.addOnClick(this,this.previewTag,this.onClickPretag);
        this.addOnClick(this, this.boxEvolveSkill, this.onClickBtnEvolveSkill);
        this.addOnClick(this, this.btnItem1Add, this.onItemAddClick,Event.CLICK,[this.type_id_1]);
        this.addOnClick(this, this.btnItem2Add, this.onItemAddClick,Event.CLICK,[this.type_id_2]);
	    this.addOnClick(this, this.boxItem1, this.onItemAddClick,Event.CLICK,[this.type_id_1]);
        this.addOnClick(this, this.boxItem2, this.onItemAddClick,Event.CLICK,[this.type_id_2]);
    }

    onClickPretag(){

        if (HeroTuJianDataCenter.instance.isPreHero( this._heroCfg?.type_id )) {

            let desc=window.iLang.L2_BEN_HERO_WEI_YU_GAO_ZHAN_SHI_ch31_ZUI_ZHONG_HERO_SHU_XING_ch18_JI_NENG_ch18_MO.il();

            TipsUtil.showDialog(this,desc,window.iLang.L2_TI_ch11_SHI.il(),()=>{
        
            },{btnType:TipsUtil.TYPE_OK});

        }

    }

    private onItemAddClick(type_id: number): void {
        switch (type_id) {
            case GameConst.ITEM_TYPE_SILVER:
                this.dispatchEvent(ModuleCommand.OPEN_TAX_DIALOG);
                break;
            case GameConst.ITEM_TYPE_HERO_EXP:
                this.dispatchEvent(ModuleCommand.OPEN_TAX_HERO_EXP_DIALOG);
                break;
            default:
                break;
        }
    }

    private onClickFeekBack() {
        this.dispatchEvent(ModuleCommand.OPEN_COMPLAINTS_DIALOG);
    }

    onClickBtnWar(){
        
        let match_type=MatchConst.MATCH_TYPE_FIGHT_SHOW;

        let heroTujainInfo=    ConfigManager.cfg_hero_baseCache.get(this._heroCfg?.type_id);

        if(heroTujainInfo?.fight_show_id!=0){
            
        FightDataCenter.instance.m_fight_start_tos(match_type,heroTujainInfo?.fight_show_id);

        }
    }

  
   

    private onClickgrowth_road(): void {
        this.dispatchEvent(ModuleCommand.OPEN_HERO_STAR_UP_GROWTH_ROAD_DIALOG, { isTujian: true });
    }

    private onClickGet(): void {
        if (this._heroCfg) {
            TipsUtil.ShowPathTypeIdToTips(this._heroCfg.hero_complete_id);
        }
    }
    private onSelectTab(tabData: UITabData): void {
        if (this._selStarIndex == tabData.index) {
            return;
        }
        this._selStarIndex = tabData.index;
        this.refreshInfo();
        this.refreshUI();
        this.refreshSubView();
    }

    private OnClickChange(index: number): void {
        let totalCount = this._heroCfgList.length;
        this._selIndex = (this._selIndex + index + totalCount) % totalCount;
        this.refreshView();
    }

    private OnClickBtnLieZ(): void {
        if (this._selSubType == 1) {
            this._selSubType = 2;
            this.refreshSubView();
        }
    }

    private OnClickImgReward(): void {
        if (!this._heroCfg) {
            console.warn("无效参数");
            return;
        }
        let typeId = this._heroCfg.type_id;
        if (!HeroTuJianDataCenter.instance.isHadHeroTuJian(typeId)) {
            TipsUtil.showTips(window.iLang.L2_HUO_DE_GAI_HERO_HOU_KE_RECEIVE.il());
        }
        else if (HeroTuJianDataCenter.instance.isCanFetchReward(typeId)) {
            HeroTuJianDataCenter.instance.m_hero_handbook_fetch_tos(typeId);
        }
        else if (HeroTuJianDataCenter.instance.isFetchedReward(typeId)) {
            TipsUtil.showTips(window.iLang.L2_JIANG_LI_YI_RECEIVE.il());
        }
        HeroTuJianListView.instance.getAllTuJianbtnshow();
    }

    private OnClickBtnSound(): void {
        this.playSound();
    }

    private onClickimgPillar(): void {
        if (this._heroCfg && this._heroCfg.pillar != 0) {
            const desc:string = this._heroCfg.pillar === 1 ? window.iLang.L2_CHUAN_QI_HERO_HUO_QU_NAN_DU_JIAO_DA_ch31_YI_BAN_SHI_GE_ZHEN_YING_HE_XIN.il():"领域类英雄获取途径十分稀缺，养成难度更高，等同于神魔英雄";
            TipsUtil.showDescDialog(desc, {
                fontSize: 24,
                color: "#ed6e3c",
                width: 500,
                height: 200,
                bgSkin: "common2/panel_bg2.png",
            });
        }
    }

    private onClickBtnSoulDesc(): void {
        this.dispatchEvent(ModuleCommand.OPEN_HELP_RULE_DIALOG, { group_name: "hun_jiang" });
    }

    private OnCLickBtnClose(): void {
        if (this._selSubType == 1) {
            this.close();
        }
        else if (this._selSubType == 2) {
            this._selSubType = 1;
            this.refreshSubView();
        }
    }

    onOpen(param: any) {
        this._selTypeId = param.typeId;
        this._selIndex = param.index;
        this._selNation = param.nation;
        this._selStarIndex = param.starIndex || 0;
        this._selSubType = 1;

        if (this._selTypeId > 0) {
            let cfgHero = ConfigManager.cfg_hero_baseCache.get(this._selTypeId);
            this._selNation = cfgHero.nation;
        }

        let nation = this._selNation;
        if (this._heroCfgList) {

            this._heroCfgList = this._heroCfgList.filter(function (heroCfg) {
                let is_shield_hero = HeroTuJianDataCenter.instance.isShieldHero(heroCfg.type_id);
                let is_pre_hero = HeroTuJianDataCenter.instance.isPreHero(heroCfg.type_id);
                let is_show_hero = true;
                if (is_shield_hero && !is_pre_hero) {
                    is_show_hero = false;
                }
                return heroCfg.nation == nation && is_show_hero;
            })

        } else {

            this._heroCfgList = HeroDataCenter.baseCfgHeroArr.filter(function (heroCfg) {
                let is_shield_hero = HeroTuJianDataCenter.instance.isShieldHero(heroCfg.type_id);
                let is_pre_hero = HeroTuJianDataCenter.instance.isPreHero(heroCfg.type_id);
                let is_show_hero = true;
                if (is_shield_hero && !is_pre_hero) {
                    is_show_hero = false;
                }
                return heroCfg.nation == nation && is_show_hero;
            }).sort(function (cfg1, cfg2) {
                return cfg2.sort_id - cfg1.sort_id;
            })

        }

        if (this._selTypeId > 0) {
            for (let i = 0; i < this._heroCfgList.length; ++i) {
                let cfg = this._heroCfgList[i];
                if (cfg.type_id == this._selTypeId) {
                    this._selIndex = i;
                    break;
                }
            }
        }

        this.refreshView();
    }

    private refreshView(): void {
        this.refreshInfo();
        this.refreshUI();
        this.refreshTab();
        this.refreshShow3D();
        this.refreshSubView();
        this.refreshRedPoint();
    }

    private refreshInfo(): void {
        if (!this._heroCfgList || this._selIndex >= this._heroCfgList.length) {
            return;
        }
        this._heroCfg = this._heroCfgList[this._selIndex];

      /* 如果为预告英雄则展示反馈按钮 */
      if (HeroTuJianDataCenter.instance.isPreHero( this._heroCfg?.type_id )) {

            this.previewBox.visible = true;

        } else {

            this.previewBox.visible = false;

        }

        /* 是否为内部英雄 */
        if (HeroTuJianDataCenter.instance.isInnerHero( this._heroCfg?.type_id )) {

            this.innerTag.visible = true;

        } else {

            this.innerTag.visible = false;
        }

        let typeId = this._heroCfg.type_id;
        let nation = this._heroCfg.nation;
        let isSoulHero = nation == HeroNation.NATION_HUN;
        HeroTuJianDataCenter.instance._select_hero_type_id = typeId;
        let initStar = this._heroCfg.init_star;
        let maxStar = HeroDataCenter.instance.getMaxStarByLimit(typeId, !HeroDataCenter.instance.isHadActiveUpStar14());
        let maxStarStages = HeroDataCenter.instance.getMaxStarStageListByLimit(typeId, maxStar + 1);
        let maxZhouyinLv = HeroZhouyinDataCenter.getMaxLevel(typeId);
        let curZhouyinLv = HeroZhouyinDataCenter.instance.getCurLevel(typeId);
        let minSoulLv = ConfigManager.cfg_soul_hero_link_levelCache.get_min();
        let maxSoulLv = ConfigManager.cfg_soul_hero_link_levelCache.get_max();
        let curStar = this._selStarIndex >= 1 ? maxStar : initStar;
        let curStarStage = this._selStarIndex >= 1 ? maxStarStages : [];
        let curSoulLv = this._selStarIndex >= 1 ? maxSoulLv : minSoulLv;
        this._heroZhouyinLv = this._selStarIndex == 3 ? maxZhouyinLv : curZhouyinLv;
        let curSatge = ConfigManager.getHeroMaxUpgradeStage(curStar);
        let curLevel = ConfigManager.GetHeroMaxUpgradeLevel(curStar, curSatge, curStarStage);

        let heroInfo = this._heroInfo || new p_hero();
        heroInfo.type_id = typeId;
        heroInfo.star = curStar;
        heroInfo.star_stage = curStarStage;
        heroInfo.stage = curSatge;
        heroInfo.level = curLevel;
        heroInfo.soul_level = isSoulHero ? curSoulLv : 0;
        heroInfo.skin_id = HeroUtil.getRealSkinId(heroInfo.skin_id);

        heroInfo.fight = HeroUtil.getHeroFightAttr(typeId, { 
            level: curLevel, stage: curSatge, star: curStar, zhouyinLv: this._heroZhouyinLv,
            skinId: heroInfo.skin_id,showSelfAwake: this._heroZhouyinLv > 0});

        heroInfo.skill_list = [];
        let skillDatas = GameUtil.parseCfgByField(this._heroCfg, "skill_");
        for (let i = 0; i < skillDatas.length; ++i) {
            let skillId = skillDatas[i][0];
            let skillLv = SkillDataCenter.ins.getMaxSkillLv(skillId, curLevel, curSatge, curStar);
            if (skillId) {
                let skillKV = new p_kv();
                skillKV.key = skillId;
                skillKV.val = skillLv;
                heroInfo.skill_list.push(skillKV);
            }
        }

        heroInfo.power = HeroUtil.calcPower(heroInfo.fight);
        this._heroInfo = heroInfo;
    }
    public updateAttrInfo() {
         //设置英雄属性
         let arr: any[] = [];
         if (this._heroInfo != null) {//代表是已经激活的英雄
             let cfgArr: fightAttr[] = ConfigManager.cfg_fightAttrByTypeCache.get(HeroDataCenter.ATTR_SHOW_TYPE_NORAML);
             cfgArr.sort(function (vo1: fightAttr, vo2: fightAttr) {
                 return vo1.sort - vo2.sort;
             });
 
             for (let i: number = 0; i < cfgArr.length; i++) {
                 let vo: fightAttr = cfgArr[i];
                 let obj: any = new Object();
                 obj.attrValue = FightAttrMgr.getIncValue(this._heroInfo.fight[vo.attrKey], this._heroInfo.fight[vo.attrKey + "_inc"]);
                 obj.attrName = vo.attrDesc;
                 obj.icon = vo.icon;
                 arr.push(obj);
             }
         }
         else if (this._heroCfg != null) {
             //英雄基础属性
             let fight_attr_vos = HeroUtil.getHeroFinalAttr(this._heroCfg.type_id);
             for (let i: number = 0; i < fight_attr_vos.length; i++) {
                 let obj: any = new Object();
                 let vo: FightAttrVO = fight_attr_vos[i];
                 obj.attrValue = vo.valString;
                 obj.attrName = vo.name;
                 obj.icon = vo.icon;
                 arr.push(obj);
             }
         }
         for (let index = 0; index < arr.length; index++) {
              const attrItem = this.attrList[index];
              if (attrItem) {
                attrItem.setData(arr[index]);
              }
         }
    }
    public updateSkillInfo() {
        //w10 sentry容错
        if(!this._heroInfo){
            return;
        }
        let skillIds = SkillDataCenter.ins.getHeroSkillIdsByHeroInfo(this._heroInfo);
        let skill_id_level_map = SkillDataCenter.ins.GetHeroActiveSkills_id_Level_map(this._heroInfo);
        for (let i = 0; i < this._skillItemList.length; ++i) {
            let item = this._skillItemList[i];
            let skillId = skillIds[i];
            if (item && skillId) {
                item.visible = true;
                let level = skill_id_level_map[skillId];
                let vo = new SkillItemVo();
                vo.skill_id = skillId;
                vo.skill_level = level;
                vo.heroInfo = this._heroInfo;
                vo.id_level_map = skill_id_level_map;
                vo.isShowName = true;
                vo.isShowLevel = true;
                vo.isGrayWhenNotActive = true;
                item.setDataBySkillItemVO(vo,true);
                item.setIsShowCastSoulBg(vo.skill_level == 4);
            }
            else {
                item.visible = false;
            }
        }
    }


    private _starScript: HeroStarScript;
    private get starScript(): HeroStarScript {
        if (!this._starScript) {
            this._starScript = UIFactory.bindHeroStarScript(this.boxStar, UIFactory.SIZE_38);
        }
        return this._starScript;
    }
    private refreshUI(): void {
        this.lbHeroName.text = this._heroCfg.name;
        this.nationImg.skin = `${UrlConfig.COMMON_PATH}hero_nation_${this._selNation}.png`;
        this.backGround.skin = `${UrlConfig.HERO_INFO_V2_UI_URL}nation_${this._heroCfg.nation}_bg.jpg`;
        this.fcPower.value = Math.floor(this._heroInfo.power).toString();
        this.starScript.setStar(this._heroInfo.star);
        this.careerImg.skin = UrlConfig.COMMON_PATH + "career_" +  this._heroCfg.career + ".png";
        this.lbCareerName.text = GameConst.getCareerName( this._heroCfg.career);
        this.lbCareerDesc.text = `(${ this._heroCfg.desc})`;
        this.btnLeft.visible = this._selIndex > 0;
        this.btnRight.visible = this._selIndex < this._heroCfgList.length - 1;
        this.imgPillar.visible = this._heroCfg.pillar !== 0;
        this.imgPillar.skin = HeroUtil.getPillarSkinUrl(this._heroCfg.pillar);
        this.showPillarEff(this._heroCfg.pillar === 2,this._heroCfg.nation);
        this.btnGet.visible = !HeroTuJianDataCenter.instance.isPreHero(this._heroCfg.type_id);
        this.updateLevelInfo();
        /* 战斗演示是否展示 */
        this.btnShow.visible=this._heroCfg.fight_show_id==0 ? false : true;

        if (this._heroInfo && this._heroInfo.star >= 10) {
            if (this._heroCfg.nation != this._preNation && this._starBgEff) {
                this._starBgEff.removeSelf();
                this._starBgEff.destroy();
                this._starBgEff = null;
            }
            if (!this._starBgEff) {
                this._starBgEff = this.ShowEffectCenter("hero_back_" + this._heroCfg.nation, this.effectBox, true);
            }
            this._starBgEff.resume();
            this._starBgEff.visible = true;
        }
        else if (this._starBgEff) {
            this._starBgEff.stop();
            this._starBgEff.visible = false;
        }
    }
    private updateLevelInfo() {
        let curLevel = this._heroInfo["ori_level"] || this._heroInfo.level;
        let curStage = HeroDataCenter.getHeroBigStage(this._heroInfo);
        let curStar = this._heroInfo.star;
        let curStarStage = this._heroInfo.star_stage;
        let curSoulLv = this._heroInfo.soul_level;
        let maxStar = this._heroCfg.max_star;
        let nextStar = Math.min(maxStar, curStar + 1);
        let isMaxFinalStar = maxStar == HeroConsts.FINAL_STAR;
        if (isMaxFinalStar && !HeroDataCenter.instance.isHadActiveUpStar14()) {
            nextStar = Math.min(maxStar - 1, curStar + 1);
        }

        //异能英雄等级显示
        let heroCfg = ConfigManager.cfg_hero_baseCache.get(this._heroInfo.type_id);
        let isSoulHero = heroCfg.nation == HeroNation.NATION_HUN;
        //当前星级允许的最大阶级
        let maxStage: number = ConfigManager.getHeroMaxUpgradeStage(this._heroInfo.star);
        let starMaxLevel: number = ConfigManager.GetHeroMaxUpgradeLevel(this._heroInfo.star, maxStage, this._heroInfo.star_stage);
        //当前星级、星阶和阶级允许的最大等级
        let stageMaxLevel: number = ConfigManager.GetHeroMaxUpgradeLevel(this._heroInfo.star, this._heroInfo.stage, this._heroInfo.star_stage);
        this.lbStarStage.text = curStar >= HeroConsts.openStarStageLV ? window.iLang.L2_XING_JIE_ch05.il() : window.iLang.L2_JIN_JIE_ch05.il();
        this.lbLevel.text = (this._heroInfo ? curLevel : 1) + "/" + stageMaxLevel;
        this.stageBox.visible = curStar < HeroConsts.openStarStageLV;
        this.starStageBox.visible = curStar >= HeroConsts.openStarStageLV;
        this.soulLvBox.visible = isSoulHero && curSoulLv > 1;
        if (this.soulLvBox.visible) {
            //设置能级
            this.lbStarStage.text = window.iLang.L2_NENG_JI_ch05.il();
            this.setSoulLv(curSoulLv, curSoulLv);
        }
        else if (curStar >= HeroConsts.openStarStageLV) {
            //设置星阶
            let cfgList = ConfigManager.getCfgHeroStarStageList(this._heroInfo.type_id, nextStar);
            this.setStarStageList(curStarStage, cfgList, curStar);
        }
        else {
            //设置英雄阶级
            this.setStageLv(curStage, maxStage);
        }
    }
    /**设置领域英雄特效 */
    private showPillarEff(isShowEff:boolean,nation: number){
        if (this.pillarEff) {
            this.pillarEff.removeSelf();
            this.pillarEff.destroy();
            this.pillarEff = null;
        }
        if (isShowEff) {
            const skName = PillarEffType[nation];
            this.pillarEff = this.showGSkeleton(this.nationImg,"hunyu",this.pillarEff,{x:30,y:8});
            this.pillarEff.playAction(skName);
        }

    }
    /**设置能级 */
    public setSoulLv(level: number = 0, maxLevel: number = 0): void {
        let arr: any[] = [];
        for (let i: number = 0; i < maxLevel; i++) {
            arr.push(i + 1);
        }
        this._soulLvList.visible = true;
        this._soulLvList.AddOtherParamete("curStage", level);
        this._soulLvList.array = arr;
    }
    /**设置英雄阶级 */
    public setStageLv(level: number = 0, maxStage: number = 0): void {
        let arr: any[] = [];
        for (let i: number = 0; i < maxStage; i++) {
            arr.push(i + 1);
        }
        this._stageList.visible = true;
        this._stageList.AddOtherParamete("curStage", level);
        this._stageList.array = arr;
    }

    /**设置星阶 */
    public setStarStageList(starStageList: number[], starStageCfg: cfg_hero_star_stage[], star: number = 0): void {
        this._starStageList.parameter = { 
            starStageList: starStageList,
            star: star,
        };
        this._starStageList.array = starStageCfg;
    }
    private refreshTab(): void {
        let tabDatas = [];
        if (this._heroCfg.max_star != this._heroCfg.init_star) {
            tabDatas.push(UITab.GetItemData(0, window.iLang.L2_CHU_SHI_XING_JI.il()));
        }
        if (this._heroCfg.nation == HeroNation.NATION_HUN) {
            tabDatas.push(UITab.GetItemData(1, window.iLang.L2_ZUI_GAO_NENG_JI.il()));
        }
        else if (HeroDataCenter.instance.isHadActiveUpStar14()) {
            tabDatas.push(UITab.GetItemData(1, window.iLang.L2_ZHONG_JI_XING_JI.il()));
        }
        else {
            let maxStar = HeroDataCenter.instance.getMaxStarByLimit(this._heroCfg.type_id, true);
            tabDatas.push(UITab.GetItemData(1, window.iLang.L2_P0_XING.il([StringUtil.numberToCN(maxStar)])));
        }
        //w10优化,新版本觉醒,先屏蔽下面旧的觉醒.
        if (HeroZhouyinDataCenter.checkIsOpenZhouyin(this._heroCfg.type_id)) {
            tabDatas.push(UITab.GetItemData(3, window.iLang.L2_MAN_JI_JUE_XING.il()));
        }

        this._starTab.array = tabDatas;
        this._starTab.selectedIndex = Math.min(this._selStarIndex, tabDatas.length - 1);
    }

    private refreshSubView(): void {
        let isShowSubView = this._selSubType == 1;
        this.rewardBox.visible = this.lieZView.visible = !isShowSubView;
        this.contentBox.visible = this.imgPanelBg.visible = this.tabBox.visible = isShowSubView;
        this.btnLieZ.visible = isShowSubView;
        let type_id = this._heroCfg.type_id;
        let isSoulHero = this._heroCfg.nation == HeroNation.NATION_HUN;
        let isHadHero = HeroDataCenter.instance.getHeroByTypeId(type_id);
        let isHadHeroTuJIan = HeroTuJianDataCenter.instance.isHadHeroTuJian(type_id);
        let isFetchedReward = HeroTuJianDataCenter.instance.isFetchedReward(type_id);
        let isCanFetchReward = HeroTuJianDataCenter.instance.isCanFetchReward(type_id);
        let descCfg = ConfigManager.cfg_hero_handbook_descCache.get(type_id);
        if (descCfg) {
            this.lbDesc.text = "       " + StringUtil.br2LF(descCfg.desc);
        }
        this.lbFetched.visible = isFetchedReward;
        this.lbCanFetch.visible = isCanFetchReward;
        this.lbNotFetch.visible = !isHadHeroTuJIan;
        this.imgReward.gray = !isHadHeroTuJIan;
        this.growth_road.visible = isHadHero && !isSoulHero && isHadHeroTuJIan && isShowSubView;
        this.btnSoulHelp.visible = isSoulHero;
        this.lbNum.text = GameUtil.gold(this._heroCfg.collect).toString();
        this.updateSkillInfo();
        this.updateAttrInfo();
        this.updateEvolveSkill();
    }

    private refreshShow3D(): void {
        this.show3d.offAllCaller(this);
        this.show3d.once(Event.COMPLETE, this, () => {
            this.playSound();
        });

        if (this._heroInfo && this._heroInfo.skin_id != 0) {
            this.show3d.refreshHeroSkin(this._heroInfo.skin_id);
        }
        else {
            this.show3d.refreshHero(this._heroCfg.type_id);
        }
    }

    private refreshRedPoint(): void {
        if (!this._heroCfg) {
            return;
        }
        let isCanFetch = HeroTuJianDataCenter.instance.isCanFetchReward(this._heroCfg.type_id)
        this.SetRedPoint(this.btnLieZ, isCanFetch);
        this.SetRedPoint(this.imgReward, isCanFetch, 65, 10);
    }

    private updateEvolveSkill() {
        //进化
        let heroInfo = this._heroInfo;
        let cfg = CfgCacheMapMgr.cfg_hero_evolve_skillCache.get(heroInfo.type_id);
        if (!cfg) {
            this.boxEvolveSkill.visible = false;
            return;
        }

        this.boxEvolveSkill.visible = true && this._selSubType == 1;
        this.btnEvolveSkill.visible = false;
        //改为默认显示
        let canEvolve = true;
        this.lbEvolveSkill.text = cfg.star_limit + window.iLang.L2_XING_KAI_QI.il();
        this.lbEvolveSkill.visible = false;
        this.btnEvolveSkill.visible = true;

        if (canEvolve) {
            canEvolve = HeroEvolveDataCenter.ins.checkCanEvolveSkill(heroInfo.hero_id);
            this.SetRedPoint(this.btnEvolveSkill, canEvolve,);
        }
    }

    private playSound(): void {
        if (!this._heroCfg) {
            return;
        }

        if (this._preSound1) GameSoundManager.instance.stopEffect(this._preSound1);
        if (this._preSound2) GameSoundManager.instance.stopEffect(this._preSound2);
        if (this._heroCfg.sound1) GameSoundManager.instance.playEffect(this._heroCfg.sound1);
        if (this._heroCfg.sound2) GameSoundManager.instance.playEffect(this._heroCfg.sound2);
        this._preSound1 = this._heroCfg.sound1;
        this._preSound2 = this._heroCfg.sound2;
    }

        public close(): void {
        if(this.show3d) this.show3d.offAllCaller(this);
        if (this._preSound1) GameSoundManager.instance.stopEffect(this._preSound1);
        if (this._preSound2) GameSoundManager.instance.stopEffect(this._preSound2);

        super.close();
    }
    public onClickBtnEvolveSkill() {
        if (!this._heroInfo) {
            return
        }
        this.dispatchEvent(ModuleCommand.OPEN_EVOLVE_SKILL_INFO_DIALOG, { heroInfo: this._heroInfo,showUpgrade:false });
    }
}