
/**
 * 官渡之战 战斗相关 DataCenter
 */

import { cfg_guandu } from "../../../cfg/vo/cfg_guandu";
import { ConfigManager } from "../../../managers/ConfigManager";
import { DispatchManager } from "../../../managers/DispatchManager";
import { p_guandu_enemy } from "../../../proto/common/p_guandu_enemy";
import { p_guandu_hero } from "../../../proto/common/p_guandu_hero";
import { LocalStorageUtil } from "../../../util/LocalStorageUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { DataCenter } from "../../DataCenter";
import { FightDataCenter } from "../../fight/data/FightDataCenter";
import { GameConst } from "../../GameConst";
import { LineUpDataCenter } from "../../lineUp/data/LineUpDataCenter";
import { MiscConst } from "../../misc_config/MiscConst";
import { ModuleCommand } from "../../ModuleCommand";
import { YueKaDataCenter} from "../../welfare/data/YueKaDataCenter";
import { GuanDuConst } from "../GuanDuConst";
import { GuanDuDataCenter } from "./GuanDuDataCenter";
import { MatchConst } from "../../../auto/ConstAuto";

//本模块引用的时候不要缓存instance，每次通过instance获取即可
export class GuanDuFightDataCenter {
    private static _instance: GuanDuFightDataCenter = null;
    static get instance(): GuanDuFightDataCenter {
        if (GuanDuFightDataCenter._instance == null) {
            GuanDuFightDataCenter._instance = new GuanDuFightDataCenter();
        }
        return GuanDuFightDataCenter._instance;
    }

    reset(): void {
        GuanDuFightDataCenter._instance = null;
    }

    /**英雄列表 */
    private heroList:p_guandu_hero[];
    /**怪物列表 */
    private monsterList:p_guandu_enemy[];
    /**当前选中的英雄id */
    private _selectHeroId:number = 0;
    set selectHeroId(id:number) {
        // if(id == this._selectHeroId) return;
        this._selectHeroId = id;
        DispatchManager.dispatchEvent(ModuleCommand.CHANGE_GUAN_DU_HERO_SELECT,id);
    }
    get selectHeroId() {
        return this._selectHeroId;
    }

    /**自动扫荡 */
    private _isAutoFightIng:boolean = false;
    set isAutoFightIng(val:boolean) {
        this._isAutoFightIng = val;
    }
    get isAutoFightIng() {
        return this._isAutoFightIng;
    }

    getHeroByHeroId(hero_id:number):p_guandu_hero {
        if(!this.heroList) return null;
        for(let h of this.heroList){
            if(h.hero_id == hero_id){
                return h;
            }
        }
        return null;
    }

    /**获取英雄列表 */
    getHeroList():p_guandu_hero[] {
        return this.heroList;
    }

    getMonsterList():p_guandu_enemy[] {
        return this.monsterList;
    }

    /**获取怪物列表 */
    getMonsterShowList():p_guandu_enemy[] {
        if(!this.monsterList) return [];
        //筛选出没死亡的怪物
        let list = this.monsterList.filter(p => {
            return p.status != GuanDuConst.STATUS_ENEMY_DEATH;
        });
        //TODO 最后一个怪 先不显示，打完前4个后再显示
        if(list.length >= 2){
            //过滤掉最后一个怪
            list = list.filter(p => p.level != 5);
        }
        return list;
    }

    updateHeroList(list:p_guandu_hero[]):void {
        this.heroList = list;
        //排序
        if(list){
            list.sort((t1,t2) => t2.power - t1.power);
        }
        DispatchManager.dispatchEvent(ModuleCommand.UPDATE_GUAN_DU_HERO_LIST);
    }

    //是否显示怪物已击杀完 飘字提示
    public isShowKillAllTip:boolean = false;
    
    updateMonsterList(list:p_guandu_enemy[]):void {
        let oldList = this.monsterList;
        this.monsterList = list;
        //排序
        if(list){
            list.sort((a,b) => a.level - b.level);
        }
        if(oldList){
            let oldKillAll:boolean = true;
            for(let e of oldList){
                if(e.status != GuanDuConst.STATUS_ENEMY_DEATH){
                    oldKillAll = false;
                    break;
                }
            }
            let isKillAll:boolean = true;
            for(let e of list){
                if(e.status != GuanDuConst.STATUS_ENEMY_DEATH){
                    isKillAll = false;
                    break;
                }
            }
            if(oldKillAll == false && isKillAll == true){
                //当前关卡的怪全部击杀完
                // TipsUtil.showTips("击败守关将领，可进入下一关");//不能在这飘 得重新打开界面才飘提示
                this.isShowKillAllTip = true;
            }
        }
        DispatchManager.dispatchEvent(ModuleCommand.UPDATE_GUAN_DU_MONSTER_LIST);
    }

    getMonsterInfoByLevel(level:number):p_guandu_enemy {
        if(!this.monsterList) return null;
        for(let m of this.monsterList){
            if(m.level == level) return m;
        }
        return null;
    }

    getGuanDuCfg(order_id:number , floor:number = -1):cfg_guandu {
        if(floor == -1){
            floor = GuanDuDataCenter.instance.floor;
        }
        let cfgList = ConfigManager.cfg_guanduCache.get(floor);
        return cfgList[order_id-1];
    }

    /**是否选中跳过战斗 */
    set isSelectSkipFight(val:boolean) {
        LocalStorageUtil.setUserLocalStorage("GuanDuSkipFight",val+"");
    }
    /**是否选中跳过战斗 */
    get isSelectSkipFight(){
        if(!this.isShowSkipFightCheckBox()) return false;
        let b:boolean = LocalStorageUtil.CheckUserLocalStorage("GuanDuSkipFight");
        return b;
    }

    /**是否显示跳过战斗 勾选框 */
    isShowSkipFightCheckBox():boolean {
        // let nums = MiscConst.guandu_pass_limit;
        // // if(GuanDuDataCenter.instance.floor >= nums[0] || DataCenter.vipLevel >= nums[1]){
        // //     return true;
        // // }
        // //2022/10/10 只保留VIP限制
        // if(DataCenter.vipLevel >= nums[1]){
        //     return true;
        // }
        // let yueKaType = EYueKaType.TYPE_4_KING;//2022/10/20 改为月卡激活
        // if(YueKaDataCenter.instance.isOpenFuliYueKa(yueKaType)){
        //     return true;
        // }
        // return false;
        return true;//2022/11/30 改为默认激活
    }

    /**战斗开始协议 */
    static guandu_fight_start_tos(boss_level:number,is_skin:boolean,targetname:string = ""):void {
        LineUpDataCenter.setFightSkip(MatchConst.MATCH_TYPE_GUAN_DU,is_skin);
        let lineUp:number[] = [];
        for(let i:number = 0; i < 9; i++){
            lineUp.push(0);
        }
        lineUp[1] = GuanDuFightDataCenter.instance.selectHeroId;
        FightDataCenter.instance.m_fight_start_tos(MatchConst.MATCH_TYPE_GUAN_DU,boss_level,{
            is_show_skip:is_skin,
            forceSkip:is_skin,
            fighter_list:lineUp,
            targetname: targetname,
            finishevent:ModuleCommand.GUAN_DU_FIGHT_FINISH
        });
    }
}
