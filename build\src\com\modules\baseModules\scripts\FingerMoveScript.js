import { BaseScript } from "../../BaseScript";
import { <PERSON><PERSON> } from "laya/utils/Handler";
import { Tween } from "laya/utils/Tween";
import { Image } from "laya/ui/Image";
import { LayerManager } from "../../../managers/LayerManager";
/**
 * 手指移动脚本
 */
export class FingerMoveScript extends BaseScript {
    constructor() {
        super(...arguments);
        this.isPlaying = false;
    }
    initUI() {
        this.fingerIcon = new Image();
        this.fingerIcon.skin = "common3/guide_1.png";
        this.fingerIcon.name = "fingerIcon";
        this.ownUi.addChild(this.fingerIcon);
        this.fingerIcon.zOrder = LayerManager.DIALOG_ZORDE_99999;
    }
    addClick() {
    }
    addEvent() {
    }
    /**
     * 设置手指移动
     * @param from 开始位置
     * @param to 结束位置
     * @param loopInterval 循环间隔,若小于等于0则只移动一次
     */
    setMove(from, to, duration = 1000, loopInterval = 0) {
        this.isPlaying = true;
        this.fingerIcon.visible = true;
        this.fingerIcon.x = from.x;
        this.fingerIcon.y = from.y;
        this.from = from;
        this.to = to;
        this.duration = duration;
        this.loopInterval = loopInterval;
        if (this.timerLoop) {
            this.timerLoop.clear();
            this.timerLoop = null;
        }
        this.twMove = Tween.to(this.fingerIcon, { x: to.x, y: to.y }, duration, null, Handler.create(this, this.onMoveComplete), 0, true, false);
    }
    onMoveComplete() {
        this.onFinish();
        if (this.loopInterval > 0) {
            this.fingerIcon.visible = true;
            this.timerLoop = this.timer.once(this.loopInterval, this, this.setMove, [this.from, this.to, this.duration, this.loopInterval]);
        }
        else {
        }
    }
    onFinish() {
        if (this.timerLoop) {
            this.timerLoop.clear();
            this.timerLoop = null;
        }
        if (this.twMove) {
            this.twMove.clear();
            this.twMove = null;
        }
        this.fingerIcon.visible = false;
        this.isPlaying = false;
    }
    onDestroy() {
        this.fingerIcon.destroy();
        super.onDestroy();
    }
}
