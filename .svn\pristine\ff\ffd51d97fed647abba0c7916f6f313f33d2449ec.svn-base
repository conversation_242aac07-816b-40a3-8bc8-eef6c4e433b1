import { Event } from "laya/events/Event";

import { ConfigManager } from "../../../managers/ConfigManager";
import { DispatchManager } from "../../../managers/DispatchManager";
import { LayerManager } from "../../../managers/LayerManager";

import { p_fight_stats } from "../../../proto/common/p_fight_stats";
import { m_cross_ladder_fight_result_toc } from "../../../proto/line/m_cross_ladder_fight_result_toc";
import { m_fight_finish_times_toc } from "../../../proto/line/m_fight_finish_times_toc";
import { m_fight_finish_toc } from "../../../proto/line/m_fight_finish_toc";
import { m_fight_set_speed_toc } from "../../../proto/line/m_fight_set_speed_toc";
import { m_fight_simp_result_toc } from "../../../proto/line/m_fight_simp_result_toc";
import { m_fight_start_pass_toc } from "../../../proto/line/m_fight_start_pass_toc";
import { m_fight_start_toc } from "../../../proto/line/m_fight_start_toc";
import { m_god_trial_fight_result_toc } from "../../../proto/line/m_god_trial_fight_result_toc";
import { m_main_battle_fight_result_toc } from "../../../proto/line/m_main_battle_fight_result_toc";
import { m_red_cliff_fight_result_toc } from "../../../proto/line/m_red_cliff_fight_result_toc";
import { m_test_tower_fight_result_toc } from "../../../proto/line/m_test_tower_fight_result_toc";
import { SocketCommand } from "../../../proto/SocketCommand";
import { ConsoleUtils } from "../../../util/ConsoleUtils";
import { TipsUtil } from "../../../util/TipsUtil";
import { BaseController } from "../../BaseController";
import { CrossLadderDataCenter } from "../../crossLadder/data/CrossLadderDataCenter";

import { FightPlayBackDialog } from "../../fightPlayBack/dialog/FightPlayBackDialog";
import { GameConst } from "../../GameConst";
import { GodTrialDataCenter } from "../../godTrial/data/GodTrialDataCenter";
import { GoodsVO } from "../../goods/GoodsVO";
import { GuaJiDataCenter } from "../../guaJi/data/GuaJiDataCenter";
import { GuaJiUtil } from "../../guaJi/util/GuaJiUtil";
import { ModuleCommand } from "../../ModuleCommand";
import { RedCliffDataCenter } from "../../redCliff/data/RedCliffDataCenter";
import { StoryUtil } from "../../story/util/StoryUtil";
import { TestTowerDataCenter, TowerType } from "../../testTower/data/TestTowerDataCenter";
import { FightReplay, FightType } from "../data/FightConst";
import { FightDataCenter } from "../data/FightDataCenter";
import FightBuffDialog from "../dialog/FightBuffDialog";

import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { m_fight_times_toc } from "../../../proto/line/m_fight_times_toc";
import { BattleTrialModel } from "../../battleTrial/const/BattleTrialConst";
import { BattleTrialDataCenter } from "../../battleTrial/data/BattleTrialDataCenter";
import { BattleTrialMiscConst } from "../../battleTrial/data/BattleTrialMiscConst";
import { CrossTowerType } from "../../crossTestTower/data/CrossTestTowerDataCenter";
import { DataCenter } from "../../DataCenter";
import { LineUpMgr } from "../../lineUp/mgr/LineUpMgr";
import { StageCopyDataCenter } from "../../stagecopy/data/StageCopyDataCenter";
import { TdTrialDataCenter } from "../../tdTrial/data/TdTrialDataCenter";
import { FightBuffIconDialog } from "../dialog/FightBuffIconDialog";
import FightBuffInfoDialog from "../dialog/FightBuffInfoDialog";
import FightDialog from "../dialog/FightDialog";
import FightFailDialog from "../dialog/FightFailDialog";
import { FightStatDialog } from "../dialog/FightStatDialog";
import { FightStatisticsDialog } from "../dialog/FightStatisticsDialog";
import FightSuccCommon from "../dialog/FightSuccCommon";
import FightSuccDialog from "../dialog/FightSuccDialog";
import { FightUtil } from "../util/FightUtil";
import { TdMainDataCenter } from "../../tdMain/data/TdMainDataCenter";
import { FightStatVo } from "../vo/FightStatVo";
import { MatchConst } from "../../../auto/ConstAuto";
import { TestTowerUtil } from "../../testTower/util/TestTowerUtil";

export class FightController extends BaseController {

    private _fightdialog: FightDialog = null;
    private _resultdialog: any = null;
    private _playBackDialog: FightPlayBackDialog = null;
    private _fightStatDialog: FightStatisticsDialog = null;
    private _fightStat2Dialog: FightStatDialog = null;

    static instance: FightController;
    constructor() {
        super();
        FightController.instance = this;
    }

    protected reset(): void {
        super.reset();
    }

    protected initModuleListeners(): void {
        this.addEventListener(ModuleCommand.OPEN_FIGHT_DIALOG, this, this.openFightDialog);
        this.addEventListener(ModuleCommand.CLOSE_FIGHT_DIALOG, this, this.closeFightDialog);
        this.addEventListener(ModuleCommand.OPEN_FIGHT_RESULT_DIALOG, this, this.openFightResultDialog);
        this.addEventListener(ModuleCommand.OPEN_FIGHT_SUCC_DIALOG, this, this.openFightSuccDialog);
        this.addEventListener(ModuleCommand.CLOSE_FIGHT_SUCC_DIALOG, this, this.closeFightSuccDialog);
        this.addEventListener(ModuleCommand.OPEN_FIGHT_FAIL_DIALOG, this, this.openFightFailDialog);
        this.addEventListener(ModuleCommand.REPLAY_FIHISHED, this, this.toFinishFightPlayBackDialog);
        this.addEventListener(ModuleCommand.OPEN_FIGHT_STAT_DIALOG, this, this.openFightStatDialog);
        this.addEventListener(ModuleCommand.OPEN_FIGHT_STAT2_DIALOG, this, this.openFightStatDialog);

        this.addEventListener(ModuleCommand.MULTI_ENTER_FIGHT, this, this.enterFormalFight);
        this.addEventListener(ModuleCommand.TO_AUTO_FIGHT, this, this.toAutoFight);
        this.addEventListener(ModuleCommand.FIGHT_CONTEST_DATA, this, this.fightFinishInfoData);
        this.addEventListener(ModuleCommand.GROW_DAILY_FIGHT_FINISH, this, this.fightGrowDailyData);
        this.addEventListener(ModuleCommand.GOD_TRIAL_FIGHT_FINISH, this, this.fightGodTrialData);
        this.addEventListener(ModuleCommand.FIGHT_FINISH_DATA, this, this.fightFinishData);

        this.registerDialog(FightBuffDialog, ModuleCommand.OPEN_FIGHT_BUFF_DIALOG, ModuleCommand.CLOSE_FIGHT_BUFF_DIALOG);
        this.registerDialog(FightBuffInfoDialog, ModuleCommand.OPEN_FIGHT_BUFF_INFO_DIALOG, ModuleCommand.CLOSE_FIGHT_BUFF_INFO_DIALOG);
        this.registerDialog(FightBuffIconDialog, ModuleCommand.OPEN_FIGHT_BUFF_ICON_DIALOG);
    }

    protected initNetListeners(): void {
        this.addSocketListener(SocketCommand.FIGHT_SET_SPEED, this.m_fight_set_speed_toc);
        this.addSocketListener(SocketCommand.FIGHT_FINISH_TIMES, this.m_fight_finish_times_toc);
        this.addSocketListener(SocketCommand.FIGHT_START, this.m_fight_start_toc);
        this.addSocketListener(SocketCommand.FIGHT_FINISH, this.m_fight_finish_toc);

        this.addSocketListener(SocketCommand.FIGHT_START_PASS, this.m_fight_start_pass_toc);
        this.addSocketListener(SocketCommand.FIGHT_SIMP_RESULT, this.m_fight_simp_result_toc);
        this.addSocketListener(SocketCommand.MAIN_BATTLE_FIGHT_RESULT, this.m_main_battle_fight_result_toc);
        this.addSocketListener(SocketCommand.RED_CLIFF_FIGHT_RESULT, this.m_red_cliff_fight_result_toc);
        this.addSocketListener(SocketCommand.TEST_TOWER_FIGHT_RESULT, this.m_test_tower_fight_result_toc);
        this.addSocketListener(SocketCommand.GOD_TRIAL_FIGHT_RESULT, this.m_god_trial_fight_result_toc);
        // this.addSocketListener(SocketCommand.MAZE_FIGHT_RESULT, this.m_maze_fight_result_toc);
        this.addSocketListener(SocketCommand.CROSS_LADDER_FIGHT_RESULT, this.m_cross_ladder_fight_result_toc);
        this.addSocketListener(SocketCommand.FIGHT_TIMES, this.m_fight_times_toc);
    }

    openFightDialog(param: {
        info?: m_fight_start_toc,
        stepIdx?: number, roundIdx?: number, turnIdx?: number,
        isReview?: boolean, isReback?: boolean,
    }) {
        if (this._fightdialog && !this._fightdialog.destroyed) {
            this._fightdialog.reOpen(param);
            return;
        }
        this._fightdialog = new FightDialog(param);
        this._fightdialog.on(Event.CLOSE, this, function () {
            this._fightdialog = null;
        });

        this._fightdialog.open(false, param, false);
    }

    closeFightDialog() {
        if (this._fightdialog && !this._fightdialog.destroyed) {
            this._fightdialog.close();
            this._fightdialog = null;
        }
    }

    openFightResultDialog(param: any) {
        // let obj: any = { is_success: msg.is_success, rewards: msg.rewards };
        if (GuaJiDataCenter.instance.fight_finish_data.is_success == true) {
            let goods: any = GuaJiDataCenter.instance.fight_finish_data.gains;
            let goodVOList: GoodsVO[] = GuaJiDataCenter.parseGuajiReward(goods);
            if (this._resultdialog && !this._resultdialog.destroyed) {

                TipsUtil.setPopUpWindows(ModuleCommand.OPEN_FIGHT_RESULT_DIALOG, param, 0, true)
                return;
            }
            this._resultdialog = new FightSuccDialog();
            this._resultdialog.open(false, goodVOList);
            this._resultdialog.on(Event.CLOSE, this, function () {
                this._resultdialog = null;
            });

        }
        else {
            this.openFightFailDialog(MatchConst.MATCH_TYPE_MAIN_BATTLE);
        }
    }

    //成功的界面
    openFightSuccDialog(param: any): void {
        if (this._resultdialog && !this._resultdialog.destroyed) {
            //this.addEventListener(ModuleCommand.OPEN_FIGHT_SUCC_DIALOG, this, this.openFightSuccDialog);

            TipsUtil.setPopUpWindows(ModuleCommand.OPEN_FIGHT_SUCC_DIALOG, param, 0, true);
            return;
        }
        this._resultdialog = new FightSuccCommon();
        this._resultdialog.open(false, param);
        this._resultdialog.on(Event.CLOSE, this, function () {
            this._resultdialog = null;
        });

    }
    closeFightSuccDialog(param: any) {
        if (this._resultdialog && !this._resultdialog.destroyed) {
            this._resultdialog.close();
            this._resultdialog = null;
        }
    }

    openFightFailDialog(param: any) {
        //失败界面
        if (this._resultdialog && !this._resultdialog.destroyed) {

            TipsUtil.setPopUpWindows(ModuleCommand.OPEN_FIGHT_FAIL_DIALOG, param, 0, true);
            return;
        }
        this._resultdialog = new FightFailDialog();
        this._resultdialog.open(false, param, false);

        this._resultdialog.on(Event.CLOSE, this, function () {
            this._resultdialog = null;
        });

    }

    private toFinishFightPlayBackDialog(msg: m_fight_start_toc): void {

        if (msg.replay_type > FightReplay.NORMAL) {
            // if (MatchConst.MATCH_TYPE_ARENA_MATCH == msg.match_type) {
            //     this.openFightPlayBackDialog(msg);
            // } else {
                if (LayerManager.runningFight(msg.match_type, false)) {
                    if (!FightDataCenter.instance.checkFightClose(msg.match_type, { isReplay: true })) {
                    }
                }
                //this.m_fight_finish_toc_handle(msg);
            // }
        }
    }

    private openFightPlayBackDialog(param: any): void {
        if (this._playBackDialog && !this._playBackDialog.destroyed) { this._playBackDialog.reOpen(param); return; }
        this._playBackDialog = new FightPlayBackDialog(param);
        this._playBackDialog.on(Event.CLOSE, this, function () {
            this._playBackDialog = null;
        });
        this._playBackDialog.open(false, param);
    }

    openFightStatDialog(param: any): void {
        if (typeof (param) == "number") {
            let statsInfo = FightDataCenter.FIGHT_STATS_INFO_CACHE.get(param);
            if (statsInfo && statsInfo.length > 0) {
                this._openFightStat2Dialog(statsInfo[0]);
            }
        }
        else if (typeof (param) == "object") {
            this._openFightStat2Dialog(param);
        }
    }

    private _openFightStatDialog(match_type: number): void {
        if (this._fightStatDialog && !this._fightStatDialog.destroyed) { this._fightStatDialog.reOpen(match_type); return; }
        this._fightStatDialog = new FightStatisticsDialog();
        this._fightStatDialog.on(Event.CLOSE, this, function () {
            this._fightStatDialog = null;
        });

        this._fightStatDialog.open(false, match_type);
    }

    private _openFightStat2Dialog(param: any): void {
        if (this._fightStat2Dialog && !this._fightStat2Dialog.destroyed) { this._fightStat2Dialog.reOpen(param); return; }
        this._fightStat2Dialog = new FightStatDialog();
        this._fightStat2Dialog.on(Event.CLOSE, this, function () {
            this._fightStat2Dialog = null;
        });
        this._fightStat2Dialog.open(false, param);
    }





    private fightFinishInfoData(): void {
        let toc: m_fight_finish_toc = FightDataCenter.instance.fight_finish_info;
        this.m_fight_finish_toc(toc);
    }

    private fightGrowDailyData(): void {
        let toc: m_fight_simp_result_toc = FightDataCenter.instance.grow_daily_info;
        this.m_fight_simp_result_toc(toc);
    }

    private fightGodTrialData(): void {
        let toc: m_god_trial_fight_result_toc = GodTrialDataCenter.instance.fightResult;
        this.m_god_trial_fight_result_toc(toc);
    }

    private fightFinishData(matchType: number): void {
        let resultData = FightDataCenter.FIGHT_RESULT_DATA_CACHE.get(matchType);
        if (resultData) {
            let result = resultData.result;
            if (resultData.callBack) {
                resultData.callBack.runWith(result);
            }
        }
    }





    //-------------协议接收 start -------------
    m_fight_set_speed_toc(msg: m_fight_set_speed_toc): void {
        DispatchManager.dispatchEvent(ModuleCommand.UPDATE_RATE, msg.speed);
    }

    m_fight_finish_times_toc(msg: m_fight_finish_times_toc): void {
        GuaJiDataCenter.instance.finish_times = msg.finish_times;
        DispatchManager.dispatchEvent(ModuleCommand.UPDATE_FREE_PASS_TIMES);
    }

    m_fight_start_toc(msg: m_fight_start_toc): void {
        let isReview = msg.replay_type == FightReplay.REVIEW;//回放
        let swapToBack = msg.replay_type == FightReplay.BACK_FIGHT;//切换到后台战斗
        let isPass = msg.replay_type == FightReplay.PASS_FIGHT;//跳过战斗
        let hasData = !!LayerManager.getFightRecordData(msg.match_type);
        let isInBack = LayerManager.isFightInBack(msg.match_type);//当前战斗类型的状态，是否已经切换到后台
        let isCanBackFight = LayerManager.isCanFightInBack(msg.match_type);//可以连续后台战斗
        let isBaskFightType = msg.common_info.fightype == FightType.BACK_FIGHT;
        if (isCanBackFight && isBaskFightType && (!hasData || isInBack) && !isReview) {
            swapToBack = true;
        }

        let isRevert = FightDataCenter.instance.revertFightStart(msg);

        if (!isReview) {
            FightDataCenter.FIGHT_INFO_CACHE.set(msg.match_type, msg);
            let stats_list: p_fight_stats[] = [];
            for (let turnInfo of msg.turns) {
                let statsInfo = new p_fight_stats();
                if( isRevert ){
                    //统计结果也要进行反转
                    statsInfo.lf_name = turnInfo.right_name;
                    statsInfo.rt_name = turnInfo.left_name;
                    statsInfo.result = turnInfo.turn_result == 1 ? 2 : 1;
                }else{
                    statsInfo.lf_name = turnInfo.left_name;
                    statsInfo.rt_name = turnInfo.right_name;
                    statsInfo.result = turnInfo.turn_result;
                }


                statsInfo.round_cnt = turnInfo.rounds.length;
                statsInfo.stat_list = turnInfo.stat_list;
                stats_list.push(statsInfo);
            }
            FightDataCenter.FIGHT_STATS_INFO_CACHE.set(msg.match_type, stats_list);
            FightDataCenter.FIGHT_STATS_INFO_CACHE_VO.set(msg.match_type,FightStatVo.createVoByFightInfo(msg));
            LayerManager.delFightRecordData(msg.match_type);
        }
        else {
            FightDataCenter.FIGHT_REPLAY_INFO_CACHE.set(msg.match_type, msg);
        }

        //进入后台战斗
        if (swapToBack) {
            ConsoleUtils.log("进入后台战斗", msg.match_type);
            FightUtil.recordFightData(msg, {
                turnIdx: 0,
                roundIdx: 0,
                stepIdx: 0,
                isRefreshData: true,
                isInBack: true,
            });
            DispatchManager.dispatchEvent(ModuleCommand.START_FIGHT, msg);
            if (LayerManager.runningFight(msg.match_type)) {
                LayerManager.destroyMap(2);
            }
        }
        else if (!isPass) {
            if (LayerManager.runningFight(msg.match_type)) {
                this.enterFormalFight(msg, { destroyType: 2 });
            }
            else {
                this.enterFormalFight(msg);
            }
        }
    }

    private enterFormalFight(info: m_fight_start_toc, {
        stepIdx = 0,
        roundIdx = 0,
        lastTime = 0,
        turnIdx = 0,
        isReview = null,
        isReback = false,
        destroyType = 0,
    }: {
        stepIdx?: number,
        roundIdx?: number,
        turnIdx?: number,
        lastTime?: number,
        isReview?: boolean,
        isReback?: boolean,
        destroyType?: number,
    } = {}) {
        DispatchManager.dispatchEvent(ModuleCommand.START_FIGHT, info);

        //主线特制剧情战斗
        if (info.match_type == MatchConst.MATCH_TYPE_MAIN_BATTLE) {
            let storyCfg = StoryUtil.findStoryCfg(info.match_type, info.target_id);
            if (storyCfg) {
                LayerManager.runStory(storyCfg.id);
                return;
            }
        }

        else {
            GuaJiUtil.dispatchCloseGuaJiDialog();
        }

        if (isReview == null) {
            isReview = info.replay_type == FightReplay.REVIEW;
        } else {
            info.replay_type = FightReplay.REVIEW
        }


        LayerManager.runMap({
            fightData: info, stepIdx: stepIdx,
            roundIdx: roundIdx, lastTime: lastTime,
            turnIdx: turnIdx, isReview: isReview,
            isReback: isReback, destroyType: destroyType
        });
    }

    toAutoFight(matchType: number = 0, destroyType: number = 0) {
        LayerManager.exitMap(matchType, destroyType);
    }

    private m_fight_finish_toc(toc: m_fight_finish_toc): void {
        ConsoleUtils.log("m_fight_finish_toc", toc.match_type);
        if (toc == null) {
            return;
        }
        let data = LayerManager.getFightRecordData(toc.match_type);
        if (data) {
            data.isFinished = true;
        }
        if (LayerManager.currentMap && LayerManager.currentMap.match_type == toc.match_type && toc.finish_type == 99) {
            ConsoleUtils.error("战斗通信异常，强制退出");
            // DispatchManager.dispatchEvent(ModuleCommand.TO_AUTO_FIGHT, [0]);
            if (LayerManager.runningFight(toc.match_type)) {
                FightDataCenter.instance.checkFightClose(toc.match_type);
            }
            LayerManager.delFightRecordData(toc.match_type);
            return;
        }
        this.dispatchEvent(ModuleCommand.FIGHT_FINISH_END, [toc.match_type,toc.target_id]);

        FightDataCenter.instance.resetDownTime(toc.match_type);
        switch (toc.match_type) {
            case MatchConst.MATCH_TYPE_WORLD_BOSS:
            case MatchConst.MATCH_TYPE_MAIN_BATTLE:
            case MatchConst.MATCH_TYPE_ARENA:
            case MatchConst.MATCH_TYPE_DAILY_FB:
            case MatchConst.MATCH_TYPE_MAZE_ACT:
            case MatchConst.MATCH_TYPE_STORY_MAZE:
            case MatchConst.MATCH_TYPE_RED_CLIFF:
            case MatchConst.MATCH_TYPE_GOD_TRIAL:
            case MatchConst.MATCH_TYPE_TEST_TOWER:
            case MatchConst.MATCH_TYPE_GHOST_TOWER:
            case MatchConst.MATCH_TYPE_SKY_TOWER:
            case MatchConst.MATCH_TYPE_NATION_TOWER_1:
            case MatchConst.MATCH_TYPE_NATION_TOWER_2:
            case MatchConst.MATCH_TYPE_NATION_TOWER_3:
            case MatchConst.MATCH_TYPE_NATION_TOWER_4:
            case MatchConst.MATCH_TYPE_NATION_TOWER_5:
            case MatchConst.MATCH_TYPE_PASS_BEHEAD:
            case MatchConst.MATCH_TYPE_FAMILY_BOSS:
            case MatchConst.MATCH_TYPE_CROSS_FAMILY_SOLO:
            case MatchConst.MATCH_TYPE_LCQS:
            case MatchConst.MATCH_TYPE_CS_RANDOM_PVP:
            case MatchConst.MATCH_TYPE_CROSS_LADDER:
            case MatchConst.MATCH_TYPE_XSWH:
            case MatchConst.MATCH_TYPE_TEAM_XSWH:
            case MatchConst.MATCH_TYPE_STORY_ACT:
            case MatchConst.MATCH_TYPE_BATTLE_TRIAL:
            case MatchConst.MATCH_TYPE_MOCK_BATTLE:
            case MatchConst.MATCH_TYPE_DOMINATE_PVP:
            case MatchConst.MATCH_TYPE_DIVINE_COPY:
            case MatchConst.MATCH_TYPE_CROSS_CLAN_SOLO:
                return;
            case MatchConst.MATCH_TYPE_GUAN_DU:
                FightDataCenter.instance.checkFightClose(toc.match_type);
                return;
            case MatchConst.MATCH_TYPE_DICE_ACT:
                FightDataCenter.instance.fight_finish_info = toc;
                return;
            case MatchConst.MATCH_TYPE_HERO_CHALLENGE:
                FightDataCenter.instance.fight_finish_info = toc;
                return;
            case MatchConst.MATCH_TYPE_ARES_PALACE:
                if (toc.is_success) {
                    FightDataCenter.instance.fight_finish_info = toc;
                    this.dispatchEvent(ModuleCommand.OPEN_ARES_PALACE_SUCC_DIALOG, toc.target_id);
                    return;
                }
                break;
            case MatchConst.MATCH_TYPE_NEW_STORY:
                if (toc.operate_type != FightDataCenter.MULTI_FIGHT_OPREA1) {
                    this.dispatchEvent(ModuleCommand.CLOSE_FIGHT_DIALOG);
                    TipsUtil.onceOpenUI(ModuleCommand.OPEN_CITY_DIALOG);
                }
                return;
            case MatchConst.MATCH_TYPE_MASTER_CARD:
                if (toc.is_success == true) {
                    FightDataCenter.instance.checkFightClose(toc.match_type);
                } else {
                    this.openFightFailDialog(toc.match_type);
                }
                FightDataCenter.saveResultData(toc.match_type, toc, this, this.m_fight_finish_toc);
                return;
            case MatchConst.MATCH_TYPE_STAGE_COPY:
                FightDataCenter.instance.fight_finish_info = toc;
                StageCopyDataCenter.instance.isFristFight(toc);
                //胜利后播放剧情
                // if (toc.is_success && StageCopyDataCenter.instance.AfterDialogueMap.size > 0 && StageCopyDataCenter.instance.IsShowAfterStory) {
                if (toc.is_success && StageCopyDataCenter.instance.IsShowAfterStory) {
                    let Info = StageCopyDataCenter.instance.StageCopyCopyInfo;
                    this.dispatchEvent(ModuleCommand.OPEN_STAGECOPY_STORY_DIALOGUE_DIALOG,
                        // { fb_id:Info.cfg.fb_id,pass:Info.cfg.pass});
                        {});
                }
                return;
            // case GameConst.MATCH_TYPE_FIGHT_SHOW:
            //     if (toc.operate_type != FightDataCenter.MULTI_FIGHT_OPREA1) {
            //         this.dispatchEvent(ModuleCommand.TO_AUTO_FIGHT);
            //         TipsUtil.onceOpenUI(ModuleCommand.OPEN_CITY_DIALOG);
            //     }
            //     break;
            default:
                break;
        }

        FightDataCenter.instance.fight_finish_info = toc;
        FightDataCenter.saveResultData(toc.match_type, toc, this, this.m_fight_finish_toc);
        if (toc.is_success == true) {
            this.openFightSuccDialog(toc);
        } else {
            this.openFightFailDialog(toc.match_type);
        }
    }


    private m_fight_start_pass_toc(toc: m_fight_start_pass_toc): void {
        FightDataCenter.FIGHT_STATS_INFO_CACHE.set(toc.match_type, toc.stat_list);
        FightDataCenter.FIGHT_STATS_INFO_CACHE_VO.set(toc.match_type,FightStatVo.createVoByStatInfo(toc));
    }

    private m_fight_simp_result_toc(toc: m_fight_simp_result_toc): void {
        FightDataCenter.instance.grow_daily_info = toc;
        FightDataCenter.saveResultData(toc.match_type, toc, this, this.m_fight_simp_result_toc);

        if (toc.match_type == MatchConst.MATCH_TYPE_BATTLE_TRIAL) {
            this.battle_trial_fight_result(toc);
        }
        else if (toc.is_success == true) {
            if (toc.match_type == MatchConst.MATCH_TYPE_GUAN_DU) {
                //官渡不需要弹窗
                return;
            }else if (toc.match_type == MatchConst.MATCH_TYPE_PASS_BEHEAD) {
                TipsUtil.showTips(window.iLang.L2_NIN_CHENG_GONG_JI_KUI_LE_DUI_FANG.il());
            }

            if (toc.match_type == MatchConst.MATCH_TYPE_STAGE_COPY && StageCopyDataCenter.instance.IsShowAfterStory) {
                //境界副本如果有后续剧情并且在战斗场景先播放剧情再弹窗
                StageCopyDataCenter.instance.FightSimpResult = toc;
                return;
            }
            /**如果是塔防副本，并且是后台战斗时，不显示胜利弹窗 */
            if (toc.match_type == MatchConst.MATCH_TYPE_TD_TRIAL && TdTrialDataCenter.ins.isBackMode) {
                return;
            }
            if (toc.match_type == MatchConst.MATCH_TYPE_TD_MAIN || toc.match_type == MatchConst.MATCH_TYPE_TD_DAILY_COPY) {
                this.dispatchEvent(ModuleCommand.UPDATE_FIGHT_SIMP_RESULT, toc);
                //延迟2秒,等待物品掉落飞行, 在TdMainDialog中弹出结算
                return;
            }
            TipsUtil.setPopUpWindows(ModuleCommand.OPEN_FIGHT_SUCC_DIALOG, toc, 0, true);
        }
        else {
            //塔防失败弹窗
            if (toc.match_type == MatchConst.MATCH_TYPE_TD_MAIN || toc.match_type == MatchConst.MATCH_TYPE_TD_DAILY_COPY) {
                this.dispatchEvent(ModuleCommand.UPDATE_FIGHT_SIMP_RESULT, toc);
                return;
            } 
            else {
                this.openFightFailDialog(toc.match_type);
            }
           
        }

        this.dispatchEvent(ModuleCommand.UPDATE_FIGHT_SIMP_RESULT, toc);
    }

    battle_trial_fight_result(toc: m_fight_simp_result_toc): void {
        let isRunningFight = LayerManager.runningFight(toc.match_type);
        if (toc.is_success == true) {
            let infoVo = BattleTrialDataCenter.instance.infoVo;
            let curModel = BattleTrialDataCenter.instance.cur_model;
            let endTime = BattleTrialDataCenter.instance.end_tick;
            let remainTime = endTime - DataCenter.serverTimeSeconds;
            let isModelChallenge = curModel == BattleTrialModel.CHALLENGE;
            let isLastMinute = remainTime <= 60;

            let maxPass = CfgCacheMapMgr.cfg_battle_trial_pass_guanqiaCache.get_max("id");
            let curPass = BattleTrialDataCenter.instance.cur_pass;
            let isMaxPass = curPass + 1 >= maxPass;

            let todayPass = BattleTrialDataCenter.instance.today_pass;
            let todayMaxPass = BattleTrialMiscConst.battle_trial_daily_max_num;
            let isTodayMaxPass = todayPass >= todayMaxPass;

            let lineUpVo = LineUpMgr.instance.getLineUpVo(toc.match_type);
            let isAutoFight = lineUpVo && lineUpVo.checkIsCanAuto() || false;

            if (toc.is_quick_pass) {
                TipsUtil.showTips(window.iLang.L2_ZU_DUI_ZHENG_ZHAN_DI_P0_GUAN_YI_TONG_GUAN.il([curPass]));
            }
            else {
                let lastVos = infoVo.getLastGuanQiaEarnings();
                let curVos = infoVo.getCurGuanQiaEarnings();

                let tipStr = "";
                let len = Math.max(lastVos.length, curVos.length);
                for (let i = 0; i < len; ++i) {
                    let lastVo = lastVos[i];
                    let curVo = curVos[i];
                    if (lastVo || curVo) {
                        tipStr += window.iLang.L2_ch23_P0_SHOU_YI_ch05_P1_ch29_ch13_P2_ch24.il([(lastVo || curVo).name, lastVo && lastVo.showUINum || 0, curVo && curVo.showUINum || 0]);
                    }
                }

                TipsUtil.showTips(window.iLang.L2_DI_P0_GUAN_TONG_GUO_ch31_MEI_FEN_ZHONG_GUA_JI_SHOU_YI_TI_SHENG.il([curPass]));
                TipsUtil.showTips(tipStr);
            }

            if (toc.rewards && toc.rewards.length > 0) {
                TipsUtil.showGoodsListTip(GoodsVO.GetPItemToVosHaveRate(toc.rewards));
            }

            if (isModelChallenge && !isLastMinute && isAutoFight && !toc.is_quick_pass && !isMaxPass && !isTodayMaxPass) {
                this.dispatchEvent(ModuleCommand.FIGHT_NEXT_PASS + toc.match_type, toc.match_type);
            }
            else if (isRunningFight) {
                FightDataCenter.instance.checkFightClose(toc.match_type);
            }
        }
        else {
            if (isRunningFight) {
                let curPass = BattleTrialDataCenter.instance.cur_pass;
                TipsUtil.showTips(window.iLang.L2_ZU_DUI_ZHENG_ZHAN_DI_P0_GUAN_TIAO_ZHAN_SHI_BAI.il([curPass + 1]));
                FightDataCenter.instance.checkFightClose(toc.match_type);
            }
            else {
                this.openFightFailDialog(toc.match_type);
            }
        }
    }

    m_main_battle_fight_result_toc(msg: m_main_battle_fight_result_toc): void {
        ConsoleUtils.log("m_main_battle_fight_result_toc");
        if (msg.is_fight_pass) this.dispatchEvent(ModuleCommand.CLOSE_LINE_UP_DIALOG);
        GuaJiDataCenter.instance.fight_finish_data = msg;
        let matchType = MatchConst.MATCH_TYPE_MAIN_BATTLE;
        FightDataCenter.FIGHT_STATS_INFO_CACHE.set(matchType, msg.stats);
        FightDataCenter.FIGHT_STATS_INFO_CACHE_VO.set(matchType, FightStatVo.createVo(matchType, msg.is_success ? 1 : 2, msg.stats));
        FightDataCenter.saveResultData(MatchConst.MATCH_TYPE_MAIN_BATTLE, msg, this, this.m_main_battle_fight_result_toc);


        //检测是否掉落宝箱
        let isHasDropBox = false;
        for (let pitem of GuaJiDataCenter.instance.fight_finish_data.gains) {
            if (ConfigManager.cfg_main_battle_boxByItemIdCache.get(pitem.type_id) != undefined) {
                isHasDropBox = true;
                break;
            }
        }

        if (msg.is_success) {//胜利
            if (GuaJiDataCenter.isInGuaJiDialog && isHasDropBox) {
                this.dispatchEvent(ModuleCommand.DROP_BOX_GUAJI_MAIN_BATTLE, msg);
            }

            if (GuaJiDataCenter.instance.isAutoPass) {
                this.dispatchEvent(ModuleCommand.START_AUTO_MAIN_BATTLE, isHasDropBox);
            }
            else {
                if (TipsUtil.HasPopUpWindow(ModuleCommand.OPEN_LEVEL_UP_REWARD_DIALOG)) {//有升级界面，由升级界面做切换处理
                    GuaJiDataCenter.isNeedChangeScene = true;
                    GuaJiDataCenter.changeSceneType = MatchConst.MATCH_TYPE_MAIN_BATTLE;
                }
                else {
                    this.toAutoFight(MatchConst.MATCH_TYPE_MAIN_BATTLE);
                    console.log("FightController.m_main_battle_fight_result_toc");
                    this.dispatchEvent(ModuleCommand.BEGAN_RUN_GUAJI);
                }
            }
        }
        else {//失败
            // GuaJiDataCenter.instance.m_main_battle_auto_tos(false);
            GuaJiDataCenter.instance.laveAutoPassNum = 0;
            TipsUtil.setPopUpWindows(ModuleCommand.OPEN_FIGHT_RESULT_DIALOG);
        }
        
        this.dispatchEvent(ModuleCommand.MAIN_BATTLE_FIGHT_RESULT, msg.is_success);


        if (msg.is_success) {//通过成功
            if (GuaJiDataCenter.instance.isNeedNewChapter()) {

            } else {
                //判断挂机收益是否有提升
                GuaJiUtil.checkGuaJiUp();
            }
            DispatchManager.dispatchEvent(ModuleCommand.ON_MAIN_BATTLE_FIGHT_SUCC, GuaJiDataCenter.instance.curPass);
        }
    }

    private m_red_cliff_fight_result_toc(toc: m_red_cliff_fight_result_toc): void {
        RedCliffDataCenter.instance.fightResult = toc;
        FightDataCenter.saveResultData(MatchConst.MATCH_TYPE_RED_CLIFF, toc, this, this.m_red_cliff_fight_result_toc);
        if (toc.is_success == true) {
            TipsUtil.setPopUpWindows(ModuleCommand.OPEN_FIGHT_SUCC_DIALOG, toc, 0, true);
        } else {
            this.openFightFailDialog(MatchConst.MATCH_TYPE_RED_CLIFF);
        }
    }

    // private m_maze_fight_result_toc(toc: m_maze_fight_result_toc): void {
    //     MazeOldDataCenter.instance.fightResult = toc;
    //     if (toc.is_success == true) {
    //         MazeOldDataCenter.instance.isStopChoose = true;
    //         TipsUtil.setPopUpWindows(ModuleCommand.OPEN_FIGHT_SUCC_DIALOG, toc, 0, true);
    //     } else {
    //         this.openFightFailDialog(GameConst.MATCH_TYPE_MAZE_OLD);
    //     }
    // }

    private m_test_tower_fight_result_toc(toc: m_test_tower_fight_result_toc): void {
        TestTowerDataCenter.instance.fightResult = toc;
        TestTowerDataCenter.instance.setfightResult(toc);
        let towerType = toc.type;
        let matchType = TestTowerUtil.towerTypeToMatchType(towerType);
        if (!matchType) return ;
        let info = TestTowerDataCenter.instance.getInfo(towerType);
        let isInBack = LayerManager.isFightInBack(matchType);
        let towerName = TestTowerUtil.towerTypeToName(towerType);
        let isSuccess = toc.is_success;
        let isAutoFight = info ? info.fight_status == 1 : false;
        FightDataCenter.saveResultData(matchType, toc, this, this.m_test_tower_fight_result_toc);
        if (isSuccess) {
            if (isAutoFight && isInBack) {
                TipsUtil.showTips(towerName + "第" + toc.challenge_floor + "层挑战成功");
                TipsUtil.showGoodsListTip(GoodsVO.GetPItemToVos(toc.rewards));
                if (TestTowerUtil.isTestTower(towerType)) {
                    this.dispatchEvent(ModuleCommand.FIGHT_TEST_TOWER_NEXT_FLOOR,matchType);
                }
                else {
                    this.dispatchEvent(ModuleCommand.FIGHT_CROSS_TEST_TOWER_NEXT_FLOOR,matchType);
                }
            }
            else {
                TipsUtil.setPopUpWindows(ModuleCommand.OPEN_FIGHT_SUCC_DIALOG, toc, 0, true);
            }
        }
        else {
            this.openFightFailDialog(matchType);
        }
    }

    private m_god_trial_fight_result_toc(msg: m_god_trial_fight_result_toc): void {
        GodTrialDataCenter.instance.setFightResultInfo(msg);
        FightDataCenter.saveResultData(MatchConst.MATCH_TYPE_GOD_TRIAL, msg, this, this.m_god_trial_fight_result_toc);

        if (msg.is_success == true) {
            GodTrialDataCenter.instance.selectBuff();
        } else {
            msg["_exit_battle_"] = GodTrialDataCenter.instance.isExitBattle;
            GodTrialDataCenter.instance.isExitBattle = false;
            this.openFightSuccDialog(msg);
        }
        this.dispatchEvent(ModuleCommand.UPDATE_GOD_TRIAL_INFO);
    }

    private m_cross_ladder_fight_result_toc(msg: m_cross_ladder_fight_result_toc): void {
        CrossLadderDataCenter.instance.crossLadderFightResult = msg;
        FightDataCenter.saveResultData(MatchConst.MATCH_TYPE_CROSS_LADDER, msg, this, this.m_cross_ladder_fight_result_toc);
        this.dispatchEvent(ModuleCommand.OPEN_CROSS_LADDER_FIGHT_RESULT_DIALOG, msg);
    }

    private m_fight_times_toc(msg: m_fight_times_toc): void {
        let ins = FightDataCenter.instance
        for (let kv2 of msg.crush_times) {
            let data = ins.getTimesData(kv2.key);
            data.crushTimes = kv2.val;
        }
        for (let kv2 of msg.skip_times) {
            let data = ins.getTimesData(kv2.key);
            data.skipTimes = kv2.val;
        }
        this.dispatchEvent(ModuleCommand.FIGHT_TIMES);
    }

    //-------------协议接收 end ---------------
}