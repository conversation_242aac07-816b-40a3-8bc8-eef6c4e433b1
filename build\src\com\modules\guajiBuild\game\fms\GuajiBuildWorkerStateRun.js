import { TDRoleStateRun } from "../../../../scene2d/role/fsm/state/TDRoleStateRun";
import { TDRolePathPointScript } from "../../../tdBase/game/script/TDRolePathPointScript";
export class GuajiBuildWorkerStateRun extends TDRoleStateRun {
    playAni() {
        let worker = this.roleBase;
        worker.setRunAction();
    }
    /**玩家拥有战斗待机动画**/
    ToStand() {
        // this.currRoleFSMMgr.ChangeState(ESkeletonAction.STAND);
        // this.roleBase.ToStand();
        //强制跑动状态.
        let script = this.roleBase.getComponent(TDRolePathPointScript);
        if (script && script.isEnd == false) {
            let worker = this.roleBase;
            worker.setRunAction();
        }
        else {
            this.roleBase.ToStand();
        }
    }
}
