import { ESkeletonAction } from "../../../../modules/baseModules/skeleton/SkeletonData";
import { TDRolePathPointScript } from "../../../../modules/tdBase/game/script/TDRolePathPointScript";
import { RoleStateRun } from "./RoleStateRun";
export class TDRoleStateRun extends RoleStateRun {
    OnUpdate(interval) {
        super.OnUpdate(interval);
    }
    OnLeave() {
        this.isMove = false;
        super.OnLeave();
    }
    /**玩家拥有战斗待机动画**/
    ToStand() {
        // this.currRoleFSMMgr.ChangeState(ESkeletonAction.STAND);
        this.roleBase.ToStand();
        //强制跑动状态.
        let script = this.roleBase.getComponent(TDRolePathPointScript);
        if (script && script.isEnd == false) {
            this.PlayAni(ESkeletonAction.RUN, true);
        }
        else {
        }
    }
}
