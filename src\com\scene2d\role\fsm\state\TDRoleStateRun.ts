import { ESkeletonAction } from "../../../../modules/baseModules/skeleton/SkeletonData";
import { TDRolePathPointScript } from "../../../../modules/tdBase/game/script/TDRolePathPointScript";
import { RoleStateRun } from "./RoleStateRun";

export class TDRoleStateRun extends RoleStateRun {

    public OnUpdate(interval: number): void {
        super.OnUpdate(interval);
    }

    public OnLeave(): void {
        this.isMove = false;
        super.OnLeave();
    }

    /**玩家拥有战斗待机动画**/
    public ToStand(): void {
        // this.currRoleFSMMgr.ChangeState(ESkeletonAction.STAND);
        this.roleBase.ToStand();
        
        //强制跑动状态.
        let script = this.roleBase.getComponent(TDRolePathPointScript);
        if (script && script.isEnd == false) {
            this.PlayAni(ESkeletonAction.RUN, true);
        }else{
            
        }
    }
}