import { ILaya } from "ILaya";
import { Ease } from "laya/utils/Ease";
import { Handler } from "laya/utils/Handler";
import { Tween } from "laya/utils/Tween";
import { BaseScript } from "../../BaseScript";
import { Point } from "laya/maths/Point";
import { GlobalConfig } from "../../../../game/GlobalConfig";
import { DialogManager } from "laya/ui/DialogManager";
import { View } from "laya/ui/View";
import { BaseDialog, DialogNavShow } from "../../BaseDialog";
import MainUIManager from "../../menu/MainUIManager";
import { Component } from "laya/components/Component";
import { Laya } from "Laya";
import { Sprite } from "laya/display/Sprite";

export enum EUIPopupEffectType {
    None = "",

    FadeIn = "FadeIn",                          //淡入淡出
    Scale = "Scale",                            //放大缩小
    MouseScale = "MouseScale",                  //从点击位置放大缩小
    CenterScale = "CenterScale",                //自身中央放大缩小
    LeftToRight = "LeftToRight",                //从左到右
    FromPosition = "FromPosition",                //从下到上
}

// export class UIPopupEffectArgs {

//     type: EUIPopupEffectType = EUIPopupEffectType.None;
//     delay = 0;
//     duration = 300;

//     oriVisible = true;
//     oriScaleX = 1;
//     oriScaleY = 1;
//     oriAlpha = 1;
//     oriX = 0;
//     oriY = 0;

//     fromX:number = null;
//     fromY:number = null;

//     ease:Function = null;

//     private constructor(type:EUIPopupEffectType){
//         this.type = type;
//     }
//     static create(type:EUIPopupEffectType){
//         //TODO 对象池
//         return new UIPopupEffectArgs(type)
//     }
// }

// export class UIPopupEffectScript extends BaseScript {

//     private script: UIPopupEffect_Base;

//     get view() {
//         return this.ownerSpr as View;
//     }

//     get args() {
//         return this.script?.args;
//     }

//     public setPopupEffect(effect: EUIPopupEffectType, args: UIPopupEffectArgs = null, openCallback: Handler = null, closeCallback: Handler = null) {

//         if (this.script) {
//             return;
//         }

//         args = args || UIPopupEffectArgs.create(EUIPopupEffectType.None);

//         if (effect == EUIPopupEffectType.CenterScale) {
//             this.script = this.view.addComponent(UIPopupEffect_CenterScale);
//         }
//         else if (effect == EUIPopupEffectType.FadeIn) {
//             this.script = this.view.addComponent(UIPopupEffect_FadeIn);
//         }
//         else if (effect == EUIPopupEffectType.Scale) {
//             this.script = this.view.addComponent(UIPopupEffect_Scale);
//         }
//         else if (effect == EUIPopupEffectType.FromPosition) {
//             this.script = this.view.addComponent(UIPopupEffect_FromPosition);
//         }

//         if(this.script){
//             this.script.setPopupEffect(effect, args, openCallback, closeCallback);
//         }
//     }

//     public onOpenView() {
//         this.script?.onOpenView();
//     }

//     public onCloseView() {
//         this.script?.onCloseView();
//     }

//     onDestroy(): void {
//         if (this.script) {
//             this.script.destroy();
//             this.script = null;
//         }
//     }
// }

export class UIPopupEffectScript extends BaseScript {
    public effType: EUIPopupEffectType = EUIPopupEffectType.None;

    _duration_: number = 300;
    _delay_: number = 0;
    _easeOpen_: Function = Ease.linearNone;
    _easeClose_: Function = Ease.linearNone;

    private tw: Tween = null;

    public get fromScale() {
        return 0.3 * Math.min(DialogManager.ScaleX, DialogManager.ScaleY);
    }

    public isOpening: boolean = false;
    public isClosing: boolean = false;
    public clickPos: Point;

    public openCallback: Handler = null;
    public closeCallback: Handler = null;

    constructor() {
        super();
    }

    get view() {
        return this.ownerSpr as View;
    }

    _cloneTo(dest: Component): void {
        //获取所有key,判断以_开头和_结尾
        let keys = Object.keys(this) || [];
        for (let key of keys) {
            if (key[0] == "_" && key[key.length - 1] == "_") {
                dest[key] = this[key];
            }
        }
    }

    public setPopupEffect(openCallback: Handler = null, closeCallback: Handler = null) {
        this.openCallback = openCallback;
        this.closeCallback = closeCallback;
    }

    public onOpenView() {
        this.openCallback && this.openCallback.run();
    }

    protected _checkIsOpening() {
        if (this.isOpening) {
            return true;
        }
        this.isOpening = true;
        this.resetViewProps();
        return false;
    }

    public onCloseView() {
        this.isClosing = true;
        this.closeCallback && this.closeCallback.run();
    }

    public getCenterFromPos(oriScaleX: number, oriScaleY): Point {
        let view = this.view;
        // let pos = Point.create();
        // pos.x = (GlobalConfig.DeviceW - view.width * this.fromScale) / 2 + view.pivotX * this.fromScale;
        // pos.y = (GlobalConfig.DeviceH - view.height * this.fromScale) / 2 + view.pivotY * this.fromScale;
        // return pos;

        let fromX = (oriScaleX - this.fromScale) * view.width / 2 + view.x;
        let fromY = (oriScaleY - this.fromScale) * view.height / 2 + view.y;
        return Point.create().setTo(fromX, fromY);
    }

    public getMousePos(): Point {

        if (this.clickPos) {
            return this.clickPos;
        }
    }

    resetViewProps() {

    }

    to(props: any, ease: Function | null = null, complete: Handler | null = null) {
        this._clearTw();
        this.tw = Tween.to(this.view, props, this._duration_, ease, complete, this._delay_, true, false);
    }

    from(props: any, ease: Function = null, complete: Handler = null) {
        this._clearTw();
        this.tw = Tween.from(this.view, props, this._duration_, ease, complete, this._delay_, true, false);
    }

    private _clearTw(){
        if (this.tw) {
            this.tw.clear();
            this.tw = null;
        }
    }

    onDestroy(): void {
        if (this.openCallback) {
            this.openCallback.clear();
            this.openCallback = null;
        }
        if (this.closeCallback) {
            this.closeCallback.clear();
            this.closeCallback = null;
        }

        this._clearTw();
    }

    public static create(type: EUIPopupEffectType) {

        if (window.MainUIManager.instance.is_ui_pop_effect_open == false) {
            return new UIPopupEffectScript();
        }

        let ret: UIPopupEffectScript;
        switch (type) {
            case EUIPopupEffectType.None:
                ret = new UIPopupEffectScript();
                break;
            case EUIPopupEffectType.FadeIn:
                ret = new UIPopupEffect_FadeIn();
                break;
            case EUIPopupEffectType.Scale:
                ret = new UIPopupEffect_Scale();
                break;
            case EUIPopupEffectType.CenterScale:
                ret = new UIPopupEffect_CenterScale();
                break;
            case EUIPopupEffectType.MouseScale:
                ret = new UIPopupEffect_MouseScale();
                break;

            case EUIPopupEffectType.FromPosition:
                ret = new UIPopupEffect_FromPosition();
                break;
            default:
                ret = new UIPopupEffectScript();
                break;
        }
        return ret;
    }
}

export class UIPopupEffect_Scale extends UIPopupEffectScript {

    effType = EUIPopupEffectType.Scale;

    public _oriScaleX_: number = 1;
    public _oriScaleY_: number = 1;


    resetViewProps() {
        super.resetViewProps();
        this._oriScaleX_ = this.view.scaleX;
        this._oriScaleY_ = this.view.scaleY;
        this._easeOpen_ = this._backOut;
        this._easeClose_ = Ease.backIn;
    }

    //幅度s小一点
    protected _backOut(t: number, b: number, c: number, d: number, s: number = 1.2): number {
        return Ease.backOut(t, b, c, d, s);
    }

    public onOpenView() {

        if (super._checkIsOpening()) {
            return true;
        }

        let view = this.view;
        let fromScale = this.fromScale;
        view.scale(fromScale, fromScale);

        this.to({ scaleX: this._oriScaleX_, scaleY: this._oriScaleX_ }, this._easeOpen_, this.openCallback);
    }

    public onCloseView() {

        let view = this.view;
        let fromScale = this._oriScaleX_;
        view.scale(fromScale, fromScale);
        this.to({ scaleX: this.fromScale, scaleY: this.fromScale }, this._easeClose_, this.closeCallback);
    }

    onDestroy(): void {
        super.onDestroy();
    }
}
/**
 * 从界面中央弹出
 */
export class UIPopupEffect_CenterScale extends UIPopupEffect_Scale {

    effType = EUIPopupEffectType.CenterScale;

    public onOpenView() {

        if (super._checkIsOpening()) {
            return true;
        }

        let pos = this.getCenterFromPos(this._oriScaleX_, this._oriScaleY_);
        this.from({ x: pos.x, y: pos.y, scaleX: this.fromScale, scaleY: this.fromScale }, this._easeOpen_, this.openCallback);
    }

    public onCloseView() {

        let pos = this.getCenterFromPos(this._oriScaleX_, this._oriScaleY_);
        this.to({ x: pos.x, y: pos.y, scaleX: this.fromScale, scaleY: this.fromScale }, this._easeClose_, this.closeCallback);

    }
}

export class UIPopupEffect_MouseScale extends UIPopupEffect_Scale {

    effType = EUIPopupEffectType.CenterScale;

    _fromX_: number = 0;
    _fromY_: number = 0;

    //幅度s小一点
    protected _backOut(t: number, b: number, c: number, d: number, s: number = 1.1): number {
        return Ease.backOut(t, b, c, d, s);
    }

    public resetViewProps(): void {
        super.resetViewProps();

        let view = this.view;
        let mouseX = Laya.stage.mouseX;
        let mouseY = Laya.stage.mouseY;
        //计算跟stage的偏移
        // let globalPos = this.ownerSpr.parentPointToGlobal(Point.create().setTo(this.ownerSpr.x, this.ownerSpr.y));
        let parent = this.ownerSpr.parent as Sprite;
        if (parent) {
            var localPos = parent.globalToLocal(Point.create().setTo(mouseX, mouseY));
        } else {
            var localPos = Point.create().setTo(0, 0)
        }

        this._fromX_ = localPos.x;
        this._fromY_ = localPos.y;
    }

    public onOpenView() {

        if (super._checkIsOpening()) {
            return true;
        }

        this.from({ x: this._fromX_, y: this._fromY_, scaleX: this.fromScale, scaleY: this.fromScale }, this._easeOpen_, this.openCallback);
    }

    public onCloseView() {

        this.to({ x: this._fromX_, y: this._fromY_, scaleX: this.fromScale, scaleY: this.fromScale }, this._easeClose_, this.closeCallback);

    }
}


/**
 * 淡入淡出
 */
export class UIPopupEffect_FadeIn extends UIPopupEffectScript {

    effType = EUIPopupEffectType.FadeIn;
    public _oriAlpha_ = 1;

    public onOpenView() {

        if (super._checkIsOpening()) {
            return true;
        }

        this.view.alpha = 0.1;
        this.to({ alpha: this._oriAlpha_ }, null, this.openCallback);
    }

    public onCloseView() {

        this.view.alpha = this._oriAlpha_;
        this.to({ alpha: 0.1 }, null, this.closeCallback);
    }
}

/**
 * 位置变化
 */
export class UIPopupEffect_FromPosition extends UIPopupEffectScript {

    effType = EUIPopupEffectType.FromPosition;

    public _oriX_: number = 0;
    public _oriY_: number = 0;
    public _fromX_: number = 0;
    public _fromY_: number = 0;


    resetViewProps(): void {
        super.resetViewProps();
    }

    public onOpenView() {

        if (super._checkIsOpening()) {
            return true;
        }

        let view = this.view;

        this._oriX_ = view.x;
        this._oriY_ = view.y;

        view.x = this._fromX_ == null ? view.x : this._fromX_;
        view.y = this._fromY_ == null ? view.y : this._fromY_;
        this.to({ x: this._oriX_, y: this._oriY_ }, this._easeOpen_, this.openCallback);
    }

    public onCloseView() {
        this.to({ x: this._fromX_, y: this._fromY_ }, this._easeClose_, this.closeCallback);
    }
}